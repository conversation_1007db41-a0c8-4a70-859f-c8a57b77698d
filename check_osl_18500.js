// check_osl_18500.js
// Check the current status of OSL 18500

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkOSL18500() {
  try {
    console.log('Checking OSL 18500 status...');
    
    const { data: osl, error } = await supabase
      .from('t_order_sheet_lines')
      .select('id, max_weight, ready_button, ready, shopify_uploaded_at, shopify_product_uploaded_notes')
      .eq('id', 18500)
      .single();
    
    if (error) {
      console.error('Error fetching OSL:', error);
      return;
    }
    
    console.log('OSL 18500 Details:');
    console.log('Max Weight:', osl.max_weight + 'g');
    console.log('Ready Button:', osl.ready_button);
    console.log('Ready:', osl.ready);
    console.log('Shopify Uploaded At:', osl.shopify_uploaded_at);
    console.log('Upload Notes:', osl.shopify_product_uploaded_notes);
    
    if (osl.shopify_uploaded_at) {
      console.log('\n⚠️  OSL is already uploaded! This explains why the error extraction test failed.');
      console.log('The task would complete early without going through the error path.');
    } else {
      console.log('\n✅ OSL is not uploaded yet, should trigger the manual fix error.');
    }
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

checkOSL18500();
