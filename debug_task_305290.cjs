require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function debugTask305290() {
  try {
    console.log('Debugging task ID 305290 (match_osl_to_discs)...');
    
    // Get the task details
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 305290)
      .single();
    
    if (taskError) {
      console.error('Error getting task:', taskError);
      return;
    }
    
    console.log('\n=== TASK 305290 DETAILS ===');
    console.log(`Task Type: ${task.task_type}`);
    console.log(`Status: ${task.status}`);
    console.log(`Payload:`, task.payload);
    console.log(`Enqueued By: ${task.enqueued_by}`);
    console.log(`Created At: ${task.created_at}`);
    console.log(`Scheduled At: ${task.scheduled_at}`);
    console.log(`Result:`, task.result);
    
    // Get the OSL details
    const oslId = task.payload.id;
    console.log(`\n=== OSL ${oslId} DETAILS ===`);
    
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, min_weight, max_weight, color_id, vendor_id')
      .eq('id', oslId)
      .single();
    
    if (oslError) {
      console.error('Error getting OSL:', oslError);
      return;
    }
    
    console.log(`MPS ID: ${osl.mps_id}`);
    console.log(`Weight Range: ${osl.min_weight}-${osl.max_weight}g`);
    console.log(`Color ID: ${osl.color_id}`);
    console.log(`Vendor ID: ${osl.vendor_id}`);
    
    // Check what discs should match this OSL using regular weight matching
    console.log('\n=== REGULAR WEIGHT MATCHING (for order_sheet_line_id) ===');
    const { data: regularMatches, error: regularError } = await supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .eq('mps_id', osl.mps_id)
      .gte('weight', osl.min_weight)
      .lte('weight', osl.max_weight)
      .in('color_id', osl.color_id === 23 ? [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23] : [osl.color_id, 23])
      .limit(10);
    
    if (regularError) {
      console.error('Error getting regular matches:', regularError);
    } else {
      console.log(`Found ${regularMatches.length} discs that should match using regular weight:`);
      regularMatches.forEach(disc => {
        const hasRegularMapping = disc.order_sheet_line_id === oslId;
        const hasVendorMapping = disc.vendor_osl_id === oslId;
        const soldStatus = disc.sold_date ? 'SOLD' : 'UNSOLD';
        console.log(`  Disc ${disc.id}: Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id}, Regular OSL: ${disc.order_sheet_line_id}${hasRegularMapping ? ' ✅' : ''}, Vendor OSL: ${disc.vendor_osl_id}${hasVendorMapping ? ' ✅' : ''}, ${soldStatus}`);
      });
    }
    
    // Check what discs should match this OSL using manufacturer weight matching
    console.log('\n=== MANUFACTURER WEIGHT MATCHING (for vendor_osl_id) ===');
    const { data: vendorMatches, error: vendorError } = await supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .eq('mps_id', osl.mps_id)
      .not('weight_mfg', 'is', null)
      .in('color_id', osl.color_id === 23 ? [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23] : [osl.color_id, 23])
      .limit(20);
    
    if (vendorError) {
      console.error('Error getting vendor matches:', vendorError);
    } else {
      // Filter by rounded weight_mfg in JavaScript since Supabase doesn't have ROUND in filters
      const filteredVendorMatches = vendorMatches.filter(disc => {
        const roundedWeightMfg = Math.round(disc.weight_mfg);
        return roundedWeightMfg >= osl.min_weight && roundedWeightMfg <= osl.max_weight;
      });
      
      console.log(`Found ${filteredVendorMatches.length} discs that should match using manufacturer weight:`);
      filteredVendorMatches.forEach(disc => {
        const roundedWeightMfg = Math.round(disc.weight_mfg);
        const hasRegularMapping = disc.order_sheet_line_id === oslId;
        const hasVendorMapping = disc.vendor_osl_id === oslId;
        const soldStatus = disc.sold_date ? 'SOLD' : 'UNSOLD';
        console.log(`  Disc ${disc.id}: Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g (rounded: ${roundedWeightMfg}g), Color ${disc.color_id}, Regular OSL: ${disc.order_sheet_line_id}${hasRegularMapping ? ' ✅' : ''}, Vendor OSL: ${disc.vendor_osl_id}${hasVendorMapping ? ' ✅' : ''}, ${soldStatus}`);
      });
    }
    
    // Check what the current match_osl_to_discs task logic would do
    console.log('\n=== CURRENT TASK LOGIC ANALYSIS ===');
    console.log('The match_osl_to_discs task currently only handles regular weight matching.');
    console.log('It needs to be updated to also handle manufacturer weight matching for vendor_osl_id.');
    
    // Show what the task result was
    if (task.result) {
      console.log('\nTask Result:', task.result);
      if (task.result.message) {
        console.log('Message:', task.result.message);
      }
      if (task.result.discs_updated !== undefined) {
        console.log(`Discs Updated: ${task.result.discs_updated}`);
      }
    }
    
    console.log('\n=== DUAL MAPPING REQUIREMENTS ===');
    console.log('The match_osl_to_discs task should:');
    console.log('1. Find discs that match using regular weight → update order_sheet_line_id');
    console.log('2. Find discs that match using manufacturer weight → update vendor_osl_id');
    console.log('3. Handle both mappings in a single task execution');
    console.log('4. Report both types of matches in the task result');
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

debugTask305290();
