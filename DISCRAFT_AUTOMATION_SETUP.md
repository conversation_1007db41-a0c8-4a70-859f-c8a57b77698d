# 📊 Discraft Daily Automation Setup Guide

## 🎯 What This Does

Every day at **11:00 AM CST**, the system will automatically:

1. **📥 Download** the latest Discraft vendor file
2. **🔄 Import** all products with dynamic weight parsing
3. **🎯 Calculate** MPS matching for inventory alignment
4. **📊 Generate** order summary with total disc quantities needed
5. **📧 Email** you a detailed report at `<EMAIL>`

## 🚀 Setup Instructions

### Step 1: Configure Email Settings

1. **Create a `.env` file** in your project root:
   ```bash
   cp .env.example .env
   ```

2. **Set up Gmail App Password**:
   - Go to [Google Account Settings](https://myaccount.google.com/)
   - Select **Security** → **App passwords**
   - Generate an app password for "Mail"
   - Copy the 16-character password

3. **Edit `.env` file**:
   ```env
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-16-character-app-password
   ```

### Step 2: Test the Automation

```bash
# Test the full automation process
node testDiscraftAutomation.js
```

This will run through the entire process once and send you a test email.

### Step 3: Start the Scheduler

**Option A: Using PM2 (Recommended)**
```bash
# Start the scheduler as a background service
pm2 start ecosystem.config.js --only discraft-scheduler

# Check status
pm2 status

# View logs
pm2 logs discraft-scheduler
```

**Option B: Manual Start**
```bash
# Run the scheduler directly (stays in foreground)
node discraftScheduler.js

# Test run immediately
node discraftScheduler.js --test
```

### Step 4: Verify Setup

1. **Check scheduler status**:
   ```bash
   pm2 status discraft-scheduler
   ```

2. **View logs**:
   ```bash
   pm2 logs discraft-scheduler --lines 50
   ```

3. **Next execution time** should show in the logs

## 📧 Email Report Contents

Your daily email will include:

### 🎯 Order Summary
- **Total discs to order** from Discraft
- **Number of unique molds** and plastics
- **Top 10 molds** by order quantity

### 🔄 Process Status
- ✅/❌ Download status
- ✅/❌ Import status  
- ✅/❌ MPS matching status
- ✅/❌ Order calculation status

### 📊 Example Email
```
📦 Total Order Quantity: 127 discs
Unique Molds: 23 | Unique Plastics: 8

🏆 Top 10 Molds to Order:
Buzzz: 15
Zone: 12
Heat: 8
...
```

## 🛠️ Management Commands

### View Scheduler Status
```bash
pm2 status discraft-scheduler
```

### Stop Scheduler
```bash
pm2 stop discraft-scheduler
```

### Restart Scheduler
```bash
pm2 restart discraft-scheduler
```

### View Real-time Logs
```bash
pm2 logs discraft-scheduler --follow
```

### Manual Test Run
```bash
node testDiscraftAutomation.js
```

## ⏰ Schedule Details

- **Time**: 11:00 AM CST (Central Standard Time)
- **Frequency**: Daily
- **Timezone**: America/Chicago (handles DST automatically)
- **Backup**: Scheduler restarts daily at midnight

## 🔧 Troubleshooting

### Email Not Sending
1. Check `.env` file has correct credentials
2. Verify Gmail app password is correct
3. Check logs: `pm2 logs discraft-scheduler`

### Import Failing
1. Check if admin server is running: `pm2 status adminserver`
2. Verify Discraft website is accessible
3. Check file permissions in `data/external data/` folder

### Scheduler Not Running
1. Check PM2 status: `pm2 status`
2. Restart: `pm2 restart discraft-scheduler`
3. Check system time zone settings

### View Detailed Logs
```bash
# Last 100 lines
pm2 logs discraft-scheduler --lines 100

# Follow live logs
pm2 logs discraft-scheduler --follow

# Error logs only
pm2 logs discraft-scheduler --err
```

## 📁 Files Created

- `discraftDailyAutomation.js` - Main automation logic
- `discraftScheduler.js` - Cron scheduler
- `testDiscraftAutomation.js` - Test runner
- `ecosystem.config.js` - PM2 configuration (updated)

## 🎉 Success Indicators

When working correctly, you should see:
- ✅ Daily email at 11:00 AM CST
- ✅ PM2 status shows "online"
- ✅ Logs show successful executions
- ✅ Order quantities in email reports

## 📞 Support

If you encounter issues:
1. Check the logs first: `pm2 logs discraft-scheduler`
2. Try a manual test: `node testDiscraftAutomation.js`
3. Verify email settings in `.env`
4. Ensure admin server is running on port 3001
