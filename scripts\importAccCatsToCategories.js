import fs from 'fs';
import path from 'path';
import <PERSON> from 'papaparse';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

// Config
const FILE_PATH = path.join(process.cwd(), 'data', 'external data', 'access', 'tAccCats.txt');
const CHUNK_SIZE = 500; // inserts in chunks

function toIntOrNull(v) {
  if (v === undefined || v === null || v === '') return null;
  const n = Number(v);
  return Number.isFinite(n) ? Math.trunc(n) : null;
}

function parseTsv(filePath) {
  const raw = fs.readFileSync(filePath, 'utf-8');
  const { data, errors, meta } = Papa.parse(raw, {
    header: true,
    skipEmptyLines: true,
    delimiter: '\t',
    dynamicTyping: false,
  });
  if (errors && errors.length) {
    console.warn('[importAccCats] TSV parse warnings (showing first 5):', errors.slice(0, 5));
  }
  // Normalize columns and map to our model
  const rows = data.map((r) => {
    const idLegacy = toIntOrNull(r.CategoryID ?? r.categoryid ?? r.categoryId);
    const title = (r.Title ?? r.title ?? '').toString().trim();
    const parentLegacy = toIntOrNull(r.Notes ?? r.notes); // per mapping: Notes -> parent_category_id (legacy id)
    const shopify_standard_type = (r.ShopifyStandardType ?? r.shopifystandardtype ?? '').toString().trim() || null;
    const shopify_type = (r.ShopifyType ?? r.shopifytype ?? '').toString().trim() || null;
    const shopify_tag = (r.ShopifyTag ?? r.shopifytag ?? '').toString().trim() || null;
    const shopify_product_template = (r.ShopifyProductTemplate ?? r.shopifyproducttemplate ?? '').toString().trim() || null;

    return {
      id_legacy: idLegacy,
      name: title,
      parent_legacy: parentLegacy,
      shopify_standard_type,
      shopify_type,
      shopify_tag,
      shopify_product_template,
    };
  }).filter((r) => r.id_legacy !== null && r.name);

  return rows;
}

function chunk(array, size) {
  const out = [];
  for (let i = 0; i < array.length; i += size) out.push(array.slice(i, i + size));
  return out;
}

async function main() {
  const dryRun = process.argv.includes('--dry-run');

  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_KEY;
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing SUPABASE_URL or SUPABASE_KEY in environment.');
    process.exit(1);
  }
  const supabase = createClient(supabaseUrl, supabaseKey);

  if (!fs.existsSync(FILE_PATH)) {
    console.error(`Input file not found: ${FILE_PATH}`);
    process.exit(1);
  }

  console.log(`[importAccCats] Reading TSV: ${FILE_PATH}`);
  const records = parseTsv(FILE_PATH);
  console.log(`[importAccCats] Parsed ${records.length} records.`);

  if (records.length === 0) {
    console.log('No records to process.');
    return;
  }

  // Fetch existing legacy IDs map
  const legacyIds = records.map((r) => r.id_legacy);
  const { data: existingRows, error: existingErr } = await supabase
    .from('t_categories')
    .select('id, id_legacy')
    .in('id_legacy', legacyIds);
  if (existingErr) {
    console.error('Failed to fetch existing t_categories legacy IDs:', existingErr);
    process.exit(1);
  }
  const existingByLegacy = new Map((existingRows || []).map((r) => [r.id_legacy, r.id]));

  // Also fetch existing rows by name to avoid unique(name) conflicts
  const names = records.map((r) => r.name);
  const { data: existingNameRows, error: existingNameErr } = await supabase
    .from('t_categories')
    .select('id, name, id_legacy')
    .in('name', names);
  if (existingNameErr) {
    console.error('Failed to fetch existing t_categories by name:', existingNameErr);
    process.exit(1);
  }
  const existingByName = new Map((existingNameRows || []).map((r) => [r.name, { id: r.id, id_legacy: r.id_legacy }]));

  const toInsert = [];
  const toUpdateByLegacy = [];
  const toUpdateByName = [];
  for (const r of records) {
    if (existingByLegacy.has(r.id_legacy)) {
      toUpdateByLegacy.push(r);
    } else if (existingByName.has(r.name)) {
      const existing = existingByName.get(r.name);
      if (existing.id_legacy && existing.id_legacy !== r.id_legacy) {
        console.warn(`Name match has different existing id_legacy (name='${r.name}', existing=${existing.id_legacy}, incoming=${r.id_legacy}). Will overwrite with incoming id_legacy.`);
      }
      toUpdateByName.push({ ...r, _target_id: existing.id });
    } else {
      toInsert.push(r);
    }
  }

  console.log(`[importAccCats] To insert: ${toInsert.length}, to update by legacy: ${toUpdateByLegacy.length}, to update by name: ${toUpdateByName.length}`);

  if (dryRun) {
    // Show a small sample
    console.log('[importAccCats] Dry-run sample (first 5):');
    console.table(records.slice(0, 5));
    return;
  }

  // 1) Insert new categories (without parent linkage for now)
  if (toInsert.length) {
    console.log(`[importAccCats] Inserting ${toInsert.length} new categories in chunks of ${CHUNK_SIZE}...`);
    const insertChunks = chunk(toInsert, CHUNK_SIZE);
    for (let i = 0; i < insertChunks.length; i++) {
      const ins = insertChunks[i].map((r) => ({
        id_legacy: r.id_legacy,
        name: r.name,
        parent_category_id: null, // link later
        shopify_standard_type: r.shopify_standard_type,
        shopify_type: r.shopify_type,
        shopify_tag: r.shopify_tag,
        shopify_product_template: r.shopify_product_template,
      }));
      const { error } = await supabase.from('t_categories').insert(ins).select('id');
      if (error) {
        console.error(`Insert chunk ${i + 1} failed:`, error);
        process.exit(1);
      }
      console.log(`  - Inserted chunk ${i + 1}/${insertChunks.length} (${ins.length} rows)`);
    }
  }

  // 2) Update existing categories' fields by id_legacy
  if (toUpdateByLegacy.length) {
    console.log(`[importAccCats] Updating ${toUpdateByLegacy.length} existing categories (by id_legacy)...`);
    let updated = 0;
    for (const r of toUpdateByLegacy) {
      const updatePayload = {
        name: r.name,
        shopify_standard_type: r.shopify_standard_type,
        shopify_type: r.shopify_type,
        shopify_tag: r.shopify_tag,
        shopify_product_template: r.shopify_product_template,
      };
      const { error } = await supabase
        .from('t_categories')
        .update(updatePayload)
        .eq('id_legacy', r.id_legacy);
      if (error) {
        console.error(`Update failed for id_legacy=${r.id_legacy}:`, error);
        process.exit(1);
      }
      updated++;
      if (updated % 100 === 0) console.log(`  - Updated ${updated}/${toUpdateByLegacy.length}...`);
    }
    console.log(`  - Done updating ${updated} rows.`);
  }

  // 3) Update existing categories matched by name (set id_legacy and fields)
  if (toUpdateByName.length) {
    console.log(`[importAccCats] Updating ${toUpdateByName.length} existing categories (by name)...`);
    let updatedByName = 0;
    for (const r of toUpdateByName) {
      const updatePayload = {
        id_legacy: r.id_legacy,
        name: r.name,
        shopify_standard_type: r.shopify_standard_type,
        shopify_type: r.shopify_type,
        shopify_tag: r.shopify_tag,
        shopify_product_template: r.shopify_product_template,
      };
      const { error } = await supabase
        .from('t_categories')
        .update(updatePayload)
        .eq('id', r._target_id);
      if (error) {
        console.error(`Update-by-name failed for name='${r.name}':`, error);
        process.exit(1);
      }
      updatedByName++;
      if (updatedByName % 100 === 0) console.log(`  - Updated-by-name ${updatedByName}/${toUpdateByName.length}...`);
    }
    console.log(`  - Done updating-by-name ${updatedByName} rows.`);
  }

  // 3) Refresh id map (after inserts), then set parent_category_id via legacy mapping
  const { data: allNow, error: afterErr } = await supabase
    .from('t_categories')
    .select('id, id_legacy')
    .in('id_legacy', legacyIds);
  if (afterErr) {
    console.error('Failed to re-fetch categories after insert/update:', afterErr);
    process.exit(1);
  }
  const idByLegacy = new Map((allNow || []).map((r) => [r.id_legacy, r.id]));

  // Build parent updates
  const parentUpdates = records
    .map((r) => ({ childLegacy: r.id_legacy, parentLegacy: r.parent_legacy }))
    .filter((x) => x.parentLegacy !== null);

  console.log(`[importAccCats] Linking parents for ${parentUpdates.length} rows...`);
  let linked = 0;
  let missingParents = 0;
  for (const pu of parentUpdates) {
    const childId = idByLegacy.get(pu.childLegacy);
    const parentId = idByLegacy.get(pu.parentLegacy);
    if (!childId) continue; // should not happen
    if (!parentId) {
      missingParents++;
      continue;
    }
    const { error } = await supabase
      .from('t_categories')
      .update({ parent_category_id: parentId })
      .eq('id', childId);
    if (error) {
      console.error(`Failed to set parent for child legacy ${pu.childLegacy} -> parent legacy ${pu.parentLegacy}:`, error);
      process.exit(1);
    }
    linked++;
    if (linked % 100 === 0) console.log(`  - Linked ${linked}/${parentUpdates.length}...`);
  }
  console.log(`  - Done linking. Set parents on ${linked} rows. Missing parents: ${missingParents}.`);

  console.log('\n[importAccCats] Completed successfully.');
}

main().catch((e) => {
  console.error('[importAccCats] Fatal error:', e);
  process.exit(1);
});

