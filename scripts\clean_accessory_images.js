// scripts/clean_accessory_images.js
// Purpose: Read the Shopify Accessory Images TSV, download images from CloudFront for rows whose URL matches
//          the magentoproduct base, rename each to AccessoryID.jpg, and save into the UNC folder provided.
// Usage examples:
//   node scripts/clean_accessory_images.js --dry-run           # Parse and report only (default behavior)
//   node scripts/clean_accessory_images.js --run               # Perform downloads and writes
//   node scripts/clean_accessory_images.js --run --concurrency 8
//   node scripts/clean_accessory_images.js --run --include-alt-bases  # Also process rows with the alternate base
//
// Notes:
// - This script intentionally avoids external dependencies; it uses Node's builtin https and fs modules.
// - It will skip writing if the target file (AccessoryID.jpg) already exists.
// - It prints a concise summary and a failures CSV for any errors encountered.

import fs from 'fs';
import fsp from 'fs/promises';
import path from 'path';
import https from 'https';

// ---- Config ----
const DATA_FILE = path.join('data', 'external data', 'access', 'Shopify Accessory Images.txt');
const TARGET_BASE = 'https://d3f34rkxix3zin.cloudfront.net/shopify/dgaccessories/magentoproduct/';
const ALT_BASES = [
  'https://d3f34rkxix3zin.cloudfront.net/shopify/dgaccessories/'
];
const TARGET_UNC_DIR = '\\NANCY\\nancyv8\\Images\\DG Accessory Products\\Uploaded to AWS';
const SOURCE_ARCHIVE_ROOT = '\\NANCY\\nancyv8\\Images\\DG Accessory Products\\3 Magento Image Archive';
const DEFAULT_CONCURRENCY = 6;
const MAX_REDIRECTS = 5;

// ---- CLI args ----
const argv = process.argv.slice(2);
const DO_RUN = argv.includes('--run');
const INCLUDE_ALT = argv.includes('--include-alt-bases');
const FROM_ARCHIVE = argv.includes('--from-archive');
const DELETE_SOURCE = argv.includes('--delete-source');
const concArgIdx = argv.findIndex(a => a === '--concurrency');
const CONCURRENCY = concArgIdx >= 0 ? Math.max(1, parseInt(argv[concArgIdx + 1] || `${DEFAULT_CONCURRENCY}`, 10)) : DEFAULT_CONCURRENCY;
const limitIdx = argv.findIndex(a => a === '--limit');
const LIMIT = limitIdx >= 0 ? Math.max(1, parseInt(argv[limitIdx + 1] || '0', 10)) : 0; // 0 = no limit

function log(...args) { console.log('[clean_accessory_images]', ...args); }
function err(...args) { console.error('[clean_accessory_images]', ...args); }

// ---- Utilities ----
function stripQuotes(s) {
  if (s == null) return '';
  if (s.length >= 2 && s.startsWith('"') && s.endsWith('"')) return s.substring(1, s.length - 1);
  return s;
}

function parseTSVLine(line) {
  // Split on tab; fields are quoted but no embedded tabs expected
  const parts = line.split('\t').map(stripQuotes);
  return parts;
}

async function ensureDir(dir) {
  await fsp.mkdir(dir, { recursive: true });
}

function downloadToBuffer(url, maxRedirects = MAX_REDIRECTS) {
  return new Promise((resolve, reject) => {
    const req = https.get(url, { headers: { 'User-Agent': 'DZP-ImageFetcher/1.0' } }, res => {
      const { statusCode, headers } = res;
      if (statusCode && statusCode >= 300 && statusCode < 400 && headers.location) {
        if (maxRedirects <= 0) return reject(new Error(`Too many redirects for ${url}`));
        const nextUrl = headers.location.startsWith('http') ? headers.location : new URL(headers.location, url).toString();
        res.resume();
        return resolve(downloadToBuffer(nextUrl, maxRedirects - 1));
      }
      if (statusCode !== 200) {
        res.resume();
        return reject(new Error(`HTTP ${statusCode} for ${url}`));
      }
      const chunks = [];
      res.on('data', d => chunks.push(d));
      res.on('end', () => resolve(Buffer.concat(chunks)));
    });
    req.on('error', reject);
  });
}

function createLimiter(concurrency) {
  const queue = [];
  let active = 0;
  const next = () => {
    if (active >= concurrency) return;
    const job = queue.shift();
    if (!job) return;
    active++;
    job()
      .catch(() => {})
      .finally(() => { active--; next(); });
  };
  return fn => new Promise((resolve, reject) => {
    queue.push(async () => fn().then(resolve, reject));
    // microtask to avoid starving
    Promise.resolve().then(next);
  });
}

async function readAllLines(filePath) {
  const raw = await fsp.readFile(filePath, 'utf8');
  // Normalize newlines
  return raw.replace(/\r\n/g, '\n').replace(/\r/g, '\n').split('\n');
}

function summarizeCounts(rows) {
  const total = rows.length;
  const withTargetBase = rows.filter(r => r.URL === TARGET_BASE).length;
  const withAltBase = rows.filter(r => ALT_BASES.includes(r.URL)).length;
  return { total, withTargetBase, withAltBase };
}

async function main() {
  log(`Reading data file: ${DATA_FILE}`);
  const lines = await readAllLines(DATA_FILE);
  if (lines.length <= 1) throw new Error('No data rows found');

  const header = parseTSVLine(lines[0]);
  const idx = Object.fromEntries(header.map((name, i) => [name.toLowerCase(), i]));
  const required = ['filename', 'url', 'accessoryid'];
  for (const r of required) if (!(r in idx)) throw new Error(`Missing required column: ${r}`);

  // Build row objects
  const rows = [];
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i];
    if (!line || !line.trim()) continue;
    const parts = parseTSVLine(line);
    if (parts.length < header.length) continue; // skip malformed
    const row = {
      FileName: parts[idx['filename']],
      URL: parts[idx['url']],
      AccessoryID: parts[idx['accessoryid']]
    };
    rows.push(row);
  }

  const summary = summarizeCounts(rows);
  log(`Parsed ${summary.total} data rows.`);
  log(`Rows with TARGET_BASE: ${summary.withTargetBase}`);
  if (INCLUDE_ALT) log(`Rows with ALT_BASES: ${summary.withAltBase}`);

  // Diagnostics: rows with TARGET_BASE but missing AccessoryID/FileName
  const targetBaseRows = rows.filter(r => r.URL === TARGET_BASE);
  const targetBaseMissing = targetBaseRows.filter(r => !r.AccessoryID || !r.FileName).length;
  if (targetBaseMissing > 0) {
    log(`Rows with TARGET_BASE missing AccessoryID or FileName: ${targetBaseMissing}`);
  }

  const candidates = rows.filter(r => {
    if (!r.AccessoryID || !r.FileName) return false;
    if (r.URL === TARGET_BASE) return true;
    if (INCLUDE_ALT && ALT_BASES.includes(r.URL)) return true;
    return false;
  });

  log(`Candidate rows to process: ${candidates.length}`);

  // If dry-run, optionally check archive presence for a small sample
  if (!DO_RUN) {
    if (FROM_ARCHIVE) {
      const sampleCount = Math.min(5, candidates.length);
      log(`Dry-run archive check (first ${sampleCount} rows):`);
      for (let i = 0; i < sampleCount; i++) {
        const c = candidates[i];
        const srcPath = path.win32.join(SOURCE_ARCHIVE_ROOT, ...c.FileName.split('/'));
        const exists = fs.existsSync(srcPath);
        log(`  AccessoryID=${c.AccessoryID} srcExists=${exists} src="${srcPath}"`);
      }
    }
    log('Dry-run mode (default). No files will be downloaded or written.');
    log('To execute, run with --run [--concurrency N] [--include-alt-bases] [--from-archive] [--delete-source]');
    return;
  }

  // Ensure target dir exists
  await ensureDir(TARGET_UNC_DIR);

  const limiter = createLimiter(CONCURRENCY);
  const failures = [];
  let processed = 0;
  let skipped = 0;
  let written = 0;

  const totalToProcess = LIMIT > 0 ? Math.min(LIMIT, candidates.length) : candidates.length;
  log(`${FROM_ARCHIVE ? 'Starting copy from archive' : 'Starting downloads'} with concurrency=${CONCURRENCY} into: ${TARGET_UNC_DIR}`);

  const slice = candidates.slice(0, totalToProcess);
  await Promise.all(slice.map(c => limiter(async () => {
    const outName = `${c.AccessoryID}.jpg`;
    // Build UNC target path robustly (avoid collapsing leading \\)
    const joinedTarget = TARGET_UNC_DIR + '\\' + outName;
    const realOut = joinedTarget.startsWith('\\\\') ? joinedTarget : ('\\\\' + joinedTarget.replace(/^\\+/, ''));

    // Skip if exists
    try {
      await fsp.access(realOut, fs.constants.F_OK);
      skipped++;
      return;
    } catch {}

    try {
      let buf;
      if (FROM_ARCHIVE) {
        const srcPath = SOURCE_ARCHIVE_ROOT + '\\' + c.FileName.replace(/\//g, '\\');
        const realSrc = srcPath.startsWith('\\\\') ? srcPath : ('\\\\' + srcPath.replace(/^\\+/, ''));
        try {
          buf = await fsp.readFile(realSrc);
        } catch (e) {
          throw new Error(`Source missing or unreadable: ${realSrc} (${e.message})`);
        }
        await fsp.writeFile(realOut, buf);
        if (DELETE_SOURCE) {
          try { await fsp.unlink(realSrc); } catch (e) { /* non-fatal */ }
        }
      } else {
        const base = c.URL;
        const fullUrl = base.endsWith('/') ? `${base}${c.FileName}` : `${base}/${c.FileName}`;
        buf = await downloadToBuffer(fullUrl);
        if (!buf || buf.length === 0) throw new Error('Empty response');
        await fsp.writeFile(realOut, buf);
      }
      written++;
    } catch (e) {
      failures.push({ AccessoryID: c.AccessoryID, FileName: c.FileName, URL: c.URL, error: e.message });
    } finally {
      processed++;
      if (processed % 50 === 0) log(`Progress: ${processed}/${totalToProcess} (written=${written}, skipped=${skipped}, failures=${failures.length})`);
    }
  })));

  // Write failures CSV if any
  if (failures.length) {
    const csvHeader = 'AccessoryID,FileName,URL,Error\n';
    const csvBody = failures.map(f => `${f.AccessoryID},"${f.FileName}",${f.URL},"${f.error.replace(/"/g, "'")}"`).join('\n');
    const outCsv = path.join('logs', 'clean_accessory_images_failures.csv');
    await ensureDir('logs');
    await fsp.writeFile(outCsv, csvHeader + csvBody, 'utf8');
    err(`Completed with ${failures.length} failures. Details: ${outCsv}`);
  }

  log(`Done. Candidates=${totalToProcess}, written=${written}, skipped=${skipped}, failures=${failures.length}`);
}

main().catch(e => {
  err('Fatal error:', e.message);
  process.exit(1);
});

