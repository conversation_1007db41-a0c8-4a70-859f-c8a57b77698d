// createTableInSupabase.js - <PERSON><PERSON>t to create a table in Supabase based on DBF structure

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';
import analyzeDbfStructure from './analyzeDbfStructure.js';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Get the DBF file path from command line or environment variable
const dbfFilePath = process.argv[2] || process.env.DBF_FILE_PATH || './data/daily_import.dbf';

/**
 * Creates a table in Supabase based on DBF file structure
 */
async function createTableInSupabase() {
  try {
    console.log('Starting table creation process...');

    // First, analyze the DBF structure
    console.log('Analyzing DBF file structure...');
    const analysis = await analyzeDbfStructure();

    if (!analysis.success) {
      throw new Error(`Failed to analyze DBF structure: ${analysis.error}`);
    }

    // Read the generated SQL file
    const sqlFilePath = analysis.sqlFilePath;
    console.log(`Reading SQL from: ${sqlFilePath}`);
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL in Supabase
    console.log('Executing SQL in Supabase...');
    const { error } = await supabase.rpc('exec_sql', { sql_query: sql });

    if (error) {
      throw new Error(`Error executing SQL: ${error.message}`);
    }

    console.log('Table created successfully in Supabase!');

    return {
      success: true,
      message: 'Table created successfully in Supabase',
      sqlExecuted: sql
    };
  } catch (error) {
    console.error(`Error creating table: ${error.message}`);
    console.error(error.stack);

    return {
      success: false,
      error: error.message
    };
  }
}

// Run the table creation if this script is executed directly
// For ES modules, we check if the import.meta.url is the same as the process.argv[1]
if (import.meta.url === `file://${process.argv[1]}`) {
  createTableInSupabase()
    .then(result => {
      if (result.success) {
        console.log('Table creation completed successfully.');
        process.exit(0);
      } else {
        console.error('Table creation failed.');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Unhandled error during table creation:', error);
      process.exit(1);
    });
}

// Export the function for use in other scripts
export default createTableInSupabase;
