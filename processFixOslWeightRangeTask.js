import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Shopify API configuration
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
const productsEndpoint = shopifyEndpoint.replace("graphql.json", "products.json");

// Rate limiting
const RATE_LIMIT_DELAY = 500; // 500ms between requests

/**
 * Sleep function for rate limiting
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Execute Shopify GraphQL query
 */
async function executeShopifyGraphQL(query, variables = {}) {
  const response = await fetch(shopifyEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken,
    },
    body: JSON.stringify({ query, variables }),
  });

  const result = await response.json();
  
  if (result.errors) {
    throw new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`);
  }
  
  return result.data;
}

/**
 * Find Shopify product by OSL variant SKU using GraphQL
 * @param {string} sku - The variant SKU to search for (format: OS{osl_id})
 * @returns {Object|null} - Product data or null if not found
 */
async function findOslProductBySku(sku) {
  try {
    console.log(`🔍 Finding Shopify OSL product for variant SKU: ${sku}`);
    
    const query = `
      query getVariantBySku($query: String!) {
        productVariants(first: 1, query: $query) {
          edges {
            node {
              id
              sku
              product {
                id
                title
                tags
              }
            }
          }
        }
      }
    `;

    const variables = {
      query: `sku:${sku}`
    };

    const result = await executeShopifyGraphQL(query, variables);

    if (!result.productVariants.edges.length) {
      console.log(`❌ No OSL product found for variant SKU: ${sku}`);
      return null;
    }

    const variant = result.productVariants.edges[0].node;
    const productId = variant.product.id.split('/').pop(); // Extract numeric ID
    
    console.log(`✅ Found OSL product ${productId} (variant ${variant.id}) with SKU ${sku}`);
    
    return {
      productId,
      variantId: variant.id,
      title: variant.product.title,
      currentTags: variant.product.tags
    };
  } catch (error) {
    console.error(`❌ Error finding OSL product for variant SKU ${sku}:`, error.message);
    return null;
  }
}

/**
 * Update Shopify product tags
 * @param {string} productId - The product ID
 * @param {Array} newTags - Array of new tags
 * @returns {Object} - Update result
 */
async function updateProductTags(productId, newTags) {
  try {
    console.log(`🔄 Updating OSL product ${productId} with tags: ${newTags.join(', ')}`);
    
    const updateEndpoint = `${productsEndpoint.replace('.json', '')}/${productId}.json`;
    
    const payload = {
      product: {
        id: productId,
        tags: newTags.join(',')
      }
    };

    const response = await fetch(updateEndpoint, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Shopify API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const updatedProduct = await response.json();
    console.log(`✅ Successfully updated OSL product ${productId} tags`);
    
    return {
      success: true,
      product: updatedProduct.product
    };
    
  } catch (error) {
    console.error(`❌ Error updating OSL product ${productId}:`, error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Remove existing weight range tags from tag array
 * @param {Array} tags - Current tags array
 * @returns {Array} - Tags with weight range tags removed
 */
function removeExistingWeightRangeTags(tags) {
  const weightRangePattern = /^wt_rng_\d+-\d+$/;
  return tags.filter(tag => !weightRangePattern.test(tag));
}

/**
 * Calculate weight range tags for OSL products based on their weight range
 * @param {number} minWeight - The minimum weight
 * @param {number} maxWeight - The maximum weight
 * @returns {Array} - Array of weight range tags that overlap with the OSL range
 */
function calculateOslWeightRangeTags(minWeight, maxWeight) {
  const weightRangeTags = [];
  
  // Check each possible weight range to see if it overlaps with the OSL range
  const possibleRanges = [
    { min: 10, max: 49.5, tag: 'wt_rng_10-49' },
    { min: 50, max: 99.5, tag: 'wt_rng_50-99' },
    { min: 100, max: 119.5, tag: 'wt_rng_100-119' },
    { min: 120, max: 139.5, tag: 'wt_rng_120-139' },
    { min: 140, max: 149.5, tag: 'wt_rng_140-149' },
    { min: 150, max: 159.5, tag: 'wt_rng_150-159' },
    { min: 160, max: 169.5, tag: 'wt_rng_160-169' },
    { min: 170, max: 174.5, tag: 'wt_rng_170-174' },
    { min: 175, max: 180.5, tag: 'wt_rng_175-180' },
    { min: 181, max: 200, tag: 'wt_rng_181-200' },
    { min: 201, max: 249, tag: 'wt_rng_201-249' }
  ];
  
  for (const range of possibleRanges) {
    // Check if ranges overlap: OSL range overlaps with weight range if max of one >= min of other
    if (maxWeight >= range.min && minWeight <= range.max) {
      weightRangeTags.push(range.tag);
    }
  }
  
  return weightRangeTags;
}

/**
 * Process a fix_osl_weight_range task
 * @param {Object} task - The task object
 * @param {Object} context - Context object with supabase, updateTaskStatus, logError
 */
async function processFixOslWeightRangeTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processFixOslWeightRangeTask] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      if (typeof task.payload === 'object' && task.payload !== null) {
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        payload = JSON.parse(task.payload);
      } else {
        throw new Error('Invalid payload format');
      }
    } catch (err) {
      const errMsg = `[processFixOslWeightRangeTask] Error parsing task payload: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, 'Parsing task payload');
      await updateTaskStatus(task.id, 'failed', { error: errMsg });
      return;
    }

    const { osl_id, min_weight, max_weight, expected_weight_range_tags } = payload;
    
    if (!osl_id || !min_weight || !max_weight || !expected_weight_range_tags) {
      const errMsg = `[processFixOslWeightRangeTask] Missing required payload fields: osl_id=${osl_id}, min_weight=${min_weight}, max_weight=${max_weight}, expected_weight_range_tags=${expected_weight_range_tags}`;
      console.error(errMsg);
      await updateTaskStatus(task.id, 'failed', { error: errMsg });
      return;
    }

    // Validate that the expected weight range tags match what we calculate
    const calculatedTags = calculateOslWeightRangeTags(min_weight, max_weight);
    const expectedTagsSet = new Set(expected_weight_range_tags);
    const calculatedTagsSet = new Set(calculatedTags);
    
    if (expectedTagsSet.size !== calculatedTagsSet.size || 
        ![...expectedTagsSet].every(tag => calculatedTagsSet.has(tag))) {
      const errMsg = `[processFixOslWeightRangeTask] Weight range tags mismatch: expected [${expected_weight_range_tags.join(', ')}], calculated [${calculatedTags.join(', ')}] for weight range ${min_weight}g-${max_weight}g`;
      console.error(errMsg);
      await updateTaskStatus(task.id, 'failed', { error: errMsg });
      return;
    }

    console.log(`[processFixOslWeightRangeTask] Processing OSL ${osl_id} with weight range ${min_weight}g-${max_weight}g, expected tags: [${expected_weight_range_tags.join(', ')}]`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Generate SKU for the OSL (format: OS{osl_id})
    const sku = `OS${osl_id}`;
    console.log(`[processFixOslWeightRangeTask] Looking for Shopify OSL product with SKU: ${sku}`);

    // Find the product in Shopify
    const shopifyProduct = await findOslProductBySku(sku);
    await sleep(RATE_LIMIT_DELAY);

    if (!shopifyProduct) {
      const errMsg = `OSL product not found in Shopify for SKU: ${sku}`;
      console.log(`[processFixOslWeightRangeTask] ${errMsg} - marking OSL as not uploaded`);

      // Update the OSL record to mark it as not uploaded since it's not found in Shopify
      const { error: updateError } = await supabase
        .from('t_order_sheet_lines')
        .update({ shopify_uploaded_at: null })
        .eq('id', osl_id);

      if (updateError) {
        console.error(`[processFixOslWeightRangeTask] Error updating OSL ${osl_id} shopify_uploaded_at: ${updateError.message}`);
        await updateTaskStatus(task.id, 'failed', {
          error: `${errMsg} and failed to update OSL record: ${updateError.message}`,
          osl_id: osl_id,
          sku: sku
        });
        return;
      }

      console.log(`[processFixOslWeightRangeTask] Successfully marked OSL ${osl_id} as not uploaded`);
      await updateTaskStatus(task.id, 'completed', {
        message: `OSL product not found in Shopify - marked as not uploaded`,
        osl_id: osl_id,
        sku: sku,
        action: 'marked_not_uploaded'
      });
      return;
    }

    // Check if all expected weight range tags are already present
    const currentTags = shopifyProduct.currentTags;
    const hasAllExpectedTags = expected_weight_range_tags.every(tag => currentTags.includes(tag));
    
    if (hasAllExpectedTags) {
      console.log(`[processFixOslWeightRangeTask] OSL product ${shopifyProduct.productId} already has all correct weight range tags: [${expected_weight_range_tags.join(', ')}]`);
      await updateTaskStatus(task.id, 'completed', {
        message: `OSL product already has all correct weight range tags: [${expected_weight_range_tags.join(', ')}]`,
        osl_id: osl_id,
        sku: sku,
        product_id: shopifyProduct.productId,
        weight_range_tags: expected_weight_range_tags,
        action: 'no_change_needed'
      });
      return;
    }

    // Remove any existing weight range tags and add the new ones
    const tagsWithoutWeightRange = removeExistingWeightRangeTags(currentTags);
    const newTags = [...tagsWithoutWeightRange, ...expected_weight_range_tags];
    
    console.log(`[processFixOslWeightRangeTask] Updating tags from [${currentTags.join(', ')}] to [${newTags.join(', ')}]`);

    // Update the product tags
    const updateResult = await updateProductTags(shopifyProduct.productId, newTags);
    await sleep(RATE_LIMIT_DELAY);

    if (updateResult.success) {
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully updated weight range tags to: [${expected_weight_range_tags.join(', ')}]`,
        osl_id: osl_id,
        sku: sku,
        product_id: shopifyProduct.productId,
        weight_range_tags: expected_weight_range_tags,
        old_tags: currentTags,
        new_tags: newTags,
        action: 'updated'
      });
      console.log(`[processFixOslWeightRangeTask] Successfully completed weight range update for OSL ${osl_id}`);
    } else {
      const errMsg = `Failed to update Shopify OSL product tags: ${updateResult.error}`;
      console.error(`[processFixOslWeightRangeTask] ${errMsg}`);
      await updateTaskStatus(task.id, 'failed', {
        error: errMsg,
        osl_id: osl_id,
        sku: sku,
        product_id: shopifyProduct.productId
      });
    }

  } catch (err) {
    const errMsg = `[processFixOslWeightRangeTask] Exception processing task: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, 'Processing fix_osl_weight_range task');
    await updateTaskStatus(task.id, 'failed', { error: errMsg });
  }
}

export { processFixOslWeightRangeTask };
