const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize clients
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!supabaseUrl || !supabaseKey || !veeqoApiKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

console.log('🗑️  VEEQO CHANNEL PRODUCT (LISTING) MANAGER');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    // For DELETE requests, there might not be a JSON response
    if (method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to search channel products by SKU
async function searchChannelProductsBySku(sku) {
  console.log(`🔍 Searching channel products for SKU: ${sku}`);
  
  let page = 1;
  const pageSize = 100;
  let foundProducts = [];
  
  while (page <= 10) { // Limit to 10 pages for safety
    console.log(`   📄 Checking page ${page}...`);
    
    const result = await makeVeeqoRequest(`https://api.veeqo.com/channel_products?page=${page}&page_size=${pageSize}`);
    
    if (!result.success) {
      console.error(`   ❌ Error fetching page ${page}: ${result.error}`);
      break;
    }
    
    const { items, total_count } = result.data;
    console.log(`   📊 Page ${page}: ${items.length} items (total: ${total_count})`);
    
    // Search for matching SKUs in channel sellables
    const matchingProducts = items.filter(channelProduct => {
      if (channelProduct.channel_sellables) {
        return channelProduct.channel_sellables.some(cs => cs.remote_sku === sku);
      }
      return false;
    });
    
    if (matchingProducts.length > 0) {
      console.log(`   ✅ Found ${matchingProducts.length} matching channel product(s) on page ${page}`);
      foundProducts.push(...matchingProducts);
    }
    
    // If we've processed all items, break
    if (items.length < pageSize) {
      console.log(`   📋 Reached end of results on page ${page}`);
      break;
    }
    
    page++;
  }
  
  console.log(`\n📊 Total found: ${foundProducts.length} channel product(s) with SKU: ${sku}`);
  return foundProducts;
}

// Function to display channel product details
function displayChannelProduct(channelProduct, index) {
  console.log(`\n📋 Channel Product ${index + 1}:`);
  console.log(`   ID: ${channelProduct.id}`);
  console.log(`   Status: ${channelProduct.status}`);
  console.log(`   Channel: ${channelProduct.channel?.name || 'Unknown'} (${channelProduct.channel?.short_name || 'N/A'})`);
  console.log(`   Remote ID: ${channelProduct.remote_id}`);
  console.log(`   Remote Title: ${channelProduct.remote_title}`);
  console.log(`   Created: ${channelProduct.created_at}`);
  console.log(`   Updated: ${channelProduct.updated_at}`);
  
  if (channelProduct.channel_sellables && channelProduct.channel_sellables.length > 0) {
    console.log(`   Channel Sellables (${channelProduct.channel_sellables.length}):`);
    channelProduct.channel_sellables.forEach((cs, csIndex) => {
      console.log(`      ${csIndex + 1}. ID: ${cs.id}, Remote SKU: ${cs.remote_sku}, Price: ${cs.remote_price}`);
    });
  }
}

// Function to delete a channel product
async function deleteChannelProduct(channelProductId, dryRun = true) {
  console.log(`\n🗑️  ${dryRun ? 'DRY RUN: Would delete' : 'Deleting'} channel product ID: ${channelProductId}`);
  
  if (dryRun) {
    console.log(`   💡 Would send: DELETE https://api.veeqo.com/channel_products/${channelProductId}`);
    return { success: true, reason: 'Dry run - no actual deletion' };
  }
  
  const result = await makeVeeqoRequest(`https://api.veeqo.com/channel_products/${channelProductId}`, 'DELETE');
  
  if (result.success) {
    console.log(`   ✅ Successfully deleted channel product ${channelProductId}`);
    return { success: true, reason: 'Deleted successfully' };
  } else {
    console.error(`   ❌ Failed to delete channel product ${channelProductId}: ${result.error}`);
    return { success: false, error: result.error };
  }
}

// Function to get SDASIN record
async function getSdasinRecord(sdasinId) {
  const { data, error } = await supabase
    .from('t_sdasins')
    .select('id, fbm_sku, fbm_uploaded_at, asin, parent_asin')
    .eq('id', sdasinId)
    .single();
  
  if (error) {
    console.error(`❌ Error fetching SDASIN record: ${error.message}`);
    return null;
  }
  
  return data;
}

// Function to find all SDASINS with deleted Amazon listings
async function findDeletedAmazonListings() {
  console.log('🔍 Finding SDASINS with deleted Amazon listings (fbm_uploaded_at = null)...');
  
  const { data, error } = await supabase
    .from('t_sdasins')
    .select('id, fbm_sku, fbm_uploaded_at, asin, parent_asin')
    .is('fbm_uploaded_at', null)
    .not('fbm_sku', 'is', null)
    .limit(20); // Limit for safety
  
  if (error) {
    console.error(`❌ Error fetching deleted Amazon listings: ${error.message}`);
    return [];
  }
  
  console.log(`📊 Found ${data.length} SDASINS with deleted Amazon listings`);
  return data;
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🛠️  USAGE:
  node veeqoChannelProductManager.cjs <command> [options]

📋 COMMANDS:
  search-sku <sku>                    - Search for channel products by SKU
  check-sdasin <sdasin_id>           - Check a specific SDASIN for channel products
  delete-channel-product <id>        - Delete a specific channel product
  find-deleted-amazon                - Find SDASINS with deleted Amazon listings
  delete-for-sdasin <sdasin_id>      - Delete channel products for a specific SDASIN
  delete-all-deleted-amazon [--confirm] - Delete channel products for all deleted Amazon listings

📝 EXAMPLES:
  node veeqoChannelProductManager.cjs search-sku DISC_46948
  node veeqoChannelProductManager.cjs check-sdasin 46948
  node veeqoChannelProductManager.cjs delete-channel-product 123456
  node veeqoChannelProductManager.cjs delete-for-sdasin 46948
  node veeqoChannelProductManager.cjs delete-all-deleted-amazon --confirm

⚠️  WARNING: Deletion operations are permanent!
`);
    return;
  }
  
  const command = args[0];
  
  try {
    switch (command) {
      case 'search-sku':
        if (args.length < 2) {
          console.error('❌ Please provide a SKU');
          return;
        }
        const sku = args[1];
        const channelProducts = await searchChannelProductsBySku(sku);
        
        if (channelProducts.length > 0) {
          channelProducts.forEach((cp, index) => displayChannelProduct(cp, index));
          
          console.log(`\n💡 To delete a channel product, use:`);
          console.log(`node veeqoChannelProductManager.cjs delete-channel-product <channel_product_id>`);
        }
        break;
        
      case 'check-sdasin':
        if (args.length < 2) {
          console.error('❌ Please provide a SDASIN ID');
          return;
        }
        const sdasinId = parseInt(args[1]);
        const sdasin = await getSdasinRecord(sdasinId);
        
        if (!sdasin) {
          console.error('❌ SDASIN not found');
          return;
        }
        
        console.log(`📋 SDASIN ${sdasin.id}: ${sdasin.fbm_sku} (fbm_uploaded_at: ${sdasin.fbm_uploaded_at})`);
        
        if (sdasin.fbm_sku) {
          const channelProducts = await searchChannelProductsBySku(sdasin.fbm_sku);
          if (channelProducts.length > 0) {
            channelProducts.forEach((cp, index) => displayChannelProduct(cp, index));
          }
        }
        break;
        
      case 'delete-channel-product':
        if (args.length < 2) {
          console.error('❌ Please provide a channel product ID');
          return;
        }
        const channelProductId = args[1];
        await deleteChannelProduct(channelProductId, false); // Actually delete
        break;
        
      case 'find-deleted-amazon':
        const deletedListings = await findDeletedAmazonListings();
        if (deletedListings.length > 0) {
          console.log(`\n📋 SDASINS with deleted Amazon listings:`);
          deletedListings.forEach(sdasin => {
            console.log(`   - ID: ${sdasin.id}, SKU: ${sdasin.fbm_sku}, ASIN: ${sdasin.asin}`);
          });
        }
        break;
        
      case 'delete-for-sdasin':
        if (args.length < 2) {
          console.error('❌ Please provide a SDASIN ID');
          return;
        }
        const targetSdasinId = parseInt(args[1]);
        const targetSdasin = await getSdasinRecord(targetSdasinId);
        
        if (!targetSdasin) {
          console.error('❌ SDASIN not found');
          return;
        }
        
        if (targetSdasin.fbm_uploaded_at !== null) {
          console.log(`⚠️  SDASIN ${targetSdasin.id} still has fbm_uploaded_at set - are you sure you want to delete?`);
        }
        
        console.log(`🔍 Searching for channel products for SDASIN ${targetSdasin.id} (${targetSdasin.fbm_sku})...`);
        const channelProductsToDelete = await searchChannelProductsBySku(targetSdasin.fbm_sku);
        
        if (channelProductsToDelete.length === 0) {
          console.log(`✅ No channel products found for ${targetSdasin.fbm_sku}`);
          return;
        }
        
        console.log(`🎯 Found ${channelProductsToDelete.length} channel product(s) to delete:`);
        channelProductsToDelete.forEach((cp, index) => displayChannelProduct(cp, index));
        
        console.log(`\n🗑️  Deleting ${channelProductsToDelete.length} channel product(s)...`);
        
        let deletedCount = 0;
        for (const cp of channelProductsToDelete) {
          const result = await deleteChannelProduct(cp.id, false);
          if (result.success) {
            deletedCount++;
          }
          
          // Add delay between deletions
          if (channelProductsToDelete.length > 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
        
        console.log(`\n📊 Successfully deleted ${deletedCount}/${channelProductsToDelete.length} channel products`);
        break;
        
      case 'delete-all-deleted-amazon':
        const confirmFlag = args.includes('--confirm');
        if (!confirmFlag) {
          console.error('❌ This will delete channel products! Use --confirm flag to proceed');
          return;
        }
        
        const allDeletedListings = await findDeletedAmazonListings();
        if (allDeletedListings.length === 0) {
          console.log('✅ No SDASINS with deleted Amazon listings found');
          return;
        }
        
        console.log(`🚨 Processing ${allDeletedListings.length} SDASINS for channel product deletion...`);
        
        let totalProcessed = 0;
        let totalDeleted = 0;
        
        for (const sdasin of allDeletedListings) {
          console.log(`\n📋 Processing SDASIN ${sdasin.id} (${sdasin.fbm_sku})...`);
          
          const channelProducts = await searchChannelProductsBySku(sdasin.fbm_sku);
          
          if (channelProducts.length > 0) {
            console.log(`   Found ${channelProducts.length} channel product(s) to delete`);
            
            for (const cp of channelProducts) {
              const result = await deleteChannelProduct(cp.id, false);
              if (result.success) {
                totalDeleted++;
              }
            }
          }
          
          totalProcessed++;
          
          // Add delay between SDASINS
          if (totalProcessed < allDeletedListings.length) {
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
        
        console.log(`\n📊 FINAL SUMMARY:`);
        console.log(`✅ Processed ${totalProcessed} SDASINS`);
        console.log(`🗑️  Deleted ${totalDeleted} channel products`);
        break;
        
      default:
        console.error(`❌ Unknown command: ${command}`);
        console.log('Use no arguments to see usage instructions.');
    }
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the main function
main();
