require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testUpdatedMatchOslIncludingSold() {
  try {
    console.log('Testing updated match_osl_to_discs task that includes sold discs...');
    
    // Use OSL 18977 which should now match disc 404583 (sold disc)
    const oslId = 18977;
    
    console.log(`\n=== BEFORE TEST - OSL ${oslId} STATUS ===`);
    
    // Check current OSL details
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, min_weight, max_weight, color_id, vendor_id')
      .eq('id', oslId)
      .single();
    
    if (oslError) {
      console.error('Error getting OSL:', oslError);
      return;
    }
    
    console.log(`OSL ${oslId}: MPS ${osl.mps_id}, Weight ${osl.min_weight}-${osl.max_weight}g, Color ${osl.color_id}`);
    
    // Clear existing mappings for clean test
    console.log('\nClearing existing mappings for clean test...');
    await supabase
      .from('t_discs')
      .update({ order_sheet_line_id: null })
      .eq('order_sheet_line_id', oslId);
    
    await supabase
      .from('t_discs')
      .update({ vendor_osl_id: null })
      .eq('vendor_osl_id', oslId);
    
    // Check expected matches (including sold discs)
    console.log('\n=== EXPECTED MATCHES (including sold discs) ===');
    
    // Regular weight matches (including sold)
    const { data: expectedRegular, error: expRegError } = await supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, color_id, sold_date')
      .eq('mps_id', osl.mps_id)
      .gte('weight', osl.min_weight)
      .lte('weight', osl.max_weight)
      .in('color_id', osl.color_id === 23 ? [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23] : [osl.color_id, 23])
      .limit(10);
    
    if (!expRegError) {
      console.log(`Expected regular matches (all discs): ${expectedRegular.length}`);
      expectedRegular.forEach(disc => {
        const soldStatus = disc.sold_date ? `SOLD (${disc.sold_date})` : 'UNSOLD';
        console.log(`  Disc ${disc.id}: Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id}, ${soldStatus}`);
      });
    }
    
    // Vendor weight matches (including sold)
    const { data: expectedVendor, error: expVendorError } = await supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, color_id, sold_date')
      .eq('mps_id', osl.mps_id)
      .not('weight_mfg', 'is', null)
      .in('color_id', osl.color_id === 23 ? [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23] : [osl.color_id, 23])
      .limit(10);
    
    if (!expVendorError) {
      const filteredVendor = expectedVendor.filter(disc => {
        const roundedWeightMfg = Math.round(disc.weight_mfg);
        return roundedWeightMfg >= osl.min_weight && roundedWeightMfg <= osl.max_weight;
      });
      
      console.log(`Expected vendor matches (all discs): ${filteredVendor.length}`);
      filteredVendor.forEach(disc => {
        const roundedWeightMfg = Math.round(disc.weight_mfg);
        const soldStatus = disc.sold_date ? `SOLD (${disc.sold_date})` : 'UNSOLD';
        console.log(`  Disc ${disc.id}: Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g (rounded: ${roundedWeightMfg}g), Color ${disc.color_id}, ${soldStatus}`);
      });
    }
    
    // Create a new match_osl_to_discs task
    console.log(`\n=== CREATING TEST TASK ===`);
    const { data: taskData, error: taskError } = await supabase
      .from('t_task_queue')
      .insert([
        {
          task_type: 'match_osl_to_discs',
          payload: { id: oslId },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: 'test_dual_mapping_including_sold'
        }
      ])
      .select();
    
    if (taskError) {
      console.error('Error creating test task:', taskError);
      return;
    }
    
    const testTaskId = taskData[0].id;
    console.log(`✅ Created test task ${testTaskId} for OSL ${oslId}`);
    
    console.log('\n🔄 Task created and ready for processing by the worker daemon.');
    console.log('The updated match_osl_to_discs task should now:');
    console.log('1. Find ALL discs (sold and unsold) matching using regular weight → update order_sheet_line_id');
    console.log('2. Find ALL discs (sold and unsold) matching using manufacturer weight → update vendor_osl_id');
    console.log('3. Update t_inv_osl with count of UNSOLD regular matches only (for inventory)');
    console.log('4. Report both types of matches in the task result');
    
    console.log('\n📋 To check results after processing:');
    console.log(`
SELECT 
  id, 
  status, 
  result->>'message' as message,
  result->>'discs_matched_regular' as regular_matches,
  result->>'discs_matched_vendor' as vendor_matches,
  processed_at
FROM t_task_queue 
WHERE id = ${testTaskId};
    `);
    
    console.log('\nTo see the actual disc mappings:');
    console.log(`
-- Regular mappings (order_sheet_line_id) - should include disc 404583
SELECT id, weight, weight_mfg, color_id, sold_date 
FROM t_discs 
WHERE order_sheet_line_id = ${oslId}
ORDER BY sold_date NULLS FIRST;

-- Vendor mappings (vendor_osl_id) - should also include disc 404583
SELECT id, weight, weight_mfg, color_id, sold_date 
FROM t_discs 
WHERE vendor_osl_id = ${oslId}
ORDER BY sold_date NULLS FIRST;
    `);
    
    console.log('\nTo check inventory count (should only count unsold discs):');
    console.log(`
SELECT id, available_quantity 
FROM t_inv_osl 
WHERE id = ${oslId};
    `);
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

testUpdatedMatchOslIncludingSold();
