require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testDifferentWeights() {
  try {
    console.log('Testing OSL mapping with different weight vs weight_mfg values...');
    
    // Find a disc where weight and weight_mfg might map to different OSLs
    const { data: testDiscs, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null)
      // We'll filter for different weights in JavaScript since Supabase doesn't support column comparison directly
      .limit(5);
    
    if (discError) {
      console.error('Error finding test discs:', discError);
      return;
    }
    
    // Filter for discs where weight and weight_mfg are different
    const differentWeightDiscs = testDiscs.filter(disc =>
      disc.weight !== disc.weight_mfg &&
      Math.abs(disc.weight - disc.weight_mfg) > 0.1 // At least 0.1g difference
    );

    console.log(`Found ${differentWeightDiscs.length} discs with significantly different weight vs weight_mfg values`);

    const discsToTest = differentWeightDiscs.length > 0 ? differentWeightDiscs : testDiscs.slice(0, 3);

    for (const disc of discsToTest) {
      console.log(`\n=== Testing Disc ${disc.id} ===`);
      console.log(`Weight: ${disc.weight}, Weight MFG: ${disc.weight_mfg}`);
      
      // Test regular OSL mapping
      const { data: regularOslData, error: regularOslError } = await supabase.rpc(
        'find_matching_osl',
        {
          mps_id_param: disc.mps_id,
          color_id_param: disc.color_id,
          weight_param: disc.weight
        }
      );
      
      // Test vendor OSL mapping
      const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
        'find_matching_osl_by_mfg_weight',
        {
          mps_id_param: disc.mps_id,
          color_id_param: disc.color_id,
          weight_mfg_param: disc.weight_mfg
        }
      );
      
      const regularOslId = regularOslData && regularOslData.length > 0 ? regularOslData[0].osl_id : null;
      const vendorOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
      
      console.log(`Regular OSL (weight ${disc.weight}): ${regularOslId}`);
      console.log(`Vendor OSL (weight_mfg ${disc.weight_mfg}): ${vendorOslId}`);
      
      if (regularOslId !== vendorOslId) {
        console.log('🎯 DIFFERENT MAPPINGS FOUND! This demonstrates the dual mapping feature.');
        
        // Get details about both OSLs
        if (regularOslId) {
          const { data: regularOslDetails } = await supabase
            .from('t_order_sheet_lines')
            .select('id, min_weight, max_weight, mps_id, color_id')
            .eq('id', regularOslId)
            .single();
          console.log(`  Regular OSL ${regularOslId}: weight range ${regularOslDetails.min_weight}-${regularOslDetails.max_weight}`);
        }
        
        if (vendorOslId) {
          const { data: vendorOslDetails } = await supabase
            .from('t_order_sheet_lines')
            .select('id, min_weight, max_weight, mps_id, color_id')
            .eq('id', vendorOslId)
            .single();
          console.log(`  Vendor OSL ${vendorOslId}: weight range ${vendorOslDetails.min_weight}-${vendorOslDetails.max_weight}`);
        }
        
        break; // Found a good example, stop here
      } else {
        console.log('Same OSL for both weights');
      }
    }
    
    console.log('\n✅ Test completed!');
    
  } catch (err) {
    console.error('Error:', err.message);
  }
}

testDifferentWeights();
