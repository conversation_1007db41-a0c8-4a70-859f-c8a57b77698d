// Test dashboard API endpoints
async function testDashboardAPI() {
  console.log('🧪 Testing Dashboard API Endpoints...\n');

  // Test 1: Worker status
  console.log('1️⃣ Testing /api/worker/status...');
  try {
    const response = await fetch('http://localhost:3001/api/worker/status');
    const data = await response.json();
    console.log('✅ Worker Status:', data.status);
    console.log('   - Pending Tasks:', data.pendingTasksCount);
    console.log('   - Future Tasks:', data.futureTasksCount);
  } catch (err) {
    console.log('❌ Worker status failed:', err.message);
  }

  // Test 2: Tasks by type
  console.log('\n2️⃣ Testing /api/tasks/by-type...');
  try {
    const response = await fetch('http://localhost:3001/api/tasks/by-type');
    const data = await response.json();
    console.log('✅ Tasks by type loaded');
    console.log('   - Pending types:', Object.keys(data.taskTypes?.pending || {}).length);
    console.log('   - Future types:', Object.keys(data.taskTypes?.future || {}).length);
  } catch (err) {
    console.log('❌ Tasks by type failed:', err.message);
  }

  // Test 3: Basic page load
  console.log('\n3️⃣ Testing page accessibility...');
  try {
    const response = await fetch('http://localhost:3001/admin.html');
    if (response.ok) {
      console.log('✅ Admin page loads successfully');
    } else {
      console.log('❌ Admin page failed to load');
    }
  } catch (err) {
    console.log('❌ Page load failed:', err.message);
  }

  console.log('\n🎉 Dashboard API Test Complete!');
}

testDashboardAPI().catch(console.error);
