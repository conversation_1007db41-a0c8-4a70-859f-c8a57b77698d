// check_task_177954.js
// Check the result of task 177954 for OSL 18533

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkTaskResult() {
  try {
    console.log('Checking result of task 177959 for OSL 18533...');
    
    const { data: task, error } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 177959)
      .single();
    
    if (error) {
      console.error('Error fetching task:', error);
      return;
    }
    
    console.log('Task Status:', task.status);
    console.log('Processed At:', task.processed_at);
    
    if (task.result) {
      const result = typeof task.result === 'string' ? JSON.parse(task.result) : task.result;
      console.log('\n📋 Task Result:');
      console.log('Message:', result.message);
      console.log('Error:', result.error);
      
      if (result.error && result.error.includes('MANUAL FIX REQUIRED')) {
        console.log('\n🎉 SUCCESS! The MANUAL FIX REQUIRED error was properly extracted!');
        console.log('✅ Error extraction is now working correctly');
      } else if (result.error === 'Unknown error') {
        console.log('\n❌ STILL FAILED: Still showing "Unknown error"');
        console.log('Need to debug further...');
      } else {
        console.log('\n⚠️  Different error extracted:', result.error);
      }
      
      // Show additional details
      if (result.exit_code) console.log('Exit Code:', result.exit_code);
      if (result.stdout_length) console.log('Stdout Length:', result.stdout_length);
      if (result.stderr_length) console.log('Stderr Length:', result.stderr_length);
    } else {
      console.log('No result found');
    }
    
    // Also check the OSL notes
    const { data: osl } = await supabase
      .from('t_order_sheet_lines')
      .select('shopify_product_uploaded_notes')
      .eq('id', 18533)
      .single();
    
    console.log('\n📝 OSL Notes:');
    console.log(osl.shopify_product_uploaded_notes);
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

checkTaskResult();
