import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testNewParsingFixes() {
  try {
    console.log('🧪 Testing new Discraft parsing fixes...\n');
    
    // Test the new API endpoint for unmatched Discraft products
    console.log('1. Testing new "Review Unmatched Discraft Products" endpoint...');
    
    const response = await fetch('http://localhost:3001/api/discraft/review-unmatched-discraft-products');
    
    if (!response.ok) {
      console.error(`❌ HTTP Error: ${response.status} ${response.statusText}`);
      return;
    }
    
    const data = await response.json();
    
    if (data.success) {
      console.log(`✅ Found ${data.totalUnmatchedDiscraftProducts} total unmatched Discraft products`);
      console.log(`📋 Showing first ${data.unmatchedDiscraftProducts.length} products:\n`);
      
      data.unmatchedDiscraftProducts.slice(0, 10).forEach((product, index) => {
        console.log(`${index + 1}. ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        console.log(`   Weight: ${product.min_weight}-${product.max_weight}g, Available: ${product.is_currently_available}`);
        if (product.vendor_description) {
          console.log(`   Description: ${product.vendor_description.substring(0, 80)}...`);
        }
        console.log('');
      });
    } else {
      console.error(`❌ API Error: ${data.error}`);
    }
    
    // Test specific parsing cases by checking the database
    console.log('\n2. Testing specific parsing fixes in database...');
    
    // Test McBeth Hard Luna parsing (line 162)
    const { data: mcbethHardLuna, error: error1 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model')
      .ilike('raw_line_type', '%McBeth%')
      .ilike('raw_model', '%Hard Luna%')
      .limit(5);
    
    if (error1) {
      console.error('Error querying McBeth Hard Luna:', error1);
    } else if (mcbethHardLuna.length > 0) {
      console.log('✅ McBeth Hard Luna parsing:');
      mcbethHardLuna.forEach(item => {
        console.log(`   Raw: "${item.raw_line_type}" | "${item.raw_model}"`);
        console.log(`   Parsed: ${item.plastic_name} | ${item.mold_name} | ${item.stamp_name}`);
      });
    } else {
      console.log('⚪ No McBeth Hard Luna records found');
    }
    
    // Test Pierce Hard Fierce parsing (line 164)
    const { data: pierceHardFierce, error: error2 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model')
      .ilike('raw_line_type', '%Pierce%')
      .ilike('raw_model', '%Hard Fierce%')
      .limit(5);
    
    if (error2) {
      console.error('Error querying Pierce Hard Fierce:', error2);
    } else if (pierceHardFierce.length > 0) {
      console.log('\n✅ Pierce Hard Fierce parsing:');
      pierceHardFierce.forEach(item => {
        console.log(`   Raw: "${item.raw_line_type}" | "${item.raw_model}"`);
        console.log(`   Parsed: ${item.plastic_name} | ${item.mold_name} | ${item.stamp_name}`);
      });
    } else {
      console.log('\n⚪ No Pierce Hard Fierce records found');
    }
    
    // Test Pierce Fierce (Swirl) parsing (line 323)
    const { data: pierceFierce, error: error3 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model')
      .ilike('raw_line_type', '%Pierce%')
      .ilike('raw_model', '%Fierce%')
      .not('raw_model', 'ilike', '%Hard%')
      .limit(5);
    
    if (error3) {
      console.error('Error querying Pierce Fierce:', error3);
    } else if (pierceFierce.length > 0) {
      console.log('\n✅ Pierce Fierce (Swirl) parsing:');
      pierceFierce.forEach(item => {
        console.log(`   Raw: "${item.raw_line_type}" | "${item.raw_model}"`);
        console.log(`   Parsed: ${item.plastic_name} | ${item.mold_name} | ${item.stamp_name}`);
      });
    } else {
      console.log('\n⚪ No Pierce Fierce (non-Hard) records found');
    }
    
    // Test Titanium Zone GT parsing (line 398)
    const { data: titaniumZoneGT, error: error4 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model')
      .ilike('raw_line_type', '%Titanium%')
      .ilike('raw_model', '%Zone GT%')
      .limit(5);
    
    if (error4) {
      console.error('Error querying Titanium Zone GT:', error4);
    } else if (titaniumZoneGT.length > 0) {
      console.log('\n✅ Titanium Zone GT parsing:');
      titaniumZoneGT.forEach(item => {
        console.log(`   Raw: "${item.raw_line_type}" | "${item.raw_model}"`);
        console.log(`   Parsed: ${item.plastic_name} | ${item.mold_name} | ${item.stamp_name}`);
      });
    } else {
      console.log('\n⚪ No Titanium Zone GT records found');
    }
    
    // Test McBeth Big Z parsing
    const { data: mcbethBigZ, error: error5 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model')
      .ilike('raw_line_type', '%McBeth%')
      .ilike('raw_model', '%Big Z%')
      .limit(5);
    
    if (error5) {
      console.error('Error querying McBeth Big Z:', error5);
    } else if (mcbethBigZ.length > 0) {
      console.log('\n✅ McBeth Big Z parsing:');
      mcbethBigZ.forEach(item => {
        console.log(`   Raw: "${item.raw_line_type}" | "${item.raw_model}"`);
        console.log(`   Parsed: ${item.plastic_name} | ${item.mold_name} | ${item.stamp_name}`);
      });
    } else {
      console.log('\n⚪ No McBeth Big Z records found');
    }
    
    // Test weight range parsing (150 should become 150-159)
    const { data: weight150, error: error6 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('min_weight, max_weight, raw_weight_range')
      .eq('min_weight', 150)
      .eq('max_weight', 159)
      .limit(5);
    
    if (error6) {
      console.error('Error querying 150g weight range:', error6);
    } else if (weight150.length > 0) {
      console.log('\n✅ Weight range parsing (150 → 150-159):');
      weight150.forEach(item => {
        console.log(`   Raw: "${item.raw_weight_range}" → Parsed: ${item.min_weight}-${item.max_weight}g`);
      });
    } else {
      console.log('\n⚪ No 150-159g weight range records found');
    }
    
    console.log('\n🎉 Parsing fixes test completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testNewParsingFixes().catch(console.error);
