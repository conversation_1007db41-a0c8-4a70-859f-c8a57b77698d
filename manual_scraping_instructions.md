# Manual Scraping Instructions for Dynamic Discs Distribution

Due to the site's Cloudflare protection and Shopify authentication system, automated scraping is being blocked. Here are manual instructions to get the product data:

## Option 1: Manual Browser Export

1. **Login to the site manually:**
   - Go to https://discgolfdistribution.com/account/login
   - Login with: <EMAIL> / Sdisplatgun9!

2. **Access the products JSON endpoint:**
   - Once logged in, navigate to: https://discgolfdistribution.com/products.json?limit=250
   - This should show you the first 250 products in JSON format
   - Save this page as a .json file

3. **Get all pages:**
   - Continue with: https://discgolfdistribution.com/products.json?page=2&limit=250
   - Keep incrementing the page number until you get an empty products array
   - Save each page as a separate JSON file

4. **Filter for Dynamic Discs:**
   - Look for products where the "vendor" field contains "Dynamic" or similar

## Option 2: Browser Developer Tools Method

1. **Login to the site**
2. **Open Developer Tools (F12)**
3. **Go to Console tab**
4. **Run this JavaScript code:**

```javascript
async function getAllProducts() {
    let allProducts = [];
    let page = 1;
    let hasMore = true;
    
    while (hasMore) {
        console.log(`Fetching page ${page}...`);
        
        try {
            const response = await fetch(`/products.json?page=${page}&limit=250`);
            const data = await response.json();
            
            if (data.products && data.products.length > 0) {
                allProducts.push(...data.products);
                console.log(`Found ${data.products.length} products on page ${page}`);
                page++;
            } else {
                hasMore = false;
                console.log('No more products found');
            }
        } catch (error) {
            console.error('Error:', error);
            hasMore = false;
        }
        
        // Add delay to be respectful
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log(`Total products: ${allProducts.length}`);
    
    // Filter for Dynamic Discs
    const dynamicDiscs = allProducts.filter(product => {
        const vendor = product.vendor?.toLowerCase() || '';
        const title = product.title?.toLowerCase() || '';
        const tags = product.tags || [];
        
        return vendor.includes('dynamic') || 
               title.includes('dynamic') ||
               tags.some(tag => tag.toLowerCase().includes('dynamic'));
    });
    
    console.log(`Dynamic Discs products: ${dynamicDiscs.length}`);
    
    // Download as JSON
    const dataStr = JSON.stringify(dynamicDiscs, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'dynamic_discs_products.json';
    link.click();
    
    return dynamicDiscs;
}

// Run the function
getAllProducts();
```

## Option 3: Alternative Endpoints to Try

Once logged in, try these URLs in your browser:

1. **Dynamic Discs Collection:**
   - https://discgolfdistribution.com/collections/dynamic-discs/products.json?limit=250

2. **Search for Dynamic Discs:**
   - https://discgolfdistribution.com/search.json?q=dynamic&limit=250

3. **All Collections:**
   - https://discgolfdistribution.com/collections.json

## Expected Data Structure

The JSON should contain products with this structure:
```json
{
  "products": [
    {
      "id": 123456,
      "title": "Product Name",
      "vendor": "Dynamic Discs",
      "product_type": "Disc Golf",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "published_at": "2024-01-01T00:00:00Z",
      "available": true,
      "tags": ["tag1", "tag2"],
      "variants": [
        {
          "id": 789012,
          "title": "Variant Name",
          "price": "19.99",
          "sku": "SKU123",
          "inventory_quantity": 10,
          "available": true,
          "weight": 175,
          "option1": "Weight",
          "option2": "Color",
          "option3": "Plastic"
        }
      ]
    }
  ]
}
```

## Next Steps

Once you have the JSON data:
1. Save it as `dynamic_discs_raw.json`
2. We can create a script to process and format it into a CSV or database import format
3. We can set up automated monitoring for inventory changes

Let me know when you have the data and I'll help process it!
