/**
 * Process update_disc_variant_msrp_on_shopify task
 * 
 * This task updates a specific disc or OSL's variant "compare at" price (MSRP) on Shopify.
 * It uses the item's SKU (format: 'D' + disc.id or 'OS' + osl.id) to find the variant
 * and updates the compare_at_price to the new MSRP price.
 */

import fetch from 'node-fetch';

// Shopify configuration
const shopifyEndpoint = 'https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/graphql.json';
const shopifyAccessToken = '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';
const variantsEndpoint = 'https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/variants/';

/**
 * Execute a Shopify GraphQL request
 * @param {string} query - The GraphQL query
 * @param {Object} variables - The query variables
 * @returns {Promise<Object>} - The response data
 */
async function shopifyGraphQLRequest(query, variables = {}) {
  const response = await fetch(shopifyEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken
    },
    body: JSON.stringify({
      query,
      variables
    })
  });

  if (!response.ok) {
    throw new Error(`Shopify GraphQL request failed: ${response.status} ${response.statusText}`);
  }

  const result = await response.json();
  
  if (result.errors) {
    throw new Error(`Shopify GraphQL errors: ${JSON.stringify(result.errors)}`);
  }

  return result.data;
}

/**
 * Find Shopify variant by SKU using GraphQL
 * @param {string} sku - The SKU to search for
 * @returns {Promise<Object|null>} - The variant data or null if not found
 */
async function findVariantBySku(sku) {
  try {
    console.log(`[processUpdateDiscVariantMsrpOnShopifyTask] Searching for variant with SKU: ${sku}`);
    
    const query = `
      query getVariantBySku($query: String!) {
        productVariants(first: 1, query: $query) {
          edges {
            node {
              id
              sku
              price
              compareAtPrice
              product {
                id
                title
              }
            }
          }
        }
      }
    `;
    
    const variables = {
      query: `sku:${sku}`
    };
    
    const data = await shopifyGraphQLRequest(query, variables);
    
    if (!data.productVariants.edges.length) {
      console.log(`[processUpdateDiscVariantMsrpOnShopifyTask] No variant found with SKU: ${sku}`);
      return null;
    }
    
    const variant = data.productVariants.edges[0].node;
    
    // Convert GraphQL IDs to REST API IDs
    const variantId = variant.id.split('/').pop();
    const productId = variant.product.id.split('/').pop();
    
    const variantData = {
      variant_id: variantId,
      product_id: productId,
      current_price: variant.price,
      current_compare_at_price: variant.compareAtPrice,
      product_title: variant.product.title
    };
    
    console.log(`[processUpdateDiscVariantMsrpOnShopifyTask] Found variant with ID: ${variantData.variant_id} for SKU: ${sku}, current compare_at_price: ${variantData.current_compare_at_price}`);
    return variantData;
  } catch (error) {
    console.error(`[processUpdateDiscVariantMsrpOnShopifyTask] Failed to find variant by SKU: ${error.message}`);
    throw error;
  }
}

/**
 * Update variant compare_at_price on Shopify using REST API
 * @param {string} variantId - The variant ID
 * @param {number} newMsrpPrice - The new MSRP price
 * @returns {Promise<Object>} - The updated variant data
 */
async function updateVariantMsrp(variantId, newMsrpPrice) {
  try {
    console.log(`[processUpdateDiscVariantMsrpOnShopifyTask] Updating variant ${variantId} with new compare_at_price: ${newMsrpPrice}`);
    
    const updateEndpoint = `${variantsEndpoint}${variantId}.json`;
    
    const payload = {
      variant: {
        id: variantId,
        compare_at_price: newMsrpPrice ? newMsrpPrice.toString() : null
      }
    };
    
    const response = await fetch(updateEndpoint, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      },
      body: JSON.stringify(payload)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Shopify REST API request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    const result = await response.json();
    console.log(`[processUpdateDiscVariantMsrpOnShopifyTask] Successfully updated variant: ${JSON.stringify(result.variant)}`);
    return result.variant;
  } catch (error) {
    console.error(`[processUpdateDiscVariantMsrpOnShopifyTask] Failed to update variant ${variantId}: ${error.message}`);
    throw error;
  }
}

/**
 * Process update disc variant MSRP on Shopify task
 * @param {Object} task - The task object from the queue
 * @param {Object} context - Context object containing supabase client and helper functions
 */
export default async function processUpdateDiscVariantMsrpOnShopifyTask(task, context = {}) {
  const { supabase, updateTaskStatus, logError } = context;
  
  if (!supabase) {
    console.error('[processUpdateDiscVariantMsrpOnShopifyTask] Supabase client not provided in context');
    return;
  }

  const { payload } = task;
  const itemId = payload.id;
  const itemType = payload.item_type || 'disc'; // Default to 'disc' for backward compatibility
  const newMsrpPrice = payload.new_msrp_price;

  console.log(`[processUpdateDiscVariantMsrpOnShopifyTask] Processing MSRP update for ${itemType} id=${itemId}, new MSRP=${newMsrpPrice}`);

  try {
    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Validate that we have the required data
    if (!itemId) {
      throw new Error(`${itemType} ID is required in payload`);
    }

    // Generate the SKU based on item type
    let itemSku;
    if (itemType === 'osl') {
      itemSku = `OS${itemId}`;
    } else {
      itemSku = `D${itemId}`;
    }
    console.log(`[processUpdateDiscVariantMsrpOnShopifyTask] Generated SKU: ${itemSku} for ${itemType} id=${itemId}`);

    // Find the variant in Shopify
    const variantData = await findVariantBySku(itemSku);
    
    if (!variantData) {
      await updateTaskStatus(task.id, 'completed', {
        message: `No Shopify variant found for ${itemType} id=${itemId} with SKU=${itemSku}`,
        item_id: itemId,
        item_type: itemType,
        sku: itemSku,
        new_msrp_price: newMsrpPrice,
        status: 'variant_not_found'
      });
      return;
    }

    // Check if MSRP actually needs updating
    const currentMsrp = variantData.current_compare_at_price ? parseFloat(variantData.current_compare_at_price) : null;
    const targetMsrp = newMsrpPrice ? parseFloat(newMsrpPrice) : null;
    
    // Compare MSRPs (handle null values)
    const msrpMatches = (currentMsrp === null && targetMsrp === null) || 
                       (currentMsrp !== null && targetMsrp !== null && Math.abs(currentMsrp - targetMsrp) < 0.01);
    
    if (msrpMatches) {
      console.log(`[processUpdateDiscVariantMsrpOnShopifyTask] MSRP already matches target MSRP for ${itemType} id=${itemId}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `MSRP already matches target MSRP for ${itemType} id=${itemId}`,
        item_id: itemId,
        item_type: itemType,
        sku: itemSku,
        variant_id: variantData.variant_id,
        current_msrp: currentMsrp,
        target_msrp: targetMsrp,
        status: 'no_update_needed'
      });
      return;
    }

    // Update the variant MSRP
    const updatedVariant = await updateVariantMsrp(variantData.variant_id, newMsrpPrice);
    
    // Mark task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully updated MSRP for ${itemType} id=${itemId} from ${currentMsrp} to ${newMsrpPrice}`,
      item_id: itemId,
      item_type: itemType,
      sku: itemSku,
      variant_id: variantData.variant_id,
      product_title: variantData.product_title,
      old_msrp: currentMsrp,
      new_msrp: updatedVariant.compare_at_price ? parseFloat(updatedVariant.compare_at_price) : null,
      status: 'updated'
    });

    console.log(`[processUpdateDiscVariantMsrpOnShopifyTask] Successfully completed MSRP update for ${itemType} id=${itemId}`);

  } catch (error) {
    console.error(`[processUpdateDiscVariantMsrpOnShopifyTask] Error updating MSRP for ${itemType} id=${itemId}:`, error.message);
    
    await updateTaskStatus(task.id, 'failed', {
      error: error.message,
      item_id: itemId,
      item_type: itemType,
      sku: itemType === 'osl' ? `OS${itemId}` : `D${itemId}`,
      new_msrp_price: newMsrpPrice
    });
  }
}
