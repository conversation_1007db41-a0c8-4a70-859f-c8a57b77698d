# RPRO to Veeqo Reconciliation

This guide explains how to set up and use the reconciliation system that compares quantities between RPRO and Veeqo data.

## Quick Start

1. **Create the Reconciliation Table**: Run the SQL in `reconcile_table_simple.sql` in the Supabase SQL Editor to create and populate the reconciliation table.
2. **Refresh the Data**: Run `node refreshReconcileData.js` to refresh the reconciliation data.
3. **Set Up Automatic Reconciliation**: Run `node setupReconcileTask.js` to set up a daily scheduled task.
4. **View Discrepancies**: Query the `v_reconcile_rpro_counts_to_veeqo` view in Supabase to see quantity discrepancies.

## Detailed Instructions

### Step 1: Create the Reconciliation Table

1. Go to the Supabase dashboard
2. Click on "SQL Editor"
3. Create a new query
4. Copy and paste the contents of `reconcile_table_simple.sql`
5. Run the SQL

This will:
- Create the `reconcile_rpro_counts_to_veeqo` table in Supabase
- Create the `v_reconcile_rpro_counts_to_veeqo` view for easy querying
- Populate the table with initial data comparing RPRO and Veeqo quantities

### Step 2: Refresh the Reconciliation Data

```
node refreshReconcileData.js
```

This script will:
- Execute the SQL to refresh the reconciliation data
- Display statistics about the reconciliation

### Step 3: Set Up Automatic Reconciliation

```
node setupReconcileTask.js
```

This script will:
- Create a batch file for running the reconciliation
- Set up a PM2 scheduled task to run at 6:30 AM every day (after the RPRO import at 6:15 AM)
- Save the PM2 configuration

### Step 4: View and Analyze Discrepancies

You can view the discrepancies in several ways:

1. **Using the Supabase UI**:
   - Go to the Supabase dashboard
   - Click on "Table Editor"
   - Select the `v_reconcile_rpro_counts_to_veeqo` view
   - Use filters to find specific discrepancies

2. **Using SQL**:
   ```sql
   -- Get all discrepancies
   SELECT * FROM v_reconcile_rpro_counts_to_veeqo
   WHERE quantity_difference != 0
   ORDER BY ABS(quantity_difference) DESC;

   -- Get cases where RPRO has more
   SELECT * FROM v_reconcile_rpro_counts_to_veeqo
   WHERE quantity_difference > 0
   ORDER BY quantity_difference DESC;

   -- Get cases where Veeqo has more
   SELECT * FROM v_reconcile_rpro_counts_to_veeqo
   WHERE quantity_difference < 0
   ORDER BY quantity_difference ASC;
   ```

## Understanding the Data

The reconciliation table contains the following columns:

- `id`: Unique identifier for the reconciliation record
- `rpro_id`: The RPRO ID (ivno)
- `veeqo_id`: The Veeqo product ID
- `sku_code`: The SKU code (e.g., R00017)
- `product_title`: The product title from Veeqo
- `rpro_qty`: The quantity from RPRO (ivqtylaw)
- `veeqo_qty`: The quantity from Veeqo (total_qty)
- `quantity_difference`: The difference between RPRO and Veeqo quantities (rpro_qty - veeqo_qty)
- `last_updated`: When the reconciliation data was last updated
- `status`: A text description of the discrepancy status (in the view)

## Manual Refresh

You can manually refresh the reconciliation data at any time:

```
node refreshReconcileData.js
```

## Troubleshooting

If you encounter any issues:

1. Check the log file:
   - `reconcile_table.log`: Log for reconciliation table creation and updates

2. Common issues:
   - **SQL Execution Errors**: The script will try to execute SQL in smaller chunks if there's an error
   - **Missing Tables**: Make sure both `imported_table_rpro` and `imported_table_veeqo_sellables_export` exist
   - **Data Type Issues**: The script handles conversion of text quantities to numeric values
