require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function simpleUpdate() {
  try {
    console.log('Checking current state before update...');
    
    // Check current state using a simple query
    const { data: beforeData, error: beforeError } = await supabase
      .from('t_discs')
      .select('vendor_osl_id', { count: 'exact' })
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (beforeError) {
      console.error('Error checking before state:', beforeError);
      return;
    }
    
    const beforeCount = beforeData ? beforeData.length : 0;
    console.log(`Found ${beforeCount} discs with null vendor_osl_id that need updating`);
    
    if (beforeCount === 0) {
      console.log('No discs need updating. Exiting.');
      return;
    }
    
    console.log('\nPerforming bulk update...');
    
    // Perform the update using the SQL function
    const updateSql = `
      UPDATE t_discs 
      SET vendor_osl_id = (
          SELECT osl.id
          FROM t_order_sheet_lines osl
          WHERE osl.mps_id = t_discs.mps_id
            AND (osl.color_id = t_discs.color_id OR osl.color_id = 23)
            AND ROUND(t_discs.weight_mfg) >= osl.min_weight
            AND ROUND(t_discs.weight_mfg) <= osl.max_weight
          LIMIT 1
      )
      WHERE vendor_osl_id IS NULL
        AND weight_mfg IS NOT NULL
        AND mps_id IS NOT NULL
        AND color_id IS NOT NULL
        AND EXISTS (
          SELECT 1
          FROM t_order_sheet_lines osl
          WHERE osl.mps_id = t_discs.mps_id
            AND (osl.color_id = t_discs.color_id OR osl.color_id = 23)
            AND ROUND(t_discs.weight_mfg) >= osl.min_weight
            AND ROUND(t_discs.weight_mfg) <= osl.max_weight
        );
    `;
    
    const { error: updateError } = await supabase.rpc('exec_sql', {
      sql_query: updateSql
    });
    
    if (updateError) {
      console.error('Error performing update:', updateError);
      return;
    }
    
    console.log('Update completed successfully!');
    
    // Check results
    console.log('\nChecking results...');
    
    const { data: afterData, error: afterError } = await supabase
      .from('t_discs')
      .select('vendor_osl_id', { count: 'exact' })
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (afterError) {
      console.error('Error checking after state:', afterError);
      return;
    }
    
    const afterCount = afterData ? afterData.length : 0;
    const updatedCount = beforeCount - afterCount;
    
    console.log('\n=== RESULTS ===');
    console.log(`Discs that needed updating: ${beforeCount}`);
    console.log(`Discs still needing updates: ${afterCount}`);
    console.log(`Successfully updated: ${updatedCount}`);
    
    if (updatedCount > 0) {
      console.log('\n✅ SUCCESS! Updated', updatedCount, 'discs with vendor_osl_id');
      
      // Check for different mappings
      const { data: differentMappings, error: diffError } = await supabase
        .from('t_discs')
        .select('id, order_sheet_line_id, vendor_osl_id', { count: 'exact' })
        .not('vendor_osl_id', 'is', null)
        .not('order_sheet_line_id', 'is', null)
        .neq('vendor_osl_id', 'order_sheet_line_id');
      
      if (!diffError && differentMappings) {
        console.log(`🎯 Found ${differentMappings.length} discs with different regular vs vendor OSL mappings!`);
        
        if (differentMappings.length > 0) {
          console.log('\nSample of different mappings:');
          differentMappings.slice(0, 5).forEach(disc => {
            console.log(`  Disc ${disc.id}: regular OSL ${disc.order_sheet_line_id}, vendor OSL ${disc.vendor_osl_id}`);
          });
        }
      }
    } else {
      console.log('\n⚠️ No discs were updated. This might mean:');
      console.log('- All discs already have vendor_osl_id set');
      console.log('- No matching OSLs found for the manufacturer weights');
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

simpleUpdate();
