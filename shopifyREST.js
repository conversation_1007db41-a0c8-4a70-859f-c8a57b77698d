// shopifyREST.js - Utility for Shopify REST API operations

import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

// Shopify REST API credentials
const SHOPIFY_DOMAIN = process.env.SHOPIFY_DOMAIN || 'dzdiscs-new-releases.myshopify.com';
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ACCESS_TOKEN;

/**
 * Execute a REST API request against the Shopify API
 * @param {string} endpoint - The REST API endpoint (e.g., 'products')
 * @param {string} method - The HTTP method (GET, POST, PUT, DELETE)
 * @param {Object} data - The data to send (for POST and PUT requests)
 * @returns {Promise<Object>} - The response data
 */
async function executeShopifyREST(endpoint, method = 'GET', data = null) {
  if (!SHOPIFY_ACCESS_TOKEN) {
    throw new Error('Shopify access token not configured in environment variables');
  }

  const url = `https://${SHOPIFY_DOMAIN}/admin/api/2024-01/${endpoint}`;

  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN
    }
  };

  if (data && (method === 'POST' || method === 'PUT')) {
    options.body = JSON.stringify(data);
  }

  console.log(`Executing Shopify REST API request: ${method} ${url}`);
  if (data) {
    console.log(`Request data: ${JSON.stringify(data)}`);
  }

  try {
    const response = await fetch(url, options);

    // Check if the response is OK (status code 200-299)
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Shopify REST API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log(`Shopify REST API response: ${JSON.stringify(result)}`);

    return result;
  } catch (error) {
    console.error(`Error executing Shopify REST API request: ${error.message}`);
    throw error;
  }
}

/**
 * Get inventory level for a specific inventory item and location
 * @param {string} inventoryItemId - The inventory item ID
 * @param {string} locationId - The location ID
 * @returns {Promise<Object>} - The inventory level
 */
export async function getInventoryLevel(inventoryItemId, locationId) {
  // Format the IDs if they're in the gid:// format
  const formattedInventoryItemId = inventoryItemId.replace('gid://shopify/InventoryItem/', '');
  const formattedLocationId = locationId.replace('gid://shopify/Location/', '');

  // Get inventory levels for the inventory item
  const endpoint = `inventory_levels.json?inventory_item_ids=${formattedInventoryItemId}&location_ids=${formattedLocationId}`;

  try {
    const result = await executeShopifyREST(endpoint);

    if (!result.inventory_levels || result.inventory_levels.length === 0) {
      console.log(`No inventory level found for inventory item ${inventoryItemId} at location ${locationId}`);
      return { available: 0 };
    }

    return result.inventory_levels[0];
  } catch (error) {
    console.error(`Error getting inventory level: ${error.message}`);
    throw error;
  }
}

/**
 * Set inventory level for a specific inventory item and location
 * @param {string} inventoryItemId - The inventory item ID
 * @param {string} locationId - The location ID
 * @param {number} available - The available quantity to set
 * @returns {Promise<Object>} - The updated inventory level
 */
export async function setInventoryLevel(inventoryItemId, locationId, available) {
  // Format the IDs if they're in the gid:// format
  const formattedInventoryItemId = inventoryItemId.replace('gid://shopify/InventoryItem/', '');
  const formattedLocationId = locationId.replace('gid://shopify/Location/', '');

  // Set inventory level
  const endpoint = 'inventory_levels/set.json';
  const data = {
    inventory_item_id: parseInt(formattedInventoryItemId),
    location_id: parseInt(formattedLocationId),
    available
  };

  try {
    const result = await executeShopifyREST(endpoint, 'POST', data);
    return result.inventory_level;
  } catch (error) {
    console.error(`Error setting inventory level: ${error.message}`);
    throw error;
  }
}

/**
 * Set inventory quantity to a specific value for an inventory item using the REST API
 * @param {string} inventoryItemId - The Shopify inventory item ID
 * @param {number} quantity - The quantity to set
 * @returns {Promise<Object>} - The result of the operation
 */
export async function setInventoryItemQuantityREST(inventoryItemId, quantity) {
  try {
    console.log(`Setting inventory to ${quantity} for inventory item ${inventoryItemId} using REST API`);

    // Format the inventory item ID if needed
    let formattedInventoryItemId = inventoryItemId;
    if (String(inventoryItemId).startsWith('gid://shopify/InventoryItem/')) {
      formattedInventoryItemId = inventoryItemId.replace('gid://shopify/InventoryItem/', '');
    }

    // Get the location ID from environment variables or use the default one
    let locationId = process.env.SHOPIFY_LOCATION_ID;
    if (!locationId) {
      console.log('Shopify location ID not found in environment variables, using default location');
      // Use the Drop Zone Disc Golf LFK Retail Shop location as default
      locationId = '63618220220';
    } else if (locationId.startsWith('gid://shopify/Location/')) {
      locationId = locationId.replace('gid://shopify/Location/', '');
    }

    // Get the current inventory level
    const currentLevel = await getInventoryLevel(formattedInventoryItemId, locationId);
    console.log(`Current inventory level: ${JSON.stringify(currentLevel)}`);

    // If the inventory is already at the target quantity, no need to update
    if (currentLevel.available === quantity) {
      console.log(`Inventory already at ${quantity}, no update needed`);
      return {
        success: true,
        inventoryItemId: formattedInventoryItemId,
        locationId,
        newQuantity: quantity,
        message: `Inventory already at ${quantity}, no update needed`
      };
    }

    // Set the inventory level to the target quantity
    const result = await setInventoryLevel(formattedInventoryItemId, locationId, quantity);
    console.log(`Inventory set to ${quantity}: ${JSON.stringify(result)}`);

    return {
      success: true,
      inventoryItemId: formattedInventoryItemId,
      locationId,
      newQuantity: quantity,
      result
    };
  } catch (error) {
    console.error(`Error setting inventory to ${quantity} using REST API: ${error.message}`);
    throw error;
  }
}

/**
 * Set inventory quantity to zero for an inventory item using the REST API
 * @param {string} inventoryItemId - The Shopify inventory item ID
 * @returns {Promise<Object>} - The result of the operation
 */
export async function setInventoryItemToZeroREST(inventoryItemId) {
  return setInventoryItemQuantityREST(inventoryItemId, 0);
}
