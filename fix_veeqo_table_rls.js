import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function fixVeeqoTableRLS() {
  try {
    console.log('Fixing RLS policies for imported_table_veeqo_sellables_export...');
    
    // First, let's check if RLS is enabled
    const { data: rlsStatus, error: rlsError } = await supabase.rpc('exec_sql', { 
      sql_query: `
        SELECT relname, relrowsecurity 
        FROM pg_class 
        WHERE relname = 'imported_table_veeqo_sellables_export';
      `
    });
    
    if (rlsError) {
      console.error('Error checking RLS status:', rlsError);
      return;
    }
    
    if (rlsStatus && rlsStatus.length > 0) {
      console.log(`RLS enabled: ${rlsStatus[0].relrowsecurity}`);
    }
    
    // Add RLS policies to allow all operations
    const { data: policyResult, error: policyError } = await supabase.rpc('exec_sql', { 
      sql_query: `
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Allow all operations on imported_table_veeqo_sellables_export" ON imported_table_veeqo_sellables_export;
        DROP POLICY IF EXISTS "Allow select on imported_table_veeqo_sellables_export" ON imported_table_veeqo_sellables_export;
        DROP POLICY IF EXISTS "Allow insert on imported_table_veeqo_sellables_export" ON imported_table_veeqo_sellables_export;
        DROP POLICY IF EXISTS "Allow update on imported_table_veeqo_sellables_export" ON imported_table_veeqo_sellables_export;
        DROP POLICY IF EXISTS "Allow delete on imported_table_veeqo_sellables_export" ON imported_table_veeqo_sellables_export;
        
        -- Create comprehensive policies
        CREATE POLICY "Allow select on imported_table_veeqo_sellables_export" 
        ON imported_table_veeqo_sellables_export FOR SELECT 
        USING (true);
        
        CREATE POLICY "Allow insert on imported_table_veeqo_sellables_export" 
        ON imported_table_veeqo_sellables_export FOR INSERT 
        WITH CHECK (true);
        
        CREATE POLICY "Allow update on imported_table_veeqo_sellables_export" 
        ON imported_table_veeqo_sellables_export FOR UPDATE 
        USING (true) WITH CHECK (true);
        
        CREATE POLICY "Allow delete on imported_table_veeqo_sellables_export" 
        ON imported_table_veeqo_sellables_export FOR DELETE 
        USING (true);
      `
    });
    
    if (policyError) {
      console.error('Error creating RLS policies:', policyError);
    } else {
      console.log('✅ Successfully created RLS policies');
      
      // Verify policies were created
      const { data: policies, error: policiesError } = await supabase.rpc('exec_sql', { 
        sql_query: `
          SELECT policyname, cmd, permissive 
          FROM pg_policies 
          WHERE tablename = 'imported_table_veeqo_sellables_export';
        `
      });
      
      if (policiesError) {
        console.error('Error verifying policies:', policiesError);
      } else if (policies && policies.length > 0) {
        console.log('✅ Policies verified:');
        policies.forEach(policy => {
          console.log(`  ${policy.policyname}: ${policy.cmd} (permissive: ${policy.permissive})`);
        });
      } else {
        console.log('❌ No policies found after creation');
      }
    }
  } catch (err) {
    console.error('Exception:', err);
  }
}

fixVeeqoTableRLS();
