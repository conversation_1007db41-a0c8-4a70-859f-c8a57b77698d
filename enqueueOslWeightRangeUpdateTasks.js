import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Calculate weight range tags for OSL products based on their weight range
 * @param {number} minWeight - The minimum weight
 * @param {number} maxWeight - The maximum weight
 * @returns {Array} - Array of weight range tags that overlap with the OSL range
 */
function calculateOslWeightRangeTags(minWeight, maxWeight) {
  const weightRangeTags = [];
  
  // Check each possible weight range to see if it overlaps with the OSL range
  const possibleRanges = [
    { min: 10, max: 49.5, tag: 'wt_rng_10-49' },
    { min: 50, max: 99.5, tag: 'wt_rng_50-99' },
    { min: 100, max: 119.5, tag: 'wt_rng_100-119' },
    { min: 120, max: 139.5, tag: 'wt_rng_120-139' },
    { min: 140, max: 149.5, tag: 'wt_rng_140-149' },
    { min: 150, max: 159.5, tag: 'wt_rng_150-159' },
    { min: 160, max: 169.5, tag: 'wt_rng_160-169' },
    { min: 170, max: 174.5, tag: 'wt_rng_170-174' },
    { min: 175, max: 180.5, tag: 'wt_rng_175-180' },
    { min: 181, max: 200, tag: 'wt_rng_181-200' },
    { min: 201, max: 249, tag: 'wt_rng_201-249' }
  ];
  
  for (const range of possibleRanges) {
    // Check if ranges overlap: OSL range overlaps with weight range if max of one >= min of other
    if (maxWeight >= range.min && minWeight <= range.max) {
      weightRangeTags.push(range.tag);
    }
  }
  
  return weightRangeTags;
}

/**
 * Get all OSL products that need weight range tag updates
 */
async function getEligibleOslProducts() {
  try {
    console.log('📊 Querying eligible OSL products for weight range tag updates...');
    
    // First get the total count
    const { count, error: countError } = await supabase
      .from('t_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .not('shopify_uploaded_at', 'is', null)
      .not('min_weight', 'is', null)
      .not('max_weight', 'is', null);

    if (countError) {
      throw new Error(`Database count error: ${countError.message}`);
    }

    console.log(`📊 Total eligible OSL products found: ${count}`);

    // Fetch all records in batches
    const allOslProducts = [];
    const batchSize = 1000;
    let offset = 0;

    while (offset < count) {
      console.log(`📊 Fetching batch ${Math.floor(offset / batchSize) + 1} (${offset + 1}-${Math.min(offset + batchSize, count)} of ${count})`);
      
      const { data: batch, error } = await supabase
        .from('t_order_sheet_lines')
        .select('id, min_weight, max_weight')
        .not('shopify_uploaded_at', 'is', null)
        .not('min_weight', 'is', null)
        .not('max_weight', 'is', null)
        .order('id')
        .range(offset, offset + batchSize - 1);

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      allOslProducts.push(...batch);
      offset += batchSize;

      // Break if we got fewer records than expected (end of data)
      if (batch.length < batchSize) {
        break;
      }
    }

    console.log(`📊 Successfully fetched ${allOslProducts.length} OSL products that need weight range tag updates`);
    return allOslProducts;
  } catch (error) {
    console.error(`❌ Error querying eligible OSL products:`, error.message);
    throw error;
  }
}

/**
 * Enqueue fix_osl_weight_range tasks for all eligible OSL products
 */
async function enqueueOslWeightRangeTasks(dryRun = false) {
  try {
    const oslProducts = await getEligibleOslProducts();
    
    if (oslProducts.length === 0) {
      console.log('ℹ️ No eligible OSL products found for weight range updates');
      return;
    }

    console.log(`🚀 ${dryRun ? 'DRY RUN: Would enqueue' : 'Enqueueing'} ${oslProducts.length} fix_osl_weight_range tasks...`);
    
    const now = new Date();
    const scheduledAt = new Date(now.getTime() + 5 * 60 * 1000); // Schedule 5 minutes in future
    
    // Prepare tasks for batch insert
    const tasks = [];
    let validOslProducts = 0;
    let skippedOslProducts = 0;
    
    for (const osl of oslProducts) {
      const weightRangeTags = calculateOslWeightRangeTags(osl.min_weight, osl.max_weight);
      
      if (weightRangeTags.length === 0) {
        console.log(`⚠️ Skipping OSL ${osl.id} with weight range ${osl.min_weight}g-${osl.max_weight}g (no matching weight range tags)`);
        skippedOslProducts++;
        continue;
      }
      
      tasks.push({
        task_type: 'fix_osl_weight_range',
        payload: {
          osl_id: osl.id,
          min_weight: osl.min_weight,
          max_weight: osl.max_weight,
          expected_weight_range_tags: weightRangeTags
        },
        status: 'pending',
        scheduled_at: scheduledAt.toISOString(),
        created_at: now.toISOString(),
        enqueued_by: `osl_weight_range_update_batch_${now.getTime()}`
      });
      
      validOslProducts++;
    }
    
    console.log(`✅ Prepared ${validOslProducts} tasks for OSL products with valid weight ranges`);
    console.log(`⚠️ Skipped ${skippedOslProducts} OSL products with no matching weight range tags`);
    
    if (dryRun) {
      console.log('🧪 DRY RUN: Tasks prepared but not enqueued');
      console.log('Sample task:', JSON.stringify(tasks[0], null, 2));
      return;
    }

    // Insert tasks in batches to avoid potential issues with large inserts
    const BATCH_SIZE = 100;
    let totalEnqueued = 0;
    
    for (let i = 0; i < tasks.length; i += BATCH_SIZE) {
      const batch = tasks.slice(i, i + BATCH_SIZE);
      
      const { error: insertError } = await supabase
        .from('t_task_queue')
        .insert(batch);

      if (insertError) {
        console.error(`❌ Error enqueueing batch ${Math.floor(i / BATCH_SIZE) + 1}:`, insertError);
        throw insertError;
      }

      totalEnqueued += batch.length;
      console.log(`✅ Enqueued batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(tasks.length / BATCH_SIZE)} (${batch.length} tasks, ${totalEnqueued} total)`);
    }
    
    console.log(`🎉 Successfully enqueued ${totalEnqueued} fix_osl_weight_range tasks`);
    console.log(`📅 Tasks scheduled to start at: ${scheduledAt.toISOString()}`);
    
  } catch (error) {
    console.error(`❌ Error enqueueing OSL weight range tasks:`, error.message);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    // Check for dry run flag
    const dryRun = process.argv.includes('--dry-run');
    
    if (dryRun) {
      console.log('🧪 Running in DRY RUN mode - no tasks will be enqueued');
    }
    
    await enqueueOslWeightRangeTasks(dryRun);
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
