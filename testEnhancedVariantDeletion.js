// testEnhancedVariantDeletion.js - Test the enhanced variant deletion with product cleanup
import dotenv from 'dotenv';
dotenv.config();

import { deleteVariantFromShopify } from './processDeleteVariantFromShopifyTask.js';

async function testEnhancedVariantDeletion() {
  console.log('🧪 Testing Enhanced Variant Deletion with Product Cleanup');
  console.log('========================================================');

  try {
    // Test with a disc SKU that should exist in Shopify
    // Replace with an actual SKU from your Shopify store for testing
    const testSku = 'D401743'; // This was the SKU from the previous test
    const reason = 'Testing enhanced variant deletion with product cleanup';

    console.log(`\n🎯 Testing deletion of SKU: ${testSku}`);
    console.log(`📝 Reason: ${reason}`);

    // Call the enhanced deletion function
    const result = await deleteVariantFromShopify(testSku, reason);

    console.log('\n📊 Deletion Results:');
    console.log('====================');
    console.log(`✅ Success: ${result.success}`);
    console.log(`🔍 Found: ${result.found}`);
    console.log(`📄 Message: ${result.message}`);
    
    if (result.found) {
      console.log(`\n📦 Product Details:`);
      console.log(`   Product Title: ${result.productTitle}`);
      console.log(`   Product Handle: ${result.productHandle}`);
      console.log(`   Product ID: ${result.productId}`);
      
      console.log(`\n🔄 Deletion Details:`);
      console.log(`   Deleted Variant ID: ${result.deletedVariantId}`);
      console.log(`   Was Only Variant: ${result.wasOnlyVariant}`);
      console.log(`   Product Also Deleted: ${result.productDeleted}`);
      console.log(`   Remaining Variants: ${result.remainingVariants}`);
      
      if (result.productDeletionError) {
        console.log(`   ⚠️  Product Deletion Error: ${result.productDeletionError}`);
      }
    }

    console.log('\n🎉 Test completed successfully!');

    // Show the expected behavior
    console.log('\n📋 Expected Behavior:');
    console.log('=====================');
    console.log('✅ Single Variant Products:');
    console.log('   - Delete the variant');
    console.log('   - Also delete the product to avoid empty shells');
    console.log('   - wasOnlyVariant: true');
    console.log('   - productDeleted: true');
    console.log('   - remainingVariants: 0');
    console.log('');
    console.log('✅ Multi-Variant Products:');
    console.log('   - Delete only the specific variant');
    console.log('   - Keep the product and other variants');
    console.log('   - wasOnlyVariant: false');
    console.log('   - productDeleted: false');
    console.log('   - remainingVariants: > 0');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testEnhancedVariantDeletion()
  .then(() => {
    console.log('\n🏁 Test script completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Test script failed:', error);
    process.exit(1);
  });
