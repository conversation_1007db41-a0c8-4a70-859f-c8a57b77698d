// enqueueCheckRproReadinessTasks.js - Helper to enqueue check_if_rpro_is_ready tasks for all RPRO records
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

/**
 * Enqueue check_if_rpro_is_ready tasks for a batch of RPRO records
 * @param {Array} records - Array of RPRO records
 * @returns {Object} - Enqueue results
 */
async function enqueueBatch(records) {
  console.log(`\n📋 Enqueueing tasks for batch of ${records.length} RPRO records...`);
  
  const results = {
    processed: 0,
    enqueued: 0,
    errors: 0
  };

  // Prepare tasks for batch insert
  const tasks = records.map((record, index) => {
    // Stagger the scheduling to avoid overwhelming the worker
    const scheduledAt = new Date(Date.now() + (index * 1000)); // 1 second apart
    
    return {
      task_type: 'check_if_rpro_is_ready',
      payload: { id: record.id },
      status: 'pending',
      scheduled_at: scheduledAt.toISOString(),
      created_at: new Date().toISOString(),
      enqueued_by: 'catchup_script_rpro_readiness'
    };
  });

  try {
    // Insert tasks in batch
    const { data: insertedTasks, error: insertError } = await supabase
      .from('t_task_queue')
      .insert(tasks)
      .select('id');

    if (insertError) {
      console.error(`❌ Error inserting batch of tasks: ${insertError.message}`);
      results.errors = records.length;
    } else {
      results.enqueued = insertedTasks ? insertedTasks.length : 0;
      console.log(`✅ Successfully enqueued ${results.enqueued} tasks`);
    }
    
    results.processed = records.length;
    
  } catch (err) {
    console.error(`❌ Exception enqueueing batch: ${err.message}`);
    results.errors = records.length;
    results.processed = records.length;
  }

  return results;
}

/**
 * Main function to enqueue tasks for all RPRO records
 */
async function main() {
  console.log('🚀 Starting RPRO Readiness Task Enqueue Script');
  console.log('==============================================');

  try {
    // Get total count of RPRO records
    console.log('\n📊 Getting total count of RPRO records...');
    const { count: totalCount, error: countError } = await supabase
      .from('imported_table_rpro')
      .select('id', { count: 'exact', head: true });

    if (countError) {
      console.error('❌ Error getting total count:', countError.message);
      process.exit(1);
    }

    console.log(`📋 Found ${totalCount} RPRO records to enqueue tasks for`);

    if (totalCount === 0) {
      console.log('✅ No RPRO records found. Nothing to enqueue.');
      return;
    }

    // Process records in batches
    const batchSize = 100; // Smaller batches for task enqueueing
    let offset = 0;
    let totalResults = {
      processed: 0,
      enqueued: 0,
      errors: 0
    };

    while (offset < totalCount) {
      console.log(`\n📦 Fetching batch ${Math.floor(offset / batchSize) + 1} (records ${offset + 1}-${Math.min(offset + batchSize, totalCount)})...`);
      
      const { data: records, error: fetchError } = await supabase
        .from('imported_table_rpro')
        .select('id, ivno')
        .range(offset, offset + batchSize - 1)
        .order('id');

      if (fetchError) {
        console.error('❌ Error fetching records:', fetchError.message);
        break;
      }

      if (!records || records.length === 0) {
        console.log('✅ No more records to process');
        break;
      }

      const batchResults = await enqueueBatch(records);
      
      // Accumulate results
      totalResults.processed += batchResults.processed;
      totalResults.enqueued += batchResults.enqueued;
      totalResults.errors += batchResults.errors;

      console.log(`✅ Batch completed: ${batchResults.enqueued} enqueued, ${batchResults.errors} errors`);

      offset += batchSize;
      
      // Small delay between batches to avoid overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Final summary
    console.log('\n🎉 RPRO Readiness Task Enqueue Script Completed');
    console.log('===============================================');
    console.log(`📊 Total Records Processed: ${totalResults.processed}`);
    console.log(`✅ Successfully Enqueued: ${totalResults.enqueued}`);
    console.log(`❌ Errors: ${totalResults.errors}`);
    
    if (totalResults.errors > 0) {
      console.log(`\n⚠️  ${totalResults.errors} records had errors during task enqueueing`);
    }

    if (totalResults.enqueued > 0) {
      console.log(`\n📋 ${totalResults.enqueued} check_if_rpro_is_ready tasks have been enqueued`);
      console.log('🔄 The worker will process these tasks automatically');
      console.log('📊 You can monitor progress in the t_task_queue table');
    }

  } catch (err) {
    console.error('❌ Fatal error in enqueue script:', err.message);
    console.error(err.stack);
    process.exit(1);
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(err => {
    console.error('❌ Unhandled error:', err.message);
    console.error(err.stack);
    process.exit(1);
  });
}
