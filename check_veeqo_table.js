import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkVeeqoTableStructure() {
  try {
    // First check if table exists
    const { data: tableExists, error: tableError } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT table_name
        FROM information_schema.tables
        WHERE table_name = 'imported_table_veeqo_sellables_export'
        AND table_schema = 'public';
      `
    });

    if (tableError) {
      console.error('Error checking table existence:', tableError);
      return;
    }

    if (!tableExists || tableExists.length === 0) {
      console.log('❌ Table imported_table_veeqo_sellables_export does NOT exist!');
      console.log('This is why the import is failing.');
      return;
    }

    console.log('✅ Table exists, checking columns...');

    const { data, error } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'imported_table_veeqo_sellables_export'
        AND table_schema = 'public'
        ORDER BY ordinal_position;
      `
    });

    if (error) {
      console.error('Error getting columns:', error);
    } else if (data && data.length > 0) {
      console.log('Current imported_table_veeqo_sellables_export columns:');
      data.forEach(col => {
        console.log(`  ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
      });
    } else {
      console.log('No columns found (this should not happen if table exists)');
    }
  } catch (err) {
    console.error('Exception:', err);
  }
}

checkVeeqoTableStructure();
