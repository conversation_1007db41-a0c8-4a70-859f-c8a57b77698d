// pgInsert.js
import dotenv from 'dotenv';
dotenv.config();

import pg from 'pg';
import minimist from 'minimist';

// Parse command-line arguments
const args = minimist(process.argv.slice(2));
const tableName = args.table || 't_discs';
const recordId = args.id || 418869;

// Create a PostgreSQL client
const { Pool } = pg;
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || process.env.POSTGRES_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

async function main() {
  const client = await pool.connect();
  
  try {
    console.log(`Inserting t_images record for ${tableName} with record_id=${recordId}`);
    
    // First, check if the record already exists
    const checkQuery = {
      text: 'SELECT id FROM t_images WHERE table_name = $1 AND record_id = $2',
      values: [tableName, recordId]
    };
    
    const checkResult = await client.query(checkQuery);
    
    if (checkResult.rows.length > 0) {
      console.log(`Record already exists for ${tableName} with record_id=${recordId}`);
      return;
    }
    
    // Insert the record
    const insertQuery = {
      text: 'INSERT INTO t_images (table_name, record_id, created_by, created_at) VALUES ($1, $2, $3, NOW()) RETURNING id',
      values: [tableName, recordId, 'system']
    };
    
    console.log(`Executing query: ${insertQuery.text}`);
    console.log(`With values: ${JSON.stringify(insertQuery.values)}`);
    
    const insertResult = await client.query(insertQuery);
    
    console.log(`Record inserted successfully with id: ${insertResult.rows[0].id}`);
    
    // Verify the record was inserted
    const verifyQuery = {
      text: 'SELECT * FROM t_images WHERE id = $1',
      values: [insertResult.rows[0].id]
    };
    
    const verifyResult = await client.query(verifyQuery);
    
    console.log('Inserted record:');
    console.log(JSON.stringify(verifyResult.rows[0], null, 2));
  } catch (err) {
    console.error(`Error: ${err.message}`);
    console.error(err.stack);
  } finally {
    client.release();
  }
}

main().catch(err => {
  console.error(`Unhandled error: ${err.message}`);
  console.error(err.stack);
  process.exit(1);
});
