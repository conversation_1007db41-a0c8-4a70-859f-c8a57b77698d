-- Function to enqueue a task for generating MPS fields
CREATE OR REPLACE FUNCTION fn_queue_generate_mps_fields()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert a task into the task queue
    INSERT INTO t_task_queue (
        task_type,
        payload,
        status,
        scheduled_at,
        created_at
    ) VALUES (
        'generate_mps_fields',
        jsonb_build_object('id', NEW.id),
        'pending',
        NOW(),
        NOW()
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trg_queue_generate_mps_fields ON t_mps;

CREATE TRIGGER trg_queue_generate_mps_fields
AFTER INSERT OR UPDATE OF
    mold_id,
    plastic_id,
    stamp_id,
    description,
    override_speed,
    override_glide,
    override_turn,
    override_fade
ON t_mps
FOR EACH ROW
EXECUTE FUNCTION fn_queue_generate_mps_fields();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger trg_queue_generate_mps_fields has been created.';
END $$;
