# Image API

A REST API for accessing images and folders in the specified directory. This API is designed to be used by custom GPT models to browse and access image files.

## Features

- List directories and files with pagination, sorting, and filtering
- Get image metadata
- Create new directories
- Serve static image files
- View latest files in large directories
- CORS enabled for cross-origin requests

## Installation

1. Make sure you have Node.js installed (v14 or higher)
2. Install the required dependencies:

```bash
npm install
```

## Usage

Start the API server:

```bash
npm run start-image-api
```

Or run it directly:

```bash
node imageApi.js
```

The server will start on port 3005 by default. You can change this by setting the `IMAGE_API_PORT` environment variable.

### Web-based Image Browser

A web-based image browser is included to help you explore the images and directories. To use it:

1. Make sure the API server is running
2. Open `image-browser.html` in your web browser
3. Browse through directories and view images

The image browser provides a user-friendly interface to:
- Navigate through directories
- View image thumbnails
- See image metadata
- View full-size images

## API Endpoints

### List Directory Contents

```
GET /api/list?path=<relative-path>&page=<page>&pageSize=<pageSize>&sort=<sort>&order=<order>&latestOnly=<latestOnly>&latestCount=<latestCount>
```

Lists directories and files in the specified path with pagination, sorting, and filtering options.

**Parameters:**

- `path` (string, optional): Relative path to list. If not provided, lists the root directory.
- `page` (number, optional): Page number (1-based). Default: 1.
- `pageSize` (number, optional): Number of items per page. Default: 50.
- `sort` (string, optional): Sort field. Options: `name`, `date`, `size`. Default: `name`.
- `order` (string, optional): Sort order. Options: `asc`, `desc`. Default: `asc`.
- `latestOnly` (boolean, optional): Show only the latest files. Default: `false`.
- `latestCount` (number, optional): Number of latest files to show. Default: 20.

**Example Requests:**

```
# Basic listing
GET /api/list?path=subfolder

# Paginated listing (page 2 with 20 items per page)
GET /api/list?path=subfolder&page=2&pageSize=20

# Sorted by date (newest first)
GET /api/list?path=subfolder&sort=date&order=desc

# Show only the 10 latest files
GET /api/list?path=subfolder&latestOnly=true&latestCount=10
```

**Example Response:**

```json
{
  "path": "subfolder",
  "directories": [
    {
      "name": "nested",
      "path": "subfolder/nested",
      "type": "directory",
      "url": "/api/list?path=subfolder%2Fnested"
    }
  ],
  "files": [
    {
      "name": "image.jpg",
      "path": "subfolder/image.jpg",
      "type": "file",
      "isImage": true,
      "url": "/static/subfolder%2Fimage.jpg",
      "metadataUrl": "/api/metadata?path=subfolder%2Fimage.jpg"
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 50,
    "totalItems": 10,
    "totalPages": 1,
    "totalDirectories": 1,
    "totalFiles": 9,
    "hasNextPage": false,
    "hasPrevPage": false
  },
  "sorting": {
    "sort": "name",
    "order": "asc"
  },
  "filtering": {
    "latestOnly": false,
    "latestCount": 20
  }
}
```

### Get File or Directory Metadata

```
GET /api/metadata?path=<relative-path>
```

Returns metadata for the specified file or directory.

**Example Response:**

```json
{
  "path": "subfolder/image.jpg",
  "metadata": {
    "size": 12345,
    "created": "2023-01-01T00:00:00.000Z",
    "modified": "2023-01-01T00:00:00.000Z",
    "isDirectory": false,
    "isFile": true,
    "mimeType": "image/jpeg",
    "extension": "jpg"
  }
}
```

### Create a New Directory

```
POST /api/directory
```

Creates a new directory at the specified path.

**Request Body:**

```json
{
  "path": "subfolder",
  "name": "new-folder"
}
```

**Example Response:**

```json
{
  "success": true,
  "message": "Folder 'new-folder' created successfully",
  "path": "subfolder/new-folder"
}
```

### Get Static Image File

```
GET /static/<relative-path>
```

Returns the image file at the specified path.

## Using with Custom GPT

To use this API with a custom GPT:

1. Make sure the API server is running and accessible from the internet (you may need to use a service like ngrok to expose your local server)
2. Configure your custom GPT to use the API endpoints
3. Use the API to browse and access image files

Example custom GPT prompt:

```
You are an assistant that can browse and analyze images.
You can use the following API endpoints to access images:

- GET https://your-server.com/api/list?path=<path> - List directory contents
  - Add &latestOnly=true to show only the latest files
  - Add &sort=date&order=desc to sort by newest first
  - Add &page=1&pageSize=20 for pagination
- GET https://your-server.com/api/metadata?path=<path> - Get file metadata
- POST https://your-server.com/api/directory - Create a new directory (body: {"path": "path/to/parent", "name": "new-folder"})
- GET https://your-server.com/static/<path> - Get image file

When a user asks to see images, use these endpoints to browse the available images and show them to the user.
When a user asks to see the latest images, use the latestOnly parameter.
When a user asks to create a new folder, use the POST endpoint to create it.
For large directories, use pagination or the latestOnly filter to avoid performance issues.
```

## Security Considerations

This API provides access to files in the specified directory. Make sure you:

1. Only expose files that you want to be publicly accessible
2. Consider adding authentication if needed
3. Be aware that the API allows listing all files in the directory

## Troubleshooting

If you encounter issues:

1. Make sure the base directory exists and is accessible
2. Check that the server has permission to read the files
3. Verify that the port is not already in use
4. Check the console for error messages
