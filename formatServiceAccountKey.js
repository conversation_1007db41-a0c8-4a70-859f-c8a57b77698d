// formatServiceAccountKey.js - Helper to format service account key for .env file

import fs from 'fs';
import path from 'path';

console.log('🔧 Service Account Key Formatter');
console.log('================================');

// Check if a JSON file was provided as argument
const jsonFile = process.argv[2];

if (!jsonFile) {
    console.log('Usage: node formatServiceAccountKey.js <path-to-service-account.json>');
    console.log('');
    console.log('This will format your service account JSON for use in .env file');
    console.log('');
    console.log('Example:');
    console.log('  node formatServiceAccountKey.js ./my-service-account.json');
    process.exit(1);
}

if (!fs.existsSync(jsonFile)) {
    console.error(`❌ File not found: ${jsonFile}`);
    process.exit(1);
}

try {
    // Read and parse the JSON file
    const jsonContent = fs.readFileSync(jsonFile, 'utf8');
    const serviceAccount = JSON.parse(jsonContent);
    
    console.log('✅ Successfully parsed service account JSON');
    console.log(`📧 Service Account Email: ${serviceAccount.client_email}`);
    console.log(`🏗️ Project ID: ${serviceAccount.project_id}`);
    
    // Format for .env file (escape quotes and put on one line)
    const envValue = JSON.stringify(serviceAccount);
    
    console.log('');
    console.log('📋 Add this line to your .env file:');
    console.log('=====================================');
    console.log(`GOOGLE_SERVICE_ACCOUNT_KEY=${envValue}`);
    console.log('');
    
    // Also save to a file for easy copying
    const outputFile = 'service-account-env.txt';
    fs.writeFileSync(outputFile, `GOOGLE_SERVICE_ACCOUNT_KEY=${envValue}\n`);
    console.log(`💾 Also saved to: ${outputFile}`);
    
    console.log('');
    console.log('🔒 Remember to:');
    console.log('1. Share your Google Sheet with this service account email:');
    console.log(`   ${serviceAccount.client_email}`);
    console.log('2. Give it Viewer permissions');
    console.log('3. Test with: node setupGoogleSheetsAPI.js');
    
} catch (error) {
    console.error(`❌ Error processing JSON file: ${error.message}`);
    process.exit(1);
}
