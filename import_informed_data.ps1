# Import Informed Repricer data using psql's \copy command
# This script can be called from Node.js using child_process.exec

# Get parameters from command line arguments or use defaults
param(
    [string]$pgUser = "postgres",
    [System.Security.SecureString]$pgSecurePassword,
    [string]$pgDatabase = "postgres",
    [string]$pgHost = "localhost",
    [string]$pgPort = "5432",
    [switch]$silent = $false,
    [string]$pgPasswordEnvVar = "PGPASSWORD"
)

# Get the password from environment variable if secure password not provided
if ($null -eq $pgSecurePassword) {
    $pgPassword = [Environment]::GetEnvironmentVariable($pgPasswordEnvVar)

    # If still empty, try the default PGPASSWORD environment variable
    if ([string]::IsNullOrEmpty($pgPassword)) {
        $pgPassword = [Environment]::GetEnvironmentVariable("PGPASSWORD")
    }
} else {
    # Convert SecureString to plain text for psql
    $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($pgSecurePassword)
    $pgPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
    [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($BSTR)
}

# Set the paths to the CSV files and SQL script
$allFieldsPath = "C:\Users\<USER>\supabase_project\data\external data\informed\from_informed_all_fields.csv"
$competitionLandscapePath = "C:\Users\<USER>\supabase_project\data\external data\informed\from_informed_competition_landscape.csv"
$noBuyBoxPath = "C:\Users\<USER>\supabase_project\data\external data\informed\from_informed_no_buy_box.csv"

# Create a temporary SQL script with the \copy commands
$tempScript = @"
-- First, truncate the tables
TRUNCATE TABLE public.it_infor_all_fields;
TRUNCATE TABLE public.it_infor_competition_landscape;
TRUNCATE TABLE public.it_infor_no_buy_box;

-- Import All Fields data
\copy public.it_infor_all_fields ("SKU", "STOCK", "ITEM_ID", "MEMO", "TITLE", "MARKETPLACE_ID", "COST", "CURRENCY", "CURRENT_VELOCITY", "MIN_PRICE", "CALC_MIN_PRICE", "MAX_PRICE", "CALC_MAX_PRICE", "CURRENT_PRICE", "CURRENT_SHIPPING", "MANUAL_PRICE", "ORIGINAL_PRICE", "MAP_PRICE", "LISTING_TYPE", "STRATEGY_ID", "BUY_BOX_PRICE", "BUYBOX_SELLER", "BUYBOX_WINNER", "VAT_PERCENTAGE", "DATE_ADDED", "STOCK_COST_VALUE", "DAYS_SINCE_BUYBOX", "DATE_OF_LAST_SALE", "DAYS_REMAINING", "AVAILABILITY", "OLDEST_STOCK_DATE") FROM '$allFieldsPath' WITH (FORMAT csv, HEADER true);

-- Import Competition Landscape data
\copy public.it_infor_competition_landscape ("SKU", "ITEM_ID", "STOCK", "MARKETPLACE_ID", "COST", "CURRENCY", "MIN_PRICE", "CALC_MIN_PRICE", "MAX_PRICE", "CALC_MAX_PRICE", "CURRENT_PRICE", "COMP_PRICE", "COMP_ID", "COMP_LISTING_TYPE", "BUYBOX_PRICE", "BUYBOX_SELLER", "BUYBOX_WINNER", "STRATEGY_ID") FROM '$competitionLandscapePath' WITH (FORMAT csv, HEADER true);

-- Import No Buy Box data
\copy public.it_infor_no_buy_box ("SKU", "ITEM_ID", "TITLE", "MARKETPLACE_ID", "COST", "CURRENCY", "MIN_PRICE", "CALC_MIN_PRICE", "MAX_PRICE", "CALC_MAX_PRICE", "CURRENT_PRICE", "BUY_BOX_PRICE", "BUY_BOX_WINNER", "STRATEGY_ID", "Fulfillment Type", "Marketplace ID") FROM '$noBuyBoxPath' WITH (FORMAT csv, HEADER true);

-- Update the created_at timestamp
UPDATE public.it_infor_all_fields SET created_at = NOW();
UPDATE public.it_infor_competition_landscape SET created_at = NOW();
UPDATE public.it_infor_no_buy_box SET created_at = NOW();

-- Log the import
INSERT INTO public.t_task_queue (
  task_type,
  payload,
  status,
  result,
  scheduled_at,
  created_at,
  completed_at,
  enqueued_by
) VALUES (
  'informed_import_completed',
  jsonb_build_object(
    'all_fields_count', (SELECT COUNT(*) FROM public.it_infor_all_fields),
    'competition_landscape_count', (SELECT COUNT(*) FROM public.it_infor_competition_landscape),
    'no_buy_box_count', (SELECT COUNT(*) FROM public.it_infor_no_buy_box)
  ),
  'completed',
  'Imported Informed Repricer data via SQL script',
  NOW(),
  NOW(),
  NOW(),
  'import_informed_data.ps1'
);

-- Print the results
SELECT 'Imported ' || COUNT(*) || ' records into it_infor_all_fields' FROM public.it_infor_all_fields;
SELECT 'Imported ' || COUNT(*) || ' records into it_infor_competition_landscape' FROM public.it_infor_competition_landscape;
SELECT 'Imported ' || COUNT(*) || ' records into it_infor_no_buy_box' FROM public.it_infor_no_buy_box;
"@

# Save the temporary script to a file
$tempScriptPath = [System.IO.Path]::GetTempFileName() + ".sql"
$tempScript | Out-File -FilePath $tempScriptPath -Encoding utf8

# Run the psql command with the temporary script
if (-not $silent) {
    Write-Host "Importing Informed Repricer data..."
}

# Set the PGPASSWORD environment variable temporarily
$env:PGPASSWORD = $pgPassword

# Find psql executable
$psqlPath = $null

# Common PostgreSQL installation paths
$pgPaths = @(
    "C:\Program Files\PostgreSQL",
    "C:\Program Files (x86)\PostgreSQL"
)

# Look for psql in common PostgreSQL installation paths
foreach ($basePath in $pgPaths) {
    if (Test-Path $basePath) {
        # Find all version folders
        $versionFolders = Get-ChildItem -Path $basePath -Directory | Sort-Object -Property Name -Descending

        foreach ($versionFolder in $versionFolders) {
            $testPath = Join-Path -Path $versionFolder.FullName -ChildPath "bin\psql.exe"
            if (Test-Path $testPath) {
                $psqlPath = $testPath
                if (-not $silent) {
                    Write-Host "Found psql at: $psqlPath"
                }
                break
            }
        }

        if ($psqlPath) {
            break
        }
    }
}

# If psql not found in common paths, try to find it in the PATH
if (-not $psqlPath) {
    $psqlInPath = Get-Command "psql" -ErrorAction SilentlyContinue
    if ($psqlInPath) {
        $psqlPath = $psqlInPath.Source
        if (-not $silent) {
            Write-Host "Found psql in PATH: $psqlPath"
        }
    }
}

# If still not found, try one more common location
if (-not $psqlPath) {
    $defaultPath = "C:\Program Files\PostgreSQL\14\bin\psql.exe"
    if (Test-Path $defaultPath) {
        $psqlPath = $defaultPath
        if (-not $silent) {
            Write-Host "Found psql at default location: $psqlPath"
        }
    }
}

# If psql is still not found, return an error
if (-not $psqlPath) {
    $errorMessage = "Could not find psql executable. Please make sure PostgreSQL is installed and psql is in your PATH."
    if (-not $silent) {
        Write-Host $errorMessage -ForegroundColor Red
    }

    $result = @{
        success = $false
        message = $errorMessage
        details = "PostgreSQL client (psql) not found"
    } | ConvertTo-Json

    Write-Output $result
    exit 1
}

try {
    # Run psql and capture output
    $output = & $psqlPath -U $pgUser -d $pgDatabase -h $pgHost -p $pgPort -f $tempScriptPath 2>&1

    # Check if psql command was successful
    if ($LASTEXITCODE -eq 0) {
        if (-not $silent) {
            Write-Host "Import completed successfully."
            $output | ForEach-Object { Write-Host $_ }
        }

        # Return success result as JSON for Node.js to parse
        $result = @{
            success = $true
            message = "Import completed successfully"
            details = $output -join "`n"
        } | ConvertTo-Json -Depth 3 -Compress

        # Make sure we output only the JSON with no other text
        Write-Output "JSON_START"
        Write-Output $result
        Write-Output "JSON_END"
        exit 0
    } else {
        if (-not $silent) {
            Write-Host "Import failed with exit code $LASTEXITCODE" -ForegroundColor Red
            $output | ForEach-Object { Write-Host $_ -ForegroundColor Red }
        }

        # Return error result as JSON for Node.js to parse
        $result = @{
            success = $false
            message = "Import failed with exit code $LASTEXITCODE"
            details = $output -join "`n"
        } | ConvertTo-Json -Depth 3 -Compress

        # Make sure we output only the JSON with no other text
        Write-Output "JSON_START"
        Write-Output $result
        Write-Output "JSON_END"
        exit 1
    }
} finally {
    # Clean up
    Remove-Item -Path $tempScriptPath -ErrorAction SilentlyContinue
    $env:PGPASSWORD = ""

    # Clear the password variable for security
    $pgPassword = $null
    [System.GC]::Collect()
}
