// createTableDirect.js - Direct table creation for Amazon Active Listings Report

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

const createTableSQL = `
-- Create table for Amazon Active Listings Report
CREATE TABLE IF NOT EXISTS public.it_amaz_active_listings_report (
  id SERIAL PRIMARY KEY,
  item_name TEXT,
  item_description TEXT,
  listing_id TEXT,
  seller_sku TEXT,
  price NUMERIC(10,2),
  quantity INTEGER,
  open_date TIMESTAMPTZ,
  image_url TEXT,
  item_is_marketplace TEXT,
  product_id_type TEXT,
  zshop_shipping_fee NUMERIC(10,2),
  item_note TEXT,
  item_condition TEXT,
  zshop_category1 TEXT,
  zshop_browse_path TEXT,
  zshop_storefront_feature TEXT,
  asin1 TEXT,
  asin2 TEXT,
  asin3 TEXT,
  will_ship_internationally TEXT,
  expedited_shipping TEXT,
  zshop_boldface TEXT,
  product_id TEXT,
  bid_for_featured_placement TEXT,
  add_delete TEXT,
  pending_quantity INTEGER,
  fulfillment_channel TEXT,
  business_price NUMERIC(10,2),
  quantity_price_type TEXT,
  quantity_lower_bound_1 INTEGER,
  quantity_price_1 NUMERIC(10,2),
  quantity_lower_bound_2 INTEGER,
  quantity_price_2 NUMERIC(10,2),
  quantity_lower_bound_3 INTEGER,
  quantity_price_3 NUMERIC(10,2),
  quantity_lower_bound_4 INTEGER,
  quantity_price_4 NUMERIC(10,2),
  quantity_lower_bound_5 INTEGER,
  quantity_price_5 NUMERIC(10,2),
  merchant_shipping_group TEXT,
  progressive_price_type TEXT,
  progressive_lower_bound_1 INTEGER,
  progressive_price_1 NUMERIC(10,2),
  progressive_lower_bound_2 INTEGER,
  progressive_price_2 NUMERIC(10,2),
  progressive_lower_bound_3 INTEGER,
  progressive_price_3 NUMERIC(10,2),
  
  -- Additional tracking fields
  report_date DATE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  import_batch_id UUID DEFAULT gen_random_uuid()
);
`;

const createIndexesSQL = `
-- Create indexes for common search fields
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_seller_sku ON public.it_amaz_active_listings_report(seller_sku);
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_asin1 ON public.it_amaz_active_listings_report(asin1);
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_product_id ON public.it_amaz_active_listings_report(product_id);
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_listing_id ON public.it_amaz_active_listings_report(listing_id);
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_report_date ON public.it_amaz_active_listings_report(report_date);
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_import_batch ON public.it_amaz_active_listings_report(import_batch_id);
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_fulfillment_channel ON public.it_amaz_active_listings_report(fulfillment_channel);
`;

const createFunctionSQL = `
-- Create function to truncate the table (for use by the import process)
CREATE OR REPLACE FUNCTION truncate_it_amaz_active_listings_report()
RETURNS void AS $$
BEGIN
  TRUNCATE TABLE public.it_amaz_active_listings_report RESTART IDENTITY;
END;
$$ LANGUAGE plpgsql;
`;

async function createTable() {
  try {
    console.log('Creating table...');
    
    // Try to create table using exec_sql function
    const { error: tableError } = await supabase.rpc('exec_sql', { 
      sql_query: createTableSQL 
    });
    
    if (tableError) {
      console.error('Error creating table:', tableError.message);
      return false;
    }
    
    console.log('Table created successfully');
    
    // Create indexes
    console.log('Creating indexes...');
    const { error: indexError } = await supabase.rpc('exec_sql', { 
      sql_query: createIndexesSQL 
    });
    
    if (indexError) {
      console.error('Error creating indexes:', indexError.message);
      // Continue anyway, indexes are not critical
    } else {
      console.log('Indexes created successfully');
    }
    
    // Create truncate function
    console.log('Creating truncate function...');
    const { error: functionError } = await supabase.rpc('exec_sql', { 
      sql_query: createFunctionSQL 
    });
    
    if (functionError) {
      console.error('Error creating function:', functionError.message);
      // Continue anyway, function can be created later
    } else {
      console.log('Truncate function created successfully');
    }
    
    return true;
    
  } catch (error) {
    console.error('Exception creating table:', error.message);
    return false;
  }
}

// Test the table
async function testTable() {
  try {
    console.log('Testing table...');
    const { data, error } = await supabase
      .from('it_amaz_active_listings_report')
      .select('*')
      .limit(0);
    
    if (error) {
      console.error('Error testing table:', error.message);
      return false;
    }
    
    console.log('Table test successful');
    return true;
  } catch (error) {
    console.error('Exception testing table:', error.message);
    return false;
  }
}

async function main() {
  const created = await createTable();
  if (created) {
    const tested = await testTable();
    if (tested) {
      console.log('✅ Table setup completed successfully!');
    } else {
      console.log('❌ Table created but test failed');
    }
  } else {
    console.log('❌ Failed to create table');
    console.log('Please run the SQL manually in Supabase SQL Editor:');
    console.log(createTableSQL);
    console.log(createIndexesSQL);
    console.log(createFunctionSQL);
  }
}

main().catch(console.error);
