-- Function to enqueue a task when t_mps.plastic_id, mold_id, or stamp_id is inserted or updated
CREATE OR REPLACE FUNCTION fn_enqueue_check_if_mps_is_ready_task()
RETURNS TRIGGER AS $$
BEGIN
    -- Enqueue a task if plastic_id, mold_id, or stamp_id has changed
    IF (TG_OP = 'INSERT') OR 
       (OLD.plastic_id IS DISTINCT FROM NEW.plastic_id) OR 
       (OLD.mold_id IS DISTINCT FROM NEW.mold_id) OR 
       (OLD.stamp_id IS DISTINCT FROM NEW.stamp_id) THEN
        
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at,
            enqueued_by
        ) VALUES (
            'check_if_mps_is_ready',
            jsonb_build_object('id', NEW.id),
            'pending',
            NOW(),
            NOW(),
            't_mps ' || TG_OP || '_trigger_' || NEW.id
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE OR REPLACE TRIGGER trg_check_if_mps_is_ready
AFTER INSERT OR UPDATE OF plastic_id, mold_id, stamp_id ON t_mps
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_check_if_mps_is_ready_task();
