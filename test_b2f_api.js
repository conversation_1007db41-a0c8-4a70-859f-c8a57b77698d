// Test B2F API endpoints
async function testEndpoint(url, description) {
  try {
    console.log(`\n🧪 Testing ${description}...`);
    const response = await fetch(url);
    const text = await response.text();
    
    console.log(`Status: ${response.status}`);
    console.log(`Content-Type: ${response.headers.get('content-type')}`);
    
    if (response.headers.get('content-type')?.includes('application/json')) {
      const data = JSON.parse(text);
      console.log(`✅ ${description} successful:`, data);
    } else {
      console.log(`❌ ${description} returned HTML/text:`, text.substring(0, 200));
    }
  } catch (err) {
    console.log(`❌ ${description} failed:`, err.message);
  }
}

console.log('🚀 Testing B2F API Endpoints');

await testEndpoint('http://localhost:3001/api/b2f/count', 'B2F Count');
await testEndpoint('http://localhost:3001/api/b2f/records', 'B2F Records');

console.log('\n✅ API tests complete');
