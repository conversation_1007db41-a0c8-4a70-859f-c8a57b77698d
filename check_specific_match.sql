-- Create a function to specifically check if disc 421349 should match with OSL 16890
CREATE OR REPLACE FUNCTION check_specific_match()
RETURNS TABLE (
    match_result BOOLEAN,
    debug_info TEXT
) AS $$
DECLARE
    disc_id INTEGER := 421349;
    disc_mps_id INTEGER := 19105;
    disc_color_id INTEGER := 6;
    disc_weight NUMERIC := 172.2;
    
    osl_id INTEGER := 16890;
    osl_mps_id INTEGER;
    osl_min_weight NUMERIC;
    osl_max_weight NUMERIC;
    osl_color_id INTEGER;
    
    rounded_weight NUMERIC;
    decimal_part NUMERIC;
    
    mps_match BOOLEAN;
    color_match BOOLEAN;
    weight_match BOOLEAN;
    
    debug_text TEXT;
BEGIN
    -- Get OSL details
    SELECT mps_id, min_weight, max_weight, color_id
    INTO osl_mps_id, osl_min_weight, osl_max_weight, osl_color_id
    FROM t_order_sheet_lines
    WHERE id = osl_id;
    
    -- Custom rounding logic
    decimal_part := disc_weight - FLOOR(disc_weight);
    
    IF decimal_part >= 0.5 THEN
        rounded_weight := CEIL(disc_weight);
    ELSE
        rounded_weight := FLOOR(disc_weight);
    END IF;
    
    -- Check matching criteria
    mps_match := (disc_mps_id = osl_mps_id);
    color_match := (osl_color_id = disc_color_id OR osl_color_id = 23);
    weight_match := (rounded_weight >= osl_min_weight AND rounded_weight <= osl_max_weight);
    
    -- Create debug info
    debug_text := 'Disc ID: ' || disc_id || 
                  ', MPS ID: ' || disc_mps_id || 
                  ', Color ID: ' || disc_color_id || 
                  ', Weight: ' || disc_weight || 
                  ', Rounded Weight: ' || rounded_weight || 
                  ', OSL ID: ' || osl_id || 
                  ', OSL MPS ID: ' || osl_mps_id || 
                  ', OSL Min Weight: ' || osl_min_weight || 
                  ', OSL Max Weight: ' || osl_max_weight || 
                  ', OSL Color ID: ' || osl_color_id || 
                  ', MPS Match: ' || mps_match || 
                  ', Color Match: ' || color_match || 
                  ', Weight Match: ' || weight_match;
    
    RETURN QUERY
    SELECT 
        (mps_match AND color_match AND weight_match) AS match_result,
        debug_text AS debug_info;
END;
$$ LANGUAGE plpgsql;

-- Call the function
SELECT * FROM check_specific_match();
