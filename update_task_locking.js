const { createClient } = require('@supabase/supabase-js');

// Get Supabase configuration from environment or use defaults
const supabaseUrl = process.env.SUPABASE_URL || 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzc4NjU0MSwiZXhwIjoyMDQ5MzYyNTQxfQ.FQyPTgdBP83VhuykdlZkagHADc5nBmx7-yd0JYt5aP8';
const supabase = createClient(supabaseUrl, supabaseKey);

async function updateTaskLockingFunction() {
  try {
    console.log('Updating lock_pending_tasks function to include future publish_product_osl tasks...');

    const sql = `
-- Update the lock_pending_tasks function to include future publish_product_osl tasks
-- This allows the worker to process future OSL tasks for the secondary action of enqueueing disc ready checks

CREATE OR REPLACE FUNCTION lock_pending_tasks(
    worker_id TEXT,
    max_tasks INTEGER,
    task_time TIMESTAMP WITH TIME ZONE
)
RETURNS SETOF t_task_queue AS $$
DECLARE
    lock_timeout INTERVAL := INTERVAL '5 minutes';
BEGIN
    RETURN QUERY
    WITH locked_tasks AS (
        UPDATE t_task_queue
        SET
            status = 'processing',
            locked_at = task_time,
            locked_by = worker_id
        WHERE id IN (
            SELECT id
            FROM t_task_queue
            WHERE
                status = 'pending'
                AND (
                    -- Normal tasks: scheduled for now or in the past
                    scheduled_at <= task_time
                    OR
                    -- Special case: future publish_product_osl tasks for secondary action
                    (task_type = 'publish_product_osl' AND scheduled_at > task_time)
                )
                AND (locked_at IS NULL OR locked_at < task_time - lock_timeout)
            ORDER BY
                -- Prioritize tasks that are ready to run, then future OSL tasks
                CASE
                    WHEN scheduled_at <= task_time THEN 0
                    ELSE 1
                END,
                scheduled_at ASC
            LIMIT max_tasks
            FOR UPDATE SKIP LOCKED
        )
        RETURNING *
    )
    SELECT * FROM locked_tasks;
END;
$$ LANGUAGE plpgsql;
    `;

    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });

    if (error) {
      console.error('Error updating function:', error);
      console.error('Full error details:', JSON.stringify(error, null, 2));
      return false;
    }

    console.log('✅ Successfully updated lock_pending_tasks function');
    console.log('✅ The worker can now process future publish_product_osl tasks for disc ready checks');
    return true;

  } catch (err) {
    console.error('Exception:', err.message);
    return false;
  }
}

// Run the update
updateTaskLockingFunction().then(success => {
  if (success) {
    console.log('\n🎉 Update completed successfully!');
    console.log('The task queue worker will now:');
    console.log('1. Process normal tasks scheduled for now or in the past');
    console.log('2. Process future publish_product_osl tasks to enqueue disc ready checks');
    console.log('3. Prioritize ready tasks over future tasks');
  } else {
    console.log('\n❌ Update failed. Please check the error messages above.');
    process.exit(1);
  }
});
