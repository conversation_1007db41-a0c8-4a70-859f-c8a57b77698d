-- Function to find a matching order sheet line using actual weight
CREATE OR REPLACE FUNCTION find_matching_osl(
    mps_id_param INTEGER,
    color_id_param INTEGER,
    weight_param NUMERIC
)
RETURNS TABLE (
    osl_id INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT id AS osl_id
    FROM t_order_sheet_lines
    WHERE mps_id = mps_id_param
      AND (color_id = color_id_param OR color_id = 23)
      AND weight_param >= min_weight
      AND weight_param <= max_weight
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to find a matching order sheet line using manufacturer weight
CREATE OR REPLACE FUNCTION find_matching_osl_by_mfg_weight(
    mps_id_param INTEGER,
    color_id_param INTEGER,
    weight_mfg_param NUMERIC
)
RETURNS TABLE (
    osl_id INTEGER
) AS $$
DECLARE
    rounded_weight NUMERIC;
BEGIN
    -- Round the weight to the nearest integer using standard rounding rules
    -- (0.5 and up rounds up, below 0.5 rounds down)
    rounded_weight := ROUND(weight_mfg_param);

    RETURN QUERY
    SELECT id AS osl_id
    FROM t_order_sheet_lines
    WHERE mps_id = mps_id_param
      AND (color_id = color_id_param OR color_id = 23)
      AND rounded_weight >= min_weight
      AND rounded_weight <= max_weight
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to update inventory in t_inv_osl
CREATE OR REPLACE FUNCTION update_osl_inventory(
    osl_id_param INTEGER,
    quantity_change_param INTEGER
)
RETURNS VOID AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    UPDATE t_inv_osl
    SET available_quantity = available_quantity + quantity_change_param
    WHERE id = osl_id_param;
    
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    
    IF affected_rows = 0 THEN
        INSERT INTO t_error_logs(error_message, created_at, context, created_by)
        VALUES (
            'No inventory record found in t_inv_osl for order_sheet_line_id ' || osl_id_param,
            NOW(),
            'update_osl_inventory: quantity_change=' || quantity_change_param,
            'task_queue_worker'
        );
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'OSL helper functions created.';
END $$;
