require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testMatchOslToDiscsWithRedirect() {
  try {
    console.log('Testing match_osl_to_discs task with redirect logic for vendor mapping...');
    
    // Test with OSL 19033 from our previous example
    const testOslId = 19033;
    
    console.log(`\n=== CREATING TEST TASK ===`);
    console.log(`Creating match_osl_to_discs task for OSL ${testOslId}...`);
    
    const { data: taskData, error: taskError } = await supabase
      .from('t_task_queue')
      .insert([
        {
          task_type: 'match_osl_to_discs',
          payload: { id: testOslId },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: 'test_match_osl_to_discs_with_redirect'
        }
      ])
      .select();
    
    if (taskError) {
      console.error('Error creating test task:', taskError);
      return;
    }
    
    const testTaskId = taskData[0].id;
    console.log(`✅ Created test task ${testTaskId} for OSL ${testOslId}`);
    
    // Get OSL details
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, min_weight, max_weight, color_id')
      .eq('id', testOslId)
      .single();
    
    if (!oslError) {
      console.log(`\nOSL ${testOslId}: MPS ${osl.mps_id}, Weight ${osl.min_weight}-${osl.max_weight}g, Color ${osl.color_id}`);
      
      // Check if this OSL's MPS has a redirect
      const { data: mps, error: mpsError } = await supabase
        .from('t_mps')
        .select('id, order_through_mps_id')
        .eq('id', osl.mps_id)
        .single();
      
      if (!mpsError) {
        if (mps.order_through_mps_id) {
          console.log(`⚠️  MPS ${mps.id} has redirect to MPS ${mps.order_through_mps_id}`);
          console.log('   → Regular mapping: Uses OSL 19033 directly (no redirect)');
          console.log('   → Vendor mapping: Uses redirect logic to find OSL in MPS 391');
        } else {
          console.log(`✅ MPS ${mps.id} has no redirect`);
          console.log('   → Both regular and vendor mapping use OSL 19033');
        }
      }
    }
    
    console.log('\n🔄 Task created and ready for processing by the worker daemon.');
    console.log('The updated task should now:');
    console.log('1. Process regular mapping normally (no redirect)');
    console.log('2. Process vendor mapping with redirect logic');
    console.log('3. Each disc may get a different vendor_osl_id based on redirect results');
    console.log('4. Update each disc individually with its correct vendor OSL');
    
    console.log('\n📋 EXPECTED BEHAVIOR:');
    
    console.log('\n✅ Regular Mapping:');
    console.log('   - Finds discs with MPS 19438 that match weight 173-175g');
    console.log('   - Sets order_sheet_line_id = 19033 for matching discs');
    console.log('   - No redirect logic applied');
    
    console.log('\n🔄 Vendor Mapping:');
    console.log('   - Finds discs with MPS 19438 that have manufacturer weight data');
    console.log('   - For each disc, calls redirect function with disc\'s MPS and weight');
    console.log('   - Redirect function detects MPS 19438 → MPS 391');
    console.log('   - Looks for matching OSL in MPS 391');
    console.log('   - Sets vendor_osl_id to the found OSL (or null if none)');
    
    console.log('\n📊 To monitor results:');
    console.log(`
-- Check task processing
SELECT 
  id, status, 
  result->>'message' as message,
  result->>'discs_matched_regular' as regular_matches,
  result->>'discs_matched_vendor' as vendor_matches,
  processed_at
FROM t_task_queue 
WHERE id = ${testTaskId};

-- Check vendor debug info
SELECT 
  result->'vendor_debug_info' as vendor_debug
FROM t_task_queue 
WHERE id = ${testTaskId};
    `);
    
    console.log('\n🎯 SUCCESS INDICATORS:');
    console.log('✅ Task completes successfully');
    console.log('✅ Regular matches use OSL 19033 directly');
    console.log('✅ Vendor matches use redirect logic');
    console.log('✅ Each disc gets appropriate vendor_osl_id');
    console.log('✅ Debug info shows redirect information');
    
    // Show some example discs that might be affected
    console.log('\n=== EXAMPLE DISCS THAT MIGHT BE PROCESSED ===');
    
    const { data: exampleDiscs, error: exampleError } = await supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .eq('mps_id', osl.mps_id)
      .not('weight_mfg', 'is', null)
      .limit(5);
    
    if (!exampleError && exampleDiscs) {
      console.log(`Found ${exampleDiscs.length} example discs with MPS ${osl.mps_id}:`);
      exampleDiscs.forEach(disc => {
        const regularMatch = disc.weight >= osl.min_weight && disc.weight <= osl.max_weight;
        const vendorMatch = Math.round(disc.weight_mfg) >= osl.min_weight && Math.round(disc.weight_mfg) <= osl.max_weight;
        const soldStatus = disc.sold_date ? 'SOLD' : 'UNSOLD';
        
        console.log(`  Disc ${disc.id}: Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, ${soldStatus}`);
        console.log(`    Regular match: ${regularMatch ? '✅' : '❌'}, Vendor match potential: ${vendorMatch ? '✅' : '❌'}`);
        console.log(`    Current mappings: Regular=${disc.order_sheet_line_id}, Vendor=${disc.vendor_osl_id}`);
      });
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

testMatchOslToDiscsWithRedirect();
