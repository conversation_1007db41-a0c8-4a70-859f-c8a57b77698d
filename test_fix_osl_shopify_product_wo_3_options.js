// test_fix_osl_shopify_product_wo_3_options.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import { processFixOslShopifyProductWo3OptionsTask } from './processFixOslShopifyProductWo3OptionsTask.js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testFixOslShopifyProductWo3OptionsTask() {
  console.log('🧪 Testing fix_osl_shopify_product_wo_3_options task processor...');

  try {
    // Create a test task
    const testTask = {
      id: 'test-task-' + Date.now(),
      task_type: 'fix_osl_shopify_product_wo_3_options',
      payload: {
        mps_id: 12345, // Test MPS ID
        shopify_product_id: '8888888888888', // Test Shopify product ID
        original_task_id: 'test-original-task-' + Date.now()
      },
      status: 'pending',
      created_at: new Date().toISOString()
    };

    console.log('📝 Test task payload:', JSON.stringify(testTask.payload, null, 2));

    // Test the processor function
    console.log('🚀 Running processFixOslShopifyProductWo3OptionsTask...');
    
    // Note: This will fail on the Shopify deletion step since we're using a fake product ID,
    // but it should test the payload parsing and validation logic
    await processFixOslShopifyProductWo3OptionsTask(testTask);

    console.log('✅ Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testFixOslShopifyProductWo3OptionsTask();
}

export { testFixOslShopifyProductWo3OptionsTask };
