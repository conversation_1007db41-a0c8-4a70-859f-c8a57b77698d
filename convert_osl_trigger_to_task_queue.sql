-- Drop the old trigger
DROP TRIGGER IF EXISTS trig_handle_disc_matches_and_inv_on_osl_insert_or_delete_update ON t_order_sheet_lines;

-- Function to enqueue a task when an OSL is inserted
CREATE OR REPLACE FUNCTION fn_enqueue_osl_inserted_task()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert a task into the task queue
    INSERT INTO t_task_queue (
        task_type,
        payload,
        status,
        scheduled_at,
        created_at,
        enqueued_by
    ) VALUES (
        'osl_inserted',
        jsonb_build_object('id', NEW.id),
        'pending',
        NOW() + INTERVAL '5 minutes',
        NOW(),
        't_order_sheet_lines insert_trigger_' || NEW.id
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to enqueue a task when an OSL is updated
CREATE OR REPLACE FUNCTION fn_enqueue_osl_updated_task()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert a task into the task queue
    INSERT INTO t_task_queue (
        task_type,
        payload,
        status,
        scheduled_at,
        created_at,
        enqueued_by
    ) VALUES (
        'osl_updated',
        jsonb_build_object('id', NEW.id, 'old_data', row_to_json(OLD)::jsonb),
        'pending',
        NOW() + INTERVAL '5 minutes',
        NOW(),
        't_order_sheet_lines update_trigger_' || NEW.id
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to enqueue a task when an OSL is deleted
CREATE OR REPLACE FUNCTION fn_enqueue_osl_deleted_task()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert a task into the task queue
    INSERT INTO t_task_queue (
        task_type,
        payload,
        status,
        scheduled_at,
        created_at,
        enqueued_by
    ) VALUES (
        'set_inv_osl_to_0',
        jsonb_build_object('id', OLD.id),
        'pending',
        NOW() + INTERVAL '5 minutes',
        NOW(),
        't_order_sheet_lines delete_trigger_' || OLD.id
    );

    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create the INSERT trigger
CREATE TRIGGER trg_enqueue_osl_inserted
AFTER INSERT ON t_order_sheet_lines
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_osl_inserted_task();

-- Create the UPDATE trigger
CREATE TRIGGER trg_enqueue_osl_updated
AFTER UPDATE OF mps_id, color_id, min_weight, max_weight ON t_order_sheet_lines
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_osl_updated_task();

-- Create the DELETE trigger
CREATE TRIGGER trg_enqueue_osl_deleted
AFTER DELETE ON t_order_sheet_lines
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_osl_deleted_task();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'OSL triggers have been converted to use the task queue architecture.';
END $$;
