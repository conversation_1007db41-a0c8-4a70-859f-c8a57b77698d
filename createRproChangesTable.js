// createRproChangesTable.js - Create t_rpro_changes table (one-time helper)

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

const logFile = 'create_rpro_changes_table.log';
fs.writeFileSync(logFile, `Starting at ${new Date().toISOString()}\n`);

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  const msg = 'SUPABASE_URL and SUPABASE_KEY must be set in .env';
  fs.appendFileSync(logFile, msg + '\n');
  console.error(msg);
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

const createSql = `
CREATE TABLE IF NOT EXISTS public.t_rpro_changes (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  ivno INTEGER NOT NULL,
  field_changed TEXT NOT NULL,
  was TEXT,
  is_now TEXT,
  change_date TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX IF NOT EXISTS idx_t_rpro_changes_ivno ON public.t_rpro_changes(ivno);
`;

async function main() {
  try {
    // Try via exec_sql helper if present
    let { error } = await supabase.rpc('exec_sql', { sql_query: createSql });
    if (error) {
      fs.appendFileSync(logFile, `exec_sql failed: ${error.message}\n`);
      console.warn('exec_sql not available; please run the SQL below in Supabase SQL Editor:');
      console.log(createSql);
      fs.appendFileSync(logFile, 'Please run the SQL printed to console in Supabase SQL Editor.\n');
      return;
    }
    fs.appendFileSync(logFile, 't_rpro_changes ensured/created successfully.\n');
    console.log('t_rpro_changes ensured/created successfully.');
  } catch (e) {
    fs.appendFileSync(logFile, `Error: ${e.message}\n`);
    console.error(e);
  }
}

main();

