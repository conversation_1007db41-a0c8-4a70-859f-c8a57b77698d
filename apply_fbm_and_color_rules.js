import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function applyFbmAndColorRules() {
  try {
    console.log('Applying FBM deleted and color variation rules...\n');

    // Handle FBM deleted records
    await handleFbmDeletedRecords();

    // Handle color_id 23 with "Colors May Vary"
    await handleColorVariationCleanup();

    console.log('\nFBM and color rules applied successfully!');

  } catch (error) {
    console.error('Error:', error);
  }
}

async function handleFbmDeletedRecords() {
  console.log('Handling FBM deleted records...');

  const { data: fbmRecords, error: fetchError } = await supabase
    .from('t_sdasins')
    .select('id, notes, raw_notes')
    .like('notes', '%fbm deleted%')
    .not('notes', 'like', 'XXXX%');

  if (fetchError) {
    console.error('Error fetching FBM deleted records:', fetchError);
    return;
  }

  let fbmUpdated = 0;

  for (const record of fbmRecords || []) {
    const updateData = {
      notes: 'XXXX FBM Deleted'
    };

    // Store original notes in raw_notes if not already stored
    if (!record.raw_notes) {
      updateData.raw_notes = record.notes;
    }

    const { error: updateError } = await supabase
      .from('t_sdasins')
      .update(updateData)
      .eq('id', record.id);

    if (updateError) {
      console.error(`Error updating record ${record.id}:`, updateError);
    } else {
      fbmUpdated++;
      if (fbmUpdated <= 5) { // Show first 5 updates
        console.log(`  Updated ID ${record.id}: XXXX FBM Deleted`);
      }
    }
  }

  console.log(`Updated ${fbmUpdated} FBM deleted records\n`);
}

async function handleColorVariationCleanup() {
  console.log('Handling color_id 23 with "Colors May Vary" cleanup...');

  // Find records with color_id = 23 that still contain "Colors May Vary"
  const { data: colorRecords, error: fetchError } = await supabase
    .from('t_sdasins')
    .select('id, notes, raw_notes, color_id')
    .eq('color_id', 23)
    .like('notes', '%Colors May Vary%')
    .not('notes', 'like', 'XXXX%');

  if (fetchError) {
    console.error('Error fetching color variation records:', fetchError);
    return;
  }

  let colorUpdated = 0;

  for (const record of colorRecords || []) {
    // Remove "Colors May Vary" from notes
    const updatedNotes = record.notes.replace(/Colors May Vary/gi, '').trim();
    
    // Clean up extra spaces and punctuation
    const cleanedNotes = updatedNotes
      .replace(/\s+/g, ' ')
      .replace(/^\s*[-|,\[\]]\s*/, '')
      .replace(/\s*[-|,\[\]]\s*$/, '')
      .trim();

    const updateData = {
      notes: cleanedNotes
    };

    // Store original notes in raw_notes if not already stored
    if (!record.raw_notes) {
      updateData.raw_notes = record.notes;
    }

    const { error: updateError } = await supabase
      .from('t_sdasins')
      .update(updateData)
      .eq('id', record.id);

    if (updateError) {
      console.error(`Error updating record ${record.id}:`, updateError);
    } else {
      colorUpdated++;
      if (colorUpdated <= 5) { // Show first 5 updates
        console.log(`  Updated ID ${record.id}: removed "Colors May Vary"`);
      }
    }
  }

  console.log(`Updated ${colorUpdated} records with color_id 23 to remove "Colors May Vary"\n`);

  // Also check for "Colors Will Vary" with color_id 23
  console.log('Checking for "Colors Will Vary" with color_id 23...');

  const { data: colorWillVaryRecords, error: willVaryError } = await supabase
    .from('t_sdasins')
    .select('id, notes, raw_notes, color_id')
    .eq('color_id', 23)
    .like('notes', '%Colors Will Vary%')
    .not('notes', 'like', 'XXXX%');

  if (willVaryError) {
    console.error('Error fetching "Colors Will Vary" records:', willVaryError);
    return;
  }

  let willVaryUpdated = 0;

  for (const record of colorWillVaryRecords || []) {
    // Remove "Colors Will Vary" from notes
    const updatedNotes = record.notes.replace(/Colors Will Vary/gi, '').trim();
    
    // Clean up extra spaces and punctuation
    const cleanedNotes = updatedNotes
      .replace(/\s+/g, ' ')
      .replace(/^\s*[-|,\[\]]\s*/, '')
      .replace(/\s*[-|,\[\]]\s*$/, '')
      .trim();

    const updateData = {
      notes: cleanedNotes
    };

    // Store original notes in raw_notes if not already stored
    if (!record.raw_notes) {
      updateData.raw_notes = record.notes;
    }

    const { error: updateError } = await supabase
      .from('t_sdasins')
      .update(updateData)
      .eq('id', record.id);

    if (updateError) {
      console.error(`Error updating record ${record.id}:`, updateError);
    } else {
      willVaryUpdated++;
      if (willVaryUpdated <= 5) { // Show first 5 updates
        console.log(`  Updated ID ${record.id}: removed "Colors Will Vary"`);
      }
    }
  }

  console.log(`Updated ${willVaryUpdated} records with color_id 23 to remove "Colors Will Vary"`);
}

// Run the script
applyFbmAndColorRules();
