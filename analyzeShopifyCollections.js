import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Shopify configuration - extract domain from endpoint
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

// Extract domain from the endpoint URL
let shopifyDomain = 'dzdiscs-new-releases.myshopify.com'; // fallback
if (shopifyEndpoint) {
  const match = shopifyEndpoint.match(/https:\/\/([^\/]+)/);
  if (match) {
    shopifyDomain = match[1];
  }
}

if (!shopifyAccessToken) {
  console.error('ERROR: Missing SHOPIFY_ACCESS_TOKEN in environment variables.');
  process.exit(1);
}

/**
 * Fetch all smart collections from Shopify with pagination
 */
async function fetchAllShopifyCollections() {
  console.log('📊 Fetching all Shopify smart collections...');
  
  const allCollections = [];
  let nextPageInfo = null;
  let pageCount = 0;
  
  do {
    pageCount++;
    console.log(`   Fetching page ${pageCount}...`);
    
    // Build URL with pagination
    let url = `https://${shopifyDomain}/admin/api/2024-01/smart_collections.json?limit=250`;
    if (nextPageInfo) {
      url += `&page_info=${nextPageInfo}`;
    }
    
    try {
      const response = await fetch(url, {
        headers: {
          'X-Shopify-Access-Token': shopifyAccessToken,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      const collections = data.smart_collections || [];
      
      console.log(`   Found ${collections.length} collections on page ${pageCount}`);
      allCollections.push(...collections);
      
      // Check for next page using Link header
      const linkHeader = response.headers.get('Link');
      nextPageInfo = null;
      
      if (linkHeader) {
        const nextMatch = linkHeader.match(/<[^>]*[?&]page_info=([^&>]+)[^>]*>;\s*rel="next"/);
        if (nextMatch) {
          nextPageInfo = nextMatch[1];
        }
      }
      
      // Rate limiting - Shopify allows 2 requests per second
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error) {
      console.error(`❌ Error fetching collections page ${pageCount}:`, error.message);
      throw error;
    }
  } while (nextPageInfo);
  
  console.log(`✅ Total collections fetched: ${allCollections.length}`);
  return allCollections;
}

/**
 * Check if a collection has any products
 */
async function checkCollectionProducts(collectionId) {
  try {
    const response = await fetch(
      `https://${shopifyDomain}/admin/api/2024-01/products.json?collection_id=${collectionId}&limit=1`,
      {
        headers: {
          'X-Shopify-Access-Token': shopifyAccessToken,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (!response.ok) {
      console.warn(`⚠️  Could not check products for collection ${collectionId}: ${response.status}`);
      return null;
    }
    
    const data = await response.json();
    return (data.products && data.products.length > 0);
    
  } catch (error) {
    console.warn(`⚠️  Error checking products for collection ${collectionId}:`, error.message);
    return null;
  }
}

/**
 * Analyze collections and find candidates for deletion
 */
async function analyzeCollections() {
  try {
    // Fetch all collections
    const collections = await fetchAllShopifyCollections();
    
    console.log('\n📊 Analyzing collections...');
    
    // Sort by updated_at (oldest first)
    collections.sort((a, b) => new Date(a.updated_at) - new Date(b.updated_at));
    
    // Analyze collections
    const analysis = {
      total: collections.length,
      oldestCollections: [],
      emptyCollections: [],
      collectionsWithProducts: [],
      byYear: {}
    };
    
    console.log('\n🔍 Checking for empty collections (this may take a while)...');
    
    for (let i = 0; i < collections.length; i++) {
      const collection = collections[i];
      const updatedYear = new Date(collection.updated_at).getFullYear();
      
      // Track by year
      if (!analysis.byYear[updatedYear]) {
        analysis.byYear[updatedYear] = 0;
      }
      analysis.byYear[updatedYear]++;
      
      // Check if collection has products (for first 50 oldest collections)
      let hasProducts = null;
      if (i < 50) {
        console.log(`   Checking collection ${i + 1}/50: ${collection.title} (${collection.handle})`);
        hasProducts = await checkCollectionProducts(collection.id);
        
        // Rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      const collectionInfo = {
        id: collection.id,
        title: collection.title,
        handle: collection.handle,
        updated_at: collection.updated_at,
        created_at: collection.created_at,
        published: collection.published,
        hasProducts: hasProducts
      };
      
      // Add to oldest collections (first 20)
      if (i < 20) {
        analysis.oldestCollections.push(collectionInfo);
      }
      
      // Track empty vs non-empty (for checked collections only)
      if (hasProducts === false) {
        analysis.emptyCollections.push(collectionInfo);
      } else if (hasProducts === true) {
        analysis.collectionsWithProducts.push(collectionInfo);
      }
    }
    
    return { collections, analysis };
    
  } catch (error) {
    console.error('❌ Error analyzing collections:', error.message);
    throw error;
  }
}

/**
 * Display analysis results
 */
function displayResults(analysis) {
  console.log('\n' + '='.repeat(60));
  console.log('📊 SHOPIFY COLLECTIONS ANALYSIS RESULTS');
  console.log('='.repeat(60));
  
  console.log(`\n📈 SUMMARY:`);
  console.log(`   Total Collections: ${analysis.total}`);
  console.log(`   Empty Collections Found: ${analysis.emptyCollections.length}`);
  console.log(`   Collections with Products: ${analysis.collectionsWithProducts.length}`);
  
  console.log(`\n📅 COLLECTIONS BY YEAR:`);
  Object.keys(analysis.byYear)
    .sort()
    .forEach(year => {
      console.log(`   ${year}: ${analysis.byYear[year]} collections`);
    });
  
  console.log(`\n🕰️  20 OLDEST COLLECTIONS:`);
  analysis.oldestCollections.forEach((collection, index) => {
    const updatedDate = new Date(collection.updated_at).toLocaleDateString();
    const hasProductsText = collection.hasProducts === null ? 'not checked' : 
                           collection.hasProducts ? 'has products' : 'EMPTY';
    console.log(`   ${index + 1}. ${collection.title}`);
    console.log(`      Handle: ${collection.handle}`);
    console.log(`      Updated: ${updatedDate}`);
    console.log(`      Status: ${hasProductsText}`);
    console.log(`      ID: ${collection.id}`);
    console.log('');
  });
  
  if (analysis.emptyCollections.length > 0) {
    console.log(`\n🗑️  EMPTY COLLECTIONS (CANDIDATES FOR DELETION):`);
    analysis.emptyCollections.forEach((collection, index) => {
      const updatedDate = new Date(collection.updated_at).toLocaleDateString();
      console.log(`   ${index + 1}. ${collection.title} (${collection.handle})`);
      console.log(`      Updated: ${updatedDate} | ID: ${collection.id}`);
    });
  }
  
  console.log('\n' + '='.repeat(60));
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Starting Shopify Collections Analysis...\n');
  
  try {
    const { collections, analysis } = await analyzeCollections();
    
    displayResults(analysis);
    
    // Save detailed results to file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `shopify_collections_analysis_${timestamp}.json`;
    
    fs.writeFileSync(filename, JSON.stringify({
      timestamp: new Date().toISOString(),
      analysis,
      allCollections: collections.map(c => ({
        id: c.id,
        title: c.title,
        handle: c.handle,
        updated_at: c.updated_at,
        created_at: c.created_at,
        published: c.published
      }))
    }, null, 2));
    
    console.log(`\n💾 Detailed results saved to: ${filename}`);
    console.log('\n✅ Analysis complete!');
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    process.exit(1);
  }
}

// Run the analysis
main();
