// check_veeqo_table_structure.js
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkVeeqoTableStructure() {
  try {
    // Get a single row from the table to see its structure
    const { data, error } = await supabase
      .from('imported_table_veeqo_sellables_export')
      .select('*')
      .limit(1);

    if (error) {
      console.error('Error fetching table structure:', error);
      
      // Try to get the table definition from information_schema
      const { data: columns, error: columnsError } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type')
        .eq('table_name', 'imported_table_veeqo_sellables_export');
        
      if (columnsError) {
        console.error('Error fetching columns from information_schema:', columnsError);
      } else if (columns && columns.length > 0) {
        console.log('Table columns:');
        columns.forEach(col => {
          console.log(`${col.column_name}: ${col.data_type}`);
        });
      } else {
        console.log('No columns found in information_schema');
      }
      
      return;
    }

    if (data && data.length > 0) {
      console.log('Table structure:');
      console.log(JSON.stringify(data[0], null, 2));
      
      // Get the column names
      console.log('\nColumn names:');
      Object.keys(data[0]).forEach(key => {
        console.log(`- ${key}`);
      });
    } else {
      console.log('No rows found in the table');
      
      // Try to get the table definition from information_schema
      const { data: columns, error: columnsError } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type')
        .eq('table_name', 'imported_table_veeqo_sellables_export');
        
      if (columnsError) {
        console.error('Error fetching columns from information_schema:', columnsError);
      } else if (columns && columns.length > 0) {
        console.log('Table columns:');
        columns.forEach(col => {
          console.log(`${col.column_name}: ${col.data_type}`);
        });
      } else {
        console.log('No columns found in information_schema');
      }
    }
  } catch (err) {
    console.error('Exception checking table structure:', err);
  }
}

checkVeeqoTableStructure();
