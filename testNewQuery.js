import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testNewQuery() {
  try {
    console.log('🧪 Testing new query logic...\n');
    
    // Test the new query logic that gets ALL orderable products using pagination
    console.log('1. Getting ALL orderable products from base table...');
    let allOrderableData = [];
    let from = 0;
    const pageSize = 1000;

    while (true) {
      const { data: batch, error: orderableError } = await supabase
        .from('it_discraft_order_sheet_lines')
        .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
        .eq('is_orderable', true)
        .not('excel_mapping_key', 'is', null)
        .range(from, from + pageSize - 1)
        .order('excel_mapping_key');

      if (orderableError) {
        console.error('❌ Error querying orderable data:', orderableError);
        return;
      }

      if (batch.length === 0) break;

      allOrderableData = allOrderableData.concat(batch);
      from += pageSize;

      if (batch.length < pageSize) break; // Last page
    }

    console.log(`✅ Found ${allOrderableData.length} orderable products with Excel mapping`);

    // Test the view query for comparison using pagination
    console.log('\n2. Getting order quantities from view...');
    let viewOrderData = [];
    from = 0;

    while (true) {
      const { data: batch, error: viewError } = await supabase
        .from('v_stats_by_osl_discraft')
        .select('excel_mapping_key, "order"')
        .not('excel_mapping_key', 'is', null)
        .range(from, from + pageSize - 1);

      if (viewError) {
        console.error('❌ Error querying view data:', viewError);
        return;
      }

      if (batch.length === 0) break;

      viewOrderData = viewOrderData.concat(batch);
      from += pageSize;

      if (batch.length < pageSize) break; // Last page
    }

    console.log(`✅ Found ${viewOrderData.length} products with order data in view`);

    // Create a map of order quantities by excel_mapping_key
    const orderMap = {};
    viewOrderData.forEach(item => {
      orderMap[item.excel_mapping_key] = item.order || 0;
    });

    // Combine all orderable products with their order quantities (defaulting to 0)
    const finalOrderData = allOrderableData.map(item => ({
      excel_mapping_key: item.excel_mapping_key,
      excel_column: item.excel_column,
      excel_row_hint: item.excel_row_hint,
      order: orderMap[item.excel_mapping_key] || 0
    }));

    console.log(`\n📊 Final combined data: ${finalOrderData.length} records`);

    // Count how many have orders vs 0
    const withOrders = finalOrderData.filter(item => item.order > 0).length;
    const withZeros = finalOrderData.filter(item => item.order === 0).length;

    console.log(`   • Records with orders > 0: ${withOrders}`);
    console.log(`   • Records with order = 0: ${withZeros}`);

    // Check some records after line 332
    const afterLine332 = finalOrderData.filter(item => item.excel_row_hint > 332);
    console.log(`\n📋 Records after line 332: ${afterLine332.length}`);
    
    if (afterLine332.length > 0) {
      console.log('Sample records after line 332:');
      afterLine332.slice(0, 10).forEach((record, index) => {
        console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: order=${record.order}`);
      });
    }

    console.log('\n🎉 Test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testNewQuery().catch(console.error);
