import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Calculate weight range tags for OSL products based on their weight range
 * @param {number} minWeight - The minimum weight
 * @param {number} maxWeight - The maximum weight
 * @returns {Array} - Array of weight range tags that overlap with the OSL range
 */
function calculateOslWeightRangeTags(minWeight, maxWeight) {
  const weightRangeTags = [];
  
  // Check each possible weight range to see if it overlaps with the OSL range
  const possibleRanges = [
    { min: 10, max: 49.5, tag: 'wt_rng_10-49' },
    { min: 50, max: 99.5, tag: 'wt_rng_50-99' },
    { min: 100, max: 119.5, tag: 'wt_rng_100-119' },
    { min: 120, max: 139.5, tag: 'wt_rng_120-139' },
    { min: 140, max: 149.5, tag: 'wt_rng_140-149' },
    { min: 150, max: 159.5, tag: 'wt_rng_150-159' },
    { min: 160, max: 169.5, tag: 'wt_rng_160-169' },
    { min: 170, max: 174.5, tag: 'wt_rng_170-174' },
    { min: 175, max: 180.5, tag: 'wt_rng_175-180' },
    { min: 181, max: 200, tag: 'wt_rng_181-200' },
    { min: 201, max: 249, tag: 'wt_rng_201-249' }
  ];
  
  for (const range of possibleRanges) {
    // Check if ranges overlap: OSL range overlaps with weight range if max of one >= min of other
    if (maxWeight >= range.min && minWeight <= range.max) {
      weightRangeTags.push(range.tag);
    }
  }
  
  return weightRangeTags;
}

/**
 * Get a small sample of OSL products for testing
 */
async function getTestOslProducts(limit = 5) {
  try {
    console.log(`📊 Querying ${limit} test OSL products for weight range tag updates...`);
    
    const { data: oslProducts, error } = await supabase
      .from('t_order_sheet_lines')
      .select('id, min_weight, max_weight')
      .not('shopify_uploaded_at', 'is', null)
      .not('min_weight', 'is', null)
      .not('max_weight', 'is', null)
      .order('id')
      .limit(limit);

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    console.log(`📊 Found ${oslProducts.length} test OSL products`);
    return oslProducts;
  } catch (error) {
    console.error(`❌ Error querying test OSL products:`, error.message);
    throw error;
  }
}

/**
 * Enqueue fix_osl_weight_range tasks for test OSL products
 */
async function enqueueTestOslTasks(limit = 5, dryRun = false) {
  try {
    const oslProducts = await getTestOslProducts(limit);
    
    if (oslProducts.length === 0) {
      console.log('ℹ️ No test OSL products found');
      return;
    }

    console.log(`🚀 ${dryRun ? 'DRY RUN: Would enqueue' : 'Enqueueing'} ${oslProducts.length} test fix_osl_weight_range tasks...`);
    
    const now = new Date();
    const scheduledAt = new Date(now.getTime() + 1 * 60 * 1000); // Schedule 1 minute in future for testing
    
    // Prepare tasks
    const tasks = [];
    let validOslProducts = 0;
    let skippedOslProducts = 0;
    
    for (const osl of oslProducts) {
      const weightRangeTags = calculateOslWeightRangeTags(osl.min_weight, osl.max_weight);
      
      if (weightRangeTags.length === 0) {
        console.log(`⚠️ Skipping OSL ${osl.id} with weight range ${osl.min_weight}g-${osl.max_weight}g (no matching weight range tags)`);
        skippedOslProducts++;
        continue;
      }
      
      console.log(`✅ OSL ${osl.id}: weight range ${osl.min_weight}g-${osl.max_weight}g → tags [${weightRangeTags.join(', ')}]`);
      
      tasks.push({
        task_type: 'fix_osl_weight_range',
        payload: {
          osl_id: osl.id,
          min_weight: osl.min_weight,
          max_weight: osl.max_weight,
          expected_weight_range_tags: weightRangeTags
        },
        status: 'pending',
        scheduled_at: scheduledAt.toISOString(),
        created_at: now.toISOString(),
        enqueued_by: `osl_weight_range_test_batch_${now.getTime()}`
      });
      
      validOslProducts++;
    }
    
    console.log(`✅ Prepared ${validOslProducts} test OSL tasks`);
    console.log(`⚠️ Skipped ${skippedOslProducts} OSL products with no matching weight range tags`);
    
    if (dryRun) {
      console.log('🧪 DRY RUN: Tasks prepared but not enqueued');
      tasks.forEach((task, index) => {
        console.log(`Task ${index + 1}:`, JSON.stringify(task, null, 2));
      });
      return;
    }

    // Insert tasks
    const { error: insertError } = await supabase
      .from('t_task_queue')
      .insert(tasks);

    if (insertError) {
      console.error(`❌ Error enqueueing test OSL tasks:`, insertError);
      throw insertError;
    }
    
    console.log(`🎉 Successfully enqueued ${validOslProducts} test fix_osl_weight_range tasks`);
    console.log(`📅 Tasks scheduled to start at: ${scheduledAt.toISOString()}`);
    
  } catch (error) {
    console.error(`❌ Error enqueueing test OSL tasks:`, error.message);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    // Parse command line arguments
    const args = process.argv.slice(2);
    const dryRun = args.includes('--dry-run');
    const limitArg = args.find(arg => arg.startsWith('--limit='));
    const limit = limitArg ? parseInt(limitArg.split('=')[1]) : 5;
    
    if (dryRun) {
      console.log('🧪 Running in DRY RUN mode - no tasks will be enqueued');
    }
    
    console.log(`🧪 Testing OSL weight range update system with ${limit} OSL products`);
    
    await enqueueTestOslTasks(limit, dryRun);
    
  } catch (error) {
    console.error('❌ Test script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
