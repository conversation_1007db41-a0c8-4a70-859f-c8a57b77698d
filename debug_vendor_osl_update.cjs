require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function debugUpdate() {
  try {
    console.log('Debugging vendor OSL update...');
    
    // Get a sample disc that needs updating
    const { data: sampleDiscs, error: sampleError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null)
      .limit(3);
    
    if (sampleError) {
      console.error('Error getting sample discs:', sampleError);
      return;
    }
    
    if (!sampleDiscs || sampleDiscs.length === 0) {
      console.log('No discs found that need updating');
      return;
    }
    
    console.log(`Found ${sampleDiscs.length} sample discs to debug:`);
    
    for (const disc of sampleDiscs) {
      console.log(`\n=== Debugging Disc ${disc.id} ===`);
      console.log(`MPS ID: ${disc.mps_id}`);
      console.log(`Weight: ${disc.weight}`);
      console.log(`Weight MFG: ${disc.weight_mfg}`);
      console.log(`Color ID: ${disc.color_id}`);
      console.log(`Current order_sheet_line_id: ${disc.order_sheet_line_id}`);
      console.log(`Current vendor_osl_id: ${disc.vendor_osl_id}`);
      
      // Test the vendor OSL function directly
      console.log('\nTesting find_matching_osl_by_mfg_weight function...');
      const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
        'find_matching_osl_by_mfg_weight',
        {
          mps_id_param: disc.mps_id,
          color_id_param: disc.color_id,
          weight_mfg_param: disc.weight_mfg
        }
      );
      
      if (vendorOslError) {
        console.error('Error calling vendor OSL function:', vendorOslError);
        continue;
      }
      
      console.log('Vendor OSL function result:', vendorOslData);
      
      const vendorOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
      
      if (vendorOslId) {
        console.log(`✅ Found vendor OSL: ${vendorOslId}`);
        
        // Try to update this specific disc
        console.log('Attempting to update this disc...');
        const { error: updateError } = await supabase
          .from('t_discs')
          .update({ vendor_osl_id: vendorOslId })
          .eq('id', disc.id);
        
        if (updateError) {
          console.error('Error updating disc:', updateError);
        } else {
          console.log('✅ Successfully updated disc!');
        }
      } else {
        console.log('❌ No vendor OSL found');
        
        // Check what OSLs exist for this MPS and color
        const { data: availableOsls, error: oslError } = await supabase
          .from('t_order_sheet_lines')
          .select('id, min_weight, max_weight, color_id')
          .eq('mps_id', disc.mps_id)
          .in('color_id', [disc.color_id, 23]);
        
        if (!oslError && availableOsls) {
          console.log('Available OSLs for this MPS and color:');
          availableOsls.forEach(osl => {
            const roundedWeight = Math.round(disc.weight_mfg);
            const inRange = roundedWeight >= osl.min_weight && roundedWeight <= osl.max_weight;
            console.log(`  OSL ${osl.id}: weight range ${osl.min_weight}-${osl.max_weight}, color ${osl.color_id}, fits rounded weight ${roundedWeight}? ${inRange}`);
          });
        }
      }
    }
    
    // Now check if the bulk update SQL would work
    console.log('\n=== Testing Bulk Update SQL ===');
    const testSql = `
      SELECT 
        d.id,
        d.weight_mfg,
        ROUND(d.weight_mfg) as rounded_weight_mfg,
        (
          SELECT osl.id
          FROM t_order_sheet_lines osl
          WHERE osl.mps_id = d.mps_id
            AND (osl.color_id = d.color_id OR osl.color_id = 23)
            AND ROUND(d.weight_mfg) >= osl.min_weight
            AND ROUND(d.weight_mfg) <= osl.max_weight
          LIMIT 1
        ) as potential_vendor_osl_id
      FROM t_discs d
      WHERE d.vendor_osl_id IS NULL
        AND d.weight_mfg IS NOT NULL
        AND d.mps_id IS NOT NULL
        AND d.color_id IS NOT NULL
      LIMIT 5;
    `;
    
    const { data: testResults, error: testError } = await supabase.rpc('exec_sql', {
      sql_query: testSql
    });
    
    if (testError) {
      console.error('Error testing bulk SQL:', testError);
    } else {
      console.log('Test SQL results:', testResults);
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

debugUpdate();
