import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testFilenameInAutomation() {
  try {
    console.log('🧪 Testing filename format in automation export...\n');
    
    // Simulate the export step from automation
    console.log('📄 Testing export with new filename format...');
    
    const response = await fetch('http://localhost:3001/api/discraft/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        includeHeader: true,
        filename: `discraft_order_${new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0]}.xlsx`
      })
    });
    
    if (!response.ok) {
      console.error(`❌ HTTP Error: ${response.status} ${response.statusText}`);
      return;
    }
    
    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Export successful!');
      console.log(`📁 Filename: ${data.filename}`);
      console.log(`📍 Full path: ${data.filePath}`);
      console.log(`📊 Records exported: ${data.totalRecords}`);
      console.log(`📋 Header included: ${data.headerIncluded}`);
      
      // Verify the filename format
      const expectedPattern = /discraft_order_\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}\.xlsx/;
      if (expectedPattern.test(data.filename)) {
        console.log('✅ Filename format is correct: YYYY-MM-DD-HH-MM-SS');
      } else {
        console.log('❌ Filename format is incorrect');
      }
      
    } else {
      console.error(`❌ Export failed: ${data.error}`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testFilenameInAutomation().catch(console.error);
