-- Enqueue product readiness checks when a brand's Shopify collection is created (NULL -> NOT NULL)
CREATE OR REPLACE FUNCTION public.enqueue_product_ready_when_brand_collection_created()
RETURNS TRIGGER AS $$
BEGIN
  -- Guard: only act on NULL -> NOT NULL transitions
  IF (OLD.shopify_collection_created_at IS NULL AND NEW.shopify_collection_created_at IS NOT NULL) THEN
    INSERT INTO public.t_task_queue (task_type, payload, status, scheduled_at, created_at, enqueued_by)
    SELECT
      'check_if_product_is_ready' AS task_type,
      jsonb_build_object('id', p.id) AS payload,
      'pending' AS status,
      NOW() AS scheduled_at,
      NOW() AS created_at,
      'trigger:t_brands.collection_created' AS enqueued_by
    FROM public.t_products p
    WHERE p.brand_id = NEW.id
      AND p.shopify_uploaded_at IS NULL
      AND NOT EXISTS (
        SELECT 1 FROM public.t_task_queue tq
        WHERE tq.task_type = 'check_if_product_is_ready'
          AND (tq.status = 'pending' OR tq.status = 'processing')
          AND (tq.payload->>'id')::INT = p.id
      );
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop and recreate trigger
DROP TRIGGER IF EXISTS trg_t_brands_collection_created_enqueue_product_ready ON public.t_brands;
CREATE TRIGGER trg_t_brands_collection_created_enqueue_product_ready
AFTER UPDATE OF shopify_collection_created_at ON public.t_brands
FOR EACH ROW
WHEN (OLD.shopify_collection_created_at IS NULL AND NEW.shopify_collection_created_at IS NOT NULL)
EXECUTE FUNCTION public.enqueue_product_ready_when_brand_collection_created();

