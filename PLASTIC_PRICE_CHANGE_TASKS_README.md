# Plastic Price Change Task Processors

This document describes the implementation of task types for handling plastic price changes and updating Shopify variant prices.

## Overview

When a plastic's retail price or MSRP changes, we need to update the prices of all related disc and OSL variants on Shopify. This is handled through a hierarchical task queue system:

### Retail Price Changes:
1. **`plastic_retail_price_change`** - Parent task that finds all affected discs and OSLs
2. **`update_disc_variant_price_on_shopify`** - Child task that updates individual variant prices

### MSRP Price Changes:
1. **`plastic_msrp_price_change`** - Parent task that finds all affected discs and OSLs
2. **`update_disc_variant_msrp_on_shopify`** - Child task that updates individual variant "compare at" prices

### MPS Override Price Changes:
1. **`mps_override_prices_changed_so_update_shopify`** - Parent task that finds all discs and OSLs using a specific MPS
2. **Reuses existing child tasks** - `update_disc_variant_price_on_shopify` and `update_disc_variant_msrp_on_shopify`

## Task Types

### 1. plastic_retail_price_change

**Purpose**: Processes a plastic retail price change by finding all related discs that need price updates.

**Trigger**: Automatically triggered when `t_plastics.val_retail_price` is updated (via database trigger).

**Payload**:
```json
{
  "id": 123,
  "old_retail_price": 18.99,
  "new_retail_price": 19.99,
  "plastic_name": "Champion"
}
```

**Logic**:
1. Finds all discs that are:
   - Not sold (`sold_date IS NULL`)
   - Already uploaded to Shopify (`shopify_uploaded_at IS NOT NULL`)
   - Have an MPS with the specified `plastic_id`
   - MPS does NOT have `val_override_retail_price` (price not overridden)
2. Finds all order sheet lines (OSLs) that are:
   - Already uploaded to Shopify (`shopify_uploaded_at IS NOT NULL`)
   - Have an MPS with the specified `plastic_id`
   - MPS does NOT have `val_override_retail_price` (price not overridden)
3. Enqueues individual `update_disc_variant_price_on_shopify` tasks for each qualifying disc and OSL

**Result**:
```json
{
  "message": "Successfully enqueued 30 price update tasks (25 discs + 5 OSLs), 0 errors",
  "plastic_id": 123,
  "plastic_name": "Champion",
  "new_retail_price": 19.99,
  "discs_found": 25,
  "osls_found": 5,
  "total_items_found": 30,
  "tasks_enqueued": 30,
  "errors": 0
}
```

### 2. update_disc_variant_price_on_shopify

**Purpose**: Updates a specific disc or OSL variant price on Shopify.

**Trigger**: Enqueued by `plastic_retail_price_change` task or manually.

**Payload**:
```json
{
  "id": 456789,
  "item_type": "disc",
  "new_retail_price": 19.99
}
```

**Logic**:
1. Generates SKU based on item type:
   - Disc SKU: `'D' + disc.id` (e.g., "D456789")
   - OSL SKU: `'OS' + osl.id` (e.g., "OS16560")
2. Finds the Shopify variant using GraphQL API
3. Updates the variant price using REST API
4. Handles cases where variant is not found or price is already correct

**Result**:
```json
{
  "message": "Successfully updated price for disc id=456789 from 18.99 to 19.99",
  "item_id": 456789,
  "item_type": "disc",
  "sku": "D456789",
  "variant_id": "12345678901234567890",
  "product_title": "Innova Champion Destroyer",
  "old_price": 18.99,
  "new_price": 19.99,
  "status": "updated"
}
```

### 3. plastic_msrp_price_change

**Purpose**: Processes a plastic MSRP price change by finding all related discs and OSLs that need MSRP updates.

**Trigger**: Automatically triggered when `t_plastics.val_msrp` is updated (via database trigger).

**Payload**:
```json
{
  "id": 123,
  "old_msrp_price": 21.99,
  "new_msrp_price": 22.99,
  "plastic_name": "Champion"
}
```

**Logic**:
1. Finds all discs that are:
   - Not sold (`sold_date IS NULL`)
   - Already uploaded to Shopify (`shopify_uploaded_at IS NOT NULL`)
   - Have an MPS with the specified `plastic_id`
   - MPS does NOT have `val_override_msrp` (MSRP not overridden)
2. Finds all order sheet lines (OSLs) that are:
   - Already uploaded to Shopify (`shopify_uploaded_at IS NOT NULL`)
   - Have an MPS with the specified `plastic_id`
   - MPS does NOT have `val_override_msrp` (MSRP not overridden)
3. Enqueues individual `update_disc_variant_msrp_on_shopify` tasks for each qualifying disc and OSL

**Result**:
```json
{
  "message": "Successfully enqueued 30 MSRP update tasks (25 discs + 5 OSLs), 0 errors",
  "plastic_id": 123,
  "plastic_name": "Champion",
  "new_msrp_price": 22.99,
  "discs_found": 25,
  "osls_found": 5,
  "total_items_found": 30,
  "tasks_enqueued": 30,
  "errors": 0
}
```

### 4. update_disc_variant_msrp_on_shopify

**Purpose**: Updates a specific disc or OSL variant "compare at" price (MSRP) on Shopify.

**Trigger**: Enqueued by `plastic_msrp_price_change` task or manually.

**Payload**:
```json
{
  "id": 456789,
  "item_type": "disc",
  "new_msrp_price": 22.99
}
```

**Logic**:
1. Generates SKU based on item type:
   - Disc SKU: `'D' + disc.id` (e.g., "D456789")
   - OSL SKU: `'OS' + osl.id` (e.g., "OS16560")
2. Finds the Shopify variant using GraphQL API
3. Updates the variant `compare_at_price` using REST API
4. Handles cases where variant is not found or MSRP is already correct

**Result**:
```json
{
  "message": "Successfully updated MSRP for disc id=456789 from 21.99 to 22.99",
  "item_id": 456789,
  "item_type": "disc",
  "sku": "D456789",
  "variant_id": "12345678901234567890",
  "product_title": "Innova Champion Destroyer",
  "old_msrp": 21.99,
  "new_msrp": 22.99,
  "status": "updated"
}
```

### 5. mps_override_prices_changed_so_update_shopify

**Purpose**: Processes MPS override price changes by finding all related discs and OSLs that use the specific MPS.

**Trigger**: Automatically triggered when `t_mps.val_override_retail_price` or `t_mps.val_override_msrp` is updated (via database trigger).

**Payload**:
```json
{
  "id": 18810,
  "retail_price_changed": true,
  "msrp_price_changed": false,
  "old_override_retail_price": 20.99,
  "new_override_retail_price": 21.99,
  "old_override_msrp": null,
  "new_override_msrp": null,
  "mps_description": "Destroyer Champion Halo"
}
```

**Logic**:
1. Finds all discs that are:
   - Not sold (`sold_date IS NULL`)
   - Already uploaded to Shopify (`shopify_uploaded_at IS NOT NULL`)
   - Use the specified `mps_id`
2. Finds all order sheet lines (OSLs) that are:
   - Already uploaded to Shopify (`shopify_uploaded_at IS NOT NULL`)
   - Use the specified `mps_id`
3. For each item, enqueues appropriate child tasks:
   - `update_disc_variant_price_on_shopify` if retail price changed
   - `update_disc_variant_msrp_on_shopify` if MSRP changed

**Result**:
```json
{
  "message": "Successfully enqueued 15 price update tasks (10 discs + 5 OSLs), 0 errors",
  "mps_id": 18810,
  "mps_description": "Destroyer Champion Halo",
  "retail_price_changed": true,
  "msrp_price_changed": false,
  "override_retail_price": 21.99,
  "override_msrp": null,
  "discs_found": 10,
  "osls_found": 5,
  "total_items_found": 15,
  "tasks_enqueued": 15,
  "errors": 0
}
```

## Files Created

### Core Processors
- **`processPlasticRetailPriceChangeTask.js`** - Handles plastic_retail_price_change tasks
- **`processUpdateDiscVariantPriceOnShopifyTask.js`** - Handles update_disc_variant_price_on_shopify tasks
- **`processPlasticMsrpPriceChangeTask.js`** - Handles plastic_msrp_price_change tasks
- **`processUpdateDiscVariantMsrpOnShopifyTask.js`** - Handles update_disc_variant_msrp_on_shopify tasks
- **`processMpsOverridePricesChangedTask.js`** - Handles mps_override_prices_changed_so_update_shopify tasks

### Database Setup
- **`create_plastic_retail_price_change_trigger.sql`** - Creates database trigger for retail price changes
- **`create_plastic_msrp_price_change_trigger.sql`** - Creates database trigger for MSRP price changes
- **`create_mps_override_prices_change_trigger.sql`** - Creates database trigger for MPS override price changes

### Testing
- **`testPlasticPriceChangeTasks.js`** - Test script for retail price functionality
- **`testPlasticMsrpChangeTasks.js`** - Test script for MSRP price functionality
- **`testMpsOverridePriceChangeTasks.js`** - Test script for MPS override price functionality

### Integration
- **`taskQueueWorker.js`** - Updated to include new task processors

## Installation

1. **Install the database triggers**:
   ```sql
   -- Run the SQL files to create the triggers
   \i create_plastic_retail_price_change_trigger.sql
   \i create_plastic_msrp_price_change_trigger.sql
   \i create_mps_override_prices_change_trigger.sql
   ```

2. **Restart your task queue worker** to pick up the new processors.

3. **Test the implementation**:
   ```bash
   # Test retail price functionality
   node testPlasticPriceChangeTasks.js

   # Test MSRP functionality
   node testPlasticMsrpChangeTasks.js

   # Test MPS override functionality
   node testMpsOverridePriceChangeTasks.js
   ```

## Usage Examples

### Manual Task Enqueueing

```javascript
// Enqueue a plastic retail price change task
await supabase
  .from('t_task_queue')
  .insert({
    task_type: 'plastic_retail_price_change',
    payload: { id: 123 },
    status: 'pending',
    scheduled_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    enqueued_by: 'manual'
  });

// Enqueue a plastic MSRP price change task
await supabase
  .from('t_task_queue')
  .insert({
    task_type: 'plastic_msrp_price_change',
    payload: { id: 123 },
    status: 'pending',
    scheduled_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    enqueued_by: 'manual'
  });

// Enqueue an MPS override price change task
await supabase
  .from('t_task_queue')
  .insert({
    task_type: 'mps_override_prices_changed_so_update_shopify',
    payload: {
      id: 18810,
      retail_price_changed: true,
      msrp_price_changed: false
    },
    status: 'pending',
    scheduled_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    enqueued_by: 'manual'
  });

// Enqueue a single disc price update
await supabase
  .from('t_task_queue')
  .insert({
    task_type: 'update_disc_variant_price_on_shopify',
    payload: {
      id: 456789,
      item_type: 'disc',
      new_retail_price: 19.99
    },
    status: 'pending',
    scheduled_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    enqueued_by: 'manual'
  });

// Enqueue a single OSL price update
await supabase
  .from('t_task_queue')
  .insert({
    task_type: 'update_disc_variant_price_on_shopify',
    payload: {
      id: 16560,
      item_type: 'osl',
      new_retail_price: 19.99
    },
    status: 'pending',
    scheduled_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    enqueued_by: 'manual'
  });

// Enqueue a single disc MSRP update
await supabase
  .from('t_task_queue')
  .insert({
    task_type: 'update_disc_variant_msrp_on_shopify',
    payload: {
      id: 456789,
      item_type: 'disc',
      new_msrp_price: 22.99
    },
    status: 'pending',
    scheduled_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    enqueued_by: 'manual'
  });

// Enqueue a single OSL MSRP update
await supabase
  .from('t_task_queue')
  .insert({
    task_type: 'update_disc_variant_msrp_on_shopify',
    payload: {
      id: 16560,
      item_type: 'osl',
      new_msrp_price: 22.99
    },
    status: 'pending',
    scheduled_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    enqueued_by: 'manual'
  });
```

### Automatic Triggering

The system automatically enqueues tasks when you update plastic prices:

```sql
-- This will automatically trigger retail price change tasks
UPDATE t_plastics
SET val_retail_price = 19.99
WHERE id = 123;

-- This will automatically trigger MSRP price change tasks
UPDATE t_plastics
SET val_msrp = 22.99
WHERE id = 123;

-- This will automatically trigger MPS override price change tasks
UPDATE t_mps
SET val_override_retail_price = 21.99
WHERE id = 18810;

-- This will also trigger MPS override price change tasks
UPDATE t_mps
SET val_override_msrp = 24.99
WHERE id = 18810;
```

## Important Notes

### Price Override Logic
- **Plastic Price Changes**: Items are only updated if their MPS does NOT have override prices set
  - Retail: Only if `val_override_retail_price` is NULL
  - MSRP: Only if `val_override_msrp` is NULL
- **MPS Override Changes**: Items are updated regardless of plastic prices
  - When MPS override prices change, all items using that MPS are updated
  - This ensures override prices take precedence over plastic base prices

### SKU Formats
- **Disc SKUs** follow the format: `'D' + disc.id`
  - Example: Disc ID 456789 becomes SKU "D456789"
- **OSL SKUs** follow the format: `'OS' + osl.id`
  - Example: OSL ID 16560 becomes SKU "OS16560"

### Error Handling
- Tasks handle missing variants gracefully (variant not found on Shopify)
- Price comparisons use floating-point tolerance (0.01) to avoid unnecessary updates
- All errors are logged with detailed context

### Performance Considerations
- Large plastic price changes may generate many child tasks
- Tasks are processed sequentially by the worker to respect Shopify rate limits
- Consider monitoring task queue depth during bulk price updates

## Monitoring

Monitor task progress through the `t_task_queue` table:

```sql
-- Check recent plastic retail price change tasks
SELECT * FROM t_task_queue
WHERE task_type = 'plastic_retail_price_change'
ORDER BY created_at DESC
LIMIT 10;

-- Check recent plastic MSRP price change tasks
SELECT * FROM t_task_queue
WHERE task_type = 'plastic_msrp_price_change'
ORDER BY created_at DESC
LIMIT 10;

-- Check retail price update tasks
SELECT * FROM t_task_queue
WHERE task_type = 'update_disc_variant_price_on_shopify'
ORDER BY created_at DESC
LIMIT 10;

-- Check MSRP update tasks
SELECT * FROM t_task_queue
WHERE task_type = 'update_disc_variant_msrp_on_shopify'
ORDER BY created_at DESC
LIMIT 10;

-- Check MPS override price change tasks
SELECT * FROM t_task_queue
WHERE task_type = 'mps_override_prices_changed_so_update_shopify'
ORDER BY created_at DESC
LIMIT 10;
```

## Troubleshooting

### Common Issues

1. **Variant not found on Shopify**
   - Check if disc is actually uploaded to Shopify
   - Verify SKU format is correct
   - Ensure Shopify credentials are valid

2. **Price not updating**
   - Check if MPS has `val_override_retail_price` set
   - Verify disc meets all criteria (not sold, uploaded to Shopify)
   - Check Shopify API rate limits

3. **Tasks not processing**
   - Ensure task queue worker is running
   - Check for import errors in the new processors
   - Verify database connectivity

### Debug Mode

Enable detailed logging by setting environment variables:
```bash
DEBUG=true node taskQueueWorker.js
```
