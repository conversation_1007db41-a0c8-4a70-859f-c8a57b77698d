import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function comprehensiveSpecialSectionsTest() {
    try {
        console.log('🔍 Comprehensive Special Sections Test...\n');
        
        // 1. Check all special sections
        console.log('1. Checking all special sections...');
        
        const specialSections = [
            { name: 'Fundraiser', rows: [25, 28], description: 'Ben Askren Fundraiser items' },
            { name: 'New Release', rows: [132, 135], description: 'Classic reissue and Fuzed Scorch items' }
        ];

        for (const section of specialSections) {
            console.log(`\n--- ${section.name} Section (${section.description}) ---`);
            
            const { data: sectionData, error } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('excel_row_hint, excel_column, mold_name, plastic_name, stamp_name, calculated_mps_id, is_orderable, is_currently_available, cost_price')
                .in('excel_row_hint', section.rows)
                .order('excel_row_hint, excel_column');

            if (error) {
                console.error(`❌ Error querying ${section.name} section:`, error);
                continue;
            }

            console.log(`✅ Found ${sectionData.length} records in ${section.name} section:`);
            sectionData.forEach((record, index) => {
                console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.plastic_name} ${record.mold_name}`);
                console.log(`      Stamp: ${record.stamp_name}`);
                console.log(`      Available: ${record.is_currently_available}, Cost: $${record.cost_price || 'N/A'}`);
                console.log(`      Orderable: ${record.is_orderable}, MPS: ${record.calculated_mps_id || 'NO_MPS'}`);
                console.log('');
            });
        }

        // 2. Check what will be exported
        console.log('2. Checking export data for all special sections...');
        
        const allSpecialRows = specialSections.flatMap(s => s.rows);
        
        const { data: exportData, error: exportError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, calculated_mps_id, mold_name, plastic_name, id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .in('excel_row_hint', allSpecialRows)
            .order('excel_row_hint, excel_column');

        if (exportError) {
            console.error('❌ Error querying export data:', exportError);
        } else {
            console.log(`✅ Export will include ${exportData.length} records from special sections:`);
            exportData.forEach((record, index) => {
                console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.plastic_name} ${record.mold_name}`);
                console.log(`      Order cell will show: ID ${record.id}`);
                console.log(`      Column AC will show: MPS ${record.calculated_mps_id || 'NO_MPS'}`);
                console.log('');
            });
        }

        // 3. Verify structure expectations
        console.log('3. Verifying structure expectations...');
        
        const expectations = [
            // Fundraiser section
            { row: 22, shouldHaveRecords: false, description: 'Fundraiser header - should be empty' },
            { row: 24, shouldHaveRecords: false, description: 'Fundraiser order qty header - should be empty' },
            { row: 25, shouldHaveRecords: true, expectedColumn: 'B', description: 'Thrasher product - should have record in column B' },
            { row: 27, shouldHaveRecords: false, description: 'Fundraiser order qty header - should be empty' },
            { row: 28, shouldHaveRecords: true, expectedColumn: 'B', description: 'Buzzz product - should have record in column B' },
            
            // New Release section
            { row: 126, shouldHaveRecords: false, description: 'New Release header - should be empty' },
            { row: 131, shouldHaveRecords: false, description: 'New Release order qty header - should be empty' },
            { row: 132, shouldHaveRecords: true, expectedColumn: 'B', description: 'Classic reissue Drone - should have record in column B' },
            { row: 134, shouldHaveRecords: false, description: 'New Release order qty header - should be empty' },
            { row: 135, shouldHaveRecords: true, expectedColumn: 'B', description: 'Fuzed Scorch - should have record in column B' }
        ];

        let allCorrect = true;

        for (const expectation of expectations) {
            const { data: rowRecords, error: rowError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('excel_row_hint, excel_column, mold_name, plastic_name')
                .eq('excel_row_hint', expectation.row);

            if (rowError) {
                console.log(`   ❌ ERROR checking row ${expectation.row}: ${rowError.message}`);
                allCorrect = false;
                continue;
            }
            
            console.log(`\n   Row ${expectation.row}: ${expectation.description}`);
            
            if (expectation.shouldHaveRecords) {
                if (rowRecords.length === 0) {
                    console.log(`   ❌ FAIL: Expected records but found none`);
                    allCorrect = false;
                } else {
                    const correctColumnRecord = rowRecords.find(r => r.excel_column === expectation.expectedColumn);
                    if (!correctColumnRecord) {
                        console.log(`   ❌ FAIL: Expected record in column ${expectation.expectedColumn} but found in: ${rowRecords.map(r => r.excel_column).join(', ')}`);
                        allCorrect = false;
                    } else {
                        console.log(`   ✅ PASS: Found record in column ${expectation.expectedColumn} (${correctColumnRecord.plastic_name} ${correctColumnRecord.mold_name})`);
                    }
                }
            } else {
                if (rowRecords.length > 0) {
                    console.log(`   ❌ FAIL: Expected no records but found ${rowRecords.length}: ${rowRecords.map(r => `${r.excel_column}:${r.mold_name}`).join(', ')}`);
                    allCorrect = false;
                } else {
                    console.log(`   ✅ PASS: No records found (correct)`);
                }
            }
        }

        // 4. Final summary
        console.log('\n4. Final summary...');
        
        if (allCorrect) {
            console.log('🎉 ALL TESTS PASSED! Special sections are correctly configured.');
            console.log('\n📋 Your next automated order will show:');
            console.log('\n🎯 Fundraiser Section:');
            console.log('   • Row 22: EMPTY (header)');
            console.log('   • Row 24: EMPTY (header)');
            console.log('   • Row 25, Column B: Order sheet line ID | Column AC: 19704 (Thrasher MPS)');
            console.log('   • Row 27: EMPTY (header)');
            console.log('   • Row 28, Column B: Order sheet line ID | Column AC: NO_MPS (Buzzz needs MPS)');
            
            console.log('\n🆕 New Release Section:');
            console.log('   • Row 126: EMPTY (header)');
            console.log('   • Row 131: EMPTY (header)');
            console.log('   • Row 132, Column B: Order sheet line ID | Column AC: NO_MPS (Drone needs MPS)');
            console.log('   • Row 134: EMPTY (header)');
            console.log('   • Row 135, Column B: Order sheet line ID | Column AC: NO_MPS (Scorch needs MPS)');
            
            console.log('\n✅ Enhanced MPS export will show:');
            console.log('   • Order cells: Order sheet line IDs (for traceability)');
            console.log('   • Column AC: MPS IDs or NO_MPS (for verification)');
        } else {
            console.log('❌ SOME TESTS FAILED! There are still issues with the special sections.');
            console.log('   Check the failures above and fix them.');
        }
        
    } catch (error) {
        console.error('❌ Comprehensive test failed:', error.message);
    }
}

comprehensiveSpecialSectionsTest().catch(console.error);
