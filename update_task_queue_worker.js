import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM3ODY1NDEsImV4cCI6MjA0OTM2MjU0MX0.MtBXMZt7rCqXd6c9clhrNoVtfOxEilX2C8oboMceOGg';
const supabase = createClient(supabaseUrl, supabaseKey);

const testMatchDiscToOsl = async () => {
  try {
    console.log('Testing match_disc_to_osl task for disc 421349...');
    
    // Get disc record
    const { data: discRecord, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, color_id, sold_date, order_sheet_line_id')
      .eq('id', 421349)
      .single();
      
    if (discError) {
      console.error('Error fetching disc record:', discError);
      return;
    }
    
    console.log('Disc record:', discRecord);
    
    // Call the find_matching_osl function with debug info
    console.log('\nCalling find_matching_osl function...');
    const { data: oslData, error: oslError } = await supabase.rpc(
      'find_matching_osl',
      {
        mps_id_param: discRecord.mps_id,
        color_id_param: discRecord.color_id,
        weight_param: discRecord.weight
      }
    );
    
    if (oslError) {
      console.error('Error calling find_matching_osl:', oslError);
      return;
    }
    
    console.log('Function result:', oslData);
    
    // Extract debug info and osl_id
    let debugInfo = 'No debug info available';
    let oslId = null;
    
    if (oslData && oslData.length > 0) {
      debugInfo = oslData[0].debug_info || debugInfo;
      oslId = oslData[0].osl_id;
    }
    
    console.log('Debug info:', debugInfo);
    console.log('OSL ID:', oslId);
    
    // Create a task queue entry to simulate the task
    console.log('\nCreating a task queue entry...');
    const { data: taskData, error: taskError } = await supabase
      .from('t_task_queue')
      .insert([
        {
          task_type: 'match_disc_to_osl',
          payload: { 
            id: discRecord.id, 
            operation: 'UPDATE',
            old_data: discRecord
          },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString()
        }
      ])
      .select();
      
    if (taskError) {
      console.error('Error creating task:', taskError);
      return;
    }
    
    if (!taskData || taskData.length === 0) {
      console.log('No task created');
      return;
    }
    
    const taskId = taskData[0].id;
    console.log(`Created task with ID: ${taskId}`);
    
    // Simulate processing the task
    console.log('\nSimulating task processing...');
    
    // Update task to 'processing' status
    await supabase
      .from('t_task_queue')
      .update({ status: 'processing' })
      .eq('id', taskId);
    
    // Process the task (similar to what taskQueueWorker.js would do)
    let newOslId = null;
    let resultMessage = '';
    
    if (oslData && oslData.length > 0 && oslData[0].osl_id) {
      newOslId = oslData[0].osl_id;
      resultMessage = `Success! Disc matched to OSL ${newOslId}. Debug: ${debugInfo}`;
      
      // Update the disc with the new order_sheet_line_id
      const { error: updateDiscError } = await supabase
        .from('t_discs')
        .update({ order_sheet_line_id: newOslId })
        .eq('id', discRecord.id);
        
      if (updateDiscError) {
        console.error('Error updating disc:', updateDiscError);
        resultMessage = `Failed to update disc with new OSL. Error: ${updateDiscError.message}. Debug: ${debugInfo}`;
      }
    } else {
      resultMessage = `No matching order sheet line found for disc. Debug: ${debugInfo}`;
    }
    
    // Update task status to completed with result
    await supabase
      .from('t_task_queue')
      .update({ 
        status: 'completed', 
        result: { 
          message: resultMessage,
          operation: 'UPDATE',
          debug_info: debugInfo,
          osl_id: newOslId
        },
        completed_at: new Date().toISOString()
      })
      .eq('id', taskId);
    
    console.log('Task processing completed');
    console.log('Result message:', resultMessage);
    
    // Verify the disc update
    const { data: verifyData, error: verifyError } = await supabase
      .from('t_discs')
      .select('id, order_sheet_line_id')
      .eq('id', discRecord.id)
      .single();
      
    if (verifyError) {
      console.error('Error verifying disc update:', verifyError);
      return;
    }
    
    console.log('Disc after update:', verifyData);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
};

testMatchDiscToOsl();
