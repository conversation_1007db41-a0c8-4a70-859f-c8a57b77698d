// enqueueB2FTitleTasks.js
// Enqueue generate_disc_title_pull_and_handle for discs whose location starts with "B2F"
// This will refresh g_pull to include the full location string (e.g., "B2F 08-02").

import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

const argv = yargs(hideBin(process.argv))
  .option('batch', {
    describe: 'Batch size for fetching/enqueuing',
    type: 'number',
    default: 1000,
  })
  .option('startId', {
    describe: 'Start from discs with id greater than this value',
    type: 'number',
    default: 0,
  })
  .option('max', {
    describe: 'Maximum number of discs to enqueue (0 = no limit)',
    type: 'number',
    default: 0,
  })
  .option('unsoldOnly', {
    describe: 'Only enqueue discs where sold_date IS NULL',
    type: 'boolean',
    default: true,
  })
  .option('dryRun', {
    describe: 'Do not write, only log what would be enqueued',
    type: 'boolean',
    default: false,
  })
  .help()
  .alias('help', 'h')
  .argv;

async function main() {
  try {
    const BATCH_SIZE = Math.max(1, argv.batch);
    const START_ID = argv.startId;
    const MAX_RECORDS = Math.max(0, argv.max);
    const DRY_RUN = argv.dryRun;

    console.log(`[enqueueB2FTitleTasks] Starting. batch=${BATCH_SIZE} startId=${START_ID} max=${MAX_RECORDS} dryRun=${DRY_RUN}`);

    let startId = START_ID;
    let totalEnqueued = 0;
    let hasMore = true;

    while (hasMore) {
      if (MAX_RECORDS > 0 && totalEnqueued >= MAX_RECORDS) {
        console.log(`[enqueueB2FTitleTasks] Reached max=${MAX_RECORDS}. Stopping.`);
        break;
      }

      const limit = MAX_RECORDS > 0 ? Math.min(BATCH_SIZE, MAX_RECORDS - totalEnqueued) : BATCH_SIZE;

      console.log(`[enqueueB2FTitleTasks] Fetching up to ${limit} discs with id > ${startId} and location ILIKE 'B2F%'${argv.unsoldOnly ? ' and sold_date IS NULL' : ''}`);
      let query = supabase
        .from('t_discs')
        .select('id')
        .ilike('location', 'B2F%')
        .gt('id', startId);
      if (argv.unsoldOnly) {
        query = query.is('sold_date', null);
      }
      const { data: discs, error } = await query
        .order('id')
        .limit(limit);

      if (error) {
        console.error('[enqueueB2FTitleTasks] Error fetching discs:', error.message);
        break;
      }

      if (!discs || discs.length === 0) {
        console.log('[enqueueB2FTitleTasks] No more discs found. Done.');
        break;
      }

      // Prepare tasks
      const nowIso = new Date().toISOString();
      const tasks = discs.map((d) => ({
        task_type: 'generate_disc_title_pull_and_handle',
        payload: { id: d.id },
        status: 'pending',
        scheduled_at: nowIso,
        created_at: nowIso,
      }));

      if (DRY_RUN) {
        tasks.slice(0, 5).forEach((t, idx) => console.log(`[enqueueB2FTitleTasks] Would enqueue: ${idx + 1}.`, t));
        if (tasks.length > 5) console.log(`[enqueueB2FTitleTasks] ...and ${tasks.length - 5} more in this batch`);
      } else {
        const { error: insertError } = await supabase.from('t_task_queue').insert(tasks);
        if (insertError) {
          console.error('[enqueueB2FTitleTasks] Error inserting tasks:', insertError.message);
          break;
        }
      }

      totalEnqueued += tasks.length;
      startId = discs[discs.length - 1].id;

      console.log(`[enqueueB2FTitleTasks] Enqueued ${tasks.length} tasks this batch. Total so far: ${totalEnqueued}`);

      if (discs.length < limit) {
        // No more pages
        hasMore = false;
      }
    }

    console.log(`[enqueueB2FTitleTasks] Completed. Total enqueued: ${totalEnqueued}${DRY_RUN ? ' (dry run)' : ''}.`);
  } catch (err) {
    console.error('[enqueueB2FTitleTasks] Fatal error:', err);
    process.exitCode = 1;
  }
}

main();

