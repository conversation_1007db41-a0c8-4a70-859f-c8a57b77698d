require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function recheckAllNullVendorOsl() {
  try {
    console.log('Rechecking ALL discs with null vendor_osl_id to find missed matches...');
    
    // Get current count of discs with null vendor_osl_id
    const { count: totalNullCount, error: countError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (countError) {
      console.error('Error getting count:', countError);
      return;
    }
    
    console.log(`Found ${totalNullCount} discs with null vendor_osl_id to recheck`);
    
    if (totalNullCount === 0) {
      console.log('No discs to recheck!');
      return;
    }
    
    // Process in batches
    let totalProcessed = 0;
    let totalUpdated = 0;
    let totalErrors = 0;
    let totalNoMatch = 0;
    const batchSize = 100;
    
    console.log(`\nProcessing ${totalNullCount} discs in batches of ${batchSize}...`);
    console.log('This will check EVERY disc to ensure we catch all missed matches.\n');
    
    while (totalProcessed < totalNullCount) {
      const batchNumber = Math.floor(totalProcessed / batchSize) + 1;
      console.log(`Processing batch ${batchNumber}...`);
      
      // Get next batch of discs with null vendor_osl_id
      const { data: discs, error: batchError } = await supabase
        .from('t_discs')
        .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
        .is('vendor_osl_id', null)
        .not('weight_mfg', 'is', null)
        .not('mps_id', 'is', null)
        .not('color_id', 'is', null)
        .range(0, batchSize - 1);  // Always get first batch since we're updating them
      
      if (batchError) {
        console.error('Error getting batch:', batchError);
        break;
      }
      
      if (!discs || discs.length === 0) {
        console.log('No more discs to process');
        break;
      }
      
      console.log(`  Processing ${discs.length} discs in this batch...`);
      
      let batchUpdated = 0;
      let batchErrors = 0;
      let batchNoMatch = 0;
      
      for (const disc of discs) {
        try {
          // Test the vendor OSL function for this disc
          const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
            'find_matching_osl_by_mfg_weight',
            {
              mps_id_param: disc.mps_id,
              color_id_param: disc.color_id,
              weight_mfg_param: disc.weight_mfg
            }
          );
          
          if (vendorOslError) {
            console.error(`  Error finding vendor OSL for disc ${disc.id}:`, vendorOslError);
            batchErrors++;
            totalErrors++;
            continue;
          }
          
          const vendorOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
          
          if (vendorOslId) {
            // This disc SHOULD have a vendor OSL mapping!
            console.log(`  🎯 FOUND MISSED MATCH: Disc ${disc.id} should map to OSL ${vendorOslId}`);
            
            // Update the disc with vendor_osl_id
            const { error: updateError } = await supabase
              .from('t_discs')
              .update({ vendor_osl_id: vendorOslId })
              .eq('id', disc.id);
            
            if (updateError) {
              console.error(`  Error updating disc ${disc.id}:`, updateError);
              batchErrors++;
              totalErrors++;
            } else {
              batchUpdated++;
              totalUpdated++;
              
              // Show some details about the match
              if (vendorOslId !== disc.order_sheet_line_id) {
                console.log(`    Different mappings: regular OSL ${disc.order_sheet_line_id}, vendor OSL ${vendorOslId}`);
              } else {
                console.log(`    Same OSL for both mappings: ${vendorOslId}`);
              }
            }
          } else {
            // No match found - this is expected for some discs
            batchNoMatch++;
            totalNoMatch++;
          }
          
          totalProcessed++;
          
        } catch (err) {
          console.error(`  Error processing disc ${disc.id}:`, err.message);
          batchErrors++;
          totalErrors++;
          totalProcessed++;
        }
      }
      
      console.log(`  Batch ${batchNumber} completed: ${batchUpdated} updated, ${batchNoMatch} no match, ${batchErrors} errors`);
      console.log(`  Running totals: ${totalUpdated} updated, ${totalNoMatch} no match, ${totalErrors} errors, ${totalProcessed} processed\n`);
      
      // Progress update every 10 batches
      if (batchNumber % 10 === 0) {
        const percentComplete = ((totalProcessed / totalNullCount) * 100).toFixed(1);
        console.log(`🔄 Progress: ${percentComplete}% complete (${totalProcessed}/${totalNullCount})`);
        console.log(`   Found ${totalUpdated} missed matches so far!\n`);
      }
      
      // Small delay between batches to avoid overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    console.log('\n=== RECHECK COMPLETE ===');
    console.log(`Total discs rechecked: ${totalProcessed}`);
    console.log(`Missed matches found and updated: ${totalUpdated}`);
    console.log(`Discs with no vendor OSL match: ${totalNoMatch}`);
    console.log(`Errors encountered: ${totalErrors}`);
    
    if (totalUpdated > 0) {
      console.log(`\n🎉 SUCCESS! Found and updated ${totalUpdated} discs that were missed in the previous processing!`);
      
      // Get final statistics
      const { count: finalVendorOslCount, error: finalError } = await supabase
        .from('t_discs')
        .select('*', { count: 'exact', head: true })
        .not('vendor_osl_id', 'is', null);
      
      const { count: finalNullCount, error: finalNullError } = await supabase
        .from('t_discs')
        .select('*', { count: 'exact', head: true })
        .is('vendor_osl_id', null)
        .not('weight_mfg', 'is', null)
        .not('mps_id', 'is', null)
        .not('color_id', 'is', null);
      
      if (!finalError && !finalNullError) {
        console.log(`\n📊 FINAL STATISTICS:`);
        console.log(`Total discs with vendor_osl_id: ${finalVendorOslCount}`);
        console.log(`Remaining discs with null vendor_osl_id: ${finalNullCount}`);
        console.log(`Improvement: +${totalUpdated} new vendor mappings created`);
      }
      
      console.log('\n✅ The dual mapping system is now more complete!');
    } else {
      console.log('\n✅ No missed matches found - the previous processing was complete.');
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

recheckAllNullVendorOsl();
