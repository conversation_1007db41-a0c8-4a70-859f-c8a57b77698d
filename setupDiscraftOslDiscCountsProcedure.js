// setupDiscraftOslDiscCountsProcedure.js - Create the stored procedure for disc counts

import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase environment variables');
    console.error('Please ensure SUPABASE_URL and SUPABASE_KEY are set in your .env file');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupStoredProcedure() {
    console.log('🔧 Setting up Discraft OSL Disc Counts Stored Procedure...');
    console.log('=======================================================');

    try {
        // Read the SQL file
        const sqlContent = fs.readFileSync('create_discraft_osl_disc_counts_procedure.sql', 'utf8');
        
        console.log('📄 Executing SQL to create stored procedure...');
        
        // Execute the SQL
        const { data, error } = await supabase.rpc('exec_sql', { sql: sqlContent });
        
        if (error) {
            // If exec_sql doesn't exist, try direct execution
            console.log('⚠️ exec_sql function not available, trying direct execution...');
            
            // Split the SQL into individual statements and execute them
            const statements = sqlContent
                .split(';')
                .map(stmt => stmt.trim())
                .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
            
            for (const statement of statements) {
                if (statement.toLowerCase().includes('create or replace function')) {
                    console.log('📝 Creating stored procedure...');
                    const { error: createError } = await supabase.rpc('exec', { sql: statement });
                    if (createError) {
                        throw new Error(`Failed to create stored procedure: ${createError.message}`);
                    }
                }
            }
        }
        
        console.log('✅ Stored procedure created successfully');
        
        // Test the stored procedure with a small sample
        console.log('\n🧪 Testing stored procedure...');
        
        // Get a few OSL IDs to test with
        const { data: sampleIds, error: sampleError } = await supabase
            .from('it_discraft_osl_map')
            .select('id')
            .limit(3);
        
        if (sampleError) {
            throw new Error(`Failed to get sample IDs: ${sampleError.message}`);
        }
        
        if (sampleIds && sampleIds.length > 0) {
            const testIds = sampleIds.map(row => row.id);
            console.log(`📋 Testing with OSL IDs: ${testIds.join(', ')}`);
            
            const { data: testResults, error: testError } = await supabase
                .rpc('calculate_discraft_osl_disc_counts', { osl_ids: testIds });
            
            if (testError) {
                throw new Error(`Failed to test stored procedure: ${testError.message}`);
            }
            
            console.log('✅ Stored procedure test successful!');
            console.log('📊 Test results:');
            console.table(testResults);
        } else {
            console.log('⚠️ No OSL records found to test with');
        }
        
        console.log('\n🎉 Setup completed successfully!');
        console.log('The stored procedure is ready to use for efficient disc count calculations.');
        
    } catch (error) {
        console.error('❌ Setup failed:', error.message);
        console.error('\n💡 Manual setup option:');
        console.error('1. Copy the contents of create_discraft_osl_disc_counts_procedure.sql');
        console.error('2. Execute it directly in your database client');
        console.error('3. The import task will fall back to individual queries if the procedure is not available');
        throw error;
    }
}

// Run the setup
setupStoredProcedure()
    .then(() => {
        console.log('\n✅ Setup script completed successfully');
        process.exit(0);
    })
    .catch(error => {
        console.error('\n💥 Setup script failed:', error.message);
        process.exit(1);
    });
