// apply_round_weight.js
// Applies updated functions to compare using round(weight) and verifies disc 426710 vs SDASIN 10905

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs/promises';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function readFunctionSqlOnly(path) {
  const content = await fs.readFile(path, 'utf8');
  const splitOnConfirm = content.split(/\n-- Confirmation message[\s\S]*/i)[0];
  const splitOnDO = splitOnConfirm.split(/\nDO \$\$[\s\S]*?\$\$;?/i)[0];
  return splitOnDO.trim();
}

async function applyFunction(sql, name, supabase) {
  console.log(`\nApplying function: ${name} ...`);
  const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
  if (error) {
    console.error(`❌ Failed applying ${name}:`, error);
    throw new Error(`exec_sql failed for ${name}: ${error.message}`);
  }
  console.log(`✅ Applied ${name}`);
}

async function verify(supabase) {
  const discId = 426710;
  const sdasinId = 10905;

  console.log(`\nRunning match_disc_to_all_sdasins(${discId}) ...`);
  await supabase.rpc('match_disc_to_all_sdasins', { disc_id_param: discId });

  console.log(`\nChecking tjoin_discs_sdasins for disc ${discId} ↔ SDASIN ${sdasinId} ...`);
  const { data: joinRows, error: joinErr } = await supabase
    .from('tjoin_discs_sdasins')
    .select('disc_id, sdasin_id, reason, created_at')
    .eq('disc_id', discId)
    .eq('sdasin_id', sdasinId)
    .order('created_at', { ascending: false })
    .limit(5);

  if (joinErr) {
    console.error('❌ Error querying tjoin_discs_sdasins:', joinErr);
  } else if (!joinRows || joinRows.length === 0) {
    console.log('❌ No match rows found.');
  } else {
    console.log('✅ Found join rows:');
    console.log(joinRows);
  }
}

async function main() {
  try {
    console.log('Applying round-to-nearest-gram change...');
    const f1 = await readFunctionSqlOnly('create_match_disc_to_all_sdasins_function.sql');
    const f2 = await readFunctionSqlOnly('create_match_sdasin_to_all_discs_function.sql');
    const f3 = await readFunctionSqlOnly('create_disc_sdasin_match_function.sql');

    await applyFunction(f1, 'match_disc_to_all_sdasins', supabase);
    await applyFunction(f2, 'match_sdasin_to_all_discs', supabase);
    await applyFunction(f3, 'check_disc_sdasin_match', supabase);

    await verify(supabase);
  } catch (e) {
    console.error('apply_round_weight failed:', e.message);
    process.exit(1);
  }
}

main();

