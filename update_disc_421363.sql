-- Create a function to find the correct OSL for disc 421363 and update it
CREATE OR REPLACE FUNCTION update_disc_421363()
RETURNS TEXT AS $$
DECLARE
    disc_id INTEGER := 421363;
    disc_mps_id INTEGER := 17696;
    disc_color_id INTEGER := 4;
    disc_weight NUMERIC := 169.7;

    rounded_weight NUMERIC;
    decimal_part NUMERIC;

    matching_osl_id INTEGER;
    result_text TEXT;
BEGIN
    -- Custom rounding logic
    decimal_part := disc_weight - FLOOR(disc_weight);

    IF decimal_part >= 0.5 THEN
        rounded_weight := CEIL(disc_weight);
    ELSE
        rounded_weight := FLOOR(disc_weight);
    END IF;

    -- Find a matching OSL
    SELECT id INTO matching_osl_id
    FROM t_order_sheet_lines
    WHERE mps_id = disc_mps_id
      AND (color_id = disc_color_id OR color_id = 23)
      AND rounded_weight >= min_weight::NUMERIC
      AND rounded_weight <= max_weight::NUMERIC
    LIMIT 1;

    -- Update the disc if a matching OSL is found
    IF matching_osl_id IS NOT NULL THEN
        UPDATE t_discs
        SET order_sheet_line_id = matching_osl_id
        WHERE id = disc_id;

        result_text := 'Disc 421363 updated with OSL ' || matching_osl_id || '. Rounded weight: ' || rounded_weight;
    ELSE
        result_text := 'No matching OSL found for disc 421363. Rounded weight: ' || rounded_weight;
    END IF;

    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- Call the function
SELECT update_disc_421363();
