import { processFixWeightRangeTask } from './processFixWeightRangeTask.js';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

// Mock functions for testing
async function updateTaskStatus(taskId, status, result = {}) {
  console.log(`📝 Task ${taskId} status: ${status}`, result);
}

async function logError(message, context) {
  console.error(`❌ Error in ${context}: ${message}`);
}

// Test task
const testTask = {
  id: 'test-123',
  task_type: 'fix_weight_range',
  payload: {
    disc_id: 424221,
    weight: 157.88,
    expected_weight_range_tag: 'wt_rng_150-159'
  }
};

console.log('🧪 Testing processFixWeightRangeTask directly...');
console.log('Test task:', JSON.stringify(testTask, null, 2));

try {
  await processFixWeightRangeTask(testTask, { supabase, updateTaskStatus, logError });
  console.log('✅ Test completed');
} catch (error) {
  console.error('❌ Test failed:', error.message);
}
