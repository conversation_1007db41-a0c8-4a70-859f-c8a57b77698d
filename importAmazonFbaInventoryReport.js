// Import Amazon FBA Inventory Report
// This imports the comprehensive FBA inventory report to import_table_amaz_fba_inv_rpt table

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export async function importAmazonFbaInventoryReport(supabase) {
  console.log('[importAmazonFbaInventoryReport] Starting Amazon FBA Inventory Report import');

  try {
    // Define the file path
    const filePath = path.join(__dirname, 'data', 'external data', 'Amazon FBA Inventory Report.txt');
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    console.log(`[importAmazonFbaInventoryReport] Reading file: ${filePath}`);

    // Read and parse the file
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const lines = fileContent.split('\n').filter(line => line.trim() !== '');

    if (lines.length === 0) {
      throw new Error('File is empty or contains no valid data');
    }

    // Parse header line (tab-separated)
    const headers = lines[0].split('\t').map(h => h.trim());
    console.log(`[importAmazonFbaInventoryReport] Found ${headers.length} columns`);

    // Parse data rows
    const dataRows = [];
    let skippedRows = 0;

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      const values = line.split('\t');
      
      // Ensure we have enough values (pad with empty strings if needed)
      while (values.length < headers.length) {
        values.push('');
      }

      try {
        // Map the data to our database columns using the exact header names
        const row = {};
        
        // Map each header to its corresponding value
        headers.forEach((header, index) => {
          let value = values[index] || null;
          
          // Handle numeric fields
          if (header === 'available' || 
              header === 'pending-removal-quantity' ||
              header.includes('inv-age-') ||
              header.includes('units-shipped-') ||
              header.includes('sales-shipped-') ||
              header.includes('inbound-') ||
              header.includes('quantity-to-be-charged-') ||
              header === 'sales-rank' ||
              header === 'days-of-supply' ||
              header === 'estimated-excess-quantity' ||
              header.includes('weeks-of-cover-') ||
              header === 'Total Reserved Quantity' ||
              header === 'unfulfillable-quantity') {
            value = value && value !== '' ? parseInt(value) || 0 : 0;
          }
          
          // Handle decimal fields
          if (header.includes('price') ||
              header.includes('cost') ||
              header.includes('estimated-') ||
              header === 'sell-through' ||
              header === 'item-volume' ||
              header === 'storage-volume' ||
              header.includes('historical-days-of-supply') ||
              header.includes('term-historical-days-of-supply')) {
            value = value && value !== '' ? parseFloat(value) || 0 : 0;
          }
          
          row[header] = value;
        });

        // Basic validation - require sku and fnsku
        if (!row.sku || !row.fnsku) {
          skippedRows++;
          console.warn(`[importAmazonFbaInventoryReport] Skipping row ${i + 1}: missing sku or fnsku`);
          continue;
        }

        dataRows.push(row);
      } catch (error) {
        skippedRows++;
        console.warn(`[importAmazonFbaInventoryReport] Error parsing row ${i + 1}: ${error.message}`);
      }
    }

    console.log(`[importAmazonFbaInventoryReport] Parsed ${dataRows.length} valid rows, skipped ${skippedRows} rows`);

    if (dataRows.length === 0) {
      throw new Error('No valid data rows found to import');
    }

    // Truncate existing data (this is a snapshot import)
    console.log('[importAmazonFbaInventoryReport] Truncating existing data...');
    const { error: truncateError } = await supabase
      .from('import_table_amaz_fba_inv_rpt')
      .delete()
      .neq('fnsku', ''); // Delete all records

    if (truncateError) {
      throw new Error(`Failed to truncate existing data: ${truncateError.message}`);
    }

    // Import data in chunks of 500 records (smaller chunks due to many columns)
    const chunkSize = 500;
    let totalImported = 0;

    for (let i = 0; i < dataRows.length; i += chunkSize) {
      const chunk = dataRows.slice(i, i + chunkSize);
      
      console.log(`[importAmazonFbaInventoryReport] Importing chunk ${Math.floor(i / chunkSize) + 1} (${chunk.length} records)`);

      const { data, error } = await supabase
        .from('import_table_amaz_fba_inv_rpt')
        .insert(chunk);

      if (error) {
        throw new Error(`Failed to import chunk starting at row ${i + 1}: ${error.message}`);
      }

      totalImported += chunk.length;
    }

    const message = `Successfully imported ${totalImported} records into import_table_amaz_fba_inv_rpt (truncate and replace)`;
    const details = `File: Amazon FBA Inventory Report.txt\nTotal records: ${totalImported}\nSkipped rows: ${skippedRows}\nImport type: Truncate and replace`;

    console.log(`[importAmazonFbaInventoryReport] ${message}`);

    return {
      success: true,
      message: message,
      details: details,
      importCount: totalImported,
      skippedCount: skippedRows
    };

  } catch (error) {
    console.error(`[importAmazonFbaInventoryReport] Error: ${error.message}`);
    throw error;
  }
}
