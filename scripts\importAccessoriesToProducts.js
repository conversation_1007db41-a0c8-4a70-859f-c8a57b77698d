import fs from 'fs';
import path from 'path';
import Papa from 'papaparse';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

/*
  Import tAccessories (legacy Access export) into new schema:
  - t_products (parent)
  - t_product_variants (child)
  - t_variant_attributes (attributes for variants)

  Source file: data/external data/access/tAccessories.txt (tab-delimited)

  Mapping rules (summarized from user request):
  t_product_variants
    id = AccessoryID (explicit id)
    vendor_status = Vendor Status
    vendor_status_date = vendor_status_updated_at (fall back: parse from Vendor Status if date-like)
    vendor_sku = VendorSKU
    UPC = UPC
    order_cost = OrderCost
    color_id = Color Family (int); if non-numeric try special cases; else null and log
    if Color not null => op1_name = 'Color', op1_value = Color
    if Size not null  => op2_name = 'Size',  op2_value = Size
    uploaded_to_shopify_at = UploadedToShopify (parse date, or if 'Y' => now())
    notes = Note
    price = RetailPrice
    created_by = 'import'
    created_at = Date Entered if parseable else now()
    updated_at = now()
    map_price = MAPPrice
    msrp = MSRPPrice
    stock_quantity = CurrentQtyOnHand
    sku = SKU
    player_id = Signature (int if parseable)
    case_qty = CaseQty
    case_weight_lbs = CaseWeightLbs
    case_dimensions = CaseDimensions
    shopify_weight_lbs = ShopifyWeightLbs
    release_date_and_time = ReleaseDate
    notes_images = ImageNotes

  t_variant_attributes (only when source has data):
    For rows with Gender => insert { variant_id: AccessoryID, category_attribute_id: (lookup by product category and name 'Gender'), attribute_value: Gender }
    For rows with 'Age Group' => likewise with name 'Age Group'

  t_products (unique by brand_id + name):
    brand_id = Brand (assumed to match t_brands.id)
    category_id = lookup t_categories.id by CategoryID == t_categories.id_legacy
    shopify_handle = ShopifyHandle
    video_url = VideoURL
    video_status = VideoStatus
    description = Description
    mfg_info_url = LinkToMFGInfo
    shopify_product_template = ShopifyProductTemplate
    name = Title
    created_by = 'import'
    created_at = min(Date Entered) across grouped variants if available

  Product grouping:
    All variants with the same (category_id, brand_id, Title) share a single parent product.

  Truncation:
    If run with --truncate, TRUNCATE the three tables before import (RESTART IDENTITY CASCADE).

  Safety:
    - Logs all errors to console and to t_error_logs with context.
    - Supports --dry-run to validate without writing.
*/

dotenv.config();

const FILE_PATH = path.join(process.cwd(), 'data', 'external data', 'access', 'tAccessories.txt');
const CHUNK_SIZE = 500;

function toIntOrNull(v) {
  if (v === undefined || v === null || v === '') return null;
  // Remove quotes and trim
  const s = String(v).replace(/\$/g, '').trim();
  const n = Number(s);
  return Number.isFinite(n) ? Math.trunc(n) : null;
}

function toNumOrNull(v) {
  if (v === undefined || v === null || v === '') return null;
  const s = String(v).replace(/[,$]/g, '').trim();
  const n = Number(s);
  return Number.isFinite(n) ? n : null;
}

function parseDateOrNull(v) {
  if (!v) return null;
  if (typeof v === 'string') {
    const s = v.trim();
    if (!s) return null;
    if (/^Y(es)?$/i.test(s)) return new Date();
    const d = new Date(s);
    return isNaN(d.getTime()) ? null : d;
  }
  if (v instanceof Date) return isNaN(v.getTime()) ? null : v;
  return null;
}

function cleanTsvContent(raw) {
  const lines = raw.split(/\r?\n/);
  if (lines.length === 0) return raw;
  const header = lines[0];
  const out = [header];
  let acc = '';
  let merges = 0;

  const isBalanced = (s) => {
    let inQuote = false;
    for (let i = 0; i < s.length; i++) {
      if (s[i] === '"') {
        if (s[i + 1] === '"') { i++; continue; }
        inQuote = !inQuote;
      }
    }
    return !inQuote;
  };

  for (let i = 1; i < lines.length; i++) {
    if (acc === '') {
      acc = lines[i];
    } else {
      acc += '\n' + lines[i];
      merges++;
    }
    // If the accumulated text has balanced quotes, we can finalize this record
    if (isBalanced(acc)) {
      // Finalize the accumulated record without altering internal content
      out.push(acc);
      acc = '';
    }
  }
  if (acc) out.push(acc);
  if (merges > 0) console.log(`[importAccessories] Pre-clean merged multiline records: ~${merges}`);
  return out.join('\n');
}

function parseTsv(filePath) {
  const raw = fs.readFileSync(filePath, 'utf-8');
  const cleaned = cleanTsvContent(raw);
  const { data, errors } = Papa.parse(cleaned, {
    header: true,
    skipEmptyLines: true,
    delimiter: '\t',
    dynamicTyping: false,
  });
  if (errors && errors.length) {
    console.warn('[importAccessories] TSV parse warnings (showing first 5):', errors.slice(0, 5));
  }
  return data;
}

function chunk(arr, size) {
  const out = [];
  for (let i = 0; i < arr.length; i += size) out.push(arr.slice(i, i + size));
  return out;
}

async function logError(supabase, errorMessage, contextObj) {
  try {
    await supabase.from('t_error_logs').insert({ error_message: errorMessage, context: contextObj });
  } catch (e) {
    console.error('Failed to log to t_error_logs:', e?.message || e);
  }
}

async function main() {
  const dryRun = process.argv.includes('--dry-run');
  const truncateFirst = process.argv.includes('--truncate');

  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_KEY;
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing SUPABASE_URL or SUPABASE_KEY');
    process.exit(1);
  }
  const supabase = createClient(supabaseUrl, supabaseKey);

  if (!fs.existsSync(FILE_PATH)) {
    console.error(`Input file not found: ${FILE_PATH}`);
    process.exit(1);
  }

  console.log(`[importAccessories] Reading TSV: ${FILE_PATH}`);
  const rows = parseTsv(FILE_PATH);
  console.log(`[importAccessories] Parsed ${rows.length} rows.`);
  if (rows.length === 0) return;

  // Optional truncation
  if (!dryRun && truncateFirst) {
    console.log('[importAccessories] Truncating target tables...');
    const truncateSql = `TRUNCATE TABLE public.t_variant_attributes, public.t_product_variants, public.t_products RESTART IDENTITY CASCADE;`;
    try {
      let { error } = await supabase.rpc('exec_sql', { sql_statement: truncateSql });
      if (error) {
        // Try alternative arg name
        ({ error } = await supabase.rpc('exec_sql', { sql_query: truncateSql }));
      }
      if (error) throw error;
      console.log('[importAccessories] Truncated tables via exec_sql.');
    } catch (e) {
      console.warn('[importAccessories] exec_sql truncate failed, falling back to DELETEs:', e?.message || e);
      const deletes = [ 't_variant_attributes', 't_product_variants', 't_products' ];
      for (const t of deletes) {
        const { error } = await supabase.from(t).delete().neq('id', null);
        if (error) {
          console.error(`[importAccessories] Fallback delete failed for ${t}:`, error.message);
          await logError(supabase, 'fallback_delete_failed', { table: t, error: error.message });
          process.exit(1);
        }
      }
      console.log('[importAccessories] Fallback deletes completed.');
    }
  }

  // Load lookups
  console.log('[importAccessories] Loading lookup tables...');
  const [catRes, brandRes, playerRes, attrRes] = await Promise.all([
    supabase.from('t_categories').select('id, id_legacy, shopify_product_template'),
    supabase.from('t_brands').select('id'),
    supabase.from('t_players').select('id'),
    supabase.from('t_category_attributes').select('id, category_id, name')
  ]);
  if (catRes.error) { console.error('Failed to load categories:', catRes.error.message); process.exit(1); }
  if (brandRes.error) { console.error('Failed to load brands:', brandRes.error.message); process.exit(1); }
  if (playerRes.error) { console.error('Failed to load players:', playerRes.error.message); process.exit(1); }
  if (attrRes.error) { console.error('Failed to load category attributes:', attrRes.error.message); process.exit(1); }

  const categoryByLegacy = new Map((catRes.data || []).map(r => [r.id_legacy, { id: r.id, shopify_product_template: r.shopify_product_template }]));
  const brandIdSet = new Set((brandRes.data || []).map(r => r.id));
  const playerIdSet = new Set((playerRes.data || []).map(r => r.id));
  const catAttrByKey = new Map((attrRes.data || []).map(r => [`${r.category_id}|${(r.name || '').toLowerCase()}`, r.id]));

  // Transform source rows into normalized records
  console.log('[importAccessories] Transforming rows...');
  const productKeyMap = new Map(); // key -> product payload aggregator
  const variantRecords = [];
  const errors = [];

  for (const r of rows) {
    const ctx = { AccessoryID: r['AccessoryID'] };

    const accessoryId = toIntOrNull(r['AccessoryID']);
    if (!accessoryId) {
      errors.push({ msg: 'missing_accessory_id', ctx });
      continue;
    }

    const categoryLegacy = toIntOrNull(r['CategoryID']);
    const catInfo = categoryLegacy != null ? categoryByLegacy.get(categoryLegacy) : null;
    if (!catInfo) {
      errors.push({ msg: 'category_legacy_not_found', ctx: { ...ctx, CategoryID: r['CategoryID'] } });
      continue; // Cannot proceed without category
    }

    // Brand: assume numeric id referencing t_brands.id
    const brandId = toIntOrNull(r['Brand']);
    if (brandId == null || !brandIdSet.has(brandId)) {
      errors.push({ msg: 'brand_id_not_found', ctx: { ...ctx, Brand: r['Brand'] } });
      // still proceed with brandId (may be null) to allow product grouping attempt
    }

    const title = (r['Title'] || '').toString().trim();
    if (!title) {
      errors.push({ msg: 'missing_title', ctx });
      continue;
    }

    const productKey = `${catInfo.id}|${brandId ?? ''}|${title.toLowerCase()}`;
    const dateEntered = parseDateOrNull(r['Date Entered']);

    // Aggregate per-product payload
    if (!productKeyMap.has(productKey)) {
      productKeyMap.set(productKey, {
        category_id: catInfo.id,
        brand_id: brandId || null,
        name: title,
        description: (r['Description'] || null),
        shopify_handle: (r['ShopifyHandle'] || null),
        video_url: (r['VideoURL'] || null),
        video_status: (r['VideoStatus'] || null),
        mfg_info_url: (r['LinkToMFGInfo'] || null),
        shopify_product_template: (r['ShopifyProductTemplate'] || null),
        created_at: dateEntered || null, // will take min later
        created_by: 'import',
        _minDate: dateEntered || null
      });
    } else {
      const agg = productKeyMap.get(productKey);
      if (dateEntered) {
        if (!agg._minDate || dateEntered < agg._minDate) {
          agg._minDate = dateEntered;
          agg.created_at = dateEntered;
        }
      }
      // Prefer first non-null for other fields, keep existing otherwise
    }

    // Variant mapping
    const colorFamilyRaw = r['Color Family'];
    let color_id = toIntOrNull(colorFamilyRaw);
    if (color_id === null) {
      // special cases
      const colorStr = (colorFamilyRaw || '').toString().trim();
      if (/colors?\s+(will|may)\s+vary/i.test(colorStr)) {
        color_id = 23; // convention for 'any color'
      }
    }

    const uploadedRaw = r['UploadedToShopify'];
    const uploadedAt = parseDateOrNull(uploadedRaw);

    const variant = {
      id: accessoryId,
      // product_id = set later after product creation
      op1_name: r['Color'] ? 'Color' : null,
      op1_value: r['Color'] || null,
      op2_name: r['Size'] ? 'Size' : null,
      op2_value: r['Size'] || null,
      sku: r['SKU'] || null,
      price: toNumOrNull(r['RetailPrice']),
      stock_quantity: toIntOrNull(r['CurrentQtyOnHand']) || 0,
      created_at: dateEntered || new Date(),
      created_by: 'import',
      updated_at: new Date(),
      order_cost: toNumOrNull(r['OrderCost']),
      carrying_cost: null,
      vendor_status: (r['Vendor Status'] || null),
      vendor_status_date: parseDateOrNull(r['vendor_status_updated_at']) || null,
      vendor_sku: (r['VendorSKU'] || null),
      UPC: (r['UPC'] || null),
      color_id: color_id,
      uploaded_to_shopify_at: uploadedAt,
      notes: (r['Note'] || null),
      map_price: toNumOrNull(r['MAPPrice']),
      msrp: toNumOrNull(r['MSRPPrice']),
      player_id: toIntOrNull(r['Signature']) && playerIdSet.has(toIntOrNull(r['Signature'])) ? toIntOrNull(r['Signature']) : null,
      case_qty: toNumOrNull(r['CaseQty']),
      case_weight_lbs: toNumOrNull(r['CaseWeightLbs']),
      case_dimensions: (r['CaseDimensions'] || null),
      shopify_weight_lbs: toNumOrNull(r['ShopifyWeightLbs']),
      release_date_and_time: parseDateOrNull(r['ReleaseDate']),
      notes_images: (r['ImageNotes'] || null),
      id_legacy: null, // not used; we set actual id
      sku_shopify: null // generated by DB
    };

    variantRecords.push({ variant, productKey, ctx: { ...ctx, Title: title } });
  }

  if (errors.length) {
    console.warn(`[importAccessories] Pre-validation collected ${errors.length} issues (will log to DB).`);
    if (!dryRun) {
      for (const e of errors) await logError(supabase, e.msg, e.ctx);
    }
  }

  // Create products (unique by productKey)
  const uniqueProducts = Array.from(productKeyMap.values());
  console.log(`[importAccessories] Unique products to upsert: ${uniqueProducts.length}`);

  let productIdByKey = new Map();
  if (!dryRun) {
    // Insert in chunks; check if any existing product (should be empty if truncated)
    const prodChunks = chunk(uniqueProducts, CHUNK_SIZE);
    for (let i = 0; i < prodChunks.length; i++) {
      const payload = prodChunks[i].map(p => ({
        category_id: p.category_id,
        brand_id: p.brand_id,
        name: p.name,
        description: p.description,
        created_at: p.created_at || new Date(),
        created_by: p.created_by,
        shopify_handle: p.shopify_handle,
        video_url: p.video_url,
        video_status: p.video_status,
        mfg_info_url: p.mfg_info_url,
        shopify_product_template: p.shopify_product_template
      }));
      const { data, error } = await supabase.from('t_products').insert(payload).select('id, category_id, brand_id, name');
      if (error) {
        console.error(`[importAccessories] Failed inserting product chunk ${i + 1}:`, error.message);
        await logError(supabase, 'insert_products_failed', { chunk: i + 1, error: error.message });
        process.exit(1);
      }
      // Map back by key
      for (let j = 0; j < data.length; j++) {
        const p = prodChunks[i][j];
        const key = `${p.category_id}|${p.brand_id ?? ''}|${(p.name || '').toLowerCase()}`;
        productIdByKey.set(key, data[j].id);
      }
      console.log(`  - Inserted product chunk ${i + 1}/${prodChunks.length} (${payload.length} rows)`);
    }
  } else {
    // Dry-run: synthesize IDs
    let fakeId = 1000000;
    for (const [key] of productKeyMap) productIdByKey.set(key, fakeId++);
  }

  // Insert variants (attach product_id)
  console.log(`[importAccessories] Inserting ${variantRecords.length} variants...`);
  if (!dryRun) {
    const vChunks = chunk(variantRecords, CHUNK_SIZE);
    for (let i = 0; i < vChunks.length; i++) {
      const payload = vChunks[i].map(rec => ({
        id: rec.variant.id, // explicit id (AccessoryID)
        product_id: productIdByKey.get(rec.productKey),
        op1_name: rec.variant.op1_name,
        op1_value: rec.variant.op1_value,
        op2_name: rec.variant.op2_name,
        op2_value: rec.variant.op2_value,
        sku: rec.variant.sku,
        price: rec.variant.price,
        stock_quantity: rec.variant.stock_quantity,
        created_at: rec.variant.created_at,
        created_by: rec.variant.created_by,
        updated_at: rec.variant.updated_at,
        order_cost: rec.variant.order_cost,
        carrying_cost: rec.variant.carrying_cost,
        vendor_status: rec.variant.vendor_status,
        vendor_status_date: rec.variant.vendor_status_date,
        vendor_sku: rec.variant.vendor_sku,
        UPC: rec.variant.UPC,
        color_id: rec.variant.color_id,
        uploaded_to_shopify_at: rec.variant.uploaded_to_shopify_at,
        notes: rec.variant.notes,
        map_price: rec.variant.map_price,
        msrp: rec.variant.msrp,
        player_id: rec.variant.player_id,
        case_qty: rec.variant.case_qty,
        case_weight_lbs: rec.variant.case_weight_lbs,
        case_dimensions: rec.variant.case_dimensions,
        shopify_weight_lbs: rec.variant.shopify_weight_lbs,
        release_date_and_time: rec.variant.release_date_and_time,
        notes_images: rec.variant.notes_images
      }));
      const { error } = await supabase.from('t_product_variants').insert(payload);
      if (error) {
        console.error(`[importAccessories] Failed inserting variant chunk ${i + 1}:`, error.message);
        await logError(supabase, 'insert_variants_failed', { chunk: i + 1, error: error.message });
        process.exit(1);
      }
      console.log(`  - Inserted variant chunk ${i + 1}/${vChunks.length} (${payload.length} rows)`);
    }
  }

  // Insert variant attributes for Gender and Age Group
  console.log('[importAccessories] Inserting variant attributes (Gender, Age Group)...');
  const attrRows = [];
  const sourceById = new Map(rows.map(x => [toIntOrNull(x['AccessoryID']), x]));
  for (const rec of variantRecords) {
    const src = sourceById.get(rec.variant.id) || null;
    const gender = src?.['Gender'] || null;
    const ageGroup = src?.['Age Group'] || null;

    if (gender) {
      const [categoryId,] = rec.productKey.split('|');
      // Business rule: skip Gender for category_id 45 (Bags)
      if (Number(categoryId) === 45) {
        // Do not log missing attribute and do not insert Gender for Bags
      } else {
        const catAttrId = catAttrByKey.get(`${categoryId}|gender`);
        if (!catAttrId) {
          await logError(supabase, 'missing_category_attribute', { variant_id: rec.variant.id, name: 'Gender', category_id: Number(categoryId) });
        } else {
          attrRows.push({ variant_id: rec.variant.id, category_attribute_id: catAttrId, attribute_value: String(gender), created_by: 'import' });
        }
      }
    }

    if (ageGroup) {
      const [categoryId2,] = rec.productKey.split('|');
      const catAttrId2 = catAttrByKey.get(`${categoryId2}|age group`);
      if (!catAttrId2) {
        await logError(supabase, 'missing_category_attribute', { variant_id: rec.variant.id, name: 'Age Group', category_id: Number(categoryId2) });
      } else {
        attrRows.push({ variant_id: rec.variant.id, category_attribute_id: catAttrId2, attribute_value: String(ageGroup), created_by: 'import' });
      }
    }
  }

  if (!dryRun && attrRows.length) {
    const aChunks = chunk(attrRows, CHUNK_SIZE);
    for (let i = 0; i < aChunks.length; i++) {
      const { error } = await supabase.from('t_variant_attributes').insert(aChunks[i]);
      if (error) {
        console.error(`[importAccessories] Failed inserting attributes chunk ${i + 1}:`, error.message);
        await logError(supabase, 'insert_attributes_failed', { chunk: i + 1, error: error.message });
        process.exit(1);
      }
      console.log(`  - Inserted attributes chunk ${i + 1}/${aChunks.length} (${aChunks[i].length} rows)`);
    }
  }

  console.log('\n[importAccessories] Completed. Summary:');
  console.log(`  Products: ${productKeyMap.size}`);
  console.log(`  Variants: ${variantRecords.length}`);
  console.log(`  Attributes: ${attrRows.length}`);
  console.log(`  Errors noted: ${errors.length}`);
  if (dryRun) console.log('  Mode: DRY RUN (no DB writes)');
}

main().catch(async (e) => {
  console.error('[importAccessories] Fatal error:', e);
  process.exit(1);
});

