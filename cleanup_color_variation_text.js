import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function cleanupColorVariationText() {
  try {
    console.log('Cleaning up color variation text for records with color_id = 23...\n');

    const colorVariationPhrases = [
      'Colors May Vary',
      'Colors Will Vary', 
      'Color May Vary'
    ];

    let totalUpdated = 0;

    for (const phrase of colorVariationPhrases) {
      console.log(`Processing "${phrase}"...`);

      // Find records with color_id = 23 that still contain this phrase
      const { data: colorRecords, error: fetchError } = await supabase
        .from('t_sdasins')
        .select('id, notes, raw_notes, color_id')
        .eq('color_id', 23)
        .like('notes', `%${phrase}%`)
        .not('notes', 'like', 'XXXX%');

      if (fetchError) {
        console.error(`Error fetching records for "${phrase}":`, fetchError);
        continue;
      }

      let phraseUpdated = 0;

      for (const record of colorRecords || []) {
        // Remove the phrase from notes
        const updatedNotes = record.notes.replace(new RegExp(escapeRegex(phrase), 'gi'), '').trim();
        
        // Clean up extra spaces and punctuation
        const cleanedNotes = updatedNotes
          .replace(/\s+/g, ' ')
          .replace(/^\s*[-|,\[\]]\s*/, '')
          .replace(/\s*[-|,\[\]]\s*$/, '')
          .trim();

        const updateData = {
          notes: cleanedNotes
        };

        // Store original notes in raw_notes if not already stored
        if (!record.raw_notes) {
          updateData.raw_notes = record.notes;
        }

        const { error: updateError } = await supabase
          .from('t_sdasins')
          .update(updateData)
          .eq('id', record.id);

        if (updateError) {
          console.error(`Error updating record ${record.id}:`, updateError);
        } else {
          phraseUpdated++;
          totalUpdated++;
          if (phraseUpdated <= 5) { // Show first 5 updates per phrase
            console.log(`  Updated ID ${record.id}: removed "${phrase}"`);
          }
        }
      }

      console.log(`  Removed "${phrase}" from ${phraseUpdated} records`);
    }

    console.log(`\nTotal records updated: ${totalUpdated}`);

    // Verify cleanup
    console.log('\nVerifying cleanup...');
    for (const phrase of colorVariationPhrases) {
      const { count, error } = await supabase
        .from('t_sdasins')
        .select('*', { count: 'exact', head: true })
        .eq('color_id', 23)
        .like('notes', `%${phrase}%`)
        .not('notes', 'like', 'XXXX%');

      if (!error) {
        console.log(`Remaining records with color_id=23 and "${phrase}": ${count}`);
      }
    }

    console.log('\nColor variation text cleanup completed successfully!');

  } catch (error) {
    console.error('Error:', error);
  }
}

function escapeRegex(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Run the script
cleanupColorVariationText();
