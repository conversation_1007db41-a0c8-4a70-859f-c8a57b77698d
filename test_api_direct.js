// Test API endpoints directly
async function testAPI() {
  console.log('🧪 Testing B2F API Endpoints Directly\n');

  // Test count
  console.log('1️⃣ Testing /api/b2f/count...');
  try {
    const response = await fetch('http://localhost:3001/api/b2f/count');
    const data = await response.json();
    console.log('Response:', JSON.stringify(data, null, 2));
  } catch (err) {
    console.log('Error:', err.message);
  }

  // Test records
  console.log('\n2️⃣ Testing /api/b2f/records...');
  try {
    const response = await fetch('http://localhost:3001/api/b2f/records');
    const data = await response.json();
    console.log('Success:', data.success);
    console.log('Record count:', data.records?.length || 0);
    if (data.records && data.records.length > 0) {
      console.log('First record:', JSON.stringify(data.records[0], null, 2));
    }
  } catch (err) {
    console.log('Error:', err.message);
  }

  console.log('\n✅ API test complete');
}

testAPI().catch(console.error);
