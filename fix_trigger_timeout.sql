-- Fix the fn_try_publish_product_disc_q function to handle timeouts better
CREATE OR REPLACE FUNCTION fn_try_publish_product_disc_q()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_start_time TIMESTAMP;
  v_elapsed_time INTERVAL;
  v_timeout_seconds INTEGER := 25; -- Set a timeout limit in seconds
BEGIN
  -- Record the start time
  v_start_time := clock_timestamp();

  -- Log the trigger firing
  INSERT INTO t_error_logs (error_message, context, created_at)
  VALUES ('Trigger fired', 'fn_try_publish_product_disc_q triggered on table ' || TG_TABLE_NAME || ' for record id ' || COALESCE(NEW.id::text, 'null'), now());

  -- Check if we're dealing with t_images table
  IF TG_TABLE_NAME = 't_images' THEN
    -- Log the t_images branch
    INSERT INTO t_error_logs (error_message, context, created_at)
    VALUES ('t_images branch', 'NEW.table_name=' || NEW.table_name || ', NEW.image_verified=' || NEW.image_verified::text || ', NEW.record_id=' || COALESCE(NEW.record_id::text, 'null'), now());

    -- Check conditions for processing
    IF NEW.table_name = 't_discs'
       AND NEW.image_verified = TRUE
       AND (OLD.image_verified IS DISTINCT FROM NEW.image_verified) THEN
      
      -- Check elapsed time before proceeding
      v_elapsed_time := clock_timestamp() - v_start_time;
      IF extract(epoch FROM v_elapsed_time) > v_timeout_seconds THEN
        INSERT INTO t_error_logs (error_message, context, created_at)
        VALUES ('Timeout', 'fn_try_publish_product_disc_q timed out after ' || extract(epoch FROM v_elapsed_time) || ' seconds', now());
        RETURN NEW;
      END IF;
      
      -- Create a background task instead of processing directly
      INSERT INTO t_task_queue(task_type, payload, status, scheduled_at, created_at)
      VALUES (
        'process_disc_queue',
        jsonb_build_object('disc_id', NEW.record_id),
        'pending',
        NOW(),
        NOW()
      );
      
      -- Log the task creation
      INSERT INTO t_error_logs (error_message, context, created_at)
      VALUES ('Task created', 'Created process_disc_queue task for disc_id=' || NEW.record_id, now());
    END IF;

  -- Check if we're dealing with t_discs table
  ELSIF TG_TABLE_NAME = 't_discs' THEN
    -- Check elapsed time before proceeding
    v_elapsed_time := clock_timestamp() - v_start_time;
    IF extract(epoch FROM v_elapsed_time) > v_timeout_seconds THEN
      INSERT INTO t_error_logs (error_message, context, created_at)
      VALUES ('Timeout', 'fn_try_publish_product_disc_q timed out after ' || extract(epoch FROM v_elapsed_time) || ' seconds', now());
      RETURN NEW;
    END IF;
    
    -- Create a background task instead of processing directly
    INSERT INTO t_task_queue(task_type, payload, status, scheduled_at, created_at)
    VALUES (
      'process_disc_queue',
      jsonb_build_object('disc_id', NEW.id),
      'pending',
      NOW(),
      NOW()
    );
    
    -- Log the task creation
    INSERT INTO t_error_logs (error_message, context, created_at)
    VALUES ('Task created', 'Created process_disc_queue task for disc_id=' || NEW.id, now());
  END IF;

  RETURN NEW;

EXCEPTION
  WHEN OTHERS THEN
    -- Log the error
    IF TG_TABLE_NAME = 't_images' THEN
      INSERT INTO t_error_logs (error_message, context, created_at)
      VALUES (SQLERRM, 'Error in fn_try_publish_product_disc_q for product Disc ' || NEW.record_id, now());
      RAISE NOTICE 'Error in fn_try_publish_product_disc_q: %', SQLERRM;
    ELSE
      INSERT INTO t_error_logs (error_message, context, created_at)
      VALUES (SQLERRM, 'Error in fn_try_publish_product_disc_q for product Disc ' || NEW.id, now());
      RAISE NOTICE 'Error in fn_try_publish_product_disc_q: %', SQLERRM;
    END IF;
    RETURN NEW;
END;
$$;
