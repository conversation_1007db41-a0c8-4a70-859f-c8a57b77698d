import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function debugCurrentFundraiserState() {
    try {
        console.log('🔍 Debugging current fundraiser state...\n');
        
        // Check ALL records in rows 22, 25, 28
        console.log('1. Checking ALL records in rows 22, 25, 28...');
        const { data: allRecords, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*')
            .in('excel_row_hint', [22, 25, 28])
            .order('excel_row_hint, excel_column');

        if (error) {
            console.error('❌ Error querying records:', error);
            return;
        }

        console.log(`✅ Found ${allRecords.length} total records in rows 22, 25, 28:`);
        
        allRecords.forEach((record, index) => {
            console.log(`\n--- Record ${index + 1} ---`);
            console.log(`Row: ${record.excel_row_hint}, Column: ${record.excel_column}`);
            console.log(`Orderable: ${record.is_orderable}`);
            console.log(`Mold: "${record.mold_name}"`);
            console.log(`Plastic: "${record.plastic_name}"`);
            console.log(`Stamp: "${record.stamp_name}"`);
            console.log(`MPS ID: ${record.calculated_mps_id || 'NULL'}`);
            console.log(`Raw Line: "${record.raw_line_type}"`);
            console.log(`Raw Model: "${record.raw_model}"`);
            console.log(`Mapping Key: "${record.excel_mapping_key}"`);
        });

        // Check specifically what's orderable
        console.log('\n2. Checking ORDERABLE records in rows 22, 25, 28...');
        const { data: orderableRecords, error: orderableError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, mold_name, plastic_name, calculated_mps_id, excel_mapping_key')
            .in('excel_row_hint', [22, 25, 28])
            .eq('is_orderable', true)
            .order('excel_row_hint, excel_column');

        if (orderableError) {
            console.error('❌ Error querying orderable records:', orderableError);
            return;
        }

        console.log(`✅ Found ${orderableRecords.length} ORDERABLE records:`);
        orderableRecords.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.mold_name} | ${record.plastic_name}`);
            console.log(`      MPS ID: ${record.calculated_mps_id || 'NULL'}`);
            console.log(`      Mapping: ${record.excel_mapping_key}`);
        });

        // Check what would be included in daily automation export
        console.log('\n3. Checking what daily automation would export...');
        const { data: exportData, error: exportError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .in('excel_row_hint', [22, 25, 28]);

        if (exportError) {
            console.error('❌ Error querying export data:', exportError);
            return;
        }

        console.log(`✅ Daily automation would export ${exportData.length} records from rows 22, 25, 28:`);
        exportData.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}`);
            console.log(`      MPS ID: ${record.calculated_mps_id || 'NULL'}`);
            console.log(`      Mapping: ${record.excel_mapping_key}`);
        });

        console.log('\n🎯 Analysis:');
        console.log('   • If row 22 shows up in orderable records, the header fix didn\'t work');
        console.log('   • If row 25/28 have records in columns other than A, cleanup didn\'t work');
        console.log('   • If row 25/28 don\'t have records in column A, creation didn\'t work');
        
    } catch (error) {
        console.error('❌ Debug failed:', error.message);
    }
}

debugCurrentFundraiserState().catch(console.error);
