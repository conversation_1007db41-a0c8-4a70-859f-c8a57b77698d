// processMatchOslToDiscsTask.js - Process match_osl_to_discs tasks
import { createClient } from '@supabase/supabase-js';

// Function to process a match_osl_to_discs task
export default async function processMatchOslToDiscsTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processMatchOslToDiscsTask.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[processMatchOslToDiscsTask.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[processMatchOslToDiscsTask.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[processMatchOslToDiscsTask.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[processMatchOslToDiscsTask.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[processMatchOslToDiscsTask.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process match OSL to discs task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[processMatchOslToDiscsTask.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process match OSL to discs task. Missing OSL id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const oslId = payload.id;
    console.log(`[processMatchOslToDiscsTask.js] Matching discs to OSL id=${oslId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Get the OSL record to get mps_id, color_id, min_weight, max_weight
    const { data: oslRecord, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('mps_id, color_id, min_weight, max_weight')
      .eq('id', oslId)
      .maybeSingle();

    if (oslError) {
      const errMsg = `[processMatchOslToDiscsTask.js] Error retrieving OSL record: ${oslError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving OSL record for id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to retrieve OSL record. Database error.",
        error: oslError.message
      });
      return;
    }

    if (!oslRecord) {
      const errMsg = `[processMatchOslToDiscsTask.js] OSL record not found for id=${oslId}`;
      console.error(errMsg);
      await logError(errMsg, `OSL record not found for id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to match discs to OSL. OSL record not found.",
        error: 'OSL record not found'
      });
      return;
    }

    console.log(`[processMatchOslToDiscsTask.js] OSL record: ${JSON.stringify(oslRecord)}`);

    // Ensure min_weight and max_weight are numbers
    oslRecord.min_weight = parseInt(oslRecord.min_weight);
    oslRecord.max_weight = parseInt(oslRecord.max_weight);
    console.log(`[processMatchOslToDiscsTask.js] OSL weight range (parsed): ${oslRecord.min_weight}-${oslRecord.max_weight}, types: min=${typeof oslRecord.min_weight}, max=${typeof oslRecord.max_weight}`);

    // Declare vendor mapping variables at function level to avoid scope issues
    let vendorMatchedDiscIds = [];
    let vendorDebugDetails = [];

    console.log(`[processMatchOslToDiscsTask.js] OSL ${oslId} MPS ${oslRecord.mps_id} - processing both regular and vendor mapping (redirect logic handled by individual disc matching functions).`);

    // Find matching discs based on criteria
    // We need to apply the same rounding logic as in find_matching_osl function
    // Find discs with matching mps_id and color_id (or color_id=23 or OSL color_id=23) - including sold discs for complete dual mapping
    let query = supabase
      .from('t_discs')
      .select('id, weight, color_id')
      .eq('mps_id', oslRecord.mps_id);

    // Handle color matching logic
    // The correct logic is: (osl.color_id = disc.color_id OR osl.color_id = 23)
    // This means we match when either:
    // 1. The OSL color matches the disc color exactly, OR
    // 2. The OSL color is 23 (any color)
    if (oslRecord.color_id === 23) {
      // OSL color_id is 23 (any color), so match any disc color
      console.log(`[processMatchOslToDiscsTask.js] OSL color_id is 23 (any color), matching any disc color`);
    } else {
      // Match discs with the same color_id as the OSL
      console.log(`[processMatchOslToDiscsTask.js] Matching discs with color_id=${oslRecord.color_id}`);
      query = query.eq('color_id', oslRecord.color_id);
    }

    const { data: matchingDiscs, error: matchingError } = await query;

    if (matchingError) {
      const errMsg = `[processMatchOslToDiscsTask.js] Error finding matching discs: ${matchingError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Finding matching discs for OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to find matching discs. Database error.",
        error: matchingError.message
      });
      return;
    }

    console.log(`[processMatchOslToDiscsTask.js] Found ${matchingDiscs ? matchingDiscs.length : 0} potential matching discs`);

    if (!matchingDiscs || matchingDiscs.length === 0) {
      console.log(`[processMatchOslToDiscsTask.js] No matching discs found for OSL id=${oslId}`);

      // Update t_inv_osl to ensure available_quantity is 0
      const { error: updateInvError } = await supabase
        .from('t_inv_osl')
        .upsert({
          id: oslId,
          available_quantity: 0
        });

      if (updateInvError) {
        const errMsg = `[processMatchOslToDiscsTask.js] Error updating t_inv_osl: ${updateInvError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Updating t_inv_osl for OSL id=${oslId}`);
      }

      // Let's get more information about why no discs were found
      // First, let's find all discs with the matching mps_id (including sold discs)
      const { data: discsWithMpsId, error: mpsIdError } = await supabase
        .from('t_discs')
        .select('id, mps_id, color_id, weight, sold_date')
        .eq('mps_id', oslRecord.mps_id);

      if (mpsIdError) {
        console.error(`[processMatchOslToDiscsTask.js] Error fetching discs with mps_id=${oslRecord.mps_id}: ${mpsIdError.message}`);
      }

      // Create debug info for these discs
      const debugInfo = discsWithMpsId ? discsWithMpsId.map(disc => {
        const weight = parseFloat(disc.weight);
        const decimalPart = weight - Math.floor(weight);
        const roundedWeight = decimalPart >= 0.5 ? Math.ceil(weight) : Math.floor(weight);
        const isInRange = roundedWeight >= oslRecord.min_weight && roundedWeight <= oslRecord.max_weight;
        const colorMatches = disc.color_id === oslRecord.color_id || oslRecord.color_id === 23;

        return {
          disc_id: disc.id,
          mps_id: disc.mps_id,
          color_id: disc.color_id,
          osl_color_id: oslRecord.color_id,
          color_matches: colorMatches,
          original_weight: disc.weight,
          parsed_weight: weight,
          decimal_part: decimalPart,
          rounded_weight: roundedWeight,
          in_range: isInRange,
          min_weight: oslRecord.min_weight,
          max_weight: oslRecord.max_weight
        };
      }) : [];

      await updateTaskStatus(task.id, 'completed', {
        message: `No matching discs found for OSL id=${oslId}.`,
        osl_id: oslId,
        discs_matched: 0,
        osl_record: oslRecord,
        discs_with_matching_mps: discsWithMpsId ? discsWithMpsId.length : 0,
        debug_info: debugInfo
      });
      return;
    }

    // Apply weight rounding and filter by min_weight and max_weight
    const matchedDiscIds = [];
    const debugDetails = [];

    console.log(`[processMatchOslToDiscsTask.js] OSL weight range: ${oslRecord.min_weight}-${oslRecord.max_weight}`);

    for (const disc of matchingDiscs) {
      // Apply custom rounding logic: X.5+ rounds to X+1, X.4- rounds to X
      console.log(`[processMatchOslToDiscsTask.js] Raw disc weight: ${disc.weight}, type: ${typeof disc.weight}`);
      const weight = parseFloat(disc.weight);
      console.log(`[processMatchOslToDiscsTask.js] Parsed disc weight: ${weight}, type: ${typeof weight}`);
      const decimalPart = weight - Math.floor(weight);
      let roundedWeight;

      if (decimalPart >= 0.5) {
        roundedWeight = Math.ceil(weight);
      } else {
        roundedWeight = Math.floor(weight);
      }

      console.log(`[processMatchOslToDiscsTask.js] Disc ID=${disc.id}, Original weight=${weight}, Decimal part=${decimalPart}, Rounded weight=${roundedWeight}`);

      // Check if the rounded weight is within the OSL's weight range
      const floorWeight = Math.floor(weight);
      const defaultInRange = roundedWeight >= oslRecord.min_weight && roundedWeight <= oslRecord.max_weight;
      const dualEligible = decimalPart >= 0.5 && decimalPart <= 0.85;
      const floorInRange = dualEligible && (floorWeight >= oslRecord.min_weight && floorWeight <= oslRecord.max_weight);
      const isInRange = defaultInRange || floorInRange;
      console.log(`[processMatchOslToDiscsTask.js] Disc ID=${disc.id}, Weight in range: ${isInRange} (rounded=${roundedWeight} in [${oslRecord.min_weight}, ${oslRecord.max_weight}] OR${dualEligible ? '' : ' NOT'} dual floor=${floorWeight} in range)`);

      // Store debug info
      debugDetails.push({
        disc_id: disc.id,
        original_weight: disc.weight,
        parsed_weight: weight,
        decimal_part: decimalPart,
        rounded_weight: roundedWeight,
        floor_weight: floorWeight,
        dual_eligible: dualEligible,
        default_in_range: defaultInRange,
        floor_in_range: floorInRange,
        in_range: isInRange,
        min_weight: oslRecord.min_weight,
        max_weight: oslRecord.max_weight
      });

      if (isInRange) {
        matchedDiscIds.push(disc.id);
      }
    }

    console.log(`[processMatchOslToDiscsTask.js] After weight filtering, found ${matchedDiscIds.length} matching discs`);

    if (matchedDiscIds.length === 0) {
      console.log(`[processMatchOslToDiscsTask.js] No discs match the regular weight criteria for OSL id=${oslId}, checking vendor weight matching...`);

      // DUAL MAPPING: Even if no regular matches, check for vendor matches using manufacturer weight
      console.log(`[processMatchOslToDiscsTask.js] Checking for vendor matches using manufacturer weight...`);

      let vendorQuery = supabase
        .from('t_discs')
        .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
        .eq('mps_id', oslRecord.mps_id)
        .not('weight_mfg', 'is', null);

      // Handle color matching for vendor mapping
      if (oslRecord.color_id === 23) {
        console.log(`[processMatchOslToDiscsTask.js] OSL color_id is 23 (any color), matching any disc color for vendor mapping`);
      } else {
        console.log(`[processMatchOslToDiscsTask.js] Matching discs with color_id=${oslRecord.color_id} for vendor mapping`);
        vendorQuery = vendorQuery.eq('color_id', oslRecord.color_id);
      }

      const { data: vendorMatchingDiscs, error: vendorMatchingError } = await vendorQuery;

      if (!vendorMatchingError && vendorMatchingDiscs && vendorMatchingDiscs.length > 0) {
        console.log(`[processMatchOslToDiscsTask.js] Found ${vendorMatchingDiscs.length} potential vendor matching discs`);

        // Process each disc using redirect logic for vendor mapping
        for (const disc of vendorMatchingDiscs) {
          console.log(`[processMatchOslToDiscsTask.js] Processing disc ${disc.id} for vendor mapping with redirect logic...`);

          // Use redirect function to find the correct vendor OSL for this disc
          const { data: vendorOslResult, error: vendorOslError } = await supabase.rpc(
            'find_matching_osl_by_mfg_weight_with_redirect',
            {
              mps_id_param: disc.mps_id,
              color_id_param: disc.color_id,
              weight_mfg_param: disc.weight_mfg
            }
          );

          let vendorOslId = null;
          let redirectInfo = '';

          if (vendorOslError) {
            console.error(`[processMatchOslToDiscsTask.js] Error finding vendor OSL for disc ${disc.id}:`, vendorOslError);
          } else if (vendorOslResult && vendorOslResult.length > 0) {
            vendorOslId = vendorOslResult[0].osl_id;
            redirectInfo = vendorOslResult[0].redirect_info || '';
            console.log(`[processMatchOslToDiscsTask.js] Disc ${disc.id} vendor OSL: ${vendorOslId}, redirect: ${redirectInfo}`);
          } else {
            console.log(`[processMatchOslToDiscsTask.js] No vendor OSL found for disc ${disc.id}`);
          }

          // Store vendor debug info
          vendorDebugDetails.push({
            disc_id: disc.id,
            weight_mfg: disc.weight_mfg,
            rounded_weight_mfg: Math.round(disc.weight_mfg),
            vendor_osl_found: vendorOslId,
            redirect_info: redirectInfo,
            original_osl: oslId
          });

          if (vendorOslId) {
            vendorMatchedDiscIds.push({
              disc_id: disc.id,
              vendor_osl_id: vendorOslId
            });
          }
        }

        console.log(`[processMatchOslToDiscsTask.js] After vendor redirect processing, found ${vendorMatchedDiscIds.length} matching discs`);

        // Update vendor OSL mappings if any matches found
        // Note: Each disc may have a different vendor_osl_id due to redirect logic
        if (vendorMatchedDiscIds.length > 0) {
          let updateSuccessCount = 0;
          let updateErrorCount = 0;

          for (const discMapping of vendorMatchedDiscIds) {
            const { error: updateVendorDiscError } = await supabase
              .from('t_discs')
              .update({
                vendor_osl_id: discMapping.vendor_osl_id
              })
              .eq('id', discMapping.disc_id);

            if (updateVendorDiscError) {
              console.error(`[processMatchOslToDiscsTask.js] Error updating disc ${discMapping.disc_id} with vendor OSL ${discMapping.vendor_osl_id}:`, updateVendorDiscError);
              updateErrorCount++;
            } else {
              console.log(`[processMatchOslToDiscsTask.js] Successfully linked disc ${discMapping.disc_id} to vendor OSL ${discMapping.vendor_osl_id}`);
              updateSuccessCount++;
            }
          }

          console.log(`[processMatchOslToDiscsTask.js] Vendor mapping complete: ${updateSuccessCount} successful, ${updateErrorCount} errors`);
        }
      } // End of vendor mapping if statement

      // Update t_inv_osl to ensure available_quantity is 0 (no unsold regular weight matches)
      const { error: updateInvError } = await supabase
        .from('t_inv_osl')
        .upsert({
          id: oslId,
          available_quantity: 0
        });

      if (updateInvError) {
        const errMsg = `[processMatchOslToDiscsTask.js] Error updating t_inv_osl: ${updateInvError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Updating t_inv_osl for OSL id=${oslId}`);
      }

      const vendorMatchCount = vendorMatchedDiscIds.length;

      await updateTaskStatus(task.id, 'completed', {
        message: `Dual mapping complete - Regular: 0 discs, Vendor: ${vendorMatchCount} discs matched to OSL id=${oslId}.`,
        osl_id: oslId,
        discs_matched_regular: 0,
        discs_matched_vendor: vendorMatchCount,
        osl_record: oslRecord,
        discs_with_matching_mps_and_color: matchingDiscs.length,
        debug_info: debugDetails,
        vendor_debug_info: vendorDebugDetails
      });
      return;
    }

    // Update the matching discs to link them to this OSL (regular weight mapping)
    const { error: updateDiscsError } = await supabase
      .from('t_discs')
      .update({
        order_sheet_line_id: oslId
      })
      .in('id', matchedDiscIds);

    if (updateDiscsError) {
      const errMsg = `[processMatchOslToDiscsTask.js] Error updating discs with regular mapping: ${updateDiscsError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating discs for OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update discs with OSL id. Database error.",
        error: updateDiscsError.message
      });
      return;
    }

    console.log(`[processMatchOslToDiscsTask.js] Successfully linked ${matchedDiscIds.length} discs to OSL id=${oslId} using regular weight`);

    // DUAL MAPPING: Now also find discs that match using manufacturer weight for vendor_osl_id
    // Note: vendorMatchedDiscIds and vendorDebugDetails are already declared at function level

    console.log(`[processMatchOslToDiscsTask.js] Finding discs for vendor OSL mapping using manufacturer weight...`);

    // Query for discs with manufacturer weight data
    let vendorQuery = supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
      .eq('mps_id', oslRecord.mps_id)
      .not('weight_mfg', 'is', null);

      // Handle color matching for vendor mapping
      if (oslRecord.color_id === 23) {
        console.log(`[processMatchOslToDiscsTask.js] OSL color_id is 23 (any color), matching any disc color for vendor mapping`);
      } else {
        console.log(`[processMatchOslToDiscsTask.js] Matching discs with color_id=${oslRecord.color_id} for vendor mapping`);
        vendorQuery = vendorQuery.eq('color_id', oslRecord.color_id);
      }

      const { data: vendorMatchingDiscs, error: vendorMatchingError } = await vendorQuery;

      if (vendorMatchingError) {
        const errMsg = `[processMatchOslToDiscsTask.js] Error finding vendor matching discs: ${vendorMatchingError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Finding vendor matching discs for OSL id=${oslId}`);
        // Don't return error - continue with regular mapping success
      } else {
        console.log(`[processMatchOslToDiscsTask.js] Found ${vendorMatchingDiscs ? vendorMatchingDiscs.length : 0} potential vendor matching discs`);

        if (vendorMatchingDiscs && vendorMatchingDiscs.length > 0) {
          // Process each disc using redirect logic for vendor mapping
          for (const disc of vendorMatchingDiscs) {
            console.log(`[processMatchOslToDiscsTask.js] Processing disc ${disc.id} for vendor mapping with redirect logic...`);

            // Use redirect function to find the correct vendor OSL for this disc
            const { data: vendorOslResult, error: vendorOslError } = await supabase.rpc(
              'find_matching_osl_by_mfg_weight_with_redirect',
              {
                mps_id_param: disc.mps_id,
                color_id_param: disc.color_id,
                weight_mfg_param: disc.weight_mfg
              }
            );

            let vendorOslId = null;
            let redirectInfo = '';

            if (vendorOslError) {
              console.error(`[processMatchOslToDiscsTask.js] Error finding vendor OSL for disc ${disc.id}:`, vendorOslError);
            } else if (vendorOslResult && vendorOslResult.length > 0) {
              vendorOslId = vendorOslResult[0].osl_id;
              redirectInfo = vendorOslResult[0].redirect_info || '';
              console.log(`[processMatchOslToDiscsTask.js] Disc ${disc.id} vendor OSL: ${vendorOslId}, redirect: ${redirectInfo}`);
            } else {
              console.log(`[processMatchOslToDiscsTask.js] No vendor OSL found for disc ${disc.id}`);
            }

            // Store vendor debug info
            vendorDebugDetails.push({
              disc_id: disc.id,
              weight_mfg: disc.weight_mfg,
              rounded_weight_mfg: Math.round(disc.weight_mfg),
              vendor_osl_found: vendorOslId,
              redirect_info: redirectInfo,
              original_osl: oslId
            });

            if (vendorOslId) {
              vendorMatchedDiscIds.push({
                disc_id: disc.id,
                vendor_osl_id: vendorOslId
              });
            }
          }

          console.log(`[processMatchOslToDiscsTask.js] After vendor redirect processing, found ${vendorMatchedDiscIds.length} matching discs`);

          // Update vendor OSL mappings if any matches found
          // Note: Each disc may have a different vendor_osl_id due to redirect logic
          if (vendorMatchedDiscIds.length > 0) {
            let updateSuccessCount = 0;
            let updateErrorCount = 0;

            for (const discMapping of vendorMatchedDiscIds) {
              const { error: updateVendorDiscError } = await supabase
                .from('t_discs')
                .update({
                  vendor_osl_id: discMapping.vendor_osl_id
                })
                .eq('id', discMapping.disc_id);

              if (updateVendorDiscError) {
                console.error(`[processMatchOslToDiscsTask.js] Error updating disc ${discMapping.disc_id} with vendor OSL ${discMapping.vendor_osl_id}:`, updateVendorDiscError);
                updateErrorCount++;
              } else {
                console.log(`[processMatchOslToDiscsTask.js] Successfully linked disc ${discMapping.disc_id} to vendor OSL ${discMapping.vendor_osl_id}`);
                updateSuccessCount++;
              }
            }

            console.log(`[processMatchOslToDiscsTask.js] Vendor mapping complete: ${updateSuccessCount} successful, ${updateErrorCount} errors`);
          }
        }
        }

    // Update t_inv_osl with the new available_quantity (based on UNSOLD regular weight matches only)
    // Count only unsold discs for inventory purposes
    const { data: unsoldRegularMatches, error: unsoldCountError } = await supabase
      .from('t_discs')
      .select('id', { count: 'exact', head: true })
      .eq('order_sheet_line_id', oslId)
      .is('sold_date', null);

    const unsoldCount = unsoldCountError ? 0 : (unsoldRegularMatches || 0);

    const { error: updateInvError } = await supabase
      .from('t_inv_osl')
      .upsert({
        id: oslId,
        available_quantity: unsoldCount
      });

    if (updateInvError) {
      const errMsg = `[processMatchOslToDiscsTask.js] Error updating t_inv_osl: ${updateInvError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating t_inv_osl for OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update t_inv_osl with new available_quantity. Database error.",
        error: updateInvError.message
      });
      return;
    }

    console.log(`[processMatchOslToDiscsTask.js] Successfully updated t_inv_osl for OSL id=${oslId} with available_quantity=${unsoldCount}`);

    const vendorMatchCount = vendorMatchedDiscIds.length;

    await updateTaskStatus(task.id, 'completed', {
      message: `Success! Dual mapping complete - Regular: ${matchedDiscIds.length} discs, Vendor: ${vendorMatchCount} discs linked to OSL id=${oslId}.`,
      osl_id: oslId,
      discs_matched_regular: matchedDiscIds.length,
      discs_matched_vendor: vendorMatchCount,
      debug_info: debugDetails,
      vendor_debug_info: vendorDebugDetails
    });
  } catch (err) {
    const errMsg = `[processMatchOslToDiscsTask.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process match OSL to discs task due to an unexpected error.",
      error: err.message
    });
  }
}
