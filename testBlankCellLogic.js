import XLSX from 'xlsx';

// Test the blank cell logic on PP Z Lite section
async function testBlankCellLogic() {
    try {
        console.log('🧪 Testing blank cell logic on PP Z Lite section...\n');
        
        const inputFile = 'data/external data/discraftstock.xlsx';
        const workbook = XLSX.readFile(inputFile);
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        
        console.log(`📊 Excel file loaded: ${range.e.r + 1} rows`);
        
        // Test specifically on PP Z Lite section (rows 158-159)
        console.log('🔍 Testing PP Z Lite section (rows 158-159)...\n');
        
        // Simulate the weight headers for this section
        const currentWeightHeaders = {
            12: { min: 141, max: 150, name: '141-150g', letter: 'M', columnIndex: 12 },
            13: { min: 151, max: 159, name: '151-159g', letter: 'N', columnIndex: 13 },
            14: { min: 160, max: 166, name: '160-166g', letter: 'O', columnIndex: 14 }
        };
        
        // Test rows 158-159 (PP Z Lite Drive and Passion)
        for (let row = 157; row <= 158; row++) { // 0-based indexing
            const rowData = {};
            
            // Read all columns for this row
            for (let col = 0; col <= range.e.c; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                const cell = worksheet[cellAddress];
                if (cell && cell.v !== null && cell.v !== undefined && cell.v !== '') {
                    rowData[`col_${col}`] = cell.v;
                }
            }
            
            const rawPlastic = rowData.col_1?.toString().trim() || '';
            const rawModel = rowData.col_4?.toString().trim() || '';
            
            console.log(`--- Row ${row + 1}: ${rawPlastic} ${rawModel} ---`);
            
            // Test each weight column
            const columnsToCheck = Object.keys(currentWeightHeaders).map(col => parseInt(col));
            
            for (const col of columnsToCheck) {
                const weightData = currentWeightHeaders[col];
                const status = rowData[`col_${col}`];
                
                console.log(`Column ${weightData.letter} (${weightData.name}):`);
                console.log(`  Raw status: "${status}" (type: ${typeof status})`);
                
                // Apply the new logic
                let isCurrentlyAvailable = false;
                let isOrderable = true;
                let shouldImport = true;
                
                if (status === undefined || status === null || status === '') {
                    // Blank cells = available and orderable
                    isCurrentlyAvailable = true;
                    isOrderable = true;
                    console.log(`  ✅ BLANK CELL -> Available: true, Orderable: true`);
                } else if (status === 'N/A' || status === 'n/a' || status === 'NA') {
                    // N/A = not available for this weight range, skip entirely
                    shouldImport = false;
                    console.log(`  ❌ N/A -> SKIP (not imported)`);
                } else {
                    // Status indicators present - use them
                    isCurrentlyAvailable = (status === 9 || status === '9');
                    isOrderable = (status === 9 || status === '9' ||
                                 (status && status.toString().toLowerCase() === 'out'));
                    console.log(`  📦 STATUS "${status}" -> Available: ${isCurrentlyAvailable}, Orderable: ${isOrderable}`);
                }
                
                if (shouldImport) {
                    console.log(`  🎯 WOULD IMPORT: ${rawPlastic} ${rawModel} ${weightData.name}`);
                } else {
                    console.log(`  🚫 WOULD SKIP`);
                }
                
                console.log('');
            }
            
            console.log('');
        }
        
        console.log('📊 Expected Results:');
        console.log('PP Z Lite Drive (row 158):');
        console.log('  - 141-150g: "out" -> Available: false, Orderable: true ✅');
        console.log('  - 151-159g: "out" -> Available: false, Orderable: true ✅');
        console.log('  - 160-166g: "out" -> Available: false, Orderable: true ✅');
        console.log('  - Total products: 3 ✅');
        console.log('');
        console.log('PP Z Lite Passion (row 159):');
        console.log('  - 141-150g: "out" -> Available: false, Orderable: true ✅');
        console.log('  - 151-159g: BLANK -> Available: true, Orderable: true ✅');
        console.log('  - 160-166g: BLANK -> Available: true, Orderable: true ✅');
        console.log('  - Total products: 3 ✅');
        
        console.log('\n✅ Test completed! The fixed logic should now import blank cells as available products.');
        
    } catch (error) {
        console.error('❌ Test error:', error);
    }
}

testBlankCellLogic();
