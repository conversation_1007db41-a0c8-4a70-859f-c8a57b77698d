const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const fs = require('fs');
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('Error: VEEQO_API_KEY must be set in .env file');
  process.exit(1);
}

console.log('🗂️  VEEQO UNARCHIVE TOOL');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}` };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to get archived products with stock
async function getArchivedProductsWithStock() {
  console.log('🔍 Finding archived products with available stock...\n');
  
  const archivedWithStock = [];
  let page = 1;
  const maxPages = 5; // Limit to first 5 pages for testing
  
  while (page <= maxPages) {
    console.log(`📄 Checking page ${page}...`);
    
    const result = await makeVeeqoRequest(`https://api.veeqo.com/products?page=${page}&page_size=20`);
    
    if (!result.success) {
      console.error(`Error fetching page ${page}: ${result.error}`);
      break;
    }
    
    if (!Array.isArray(result.data) || result.data.length === 0) {
      console.log(`No more products found on page ${page}`);
      break;
    }
    
    const products = result.data;
    console.log(`   Analyzing ${products.length} products...`);
    
    for (const product of products) {
      // Check if product is archived (all channels pulled)
      const isArchived = product.channel_products && 
                        product.channel_products.length > 0 && 
                        product.channel_products.every(cp => cp.status === 'pulled');
      
      if (isArchived) {
        // Check if product has stock
        let totalStock = 0;
        if (product.sellables && product.sellables.length > 0) {
          totalStock = product.sellables.reduce((sum, sellable) => {
            return sum + (sellable.available_stock_level_at_all_warehouses || 0);
          }, 0);
        }
        
        if (totalStock > 0) {
          archivedWithStock.push({
            id: product.id,
            title: product.title,
            stock: totalStock,
            channel_products: product.channel_products,
            created_at: product.created_at,
            sellables_count: product.sellables ? product.sellables.length : 0
          });
          
          console.log(`   ✅ Found: ID ${product.id} with ${totalStock} units in stock`);
        }
      }
    }
    
    console.log(`   Found ${archivedWithStock.length} archived products with stock so far...`);
    
    if (products.length < 20) break; // Last page
    page++;
  }
  
  console.log(`\n📊 Total found: ${archivedWithStock.length} archived products with stock\n`);
  return archivedWithStock;
}

// Function to unarchive a product
async function unarchiveProduct(productId) {
  console.log(`\n🔄 Attempting to unarchive product ${productId}...`);
  
  // Get product details
  const productResult = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!productResult.success) {
    console.error(`❌ Failed to get product: ${productResult.error}`);
    return false;
  }
  
  const product = productResult.data;
  console.log(`📋 Product: "${product.title}"`);
  
  if (!product.channel_products || product.channel_products.length === 0) {
    console.log(`⚠️  Product has no channel products`);
    return false;
  }
  
  const pulledChannels = product.channel_products.filter(cp => cp.status === 'pulled');
  console.log(`📤 Found ${pulledChannels.length} pulled channel(s)`);
  
  if (pulledChannels.length === 0) {
    console.log(`✅ Product is already active`);
    return true;
  }
  
  // For now, just show what would need to be done
  console.log(`\n💡 To unarchive this product, you would need to:`);
  pulledChannels.forEach(cp => {
    console.log(`   - Update channel product ID ${cp.id} on channel "${cp.channel?.short_name || 'Unknown'}"`);
    console.log(`     From status: "${cp.status}" to status: "active" or "live"`);
    console.log(`     Channel Product Remote ID: ${cp.remote_id}`);
  });
  
  console.log(`\n🔧 This can be done through:`);
  console.log(`   1. Veeqo web interface (recommended)`);
  console.log(`   2. API call to update channel product status`);
  console.log(`   3. Re-publishing the product to the channel`);
  
  return true;
}

// Main function
async function main() {
  try {
    // Get archived products with stock
    const archivedProducts = await getArchivedProductsWithStock();
    
    if (archivedProducts.length === 0) {
      console.log('✅ No archived products with stock found in the sample!');
      return;
    }
    
    // Display results
    console.log('📋 ARCHIVED PRODUCTS WITH STOCK:');
    console.log('='.repeat(80));
    
    archivedProducts.forEach((product, index) => {
      console.log(`${index + 1}. ID: ${product.id} | Stock: ${product.stock} units | Sellables: ${product.sellables_count}`);
      console.log(`   Title: ${product.title}`);
      console.log(`   Created: ${new Date(product.created_at).toLocaleDateString()}`);
      console.log('');
    });
    
    // Save to file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `archived_products_with_stock_${timestamp}.json`;
    fs.writeFileSync(filename, JSON.stringify(archivedProducts, null, 2));
    console.log(`💾 Saved ${archivedProducts.length} products to: ${filename}\n`);
    
    // Show example of how to unarchive
    if (archivedProducts.length > 0) {
      console.log('🛠️  EXAMPLE: Analyzing first product for unarchiving...');
      await unarchiveProduct(archivedProducts[0].id);
    }
    
    console.log('\n📝 SUMMARY:');
    console.log(`- Found ${archivedProducts.length} archived products with available stock`);
    console.log(`- These products are "pulled" from channels but have inventory`);
    console.log(`- They could potentially be reactivated to generate sales`);
    console.log(`- Use the Veeqo interface or API to change status from "pulled" to "active"`);
    
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the main function
main();
