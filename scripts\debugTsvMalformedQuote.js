import fs from 'fs';
import path from 'path';

/*
Usage:
  node scripts/debugTsvMalformedQuote.js --index 2595048 --row 4290

- --index: character offset reported by <PERSON> (index)
- --row: row number reported by <PERSON> (1-based for data rows; header is line 1 in file)
If not provided, defaults to the numbers we've observed.
*/

const FILE_PATH = path.join(process.cwd(), 'data', 'external data', 'access', 'tAccessories.txt');

function getArg(name, defVal) {
  const i = process.argv.indexOf(`--${name}`);
  if (i === -1) return defVal;
  const v = process.argv[i + 1];
  const n = Number(v);
  return Number.isFinite(n) ? n : defVal;
}

const errorIndex = getArg('index', 2595048);
const errorRow = getArg('row', 4290);

if (!fs.existsSync(FILE_PATH)) {
  console.error('File not found:', FILE_PATH);
  process.exit(1);
}

const raw = fs.readFileSync(FILE_PATH, 'utf-8');
const len = raw.length;

const start = Math.max(0, errorIndex - 200);
const end = Math.min(len, errorIndex + 200);
const snippet = raw.slice(start, end);

// Compute line number from index
let computedLine = 1;
for (let i = 0; i < Math.min(errorIndex, len); i++) {
  if (raw[i] === '\n') computedLine++;
}

const lines = raw.split(/\r?\n/);
const showIdx = Math.max(1, Math.min(lines.length, errorRow));
const prevLine = lines[showIdx - 2] ?? '';
const thisLine = lines[showIdx - 1] ?? '';
const nextLine = lines[showIdx] ?? '';

function visualize(s) {
  return s
    .replace(/\t/g, '⟶\t')
    .replace(/\r/g, '⟵r')
    .replace(/\n/g, '⟵n');
}

function summarizeLine(line) {
  const tabs = (line.match(/\t/g) || []).length;
  const quotes = (line.match(/\"/g) || []).length;
  const dblQuotes = (line.match(/"/g) || []).length;
  return { length: line.length, tabs, quotes, dblQuotes, startsWithQuote: line.trimStart().startsWith('"'), endsWithQuote: line.trimEnd().endsWith('"') };
}

console.log('File:', FILE_PATH);
console.log('Reported index:', errorIndex);
console.log('Reported row:', errorRow);
console.log('Computed line from index:', computedLine);
console.log('\nContext around index (±200 chars):');
console.log('---8<---');
console.log(visualize(snippet));
console.log('---8<---');

console.log(`\nPrev line (#${showIdx - 1}):`);
console.log(visualize(prevLine));
console.log('Summary:', summarizeLine(prevLine));

console.log(`\nThis line (#${showIdx}):`);
console.log(visualize(thisLine));
console.log('Summary:', summarizeLine(thisLine));

console.log(`\nNext line (#${showIdx + 1}):`);
console.log(visualize(nextLine));
console.log('Summary:', summarizeLine(nextLine));

// Also try to split header and row by tabs for easy spot check
if (lines.length > 1) {
  const header = lines[0].split('\t');
  const rowVals = thisLine.split('\t');
  console.log(`\nHeader columns: ${header.length}, Row columns: ${rowVals.length}`);
  const maxShow = Math.min(header.length, rowVals.length, 30);
  for (let i = 0; i < maxShow; i++) {
    const h = header[i];
    const v = rowVals[i];
    console.log(`[${i}] ${h} = ${v}`);
  }
  if (rowVals.length > maxShow) {
    console.log(`... (${rowVals.length - maxShow} more columns)`);
  }
}

