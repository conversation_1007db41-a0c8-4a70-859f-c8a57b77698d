#!/usr/bin/env node
/*
Generates manifests for the Shopify Accessory Images cleanup run.
- Reads the TSV at data/external data/access/Shopify Accessory Images.txt
- Filters rows where URL equals the magentoproduct base
- For each AccessoryID, checks target and source to classify:
  - newly moved (target exists, source missing)
  - pre-existing (target exists, source still present)
- Excludes any AccessoryIDs listed in logs/clean_accessory_images_failures.csv
- Writes two CSVs under logs/:
  - accessory_images_manifest_newly_moved.csv
  - accessory_images_manifest_preexisting.csv
*/

import fs from 'fs';
import { promises as fsp } from 'fs';
import path from 'path';

const TSV_PATH = path.join('data', 'external data', 'access', 'Shopify Accessory Images.txt');
const FAIL_CSV = path.join('logs', 'clean_accessory_images_failures.csv');
const TARGET_DIR = "\\\\NANCY\\nancyv8\\Images\\DG Accessory Products\\Uploaded to AWS";
const SOURCE_ARCHIVE = "\\\\NANCY\\nancyv8\\Images\\DG Accessory Products\\3 Magento Image Archive";
const BASE = 'https://d3f34rkxix3zin.cloudfront.net/shopify/dgaccessories/magentoproduct/';

function unquote(s){
  if (s == null) return '';
  s = String(s);
  if (s.startsWith('"') && s.endsWith('"')) return s.slice(1, -1);
  return s;
}

function parseTSV(raw){
  const lines = raw.split(/\r?\n/).filter(l => l.trim().length > 0);
  if (lines.length === 0) return { header: [], rows: [] };
  const header = lines.shift().split('\t').map(unquote);
  const rows = lines.map(line => {
    const cols = line.split('\t').map(unquote);
    const obj = {};
    header.forEach((h, i) => { obj[h] = cols[i] ?? ''; });
    return obj;
  });
  return { header, rows };
}

function readFailuresCsv(csv){
  const lines = csv.split(/\r?\n/).filter(Boolean);
  lines.shift(); // header
  const set = new Set();
  for (const line of lines){
    const parts = line.split(',');
    const acc = parts[0];
    if (acc) set.add(acc.trim());
  }
  return set;
}

(async () => {
  try {
    await fsp.mkdir('logs', { recursive: true });
    const raw = await fsp.readFile(TSV_PATH, 'utf8');
    const { header, rows } = parseTSV(raw);
    const baseRows = rows.filter(r => r.URL === BASE);

    let failed = new Set();
    try {
      const fraw = await fsp.readFile(FAIL_CSV, 'utf8');
      failed = readFailuresCsv(fraw);
    } catch {}

    const newly = [];
    const preexist = [];

    for (const r of baseRows){
      const id = String(r.AccessoryID || '').trim();
      if (!id || failed.has(id)) continue;
      const target = path.win32.join(TARGET_DIR, `${id}.jpg`);
      const src = path.win32.join(SOURCE_ARCHIVE, ...r.FileName.split('/'));
      const targetExists = fs.existsSync(target);
      if (!targetExists) continue; // skip missing targets
      const srcExists = fs.existsSync(src);
      if (srcExists) {
        preexist.push({ id, fileName: r.FileName, target, src });
      } else {
        newly.push({ id, fileName: r.FileName, target });
      }
    }

    const pretty = n => n.toLocaleString('en-US');

    const newCsv = 'AccessoryID,FileName,TargetPath\n' + newly.map(x => `${x.id},"${x.fileName}","${x.target}"`).join('\n');
    const preCsv = 'AccessoryID,FileName,TargetPath,SourcePath\n' + preexist.map(x => `${x.id},"${x.fileName}","${x.target}","${x.src}"`).join('\n');

    const newPath = path.join('logs', 'accessory_images_manifest_newly_moved.csv');
    const prePath = path.join('logs', 'accessory_images_manifest_preexisting.csv');
    await fsp.writeFile(newPath, newCsv, 'utf8');
    await fsp.writeFile(prePath, preCsv, 'utf8');

    console.log(`Manifest generated.`);
    console.log(`  Newly moved: ${pretty(newly.length)} -> ${newPath}`);
    console.log(`  Pre-existing: ${pretty(preexist.length)} -> ${prePath}`);
  } catch (e) {
    console.error('Error generating manifest:', e);
    process.exitCode = 1;
  }
})();

