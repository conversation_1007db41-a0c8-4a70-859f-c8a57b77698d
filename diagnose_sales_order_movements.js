// diagnose_sales_order_movements.js - show recent Complete sales orders and whether movements exist
import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function run() {
  try {
    // Fetch recent Complete orders not by Migration
    let { data: orders, error } = await supabase
      .from('t_sales_orders')
      .select('id, created_at, created_by, status')
      .eq('status', 'Complete')
      .not('created_by', 'eq', 'Migration')
      .order('created_at', { ascending: false })
      .limit(10);
    if (error) throw error;

    if (!orders || orders.length === 0) {
      console.log('No recent Complete orders (non-Migration) found.');
      return;
    }

    for (const o of orders) {
      const { data: movs, error: mErr } = await supabase
        .from('t_inventory_movements')
        .select('id')
        .eq('source_type', 'sale')
        .eq('source_id', o.id)
        .limit(1);
      if (mErr) {
        console.log(`Order ${o.id}: error checking movements: ${mErr.message}`);
      } else {
        console.log(`Order ${o.id} (${o.created_at}) movements_present=${(movs && movs.length>0)}`);
      }
    }
  } catch (e) {
    console.error('Error:', e?.message || e);
    process.exit(1);
  }
}

run();

