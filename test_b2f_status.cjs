const http = require('http');

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(body));
        } catch (e) {
          resolve(body);
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testB2FStatus() {
  try {
    console.log('Testing B2F status and availability...\n');
    
    // Check B2F count
    console.log('1. Checking B2F count...');
    const countResult = await makeRequest('/api/b2f/count');
    console.log('B2F Count Result:', countResult);
    
    // Check if there are any candidates available
    console.log('\n2. Testing auto-select with debug info...');
    const autoSelectResult = await makeRequest('/api/b2f/auto-select', 'POST', { maxOsls: 1 });
    console.log('Auto-select result:');
    console.log('- Success:', autoSelectResult.success);
    console.log('- Message:', autoSelectResult.message);
    console.log('- Total OSLs:', autoSelectResult.summary?.totalOsls || 0);
    console.log('- Total discs:', autoSelectResult.summary?.totalDiscs || 0);
    
    if (autoSelectResult.error) {
      console.log('- Error:', autoSelectResult.error);
    }
    
    if (autoSelectResult.debug) {
      console.log('- Debug info:', autoSelectResult.debug);
    }
    
  } catch (err) {
    console.error('Error:', err.message);
  }
}

testB2FStatus();
