// inspectExcelHeader.js - Inspect the Excel file header structure

import ExcelJS from 'exceljs';

async function inspectExcelHeader() {
    const excelFilePath = 'C:\\Users\\<USER>\\supabase_project\\data\\external data\\discraftstock.xlsx';
    
    try {
        console.log('🔍 Inspecting Excel file header structure...');
        console.log('================================================');
        
        // Load the Excel workbook
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(excelFilePath);

        // Get the first worksheet
        const worksheet = workbook.worksheets[0];
        console.log(`📋 Worksheet name: ${worksheet.name}`);
        
        // Inspect the first 20 rows and columns A-Z to find header structure
        console.log('\n📊 Header area content (rows 1-20, columns A-Z):');
        console.log('='.repeat(80));
        
        for (let row = 1; row <= 20; row++) {
            const rowData = [];
            let hasContent = false;
            
            for (let col = 1; col <= 26; col++) { // A-Z
                const cell = worksheet.getCell(row, col);
                const value = cell.value;
                const colLetter = String.fromCharCode(64 + col); // Convert to letter
                
                if (value !== null && value !== undefined && value !== '') {
                    rowData.push(`${colLetter}${row}: "${value}"`);
                    hasContent = true;
                }
            }
            
            if (hasContent) {
                console.log(`Row ${row}: ${rowData.join(' | ')}`);
            }
        }
        
        // Look for specific text that might indicate header fields
        console.log('\n🔍 Looking for specific header field indicators...');
        console.log('='.repeat(60));
        
        const searchTerms = [
            'order date', 'Order Date', 'ORDER DATE',
            'phone', 'Phone', 'PHONE',
            'email', 'Email', 'EMAIL',
            'address', 'Address', 'ADDRESS',
            'billing', 'Billing', 'BILLING',
            'shipping', 'Shipping', 'SHIPPING',
            'contact', 'Contact', 'CONTACT',
            'name', 'Name', 'NAME',
            'customer', 'Customer', 'CUSTOMER',
            'updated', 'Updated', 'UPDATED'
        ];
        
        for (let row = 1; row <= 30; row++) {
            for (let col = 1; col <= 30; col++) {
                const cell = worksheet.getCell(row, col);
                const value = cell.value;
                const colLetter = String.fromCharCode(64 + col);
                
                if (value && typeof value === 'string') {
                    for (const term of searchTerms) {
                        if (value.toLowerCase().includes(term.toLowerCase())) {
                            console.log(`Found "${term}" at ${colLetter}${row}: "${value}"`);
                        }
                    }
                }
            }
        }
        
        // Check specific cells that were mentioned in the code
        console.log('\n🎯 Checking specific cells from the code...');
        console.log('='.repeat(50));
        
        const cellsToCheck = ['D14', 'C5', 'O5', 'C6', 'C7', 'E7', 'G7', 'O6', 'C9', 'C10', 'C11', 'AA3'];
        
        for (const cellRef of cellsToCheck) {
            const cell = worksheet.getCell(cellRef);
            console.log(`${cellRef}: "${cell.value || 'EMPTY'}"`);
        }
        
    } catch (error) {
        console.error('❌ Error inspecting Excel file:', error.message);
    }
}

// Run the inspection
inspectExcelHeader()
    .then(() => {
        console.log('\n✅ Inspection completed');
        process.exit(0);
    })
    .catch(error => {
        console.error('💥 Inspection failed:', error);
        process.exit(1);
    });
