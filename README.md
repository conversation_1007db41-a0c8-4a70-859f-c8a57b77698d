# Task Queue Worker

A worker system for processing tasks from the Supabase `t_task_queue` table, with a focus on image verification tasks.

## Files

- **taskQueueWorker.js**: The main worker script that processes pending tasks
- **runTaskQueueWorker.js**: A runner script that executes the worker at regular intervals
- **enqueueImageVerificationTask.js**: A utility script for manually enqueueing tasks
- **fix_trigger_function.sql**: SQL script that fixes the trigger function to properly handle JSONB payloads

## Task Queue Schema

The system assumes a `t_task_queue` table with the following structure:

```sql
CREATE TABLE public.t_task_queue (
  id SERIAL PRIMARY KEY,
  task_type TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending',
  payload JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE,
  scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL, -- Required field, cannot be NULL
  result JSONB
);
```

## Task Types

The worker processes the following task types with simplified payload handling. It accepts either native JSON objects or simple JSON strings (no escaped quotes):

### 1. `verify_t_images_image`

Verifies that an image exists and is accessible. The payload should be a JSON object with an `id` field:

```json
{
  "id": 12345
}
```

For backward compatibility, the worker also supports the legacy format with `image_id`:

```json
{
  "image_id": 12345
}
```

### 2. `check_if_disc_ready_to_publish`

Checks if a disc is ready to be published and creates a publish task if it is. The payload should be a JSON object with a `disc_id` field:

```json
{
  "disc_id": 12345
}
```

This task type replaces the `fn_handle_disc_queue` database function, moving the logic to the worker. It:

1. Checks if the disc is in the `v_todo_discs` view
2. If it is, updates `t_discs.shopify_uploaded_notes` with a message
3. If it's not, creates a `publish_product_disc` task with the appropriate scheduled time

### 3. `insert_new_t_images_record`

Creates a new record in the `t_images` table. The payload should be a JSON object with `table_name` and `record_id` fields:

```json
{
  "table_name": "t_discs",
  "record_id": 12345
}
```

### 3. `delete_t_images_record`

Deletes a record from the `t_images` table. The payload should be a JSON object with `table_name` and `record_id` fields:

```json
{
  "table_name": "t_discs",
  "record_id": 12345
}
```

### 4. `verify_disc_image`

Verifies that a disc's image exists and is accessible, updating the verification fields directly in the t_discs table. The payload should be a JSON object with an `id` field that corresponds to a t_discs record:

```json
{
  "id": 12345
}
```

This task type is used for direct verification of disc images, updating the following fields in the t_discs table:
- `image_verified`: Boolean indicating if the image is accessible
- `image_verified_at`: Timestamp of when the verification was performed
- `image_verified_by`: Set to 'taskQueueWorker'
- `image_verified_notes`: Details about the verification result

### 5. `clear_disc_verification`

Clears the verification fields in the t_discs table when the image file name changes. The payload should be a JSON object with an `id` field that corresponds to a t_discs record:

```json
{
  "id": 12345
}
```

This task type sets the following fields in the t_discs table:
- `image_verified`: null
- `image_verified_at`: null
- `image_verified_by`: null
- `image_verified_notes`: Set to a message indicating the verification was cleared due to a file name update

### 6. `check_if_disc_is_ready`

Checks if a disc meets all the readiness criteria and updates the `ready_new` field accordingly. The payload should be a JSON object with an `id` field that corresponds to a t_discs record:

```json
{
  "id": 12345
}
```

This task type checks the following conditions:
1. All required fields are not null:
   - `image_file_name`
   - `shipment_id`
   - `weight`
   - `color_id`
   - `location`
   - `order_sheet_line_id`
2. `ready_button` is TRUE
3. `image_verified` is TRUE
4. `shopify_uploaded_at` is NULL
5. `sold_date` is NULL (disc is not already sold)

If all conditions are met, it sets `t_discs.ready_new` to TRUE, otherwise it sets it to FALSE.

### 7. `publish_disc` and `publish_product_disc`

Publishes a disc to Shopify using the publishProductDisc.js script. The payload should be a JSON object with an `id` field that corresponds to a t_discs record:

```json
{
  "id": 12345
}
```

This task type:
1. Spawns the publishProductDisc.js script with the disc ID
2. Captures the output from the script
3. Updates the task status based on the script's exit code

Both `publish_disc` and `publish_product_disc` task types are handled by the same function for compatibility.

### 8. `clear_incorrect_mps_image`

Clears an incorrect MPS image by deleting the t_images record and removing the file from S3. The payload should be a JSON object with an `id` field:

```json
{
  "id": 12345,
  "reason": "Incorrect image uploaded"
}
```

This task type:
1. Finds the t_images record where `table_name = 't_mps'` and `record_id = id`
2. Deletes that record from the t_images table
3. Deletes the corresponding file from the S3 bucket (in the `mps` folder, named `{id}.jpg`)

The task will complete successfully even if the t_images record or S3 file doesn't exist, making it safe to run multiple times.

## Usage

### Running the Worker

To run the worker once:

```bash
node taskQueueWorker.js
```

To run the worker as a daemon (continuously):

```bash
node runTaskQueueWorker.js
```

### Enqueueing Tasks

To manually enqueue a task for immediate execution:

```bash
node enqueueImageVerificationTask.js --id=12345
```

To schedule a task for future execution (e.g., 5 minutes from now):

```bash
node enqueueImageVerificationTask.js --id=12345 --delay=5
```

To enqueue a clear_incorrect_mps_image task:

```bash
node enqueueClearIncorrectMpsImageTask.js 12345 "Incorrect image uploaded"
```

## Task Results

When a task is completed, the `result` field in the task record will contain a message indicating the outcome:

### Successful Verification

```json
{
  "message": "Success! Image verified and t_images updated.",
  "stdout": "...",
  "stderr": "...",
  "exit_code": 0
}
```

### Failed Verification

```json
{
  "message": "Image Failed. t_images updated and can be queued for verification later by editing the t_images table.",
  "error": "Error details",
  "stdout": "...",
  "stderr": "...",
  "exit_code": 1
}
```

## Task Statuses

Tasks can have one of three statuses:

1. **completed**: The task was processed successfully. This includes both successful verifications and cases where the image failed verification but the process completed normally.

2. **error**: An error occurred during task processing, such as invalid payload, database errors, or unexpected exceptions. The task was not processed successfully.

3. **pending**: The task is waiting to be processed.

When a task is in the `completed` status, the t_images table will be updated with the verification result. When a task is in the `error` status, no changes are made to the t_images table.

## Task Scheduling

Tasks can be scheduled for future execution by setting the `scheduled_at` field. The worker will only process tasks where:

1. `status` is 'pending'
2. `task_type` is 'verify_t_images_image'
3. `scheduled_at` is less than or equal to the current time

Tasks with a future `scheduled_at` timestamp will be skipped until that time is reached.

The worker processes tasks in order of the oldest `scheduled_at` timestamp first, ensuring that tasks are executed in the order they were scheduled.

**Note:** The `scheduled_at` field is required and cannot be NULL. When enqueueing a task for immediate execution, it will be set to the current time.

## Error Handling

Errors are logged to both the console and the `t_error_logs` table. Failed tasks have their status set to 'failed' and include error details in the `result` field.

## Configuration

The worker uses the following environment variables:

- `SUPABASE_URL`: The URL of your Supabase project
- `SUPABASE_KEY`: Your Supabase service role key

These should be defined in a `.env` file in the project root.

The runner script (`runTaskQueueWorker.js`) runs the worker every 60 seconds by default. This interval can be adjusted by modifying the `INTERVAL_MS` constant.

## Troubleshooting

### JSONB Payload Type Error

If you encounter an error like `column "payload" is of type jsonb but expression is of type text`, it means there's a type mismatch when inserting into the `t_task_queue` table. This can happen if a trigger function is casting JSON to text before inserting it into a JSONB column.

The fix is to remove the `::text` cast from the trigger function, as shown in `fix_trigger_function.sql`:

```sql
-- Before (problematic)
to_json(json_build_object('id', NEW.id))::text

-- After (fixed)
json_build_object('id', NEW.id)
```

Apply the fix by running:
```
node applyTriggerFix.js
```
