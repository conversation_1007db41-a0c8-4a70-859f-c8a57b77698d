// Test if admin page is accessible
async function testAdminPage() {
  console.log('🧪 Testing admin page accessibility...');
  
  try {
    const response = await fetch('http://localhost:3001/admin.html');
    console.log('Status:', response.status);
    console.log('Content-Type:', response.headers.get('content-type'));
    
    if (response.ok) {
      const text = await response.text();
      console.log('Page size:', text.length, 'characters');
      
      // Check for key elements
      const hasTitle = text.includes('<title>');
      const hasB2FTab = text.includes('data-tab="b2f"');
      const hasB2FContent = text.includes('id="b2f"');
      const hasScript = text.includes('<script>');
      const hasClosingTags = text.includes('</html>');
      
      console.log('✅ Has title tag:', hasTitle);
      console.log('✅ Has B2F tab:', hasB2FTab);
      console.log('✅ Has B2F content:', hasB2FContent);
      console.log('✅ Has script section:', hasScript);
      console.log('✅ Has closing HTML:', hasClosingTags);
      
      if (text.length < 1000) {
        console.log('⚠️ Page content seems too short');
        console.log('First 500 chars:', text.substring(0, 500));
      } else {
        console.log('✅ Page content looks normal');
      }
    } else {
      console.log('❌ Page not accessible');
    }
  } catch (err) {
    console.log('❌ Error accessing page:', err.message);
  }
}

testAdminPage().catch(console.error);
