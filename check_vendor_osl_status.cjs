require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkVendorOslStatus() {
  try {
    console.log('Checking overall vendor_osl_id status...');
    
    // Check total discs with weight_mfg data
    const { count: totalWithWeightMfg, error: totalError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    // Check how many already have vendor_osl_id
    const { count: withVendorOsl, error: vendorError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .not('vendor_osl_id', 'is', null);
    
    // Check how many need vendor_osl_id
    const { count: needVendorOsl, error: needError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    console.log('=== VENDOR OSL STATUS ===');
    console.log(`Total discs with weight_mfg data: ${totalWithWeightMfg}`);
    console.log(`Discs with vendor_osl_id already set: ${withVendorOsl}`);
    console.log(`Discs needing vendor_osl_id: ${needVendorOsl}`);
    
    // Check weight ranges in OSLs to understand the scope
    const { data: oslRanges, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, min_weight, max_weight, mps_id')
      .not('min_weight', 'is', null)
      .not('max_weight', 'is', null)
      .order('min_weight')
      .limit(10);
    
    if (!oslError && oslRanges) {
      console.log('\nSample OSL weight ranges (lowest weights):');
      oslRanges.forEach(osl => {
        console.log(`  OSL ${osl.id} (MPS ${osl.mps_id}): ${osl.min_weight}-${osl.max_weight}g`);
      });
    }
    
    // Check some sample manufacturer weights
    const { data: sampleWeights, error: weightError } = await supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, mps_id')
      .not('weight_mfg', 'is', null)
      .order('weight_mfg')
      .limit(10);
    
    if (!weightError && sampleWeights) {
      console.log('\nSample manufacturer weights (lowest weights):');
      sampleWeights.forEach(disc => {
        console.log(`  Disc ${disc.id} (MPS ${disc.mps_id}): actual ${disc.weight}g, mfg ${disc.weight_mfg}g`);
      });
    }
    
    // Check if there are any OSLs with lower weight ranges that might match mfg weights
    const { data: lowOslRanges, error: lowOslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, min_weight, max_weight, mps_id')
      .not('min_weight', 'is', null)
      .not('max_weight', 'is', null)
      .lte('min_weight', 170)  // Look for OSLs with min weight <= 170
      .order('min_weight')
      .limit(20);
    
    if (!lowOslError && lowOslRanges) {
      console.log('\nOSLs with lower weight ranges (min_weight <= 170g):');
      lowOslRanges.forEach(osl => {
        console.log(`  OSL ${osl.id} (MPS ${osl.mps_id}): ${osl.min_weight}-${osl.max_weight}g`);
      });
    }
    
    // Let's try to find a potential match manually
    console.log('\n=== LOOKING FOR POTENTIAL MATCHES ===');
    
    // Get a disc with weight_mfg around 165-175 range
    const { data: testDiscs, error: testError } = await supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, mps_id, color_id')
      .not('weight_mfg', 'is', null)
      .gte('weight_mfg', 165)
      .lte('weight_mfg', 175)
      .limit(5);
    
    if (!testError && testDiscs) {
      for (const disc of testDiscs) {
        console.log(`\nTesting disc ${disc.id} (weight_mfg: ${disc.weight_mfg}g):`);
        
        // Look for OSLs that might match
        const { data: matchingOsls, error: matchError } = await supabase
          .from('t_order_sheet_lines')
          .select('id, min_weight, max_weight, color_id')
          .eq('mps_id', disc.mps_id)
          .lte('min_weight', Math.round(disc.weight_mfg))
          .gte('max_weight', Math.round(disc.weight_mfg));
        
        if (!matchError && matchingOsls && matchingOsls.length > 0) {
          console.log(`  ✅ Found ${matchingOsls.length} potentially matching OSLs:`);
          matchingOsls.forEach(osl => {
            const colorMatch = osl.color_id === disc.color_id || osl.color_id === 23;
            console.log(`    OSL ${osl.id}: ${osl.min_weight}-${osl.max_weight}g, color ${osl.color_id}, color match: ${colorMatch}`);
          });
        } else {
          console.log(`  ❌ No matching OSLs found for MPS ${disc.mps_id} with weight ${Math.round(disc.weight_mfg)}g`);
        }
      }
    }
    
  } catch (err) {
    console.error('Error:', err.message);
  }
}

checkVendorOslStatus();
