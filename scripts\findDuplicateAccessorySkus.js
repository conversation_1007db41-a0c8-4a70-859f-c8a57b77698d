import fs from 'fs';
import path from 'path';
import Papa from 'papaparse';

const FILE_PATH = path.join(process.cwd(), 'data', 'external data', 'access', 'tAccessories.txt');

function parseTsv(filePath) {
  const raw = fs.readFileSync(filePath, 'utf-8');
  const { data, errors } = Papa.parse(raw, {
    header: true,
    skipEmptyLines: true,
    delimiter: '\t',
    dynamicTyping: false,
  });
  if (errors?.length) {
    console.warn('TSV parse warnings (first 5):', errors.slice(0, 5));
  }
  return data;
}

function toIntOrNull(v) {
  if (v === undefined || v === null || v === '') return null;
  const n = Number(String(v).trim());
  return Number.isFinite(n) ? Math.trunc(n) : null;
}

function main() {
  if (!fs.existsSync(FILE_PATH)) {
    console.error('File not found:', FILE_PATH);
    process.exit(1);
  }
  const rows = parseTsv(FILE_PATH);
  console.log('Rows:', rows.length);

  const seen = new Map(); // sku -> { count, rows: [{AccessoryID, Title}] }
  for (const r of rows) {
    const sku = (r['SKU'] || '').toString().trim();
    if (!sku) continue;
    if (!seen.has(sku)) seen.set(sku, { count: 0, rows: [] });
    const rec = seen.get(sku);
    rec.count++;
    if (rec.rows.length < 10) {
      rec.rows.push({ AccessoryID: toIntOrNull(r['AccessoryID']), Title: (r['Title'] || '').toString().trim() });
    }
  }

  const dups = Array.from(seen.entries()).filter(([, v]) => v.count > 1)
    .sort((a, b) => b[1].count - a[1].count);

  if (dups.length === 0) {
    console.log('No duplicate SKUs found.');
    return;
  }

  console.log(`Found ${dups.length} duplicate SKU values. Top 50:`);
  for (let i = 0; i < Math.min(50, dups.length); i++) {
    const [sku, info] = dups[i];
    console.log(`#${i + 1}: SKU="${sku}" count=${info.count} example AccessoryIDs=${info.rows.map(r => r.AccessoryID).join(', ')} TitleSample="${info.rows[0]?.Title || ''}"`);
  }
}

main();

