import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function fixRow22HeaderParsing() {
    try {
        console.log('🔧 Fixing row 22 header parsing issue...\n');
        
        // 1. Delete the problematic row 22 records
        console.log('1. Deleting row 22 header records...');
        
        const { data: deletedRecords, error: deleteError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .delete()
            .eq('excel_row_hint', 22)
            .select();

        if (deleteError) {
            console.error('❌ Error deleting row 22:', deleteError);
            return;
        }

        console.log(`✅ Deleted ${deletedRecords?.length || 0} header records from row 22`);

        // 2. Check other header rows that might have the same issue
        console.log('\n2. Checking other header rows...');
        
        const headerRows = [24, 27, 126, 131, 134];
        
        for (const row of headerRows) {
            const { data: headerData, error: headerError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('id, excel_column, raw_line_type')
                .eq('excel_row_hint', row);

            if (headerError) {
                console.error(`❌ Error checking row ${row}:`, headerError);
                continue;
            }

            if (headerData.length > 0) {
                console.log(`⚠️  Row ${row} has ${headerData.length} header records - deleting...`);
                
                const { data: deletedHeaders, error: deleteHeaderError } = await supabase
                    .from('it_discraft_order_sheet_lines')
                    .delete()
                    .eq('excel_row_hint', row)
                    .select();

                if (deleteHeaderError) {
                    console.error(`❌ Error deleting row ${row}:`, deleteHeaderError);
                } else {
                    console.log(`✅ Deleted ${deletedHeaders?.length || 0} header records from row ${row}`);
                }
            } else {
                console.log(`✅ Row ${row} is clean`);
            }
        }

        // 3. Verify the special product rows are still intact
        console.log('\n3. Verifying special product rows are intact...');
        
        const productRows = [25, 28, 132, 135];
        
        for (const row of productRows) {
            const { data: productData, error: productError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('excel_column, mold_name, plastic_name')
                .eq('excel_row_hint', row);

            if (productError) {
                console.error(`❌ Error checking row ${row}:`, productError);
                continue;
            }

            if (productData.length > 0) {
                console.log(`✅ Row ${row}: ${productData.length} product records intact`);
                productData.forEach(record => {
                    console.log(`   Col ${record.excel_column}: ${record.plastic_name} ${record.mold_name}`);
                });
            } else {
                console.log(`❌ Row ${row}: No product records found - this is a problem!`);
            }
        }

        // 4. Test a quick export to see if it works
        console.log('\n4. Testing basic export functionality...');
        
        const { data: exportTestData, error: exportTestError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .in('excel_row_hint', [25, 28, 340, 341])
            .limit(10);

        if (exportTestError) {
            console.error('❌ Error getting export test data:', exportTestError);
        } else {
            console.log(`✅ Found ${exportTestData.length} exportable records for test`);
            
            // Create simple test data
            const testData = exportTestData.map(item => ({
                ...item,
                order: 1  // Simple test quantity
            }));

            console.log('Sample test data:');
            testData.slice(0, 3).forEach((record, index) => {
                console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: order=${record.order}`);
            });
        }

        console.log('\n🎉 Row 22 header parsing fix completed!');
        console.log('\n📋 Next steps:');
        console.log('1. Test the daily automation to see if export works');
        console.log('2. Check if email is being sent');
        console.log('3. If both work, the core issues are resolved');
        
    } catch (error) {
        console.error('❌ Fix failed:', error.message);
    }
}

fixRow22HeaderParsing().catch(console.error);
