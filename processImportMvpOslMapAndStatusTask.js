// processImportMvpOslMapAndStatusTask.js - Process import_mvp_osl_map_and_status tasks
import { google } from 'googleapis';
import dotenv from 'dotenv';
import ExcelJS from 'exceljs';
import fs from 'fs';
import path from 'path';
import nodemailer from 'nodemailer';
import { execSync } from 'child_process';
import * as XLSX from 'xlsx';

dotenv.config();

// --- Google Sheets helpers ---
async function initializeGoogleSheetsAPI() {
  let auth;
  if (process.env.GOOGLE_SERVICE_ACCOUNT_KEY) {
    try {
      const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_KEY);
      auth = new google.auth.GoogleAuth({ credentials, scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'] });
    } catch (e) {
      throw new Error(`Invalid GOOGLE_SERVICE_ACCOUNT_KEY JSON: ${e.message}`);
    }
  } else if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
    auth = new google.auth.GoogleAuth({ keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS, scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly'] });
  } else if (process.env.GOOGLE_API_KEY) {
    auth = process.env.GOOGLE_API_KEY; // for public sheets only
  } else {
    throw new Error('Google Sheets auth not configured. Set GOOGLE_SERVICE_ACCOUNT_KEY or GOOGLE_APPLICATION_CREDENTIALS or GOOGLE_API_KEY.');
  }
  return google.sheets({ version: 'v4', auth });
}

function extractSpreadsheetId(sheetsUrl) {
  const m = sheetsUrl.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
  if (!m) throw new Error('Invalid Google Sheets URL');
  return m[1];
}

async function fetchSheetValues(sheetsUrl, sheetName) {
  const spreadsheetId = extractSpreadsheetId(sheetsUrl);
  const sheets = await initializeGoogleSheetsAPI();
  const resp = await sheets.spreadsheets.values.get({
    spreadsheetId,
    range: `${sheetName}!A1:ZZ1000`,
    valueRenderOption: 'UNFORMATTED_VALUE',
    dateTimeRenderOption: 'FORMATTED_STRING'
  });
  return resp.data.values || [];
}

// Fetch cell formats (backgroundColor) for range to determine grey fills
async function fetchSheetFormats(sheetsUrl, sheetName) {
  const spreadsheetId = extractSpreadsheetId(sheetsUrl);
  const sheets = await initializeGoogleSheetsAPI();
  const resp = await sheets.spreadsheets.get({
    spreadsheetId,
    ranges: [`${sheetName}!A1:ZZ1000`],
    includeGridData: true,
    fields: 'sheets(data(rowData(values(effectiveFormat(backgroundColor),userEnteredFormat(backgroundColor)))))'
  });
  const data = resp.data?.sheets?.[0]?.data?.[0]?.rowData || [];
  const rows = [];
  for (const row of data) {
    const vals = row?.values || [];
    rows.push(vals.map(c => {
      const fmt = c?.effectiveFormat?.backgroundColor || c?.userEnteredFormat?.backgroundColor || null;
      if (!fmt) return { r: 1, g: 1, b: 1, a: 1 };
      const r = fmt.red ?? 1, g = fmt.green ?? 1, b = fmt.blue ?? 1, a = fmt.alpha ?? 1;
      return { r, g, b, a };
    }));
  }
  return rows; // 2D array of {r,g,b,a}
}

function colNumToLetter(n) {
  let s = '';
  while (n > 0) { n--; s = String.fromCharCode(65 + (n % 26)) + s; n = Math.floor(n / 26); }
  return s;
}

function findOSValues(mapData) {
  const os = [];
  for (let r = 0; r < mapData.length; r++) {
    const row = mapData[r] || [];
    for (let c = 0; c < row.length; c++) {
      const v = row[c];
      if (v && typeof v === 'string' && v.startsWith('OS')) {
        const num = v.substring(2);
        if (num && !isNaN(num)) os.push({ row: String(r + 1), col: colNumToLetter(c + 1), id: parseInt(num, 10), originalValue: v });
      }
    }
  }
  return os;
}

// Determine status from cell background color
function statusFromColor(color) {
  // Treat true white/blank as In Stock
  const r = color?.r ?? 1, g = color?.g ?? 1, b = color?.b ?? 1;
  const nearWhite = r >= 0.97 && g >= 0.97 && b >= 0.97;
  if (nearWhite) return 'In Stock';
  // If not near-white, only grey fills (r≈g≈b) carry meaning. Colored fills default to In Stock.
  const isGrey = Math.abs(r - g) < 0.06 && Math.abs(g - b) < 0.06 && Math.abs(r - b) < 0.06;
  if (!isGrey) return 'In Stock';
  // Grey: decide dark vs light by perceived luminance
  const L = 0.2126 * r + 0.7152 * g + 0.0722 * b; // 0=black, 1=white
  if (L <= 0.40) return 'Not Available';
  if (L < 0.95) return 'Out of Stock';
  return 'In Stock';
}

function addStatusFromFormats(newSheetFormats, osValues) {
  return osValues.map(v => {
    const r = parseInt(v.row, 10) - 1;
    // Support multi-letter columns
    const colIndex = v.col.split('').reduce((acc, ch) => acc * 26 + (ch.charCodeAt(0) - 64), 0) - 1;
    const color = newSheetFormats?.[r]?.[colIndex] || { r: 1, g: 1, b: 1, a: 1 };
    return { ...v, status: statusFromColor(color) };
  });
}

// --- Counts ---
async function calculateMvpDiscCounts(supabase, oslIds) {
  let updated = 0, totalInStock = 0, totalSold = 0; const errors = [];
  const batchSize = 50;
  for (let i = 0; i < oslIds.length; i += batchSize) {
    const batch = oslIds.slice(i, i + batchSize);
    try {
      const { data, error } = await supabase.rpc('calculate_mvp_osl_disc_counts', { osl_ids: batch });
      if (error) throw error;
      for (const rec of data || []) { updated++; totalInStock += rec.in_stock || 0; totalSold += rec.sold_last_90 || 0; }
    } catch (rpcErr) {
      // Fallback per-ID
      for (const id of batch) {
        try {
          const { count: inStockCount, error: inErr } = await supabase.from('t_discs').select('*', { count: 'exact', head: true }).eq('vendor_osl_id', id).is('sold_date', null);
          if (inErr) throw new Error(`in-stock: ${inErr.message}`);
          const { data: cfg, error: cfgErr } = await supabase.from('t_config').select('value').eq('key', 'mvp_disc_order_look_back_days').single();
          const lookbackDays = cfgErr || !cfg ? 60 : (parseInt(cfg.value) || 60);
          const lookbackDate = new Date(); lookbackDate.setDate(lookbackDate.getDate() - lookbackDays);
          const { count: soldCount, error: sErr } = await supabase
            .from('t_discs')
            .select('*', { count: 'exact', head: true })
            .eq('vendor_osl_id', id)
            .gte('sold_date', lookbackDate.toISOString())
            .not('sold_channel', 'eq', 'Fixed');
          if (sErr) throw new Error(`sold: ${sErr.message}`);
          const { error: updErr } = await supabase.from('it_mvp_osl_map').update({ in_stock: inStockCount || 0, sold_last_90: soldCount || 0 }).eq('id', id);
          if (updErr) throw new Error(`update: ${updErr.message}`);
          updated++; totalInStock += inStockCount || 0; totalSold += soldCount || 0;
        } catch (e) { errors.push(`OSL ${id}: ${e.message}`); }
      }
    }
  }
  return { updatedRecords: updated, totalInStock, totalSoldLast90: totalSold, errors: errors.length ? errors : null };
}

// --- Excel + Email ---
async function emailMvpOrderSheet(filePath, totalDiscs, summary = {}) {
  try {
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: process.env.SMTP_PORT || 587,
      secure: false,
      auth: { user: process.env.EMAIL_USER, pass: process.env.EMAIL_PASS }
    });
    const dateStr = new Date().toLocaleDateString('en-US', { year: 'numeric', month: '2-digit', day: '2-digit' });
    const subject = `MVP Order Sheet — qty:${summary.qtySum ?? totalDiscs} / cells:${summary.qtyCount ?? 'N/A'} / sold(${summary.lookbackDays ?? 'N/A'}d):${summary.totalSoldLast90 ?? 'N/A'} — ${dateStr}`;
    const html = `
      <h2>MVP Order Sheet Updated</h2>
      <p><strong>Date:</strong> ${dateStr}</p>
      <p><strong>Look back period:</strong> ${summary.lookbackDays ?? 'N/A'} days</p>
      <p><strong>Total Discs Added (sum of qty in Excel):</strong> ${totalDiscs}</p>
      <p><strong>Total discs sold during lookback (sum of it_mvp_osl_map.sold_last_90):</strong> ${summary.totalSoldLast90 ?? 'N/A'}</p>
      <p><strong>Count of .qty cells updated:</strong> ${summary.qtyCount ?? 'N/A'}</p>
      <p><strong>Sum of .qty values:</strong> ${summary.qtySum ?? totalDiscs}</p>
      <p><strong>Sum of .in_stock:</strong> ${summary.inStockSum ?? 'N/A'}</p>
      <p><strong>File:</strong> ${path.basename(filePath)}</p>
    `;
    const info = await transporter.sendMail({ from: process.env.EMAIL_USER, to: '<EMAIL>', subject, html, attachments: [{ filename: path.basename(filePath), path: filePath }] });
    return { success: true, messageId: info.messageId };
  } catch (e) { return { success: false, error: e.message }; }
}

async function updateMvpExcelFile(supabase) {
  // Prefer .xlsx; if missing, try to convert the .xls template using Excel COM via PowerShell
  const xlsxPath = 'C:\\Users\\<USER>\\supabase_project\\data\\external data\\mvp\\MVP-Wholesale.xlsx';
  const xlsPath  = 'C:\\Users\\<USER>\\supabase_project\\data\\external data\\mvp\\MVP-Wholesale.xls';
  try {
    // Always convert from .xls to fresh .xlsx using a temp file; replace atomically if successful
    const tmplDir = path.dirname(xlsxPath);
    const tmplBase = path.basename(xlsxPath, '.xlsx');
    const xlsxTemp = path.join(tmplDir, `${tmplBase}.tmp.xlsx`);

    // Prefer pure Node conversion via SheetJS; fallback to Excel/LibreOffice
    // Option B: If a manually converted XLSX template exists, do not overwrite it
    if (!fs.existsSync(xlsxPath) && fs.existsSync(xlsPath)) {
      try {
        if (fs.existsSync(xlsxTemp)) fs.unlinkSync(xlsxTemp);
      } catch {}
      // Prefer highest-fidelity: Excel COM -> LibreOffice -> SheetJS
      let producedVia = null;
      try {
        console.log('[MVP] Converting XLS -> XLSX via Excel COM (highest fidelity)...');
        const ps = `
$excel = New-Object -ComObject Excel.Application
$excel.Visible = $false
$wb = $excel.Workbooks.Open('${xlsPath.replace(/\\/g,"\\\\")}')
$wb.SaveAs('${xlsxTemp.replace(/\\/g,"\\\\")}', 51)
$wb.Close($false)
$excel.Quit()
        `;
        execSync(`powershell -NoProfile -ExecutionPolicy Bypass -Command "${ps.replace(/\"/g, '\\\"')}"`, { stdio: 'inherit' });
        producedVia = 'excel-com';
      } catch (convErr) {
        console.warn(`[MVP] Excel COM failed: ${convErr.message}. Trying LibreOffice...`);
        try {
          const sofficeCandidates = [
            'C\\\\Program Files\\\\LibreOffice\\\\program\\\\soffice.exe',
            'C\\\\Program Files (x86)\\\\LibreOffice\\\\program\\\\soffice.exe',
            'soffice'
          ];
          for (const exe of sofficeCandidates) {
            try {
              execSync(`"${exe}" --headless --convert-to xlsx --outdir "${tmplDir}" "${xlsPath}"`, { stdio: 'inherit' });
              producedVia = 'libreoffice';
              break;
            } catch {}
          }
          if (!producedVia) throw new Error('LibreOffice not found or conversion failed');
          if (producedVia === 'libreoffice') {
            const produced = xlsxPath;
            if (fs.existsSync(produced)) {
              try { if (fs.existsSync(xlsxTemp)) fs.unlinkSync(xlsxTemp); } catch {}
              fs.copyFileSync(produced, xlsxTemp);
            } else {
              throw new Error('LibreOffice did not produce XLSX');
            }
          }
        } catch (loErr) {
          console.warn(`[MVP] LibreOffice failed: ${loErr.message}. Falling back to SheetJS (lower fidelity)...`);
          try {
            const buf = fs.readFileSync(xlsPath);
            const wb = XLSX.read(buf, { type: 'buffer' });
            XLSX.writeFile(wb, xlsxTemp, { bookType: 'xlsx' });
            producedVia = 'sheetjs';
          } catch (sjErr) {
            console.error(`[MVP] SheetJS conversion failed: ${sjErr.message}`);
          }
        }
      }
      if (!fs.existsSync(xlsxTemp)) {
        console.warn('[MVP] Conversion did not produce XLSX; keeping existing template if present.');
      } else {
        try { if (fs.existsSync(xlsxPath)) fs.unlinkSync(xlsxPath); } catch {}
        fs.copyFileSync(xlsxTemp, xlsxPath);
        try { fs.unlinkSync(xlsxTemp); } catch {}
        console.log(`[MVP] XLSX template refreshed from XLS via: ${producedVia}`);
      }
    } else {
      if (fs.existsSync(xlsxPath)) {
        console.log('[MVP] Using existing manually-converted XLSX template; skipping auto conversion.');
      }
    }



    const now = new Date();
    const ts = now.toISOString().replace(/:/g, '-').replace(/\./g, '-').replace('T', '_').substring(0, 19);
    const dir = path.dirname(xlsxPath);
    const base = path.basename(xlsxPath, '.xlsx');
    const timestampedFilePath = path.join(dir, `${base}_${ts}.xlsx`);

    // Load OSL map rows in chunks
    const page = 1000; const rows = [];
    for (let from = 0; ; from += page) {
      const to = from + page - 1;
      const { data, error } = await supabase.from('it_mvp_osl_map').select('id,row,col,qty').order('id', { ascending: true }).range(from, to);
      if (error) throw new Error(`Fetch it_mvp_osl_map ${from}-${to}: ${error.message}`);
      if (!data || data.length === 0) break; rows.push(...data); if (data.length < page) break;
    }

    const nonZero = rows.filter(r => r.qty !== null && r.qty !== 0 && r.row && r.col);
    let updatedCells = 0, totalDiscsAdded = 0, nonZeroQtyCount = 0;

    // Prepare a CSV file for PowerShell COM to apply updates
    const updatesCsv = path.join(dir, `${base}_${ts}_updates.csv`);
    const lines = ['row,col,qty'];
    for (const rec of nonZero) {
      const rowNum = parseInt(rec.row, 10);
      const colNum = rec.col.split('').reduce((res, ch) => res * 26 + (ch.charCodeAt(0) - 64), 0);
      const qty = rec.qty;
      lines.push(`${rowNum},${colNum},${qty}`);
      updatedCells++; totalDiscsAdded += qty; nonZeroQtyCount++;
    }
    fs.writeFileSync(updatesCsv, lines.join('\n'));

    // Use existing manually converted XLSX if present; otherwise try LibreOffice to create the timestamped XLSX.
    let wroteVia = 'template+com';
    let producedTs = false;
    if (fs.existsSync(xlsxPath)) {
      try {
        fs.copyFileSync(xlsxPath, timestampedFilePath);
        producedTs = fs.existsSync(timestampedFilePath);
      } catch {}
    }
    if (!producedTs) {
      wroteVia = 'libreoffice+com';
      try {
        const dirOut = path.dirname(timestampedFilePath);
        const sofficeCandidates = [
        'C\\\\Program Files\\\\LibreOffice\\\\program\\\\soffice.exe',
        'C\\\\Program Files (x86)\\\\LibreOffice\\\\program\\\\soffice.exe',
        'soffice'
      ];
      for (const exe of sofficeCandidates) {
        try {
          execSync(`"${exe}" --headless --convert-to xlsx --outdir "${dirOut}" "${xlsPath}"`, { stdio: 'inherit' });
          // LibreOffice writes MVP-Wholesale.xlsx; copy to timestamped path
          if (fs.existsSync(xlsxPath)) {
            fs.copyFileSync(xlsxPath, timestampedFilePath);
            producedTs = true;
          }
          break;
        } catch {}
      }
    } catch {}

    }

    if (!producedTs) {
      // Fallback: Excel COM SaveAs directly to timestamped file
      wroteVia = 'excel-com';
      try {
        const ps = `
$excel = New-Object -ComObject Excel.Application
$excel.DisplayAlerts = $false
$excel.Visible = $false
$wb = $excel.Workbooks.Open('${xlsPath.replace(/\\/g,"\\\\")}')
$wb.SaveAs('${timestampedFilePath.replace(/\\/g,"\\\\")}', 51)
$wb.Close($false)
$excel.Quit()
        `;
        execSync(`powershell -NoProfile -ExecutionPolicy Bypass -Command "${ps.replace(/\"/g, '\\\"')}"`, { stdio: 'inherit' });
        producedTs = fs.existsSync(timestampedFilePath);
      } catch (e) {
        producedTs = false;
      }
    }

    if (!producedTs) {
      // Last fallback: SheetJS direct write (lower fidelity)
      wroteVia = 'sheetjs+com';
      try {
        const buf = fs.readFileSync(xlsPath);
        const wb = XLSX.read(buf, { type: 'buffer' });
        XLSX.writeFile(wb, timestampedFilePath, { bookType: 'xlsx' });
        producedTs = fs.existsSync(timestampedFilePath);
      } catch {}
    }

    if (!producedTs) {
      throw new Error('Failed to create timestamped XLSX via LibreOffice/Excel/SheetJS');
    }

    // Now open the timestamped workbook and write F15 + qty via Excel COM (to preserve formatting), unprotect, save
    try {
      const psWrite = `
$excel = New-Object -ComObject Excel.Application
$excel.DisplayAlerts = $false
$excel.Visible = $false
$excel.ScreenUpdating = $false
$excel.Calculation = -4135  # xlCalculationManual
$path = '${timestampedFilePath.replace(/\\/g,"\\\\")}'
# Ensure not read-only on disk
try { $fi = Get-Item $path; if ($fi.Attributes -band [IO.FileAttributes]::ReadOnly) { $fi.Attributes = 'Archive' } } catch {}
$wb = $excel.Workbooks.Open($path)
# Unprotect first before any writes
try { $wb.Unprotect() } catch {}
foreach ($s in $wb.Worksheets) { try { $s.Unprotect() } catch {} }
# Choose the Order Form worksheet by exact name (trim/case-insensitive). Fallback to contains('Order')
$ws = $null
$target = 'order form'
foreach ($s in $wb.Worksheets) { if (($s.Name + '').Trim().ToLower() -eq $target) { $ws = $s; break } }
if ($ws -eq $null) { foreach ($s in $wb.Worksheets) { if ((($s.Name + '').ToLower()) -like '*order*') { $ws = $s; break } } }
if ($ws -eq $null) { $ws = $wb.Worksheets.Item(1) }
$ws.Activate()
# Write header and qty updates; force text format for F15
$cell = $ws.Range('F15')
try { $cell.NumberFormat = '@' } catch {}
try { $cell.Value2 = '<EMAIL>' } catch {}
$ws.Cells.Item(15,6).NumberFormat = '@'
$ws.Cells.Item(15,6).Value2 = '<EMAIL>'
$rows = Import-Csv '${updatesCsv.replace(/\\/g,"\\\\")}'
foreach ($r in $rows) { $ws.Cells.Item([int]$r.row, [int]$r.col).Value2 = [int]$r.qty }
# Read-back verification of F15 and one sample updated cell
$first = $null; try { $first = ($rows | Select-Object -First 1) } catch {}
$v = $ws.Cells.Item(15,6).Text
$sv = ''
if ($first -ne $null) { try { $sv = $ws.Cells.Item([int]$first.row, [int]$first.col).Value2 } catch {} }
Write-Output ("[MVP] Applied {0} updates to sheet '{1}'. F15 now: '{2}'. SampleCell=({3},{4}) -> {5}" -f $rows.Count, $ws.Name, $v, ($first.row), ($first.col), $sv)
# Force calculation so dependent sheets like 'SalesOrder-Import' update
$excel.EnableEvents = $true
$excel.CalculateBeforeSave = $true
$excel.Calculation = -4105  # xlCalculationAutomatic
try { $wb.RefreshAll() } catch {}
# Activate dependent sheets to encourage recalc
try { ($wb.Worksheets.Item('Order Form')).Activate() } catch {}
try { ($wb.Worksheets.Item('SalesOrder-Indexing')).Activate() } catch {}
try { ($wb.Worksheets.Item('SalesOrder-Import')).Activate() } catch {}
try { $excel.CalculateFullRebuild() } catch {}
try { $excel.CalculateUntilAsyncQueriesDone() } catch {}
$wb.Save()
Start-Sleep -Milliseconds 500
$wb.Save()
$wb.Close($false)
$excel.ScreenUpdating = $true
$excel.Calculation = -4105  # xlCalculationAutomatic
$excel.Quit()
      `;
      execSync(`powershell -NoProfile -ExecutionPolicy Bypass -Command "${psWrite.replace(/\"/g, '\\\"')}"`, { stdio: 'inherit' });
      // Force-update the file timestamps so 'Date modified' reflects this run
      try { const nowTs = new Date(); fs.utimesSync(timestampedFilePath, nowTs, nowTs); } catch {}

      // Post-verify in Node as a backup: write F15 (if empty) and all qty cells via ExcelJS fallback.
      try {
        const wbVerify = new ExcelJS.Workbook();
        await wbVerify.xlsx.readFile(timestampedFilePath);
        const wsVerify = wbVerify.getWorksheet('Order Form') || wbVerify.worksheets[0];
        if (!wsVerify) {
          console.warn('[MVP] Node verify: no worksheet found for ExcelJS fallback');
        } else {
          let f15Val = wsVerify.getCell('F15').value;
          if (f15Val === null || f15Val === undefined || f15Val === '') {
            wsVerify.getCell('F15').value = '<EMAIL>';
          }
          let qtyCellsUpdated = 0;
          for (const rec of nonZero) {
            const rowNum = parseInt(rec.row, 10);
            const colNum = rec.col.split('').reduce((res, ch) => res * 26 + (ch.charCodeAt(0) - 64), 0);
            wsVerify.getCell(rowNum, colNum).value = rec.qty;
            qtyCellsUpdated++;
          }

          await wbVerify.xlsx.writeFile(timestampedFilePath);
          console.log('[MVP] Node verify fallback: wrote qty cells via ExcelJS', { qtyCellsUpdated });
        }
      } catch (ve) {
        console.warn('[MVP] Node verify failed (non-fatal):', ve.message);
      }

      // After ExcelJS fallback, reopen in Excel COM to force full recalc so dependent tabs (e.g., SalesOrder-Indexing/Import) populate
      try {
        const psRecalc = `
$excel = New-Object -ComObject Excel.Application
$excel.DisplayAlerts = $false
$excel.Visible = $false
$excel.EnableEvents = $true
$excel.Calculation = -4105  # xlCalculationAutomatic
$wb = $excel.Workbooks.Open('${timestampedFilePath.replace(/\\/g,"\\\\")}')
try { $wb.RefreshAll() } catch {}
try { $excel.CalculateFullRebuild() } catch {}
try { $excel.CalculateUntilAsyncQueriesDone() } catch {}
$wb.Save()
$wb.Close($false)
$excel.Quit()
        `;
        execSync(`powershell -NoProfile -ExecutionPolicy Bypass -Command "${psRecalc.replace(/\"/g, '\\\"')}"`, { stdio: 'inherit' });

        // Read-back values from dependent sheets after recalc (SalesOrder-Indexing & SalesOrder-Import)
        try {
          const psRead = `
$excel = New-Object -ComObject Excel.Application
$excel.DisplayAlerts = $false
$excel.Visible = $false
$wb = $excel.Workbooks.Open('${timestampedFilePath.replace(/\\/g,"\\\\")}')
$soi = $null; $sox = $null
try { $soi = $wb.Worksheets.Item('SalesOrder-Import') } catch {}
try { $sox = $wb.Worksheets.Item('SalesOrder-Indexing') } catch {}
$valImport = ''; $valIndex = ''
if ($soi -ne $null) { try { $valImport = $soi.Cells.Item(1,1).Text } catch {} }
if ($sox -ne $null) { try { $valIndex = $sox.Cells.Item(116,6).Text } catch {} }
Write-Output ("[MVP] Readback: SalesOrder-Import A1='{0}', SalesOrder-Indexing F116='{1}'" -f $valImport, $valIndex)
$wb.Close($false)
$excel.Quit()
          `;
          execSync(`powershell -NoProfile -ExecutionPolicy Bypass -Command "${psRead.replace(/\"/g, '\\\"')}"`, { stdio: 'inherit' });
        } catch (rb) {
          console.warn('[MVP] Readback via COM failed (non-fatal):', rb.message);
        }
      } catch (re) {
        console.warn('[MVP] COM recalc after ExcelJS fallback failed (non-fatal):', re.message);
      }
    } catch (e) {
      // If COM write fails, last resort: write values via ExcelJS
      wroteVia += '+exceljs';
      const wb2 = new ExcelJS.Workbook();
      await wb2.xlsx.readFile(timestampedFilePath);
      const ws2 = wb2.worksheets[0];
      if (ws2) {
        ws2.getCell('F15').value = '<EMAIL>';
        for (const rec of nonZero) {
          const rowNum = parseInt(rec.row, 10);
          const colNum = rec.col.split('').reduce((res, ch) => res * 26 + (ch.charCodeAt(0) - 64), 0);
          ws2.getCell(rowNum, colNum).value = rec.qty;
        }
        await wb2.xlsx.writeFile(timestampedFilePath);
      }
    } finally {
      try { fs.unlinkSync(updatesCsv); } catch {}
    }

    // Build summary numbers
    const { data: cfg, error: cfgErr } = await supabase.from('t_config').select('value').eq('key', 'mvp_disc_order_look_back_days').single();
    const lookbackDays = cfgErr || !cfg ? 60 : (parseInt(cfg.value) || 60);

    async function sumColumn(colName) {
      let sum = 0; for (let from = 0; ; from += page) { const to = from + page - 1; const { data, error } = await supabase.from('it_mvp_osl_map').select(colName).order('id', { ascending: true }).range(from, to); if (error) throw new Error(error.message); if (!data || data.length === 0) break; sum += data.reduce((s, r) => s + (r[colName] || 0), 0); if (data.length < page) break; } return sum;
    }
    const totalSoldLast90 = await sumColumn('sold_last_90');
    const inStockSum = await sumColumn('in_stock');

    // Attempt a LibreOffice UNO hard-recalc (CalculateHard) using scripts/lo_hard_recalc.py
    try {
      const pyCandidates = [];
      // 1) Try to derive LibreOffice python.exe from 'where soffice'
      try {
        const whereOut = execSync('where soffice', { stdio: 'pipe' }).toString();
        const lines = whereOut.split(/\r?\n/).map(s => s.trim()).filter(Boolean);
        for (const soff of lines) {
          const dir = path.dirname(soff);
          const py = path.join(dir, 'python.exe');
          if (fs.existsSync(py)) pyCandidates.push(py);
        }
      } catch {}
      // 2) Fallback to common install dirs
      const loDirs = [
        path.join('C:', 'Program Files', 'LibreOffice', 'program'),
        path.join('C:', 'Program Files (x86)', 'LibreOffice', 'program'),
        process.env['ProgramFiles'] ? path.join(process.env['ProgramFiles'], 'LibreOffice', 'program') : null,
        process.env['ProgramFiles(x86)'] ? path.join(process.env['ProgramFiles(x86)'], 'LibreOffice', 'program') : null,
      ].filter(Boolean);
      for (const d of loDirs) {
        const py = path.join(d, 'python.exe');
        if (fs.existsSync(py)) pyCandidates.push(py);
      }
      // 3) Lastly, try system python (will only work if pyuno is installed site-wide)
      pyCandidates.push('python');
      pyCandidates.push('python3');

      let ran = false, exitCode = -1;
      const scriptPath = path.join(process.cwd(), 'scripts', 'lo_hard_recalc.py');
      for (const py of pyCandidates) {
        try {
          const res = execSync(`"${py}" "${scriptPath}" "${timestampedFilePath}"`, { stdio: 'pipe' });
          console.log('[MVP] LO hard recalc output:', res.toString());
          ran = true; exitCode = 0; break;
        } catch (e) {
          exitCode = e.status ?? -1;
          const out = e.stdout ? e.stdout.toString() : '';
          const err = e.stderr ? e.stderr.toString() : '';
          console.warn(`[MVP] LO hard recalc try failed with ${py} (code ${exitCode}). stdout: ${out} stderr: ${err}`);
        }
      }
      if (!ran) {
        console.warn('[MVP] LibreOffice UNO hard recalc could not be executed');
      }
    } catch (loe) {
      console.warn('[MVP] LibreOffice UNO hard recalc failed (non-fatal):', loe.message);
    }

    // Only email if the file exists to avoid ENOENT
    let emailResult = { success: false, error: 'No file to email' };
    if (fs.existsSync(timestampedFilePath)) {
      emailResult = await emailMvpOrderSheet(timestampedFilePath, totalDiscsAdded, { lookbackDays, totalSoldLast90, qtyCount: nonZeroQtyCount, qtySum: totalDiscsAdded, inStockSum });
    } else {
      console.warn('[MVP] Skipping email; timestamped file missing:', timestampedFilePath);
    }

    return { success: true, message: `Updated ${updatedCells} cells and ${emailResult.success ? 'emailed file' : 'skipped email'}`, updatedCells, totalDiscsAdded, wroteVia, timestampedFilePath, emailResult };
  } catch (e) {
    return { success: false, error: e.message };
  }
}

// --- Main task processor ---
export async function processImportMvpOslMapAndStatusTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processImportMvpOslMapAndStatusTask] Processing task ${task.id} of type ${task.task_type}`);
  const start = new Date();
  try {
    await updateTaskStatus(task.id, 'processing');

    // Parse payload (object or JSON string)
    let payload;
    try {
      payload = typeof task.payload === 'object' && task.payload !== null ? task.payload : JSON.parse(task.payload || '{}');
    } catch (e) { throw new Error(`Failed to parse payload: ${e.message}`); }

    // Hardcoded MVP Google Sheet URL
    const googleSheetsUrl = 'https://docs.google.com/spreadsheets/d/1Jye3hwMIjiH_grovIKN6ydVK2BJYhnCswa29sLbMaQ8/edit?gid=330904296#gid=330904296';

    // Step 1: Map sheet values
    const mapData = await fetchSheetValues(googleSheetsUrl, 'Map');
    const osValues = findOSValues(mapData);
    if (osValues.length === 0) throw new Error('No OS values found on Map sheet');

    // Step 2: New sheet formats -> statuses
    const newFormats = await fetchSheetFormats(googleSheetsUrl, 'New');
    const osWithStatus = addStatusFromFormats(newFormats, osValues);

    // Step 3: Clear existing row/col/status on it_mvp_osl_map
    const { error: clearErr } = await supabase.from('it_mvp_osl_map').update({ row: null, col: null, status: null }).neq('id', 0);
    if (clearErr) throw new Error(`Failed to clear it_mvp_osl_map: ${clearErr.message}`);

    // Step 4: Upsert id,row,col,status
    const upsertData = osWithStatus.map(x => ({ id: x.id, row: x.row, col: x.col, status: x.status }));
    const { error: upsertErr } = await supabase.from('it_mvp_osl_map').upsert(upsertData, { onConflict: 'id' });
    if (upsertErr) throw new Error(`Upsert failed: ${upsertErr.message}`);

    // Step 5: Mark still-missing as Not Available (don't touch qty)
    const { error: markErr } = await supabase.from('it_mvp_osl_map').update({ status: 'Not Available' }).is('row', null).is('col', null);
    if (markErr) throw new Error(`Mark missing failed: ${markErr.message}`);

    // Step 6: Calculate counts for all IDs
    const { data: allIdsData, error: allIdsErr } = await supabase.from('it_mvp_osl_map').select('id');
    if (allIdsErr) throw new Error(`Fetch all IDs failed: ${allIdsErr.message}`);
    const allIds = (allIdsData || []).map(r => r.id);
    const counts = await calculateMvpDiscCounts(supabase, allIds);

    // Step 7: Update Excel and email
    const excelRes = await updateMvpExcelFile(supabase);

    const duration = new Date() - start;
    const result = {
      success: true,
      message: `Imported ${osWithStatus.length} MVP OSL map records, updated counts, and Excel` ,
      recordsProcessed: osWithStatus.length,
      duration: `${duration}ms`,
      summary: {
        totalRecords: osWithStatus.length,
        statusBreakdown: {
          'In Stock': osWithStatus.filter(i => i.status === 'In Stock').length,
          'Out of Stock': osWithStatus.filter(i => i.status === 'Out of Stock').length,
          'Not Available': osWithStatus.filter(i => i.status === 'Not Available').length
        },
        discCounts: counts,
        excelUpdate: excelRes
      }
    };

    await updateTaskStatus(task.id, 'completed', result);
  } catch (err) {
    const duration = new Date() - start;
    const errorResult = { success: false, error: err.message, duration: `${duration}ms` };
    console.error(`[processImportMvpOslMapAndStatusTask] Task failed: ${err.message}`);
    await logError(`Import MVP OSL map task failed: ${err.message}`, `Task ${task.id}`);
    await updateTaskStatus(task.id, 'error', errorResult);
  }
}

export default processImportMvpOslMapAndStatusTask;

