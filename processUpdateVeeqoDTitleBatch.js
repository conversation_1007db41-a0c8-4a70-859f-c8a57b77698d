/**
 * Batch processor for update_veeqo_d_title tasks
 * 
 * This processes multiple Veeqo title update tasks in parallel to improve throughput.
 * It respects Veeqo API rate limits while maximizing efficiency.
 */

import fetch from 'node-fetch';

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ VEEQO_API_KEY environment variable is not set');
}

// Function to make Veeqo API request with retry logic and tolerant parsing
async function makeVeeqoRequest(endpoint, method = 'GET', data = null, retries = 2) {
  for (let attempt = 1; attempt <= retries + 1; attempt++) {
    try {
      const options = {
        method,
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': veeqoApiKey
        }
      };

      if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        options.body = JSON.stringify(data);
      }

      const response = await fetch(endpoint, options);

      if (!response.ok) {
        const errorText = await response.text();

        // If it's a rate limit error (429), wait and retry
        if (response.status === 429 && attempt <= retries) {
          const waitTime = Math.pow(2, attempt) * 1000; // Exponential backoff
          console.log(`[processUpdateVeeqoDTitleBatch] Rate limited, waiting ${waitTime}ms before retry ${attempt}/${retries}`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
          continue;
        }

        return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
      }

      // Some endpoints return 204/empty body; treat as success
      const rawText = await response.text();
      if (!rawText || rawText.trim().length === 0) {
        return { success: true, data: null, status: response.status };
      }

      try {
        const json = JSON.parse(rawText);
        return { success: true, data: json, status: response.status };
      } catch (parseErr) {
        return { success: true, data: rawText, status: response.status };
      }
    } catch (error) {
      if (attempt <= retries) {
        const waitTime = Math.pow(2, attempt) * 1000;
        console.log(`[processUpdateVeeqoDTitleBatch] Request failed, retrying in ${waitTime}ms: ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        continue;
      }
      return { success: false, error: error.message };
    }
  }
}

// Function to search for Veeqo products by SKU
async function searchVeeqoProductBySku(sku) {
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products?query=${encodeURIComponent(sku)}`);
  
  if (!result.success) {
    return [];
  }
  
  if (!Array.isArray(result.data)) {
    return [];
  }
  
  // Filter results to find products with matching SKU in their sellables
  const matchingProducts = result.data.filter(product => {
    if (product.sellables && Array.isArray(product.sellables)) {
      return product.sellables.some(sellable => sellable.sku_code === sku);
    }
    return false;
  });
  
  return matchingProducts;
}

// Helpers for title handling
function normalizeTitle(t) {
  return (t ?? '').toString().replace(/\s+/g, ' ').trim();
}
function titlesEqual(a, b) {
  return normalizeTitle(a).toLowerCase() === normalizeTitle(b).toLowerCase();
}
function getSafeTitle(t) {
  return normalizeTitle(t).slice(0, 255);
}
function extractTitle(data) {
  if (!data) return null;
  if (typeof data === 'string') return data;
  if (data.product && typeof data.product.title === 'string') return data.product.title;
  if (typeof data.title === 'string') return data.title;
  if (typeof data.name === 'string') return data.name;
  return null;
}

// Function to update Veeqo product title with PUT->PATCH fallback and verification
async function updateVeeqoProductTitle(productId, newTitle) {
  const safeTitle = getSafeTitle(newTitle);
  const url = `https://api.veeqo.com/products/${productId}`;

  // Try PUT first
  let result = await makeVeeqoRequest(url, 'PUT', { product: { title: safeTitle } });

  if (!result.success && result.status === 400) {
    // Try PATCH fallback
    console.warn(`[processUpdateVeeqoDTitleBatch] PUT returned 400 for product ${productId}; trying PATCH`);
    result = await makeVeeqoRequest(url, 'PATCH', { product: { title: safeTitle } });
  }

  if (result.success) {
    return result.data;
  }

  // Verification: fetch product and compare current title
  console.warn(`[processUpdateVeeqoDTitleBatch] Update call failed (status=${result.status || 'n/a'}). Verifying current title for product ${productId}...`);
  try {
    const verify = await makeVeeqoRequest(url, 'GET');
    if (verify.success) {
      const currentTitle = extractTitle(verify.data);
      if (titlesEqual(currentTitle, safeTitle)) {
        console.log(`[processUpdateVeeqoDTitleBatch] Verification shows title already set correctly for product ${productId}`);
        return verify.data;
      }
      console.warn(`[processUpdateVeeqoDTitleBatch] Verification title mismatch for product ${productId}. current="${currentTitle}", desired="${safeTitle}"`);
    } else {
      console.warn(`[processUpdateVeeqoDTitleBatch] Verification GET failed for product ${productId}: ${verify.error}`);
    }
  } catch (vErr) {
    console.warn(`[processUpdateVeeqoDTitleBatch] Verification exception for product ${productId}: ${vErr.message}`);
  }

  throw new Error(`Failed to update Veeqo product title: ${result.error || 'Unknown error'}`);
}

// Function to process a single task
async function processSingleTask(task, supabase, updateTaskStatus) {
  const payload = task.payload || {};
  const discId = payload.id;

  try {
    // Get the disc record (including current g_pull)
    const { data: discRecord, error: discError } = await supabase
      .from('t_discs')
      .select('id, shopify_sku, shopify_uploaded_at, sold_date, g_pull')
      .eq('id', discId)
      .single();

    if (discError || !discRecord) {
      throw new Error(`Failed to get disc record: ${discError?.message || 'Disc not found'}`);
    }

    const newTitle = discRecord.g_pull;
    if (!newTitle || normalizeTitle(newTitle) === '') {
      throw new Error('New title (g_pull) is empty or null');
    }

    // Verify the disc is still eligible
    if (discRecord.sold_date !== null) {
      return {
        taskId: task.id,
        success: true,
        message: `Disc is now sold, skipping title update`,
        discId
      };
    }

    if (!discRecord.shopify_uploaded_at || !discRecord.shopify_sku) {
      return {
        taskId: task.id,
        success: true,
        message: `Disc not uploaded to Shopify or missing SKU, skipping title update`,
        discId
      };
    }

    const sku = discRecord.shopify_sku;

    // Search for the Veeqo product by SKU
    const veeqoProducts = await searchVeeqoProductBySku(sku);

    if (!veeqoProducts || veeqoProducts.length === 0) {
      // Implement retry scheduling similar to single-task processor
      const attempt = typeof payload.retry_count === 'number' ? payload.retry_count : 0;
      const delays = [2, 12, 48]; // hours
      if (attempt < delays.length) {
        const nextDelay = delays[attempt];
        const nextAttempt = attempt + 1;
        const scheduledAt = new Date(Date.now() + nextDelay * 60 * 60 * 1000).toISOString();
        const { error: enqueueError } = await supabase
          .from('t_task_queue')
          .insert([{
            task_type: 'update_veeqo_d_title',
            payload: { id: discId, retry_count: nextAttempt },
            status: 'pending',
            scheduled_at: scheduledAt,
            created_at: new Date().toISOString(),
            enqueued_by: `update_veeqo_d_title retry attempt ${nextAttempt} (batch)`
          }]);
        if (enqueueError) {
          return {
            taskId: task.id,
            success: false,
            error: `No Veeqo product found and failed to enqueue retry: ${enqueueError.message}`,
            discId,
            sku,
            attempt,
            nextAttempt
          };
        }
        return {
          taskId: task.id,
          success: true,
          message: `No Veeqo product found; scheduled retry attempt ${nextAttempt} in ${nextDelay} hour(s)`,
          discId,
          sku,
          attempt,
          nextAttempt,
          nextDelayHours: nextDelay
        };
      }

      return {
        taskId: task.id,
        success: false,
        error: `No Veeqo product found after final retry attempt`,
        discId,
        sku,
        attempt
      };
    }

    // Update title for all Veeqo products with this SKU
    let successCount = 0;
    let failureCount = 0;
    let errors = [];
    const updatedProductIds = [];

    for (const product of veeqoProducts) {
      try {
        // Skip update if already matches
        if (typeof product.title === 'string' && titlesEqual(product.title, newTitle)) {
          successCount++;
          updatedProductIds.push(product.id);
          continue;
        }

        await updateVeeqoProductTitle(product.id, newTitle);
        successCount++;
        updatedProductIds.push(product.id);
      } catch (error) {
        // Verify current title; if it already matches, treat as success
        try {
          const verifyResp = await makeVeeqoRequest(`https://api.veeqo.com/products/${product.id}`, 'GET');
          const currentTitle = extractTitle(verifyResp.data);
          if (titlesEqual(currentTitle, newTitle)) {
            successCount++;
            updatedProductIds.push(product.id);
            continue;
          }
        } catch (verifyErr) {
          // ignore, we'll count as failure
        }
        failureCount++;
        errors.push(`Product ${product.id}: ${error.message}`);
      }
    }

    if (successCount > 0) {
      return {
        taskId: task.id,
        success: true,
        message: failureCount === 0 ? `Successfully updated title for ${successCount} Veeqo product(s)` : `Partially successful: ${successCount} updated, ${failureCount} failed`,
        discId,
        sku,
        newTitle,
        veeqoProductIds: updatedProductIds,
        veeqoProductsUpdated: successCount,
        veeqoProductsFailed: failureCount,
        errors: errors.length > 0 ? errors : undefined
      };
    } else {
      throw new Error(`Failed to update any Veeqo products. Errors: ${errors.join('; ')}`);
    }

  } catch (error) {
    return {
      taskId: task.id,
      success: false,
      error: error.message,
      discId,
      newTitle: undefined
    };
  }
}

// Main batch processor function
export async function processUpdateVeeqoDTitleBatch(tasks, { supabase, updateTaskStatus, logError }) {
  console.log(`[processUpdateVeeqoDTitleBatch] Processing batch of ${tasks.length} update_veeqo_d_title tasks`);
  
  // Update all tasks to 'processing' status
  const taskIds = tasks.map(task => task.id);
  await Promise.all(taskIds.map(taskId => updateTaskStatus(taskId, 'processing')));
  
  // Process tasks with controlled concurrency to respect API limits
  const CONCURRENT_LIMIT = 5; // Process 5 tasks at a time
  const results = [];
  
  for (let i = 0; i < tasks.length; i += CONCURRENT_LIMIT) {
    const batch = tasks.slice(i, i + CONCURRENT_LIMIT);
    console.log(`[processUpdateVeeqoDTitleBatch] Processing batch ${Math.floor(i / CONCURRENT_LIMIT) + 1}/${Math.ceil(tasks.length / CONCURRENT_LIMIT)} (${batch.length} tasks)`);
    
    // Process this batch concurrently
    const batchPromises = batch.map(task => processSingleTask(task, supabase, updateTaskStatus));
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
    
    // Small delay between batches to be respectful to the API
    if (i + CONCURRENT_LIMIT < tasks.length) {
      await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay
    }
  }
  
  // Update task statuses based on results
  let successCount = 0;
  let errorCount = 0;
  
  for (const result of results) {
    try {
      if (result.success) {
        await updateTaskStatus(result.taskId, 'completed', result);
        successCount++;
      } else {
        await updateTaskStatus(result.taskId, 'error', {
          message: "Failed to update Veeqo product title",
          error: result.error,
          discId: result.discId,
          newTitle: result.newTitle
        });
        errorCount++;
      }
    } catch (updateError) {
      console.error(`[processUpdateVeeqoDTitleBatch] Failed to update task ${result.taskId} status: ${updateError.message}`);
      errorCount++;
    }
  }
  
  console.log(`[processUpdateVeeqoDTitleBatch] Batch completed: ${successCount} successful, ${errorCount} errors`);
  
  return {
    processed: tasks.length,
    successful: successCount,
    errors: errorCount
  };
}

export default processUpdateVeeqoDTitleBatch;
