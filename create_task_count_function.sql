-- Create a function to get task counts by status
CREATE OR REPLACE FUNCTION get_task_counts_by_status(
    task_type_param TEXT
)
RETURNS TABLE (
    status TEXT,
    count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.status,
        COUNT(*)::BIGINT
    FROM 
        t_task_queue t
    WHERE 
        t.task_type = task_type_param
    GROUP BY 
        t.status;
END;
$$ LANGUAGE plpgsql;
