// updateShopifyPrices.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// For Node 18+ the global fetch is available.
// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error("ERROR: Missing Supabase URL or key in environment variables.");
  process.exit(1);
}
console.log("INFO: Initializing Supabase client...");
const supabase = createClient(supabaseUrl, supabaseKey);

// Shopify Configuration
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
if (!shopifyEndpoint || !shopifyAccessToken) {
  console.error("ERROR: Missing Shopify endpoint or access token.");
  process.exit(1);
}
const productsEndpoint = shopifyEndpoint.replace("graphql.json", "products.json");
const variantsEndpoint = shopifyEndpoint.replace("graphql.json", "variants/");
console.log(`INFO: Shopify products endpoint: ${productsEndpoint}`);

// Helper function for logging errors
async function logError(errorMessage, context) {
  try {
    const { error } = await supabase
      .from('t_error_logs')
      .insert({ error_message: errorMessage, context });
    if (error) {
      console.error('Failed to log error to t_error_logs:', error);
    }
  } catch (err) {
    console.error('Exception while logging error:', err);
  }
}

// Helper function for Shopify GraphQL API requests
async function shopifyGraphQLRequest(query, variables = {}) {
  try {
    const response = await fetch(shopifyEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": shopifyAccessToken
      },
      body: JSON.stringify({
        query,
        variables
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Shopify GraphQL API error (${response.status}): ${errorText}`);
    }

    const result = await response.json();
    
    if (result.errors) {
      throw new Error(`Shopify GraphQL API returned errors: ${JSON.stringify(result.errors)}`);
    }
    
    return result.data;
  } catch (error) {
    console.error(`ERROR: GraphQL request failed: ${error.message}`);
    throw error;
  }
}

// Helper function to find Shopify variant by SKU using GraphQL
async function findVariantBySku(sku) {
  try {
    console.log(`INFO: Searching for variant with SKU: ${sku}`);
    
    // GraphQL query to find a variant by SKU
    const query = `
      query getVariantBySku($query: String!) {
        productVariants(first: 1, query: $query) {
          edges {
            node {
              id
              sku
              product {
                id
                title
              }
            }
          }
        }
      }
    `;
    
    const variables = {
      query: `sku:${sku}`
    };
    
    const data = await shopifyGraphQLRequest(query, variables);
    
    if (!data.productVariants.edges.length) {
      console.log(`INFO: No variant found with SKU: ${sku}`);
      return null;
    }
    
    const variant = data.productVariants.edges[0].node;
    
    // Convert GraphQL IDs to REST API IDs
    const variantId = variant.id.split('/').pop();
    const productId = variant.product.id.split('/').pop();
    
    const variantData = {
      variant_id: variantId,
      product_id: productId
    };
    
    console.log(`INFO: Found variant with ID: ${variantData.variant_id} for SKU: ${sku}`);
    return variantData;
  } catch (error) {
    console.error(`ERROR: Failed to find variant by SKU: ${error.message}`);
    await logError(`Failed to find variant by SKU: ${error.message}`, `SKU: ${sku}`);
    return null;
  }
}

// Function to update variant price on Shopify
async function updateVariantPrice(variantId, retail, msrp) {
  try {
    console.log(`INFO: Updating variant ${variantId} with retail: ${retail}, msrp: ${msrp}`);
    
    const updateEndpoint = `${variantsEndpoint}${variantId}.json`;
    
    const payload = {
      variant: {
        id: variantId,
        price: retail.toString()
      }
    };
    
    // Add compare_at_price only if msrp is provided and different from retail
    if (msrp && msrp !== retail) {
      payload.variant.compare_at_price = msrp.toString();
    }
    
    console.log(`INFO: Sending update to Shopify: ${JSON.stringify(payload)}`);
    
    const response = await fetch(updateEndpoint, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": shopifyAccessToken
      },
      body: JSON.stringify(payload)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Shopify API error (${response.status}): ${errorText}`);
    }
    
    const result = await response.json();
    console.log(`INFO: Successfully updated variant: ${JSON.stringify(result.variant)}`);
    return result.variant;
  } catch (error) {
    console.error(`ERROR: Failed to update variant ${variantId}: ${error.message}`);
    await logError(`Failed to update variant ${variantId}: ${error.message}`, `Variant ID: ${variantId}`);
    return null;
  }
}

// Main function to process price updates
export async function updatePrices(discsData) {
  console.log(`INFO: Processing price updates for ${discsData.length} discs`);
  
  const results = {
    success: [],
    failed: []
  };
  
  for (const disc of discsData) {
    try {
      console.log(`INFO: Processing SKU: ${disc.sku}`);
      
      // Find the variant in Shopify
      const variantData = await findVariantBySku(disc.sku);
      
      if (!variantData) {
        console.log(`WARNING: No variant found for SKU: ${disc.sku}`);
        results.failed.push({
          sku: disc.sku,
          reason: "Variant not found"
        });
        continue;
      }
      
      // Update the variant price
      const updatedVariant = await updateVariantPrice(
        variantData.variant_id,
        disc.retail,
        disc.msrp
      );
      
      if (updatedVariant) {
        results.success.push({
          sku: disc.sku,
          variantId: variantData.variant_id,
          newPrice: disc.retail,
          newCompareAtPrice: disc.msrp
        });
      } else {
        results.failed.push({
          sku: disc.sku,
          reason: "Failed to update variant"
        });
      }
    } catch (error) {
      console.error(`ERROR: Failed to process SKU ${disc.sku}: ${error.message}`);
      await logError(`Failed to process SKU ${disc.sku}: ${error.message}`, `SKU: ${disc.sku}`);
      
      results.failed.push({
        sku: disc.sku,
        reason: error.message
      });
    }
  }
  
  console.log(`INFO: Price update complete. Success: ${results.success.length}, Failed: ${results.failed.length}`);
  return results;
}

// If this script is run directly (not imported)
if (import.meta.url === import.meta.main) {
  // Check if we have command line arguments
  const args = process.argv.slice(2);
  if (args.length > 0) {
    try {
      const discsData = JSON.parse(args[0]);
      await updatePrices(discsData);
    } catch (error) {
      console.error(`ERROR: Failed to parse command line JSON: ${error.message}`);
      process.exit(1);
    }
  } else {
    console.error("ERROR: No disc data provided. Please provide JSON data as a command line argument.");
    process.exit(1);
  }
}

