-- Enqueue variant readiness checks when a product's todo changes
CREATE OR REPLACE FUNCTION public.enqueue_variant_ready_on_product_todo_change()
RETURNS TRIGGER AS $$
BEGIN
  IF (OLD.todo IS DISTINCT FROM NEW.todo) THEN
    -- Insert one task per variant, skipping duplicates already pending/processing
    INSERT INTO public.t_task_queue (task_type, payload, status, scheduled_at, created_at, enqueued_by)
    SELECT
      'check_if_product_variant_is_ready' AS task_type,
      jsonb_build_object('id', pv.id) AS payload,
      'pending' AS status,
      NOW() AS scheduled_at,
      NOW() AS created_at,
      'trigger:t_products.todo_changed' AS enqueued_by
    FROM public.t_product_variants pv
    WHERE pv.product_id = NEW.id
      AND NOT EXISTS (
        SELECT 1 FROM public.t_task_queue tq
        WHERE tq.task_type = 'check_if_product_variant_is_ready'
          AND (tq.status = 'pending' OR tq.status = 'processing')
          AND (tq.payload->>'id')::INT = pv.id
      );
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop and recreate trigger
DROP TRIGGER IF EXISTS trg_t_products_todo_enqueue_variant_ready ON public.t_products;
CREATE TRIGGER trg_t_products_todo_enqueue_variant_ready
AFTER UPDATE OF todo ON public.t_products
FOR EACH ROW
WHEN (OLD.todo IS DISTINCT FROM NEW.todo)
EXECUTE FUNCTION public.enqueue_variant_ready_on_product_todo_change();

