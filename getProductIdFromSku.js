// getProductIdFromSku.js

// Option 1: Use the built-in fetch (Node.js 18+)
// Option 2: If you need node-fetch, uncomment the following line and install node-fetch:
// import fetch from 'node-fetch';

async function getProductIdFromSku(sku) {
  // The API expects the SKU value to be a string, so we include quotes.
  // URL-encode the quoted SKU.
  const encodedSku = encodeURIComponent(`"${sku}"`);
  
  // Construct the endpoint URL with the SKU filter.
  const url = `https://api.veeqo.com/sellables?filters%5Bsku_code%5D%5B%5D=${encodedSku}`;
  
  const options = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': 'Vqt/16c3acd642be598d3ca079590a8aae87'
    }
  };
  
  try {
    const response = await fetch(url, options);
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    const data = await response.json();
    
    // Log the full data if needed:
    // console.log('Full Response:', JSON.stringify(data, null, 2));
    
    if (Array.isArray(data) && data.length > 0) {
      // Assuming the SKU uniquely identifies one sellable.
      const sellable = data[0];
      if (sellable.product && sellable.product.id) {
        return sellable.product.id;
      } else {
        throw new Error('Product object or product id not found in sellable');
      }
    } else {
      throw new Error(`No sellable found for sku ${sku}`);
    }
  } catch (error) {
    console.error('Error in getProductIdFromSku:', error);
  }
}

// Call the function with your SKU and print the product id.
getProductIdFromSku('Disc_52441')
  .then(productId => {
    if (productId) {
      console.log('Product ID:', productId);
    }
  });
