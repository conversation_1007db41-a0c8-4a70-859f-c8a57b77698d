// check_shopify_product.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

// Shopify credentials
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

async function shopifyGraphQLRequest(query, variables = {}) {
  const response = await fetch(shopifyEndpoint, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "X-Shopify-Access-Token": shopifyAccessToken
    },
    body: JSON.stringify({ query, variables })
  });

  const body = await response.json();

  if (!response.ok || body.errors) {
    throw new Error(`GraphQL error: ${JSON.stringify(body.errors || body.data)}`);
  }
  return body.data;
}

async function getProductDetailsById(productId) {
  const productsEndpoint = shopifyEndpoint.replace("graphql.json", "products.json");
  const productBase = productsEndpoint.split('/products.json')[0];
  const singleProductUrl = `${productBase}/products/${productId}.json`;
  
  console.log(`Fetching product details from: ${singleProductUrl}`);
  
  const response = await fetch(singleProductUrl, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "X-Shopify-Access-Token": shopifyAccessToken
    }
  });

  const result = await response.json();
  console.log("Get product details response status:", response.status);

  if (!response.ok) {
    throw new Error(`Error fetching product ${productId}: ${JSON.stringify(result)}`);
  }

  return result.product;
}

async function main() {
  try {
    // Get OSL details first
    const { data: oslData, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('*')
      .eq('id', 18531)
      .single();
    
    if (oslError) {
      console.error('OSL Error:', oslError);
      return;
    }
    
    // Get MPS details
    const { data: mpsData, error: mpsError } = await supabase
      .from('t_mps')
      .select('*, t_molds!inner(mold, brand_id, t_brands!inner(brand)), t_plastics!inner(plastic), t_stamps!inner(stamp)')
      .eq('id', oslData.mps_id)
      .single();
    
    if (mpsError) {
      console.error('MPS Error:', mpsError);
      return;
    }
    
    // Generate the handle
    function generateMPSHandle(brand, plastic, mold, stamp) {
      let base = `${brand}-${plastic}-${mold}-${stamp}`.toLowerCase();
      base = base
        .replace(/ /g, '-')
        .replace(/'/g, '')
        .replace(/\//g, '')
        .replace(/\./g, '-')
        .replace(/&/g, '-')
        .replace(/\(/g, '')
        .replace(/\)/g, '')
        .replace(/"/g, '')
        .replace(/%/g, '')
        .replace(/#/g, '')
        .replace(/-\$/g, '');
      while (base.includes('--')) {
        base = base.replace(/--/g, '-');
      }
      return base;
    }
    
    const handle = generateMPSHandle(
      mpsData.t_molds.t_brands.brand,
      mpsData.t_plastics.plastic,
      mpsData.t_molds.mold,
      mpsData.t_stamps.stamp
    );
    
    console.log('Generated Handle:', handle);
    
    // Check if product exists by handle
    const query = `
      query getProductByHandle($handle: String!) {
        productByHandle(handle: $handle) {
          id
          handle
          title
        }
      }
    `;
    
    const data = await shopifyGraphQLRequest(query, { handle });
    
    if (data.productByHandle) {
      console.log('Found existing product:', data.productByHandle);
      
      // Get numeric ID
      const match = data.productByHandle.id.match(/Product\/(\d+)/);
      const productId = match ? match[1] : null;
      
      if (productId) {
        console.log('Numeric Product ID:', productId);
        
        // Get detailed product info
        const productDetails = await getProductDetailsById(productId);
        
        console.log('\nProduct Details:');
        console.log('Title:', productDetails.title);
        console.log('Handle:', productDetails.handle);
        console.log('Options:', JSON.stringify(productDetails.options, null, 2));
        console.log('Number of variants:', productDetails.variants.length);
        
        console.log('\nFirst few variants:');
        productDetails.variants.slice(0, 3).forEach((variant, index) => {
          console.log(`Variant ${index + 1}:`, {
            id: variant.id,
            sku: variant.sku,
            option1: variant.option1,
            option2: variant.option2,
            option3: variant.option3
          });
        });
      }
    } else {
      console.log('No existing product found with handle:', handle);
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

main();
