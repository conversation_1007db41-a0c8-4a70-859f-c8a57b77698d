const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const fs = require('fs');
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('Error: VEEQO_API_KEY must be set in .env file');
  process.exit(1);
}

console.log('🗂️  VEEQO UNARCHIVE MANAGER');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to unarchive a single product
async function unarchiveProduct(productId) {
  console.log(`\n🔄 Unarchiving product ${productId}...`);
  
  // Get product details
  const productResult = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!productResult.success) {
    console.error(`❌ Failed to get product: ${productResult.error}`);
    return { success: false, error: productResult.error };
  }
  
  const product = productResult.data;
  console.log(`📋 Product: "${product.title}"`);
  
  if (!product.channel_products || product.channel_products.length === 0) {
    console.log(`⚠️  Product has no channel products`);
    return { success: false, error: 'No channel products' };
  }
  
  const pulledChannels = product.channel_products.filter(cp => cp.status === 'pulled');
  
  if (pulledChannels.length === 0) {
    console.log(`✅ Product is already active`);
    return { success: true, message: 'Already active' };
  }
  
  console.log(`📤 Found ${pulledChannels.length} pulled channel(s), attempting to reactivate...`);
  
  let successCount = 0;
  const results = [];
  
  for (const channelProduct of pulledChannels) {
    console.log(`🔄 Reactivating channel product ${channelProduct.id} on ${channelProduct.channel?.short_name || 'Unknown'}...`);
    
    // Try different possible endpoints and methods for updating channel product status
    const updateAttempts = [
      {
        endpoint: `https://api.veeqo.com/channel_products/${channelProduct.id}`,
        method: 'PUT',
        data: { channel_product: { status: 'active' } }
      },
      {
        endpoint: `https://api.veeqo.com/channel_products/${channelProduct.id}`,
        method: 'PATCH',
        data: { channel_product: { status: 'active' } }
      },
      {
        endpoint: `https://api.veeqo.com/channel_products/${channelProduct.id}`,
        method: 'PUT',
        data: { status: 'active' }
      },
      {
        endpoint: `https://api.veeqo.com/channel_products/${channelProduct.id}`,
        method: 'PATCH',
        data: { status: 'active' }
      },
      {
        endpoint: `https://api.veeqo.com/products/${productId}/channel_products/${channelProduct.id}`,
        method: 'PUT',
        data: { channel_product: { status: 'active' } }
      }
    ];
    
    let updated = false;
    let lastError = '';
    
    for (const attempt of updateAttempts) {
      console.log(`   Trying ${attempt.method} ${attempt.endpoint}...`);
      const result = await makeVeeqoRequest(attempt.endpoint, attempt.method, attempt.data);
      
      if (result.success) {
        console.log(`   ✅ Successfully updated channel product!`);
        updated = true;
        successCount++;
        results.push({ channelProductId: channelProduct.id, success: true });
        break;
      } else {
        console.log(`   ❌ Failed: ${result.error}`);
        lastError = result.error;
      }
    }
    
    if (!updated) {
      console.log(`   ⚠️  Could not update channel product ${channelProduct.id}`);
      results.push({ channelProductId: channelProduct.id, success: false, error: lastError });
    }
  }
  
  if (successCount > 0) {
    console.log(`✅ Successfully reactivated ${successCount}/${pulledChannels.length} channel products`);
    return { success: true, reactivated: successCount, total: pulledChannels.length, results };
  } else {
    console.log(`❌ Failed to reactivate any channel products`);
    return { success: false, error: 'No channel products could be reactivated', results };
  }
}

// Function to unarchive multiple products
async function unarchiveMultipleProducts(productIds, maxConcurrent = 3) {
  console.log(`\n🔄 Unarchiving ${productIds.length} products (max ${maxConcurrent} concurrent)...`);
  
  const results = [];
  const chunks = [];
  
  // Split into chunks for concurrent processing
  for (let i = 0; i < productIds.length; i += maxConcurrent) {
    chunks.push(productIds.slice(i, i + maxConcurrent));
  }
  
  for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
    const chunk = chunks[chunkIndex];
    console.log(`\n📦 Processing chunk ${chunkIndex + 1}/${chunks.length} (${chunk.length} products)...`);
    
    const chunkPromises = chunk.map(productId => 
      unarchiveProduct(productId).then(result => ({ productId, ...result }))
    );
    
    const chunkResults = await Promise.all(chunkPromises);
    results.push(...chunkResults);
    
    // Add delay between chunks to be respectful to the API
    if (chunkIndex < chunks.length - 1) {
      console.log('⏳ Waiting 2 seconds before next chunk...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  // Summary
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`\n📊 UNARCHIVE SUMMARY:`);
  console.log(`✅ Successfully unarchived: ${successful}`);
  console.log(`❌ Failed to unarchive: ${failed}`);
  console.log(`📋 Total processed: ${results.length}`);
  
  // Save detailed results
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const resultsFile = `unarchive_results_${timestamp}.json`;
  fs.writeFileSync(resultsFile, JSON.stringify(results, null, 2));
  console.log(`💾 Detailed results saved to: ${resultsFile}`);
  
  return results;
}

// Function to load archived products from JSON file
function loadArchivedProducts(filename) {
  try {
    if (!fs.existsSync(filename)) {
      console.error(`❌ File not found: ${filename}`);
      return null;
    }
    
    const data = fs.readFileSync(filename, 'utf8');
    const products = JSON.parse(data);
    
    console.log(`📂 Loaded ${products.length} archived products from ${filename}`);
    return products;
  } catch (error) {
    console.error(`❌ Error loading file: ${error.message}`);
    return null;
  }
}

// Main interactive function
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🛠️  USAGE:
  node veeqoUnarchiveManager.cjs <command> [options]

📋 COMMANDS:
  single <product_id>              - Unarchive a single product
  multiple <product_id1,id2,id3>   - Unarchive multiple products (comma-separated)
  from-file <json_file>            - Unarchive products from JSON file
  from-file-top <json_file> <N>    - Unarchive top N products from JSON file

📝 EXAMPLES:
  node veeqoUnarchiveManager.cjs single 211445804
  node veeqoUnarchiveManager.cjs multiple 211445804,211445803,211445802
  node veeqoUnarchiveManager.cjs from-file archived_products_with_stock_2025-07-19T02-25-21-183Z.json
  node veeqoUnarchiveManager.cjs from-file-top archived_products_with_stock_2025-07-19T02-25-21-183Z.json 10
`);
    return;
  }
  
  const command = args[0];
  
  try {
    switch (command) {
      case 'single':
        if (args.length < 2) {
          console.error('❌ Please provide a product ID');
          return;
        }
        const productId = parseInt(args[1]);
        await unarchiveProduct(productId);
        break;
        
      case 'multiple':
        if (args.length < 2) {
          console.error('❌ Please provide comma-separated product IDs');
          return;
        }
        const productIds = args[1].split(',').map(id => parseInt(id.trim()));
        await unarchiveMultipleProducts(productIds);
        break;
        
      case 'from-file':
        if (args.length < 2) {
          console.error('❌ Please provide a JSON file path');
          return;
        }
        const products = loadArchivedProducts(args[1]);
        if (products) {
          const productIds = products.map(p => p.id);
          await unarchiveMultipleProducts(productIds);
        }
        break;
        
      case 'from-file-top':
        if (args.length < 3) {
          console.error('❌ Please provide a JSON file path and number of products');
          return;
        }
        const allProducts = loadArchivedProducts(args[1]);
        const topN = parseInt(args[2]);
        if (allProducts) {
          const topProducts = allProducts.slice(0, topN);
          const topProductIds = topProducts.map(p => p.id);
          console.log(`🔝 Unarchiving top ${topN} products...`);
          await unarchiveMultipleProducts(topProductIds);
        }
        break;
        
      default:
        console.error(`❌ Unknown command: ${command}`);
        console.log('Use no arguments to see usage instructions.');
    }
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
  }
}

// Run the main function
main();
