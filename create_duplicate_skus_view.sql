-- Drop the view if it exists
DROP VIEW IF EXISTS v_duplicate_veeqo_skus;

-- Create a view to find duplicate SKU codes in imported_table_veeqo_sellables_export
CREATE VIEW v_duplicate_veeqo_skus AS
SELECT
    sku_code,
    COUNT(*) AS occurrence_count
FROM
    imported_table_veeqo_sellables_export
GROUP BY
    sku_code
HAVING
    COUNT(*) > 1
ORDER BY
    COUNT(*) DESC,
    sku_code;

-- Comment explaining the view
COMMENT ON VIEW v_duplicate_veeqo_skus IS 'Identifies duplicate SKU codes in the imported_table_veeqo_sellables_export table, showing the count of occurrences.';
