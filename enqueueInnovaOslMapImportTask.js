// enqueueInnovaOslMapImportTask.js - Enqueue import_innova_osl_map_and_status task
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function enqueueInnovaOslMapImportTask() {
  console.log('📋 Enqueueing Innova OSL Map/Status Task...');
  const taskData = {
    task_type: 'import_innova_osl_map_and_status',
    payload: {},
    status: 'pending',
    scheduled_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    enqueued_by: 'manual_enqueue_script_innova'
  };

  const { data, error } = await supabase
    .from('t_task_queue')
    .insert([taskData])
    .select()
    .single();

  if (error) throw error;
  console.log('✅ Enqueued:', data);
  return data;
}

enqueueInnovaOslMapImportTask()
  .then(() => process.exit(0))
  .catch(err => { console.error(err); process.exit(1); });

