// setupPm2Direct.js - <PERSON>ript to set up PM2 directly without ecosystem file

import { exec } from 'child_process';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configuration
const dbfFilePath = process.env.DBF_FILE_PATH || 'R:\\Rpro\\BRIDGE\\invdb.dbf';
const targetTable = process.env.TARGET_TABLE || 'imported_table_rpro';
const truncateBeforeImport = process.env.TRUNCATE_BEFORE_IMPORT === 'true' || true;

// Get the path to the import script
const scriptPath = './batchImport.js';

// Create PM2 scheduled task
const setupPm2 = () => {
  // Check if PM2 is installed
  exec('pm2 --version', (error) => {
    if (error) {
      console.error('PM2 is not installed. Please install it with: npm install -g pm2');
      return;
    }
    
    // Create the PM2 command
    const pm2Command = `pm2 start "${scriptPath}" --name "rpro-import" --cron "15 6 * * *" -- "${dbfFilePath}" "${targetTable}" ${truncateBeforeImport}`;
    
    console.log(`Setting up PM2 scheduled task...`);
    console.log(`Command: ${pm2Command}`);
    
    // Execute the PM2 command
    exec(pm2Command, (err, stdout) => {
      if (err) {
        console.error(`Error starting PM2 process: ${err.message}`);
        return;
      }
      
      console.log('PM2 scheduled task created successfully!');
      console.log(stdout);
      
      // Save the PM2 configuration
      exec('pm2 save', (saveErr, saveStdout) => {
        if (saveErr) {
          console.error(`Error saving PM2 configuration: ${saveErr.message}`);
          return;
        }
        
        console.log('PM2 configuration saved:');
        console.log(saveStdout);
        
        // Display the PM2 list
        exec('pm2 list', (listErr, listStdout) => {
          if (!listErr) {
            console.log('PM2 process list:');
            console.log(listStdout);
          }
        });
      });
    });
  });
};

// Run the setup
setupPm2();
