import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

/*
  Summarize recent t_error_logs entries from the latest accessories import.
  Strategy:
  - Look back 30 minutes by default (override with --minutes <n>)
  - Fetch up to 500 recent rows for safety
  - Group by error_message, count, and show up to 5 example contexts per type
*/

dotenv.config();

const minutesIdx = process.argv.indexOf('--minutes');
const lookbackMinutes = minutesIdx > -1 ? Number(process.argv[minutesIdx + 1]) : 30;
const sinceIso = new Date(Date.now() - lookbackMinutes * 60 * 1000).toISOString();

async function main() {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_KEY;
  if (!supabaseUrl || !supabaseKey) {
    console.error('Missing SUPABASE_URL or SUPABASE_KEY');
    process.exit(1);
  }
  const supabase = createClient(supabaseUrl, supabaseKey);

  // Page through results in batches of 1000
  const batchSize = 1000;
  let all = [];
  for (let page = 0; page < 20; page++) {
    const from = page * batchSize;
    const to = from + batchSize - 1;
    const { data, error } = await supabase
      .from('t_error_logs')
      .select('id, created_at, error_message, context')
      .gt('created_at', sinceIso)
      .order('created_at', { ascending: false })
      .range(from, to);
    if (error) {
      console.error('Query failed:', error.message);
      process.exit(1);
    }
    if (!data || data.length === 0) break;
    all = all.concat(data);
    if (data.length < batchSize) break;
  }

  if (all.length === 0) {
    console.log(`No error logs found in the last ${lookbackMinutes} minutes.`);
    return;
  }

  // Group and summarize
  const groups = new Map();
  for (const row of all) {
    const key = row.error_message || 'UNKNOWN';
    if (!groups.has(key)) groups.set(key, []);
    groups.get(key).push(row);
  }

  console.log(`Found ${all.length} error log rows in the last ${lookbackMinutes} minutes.`);
  for (const [msg, rows] of groups.entries()) {
    console.log(`\n- ${msg}: ${rows.length}`);
    const examples = rows.slice(0, 5);
    for (const ex of examples) {
      // Pull notable fields from context if available
      const ctx = ex.context || {};
      const line = [
        ctx.AccessoryID !== undefined ? `AccessoryID=${ctx.AccessoryID}` : null,
        ctx.CategoryID !== undefined ? `CategoryID=${ctx.CategoryID}` : null,
        ctx.Brand !== undefined ? `Brand=${ctx.Brand}` : null,
        ctx.name !== undefined ? `name=${ctx.name}` : null,
        ctx.category_id !== undefined ? `category_id=${ctx.category_id}` : null,
        ctx.variant_id !== undefined ? `variant_id=${ctx.variant_id}` : null,
        ctx.Title !== undefined ? `Title="${ctx.Title}"` : null,
      ].filter(Boolean).join('  ');
      console.log(`  • ${ex.created_at}  ${line || JSON.stringify(ctx)}`);
    }
  }
}

main().catch(e => { console.error('Fatal:', e); process.exit(1); });

