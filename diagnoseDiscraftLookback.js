// diagnoseDiscraftLookback.js
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

function daysAgoIso(days) {
  const d = new Date();
  d.setDate(d.getDate() - days);
  return d.toISOString();
}

async function run() {
  console.log('🔎 Diagnosing Discraft look-back mismatch...');

  // 1) Read configured lookback
  const { data: cfg, error: cfgErr } = await supabase
    .from('t_config')
    .select('value')
    .eq('key', 'discraft_disc_order_look_back_days')
    .single();
  if (cfgErr) throw cfgErr;
  const lookbackDays = parseInt(cfg.value) || 60;
  const sinceIso = daysAgoIso(lookbackDays);
  console.log(`Config lookbackDays: ${lookbackDays} (since ${sinceIso})`);

  // 2) Sum from it_discraft_osl_map (what email shows)
  const { data: mapRows, error: mapErr } = await supabase
    .from('it_discraft_osl_map')
    .select('id, sold_last_90, in_stock, qty');
  if (mapErr) throw mapErr;
  const sumMapSold = mapRows.reduce((s,r)=>s+(r.sold_last_90||0),0);
  const sumMapInStock = mapRows.reduce((s,r)=>s+(r.in_stock||0),0);
  const sumQty = mapRows.reduce((s,r)=>s+((r.qty||0)>0?(r.qty||0):0),0);
  const countQty = mapRows.filter(r=>r.qty && r.qty!==0).length;
  console.log(`it_discraft_osl_map totals -> sold_last_90(sum): ${sumMapSold}, in_stock(sum): ${sumMapInStock}, qty(sum): ${sumQty}, qty(count): ${countQty}`);

  // 3) Ground-truth using t_discs and vendor_osl_id
  const { data: oslIdsRows, error: oslErr } = await supabase
    .from('it_discraft_osl_map')
    .select('id');
  if (oslErr) throw oslErr;
  const oslIds = oslIdsRows.map(r=>r.id);

  // Count sold discs in last N days using vendor_osl_id
  const { data: soldDiscs, error: soldErr } = await supabase
    .from('t_discs')
    .select('id, vendor_osl_id, sold_date')
    .in('vendor_osl_id', oslIds)
    .gte('sold_date', sinceIso);
  if (soldErr) throw soldErr;
  const soldVendorCount = (soldDiscs||[]).length;
  console.log(`t_discs vendor_osl_id sold in last ${lookbackDays}d: ${soldVendorCount}`);

  // Alternative: using order_sheet_line_id (to detect mapping mixups)
  const { data: soldRegDiscs, error: soldRegErr } = await supabase
    .from('t_discs')
    .select('id, order_sheet_line_id, sold_date')
    .in('order_sheet_line_id', oslIds)
    .gte('sold_date', sinceIso);
  if (soldRegErr) throw soldRegErr;
  const soldRegularCount = (soldRegDiscs||[]).length;
  console.log(`t_discs order_sheet_line_id sold in last ${lookbackDays}d: ${soldRegularCount}`);

  // 4) Quick verdict
  console.log('\nVerdict:');
  if (sumMapSold !== soldVendorCount) {
    console.log(`- Mismatch: it_discraft_osl_map.sold_last_90 (sum=${sumMapSold}) != vendor_osl_id ground-truth (${soldVendorCount})`);
    console.log('  Likely cause: database function calculate_discraft_osl_disc_counts using a different lookback or mapping.');
  } else {
    console.log('- Map sum matches vendor_osl_id ground-truth.');
  }
  console.log(`- Regular mapping sold (order_sheet_line_id): ${soldRegularCount} (for comparison)`);

  console.log('\nIf mismatch exists, re-run the SQL to recreate the function using t_config lookback,'+
  ' then re-run the import to repopulate it_discraft_osl_map.');
}

run().then(()=>{process.exit(0)}).catch(err=>{console.error('❌', err.message); process.exit(1)});

