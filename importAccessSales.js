// importAccessSales.js - Import Access sales TSVs into Supabase
// Source files:
//   data/external data/access/tAccSales.txt
//   data/external data/access/tAccSaleLines.txt
//
// Mappings:
// tAccSales -> t_sales_orders
//   ID -> id (PK)
//   SaleDate -> order_date (TIMESTAMPTZ)
//   Channel_ID -> channel_id (INTEGER)
//   OrderNumber -> ignored
//   Note -> notes (TEXT)
//
// tAccSaleLines -> t_sales_order_lines
//   ID -> id (PK)
//   Sale_ID -> sales_order_id (FK to t_sales_orders.id)
//   Accessory_ID -> product_variant_id (FK to t_product_variants.id)
//   Qty -> quantity (INTEGER)
//   Price -> unit_price (NUMERIC)
//   DropShipped -> dropshipped (BOOLEAN)

import fs from 'fs';
import path from 'path';
import Papa from 'papaparse';

const SALES_PATH = path.join(process.cwd(), 'data', 'external data', 'access', 'tAccSales.txt');
const LINES_PATH = path.join(process.cwd(), 'data', 'external data', 'access', 'tAccSaleLines.txt');

const CHUNK_SIZE = 1000;

function toInt(v) {
  if (v === undefined || v === null || v === '') return null;
  const s = String(v).replace(/[,\s]/g, '').trim();
  const n = Number(s);
  return Number.isFinite(n) ? Math.trunc(n) : null;
}

function toNum(v) {
  if (v === undefined || v === null || v === '') return null;
  const s = String(v).replace(/[$,\s]/g, '').trim();
  const n = Number(s);
  return Number.isFinite(n) ? n : null;
}

function toBool(v) {
  if (v === undefined || v === null || v === '') return null;
  const s = String(v).trim().toLowerCase();
  if (s === 'y' || s === 'yes' || s === 'true' || s === '1') return true;
  if (s === 'n' || s === 'no' || s === 'false' || s === '0') return false;
  return null;
}

function parseDate(v) {
  if (!v) return null;
  const d = new Date(v);
  return isNaN(d.getTime()) ? null : d;
}

function cleanTsvContent(raw) {
  const lines = raw.split(/\r?\n/);
  if (lines.length === 0) return raw;
  const header = lines[0];
  const out = [header];
  let acc = '';
  const isBalanced = (s) => {
    let inQuote = false;
    for (let i = 0; i < s.length; i++) {
      if (s[i] === '"') {
        if (s[i + 1] === '"') { i++; continue; }
        inQuote = !inQuote;
      }
    }
    return !inQuote;
  };
  for (let i = 1; i < lines.length; i++) {
    if (acc === '') acc = lines[i]; else acc += '\n' + lines[i];
    if (isBalanced(acc)) { out.push(acc); acc = ''; }
  }
  if (acc) out.push(acc);
  return out.join('\n');
}

function parseTsv(filePath) {
  const raw = fs.readFileSync(filePath, 'utf-8');
  const cleaned = cleanTsvContent(raw);
  const { data, errors } = Papa.parse(cleaned, {
    header: true,
    skipEmptyLines: true,
    delimiter: '\t',
    dynamicTyping: false,
  });
  if (errors && errors.length) {
    console.warn(`[importAccessSales] TSV parse warnings in ${path.basename(filePath)} (first 5):`, errors.slice(0, 5));
  }
  return data;
}

function chunk(arr, size) {
  const out = [];
  for (let i = 0; i < arr.length; i += size) out.push(arr.slice(i, i + size));
  return out;
}

async function logError(supabase, msg, ctx) {
  try {
    await supabase.from('t_error_logs').insert({ error_message: msg, context: ctx });
  } catch (e) {
    console.error('[importAccessSales] Failed to log error:', e?.message || e);
  }
}

async function ensureTablesExist(supabase) {
  const sql = `
  CREATE TABLE IF NOT EXISTS public.t_sales_orders (
    id BIGINT PRIMARY KEY,
    order_date TIMESTAMPTZ,
    channel_id INTEGER,
    notes TEXT,
    status TEXT,
    created_by TEXT,
    imported_at TIMESTAMPTZ NOT NULL DEFAULT now()
  );

  -- Ensure columns exist if table was pre-existing
  ALTER TABLE public.t_sales_orders ADD COLUMN IF NOT EXISTS status TEXT;
  ALTER TABLE public.t_sales_orders ADD COLUMN IF NOT EXISTS created_by TEXT;

  CREATE TABLE IF NOT EXISTS public.t_sales_order_lines (
    id BIGINT PRIMARY KEY,
    sales_order_id BIGINT NOT NULL REFERENCES public.t_sales_orders(id) ON DELETE RESTRICT,
    product_variant_id BIGINT NOT NULL REFERENCES public.t_product_variants(id) ON DELETE RESTRICT,
    quantity INTEGER NOT NULL,
    unit_price NUMERIC(10,2),
    dropshipped BOOLEAN,
    imported_at TIMESTAMPTZ NOT NULL DEFAULT now()
  );

  CREATE INDEX IF NOT EXISTS idx_t_sales_orders_channel ON public.t_sales_orders(channel_id);
  CREATE INDEX IF NOT EXISTS idx_t_sales_lines_order ON public.t_sales_order_lines(sales_order_id);
  CREATE INDEX IF NOT EXISTS idx_t_sales_lines_variant ON public.t_sales_order_lines(product_variant_id);
  `;
  try {
    let { error } = await supabase.rpc('exec_sql', { sql_query: sql });
    if (error) {
      // fallback arg name used elsewhere
      ({ error } = await supabase.rpc('exec_sql', { sql_statement: sql }));
    }
    if (error) throw error;
  } catch (e) {
    console.warn('[importAccessSales] ensureTablesExist via exec_sql failed:', e?.message || e);
    // Try a cheap existence check to see if tables already exist
    try {
      await supabase.from('t_sales_orders').select('id').limit(1);
      await supabase.from('t_sales_order_lines').select('id').limit(1);
    } catch (ee) {
      throw new Error('Required tables t_sales_orders/t_sales_order_lines not available and creation failed.');
    }
  }
}

function mapOrderRow(r) {
  return {
    id: toInt(r['ID'] ?? r['Id'] ?? r['id']),
    order_date: parseDate(r['SaleDate'] ?? r['sale_date'] ?? r['Sale Date']),
    channel_id: toInt(r['Channel_ID'] ?? r['Channel Id'] ?? r['channel_id']),
    notes: (r['Note'] ?? r['Notes'] ?? null) || null,
    status: 'Pending',
    created_by: 'Migration',
  };
}

function mapLineRow(r) {
  return {
    id: toInt(r['ID'] ?? r['Id'] ?? r['id']),
    sales_order_id: toInt(r['Sale_ID'] ?? r['Sale Id'] ?? r['sale_id']),
    product_variant_id: toInt(r['Accessory_ID'] ?? r['Accessory Id'] ?? r['accessory_id']),
    quantity: toInt(r['Qty'] ?? r['Quantity'] ?? r['qty']),
    unit_price: toNum(r['Price'] ?? r['UnitPrice'] ?? r['unit_price']),
    dropshipped: toBool(r['DropShipped'] ?? r['Drop Shipped'] ?? r['dropshipped']),
  };
}

export async function importAccessSales(supabase, opts = {}) {
  const truncate = !!opts.truncate;
  const validateOnly = !!opts.validateOnly;
  console.log('[importAccessSales] Starting import. truncate =', truncate, ' validateOnly =', validateOnly);

  if (!fs.existsSync(SALES_PATH)) throw new Error(`Missing file: ${SALES_PATH}`);
  if (!fs.existsSync(LINES_PATH)) throw new Error(`Missing file: ${LINES_PATH}`);

  await ensureTablesExist(supabase);

  // Read files
  const salesRaw = parseTsv(SALES_PATH);
  const linesRaw = parseTsv(LINES_PATH);
  console.log(`[importAccessSales] Parsed ${salesRaw.length} sales and ${linesRaw.length} lines.`);

  // Map and validate
  const orderRecords = [];
  const lineRecords = [];
  const errors = [];

  const seenOrderIds = new Set();
  for (const r of salesRaw) {
    const rec = mapOrderRow(r);
    const ctx = { file: 'tAccSales', row: r };
    if (rec.id == null) { errors.push({ msg: 'order_missing_id', ctx }); continue; }
    if (seenOrderIds.has(rec.id)) { errors.push({ msg: 'order_duplicate_id', ctx: { file: 'tAccSales', id: rec.id } }); continue; }
    seenOrderIds.add(rec.id);
    if (rec.order_date == null) { errors.push({ msg: 'order_bad_date', ctx: { file: 'tAccSales', id: rec.id, SaleDate: r['SaleDate'] } }); }
    // channel_id optional but if present must be int
    if (r['Channel_ID'] != null && rec.channel_id == null) { errors.push({ msg: 'order_bad_channel_id', ctx: { file: 'tAccSales', id: rec.id, Channel_ID: r['Channel_ID'] } }); }
    orderRecords.push(rec);
  }

  const seenLineIds = new Set();
  const referencedOrderIds = new Set();
  const referencedVariantIds = new Set();
  for (const r of linesRaw) {
    const rec = mapLineRow(r);
    const ctx = { file: 'tAccSaleLines', row: r };
    if (rec.id == null) { errors.push({ msg: 'line_missing_id', ctx }); continue; }
    if (seenLineIds.has(rec.id)) { errors.push({ msg: 'line_duplicate_id', ctx: { file: 'tAccSaleLines', id: rec.id } }); continue; }
    seenLineIds.add(rec.id);
    if (rec.sales_order_id == null) { errors.push({ msg: 'line_missing_sales_order_id', ctx: { id: rec.id } }); }
    if (rec.product_variant_id == null) { errors.push({ msg: 'line_missing_product_variant_id', ctx: { id: rec.id } }); }
    if (rec.quantity == null) {
      errors.push({ msg: 'line_bad_quantity', ctx: { id: rec.id, Qty: r['Qty'] } });
    }
    if (r['Price'] != null && rec.unit_price == null) { errors.push({ msg: 'line_bad_unit_price', ctx: { id: rec.id, Price: r['Price'] } }); }
    if (rec.unit_price != null && rec.unit_price < 0) { errors.push({ msg: 'line_bad_unit_price_negative', ctx: { id: rec.id, unit_price: rec.unit_price } }); }
    if (r['DropShipped'] != null && rec.dropshipped == null) { errors.push({ msg: 'line_bad_dropshipped', ctx: { id: rec.id, DropShipped: r['DropShipped'] } }); }
    if (rec.sales_order_id != null) referencedOrderIds.add(rec.sales_order_id);
    if (rec.product_variant_id != null) referencedVariantIds.add(rec.product_variant_id);
    lineRecords.push(rec);
  }

  // Referential checks
  for (const soId of referencedOrderIds) {
    if (!seenOrderIds.has(soId)) {
      errors.push({ msg: 'line_references_missing_order', ctx: { sales_order_id: soId } });
    }
  }

  // Check product variants existence in DB (in batches)
  const variantIdList = Array.from(referencedVariantIds);
  const presentVariantIds = new Set();
  for (let i = 0; i < variantIdList.length; i += 1000) {
    const batch = variantIdList.slice(i, i + 1000);
    const { data, error } = await supabase.from('t_product_variants').select('id').in('id', batch);
    if (error) {
      throw new Error(`Failed to check t_product_variants: ${error.message}`);
    }
    for (const row of data || []) presentVariantIds.add(row.id);
  }
  for (const vid of variantIdList) {
    if (!presentVariantIds.has(vid)) {
      errors.push({ msg: 'line_references_missing_variant', ctx: { product_variant_id: vid } });
    }
  }

  // If any errors, log and abort
  if (errors.length) {
    console.warn(`[importAccessSales] Validation found ${errors.length} issue(s). Logging to t_error_logs and aborting.`);
    for (const e of errors) await logError(supabase, `access_sales_${e.msg}`, e.ctx);
    return { success: false, errorCount: errors.length, message: 'Validation errors found. Fix data and retry.' };
  }

  // If validate-only, stop here after validation success
  if (validateOnly) {
    console.log('[importAccessSales] Validate-only mode: no DB writes.');
    return { success: true, orders: orderRecords.length, lines: lineRecords.length, validateOnly: true };
  }

  // Truncate if requested
  if (truncate) {
    console.log('[importAccessSales] Removing sale movements, truncating sales lines and orders...');
    try {
      // 1) Delete sale movements only (do not truncate)
      let { error: delSaleMovErr } = await supabase
        .from('t_inventory_movements')
        .delete()
        .eq('movement_type', 'sale');
      if (delSaleMovErr) {
        console.warn('[importAccessSales] Supabase delete failed for sale movements; trying exec_sql DELETE…', delSaleMovErr.message);
        const delSql = `DELETE FROM public.t_inventory_movements WHERE movement_type = 'sale';`;
        let { error: delSqlErr } = await supabase.rpc('exec_sql', { sql_query: delSql });
        if (delSqlErr) { ({ error: delSqlErr } = await supabase.rpc('exec_sql', { sql_statement: delSql })); }
        if (delSqlErr) throw delSqlErr;
      }

      // 2) Then truncate sales lines and orders
      const sql2 = `TRUNCATE TABLE public.t_sales_order_lines, public.t_sales_orders RESTART IDENTITY CASCADE;`;
      let { error: err2 } = await supabase.rpc('exec_sql', { sql_query: sql2 });
      if (err2) { ({ error: err2 } = await supabase.rpc('exec_sql', { sql_statement: sql2 })); }
      if (err2) throw err2;
    } catch (e) {
      console.warn('[importAccessSales] Truncate/delete fallback path due to error:', e?.message || e);
      // Try delete sale movements again via table API
      const { error: delMovErr2 } = await supabase
        .from('t_inventory_movements')
        .delete()
        .eq('movement_type', 'sale');
      if (delMovErr2) throw new Error(`Failed to delete sale movements: ${delMovErr2.message}`);
      // Then sales lines and orders
      const { error: delLinesErr } = await supabase.from('t_sales_order_lines').delete().neq('id', null);
      if (delLinesErr) throw new Error(`Failed to clear t_sales_order_lines: ${delLinesErr.message}`);
      const { error: delOrdersErr } = await supabase.from('t_sales_orders').delete().neq('id', null);
      if (delOrdersErr) throw new Error(`Failed to clear t_sales_orders: ${delOrdersErr.message}`);
    }
  }

  // Insert orders
  console.log(`[importAccessSales] Inserting ${orderRecords.length} orders...`);
  for (const part of chunk(orderRecords, CHUNK_SIZE)) {
    const { error } = await supabase.from('t_sales_orders').insert(part);
    if (error) {
      throw new Error(`Failed inserting orders chunk: ${error.message}`);
    }
  }

  // Insert lines
  console.log(`[importAccessSales] Inserting ${lineRecords.length} lines...`);
  for (const part of chunk(lineRecords, CHUNK_SIZE)) {
    const { error } = await supabase.from('t_sales_order_lines').insert(part);
    if (error) {
      throw new Error(`Failed inserting lines chunk: ${error.message}`);
    }
  }

  // After successful inserts, set all imported orders to Complete
  try {
    console.log('[importAccessSales] Marking imported orders as Complete...');
    const importedIds = orderRecords.map(o => o.id);
    for (const idChunk of chunk(importedIds, CHUNK_SIZE)) {
      const { error: updErr } = await supabase
        .from('t_sales_orders')
        .update({ status: 'Complete' })
        .in('id', idChunk);
      if (updErr) throw updErr;
    }
  } catch (e) {
    throw new Error(`[importAccessSales] Failed to mark orders Complete: ${e.message}`);
  }

  // Reset ID sequences to MAX(id)+1 so future inserts via sequences are correct
  try {
    console.log('[importAccessSales] Resetting ID sequences for sales tables...');
    const seqSqls = [
      "SELECT setval( pg_get_serial_sequence('t_sales_orders', 'id'), COALESCE((SELECT MAX(id) FROM t_sales_orders), 0) + 1, false );",
      "SELECT setval( pg_get_serial_sequence('t_sales_order_lines', 'id'), COALESCE((SELECT MAX(id) FROM t_sales_order_lines), 0) + 1, false );",
    ];
    for (const sql of seqSqls) {
      let { error } = await supabase.rpc('exec_sql', { sql_query: sql });
      if (error) { ({ error } = await supabase.rpc('exec_sql', { sql_statement: sql })); }
      if (error) throw error;
    }
  } catch (e) {
    throw new Error(`[importAccessSales] Failed to reset sequences: ${e.message}`);
  }

  console.log('[importAccessSales] Import complete.');
  return { success: true, orders: orderRecords.length, lines: lineRecords.length };
}

export default importAccessSales;



// CLI usage: node importAccessSales.js [--truncate]
if (process.argv[1] && /importAccessSales\.js$/i.test(process.argv[1])) {
  (async () => {
    const dotenvMod = await import('dotenv');
    const dotenv = dotenvMod?.default || dotenvMod;
    dotenv.config();
    const { createClient } = await import('@supabase/supabase-js');

    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_KEY;
    if (!supabaseUrl || !supabaseKey) {
      console.error('[importAccessSales] Missing SUPABASE_URL or SUPABASE_KEY');
      process.exit(1);
    }
    const supabase = createClient(supabaseUrl, supabaseKey);

    const truncate = process.argv.includes('--truncate');
    const validateOnly = process.argv.includes('--validate') || process.argv.includes('--dry-run');
    const result = await importAccessSales(supabase, { truncate, validateOnly });
    console.log(result);
    process.exit(result?.success ? 0 : 1);
  })().catch((e) => {
    console.error('[importAccessSales] Fatal:', e?.message || e);
    process.exit(1);
  });
}
