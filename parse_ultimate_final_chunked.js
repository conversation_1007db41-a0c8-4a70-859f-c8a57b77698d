import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Cache for lookup data
let brandsCache = {};
let allMoldsCache = [];
let allPlasticsCache = [];
let moldsCache = {};
let plasticsCache = {};
let stampsCache = [];

// Brand mappings for vendor names that don't match exactly
const brandMappings = {
    'Westside Discs': 'Westside',
    'Dynamic Discs': 'Dynamic Discs',
    'Latitude 64': 'Latitude 64',
    'Discmania': 'Discmania',
    'Kastaplast': 'Kastaplast',
    'Active': 'Discmania Active'
};

// Plastic name mappings for vendor names that don't match database names
const plasticMappings = {
    'Gold': 'Gold Line',
    'Tournament': 'Tournament',
    'Lucid Moonshine': 'Lucid Moonshine Glow',
    'Classic Moonshine': 'Classic Soft Moonshine Glow',
    'Prime Moonshine': 'Prime Moonshine Glow',
    'BioGold': 'Gold Line Bio',
    'Active': 'Base Level',
    'Active Premium': 'Premium',
    'Active Premium Glow': 'Premium Glow',
    'Classic': 'Classic (Hard)',
    'Classic Burst': 'Classic (Hard) Burst',
    'Classic Blend': 'Classic Blend',
    'Classic Blend Burst': 'Classic Blend Burst',
    'Classic Soft': 'Classic Soft',
    'Classic Soft Burst': 'Classic Soft Burst',
    'K1 Grind': 'K1 - Grind',
    'Hard Exo': 'EXO Hard',
    'Grand Orbit': 'Royal Grand Orbit',
    'Grand': 'Royal Grand',
    'Gold-Ice': 'Gold Ice',
    'Frost': 'Frost Line',
    'First Run C-Line': 'C-Line',
    'Horizon S-Line': 'Horizon S-Line',
    'Hard Exo Vapor': 'Exo Hard Vapor',
    'Lux Vapor': 'Lux Vapor'
};

// Mold name mappings for vendor names that don't match database names
const moldMappings = {
    'Kaxe (new)': 'Kaxe Retooled'
};

async function loadLookupData() {
    console.log('Loading lookup data...');
    
    // Load all brands
    const { data: brands, error: brandsError } = await supabase
        .from('t_brands')
        .select('id, brand');
    
    if (brandsError) {
        throw new Error(`Error loading brands: ${brandsError.message}`);
    }
    
    // Create brand lookup
    brands.forEach(brand => {
        brandsCache[brand.brand] = brand.id;
    });
    
    // Load all molds with their brand_id
    const { data: molds, error: moldsError } = await supabase
        .from('t_molds')
        .select('mold, brand_id');
    
    if (moldsError) {
        throw new Error(`Error loading molds: ${moldsError.message}`);
    }
    
    // Store all molds and group by brand_id
    allMoldsCache = molds.map(m => ({ mold: m.mold, brand_id: m.brand_id }));
    molds.forEach(mold => {
        if (!moldsCache[mold.brand_id]) {
            moldsCache[mold.brand_id] = [];
        }
        moldsCache[mold.brand_id].push(mold.mold);
    });
    
    // Load all plastics with their brand_id
    const { data: plastics, error: plasticsError } = await supabase
        .from('t_plastics')
        .select('plastic, brand_id');
    
    if (plasticsError) {
        throw new Error(`Error loading plastics: ${plasticsError.message}`);
    }
    
    // Store all plastics and group by brand_id
    allPlasticsCache = plastics.map(p => ({ plastic: p.plastic, brand_id: p.brand_id }));
    plastics.forEach(plastic => {
        if (!plasticsCache[plastic.brand_id]) {
            plasticsCache[plastic.brand_id] = [];
        }
        plasticsCache[plastic.brand_id].push(plastic.plastic);
    });
    
    // Load all stamps (not brand-specific)
    const { data: stamps, error: stampsError } = await supabase
        .from('t_stamps')
        .select('stamp');
    
    if (stampsError) {
        throw new Error(`Error loading stamps: ${stampsError.message}`);
    }
    
    stampsCache = stamps.map(s => s.stamp);
    
    console.log(`Loaded ${brands.length} brands, ${molds.length} molds, ${plastics.length} plastics, ${stampsCache.length} stamps`);
}

function findPlasticWithMapping(plasticName, brandId) {
    // First try with mapping
    const mappedPlastic = plasticMappings[plasticName] || plasticName;
    
    // Check brand-specific plastics first
    const brandPlastics = plasticsCache[brandId] || [];
    
    // Try exact match with mapped name
    if (brandPlastics.includes(mappedPlastic)) {
        return mappedPlastic;
    }
    
    // Try exact match with original name
    if (brandPlastics.includes(plasticName)) {
        return plasticName;
    }
    
    // Try partial matches in brand
    for (const plastic of brandPlastics) {
        if (plastic.includes(mappedPlastic) || mappedPlastic.includes(plastic)) {
            return plastic;
        }
    }
    
    // If not found in brand, try across all brands
    for (const plasticData of allPlasticsCache) {
        if (plasticData.plastic === mappedPlastic) {
            return plasticData.plastic;
        }
    }
    
    for (const plasticData of allPlasticsCache) {
        if (plasticData.plastic === plasticName) {
            return plasticData.plastic;
        }
    }
    
    return null;
}

function findMoldWithMapping(moldName, brandId) {
    // First check for exact mapping match
    if (moldMappings[moldName]) {
        const mappedMold = moldMappings[moldName];
        const brandMolds = moldsCache[brandId] || [];
        
        // Check if the mapped mold exists in this brand
        if (brandMolds.includes(mappedMold)) {
            return mappedMold;
        }
        
        // If not in brand, check across all brands
        for (const moldData of allMoldsCache) {
            if (moldData.mold === mappedMold) {
                return moldData.mold;
            }
        }
    }
    
    // If no mapping found, return null to continue with regular logic
    return null;
}

function findMoldAcrossBrands(moldName) {
    // Sort all molds by length (longest first) to prioritize exact matches
    const sortedMolds = [...allMoldsCache].sort((a, b) => b.mold.length - a.mold.length);
    
    // First try exact match (case-insensitive)
    for (const moldData of sortedMolds) {
        if (moldData.mold.toLowerCase() === moldName.toLowerCase()) {
            return moldData.mold;
        }
    }
    
    // Then try starts with match (case-insensitive)
    for (const moldData of sortedMolds) {
        if (moldName.toLowerCase().startsWith(moldData.mold.toLowerCase() + ' ') || 
            moldName.toLowerCase() === moldData.mold.toLowerCase()) {
            return moldData.mold;
        }
    }
    
    // Then try if mold appears at start (case-insensitive)
    for (const moldData of sortedMolds) {
        if (moldName.toLowerCase().startsWith(moldData.mold.toLowerCase())) {
            return moldData.mold;
        }
    }
    
    // Then try partial match
    for (const moldData of sortedMolds) {
        if (moldData.mold.toLowerCase().includes(moldName.toLowerCase()) || 
            moldName.toLowerCase().includes(moldData.mold.toLowerCase())) {
            return moldData.mold;
        }
    }
    
    // Finally try space-insensitive match (remove spaces and compare)
    const moldNameNoSpaces = moldName.replace(/\s+/g, '').toLowerCase();
    for (const moldData of sortedMolds) {
        const moldNoSpaces = moldData.mold.replace(/\s+/g, '').toLowerCase();
        if (moldNameNoSpaces === moldNoSpaces || 
            moldNameNoSpaces.startsWith(moldNoSpaces) ||
            moldNoSpaces.startsWith(moldNameNoSpaces)) {
            return moldData.mold;
        }
    }
    
    return null;
}

function parseProductTitle(productTitle, brandId) {
    if (!productTitle || typeof productTitle !== 'string') {
        return { mold: null, plastic: null, stamp: null };
    }
    
    const title = productTitle.trim();
    
    // Skip non-disc products - including anything with DyeMax
    if (title.includes('Set') || title.includes('Mystery') || title.includes('Box') || 
        title.includes('DyeMax') || title.toLowerCase().includes('dyemax')) {
        return { mold: null, plastic: null, stamp: null };
    }
    
    let foundMold = null;
    let foundPlastic = null;
    let foundStamp = 'Stock';
    
    // Get brand plastics and sort by length (longest first)
    const brandPlastics = plasticsCache[brandId] || [];
    const sortedPlastics = [...brandPlastics].sort((a, b) => b.length - a.length);
    
    // Also try mapped plastic names
    const allPossiblePlastics = [...sortedPlastics];
    Object.keys(plasticMappings).forEach(vendorName => {
        if (!allPossiblePlastics.includes(vendorName)) {
            allPossiblePlastics.push(vendorName);
        }
    });
    allPossiblePlastics.sort((a, b) => b.length - a.length);
    
    // Special handling for complex titles with stamps at the beginning
    // Pattern: "Stamp - More Stamp Info Plastic Mold" or "Stamp Plastic Mold"
    
    // Check if title has pattern like "First Run C-Line FD1" or "Eternal Void - Kyle Klein..."
    let titleToProcess = title;
    let prefixStamp = '';

    // Special case for "First Run" pattern
    if (title.startsWith('First Run ')) {
        prefixStamp = 'First Run';
        titleToProcess = title.substring('First Run '.length);
    }

    // Look for plastic anywhere in the title (not just at the beginning)
    // But first check if the title starts with a longer plastic mapping
    let plasticFound = null;
    let plasticPosition = -1;

    // Only look for prefix stamps if we haven't already found one and title doesn't start with a plastic
    // But be more specific - only skip if the plastic is followed by a mold
    let titleStartsWithPlastic = false;

    if (!prefixStamp) {
        for (const plastic of allPossiblePlastics) {
            if (title.startsWith(plastic + ' ') || title === plastic) {
                // Check if this plastic is followed by a mold
                const afterPlastic = title.substring(plastic.length).trim();
                const brandMolds = moldsCache[brandId] || [];
                const allMolds = [...brandMolds, ...allMoldsCache.map(m => m.mold)];

                for (const mold of allMolds) {
                    if (afterPlastic === mold ||
                        afterPlastic.startsWith(mold + ' ') ||
                        afterPlastic.toLowerCase() === mold.toLowerCase() ||
                        afterPlastic.toLowerCase().startsWith(mold.toLowerCase() + ' ')) {
                        titleStartsWithPlastic = true;
                        break;
                    }
                }

                if (titleStartsWithPlastic) break;
            }
        }
    } else {
        titleStartsWithPlastic = true; // Skip complex detection if we already have a prefix
    }

    if (!titleStartsWithPlastic) {
        for (const plastic of allPossiblePlastics) {
            const plasticIndex = title.indexOf(plastic);
            if (plasticIndex !== -1 && plasticIndex > 0) { // Only consider if plastic is not at the beginning
                // Make sure this isn't a substring of a longer plastic name
                const beforePlastic = title.substring(0, plasticIndex);
                const afterPlasticChar = title.charAt(plasticIndex + plastic.length);
                
                // Skip if this plastic is part of a longer word (like "Fuzion" in "BioFuzion")
                if (beforePlastic.match(/[a-zA-Z]$/) || (afterPlasticChar && afterPlasticChar.match(/[a-zA-Z]/))) {
                    continue;
                }
                
                // Check if this plastic is followed by a space and then a mold
                const afterPlastic = title.substring(plasticIndex + plastic.length).trim();

                // Get brand molds to check if what follows could be a mold
                const brandMolds = moldsCache[brandId] || [];
                const allMolds = [...brandMolds, ...allMoldsCache.map(m => m.mold)];

                // Sort molds by length (longest first) for better matching
                const sortedMolds = allMolds.sort((a, b) => b.length - a.length);

                for (const mold of sortedMolds) {
                    if (afterPlastic === mold ||
                        afterPlastic.startsWith(mold + ' ') ||
                        afterPlastic.toLowerCase() === mold.toLowerCase() ||
                        afterPlastic.toLowerCase().startsWith(mold.toLowerCase() + ' ')) {
                        plasticFound = plastic;
                        plasticPosition = plasticIndex;
                        break;
                    }
                }

                if (plasticFound) break;
            }
        }
    }

    if (plasticFound && plasticPosition > 0) {
        // Extract prefix as stamp
        prefixStamp = title.substring(0, plasticPosition).trim();
        titleToProcess = title.substring(plasticPosition);

        // Clean up prefix stamp (remove trailing dashes)
        prefixStamp = prefixStamp.replace(/\s*-\s*$/, '').trim();
    }

    // Find plastic at the beginning of titleToProcess
    for (const plastic of allPossiblePlastics) {
        if (titleToProcess.startsWith(plastic + ' ') || titleToProcess === plastic) {
            // Use mapping to get the correct database plastic name
            foundPlastic = findPlasticWithMapping(plastic, brandId);
            break;
        }
    }
    
    if (foundPlastic) {
        // Find the original plastic name that matched (could be vendor name)
        let matchedPlasticName = foundPlastic;
        for (const plastic of allPossiblePlastics) {
            if (titleToProcess.startsWith(plastic + ' ') || titleToProcess === plastic) {
                matchedPlasticName = plastic;
                break;
            }
        }

        // Remove plastic from titleToProcess to find mold and stamp
        let remainingTitle = titleToProcess.substring(matchedPlasticName.length).trim();
        
        // Check if there's a dash indicating a stamp
        const dashIndex = remainingTitle.indexOf(' - ');
        let moldPart = remainingTitle;
        let stampPart = '';
        
        if (dashIndex !== -1) {
            moldPart = remainingTitle.substring(0, dashIndex).trim();
            stampPart = remainingTitle.substring(dashIndex + 3).trim();
        }
        
        // Find mold (try mapping first, then brand-specific, then cross-brand)
        
        // First check if the entire moldPart matches a mapping
        foundMold = findMoldWithMapping(moldPart, brandId);
        
        if (!foundMold) {
            const brandMolds = moldsCache[brandId] || [];
            const sortedMolds = [...brandMolds].sort((a, b) => b.length - a.length);
            
            // First try exact matches with longer molds first (case-insensitive)
            for (const mold of sortedMolds) {
                if (moldPart.toLowerCase() === mold.toLowerCase()) {
                    foundMold = mold;
                    break;
                }
            }
            
            // Then try starts with matches (case-insensitive)
            if (!foundMold) {
                for (const mold of sortedMolds) {
                    if (moldPart.toLowerCase().startsWith(mold.toLowerCase() + ' ')) {
                        foundMold = mold;
                        break;
                    }
                }
            }
            
            // Then try if mold appears at start (case-insensitive)
            if (!foundMold) {
                for (const mold of sortedMolds) {
                    if (moldPart.toLowerCase().startsWith(mold.toLowerCase())) {
                        foundMold = mold;
                        break;
                    }
                }
            }
            
            // If not found in brand, try cross-brand
            if (!foundMold) {
                foundMold = findMoldAcrossBrands(moldPart);
            }
        }
        
        // Set stamp
        if (stampPart) {
            // If we have both prefix stamp and suffix stamp, combine them
            if (prefixStamp) {
                foundStamp = prefixStamp + ' - ' + stampPart;
            } else {
                foundStamp = stampPart;
            }
        } else if (prefixStamp) {
            foundStamp = prefixStamp;
        } else if (foundMold && moldPart.length > foundMold.length) {
            // Check if there's additional text after mold (without dash)
            // Need to handle case where vendor uses spaces but database doesn't
            let afterMold = '';
            
            // Try to find where the mold ends in the original moldPart
            const moldLower = foundMold.toLowerCase();
            const moldPartLower = moldPart.toLowerCase();
            
            // Find the mold in moldPart (case-insensitive)
            let moldEndIndex = -1;
            if (moldPartLower.startsWith(moldLower)) {
                moldEndIndex = foundMold.length;
            } else {
                // Handle space differences like "Swan 1 Reborn" vs "Swan1 Reborn"
                const moldNoSpaces = moldLower.replace(/\s+/g, '');
                const moldPartNoSpaces = moldPartLower.replace(/\s+/g, '');
                if (moldPartNoSpaces.startsWith(moldNoSpaces)) {
                    // Find where the mold ends in the original string
                    let charCount = 0;
                    let moldCharIndex = 0;
                    for (let i = 0; i < moldPart.length && moldCharIndex < moldLower.length; i++) {
                        if (moldPart[i].toLowerCase() === moldLower[moldCharIndex]) {
                            moldCharIndex++;
                        } else if (moldPart[i] === ' ') {
                            // Skip spaces in moldPart that aren't in mold
                            continue;
                        }
                        charCount = i + 1;
                    }
                    moldEndIndex = charCount;
                }
            }
            
            if (moldEndIndex > 0 && moldEndIndex < moldPart.length) {
                afterMold = moldPart.substring(moldEndIndex).trim();
            }
            
            if (afterMold && afterMold !== '') {
                foundStamp = afterMold;
            }
        }
    }
    
    return {
        mold: foundMold,
        plastic: foundPlastic,
        stamp: foundStamp
    };
}

function parseVariantTitle(variantTitle, brandId) {
    if (!variantTitle || typeof variantTitle !== 'string') {
        return { mold: null, plastic: null, stamp: null };
    }
    
    const title = variantTitle.trim();
    
    // Skip default titles and DyeMax
    if (title === 'Default Title' || title === 'Assorted' || 
        title.includes('DyeMax') || title.toLowerCase().includes('dyemax')) {
        return { mold: null, plastic: null, stamp: null };
    }
    
    // For DyeMax variants like "Fuzion Verdict (Midrange)"
    // Remove type information in parentheses
    const cleanTitle = title.replace(/\s*\([^)]*\)\s*$/, '').trim();
    
    // Use same logic as product title parsing
    return parseProductTitle(cleanTitle, brandId);
}

async function parseAllRecordsChunked() {
    console.log('Starting ULTIMATE comprehensive parsing of ALL records...');
    
    try {
        // Load lookup data
        await loadLookupData();
        
        // Get total count first
        const { count: totalCount, error: countError } = await supabase
            .from('it_dd_osl')
            .select('*', { count: 'exact', head: true })
            .eq('product_product_type', 'Discs');
        
        if (countError) {
            console.error('Error getting total count:', countError);
            return;
        }
        
        console.log(`Total disc records to process: ${totalCount}`);
        
        const chunkSize = 1000;
        let processedCount = 0;
        let updatedCount = 0;
        
        const parseStats = {
            total: totalCount,
            parsed_mold: 0,
            parsed_plastic: 0,
            parsed_stamp: 0,
            failed: 0,
            by_vendor: {}
        };
        
        // Process in chunks
        for (let offset = 0; offset < totalCount; offset += chunkSize) {
            console.log(`\nProcessing chunk ${Math.floor(offset/chunkSize) + 1}/${Math.ceil(totalCount/chunkSize)} (records ${offset + 1}-${Math.min(offset + chunkSize, totalCount)})`);
            
            // Fetch chunk
            const { data: records, error: fetchError } = await supabase
                .from('it_dd_osl')
                .select('id, product_title, variant_title, product_vendor, product_product_type')
                .eq('product_product_type', 'Discs')
                .range(offset, offset + chunkSize - 1);
            
            if (fetchError) {
                console.error('Error fetching chunk:', fetchError);
                continue;
            }
            
            console.log(`Fetched ${records.length} records`);
            
            // Parse and update this chunk
            const updates = [];
            
            for (const record of records) {
                // Apply brand mapping
                const mappedBrand = brandMappings[record.product_vendor] || record.product_vendor;
                const brandId = brandsCache[mappedBrand];
                
                if (!brandId && record.product_vendor !== 'Assorted') {
                    continue;
                }
                
                // Skip Assorted products
                if (record.product_vendor === 'Assorted') {
                    continue;
                }
                
                // Try parsing variant title first (for DyeMax products)
                let parsed = parseVariantTitle(record.variant_title, brandId);
                
                // If that fails, try product title
                if (!parsed.mold && !parsed.plastic) {
                    parsed = parseProductTitle(record.product_title, brandId);
                }
                
                const update = {
                    id: record.id,
                    parsed_mold: parsed.mold,
                    parsed_plastic: parsed.plastic,
                    parsed_stamp: parsed.stamp
                };
                
                updates.push(update);
                
                // Update statistics
                if (parsed.mold) parseStats.parsed_mold++;
                if (parsed.plastic) parseStats.parsed_plastic++;
                if (parsed.stamp) parseStats.parsed_stamp++;
                if (!parsed.mold && !parsed.plastic && !parsed.stamp) parseStats.failed++;
                
                // Track by vendor
                if (!parseStats.by_vendor[record.product_vendor]) {
                    parseStats.by_vendor[record.product_vendor] = { total: 0, parsed: 0 };
                }
                parseStats.by_vendor[record.product_vendor].total++;
                if (parsed.mold || parsed.plastic) {
                    parseStats.by_vendor[record.product_vendor].parsed++;
                }
            }
            
            // Update records in database
            for (const update of updates) {
                const { error: updateError } = await supabase
                    .from('it_dd_osl')
                    .update({
                        parsed_mold: update.parsed_mold,
                        parsed_plastic: update.parsed_plastic,
                        parsed_stamp: update.parsed_stamp
                    })
                    .eq('id', update.id);
                
                if (updateError) {
                    console.error(`Error updating record ${update.id}:`, updateError);
                } else {
                    updatedCount++;
                }
            }
            
            processedCount += records.length;
            console.log(`Chunk complete. Processed: ${processedCount}/${totalCount}, Updated: ${updatedCount}`);
        }
        
        console.log(`\n=== FINAL RESULTS ===`);
        console.log(`Total records processed: ${processedCount}`);
        console.log(`Records updated: ${updatedCount}`);
        console.log(`Records with mold: ${parseStats.parsed_mold}`);
        console.log(`Records with plastic: ${parseStats.parsed_plastic}`);
        console.log(`Records with stamp: ${parseStats.parsed_stamp}`);
        console.log(`Failed to parse: ${parseStats.failed}`);
        
        // Show vendor breakdown
        console.log('\nParsing success by vendor:');
        console.log('=====================================');
        Object.entries(parseStats.by_vendor).forEach(([vendor, stats]) => {
            const percentage = ((stats.parsed / stats.total) * 100).toFixed(1);
            console.log(`${vendor}: ${stats.parsed}/${stats.total} (${percentage}%)`);
        });
        
    } catch (error) {
        console.error('Script failed:', error);
    }
}

// Run the script
console.log('ULTIMATE Multi-Vendor Product Parser\n');

parseAllRecordsChunked()
    .then(() => {
        console.log('\nULTIMATE parsing completed successfully');
        process.exit(0);
    })
    .catch(error => {
        console.error('ULTIMATE parsing failed:', error);
        process.exit(1);
    });
