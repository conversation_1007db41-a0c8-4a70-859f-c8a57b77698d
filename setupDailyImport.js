// setupDailyImport.js - Script to set up daily DBF import task

import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configuration
const dbfFilePath = process.env.DBF_FILE_PATH || './data/daily_import.dbf';
const targetTable = process.env.TARGET_TABLE || 'imported_dbf_data';
const truncateBeforeImport = process.env.TRUNCATE_BEFORE_IMPORT === 'true' || false;

// Get the absolute path to the import script
const scriptPath = path.resolve('./importDbfToSupabase.js');

// Create a batch file for the scheduled task
const createBatchFile = () => {
  const batchContent = `@echo off
echo Starting DBF import at %date% %time%
cd "${process.cwd()}"
node setupDbfImport.js --file="${dbfFilePath}" --import-only --truncate=${truncateBeforeImport}
echo Import completed at %date% %time%
`;

  const batchPath = path.join(process.cwd(), 'runDbfImport.bat');
  fs.writeFileSync(batchPath, batchContent);
  console.log(`Created batch file at: ${batchPath}`);
  return batchPath;
};

// Create a scheduled task using Windows Task Scheduler
const createWindowsScheduledTask = (batchPath) => {
  // Schedule for 6:15 AM (giving a buffer after the file is generated at 6:03 AM)
  const taskName = 'DailyDbfImport';
  const command = `schtasks /create /tn "${taskName}" /tr "${batchPath}" /sc DAILY /st 06:15 /ru SYSTEM /f`;

  console.log('Creating Windows scheduled task...');
  console.log(`Command: ${command}`);

  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`Error creating scheduled task: ${error.message}`);
      console.error('You may need to run this script with administrator privileges.');
      return;
    }

    console.log('Scheduled task created successfully!');
    console.log(stdout);

    // Display the task
    exec(`schtasks /query /tn "${taskName}" /fo LIST`, (err, out) => {
      if (!err) {
        console.log('Task details:');
        console.log(out);
      }
    });
  });
};

// Create a PM2 scheduled task (alternative to Windows Task Scheduler)
const createPm2ScheduledTask = () => {
  // Check if PM2 is installed
  exec('pm2 --version', (error) => {
    if (error) {
      console.error('PM2 is not installed. Please install it with: npm install -g pm2');
      return;
    }

    // Create a PM2 ecosystem file
    const ecosystemConfig = {
      apps: [{
        name: 'dbf-import',
        script: './setupDbfImport.js',
        args: `--file="${dbfFilePath}" --import-only --truncate=${truncateBeforeImport}`,
        cron_restart: '15 6 * * *', // Run at 6:15 AM every day
        autorestart: false
      }]
    };

    const ecosystemPath = path.join(process.cwd(), 'ecosystem.config.js');
    fs.writeFileSync(
      ecosystemPath,
      `module.exports = ${JSON.stringify(ecosystemConfig, null, 2)}`
    );

    console.log(`Created PM2 ecosystem file at: ${ecosystemPath}`);

    // Start the PM2 process
    exec(`pm2 start ${ecosystemPath}`, (err, stdout) => {
      if (err) {
        console.error(`Error starting PM2 process: ${err.message}`);
        return;
      }

      console.log('PM2 scheduled task created successfully!');
      console.log(stdout);

      // Save the PM2 configuration
      exec('pm2 save', (saveErr, saveStdout) => {
        if (!saveErr) {
          console.log('PM2 configuration saved:');
          console.log(saveStdout);
        }
      });
    });
  });
};

// Main function
const setupDailyImport = () => {
  console.log('Setting up daily DBF import task...');
  console.log(`DBF File Path: ${dbfFilePath}`);
  console.log(`Target Table: ${targetTable}`);
  console.log(`Truncate Before Import: ${truncateBeforeImport}`);

  // Create the batch file
  const batchPath = createBatchFile();

  // Ask user which scheduler to use
  console.log('\nChoose a scheduler:');
  console.log('1. Windows Task Scheduler (requires admin privileges)');
  console.log('2. PM2 (requires PM2 to be installed)');

  // Since we can't get interactive input in this environment,
  // we'll provide instructions for both options
  console.log('\nTo use Windows Task Scheduler:');
  console.log(`Run the following command as administrator:`);
  console.log(`schtasks /create /tn "DailyDbfImport" /tr "${batchPath}" /sc DAILY /st 06:15 /ru SYSTEM /f`);

  console.log('\nTo use PM2:');
  console.log('1. Install PM2 if not already installed: npm install -g pm2');
  console.log(`2. Run: pm2 start ./setupDbfImport.js --name dbf-import --cron "15 6 * * *" -- --file="${dbfFilePath}" --import-only --truncate=${truncateBeforeImport}`);
  console.log('3. Run: pm2 save');

  console.log('\nYou can also run this script with an argument:');
  console.log('node setupDailyImport.js windows - to set up Windows Task Scheduler');
  console.log('node setupDailyImport.js pm2 - to set up PM2');
};

// Check command line arguments
if (process.argv[2] === 'windows') {
  const batchPath = createBatchFile();
  createWindowsScheduledTask(batchPath);
} else if (process.argv[2] === 'pm2') {
  createPm2ScheduledTask();
} else {
  setupDailyImport();
}
