# 🚀 Quick Google Sheets API Setup (5 minutes)

This is the fastest way to get Google Sheets API working for the Discraft OSL import.

## Option 1: API Key (Simplest - 2 minutes)

### Step 1: Create API Key
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Go to **APIs & Services** > **Library**
4. Search "Google Sheets API" and **Enable** it
5. Go to **APIs & Services** > **Credentials**
6. Click **Create Credentials** > **API Key**
7. Copy the API key

### Step 2: Add to Environment
Add to your `.env` file:
```env
GOOGLE_API_KEY=your-api-key-here
```

### Step 3: Make Sheet Viewable by Link
1. Open your Google Sheet
2. Click **Share**
3. Click **Change to anyone with the link**
4. Set to **Viewer**
5. Click **Done**

**✅ Done! The import will now work.**

---

## Option 2: Service Account (More Secure - 5 minutes)

### Step 1: Create Service Account
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable Google Sheets API (same as above)
3. Go to **APIs & Services** > **Credentials**
4. Click **Create Credentials** > **Service Account**
5. Name it `discraft-reader` and click **Create**
6. Skip optional steps and click **Done**

### Step 2: Create Key
1. Click on the service account you just created
2. Go to **Keys** tab
3. Click **Add Key** > **Create New Key**
4. Choose **JSON** and click **Create**
5. Save the downloaded JSON file

### Step 3: Add to Environment
Copy the entire JSON content and add to your `.env` file:
```env
GOOGLE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"your-project",...}
```

### Step 4: Share Sheet with Service Account
1. Open the downloaded JSON file
2. Find the `client_email` field (looks like `<EMAIL>`)
3. Open your Google Sheet
4. Click **Share**
5. Add the service account email
6. Set to **Viewer**
7. Click **Send**

**✅ Done! The import will work with private sheets.**

---

## 🧪 Test Your Setup

Run this command to test:
```bash
node setupGoogleSheetsAPI.js
```

If it shows ✅ for everything, you're ready to import!

---

## 🎯 Which Option to Choose?

- **API Key**: Faster setup, but sheet must be viewable by anyone with link
- **Service Account**: More secure, sheet stays completely private

Both work perfectly with the Discraft OSL import task! 🎉
