// processMoldVideoUpdatedTask.js
// Enqueue per-disc update tasks when a mold's video_url is added/changed

export default async function processMoldVideoUpdatedTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processMoldVideoUpdatedTask] Processing task ${task.id}`);

  try {
    // Parse payload
    const payload = typeof task.payload === 'string' ? JSON.parse(task.payload) : task.payload || {};
    const moldId = payload.id;
    const videoUrl = payload.video_url;

    if (!moldId) {
      throw new Error('Missing mold id in payload');
    }
    if (!videoUrl) {
      throw new Error('Missing video_url in payload');
    }

    await updateTaskStatus(task.id, 'processing');

    // Find related discs: t_discs join t_mps on mps_id, where t_mps.mold_id = moldId
    // and t_discs.sold_date IS NULL and shopify_uploaded_at IS NOT NULL
    console.log(`[processMoldVideoUpdatedTask] Querying discs for mold ${moldId}`);
    const { data: discs, error: discsError } = await supabase
      .from('t_discs')
      .select('id, mps_id, sold_date, shopify_uploaded_at, t_mps!inner(id, mold_id)')
      .eq('t_mps.mold_id', moldId)
      .is('sold_date', null)
      .not('shopify_uploaded_at', 'is', null);

    if (discsError) {
      throw new Error(`Error fetching related discs: ${discsError.message}`);
    }

    const count = discs?.length || 0;
    console.log(`[processMoldVideoUpdatedTask] Found ${count} discs to update`);

    if (count === 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: 'No discs to update',
        mold_id: moldId,
        enqueued: 0
      });
      return;
    }

    // Enqueue child tasks in chunks
    const tasks = discs.map(d => ({
      task_type: 'update_disc_with_new_mold_video',
      payload: { id: d.id, video_url: videoUrl },
      status: 'pending',
      scheduled_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      enqueued_by: `mold_video_updated_${moldId}`
    }));

    const CHUNK_SIZE = 50;
    let enqueued = 0;
    for (let i = 0; i < tasks.length; i += CHUNK_SIZE) {
      const chunk = tasks.slice(i, i + CHUNK_SIZE);
      const { error: insertError } = await supabase
        .from('t_task_queue')
        .insert(chunk);
      if (insertError) {
        console.error(`[processMoldVideoUpdatedTask] Error inserting chunk ${i / CHUNK_SIZE}:`, insertError);
        await logError(`Failed to enqueue some disc update tasks: ${insertError.message}`, 'processMoldVideoUpdatedTask');
      } else {
        enqueued += chunk.length;
      }
    }

    await updateTaskStatus(task.id, 'completed', {
      message: `Enqueued ${enqueued} update_disc_with_new_mold_video tasks`,
      mold_id: moldId,
      enqueued
    });
  } catch (err) {
    console.error('[processMoldVideoUpdatedTask] Error:', err);
    await logError(err.message, 'processMoldVideoUpdatedTask');
    await updateTaskStatus(task.id, 'error', { message: err.message });
  }
}

