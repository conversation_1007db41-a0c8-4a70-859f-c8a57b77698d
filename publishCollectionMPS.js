import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import minimist from 'minimist';

// ========== 1. Parse Command-Line Arguments ==========
const args = minimist(process.argv.slice(2));
const mpsId = args.id;
if (!mpsId) {
  console.error('No MPS id provided. Use --id=<mpsId>');
  process.exit(1);
}
console.log(`INFO: Received MPS id: ${mpsId}`);

// ========== 2. Initialize Supabase Client ==========
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Missing Supabase URL or key in environment variables.');
  process.exit(1);
}
console.log('INFO: Initializing Supabase client...');
const supabase = createClient(supabaseUrl, supabaseKey);

// ========== 3. Helper Function for Logging Errors ==========
async function logError(errorMessage, context) {
  try {
    const { error } = await supabase
      .from('t_error_logs')
      .insert({ error_message: errorMessage, context });
    if (error) {
      console.error('Failed to log error to t_error_logs:', error);
    }
  } catch (err) {
    console.error('Exception while logging error:', err);
  }
}

// ========== 4. Setup Shopify Endpoint ==========
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
if (!shopifyEndpoint || !shopifyAccessToken) {
  console.error('ERROR: Missing Shopify endpoint or access token in environment variables.');
  process.exit(1);
}
const smartCollectionsEndpoint = shopifyEndpoint.replace('graphql.json', 'smart_collections.json');
console.log(`INFO: Shopify smart collections endpoint: ${smartCollectionsEndpoint}`);

// ========== 5. Wait & Check v_todo_mps ==========
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

console.log("INFO: Waiting 5 seconds for v_todo_mps to update...");
await delay(5000);

console.log(`INFO: Checking if MPS id ${mpsId} is present in v_todo_mps...`);
const { data: todoRecords, error: todoError } = await supabase
  .from('v_todo_mps')
  .select('id')
  .eq('id', mpsId);

if (todoError) {
  const msg = `Checking v_todo_mps: ${JSON.stringify(todoError)}`;
  console.error('ERROR:', msg);
  await logError(msg, 'v_todo_mps check');
  process.exit(1);
}

if (todoRecords && todoRecords.length > 0) {
  const noteMessage = `After delay: MPS id ${mpsId} is present in v_todo_mps and is NOT ready to be processed.`;
  console.log(`INFO: ${noteMessage}`);
  const { error: noteError } = await supabase
    .from('t_mps')
    .update({ shopify_collection_uploaded_notes: noteMessage })
    .eq('id', mpsId);
  if (noteError) {
    console.error(`ERROR: Failed to update shopify_collection_uploaded_notes for MPS id ${mpsId}: ${noteError.message}`);
    await logError(noteError.message, `Updating shopify_collection_uploaded_notes for MPS ${mpsId}`);
  } else {
    console.log(`INFO: Successfully updated shopify_collection_uploaded_notes for MPS id ${mpsId}.`);
  }
  process.exit(0);
}

// ========== 6. Retrieve MPS Record ==========
console.log(`INFO: Fetching MPS record with id ${mpsId} from t_mps...`);
let { data: mpsRecord, error: mpsError } = await supabase
  .from('t_mps')
  .select('*')
  .eq('id', mpsId)
  .single();

if (mpsError) {
  const msg = `Fetching MPS from t_mps: ${JSON.stringify(mpsError)}`;
  console.error('ERROR:', msg);
  await logError(msg, `Fetching MPS ${mpsId}`);
  process.exit(1);
}

// ========== 7. Verify Eligibility for Publishing ==========
// console.log("INFO: Verifying MPS eligibility for publishing...");
// if (mpsRecord.shopify_collection_uploaded_at !== null) {
//   const noteMessage = 'MPS is not eligible for publishing.';
//   console.error(`ERROR: ${noteMessage}`);
//   const { error: noteError } = await supabase
//     .from('t_mps')
//     .update({ shopify_collection_uploaded_notes: noteMessage })
//     .eq('id', mpsId);
//   if (noteError) {
//     console.error(`ERROR: Failed to update shopify_collection_uploaded_notes for MPS id ${mpsRecord.id}: ${noteError.message}`);
//     await logError(noteError.message, `Updating shopify_collection_uploaded_notes for MPS ${mpsId}`);
//   }
//   process.exit(1);
// }

console.log("INFO: MPS is eligible for publishing.");

// ========== 8. Retrieve Associated Records ==========
// 8.1. t_plastics for plastic info.
console.log(`INFO: Fetching associated plastic record for plastic_id ${mpsRecord.plastic_id}...`);
const { data: plasticData, error: plasticError } = await supabase
  .from('t_plastics')
  .select('plastic')
  .eq('id', mpsRecord.plastic_id)
  .single();

if (plasticError) {
  const msg = `Fetching plastic from t_plastics: ${JSON.stringify(plasticError)}`;
  console.error('ERROR:', msg);
  await logError(msg, `Fetching plastic for MPS ${mpsId}`);
  process.exit(1);
}

// 8.2. t_molds for mold info.
console.log(`INFO: Fetching associated mold record for mold_id ${mpsRecord.mold_id}...`);
const { data: moldData, error: moldError } = await supabase
  .from('t_molds')
  .select('mold, type, speed, description_mfg_short, brand_id')
  .eq('id', mpsRecord.mold_id)
  .single();

if (moldError) {
  const msg = `Fetching mold from t_molds: ${JSON.stringify(moldError)}`;
  console.error('ERROR:', msg);
  await logError(msg, `Fetching mold for MPS ${mpsId}`);
  process.exit(1);
}

// 8.3. t_stamps for stamp info.
console.log(`INFO: Fetching associated stamp record for stamp_id ${mpsRecord.stamp_id}...`);
const { data: stampData, error: stampError } = await supabase
  .from('t_stamps')
  .select('stamp, description')
  .eq('id', mpsRecord.stamp_id)
  .single();

if (stampError) {
  const msg = `Fetching stamp from t_stamps: ${JSON.stringify(stampError)}`;
  console.error('ERROR:', msg);
  await logError(msg, `Fetching stamp for MPS ${mpsId}`);
  process.exit(1);
}

// 8.4. t_brands for brand info using moldData.brand_id.
console.log(`INFO: Fetching associated brand record for brand_id ${moldData.brand_id}...`);
const { data: brandData, error: brandError } = await supabase
  .from('t_brands')
  .select('brand')
  .eq('id', moldData.brand_id)
  .single();

if (brandError) {
  const msg = `Fetching brand from t_brands: ${JSON.stringify(brandError)}`;
  console.error('ERROR:', msg);
  await logError(msg, `Fetching brand for MPS ${mpsId}`);
  process.exit(1);
}

// ========== 9. Retrieve Public Image Config ==========
console.log("INFO: Fetching public image server and folder from t_config...");
const { data: configData, error: configError } = await supabase
  .from('t_config')
  .select('key, value')
  .in('key', ['public_image_server', 'folder_mps']);
if (configError) {
  const msg = `Fetching t_config: ${JSON.stringify(configError)}`;
  console.error('ERROR:', msg);
  await logError(msg, `Fetching t_config for MPS ${mpsId}`);
  process.exit(1);
}

const publicImageServerRow = configData.find(row => row.key === 'public_image_server');
const folderMpsRow = configData.find(row => row.key === 'folder_mps');
if (!publicImageServerRow || !folderMpsRow) {
  const msg = `Missing configuration for public_image_server or folder_mps`;
  console.error('ERROR:', msg);
  await logError(msg, `Fetching t_config for MPS ${mpsId}`);
  process.exit(1);
}
const imageSrc = `${publicImageServerRow.value}/${folderMpsRow.value}/${mpsRecord.id}.jpg`;

// ========== 10. Retrieve Handle from v_mps ==========
console.log(`INFO: Fetching handle from v_mps for MPS id ${mpsId}...`);
const { data: vMPSData, error: vMPSerror } = await supabase
  .from('v_mps')
  .select('handle')
  .eq('id', mpsId)
  .single();
if (vMPSerror) {
  console.error(`ERROR: Fetching handle from v_mps: ${vMPSerror.message}`);
  process.exit(1);
}
const handle = vMPSData.handle;
console.log(`INFO: Retrieved handle: ${handle}`);

// ========== 11. Construct Title, Body, and Metafield ==========

// Retrieve the blurb from v_mps.
const { data: mpsBlurbData, error: mpsBlurbError } = await supabase
  .from('v_mps')
  .select('blurb_with_link')
  .eq('id', mpsId)
  .single();
if (mpsBlurbError) {
  console.error("ERROR: Failed to fetch v_mps.blurb_with_link:", mpsBlurbError.message);
  process.exit(1);
}
const mpsBlurb = mpsBlurbData.blurb_with_link;

// Retrieve the blurb from v_molds.
const { data: moldsBlurbData, error: moldsBlurbError } = await supabase
  .from('v_molds')
  .select('blurb_with_link')
  .eq('id', mpsRecord.mold_id)
  .single();
if (moldsBlurbError) {
  console.error("ERROR: Failed to fetch v_molds.blurb_with_link:", moldsBlurbError.message);
  process.exit(1);
}
const moldsBlurb = moldsBlurbData.blurb_with_link;

// Retrieve the blurb from v_plastics.
const { data: plasticsBlurbData, error: plasticsBlurbError } = await supabase
  .from('v_plastics')
  .select('blurb_with_link')
  .eq('id', mpsRecord.plastic_id)
  .single();
if (plasticsBlurbError) {
  console.error("ERROR: Failed to fetch v_plastics.blurb_with_link:", plasticsBlurbError.message);
  process.exit(1);
}
const plasticsBlurb = plasticsBlurbData.blurb_with_link;

// Retrieve the blurb from v_stamps.
const { data: stampsBlurbData, error: stampsBlurbError } = await supabase
  .from('v_stamps')
  .select('blurb')
  .eq('id', mpsRecord.stamp_id)
  .single();
if (stampsBlurbError) {
  console.error("ERROR: Failed to fetch v_stamps.blurb:", stampsBlurbError.message);
  process.exit(1);
}
const stampsBlurb = stampsBlurbData.blurb;

// Concatenate the four values to form the body HTML.
const bodyHtml = mpsBlurb + moldsBlurb + plasticsBlurb + stampsBlurb;

// Construct the title as before.
const title = `${brandData.brand} ${plasticData.plastic} ${moldData.mold} ${moldData.type}${stampData.stamp.toLowerCase() === "stock" ? "" : " with " + stampData.stamp + " Stamp"} - Speed ${moldData.speed}`;

// Construct the metafield description (unchanged).
const metafieldDescriptionTag = `${mpsRecord.description || ("Plastic: " + plasticData.plastic)} | Mold: ${moldData.description_mfg_short || moldData.mold} | Stamp: ${stampData.stamp.toLowerCase() === "stock" ? "Stock" : (stampData.description || stampData.stamp)}`;

console.log("INFO: Constructed Title:", title);
console.log("INFO: Constructed Body HTML:", bodyHtml);
console.log("INFO: Constructed Metafield Description Tag:", metafieldDescriptionTag);




// Aggregate data for the payload.
const mpsData = {
  id: mpsRecord.id,
  handle,
  title,
  bodyHtml,
  imageSrc,
  mold: moldData.mold,
  plastic: plasticData.plastic,
  stamp: stampData.stamp,
  metafieldDescriptionTag
};

// ========== 12. Create Shopify Smart Collection ==========


// Helper function to fetch an existing smart collection by handle
async function getShopifySmartCollectionByHandle(handle) {
  // Build a search endpoint using the handle parameter.
  // (Note: adjust this URL based on your Shopify API version if needed.)
  const searchEndpoint = smartCollectionsEndpoint.replace(
    'smart_collections.json',
    `smart_collections.json?handle=${handle}`
  );
  const response = await fetch(searchEndpoint, {
    headers: { 'X-Shopify-Access-Token': shopifyAccessToken }
  });
  const result = await response.json();
  if (result.smart_collections && result.smart_collections.length > 0) {
    return result.smart_collections[0];
  }
  return null;
}

// Main function that creates or, if needed, updates the smart collection
async function createOrUpdateShopifySmartCollection(mpsData) {
  const payload = {
    smart_collection: {
      title: mpsData.title,
      body_html: mpsData.bodyHtml,
      handle: mpsData.handle,
      sort_order: "created-desc",
      template_suffix: "pms-collection",
      published: true,
      published_scope: "global",
      disjunctive: false,
      rules: [
        {
          column: "tag",
          relation: "equals",
          condition: "disc_mold_" + mpsData.mold
        },
        {
          column: "tag",
          relation: "equals",
          condition: "disc_plastic_" + mpsData.plastic
        },
        {
          column: "tag",
          relation: "equals",
          condition: "disc_stamp_" + mpsData.stamp
        },
        {
          column: "variant_inventory",
          relation: "greater_than",
          condition: "0"
        }
      ],
      image: {
        src: mpsData.imageSrc
      }
    }
  };

  console.log("INFO: Final smart collection payload to Shopify:\n", JSON.stringify(payload, null, 2));

  // Attempt to create the smart collection.
  let response = await fetch(smartCollectionsEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken
    },
    body: JSON.stringify(payload)
  });

  let result = await response.json();

  // If creation failed, check if it's because the handle already exists.
  if (!response.ok) {
    const errorMsg = JSON.stringify(result);
    if (errorMsg.includes("handle") && errorMsg.includes("has already been taken")) {
      console.log("INFO: Smart collection handle already exists. Attempting to update the existing smart collection.");
      // Fetch the existing smart collection by handle.
      const existingCollection = await getShopifySmartCollectionByHandle(mpsData.handle);
      if (existingCollection && existingCollection.id) {
        // Remove the handle property from the payload since it cannot be updated.
        delete payload.smart_collection.handle;
        // Build the update endpoint using the existing collection's id.
        const updateEndpoint = smartCollectionsEndpoint.replace(
          'smart_collections.json',
          `smart_collections/${existingCollection.id}.json`
        );
        response = await fetch(updateEndpoint, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': shopifyAccessToken
          },
          body: JSON.stringify(payload)
        });
        const updateResult = await response.json();
        if (!response.ok) {
          throw new Error(`Error updating smart collection for MPS id "${mpsData.id}": ${JSON.stringify(updateResult)}`);
        }
        return updateResult.smart_collection;
      } else {
        throw new Error(`Smart collection handle exists but unable to fetch the existing collection by handle: ${mpsData.handle}`);
      }
    } else {
      throw new Error(`Error creating smart collection for MPS id "${mpsData.id}": ${errorMsg}`);
    }
  }
  return result.smart_collection;
}



console.log("INFO: Creating Shopify smart collection for MPS...");
try {
  const collection = await createOrUpdateShopifySmartCollection(mpsData);
  console.log(`INFO: Successfully created collection for MPS id ${mpsRecord.id}:`);
  console.log(collection);

  // ========== 13. Update t_mps Record with Success Note ==========
  console.log(`INFO: Fetching current updated_at and updated_by for MPS id ${mpsId}...`);
  const { data: currentRecord, error: fetchError } = await supabase
    .from('t_mps')
    .select('updated_at, updated_by')
    .eq('id', mpsId)
    .single();
  if (fetchError) {
    console.error('ERROR: Failed to fetch current update metadata:', fetchError.message);
  }

  const successNote = "Success! MPS Collection created on Shopify through webhook > .js > Shopify API.";
  console.log("INFO: Updating t_mps record with success note and current timestamp, preserving update metadata...");
  const updatePayload = {
    shopify_collection_uploaded_at: new Date().toISOString(),
    shopify_collection_uploaded_notes: successNote
  };
  if (currentRecord) {
    updatePayload.updated_at = currentRecord.updated_at;
    updatePayload.updated_by = currentRecord.updated_by;
  }

  const { error: updateError } = await supabase
    .from('t_mps')
    .update(updatePayload)
    .eq('id', mpsId);
  if (updateError) {
    const msg = `Failed to update shopify_collection_uploaded_at for MPS id ${mpsRecord.id}: ${updateError.message}`;
    console.error('ERROR:', msg);
    await logError(msg, `Updating t_mps for MPS ${mpsId}`);
    process.exit(1);
  }
  console.log(`INFO: Updated shopify_collection_uploaded_at for MPS id ${mpsRecord.id}.`);
} catch (err) {
  const msg = `Failed to create collection for MPS id ${mpsRecord.id}: ${err.message}`;
  console.error('ERROR:', msg);
  await logError(msg, `Creating collection for MPS ${mpsId}`);
  const { error: noteError } = await supabase
    .from('t_mps')
    .update({ shopify_collection_uploaded_notes: err.message })
    .eq('id', mpsId);
  if (noteError) {
    console.error(`ERROR: Failed to update shopify_collection_uploaded_notes for MPS id ${mpsRecord.id}: ${noteError.message}`);
    await logError(noteError.message, `Updating shopify_collection_uploaded_notes for MPS ${mpsId}`);
  }
  process.exit(1);
}

console.log("INFO: publishCollectionMPS process completed successfully.");
// ========== 14. Global Error Handler ==========
process.on('unhandledRejection', async (err) => {
  console.error('ERROR: Unexpected error:', err);
  await logError(err.message, 'Unexpected error in main function of publishCollectionMPS.js');
  process.exit(1);
});
