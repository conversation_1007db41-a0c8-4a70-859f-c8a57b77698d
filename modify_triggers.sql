-- Drop the existing triggers that have INSERT portions
DROP TRIGGER IF EXISTS tr_enqueue_match_disc_to_asins_insert ON t_discs;
DROP TRIGGER IF EXISTS tr_enqueue_set_carry_cost_insert ON t_discs;
DROP TRIGGER IF EXISTS tr_enqueue_match_disc_to_osl ON t_discs;
DROP TRIGGER IF EXISTS trg_queue_generate_disc_title_pull_and_handle ON t_discs;

-- Check if the update triggers already exist, and only create them if they don't

-- 1. Check and create tr_enqueue_match_disc_to_osl_update and tr_enqueue_match_disc_to_osl_delete
DO $$
BEGIN
    -- Check if tr_enqueue_match_disc_to_osl_update exists
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'tr_enqueue_match_disc_to_osl_update') THEN
        CREATE TRIGGER tr_enqueue_match_disc_to_osl_update
        AFTER UPDATE OF mps_id, weight, color_id, location, order_sheet_line_id ON t_discs
        FOR EACH ROW
        EXECUTE FUNCTION fn_enqueue_match_disc_to_osl();
    END IF;

    -- Check if tr_enqueue_match_disc_to_osl_delete exists
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'tr_enqueue_match_disc_to_osl_delete') THEN
        CREATE TRIGGER tr_enqueue_match_disc_to_osl_delete
        AFTER DELETE ON t_discs
        FOR EACH ROW
        EXECUTE FUNCTION fn_enqueue_match_disc_to_osl();
    END IF;
END $$;

-- 2. Check and create trg_queue_generate_disc_title_pull_and_handle_update
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trg_queue_generate_disc_title_pull_and_handle_update') THEN
        CREATE TRIGGER trg_queue_generate_disc_title_pull_and_handle_update
        AFTER UPDATE OF mps_id, weight, color_id, color_modifier, location ON t_discs
        FOR EACH ROW
        EXECUTE FUNCTION fn_queue_generate_disc_title_pull_and_handle();
    END IF;
END $$;

-- 3. Create the new trigger for new_t_discs_record if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'tr_enqueue_new_t_discs_record') THEN
        CREATE TRIGGER tr_enqueue_new_t_discs_record
        AFTER INSERT ON t_discs
        FOR EACH ROW
        EXECUTE FUNCTION fn_enqueue_new_t_discs_record();
    END IF;
END $$;
