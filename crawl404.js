// crawl404.js
import fetch from 'node-fetch';
import * as cheerio from 'cheerio';
import pLimit from 'p-limit';
import { URL } from 'url';

const START_URL   = 'https://www.dzdiscs.com/'; // change to your homepage
const MAX_DEPTH   = 1;                          // 0 = just the homepage HTML
const CONCURRENCY = 6;                          // tweak for bandwidth
const TIMEOUT_MS  = 15000;

const seen  = new Map(); // url -> { status, sources: Set<string> }
const limit = pLimit(CONCURRENCY);

async function crawl(url, from = null, depth = 0) {
  if (seen.has(url)) {
    if (from) seen.get(url).sources.add(from);
    return;
  }
  seen.set(url, { status: 'fetching', sources: new Set(from ? [from] : []) });

  try {
    console.log(`Fetching: ${url}`);
    const res = await fetch(url, { timeout: TIMEOUT_MS, redirect: 'follow' });
    seen.get(url).status = res.status;

    // parse links only if HTML, < MAX_DEPTH, and response is OK (<400)
    if (
      depth < MAX_DEPTH &&
      res.headers.get('content-type')?.startsWith('text/html') &&
      res.status < 400
    ) {
      const html = await res.text();
      const $    = cheerio.load(html);
      const base = new URL(url);

      const promises = [];
      $('a[href]').each((_, a) => {
        const href = $(a).attr('href').split('#')[0]; // strip fragments
        if (!href || href.startsWith('mailto:') || href.startsWith('tel:')) return;
        try {
          const next = new URL(href, base).toString();
          if (next.startsWith(base.origin)) {
            promises.push(limit(() => crawl(next, url, depth + 1)));
          }
        } catch (err) {
          console.error(`Invalid URL: ${href} on page ${url}`);
        }
      });

      // Wait for all links from this page to be processed
      await Promise.all(promises);
    }
  } catch (err) {
    console.error(`Error fetching ${url}: ${err.message}`);
    seen.get(url).status = 'error';
  }
}

// Start the crawl and wait for all pending tasks to complete
const initialCrawl = limit(() => crawl(START_URL));
await initialCrawl;

// ---------- report ----------
console.log('\nBroken links (homepage depth ' + MAX_DEPTH + '):');
for (const [url, { status, sources }] of seen) {
  if (typeof status === 'number' && status < 400) continue; // skip OKs
  console.log(`${url}  ->  ${status}`);
  for (const src of sources) console.log(`   referenced from: ${src}`);
}
