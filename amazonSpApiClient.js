// amazonSpApiClient.js - Amazon SP-API Client for listing management
import dotenv from 'dotenv';
import axios from 'axios';
import crypto from 'crypto';

// Load environment variables
dotenv.config();

class AmazonSpApiClient {
  constructor() {
    this.clientId = process.env.AMAZON_CLIENT_ID;
    this.clientSecret = process.env.AMAZON_CLIENT_SECRET;
    this.refreshToken = process.env.AMAZON_REFRESH_TOKEN;
    this.sellerId = process.env.AMAZON_SELLER_ID;
    this.marketplaceId = process.env.AMAZON_MARKETPLACE_ID || 'ATVPDKIKX0DER'; // US marketplace
    this.region = process.env.AMAZON_REGION || 'us-east-1';
    this.endpoint = process.env.AMAZON_SP_API_ENDPOINT || 'https://sellingpartnerapi-na.amazon.com';
    
    // Validate required credentials
    if (!this.clientId || !this.clientSecret || !this.refreshToken || !this.sellerId) {
      throw new Error('Missing required Amazon SP-API credentials in environment variables');
    }
    
    this.accessToken = null;
    this.tokenExpiry = null;
  }

  /**
   * Get a fresh access token using the refresh token
   */
  async getAccessToken() {
    // Return cached token if still valid (with 5 minute buffer)
    if (this.accessToken && this.tokenExpiry && Date.now() < this.tokenExpiry - 300000) {
      return this.accessToken;
    }

    try {
      const response = await axios.post('https://api.amazon.com/auth/o2/token', {
        grant_type: 'refresh_token',
        refresh_token: this.refreshToken,
        client_id: this.clientId,
        client_secret: this.clientSecret
      }, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });

      this.accessToken = response.data.access_token;
      this.tokenExpiry = Date.now() + (response.data.expires_in * 1000);
      
      console.log('[amazonSpApiClient] Successfully obtained access token');
      return this.accessToken;
    } catch (error) {
      console.error('[amazonSpApiClient] Error getting access token:', error.response?.data || error.message);
      throw new Error(`Failed to get Amazon access token: ${error.response?.data?.error_description || error.message}`);
    }
  }

  /**
   * Create AWS Signature Version 4 for SP-API requests
   */
  createAwsSignature(method, path, queryString, headers, payload) {
    const accessKeyId = 'AKIA6ODU2NRFQ7XKQZQX'; // This would need to be your actual AWS access key
    const secretAccessKey = 'your-secret-key'; // This would need to be your actual AWS secret key
    const service = 'execute-api';
    const region = this.region;
    
    const now = new Date();
    const amzDate = now.toISOString().replace(/[:\-]|\.\d{3}/g, '');
    const dateStamp = amzDate.substr(0, 8);
    
    // Create canonical request
    const canonicalUri = path;
    const canonicalQuerystring = queryString || '';
    const canonicalHeaders = Object.keys(headers)
      .sort()
      .map(key => `${key.toLowerCase()}:${headers[key]}\n`)
      .join('');
    const signedHeaders = Object.keys(headers)
      .sort()
      .map(key => key.toLowerCase())
      .join(';');
    
    const payloadHash = crypto.createHash('sha256').update(payload || '').digest('hex');
    
    const canonicalRequest = [
      method,
      canonicalUri,
      canonicalQuerystring,
      canonicalHeaders,
      signedHeaders,
      payloadHash
    ].join('\n');
    
    // Create string to sign
    const algorithm = 'AWS4-HMAC-SHA256';
    const credentialScope = `${dateStamp}/${region}/${service}/aws4_request`;
    const stringToSign = [
      algorithm,
      amzDate,
      credentialScope,
      crypto.createHash('sha256').update(canonicalRequest).digest('hex')
    ].join('\n');
    
    // Calculate signature
    const kDate = crypto.createHmac('sha256', `AWS4${secretAccessKey}`).update(dateStamp).digest();
    const kRegion = crypto.createHmac('sha256', kDate).update(region).digest();
    const kService = crypto.createHmac('sha256', kRegion).update(service).digest();
    const kSigning = crypto.createHmac('sha256', kService).update('aws4_request').digest();
    const signature = crypto.createHmac('sha256', kSigning).update(stringToSign).digest('hex');
    
    // Create authorization header
    const authorizationHeader = `${algorithm} Credential=${accessKeyId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;
    
    return {
      'Authorization': authorizationHeader,
      'X-Amz-Date': amzDate
    };
  }

  /**
   * Make an authenticated request to the SP-API
   */
  async makeSpApiRequest(method, path, queryParams = {}, body = null) {
    const accessToken = await this.getAccessToken();
    
    // Build query string
    const queryString = Object.keys(queryParams)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
      .join('&');
    
    const url = `${this.endpoint}${path}${queryString ? '?' + queryString : ''}`;
    
    const headers = {
      'Content-Type': 'application/json',
      'x-amz-access-token': accessToken,
      'x-amz-date': new Date().toISOString().replace(/[:\-]|\.\d{3}/g, ''),
      'host': new URL(this.endpoint).hostname
    };
    
    // Add AWS signature (simplified - in production you'd need proper AWS credentials)
    // For now, we'll rely on the access token authentication
    
    try {
      const response = await axios({
        method,
        url,
        headers,
        data: body
      });
      
      return response.data;
    } catch (error) {
      console.error(`[amazonSpApiClient] SP-API request failed:`, error.response?.data || error.message);
      throw new Error(`SP-API request failed: ${error.response?.data?.errors?.[0]?.message || error.message}`);
    }
  }

  /**
   * Delete a listing by SKU
   */
  async deleteListing(sku, marketplaceIds = null) {
    if (!sku) {
      throw new Error('SKU is required to delete a listing');
    }
    
    const marketplaces = marketplaceIds || [this.marketplaceId];
    const path = `/listings/2020-09-01/items/${this.sellerId}/${encodeURIComponent(sku)}`;
    const queryParams = {
      marketplaceIds: marketplaces.join(',')
    };
    
    console.log(`[amazonSpApiClient] Deleting listing for SKU: ${sku} in marketplaces: ${marketplaces.join(',')}`);
    
    try {
      const result = await this.makeSpApiRequest('DELETE', path, queryParams);
      console.log(`[amazonSpApiClient] Successfully deleted listing for SKU: ${sku}`);
      return result;
    } catch (error) {
      console.error(`[amazonSpApiClient] Failed to delete listing for SKU: ${sku}`, error.message);
      throw error;
    }
  }

  /**
   * Get listing information by SKU
   */
  async getListing(sku, marketplaceIds = null) {
    if (!sku) {
      throw new Error('SKU is required to get listing information');
    }
    
    const marketplaces = marketplaceIds || [this.marketplaceId];
    const path = `/listings/2020-09-01/items/${this.sellerId}/${encodeURIComponent(sku)}`;
    const queryParams = {
      marketplaceIds: marketplaces.join(',')
    };
    
    console.log(`[amazonSpApiClient] Getting listing for SKU: ${sku}`);
    
    try {
      const result = await this.makeSpApiRequest('GET', path, queryParams);
      return result;
    } catch (error) {
      console.error(`[amazonSpApiClient] Failed to get listing for SKU: ${sku}`, error.message);
      throw error;
    }
  }

  /**
   * Test the connection to Amazon SP-API
   */
  async testConnection() {
    try {
      console.log('[amazonSpApiClient] Testing Amazon SP-API connection...');
      
      // Test by getting access token
      const token = await this.getAccessToken();
      
      // Test by making a simple API call (get seller info)
      const path = '/sellers/v1/marketplaceParticipations';
      const result = await this.makeSpApiRequest('GET', path);
      
      console.log('[amazonSpApiClient] Connection test successful');
      return {
        success: true,
        message: 'Successfully connected to Amazon SP-API',
        marketplaces: result.payload?.length || 0
      };
    } catch (error) {
      console.error('[amazonSpApiClient] Connection test failed:', error.message);
      return {
        success: false,
        message: `Connection test failed: ${error.message}`
      };
    }
  }
}

export default AmazonSpApiClient;
