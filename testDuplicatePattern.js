import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Shopify GraphQL Admin API credentials
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

async function testDuplicatePattern() {
  console.log('Testing duplicate pattern detection...');
  
  // Regular expression to match handles ending in d######-1 or d######-2
  const duplicatePattern = /d\d{6}-[12]$/;
  
  // Test the regex with some sample handles
  const testHandles = [
    'discmania-c-line-md5-171-74g-blue-d423546-1',
    'discmania-c-line-md5-171-74g-blue-d423546-2',
    'normal-product-handle',
    'another-product-d123456-1',
    'product-d999999-2',
    'not-a-duplicate-d12345',
    'not-a-duplicate-d1234567-1'
  ];
  
  console.log('\nTesting regex pattern:');
  testHandles.forEach(handle => {
    const matches = duplicatePattern.test(handle);
    console.log(`${matches ? '✅' : '❌'} ${handle}`);
  });
  
  // Now test with a small sample of real products
  const query = `
    query getProducts($first: Int!) {
      products(first: $first) {
        edges {
          node {
            id
            title
            handle
            status
            createdAt
          }
        }
      }
    }
  `;

  try {
    console.log('\nFetching first 50 products to test pattern...');
    
    const response = await fetch(shopifyEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      },
      body: JSON.stringify({ 
        query, 
        variables: { first: 50 }
      })
    });

    const result = await response.json();
    
    if (result.errors) {
      console.error('GraphQL errors:', result.errors);
      return;
    }

    const products = result.data.products.edges;
    console.log(`Checking ${products.length} products for duplicate patterns...`);
    
    let duplicateCount = 0;
    products.forEach(edge => {
      const product = edge.node;
      if (duplicatePattern.test(product.handle)) {
        duplicateCount++;
        console.log(`🔍 Found duplicate: ${product.handle} - ${product.title}`);
      }
    });
    
    console.log(`\nFound ${duplicateCount} duplicates in first 50 products`);
    
    if (duplicateCount === 0) {
      console.log('No duplicates found in sample. This could mean:');
      console.log('1. There are no duplicates in the first 50 products');
      console.log('2. The duplicate pattern might be different than expected');
      console.log('3. Duplicates exist but are further down in the product list');
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

testDuplicatePattern();
