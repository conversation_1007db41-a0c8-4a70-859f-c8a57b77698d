-- Function to enqueue a task for stamp updates
CREATE OR REPLACE FUNCTION fn_queue_stamp_update_downstream_generate_mps_fields()
RETURNS TRIGGER AS $$
BEGIN
    -- Only proceed if stamp or description has changed
    IF (OLD.stamp <> NEW.stamp OR OLD.description <> NEW.description OR 
        (OLD.stamp IS NULL AND NEW.stamp IS NOT NULL) OR 
        (OLD.description IS NULL AND NEW.description IS NOT NULL)) THEN
        
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'stamp_update_downstream_generate_mps_fields',
            jsonb_build_object('id', NEW.id),
            'pending',
            NOW(),
            NOW()
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trg_queue_stamp_update_downstream_generate_mps_fields ON t_stamps;

CREATE TRIGGER trg_queue_stamp_update_downstream_generate_mps_fields
AFTER UPDATE OF stamp, description ON t_stamps
FOR EACH ROW
EXECUTE FUNCTION fn_queue_stamp_update_downstream_generate_mps_fields();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger trg_queue_stamp_update_downstream_generate_mps_fields has been created.';
END $$;
