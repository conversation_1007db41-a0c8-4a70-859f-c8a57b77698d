require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function resetTask302684() {
  try {
    console.log('Resetting task 302684 to pending status...');
    
    // Reset the task to pending status so it can be processed again with the fixed code
    const { data: updatedTask, error: updateError } = await supabase
      .from('t_task_queue')
      .update({
        status: 'pending',
        locked_at: null,
        locked_by: null,
        result: null,
        processed_at: null
      })
      .eq('id', 302684)
      .select();
    
    if (updateError) {
      console.error('Error resetting task:', updateError);
      return;
    }
    
    console.log('✅ Task 302684 has been reset to pending status');
    console.log('Updated task:', updatedTask[0]);
    
    console.log('\n🔄 The task will now be picked up by the worker daemon and processed with the fixed code.');
    console.log('The relationship ambiguity error should be resolved.');
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

resetTask302684();
