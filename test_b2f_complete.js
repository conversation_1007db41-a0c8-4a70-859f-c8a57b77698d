// Complete B2F workflow test
async function testB2FWorkflow() {
  console.log('🚀 Testing Complete B2F Workflow\n');

  // Test 1: Count endpoint
  console.log('1️⃣ Testing B2F Count...');
  try {
    const countResponse = await fetch('http://localhost:3001/api/b2f/count');
    const countData = await countResponse.json();
    console.log('✅ Count:', countData.count, 'discs available');
  } catch (err) {
    console.log('❌ Count test failed:', err.message);
  }

  // Test 2: Records endpoint
  console.log('\n2️⃣ Testing B2F Records...');
  try {
    const recordsResponse = await fetch('http://localhost:3001/api/b2f/records');
    const recordsData = await recordsResponse.json();
    console.log('✅ Records:', recordsData.records?.length || 0, 'discs found');
    
    if (recordsData.records && recordsData.records.length > 0) {
      console.log('📋 Sample disc:', recordsData.records[0].disc);
      console.log('📋 Sample OSL:', recordsData.records[0].osl);
      
      // Test 3: Update location (using first disc)
      console.log('\n3️⃣ Testing Location Update...');
      const testDisc = recordsData.records[0];
      const today = new Date();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      const b2fLocation = `B2F ${month}-${day}`;
      
      try {
        const updateResponse = await fetch('http://localhost:3001/api/b2f/update-location', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            discId: testDisc.disc,
            location: b2fLocation
          })
        });
        
        const updateData = await updateResponse.json();
        if (updateData.success) {
          console.log('✅ Location updated successfully to:', b2fLocation);
          
          // Test 4: PDF generation
          console.log('\n4️⃣ Testing PDF Generation...');
          try {
            const pdfResponse = await fetch('http://localhost:3001/api/b2f/generate-pdf', {
              method: 'POST'
            });
            
            if (pdfResponse.ok) {
              const blob = await pdfResponse.blob();
              console.log('✅ PDF generated successfully, size:', blob.size, 'bytes');
            } else {
              const errorData = await pdfResponse.json();
              console.log('❌ PDF generation failed:', errorData.error);
            }
          } catch (err) {
            console.log('❌ PDF test failed:', err.message);
          }
          
        } else {
          console.log('❌ Location update failed:', updateData.error);
        }
      } catch (err) {
        console.log('❌ Update test failed:', err.message);
      }
    } else {
      console.log('ℹ️ No discs available for testing location update');
    }
  } catch (err) {
    console.log('❌ Records test failed:', err.message);
  }

  console.log('\n🎉 B2F Workflow Test Complete!');
  console.log('\n📋 Manual Testing Steps:');
  console.log('1. Open http://localhost:3001/admin.html');
  console.log('2. Click the "B2F" tab');
  console.log('3. Verify count loads automatically');
  console.log('4. Click "Pick B2Fs" to see table');
  console.log('5. Click action button on a disc');
  console.log('6. Click "Print B2F" to generate PDF');
}

testB2FWorkflow().catch(console.error);
