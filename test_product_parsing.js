import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Cache for lookup data
let brandsCache = {};
let moldsCache = {};
let plasticsCache = {};

async function loadLookupData() {
    console.log('Loading lookup data for testing...');
    
    // Load all brands
    const { data: brands, error: brandsError } = await supabase
        .from('t_brands')
        .select('id, brand');
    
    if (brandsError) {
        throw new Error(`Error loading brands: ${brandsError.message}`);
    }
    
    // Create brand lookup
    brands.forEach(brand => {
        brandsCache[brand.brand] = brand.id;
    });
    
    // Load all molds with their brand_id
    const { data: molds, error: moldsError } = await supabase
        .from('t_molds')
        .select('mold, brand_id');
    
    if (moldsError) {
        throw new Error(`Error loading molds: ${moldsError.message}`);
    }
    
    // Group molds by brand_id
    molds.forEach(mold => {
        if (!moldsCache[mold.brand_id]) {
            moldsCache[mold.brand_id] = [];
        }
        moldsCache[mold.brand_id].push(mold.mold);
    });
    
    // Load all plastics with their brand_id
    const { data: plastics, error: plasticsError } = await supabase
        .from('t_plastics')
        .select('plastic, brand_id');
    
    if (plasticsError) {
        throw new Error(`Error loading plastics: ${plasticsError.message}`);
    }
    
    // Group plastics by brand_id
    plastics.forEach(plastic => {
        if (!plasticsCache[plastic.brand_id]) {
            plasticsCache[plastic.brand_id] = [];
        }
        plasticsCache[plastic.brand_id].push(plastic.plastic);
    });
    
    console.log(`Loaded ${brands.length} brands, ${molds.length} molds, ${plastics.length} plastics`);
}

function parseProductTitle(productTitle, brandId) {
    if (!productTitle || typeof productTitle !== 'string') {
        return { mold: null, plastic: null, stamp: null };
    }
    
    const title = productTitle.trim();
    
    // Skip non-disc products
    if (title.includes('Set') || title.includes('Mystery') || title.includes('Box') || 
        title.includes('DyeMax')) {
        return { mold: null, plastic: null, stamp: null };
    }
    
    // Get molds and plastics for this brand
    const brandMolds = moldsCache[brandId] || [];
    const brandPlastics = plasticsCache[brandId] || [];
    
    let foundMold = null;
    let foundPlastic = null;
    let foundStamp = 'Stock';
    
    // Standard pattern: "Plastic Mold - Stamp" or "Plastic Mold"
    
    // Find plastic at the beginning (prioritize longer matches)
    const sortedPlastics = [...brandPlastics].sort((a, b) => b.length - a.length);
    for (const plastic of sortedPlastics) {
        // Check if title starts with this plastic followed by space or is exactly this plastic
        if (title.startsWith(plastic + ' ') || title === plastic) {
            foundPlastic = plastic;
            break;
        }
    }
    
    if (foundPlastic) {
        // Remove plastic from title to find mold and stamp
        let remainingTitle = title.substring(foundPlastic.length).trim();
        
        // Check if there's a dash indicating a stamp
        const dashIndex = remainingTitle.indexOf(' - ');
        let moldPart = remainingTitle;
        let stampPart = '';
        
        if (dashIndex !== -1) {
            moldPart = remainingTitle.substring(0, dashIndex).trim();
            stampPart = remainingTitle.substring(dashIndex + 3).trim();
        }
        
        // Find mold in the mold part
        const sortedMolds = [...brandMolds].sort((a, b) => b.length - a.length);
        for (const mold of sortedMolds) {
            if (moldPart.startsWith(mold + ' ') || moldPart === mold) {
                foundMold = mold;
                break;
            }
        }
        
        // Set stamp
        if (stampPart) {
            foundStamp = stampPart;
        } else if (foundMold && moldPart.length > foundMold.length) {
            // Check if there's additional text after mold (without dash)
            const afterMold = moldPart.substring(foundMold.length).trim();
            if (afterMold) {
                foundStamp = afterMold;
            }
        }
    }
    
    return {
        mold: foundMold,
        plastic: foundPlastic,
        stamp: foundStamp
    };
}

async function testParsing() {
    console.log('Testing product title parsing...\n');
    
    try {
        await loadLookupData();
        
        // Test cases with expected results
        const testCases = [
            {
                title: 'BioFuzion Defender - Chris Clemons 2023',
                brand: 'Dynamic Discs',
                expected: { plastic: 'BioFuzion', mold: 'Defender', stamp: 'Chris Clemons 2023' }
            },
            {
                title: 'K3 Tuff',
                brand: 'Kastaplast',
                expected: { plastic: 'K3', mold: 'Tuff', stamp: 'Stock' }
            },
            {
                title: 'VIP Tide',
                brand: 'Westside',
                expected: { plastic: 'VIP', mold: 'Tide', stamp: 'Stock' }
            },
            {
                title: 'Classic Swirl Judge - Judgement Day',
                brand: 'Dynamic Discs',
                expected: { plastic: 'Classic Swirl', mold: 'Judge', stamp: 'Judgement Day' }
            },
            {
                title: 'Lucid Moonshine Deputy - Timehop',
                brand: 'Dynamic Discs',
                expected: { plastic: 'Lucid Moonshine Glow', mold: 'Deputy', stamp: 'Timehop' }
            },
            {
                title: 'Prime Truth',
                brand: 'Dynamic Discs',
                expected: { plastic: 'Prime', mold: 'Truth', stamp: 'Stock' }
            }
        ];
        
        console.log('Test Results:');
        console.log('=============');
        
        for (const testCase of testCases) {
            const brandId = brandsCache[testCase.brand];
            
            if (!brandId) {
                console.log(`❌ Brand "${testCase.brand}" not found`);
                continue;
            }
            
            const result = parseProductTitle(testCase.title, brandId);
            
            console.log(`\nTitle: "${testCase.title}" [${testCase.brand}]`);
            console.log(`Expected: Plastic="${testCase.expected.plastic}", Mold="${testCase.expected.mold}", Stamp="${testCase.expected.stamp}"`);
            console.log(`Actual:   Plastic="${result.plastic || 'NOT FOUND'}", Mold="${result.mold || 'NOT FOUND'}", Stamp="${result.stamp || 'NOT FOUND'}"`);
            
            const plasticMatch = result.plastic === testCase.expected.plastic;
            const moldMatch = result.mold === testCase.expected.mold;
            const stampMatch = result.stamp === testCase.expected.stamp;
            
            if (plasticMatch && moldMatch && stampMatch) {
                console.log('✅ PASS');
            } else {
                console.log('❌ FAIL');
                if (!plasticMatch) console.log(`  - Plastic mismatch`);
                if (!moldMatch) console.log(`  - Mold mismatch`);
                if (!stampMatch) console.log(`  - Stamp mismatch`);
            }
        }
        
        // Show available molds and plastics for debugging
        console.log('\n\nAvailable data for debugging:');
        console.log('=============================');
        
        const ddBrandId = brandsCache['Dynamic Discs'];
        const kastaBrandId = brandsCache['Kastaplast'];
        const westsideBrandId = brandsCache['Westside'];
        
        if (ddBrandId) {
            console.log(`\nDynamic Discs plastics: ${(plasticsCache[ddBrandId] || []).slice(0, 10).join(', ')}...`);
            console.log(`Dynamic Discs molds: ${(moldsCache[ddBrandId] || []).slice(0, 10).join(', ')}...`);
        }
        
        if (kastaBrandId) {
            console.log(`\nKastaplast plastics: ${(plasticsCache[kastaBrandId] || []).join(', ')}`);
            console.log(`Kastaplast molds: ${(moldsCache[kastaBrandId] || []).join(', ')}`);
        }
        
        if (westsideBrandId) {
            console.log(`\nWestside plastics: ${(plasticsCache[westsideBrandId] || []).join(', ')}`);
            console.log(`Westside molds: ${(moldsCache[westsideBrandId] || []).join(', ')}`);
        }
        
    } catch (error) {
        console.error('Test failed:', error);
    }
}

// Run the test
testParsing();
