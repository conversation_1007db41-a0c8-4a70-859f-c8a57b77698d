// testDiscUpdatedWorkflow.js - Test script for the disc updated workflow
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import { enqueueDiscUpdatedNeedToResetTask } from './enqueueDiscUpdatedNeedToResetTask.js';

// Create a Supabase client using environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDiscUpdatedWorkflow() {
  console.log('🧪 Testing Disc Updated Workflow');
  console.log('================================');

  try {
    // Get a test disc ID from the database
    console.log('📋 Finding a test disc...');
    const { data: testDisc, error: discError } = await supabase
      .from('t_discs')
      .select('id, shopify_sku, sold_date, sold_channel')
      .limit(1)
      .single();

    if (discError) {
      console.error('❌ Error finding test disc:', discError);
      return;
    }

    if (!testDisc) {
      console.error('❌ No test disc found in database');
      return;
    }

    const discId = testDisc.id;
    console.log(`✅ Found test disc: ID ${discId}`);
    console.log(`   Current shopify_sku: ${testDisc.shopify_sku || 'null'}`);
    console.log(`   Current sold_date: ${testDisc.sold_date || 'null'}`);
    console.log(`   Current sold_channel: ${testDisc.sold_channel || 'null'}`);

    // Enqueue the main task
    console.log('\n🚀 Enqueueing disc_updated_need_to_reset task...');
    const task = await enqueueDiscUpdatedNeedToResetTask(discId, testDisc.sold_date);
    console.log(`✅ Task enqueued successfully: ID ${task.id}`);
    console.log(`   Scheduled for: ${task.scheduled_at}`);
    console.log(`   Payload includes sold_date: ${task.payload.sold_date}`);

    // Wait a moment and check the task queue
    console.log('\n⏳ Waiting 2 seconds to check task queue...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check what tasks were created
    console.log('\n📊 Checking task queue for related tasks...');
    const { data: relatedTasks, error: tasksError } = await supabase
      .from('t_task_queue')
      .select('id, task_type, status, scheduled_at, payload')
      .or(`id.eq.${task.id},enqueued_by.eq.disc_updated_need_to_reset`)
      .order('scheduled_at', { ascending: true });

    if (tasksError) {
      console.error('❌ Error fetching related tasks:', tasksError);
      return;
    }

    console.log(`✅ Found ${relatedTasks.length} related tasks:`);
    relatedTasks.forEach((t, index) => {
      const payload = typeof t.payload === 'object' ? t.payload : JSON.parse(t.payload || '{}');
      const scheduledTime = new Date(t.scheduled_at);
      const now = new Date();
      const minutesFromNow = Math.round((scheduledTime - now) / (1000 * 60));
      
      console.log(`   ${index + 1}. ${t.task_type} (ID: ${t.id})`);
      console.log(`      Status: ${t.status}`);
      console.log(`      Disc ID: ${payload.id}`);
      console.log(`      Scheduled: ${minutesFromNow} minutes from now`);
    });

    // Show the expected workflow
    console.log('\n📋 Expected Workflow:');
    console.log('   1. disc_updated_sell_it (immediate) - Set sold_date and sold_channel to "Fixed" (ONLY if not already sold)');
    console.log('   2. disc_updated_delete_from_shopify (2min) - Delete from Shopify and update database (ONLY if shopify_uploaded_at is not null)');
    console.log('   3. disc_updated_reset (4min) - Reset specified fields and set todo');
    console.log('   4. disc_updated_unsell (6min) - Clear sold_date and sold_channel');
    console.log('   5. new_t_discs_record (8min) - Trigger existing workflow');

    console.log('\n✅ Test completed successfully!');
    console.log('\n💡 To monitor the workflow execution:');
    console.log(`   - Watch the task queue worker logs`);
    console.log(`   - Check t_task_queue table for task status updates`);
    console.log(`   - Monitor t_discs table for field changes on disc ${discId}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testDiscUpdatedWorkflow()
  .then(() => {
    console.log('\n🎉 Test script completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Test script failed:', error);
    process.exit(1);
  });
