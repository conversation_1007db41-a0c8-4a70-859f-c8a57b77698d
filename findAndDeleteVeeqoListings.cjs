const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!supabaseUrl || !supabaseKey || !veeqoApiKey) {
  console.error('❌ Missing required environment variables');
  console.error('Required: SUPABASE_URL, SUPABASE_ANON_KEY, VEEQO_API_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

console.log('🗑️  VEEQO LISTING DELETION TOOL');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    // For DELETE requests, there might not be a JSON response
    if (method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to get SDASIN record by ID
async function getSdasinRecord(sdasinId) {
  console.log(`🔍 Getting SDASIN record for ID: ${sdasinId}`);
  
  const { data, error } = await supabase
    .from('t_sdasins')
    .select('id, fbm_sku, fbm_uploaded_at, asin, parent_asin, veeqo_id')
    .eq('id', sdasinId)
    .single();
  
  if (error) {
    console.error(`❌ Error fetching SDASIN record: ${error.message}`);
    return null;
  }
  
  return data;
}

// Function to find SDASINS with deleted Amazon listings
async function findDeletedAmazonListings() {
  console.log('🔍 Finding SDASINS with deleted Amazon listings (fbm_uploaded_at = null)...');
  
  const { data, error } = await supabase
    .from('t_sdasins')
    .select('id, fbm_sku, fbm_uploaded_at, asin, parent_asin, veeqo_id')
    .is('fbm_uploaded_at', null)
    .not('fbm_sku', 'is', null)
    .limit(100); // Limit for safety
  
  if (error) {
    console.error(`❌ Error fetching deleted Amazon listings: ${error.message}`);
    return [];
  }
  
  console.log(`📊 Found ${data.length} SDASINS with deleted Amazon listings`);
  return data;
}

// Function to find Veeqo product by SKU
async function findVeeqoProductBySku(sku) {
  console.log(`🔍 Looking up Veeqo product for SKU: ${sku}`);
  
  // First check the imported table
  const { data: importedData, error: importedError } = await supabase
    .from('imported_table_veeqo_sellables_export')
    .select('product_id, sku_code, title')
    .eq('sku_code', sku);
  
  if (importedError) {
    console.error(`❌ Error querying imported Veeqo table: ${importedError.message}`);
    return [];
  }
  
  if (importedData && importedData.length > 0) {
    console.log(`✅ Found ${importedData.length} Veeqo product(s) for SKU ${sku}`);
    return importedData.map(item => ({
      id: item.product_id,
      sku: item.sku_code,
      title: item.title
    }));
  }
  
  // If not found in imported table, try direct API call
  console.log(`🔍 SKU not found in imported table, trying direct API search...`);
  
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products?page=1&page_size=50`);
  
  if (!result.success || !Array.isArray(result.data)) {
    console.log(`❌ Failed to search Veeqo API: ${result.error}`);
    return [];
  }
  
  // Search through products for matching SKU
  const matchingProducts = [];
  for (const product of result.data) {
    if (product.sellables) {
      for (const sellable of product.sellables) {
        if (sellable.sku_code === sku) {
          matchingProducts.push({
            id: product.id,
            sku: sellable.sku_code,
            title: product.title
          });
          break;
        }
      }
    }
  }
  
  console.log(`📊 Found ${matchingProducts.length} matching products via API search`);
  return matchingProducts;
}

// Function to delete a Veeqo product
async function deleteVeeqoProduct(productId) {
  console.log(`🗑️  Attempting to delete Veeqo product ID: ${productId}`);
  
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`, 'DELETE');
  
  if (result.success) {
    console.log(`✅ Successfully deleted Veeqo product ${productId}`);
    return true;
  } else {
    console.error(`❌ Failed to delete Veeqo product ${productId}: ${result.error}`);
    return false;
  }
}

// Function to process a single SDASIN
async function processSdasin(sdasin, dryRun = true) {
  console.log(`\n📋 Processing SDASIN ID: ${sdasin.id}`);
  console.log(`   FBM SKU: ${sdasin.fbm_sku}`);
  console.log(`   FBM Uploaded At: ${sdasin.fbm_uploaded_at}`);
  console.log(`   ASIN: ${sdasin.asin}`);
  
  if (sdasin.fbm_uploaded_at !== null) {
    console.log(`⚠️  SDASIN ${sdasin.id} still has fbm_uploaded_at set - skipping`);
    return { success: false, reason: 'Still has fbm_uploaded_at' };
  }
  
  // Find Veeqo products for this SKU
  const veeqoProducts = await findVeeqoProductBySku(sdasin.fbm_sku);
  
  if (veeqoProducts.length === 0) {
    console.log(`ℹ️  No Veeqo products found for SKU ${sdasin.fbm_sku}`);
    return { success: true, reason: 'No Veeqo products found' };
  }
  
  console.log(`🎯 Found ${veeqoProducts.length} Veeqo product(s) to delete:`);
  veeqoProducts.forEach(product => {
    console.log(`   - Product ID: ${product.id}, Title: ${product.title}`);
  });
  
  if (dryRun) {
    console.log(`🔍 DRY RUN: Would delete ${veeqoProducts.length} Veeqo product(s)`);
    return { success: true, reason: 'Dry run - no actual deletion', productsFound: veeqoProducts.length };
  }
  
  // Actually delete the products
  let deletedCount = 0;
  let errors = [];
  
  for (const product of veeqoProducts) {
    const deleted = await deleteVeeqoProduct(product.id);
    if (deleted) {
      deletedCount++;
    } else {
      errors.push(`Failed to delete product ${product.id}`);
    }
  }
  
  if (deletedCount === veeqoProducts.length) {
    console.log(`✅ Successfully deleted all ${deletedCount} Veeqo products`);
    return { success: true, reason: 'All products deleted', deletedCount };
  } else {
    console.log(`⚠️  Partially successful: deleted ${deletedCount}/${veeqoProducts.length} products`);
    return { success: false, reason: 'Partial deletion', deletedCount, errors };
  }
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🛠️  USAGE:
  node findAndDeleteVeeqoListings.cjs <command> [options]

📋 COMMANDS:
  check <sdasin_id>           - Check a specific SDASIN for Veeqo products to delete
  find-all                    - Find all SDASINS with deleted Amazon listings
  delete-single <sdasin_id>   - Delete Veeqo products for a specific SDASIN
  delete-all [--confirm]      - Delete Veeqo products for all SDASINS with deleted Amazon listings

📝 EXAMPLES:
  node findAndDeleteVeeqoListings.cjs check 46948
  node findAndDeleteVeeqoListings.cjs find-all
  node findAndDeleteVeeqoListings.cjs delete-single 46948
  node findAndDeleteVeeqoListings.cjs delete-all --confirm

⚠️  WARNING: Deletion operations are permanent! Use 'check' and 'find-all' first.
`);
    return;
  }
  
  const command = args[0];
  
  try {
    switch (command) {
      case 'check':
        if (args.length < 2) {
          console.error('❌ Please provide a SDASIN ID');
          return;
        }
        const sdasinId = parseInt(args[1]);
        const sdasin = await getSdasinRecord(sdasinId);
        if (sdasin) {
          await processSdasin(sdasin, true); // Dry run
        }
        break;
        
      case 'find-all':
        const deletedListings = await findDeletedAmazonListings();
        if (deletedListings.length > 0) {
          console.log(`\n📋 SDASINS with deleted Amazon listings:`);
          deletedListings.forEach(sdasin => {
            console.log(`   - ID: ${sdasin.id}, SKU: ${sdasin.fbm_sku}, ASIN: ${sdasin.asin}`);
          });
          console.log(`\n💡 Use 'delete-all --confirm' to delete corresponding Veeqo products`);
        }
        break;
        
      case 'delete-single':
        if (args.length < 2) {
          console.error('❌ Please provide a SDASIN ID');
          return;
        }
        const targetSdasinId = parseInt(args[1]);
        const targetSdasin = await getSdasinRecord(targetSdasinId);
        if (targetSdasin) {
          await processSdasin(targetSdasin, false); // Actually delete
        }
        break;
        
      case 'delete-all':
        const confirmFlag = args.includes('--confirm');
        if (!confirmFlag) {
          console.error('❌ This will delete Veeqo products! Use --confirm flag to proceed');
          console.log('💡 Run with --confirm to actually delete products');
          return;
        }
        
        const allDeletedListings = await findDeletedAmazonListings();
        if (allDeletedListings.length === 0) {
          console.log('✅ No SDASINS with deleted Amazon listings found');
          return;
        }
        
        console.log(`🚨 About to process ${allDeletedListings.length} SDASINS for Veeqo product deletion...`);
        
        let processedCount = 0;
        let successCount = 0;
        let errorCount = 0;
        
        for (const sdasin of allDeletedListings) {
          const result = await processSdasin(sdasin, false);
          processedCount++;
          
          if (result.success) {
            successCount++;
          } else {
            errorCount++;
          }
          
          // Add delay between deletions to be respectful to the API
          if (processedCount < allDeletedListings.length) {
            console.log('⏳ Waiting 1 second before next deletion...');
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
        
        console.log(`\n📊 DELETION SUMMARY:`);
        console.log(`✅ Successfully processed: ${successCount}`);
        console.log(`❌ Errors: ${errorCount}`);
        console.log(`📋 Total processed: ${processedCount}`);
        break;
        
      default:
        console.error(`❌ Unknown command: ${command}`);
        console.log('Use no arguments to see usage instructions.');
    }
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the main function
main();
