import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function applyAdditionalBrandAndFillerRules() {
  try {
    console.log('Applying additional brand exclusions and filler text rules...\n');

    // Handle additional brand exclusions
    await handleAdditionalBrandExclusions();

    // Remove additional filler text
    await removeMoreFillerText();

    console.log('\nAdditional brand and filler text rules applied successfully!');

  } catch (error) {
    console.error('Error:', error);
  }
}

async function handleAdditionalBrandExclusions() {
  console.log('Handling additional brand exclusions...');

  const additionalBrandExclusions = [
    { contains: 'ONEATTACK', replacement: 'XXXX ONEATTACK' },
    { contains: 'ONE ATTACK', replacement: 'XXXX ONE ATTACK' },
    { contains: 'OXYEFEI', replacement: 'XXXX OXYEFEI' },
    { contains: 'Finish Line', replacement: 'XXXX Finish Line' },
    { contains: 'Pick Your Disc', replacement: 'XXXX Pick Your Disc' },
    { contains: 'Pongnas', replacement: 'XXXX Pongnas' },
    { contains: 'TSOTMO', replacement: 'XXXX TSOTMO' },
    { contains: 'VBESTLIFE', replacement: 'XXXX VBESTLIFE' },
    { contains: 'Crosslap', replacement: 'XXXX Crosslap' },
    { contains: 'Vodolo', replacement: 'XXXX Vodolo' },
    { contains: 'Wham-O', replacement: 'XXXX Wham-O' },
    { contains: 'Discineer', replacement: 'XXXX Discineer' }
  ];

  for (const brandCase of additionalBrandExclusions) {
    console.log(`Processing "${brandCase.contains}"...`);

    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .like('notes', `%${brandCase.contains}%`)
      .not('notes', 'like', 'XXXX%');

    if (fetchError) {
      console.error(`Error fetching records for ${brandCase.contains}:`, fetchError);
      continue;
    }

    let brandUpdated = 0;

    for (const record of matchingRecords || []) {
      const updateData = {
        notes: brandCase.replacement
      };

      if (!record.raw_notes) {
        updateData.raw_notes = record.notes;
      }

      const { error: updateError } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', record.id);

      if (updateError) {
        console.error(`Error updating record ${record.id}:`, updateError);
      } else {
        brandUpdated++;
        if (brandUpdated <= 3) { // Show first 3 updates per brand
          console.log(`  Updated ID ${record.id}: ${brandCase.replacement}`);
        }
      }
    }

    console.log(`  Updated ${brandUpdated} records containing "${brandCase.contains}"`);
  }

  console.log('Additional brand exclusions handled.\n');
}

async function removeMoreFillerText() {
  console.log('Removing additional filler text...');

  // Additional filler phrases to remove (longest to shortest)
  const moreFillerPhrases = [
    'Ideal for All Disc Golf Courses and Competitions',
    'Perfect for Disc Golf Course Games & Outdoor Sports',
    'Ideal for Beginners to Advanced Players',
    'Durable Eco-Friendly Construction',
    'Putter and Approach Disc',
    'High-Visibility Durable Disc',
    'Discs Midrange Golf Disc',
    'Controllable Golf Disc',
    'Disc Golf Mid-Range',
    'Disc Golf Mid Range',
    'Midrange Golf Disc',
    'Driver Golf Disc',
    'Putter Golf Disc',
    'Innovative Design',
    'Fairway Driver',
    'Beginner Friendly',
    'PDGA Approved',
    'Disc Sports',
    'Discs Golf'
  ];

  let totalUpdated = 0;

  for (const phrase of moreFillerPhrases) {
    console.log(`Removing "${phrase}"...`);

    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .like('notes', `%${phrase}%`)
      .not('notes', 'like', 'XXXX%');

    if (fetchError) {
      console.error(`Error fetching records for "${phrase}":`, fetchError);
      continue;
    }

    let phraseUpdated = 0;

    for (const record of matchingRecords || []) {
      const updatedNotes = record.notes.replace(new RegExp(escapeRegex(phrase), 'gi'), '').trim();
      
      const cleanedNotes = updatedNotes
        .replace(/\s+/g, ' ')
        .replace(/^\s*[-|,]\s*/, '')
        .replace(/\s*[-|,]\s*$/, '')
        .trim();

      const updateData = {
        notes: cleanedNotes
      };

      if (!record.raw_notes) {
        updateData.raw_notes = record.notes;
      }

      const { error: updateError } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', record.id);

      if (updateError) {
        console.error(`Error updating record ${record.id}:`, updateError);
      } else {
        phraseUpdated++;
        totalUpdated++;
        if (phraseUpdated <= 3) { // Show first 3 updates per phrase
          console.log(`  Updated ID ${record.id}: removed "${phrase}"`);
        }
      }
    }

    console.log(`  Removed from ${phraseUpdated} records`);
  }

  console.log(`\nTotal records updated with additional filler text removal: ${totalUpdated}`);
}

function escapeRegex(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Run the script
applyAdditionalBrandAndFillerRules();
