-- Superseded by consolidated sync: sql/triggers/t_product_variants_sync_shopify_on_update.sql
-- Keeping this file to avoid confusion; it now only ensures old trigger is dropped.

DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'trg_t_product_variants_price_updated_update_shopify_price'
  ) THEN
    EXECUTE 'DROP TRIGGER trg_t_product_variants_price_updated_update_shopify_price ON public.t_product_variants';
  END IF;
END $$;
