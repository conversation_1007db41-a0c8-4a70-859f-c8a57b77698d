// fix_task_173717.js
// Update task 173717 result with the correct error message from the database

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function fixTask173717() {
  try {
    console.log('Fixing task 173717 result with correct error message...');
    
    // Get the current task result
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 173717)
      .single();
    
    if (taskError) {
      console.error('Error fetching task:', taskError);
      return;
    }
    
    if (!task) {
      console.log('Task 173717 not found');
      return;
    }
    
    console.log('Current task result:', JSON.stringify(task.result, null, 2));
    
    // Get the error message from OSL 18500
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('shopify_product_uploaded_notes')
      .eq('id', 18500)
      .single();
    
    if (oslError) {
      console.error('Error fetching OSL:', oslError);
      return;
    }
    
    if (!osl || !osl.shopify_product_uploaded_notes) {
      console.log('No error message found in OSL notes');
      return;
    }
    
    console.log('OSL error message:', osl.shopify_product_uploaded_notes);
    
    // Update the task result with the correct error message
    const currentResult = typeof task.result === 'string' ? JSON.parse(task.result) : task.result;
    const updatedResult = {
      ...currentResult,
      error: osl.shopify_product_uploaded_notes,
      error_source: 'extracted_from_database'
    };
    
    const { error: updateError } = await supabase
      .from('t_task_queue')
      .update({ result: updatedResult })
      .eq('id', 173717);
    
    if (updateError) {
      console.error('Error updating task:', updateError);
      return;
    }
    
    console.log('✅ Successfully updated task 173717 with correct error message');
    console.log('New result:', JSON.stringify(updatedResult, null, 2));
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

fixTask173717();
