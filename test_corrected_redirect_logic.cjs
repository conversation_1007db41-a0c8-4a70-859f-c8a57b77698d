require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testCorrectedRedirectLogic() {
  try {
    console.log('Testing corrected redirect logic - redirect ONLY for vendor mapping...');
    
    // Test with disc 425777 and OSL 19033 from the previous example
    const testDiscId = 425777;
    const testOslId = 19033;
    
    console.log(`\n=== TESTING CORRECTED LOGIC ===`);
    console.log(`Disc ${testDiscId} should now match OSL ${testOslId} for REGULAR mapping`);
    console.log(`But vendor mapping should use redirect logic`);
    
    // Clear existing mappings for clean test
    console.log('\nClearing existing mappings for clean test...');
    await supabase
      .from('t_discs')
      .update({ 
        order_sheet_line_id: null,
        vendor_osl_id: null 
      })
      .eq('id', testDiscId);
    
    // Create a match_disc_to_osl task
    console.log(`\n=== CREATING match_disc_to_osl TASK ===`);
    
    const { data: matchDiscTask, error: matchDiscError } = await supabase
      .from('t_task_queue')
      .insert([
        {
          task_type: 'match_disc_to_osl',
          payload: { 
            id: testDiscId,
            operation: 'INSERT'
          },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: 'test_corrected_redirect_logic'
        }
      ])
      .select();
    
    if (matchDiscError) {
      console.error('Error creating match_disc_to_osl task:', matchDiscError);
    } else {
      const taskId = matchDiscTask[0].id;
      console.log(`✅ Created match_disc_to_osl task ${taskId} for disc ${testDiscId}`);
    }
    
    // Create a match_osl_to_discs task
    console.log(`\n=== CREATING match_osl_to_discs TASK ===`);
    
    const { data: matchOslTask, error: matchOslError } = await supabase
      .from('t_task_queue')
      .insert([
        {
          task_type: 'match_osl_to_discs',
          payload: { id: testOslId },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: 'test_corrected_redirect_logic'
        }
      ])
      .select();
    
    if (matchOslError) {
      console.error('Error creating match_osl_to_discs task:', matchOslError);
    } else {
      const taskId = matchOslTask[0].id;
      console.log(`✅ Created match_osl_to_discs task ${taskId} for OSL ${testOslId}`);
    }
    
    console.log('\n🔄 Tasks created and ready for processing by the worker daemon.');
    
    console.log('\n📋 EXPECTED BEHAVIOR (corrected):');
    
    console.log('\n1. match_disc_to_osl task:');
    console.log('   ✅ Regular mapping: Should find OSL 19033 (no redirect for regular weight)');
    console.log('   🔄 Vendor mapping: Should use redirect logic and find OSL in MPS 391');
    console.log('   📊 Result: Disc should get order_sheet_line_id = 19033');
    console.log('   📊 Result: Disc should get vendor_osl_id = (OSL in MPS 391 or null if none found)');
    
    console.log('\n2. match_osl_to_discs task:');
    console.log('   ✅ Regular mapping: Should process normally (no redirect logic)');
    console.log('   🔄 Vendor mapping: Should process normally (redirect handled by individual disc functions)');
    console.log('   📊 Result: Should find discs that match OSL 19033 criteria');
    
    console.log('\n🎯 KEY DIFFERENCES FROM BEFORE:');
    console.log('✅ Regular weight matching: NO redirect (uses original OSL)');
    console.log('🔄 Vendor weight matching: WITH redirect (finds replacement OSL)');
    console.log('✅ match_osl_to_discs: Processes both mappings (no skipping)');
    
    console.log('\n📊 To check results after processing:');
    console.log(`
-- Check disc mappings (should have regular mapping now)
SELECT id, order_sheet_line_id, vendor_osl_id 
FROM t_discs 
WHERE id = ${testDiscId};

-- Check task results
SELECT 
  id, task_type, status, 
  result->>'message' as message,
  processed_at
FROM t_task_queue 
WHERE enqueued_by = 'test_corrected_redirect_logic'
ORDER BY created_at;
    `);
    
    // Show the expected matching details
    console.log('\n=== EXPECTED MATCHING DETAILS ===');
    
    const { data: disc, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id')
      .eq('id', testDiscId)
      .single();
    
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, min_weight, max_weight, color_id')
      .eq('id', testOslId)
      .single();
    
    if (!discError && !oslError) {
      console.log(`Disc ${testDiscId}: MPS ${disc.mps_id}, Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id}`);
      console.log(`OSL ${testOslId}: MPS ${osl.mps_id}, Weight ${osl.min_weight}-${osl.max_weight}g, Color ${osl.color_id}`);
      
      const regularMatch = disc.weight >= osl.min_weight && disc.weight <= osl.max_weight;
      const vendorMatch = disc.weight_mfg ? Math.round(disc.weight_mfg) >= osl.min_weight && Math.round(disc.weight_mfg) <= osl.max_weight : false;
      
      console.log(`Regular weight match: ${regularMatch ? '✅' : '❌'} (${disc.weight}g in ${osl.min_weight}-${osl.max_weight}g)`);
      console.log(`Vendor weight match: ${vendorMatch ? '✅' : '❌'} (${Math.round(disc.weight_mfg)}g in ${osl.min_weight}-${osl.max_weight}g)`);
      
      if (regularMatch) {
        console.log('🎯 Regular mapping should work: order_sheet_line_id = 19033');
      }
      if (vendorMatch) {
        console.log('🔄 Vendor mapping should redirect to find OSL in MPS 391');
      }
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

testCorrectedRedirectLogic();
