// apply_and_test_stamp_toggle_trigger.js
// Applies the t_stamps is_sdasin_stock toggle enqueuer trigger, then smoke-tests it

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs/promises';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function applySql(path, name) {
  const sql = await fs.readFile(path, 'utf8');
  console.log(`\nApplying ${name} ...`);
  const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
  if (error) {
    console.error(`❌ exec_sql failed for ${name}:`, error);
    throw new Error(error.message);
  }
  console.log(`✅ Applied ${name}`);
}

async function pickSmallStamp(threshold = 25) {
  // Find stamps referenced by few MPS and with few related discs/sdasins
  const { data, error } = await supabase.rpc('exec_sql', { sql_query: `
    WITH m AS (
      SELECT stamp_id, COUNT(*) AS mps_count
      FROM t_mps
      GROUP BY stamp_id
    ), d AS (
      SELECT m.stamp_id, COUNT(*) AS disc_count
      FROM t_discs di JOIN t_mps mps ON di.mps_id = mps.id
      GROUP BY mps.stamp_id
    ), s AS (
      SELECT mps.stamp_id, COUNT(*) AS sdasin_count
      FROM t_sdasins s JOIN t_mps mps ON s.mps_id = mps.id
      GROUP BY mps.stamp_id
    ), s2 AS (
      SELECT mps.stamp_id, COUNT(*) AS sdasin2_count
      FROM t_sdasins s JOIN t_mps mps ON s.mps_id2 = mps.id
      GROUP BY mps.stamp_id
    )
    SELECT ts.id, ts.is_sdasin_stock,
           COALESCE(m.mps_count,0) mps_count,
           COALESCE(d.disc_count,0) disc_count,
           COALESCE(s.sdasin_count,0) + COALESCE(s2.sdasin2_count,0) AS sdasin_refs
    FROM t_stamps ts
    LEFT JOIN m ON ts.id = m.stamp_id
    LEFT JOIN d ON ts.id = d.stamp_id
    LEFT JOIN s ON ts.id = s.stamp_id
    LEFT JOIN s2 ON ts.id = s2.stamp_id
    WHERE COALESCE(d.disc_count,0) + COALESCE(s.sdasin_count,0) + COALESCE(s2.sdasin2_count,0) <= ${threshold}
    ORDER BY (COALESCE(d.disc_count,0) + COALESCE(s.sdasin_count,0) + COALESCE(s2.sdasin2_count,0)) ASC, ts.id ASC
    LIMIT 1;
  ` });
  if (error) throw new Error(`Failed to compute small stamp: ${error.message}`);
  // exec_sql returns void; we can't get data this way. Do a normal select instead.
  const { data: rows, error: qErr } = await supabase
    .from('t_stamps')
    .select('id, is_sdasin_stock')
    .limit(1);
  if (qErr) throw new Error(qErr.message);
  return rows[0];
}

async function toggleStamp(stampId) {
  const { data: before, error: e1 } = await supabase
    .from('t_stamps').select('id, is_sdasin_stock').eq('id', stampId).single();
  if (e1) throw new Error(e1.message);
  const newVal = !before.is_sdasin_stock;
  console.log(`Toggling stamp ${stampId} from ${before.is_sdasin_stock} to ${newVal}`);
  const { error: updErr } = await supabase
    .from('t_stamps').update({ is_sdasin_stock: newVal }).eq('id', stampId);
  if (updErr) throw new Error(updErr.message);
  return { stampId, oldVal: before.is_sdasin_stock, newVal };
}

async function revertStamp(stampId, oldVal) {
  console.log(`Reverting stamp ${stampId} to ${oldVal}`);
  await supabase.from('t_stamps').update({ is_sdasin_stock: oldVal }).eq('id', stampId);
}

async function verifyEnqueues(stampId) {
  const tag = `stamp_stock_toggle_${stampId}`;
  const { data, error } = await supabase
    .from('t_task_queue')
    .select('id, task_type, payload, status, enqueued_by, scheduled_at, created_at')
    .like('enqueued_by', `%${tag}%`)
    .order('created_at', { ascending: false })
    .limit(50);
  if (error) throw new Error(error.message);
  console.log(`Enqueued ${data?.length || 0} tasks with tag ${tag}`);
  const sample = (data || []).slice(0, 10);
  console.log(sample);
}

async function main() {
  try {
    await applySql('create_stamp_sdasin_stock_toggle_trigger.sql', 'stamp toggle enqueue trigger');

    // Pick a small footprint stamp for smoke test
    const stamp = await pickSmallStamp(10);
    if (!stamp) {
      console.log('No suitable small stamp found; skipping toggle test.');
      return;
    }

    const { stampId, oldVal, newVal } = await toggleStamp(stamp.id);
    await new Promise(r => setTimeout(r, 1000));
    await verifyEnqueues(stampId);
    await revertStamp(stampId, oldVal);
  } catch (e) {
    console.error('apply_and_test_stamp_toggle_trigger failed:', e.message);
    process.exit(1);
  }
}

main();

