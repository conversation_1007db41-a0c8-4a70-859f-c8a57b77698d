import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Shopify GraphQL Admin API credentials
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

if (!shopifyEndpoint || !shopifyAccessToken) {
  console.error('ERROR: Missing Shopify endpoint or access token in environment variables.');
  process.exit(1);
}

/**
 * Execute a GraphQL request to Shopify
 * @param {string} query - The GraphQL query
 * @param {Object} variables - Variables for the GraphQL query
 * @returns {Promise<Object>} - The response data
 */
async function executeShopifyGraphQL(query, variables = {}) {
  try {
    const response = await fetch(shopifyEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      },
      body: JSON.stringify({ query, variables })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    if (result.errors) {
      throw new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`);
    }

    return result.data;
  } catch (error) {
    console.error('Error executing Shopify GraphQL request:', error);
    throw error;
  }
}

/**
 * Delete a single product by ID
 * @param {string} productId - The product ID (without gid prefix)
 * @returns {Promise<Object>} - Result of the deletion
 */
async function deleteProduct(productId) {
  const mutation = `
    mutation productDelete($input: ProductDeleteInput!) {
      productDelete(input: $input) {
        deletedProductId
        userErrors {
          field
          message
        }
      }
    }
  `;

  const variables = {
    input: {
      id: `gid://shopify/Product/${productId}`
    }
  };

  try {
    console.log(`Deleting product ID: ${productId}`);
    const data = await executeShopifyGraphQL(mutation, variables);
    
    if (data.productDelete.userErrors && data.productDelete.userErrors.length > 0) {
      const errors = data.productDelete.userErrors.map(error => error.message).join(', ');
      throw new Error(`User errors: ${errors}`);
    }

    if (data.productDelete.deletedProductId) {
      console.log(`Successfully deleted product: ${productId}`);
      return { success: true, productId: productId };
    } else {
      throw new Error('Product deletion failed - no deleted product ID returned');
    }
  } catch (error) {
    console.error(`Failed to delete product ${productId}:`, error.message);
    return { success: false, productId: productId, error: error.message };
  }
}

/**
 * Delete multiple products by their IDs
 * @param {Array<string>} productIds - Array of product IDs to delete
 * @returns {Promise<Object>} - Results of the deletions
 */
async function deleteProducts(productIds) {
  console.log(`Starting deletion of ${productIds.length} products...`);
  
  const results = [];
  const errors = [];
  let successCount = 0;

  for (const productId of productIds) {
    try {
      const result = await deleteProduct(productId);
      results.push(result);
      
      if (result.success) {
        successCount++;
      } else {
        errors.push(`Product ${productId}: ${result.error}`);
      }

      // Add a delay between deletions to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error) {
      const errorMsg = `Product ${productId}: ${error.message}`;
      errors.push(errorMsg);
      results.push({ success: false, productId: productId, error: error.message });
      console.error(errorMsg);
    }
  }

  console.log(`\nDeletion completed!`);
  console.log(`Total products processed: ${productIds.length}`);
  console.log(`Successfully deleted: ${successCount}`);
  console.log(`Failed deletions: ${errors.length}`);

  if (errors.length > 0) {
    console.log('\nErrors:');
    errors.forEach(error => console.log(`  - ${error}`));
  }

  return {
    success: true,
    totalProcessed: productIds.length,
    deletedCount: successCount,
    errorCount: errors.length,
    errors: errors,
    results: results
  };
}

/**
 * Main function to delete products
 * @param {Array<string>} productIds - Array of product IDs to delete
 */
async function main(productIds = []) {
  try {
    if (!productIds || productIds.length === 0) {
      throw new Error('No product IDs provided for deletion');
    }

    // Validate product IDs (should be numeric strings)
    const validIds = productIds.filter(id => /^\d+$/.test(id.toString()));
    const invalidIds = productIds.filter(id => !/^\d+$/.test(id.toString()));

    if (invalidIds.length > 0) {
      console.warn(`Warning: Invalid product IDs found and will be skipped: ${invalidIds.join(', ')}`);
    }

    if (validIds.length === 0) {
      throw new Error('No valid product IDs provided for deletion');
    }

    console.log(`Proceeding to delete ${validIds.length} products...`);
    const result = await deleteProducts(validIds);
    
    return result;
  } catch (error) {
    console.error('Error in main function:', error);
    return {
      success: false,
      error: error.message,
      totalProcessed: 0,
      deletedCount: 0,
      errorCount: 0,
      errors: [error.message]
    };
  }
}

// Export the main function for use in other modules
export default main;

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  // Get product IDs from command line arguments
  const productIds = process.argv.slice(2);
  
  if (productIds.length === 0) {
    console.error('Usage: node deleteDuplicateShopifyProducts.js <productId1> <productId2> ...');
    process.exit(1);
  }

  main(productIds).then(result => {
    console.log('\nResult:', JSON.stringify(result, null, 2));
    process.exit(result.success ? 0 : 1);
  });
}
