// testWorkflowStopsOnError.js - Test that workflow stops when Shopify deletion fails
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testWorkflowStopsOnError() {
  console.log('🧪 Testing Workflow Stops on Shopify Deletion Error');
  console.log('===================================================');

  try {
    // Check the problematic task 192074
    console.log('\n🔍 Checking Task 192074 (the one that failed but still enqueued next task):');
    
    const { data: task192074, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 192074)
      .single();

    if (taskError) {
      console.error('❌ Error fetching task 192074:', taskError.message);
      return;
    }

    console.log(`📊 Task 192074 Status: ${task192074.status}`);
    console.log(`📄 Task 192074 Message: ${task192074.result?.message || 'No message'}`);
    console.log(`❌ Task 192074 Error: ${task192074.result?.error || 'No error'}`);
    console.log(`🔗 Next Task Enqueued: ${task192074.result?.next_task_enqueued || 'Not specified'}`);

    // Check if task 192075 was enqueued by this task
    console.log('\n🔍 Checking Task 192075 (the incorrectly enqueued next task):');
    
    const { data: task192075, error: nextTaskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 192075)
      .single();

    if (nextTaskError) {
      console.error('❌ Error fetching task 192075:', nextTaskError.message);
      return;
    }

    console.log(`📊 Task 192075 Type: ${task192075.task_type}`);
    console.log(`📊 Task 192075 Status: ${task192075.status}`);
    console.log(`🔗 Task 192075 Enqueued By: ${task192075.enqueued_by}`);
    console.log(`📅 Task 192075 Created: ${task192075.created_at}`);

    // Verify the issue
    const isIncorrectlyEnqueued = task192075.enqueued_by === 'disc_updated_delete_from_shopify_350463' &&
                                  task192074.status === 'completed' && // Should be 'error'
                                  task192074.result?.error;

    if (isIncorrectlyEnqueued) {
      console.log('\n❌ CONFIRMED ISSUE:');
      console.log('   - Task 192074 has an error but status is "completed"');
      console.log('   - Task 192075 was incorrectly enqueued despite the error');
      console.log('   - This is exactly what we fixed!');
    } else {
      console.log('\n✅ No issue detected with these specific tasks');
    }

    console.log('\n🔧 FIXES IMPLEMENTED:');
    console.log('=====================');
    console.log('1. ✅ Fixed variable scoping issue (resetError undefined)');
    console.log('2. ✅ Proper error status setting when Shopify deletion fails');
    console.log('3. ✅ Next task only enqueued on true success');
    console.log('4. ✅ Enhanced error reporting with workflow_stopped flag');

    console.log('\n📋 Expected Behavior After Fix:');
    console.log('===============================');
    console.log('✅ Shopify deletion success → Task status: completed → Next task enqueued');
    console.log('❌ Shopify deletion failure → Task status: error → Workflow stops');
    console.log('❌ Any exception → Task status: error → Workflow stops');

    console.log('\n🧪 Test New Workflow:');
    console.log('=====================');
    console.log('1. Create a new disc_updated_need_to_reset task');
    console.log('2. Monitor the disc_updated_delete_from_shopify task');
    console.log('3. Verify it only enqueues next task on true success');
    console.log('4. Check that errors properly stop the workflow');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testWorkflowStopsOnError()
  .then(() => {
    console.log('\n🏁 Analysis completed');
    console.log('The workflow should now properly stop on Shopify deletion errors!');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Analysis failed:', error);
    process.exit(1);
  });
