-- Function to find t_order_sheet_lines records where g_code is null
-- and enqueue tasks to generate the fields
CREATE OR REPLACE FUNCTION fn_enqueue_generate_osl_fields_for_null_g_code(
  batch_size INTEGER DEFAULT 10
)
RETURNS TABLE (
  osl_id INTEGER,
  task_id INTEGER
)
LANGUAGE plpgsql
AS $$
DECLARE
  v_count INTEGER := 0;
BEGIN
  -- Create a temporary table to store the results
  CREATE TEMP TABLE temp_results (
    osl_id INTEGER,
    task_id INTEGER
  ) ON COMMIT DROP;

  -- Insert tasks into t_task_queue for OSLs with null g_code
  WITH osls_with_null_g_code AS (
    SELECT id AS osl_record_id
    FROM t_order_sheet_lines
    WHERE g_code IS NULL
    AND NOT EXISTS (
      -- Check if there's already a pending task for this OSL
      SELECT 1 FROM t_task_queue
      WHERE task_type = 'generate_osl_fields'
      AND payload->>'id' = t_order_sheet_lines.id::text
      AND status IN ('pending', 'processing')
    )
    LIMIT batch_size
  ),
  inserted_tasks AS (
    INSERT INTO t_task_queue (
      task_type,
      payload,
      status,
      scheduled_at,
      created_at,
      enqueued_by
    )
    SELECT
      'generate_osl_fields',
      jsonb_build_object('id', osl_record_id),
      'pending',
      NOW(),
      NOW(),
      'Manual catch-up'
    FROM osls_with_null_g_code
    RETURNING id AS inserted_task_id, (payload->>'id')::INTEGER AS inserted_osl_id
  )
  INSERT INTO temp_results (osl_id, task_id)
  SELECT inserted_osl_id, inserted_task_id
  FROM inserted_tasks;

  -- Get the count of inserted tasks
  SELECT COUNT(*) INTO v_count FROM temp_results;

  -- Log the result
  RAISE NOTICE 'Enqueued % tasks for OSLs with null g_code', v_count;

  -- Return the results
  RETURN QUERY SELECT * FROM temp_results;
END;
$$;

-- Example usage:
-- SELECT * FROM fn_enqueue_generate_osl_fields_for_null_g_code(10);

-- Confirmation message
DO $$
BEGIN
  RAISE NOTICE 'Function fn_enqueue_generate_osl_fields_for_null_g_code has been created.';
  RAISE NOTICE 'Usage: SELECT * FROM fn_enqueue_generate_osl_fields_for_null_g_code(batch_size);';
  RAISE NOTICE 'Example: SELECT * FROM fn_enqueue_generate_osl_fields_for_null_g_code(10);';
END $$;
