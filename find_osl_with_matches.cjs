require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function findOslWithMatches() {
  try {
    console.log('Finding an OSL that should have disc matches for testing...');
    
    // Find an OSL that has unsold discs that should match
    const { data: osls, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, min_weight, max_weight, color_id')
      .limit(20);
    
    if (oslError) {
      console.error('Error getting OSLs:', oslError);
      return;
    }
    
    console.log(`Checking ${osls.length} OSLs for potential matches...`);
    
    for (const osl of osls) {
      // Check for regular weight matches
      const { data: regularMatches, error: regError } = await supabase
        .from('t_discs')
        .select('id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
        .eq('mps_id', osl.mps_id)
        .gte('weight', osl.min_weight)
        .lte('weight', osl.max_weight)
        .in('color_id', osl.color_id === 23 ? [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23] : [osl.color_id, 23])
        .is('sold_date', null)
        .limit(5);
      
      // Check for vendor weight matches
      const { data: vendorCandidates, error: vendorError } = await supabase
        .from('t_discs')
        .select('id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
        .eq('mps_id', osl.mps_id)
        .not('weight_mfg', 'is', null)
        .in('color_id', osl.color_id === 23 ? [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23] : [osl.color_id, 23])
        .is('sold_date', null)
        .limit(10);
      
      let vendorMatches = [];
      if (!vendorError && vendorCandidates) {
        vendorMatches = vendorCandidates.filter(disc => {
          const roundedWeightMfg = Math.round(disc.weight_mfg);
          return roundedWeightMfg >= osl.min_weight && roundedWeightMfg <= osl.max_weight;
        });
      }
      
      const regularCount = !regError && regularMatches ? regularMatches.length : 0;
      const vendorCount = vendorMatches.length;
      
      if (regularCount > 0 || vendorCount > 0) {
        console.log(`\n🎯 OSL ${osl.id}: MPS ${osl.mps_id}, Weight ${osl.min_weight}-${osl.max_weight}g, Color ${osl.color_id}`);
        console.log(`   Regular matches: ${regularCount}, Vendor matches: ${vendorCount}`);
        
        if (regularCount > 0) {
          console.log('   Regular match examples:');
          regularMatches.slice(0, 3).forEach(disc => {
            console.log(`     Disc ${disc.id}: Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id}, Regular OSL: ${disc.order_sheet_line_id}, Vendor OSL: ${disc.vendor_osl_id}`);
          });
        }
        
        if (vendorCount > 0) {
          console.log('   Vendor match examples:');
          vendorMatches.slice(0, 3).forEach(disc => {
            const roundedWeightMfg = Math.round(disc.weight_mfg);
            console.log(`     Disc ${disc.id}: Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g (rounded: ${roundedWeightMfg}g), Color ${disc.color_id}, Regular OSL: ${disc.order_sheet_line_id}, Vendor OSL: ${disc.vendor_osl_id}`);
          });
        }
        
        // If this OSL has good matches, suggest it for testing
        if (regularCount >= 2 || vendorCount >= 2) {
          console.log(`\n✅ OSL ${osl.id} would be good for testing dual mapping!`);
          console.log(`   Use this OSL ID in the test: ${osl.id}`);
          return osl.id;
        }
      }
    }
    
    console.log('\n❌ No OSLs found with sufficient matches for testing.');
    console.log('This might indicate that most discs are already properly mapped.');
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

findOslWithMatches();
