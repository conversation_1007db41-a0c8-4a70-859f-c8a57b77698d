// This is a patch for the processMatchDiscToOslTask function in taskQueueWorker.js
// Replace the find_matching_osl call in the UPDATE operation with this code:

// Find new matching order sheet line
const { data: oslData, error: oslError } = await supabase.rpc(
  'find_matching_osl',
  {
    mps_id_param: discRecord.mps_id,
    color_id_param: discRecord.color_id,
    weight_param: discRecord.weight
  }
);

if (oslError) {
  const errMsg = `[taskQueueWorker.js] Error finding new matching OSL: ${oslError.message}`;
  console.error(errMsg);
  await logError(errMsg, `Finding new matching OSL`);
  await updateTaskStatus(task.id, 'error', {
    message: "Failed to find new matching OSL. Database error.",
    error: oslError.message
  });
  return;
}

// Extract debug info and osl_id
let debugInfo = 'No debug info available';
let newOslId = null;

if (oslData && oslData.length > 0) {
  debugInfo = oslData[0].debug_info || debugInfo;
  newOslId = oslData[0].osl_id;
}

console.log(`[taskQueueWorker.js] Debug info: ${debugInfo}`);

// Update the disc with the new order_sheet_line_id
const { error: updateDiscError } = await supabase
  .from('t_discs')
  .update({ order_sheet_line_id: newOslId })
  .eq('id', discId);

if (updateDiscError) {
  const errMsg = `[taskQueueWorker.js] Error updating disc with new OSL: ${updateDiscError.message}`;
  console.error(errMsg);
  await logError(errMsg, `Updating disc with new OSL`);
  await updateTaskStatus(task.id, 'error', {
    message: "Failed to update disc with new OSL. Database error.",
    error: updateDiscError.message,
    debug_info: debugInfo
  });
  return;
}

// Also replace the final updateTaskStatus call with this:
console.log(`[taskQueueWorker.js] Successfully processed UPDATE operation for disc ${discId}`);
await updateTaskStatus(task.id, 'completed', {
  message: newOslId ? 
    `Success! Disc matched to OSL ${newOslId}.` : 
    "No matching order sheet line found for disc.",
  operation: 'UPDATE',
  debug_info: debugInfo,
  osl_id: newOslId
});
