import XLSX from 'xlsx';
import path from 'path';

async function examineNewReleaseSection() {
    try {
        console.log('🔍 Examining NEW RELEASE section...\n');
        
        const filePath = path.join(process.cwd(), 'data', 'external data', 'discraftstock.xlsx');
        console.log(`📁 Reading file: ${filePath}`);
        
        // Read the Excel file
        const workbook = XLSX.readFile(filePath);
        const sheetName = 'Order Form';
        const worksheet = workbook.Sheets[sheetName];
        
        if (!worksheet) {
            throw new Error(`Sheet "${sheetName}" not found`);
        }
        
        // Examine rows 125-140 to understand the NEW RELEASE section structure
        console.log('🔍 Examining rows 125-140 (NEW RELEASE section):');
        
        for (let row = 124; row <= 139; row++) { // 0-based, so 124 = Excel row 125
            console.log(`\n--- Row ${row + 1} (Excel row ${row + 1}) ---`);
            
            const rowData = {};
            let hasData = false;
            
            // Read all columns for this row
            for (let col = 0; col <= 30; col++) { // A-AE columns
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                const cell = worksheet[cellAddress];
                if (cell && cell.v !== null && cell.v !== undefined && cell.v !== '') {
                    const colLetter = col < 26 ? 
                        String.fromCharCode(65 + col) : 
                        'A' + String.fromCharCode(65 + col - 26); // AA, AB, AC, etc.
                    rowData[colLetter] = cell.v;
                    hasData = true;
                }
            }
            
            if (hasData) {
                // Show the data in a readable format
                Object.entries(rowData).forEach(([col, value]) => {
                    console.log(`   ${col}: "${value}"`);
                });
            } else {
                console.log('   (empty row)');
            }
        }
        
        // Look for "Classic reissue" and "FuZed Scorch" content
        console.log('\n🔍 Looking for NEW RELEASE products...');
        
        const searchTerms = ['Classic reissue', 'FuZed Scorch', 'NEW RELEASE'];
        
        for (const term of searchTerms) {
            console.log(`\n--- Searching for "${term}" ---`);
            
            let foundCells = [];
            const range = XLSX.utils.decode_range(worksheet['!ref']);
            
            for (let row = 0; row <= range.e.r; row++) {
                for (let col = 0; col <= range.e.c; col++) {
                    const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                    const cell = worksheet[cellAddress];
                    if (cell && cell.v && cell.v.toString().toLowerCase().includes(term.toLowerCase())) {
                        const colLetter = col < 26 ? 
                            String.fromCharCode(65 + col) : 
                            'A' + String.fromCharCode(65 + col - 26);
                        foundCells.push({
                            row: row + 1,
                            col: colLetter,
                            value: cell.v
                        });
                    }
                }
            }
            
            console.log(`✅ Found ${foundCells.length} cells containing "${term}":`);
            foundCells.forEach((item, index) => {
                console.log(`   ${index + 1}. Row ${item.row}, Col ${item.col}: "${item.value}"`);
            });
        }
        
        console.log('\n🎯 Analysis complete! This will help understand the NEW RELEASE structure.');
        
    } catch (error) {
        console.error('❌ Error examining file:', error.message);
    }
}

examineNewReleaseSection().catch(console.error);
