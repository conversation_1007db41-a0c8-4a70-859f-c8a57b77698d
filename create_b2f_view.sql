-- Create the B2F (Back to Front) pick view
-- This view shows discs that are candidates for moving from back stock to front stock

-- Drop the existing view first to allow column changes
DROP VIEW IF EXISTS public.v_b2f_pick_slim;

CREATE VIEW public.v_b2f_pick_slim AS
SELECT
  d.id,
  d.g_pull as disc,
  osl.id as osl_id,
  osl.g_code as osl,
  vs.discs_sold_last_30_days_retail + vs.discs_sold_last_30_days_dz as osl_sold_last_30_dz_plus_retail,
  d.grade as disc_grade,
  d.color_id,
  mps.release_date_online,
  mps.created_at as mps_created_at,
  vs.discs_in_stock_fs,
  vs.discs_in_stock_b2f,
  m.mold as mold_name
FROM
  t_discs d
  JOIN t_order_sheet_lines osl ON d.order_sheet_line_id = osl.id
  JOIN v_stats_by_osl vs ON osl.id = vs.id
  JOIN t_mps mps ON osl.mps_id = mps.id
  JOIN t_molds m ON mps.mold_id = m.id
WHERE
  vs.discs_in_stock_bs > 0
  AND (vs.discs_in_stock_fs + vs.discs_in_stock_b2f) = 0
  AND d.location = 'BS'::text
  AND d.sold_date IS NULL
ORDER BY
  d.order_sheet_line_id,
  d.g_pull;

-- Grant permissions
GRANT SELECT ON public.v_b2f_pick_slim TO anon;
GRANT SELECT ON public.v_b2f_pick_slim TO authenticated;
