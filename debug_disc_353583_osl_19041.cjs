require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function debugDisc353583Osl19041() {
  try {
    console.log('Debugging why disc 353583 does not get vendor OSL ID 19041...');
    
    const discId = 353583;
    const expectedOslId = 19041;
    const taskId = 313131;
    
    // Get disc details
    const { data: disc, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .eq('id', discId)
      .single();
    
    if (discError) {
      console.error('Error getting disc:', discError);
      return;
    }
    
    // Get OSL details
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, min_weight, max_weight, color_id, vendor_id')
      .eq('id', expectedOslId)
      .single();
    
    if (oslError) {
      console.error('Error getting OSL:', oslError);
      return;
    }
    
    // Get task details
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .select('id, task_type, payload, status, result, processed_at')
      .eq('id', taskId)
      .single();
    
    console.log('\n=== DISC 353583 DETAILS ===');
    console.log(`MPS ID: ${disc.mps_id}`);
    console.log(`Weight: ${disc.weight}g`);
    console.log(`Weight MFG: ${disc.weight_mfg}g`);
    console.log(`Color ID: ${disc.color_id}`);
    console.log(`Sold Date: ${disc.sold_date || 'NULL (unsold)'}`);
    console.log(`Current order_sheet_line_id: ${disc.order_sheet_line_id}`);
    console.log(`Current vendor_osl_id: ${disc.vendor_osl_id}`);
    
    console.log('\n=== OSL 19041 DETAILS ===');
    console.log(`MPS ID: ${osl.mps_id}`);
    console.log(`Weight Range: ${osl.min_weight}-${osl.max_weight}g`);
    console.log(`Color ID: ${osl.color_id}`);
    console.log(`Vendor ID: ${osl.vendor_id}`);
    
    console.log('\n=== TASK 313131 DETAILS ===');
    if (!taskError && task) {
      console.log(`Task Type: ${task.task_type}`);
      console.log(`Payload: ${JSON.stringify(task.payload)}`);
      console.log(`Status: ${task.status}`);
      console.log(`Processed: ${task.processed_at || 'Not yet processed'}`);
      if (task.result) {
        console.log(`Result: ${JSON.stringify(task.result, null, 2)}`);
      }
    }
    
    // Check basic matching criteria
    console.log('\n=== BASIC MATCHING CRITERIA ===');
    
    const mpsMatch = disc.mps_id === osl.mps_id;
    console.log(`1. MPS ID match (${disc.mps_id} === ${osl.mps_id}): ${mpsMatch ? '✅' : '❌'}`);
    
    const colorMatch = disc.color_id === osl.color_id || osl.color_id === 23;
    console.log(`2. Color match (${disc.color_id} === ${osl.color_id} OR ${osl.color_id} === 23): ${colorMatch ? '✅' : '❌'}`);
    
    const soldMatch = disc.sold_date === null;
    console.log(`3. Unsold disc (sold_date IS NULL): ${soldMatch ? '✅' : '❌'}`);
    
    const hasWeightMfg = disc.weight_mfg !== null;
    console.log(`4. Has manufacturer weight: ${hasWeightMfg ? '✅' : '❌'} (${disc.weight_mfg}g)`);
    
    if (hasWeightMfg) {
      const roundedWeightMfg = Math.round(disc.weight_mfg);
      const vendorWeightMatch = roundedWeightMfg >= osl.min_weight && roundedWeightMfg <= osl.max_weight;
      console.log(`5. Vendor weight match (ROUND(${disc.weight_mfg}) = ${roundedWeightMfg} >= ${osl.min_weight} AND <= ${osl.max_weight}): ${vendorWeightMatch ? '✅' : '❌'}`);
      
      const basicMatch = mpsMatch && colorMatch && soldMatch && hasWeightMfg && vendorWeightMatch;
      console.log(`\nBASIC VENDOR MATCH (without redirect): ${basicMatch ? '✅' : '❌'}`);
    }
    
    // Test the redirect function directly
    console.log('\n=== TESTING REDIRECT FUNCTION ===');
    
    if (hasWeightMfg) {
      console.log(`Testing find_matching_osl_by_mfg_weight_with_redirect for disc ${discId}...`);
      
      const { data: redirectResult, error: redirectError } = await supabase.rpc(
        'find_matching_osl_by_mfg_weight_with_redirect',
        {
          mps_id_param: disc.mps_id,
          color_id_param: disc.color_id,
          weight_mfg_param: disc.weight_mfg
        }
      );
      
      if (redirectError) {
        console.error('❌ Redirect function error:', redirectError);
      } else {
        console.log('✅ Redirect function result:', redirectResult);
        
        if (redirectResult && redirectResult.length > 0) {
          const result = redirectResult[0];
          console.log(`   OSL ID: ${result.osl_id}`);
          console.log(`   Redirect Info: ${result.redirect_info}`);
          
          if (result.osl_id === expectedOslId) {
            console.log('🎯 SUCCESS: Redirect function returns expected OSL 19041!');
          } else {
            console.log(`⚠️  Redirect function returns OSL ${result.osl_id}, expected ${expectedOslId}`);
            
            // Check details of returned OSL
            if (result.osl_id) {
              const { data: returnedOsl, error: returnedOslError } = await supabase
                .from('t_order_sheet_lines')
                .select('id, mps_id, min_weight, max_weight, color_id')
                .eq('id', result.osl_id)
                .single();
              
              if (!returnedOslError) {
                console.log(`   Returned OSL ${result.osl_id}: MPS ${returnedOsl.mps_id}, Weight ${returnedOsl.min_weight}-${returnedOsl.max_weight}g, Color ${returnedOsl.color_id}`);
              }
            }
          }
        } else {
          console.log('❌ Redirect function returned no results');
        }
      }
    }
    
    // Check if OSL 19041's MPS has redirect
    console.log('\n=== REDIRECT ANALYSIS ===');
    const { data: mpsRecord, error: mpsError } = await supabase
      .from('t_mps')
      .select('id, order_through_mps_id')
      .eq('id', osl.mps_id)
      .single();
    
    if (!mpsError && mpsRecord) {
      if (mpsRecord.order_through_mps_id) {
        console.log(`⚠️  OSL ${expectedOslId} belongs to MPS ${mpsRecord.id} which redirects to MPS ${mpsRecord.order_through_mps_id}`);
        console.log('   → This OSL should NOT be used for vendor mapping');
        console.log('   → Disc should be mapped to an OSL in the redirect MPS instead');
        
        // Find the correct OSL in the redirect MPS
        console.log(`\n=== FINDING CORRECT OSL IN REDIRECT MPS ${mpsRecord.order_through_mps_id} ===`);
        
        const { data: redirectOsls, error: redirectError } = await supabase
          .from('t_order_sheet_lines')
          .select('id, mps_id, min_weight, max_weight, color_id')
          .eq('mps_id', mpsRecord.order_through_mps_id)
          .in('color_id', disc.color_id === 23 ? [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23] : [disc.color_id, 23]);
        
        if (!redirectError && redirectOsls) {
          console.log(`Found ${redirectOsls.length} OSLs in redirect MPS ${mpsRecord.order_through_mps_id}:`);
          
          redirectOsls.forEach(redirectOsl => {
            const vendorMatch = disc.weight_mfg ? Math.round(disc.weight_mfg) >= redirectOsl.min_weight && Math.round(disc.weight_mfg) <= redirectOsl.max_weight : false;
            
            console.log(`  OSL ${redirectOsl.id}: Weight ${redirectOsl.min_weight}-${redirectOsl.max_weight}g, Color ${redirectOsl.color_id}`);
            console.log(`    Vendor match: ${vendorMatch ? '✅' : '❌'}`);
            
            if (vendorMatch) {
              console.log(`    🎯 CORRECT OSL: ${redirectOsl.id} should be used instead of ${expectedOslId}`);
            }
          });
        }
      } else {
        console.log(`✅ OSL ${expectedOslId} belongs to MPS ${mpsRecord.id} which has no redirect`);
        console.log('   → This OSL can be used for vendor mapping');
      }
    }
    
    // Check if the task was a match_osl_to_discs task and what OSL it was processing
    if (task && task.task_type === 'match_osl_to_discs' && task.payload && task.payload.id) {
      const processedOslId = task.payload.id;
      console.log(`\n=== TASK CONTEXT ===`);
      console.log(`Task was processing OSL ${processedOslId}`);
      
      if (processedOslId !== expectedOslId) {
        console.log(`⚠️  Task processed OSL ${processedOslId}, but you expected disc to get OSL ${expectedOslId}`);
        console.log('   → The task only maps discs to the OSL it\'s processing');
        console.log('   → For vendor mapping, it uses redirect logic to find the correct OSL for each disc');
      }
    }
    
    console.log('\n=== SUMMARY ===');
    console.log('Possible reasons disc 353583 does not get vendor OSL ID 19041:');
    console.log('1. Basic matching criteria not met (MPS, color, weight, sold status)');
    console.log('2. OSL 19041 belongs to MPS with redirect (vendor mapping uses different OSL)');
    console.log('3. Task was processing different OSL (match_osl_to_discs only maps to processed OSL)');
    console.log('4. Redirect function finds different/better matching OSL');
    console.log('5. Task processing error or disc not in candidate set');
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

debugDisc353583Osl19041();
