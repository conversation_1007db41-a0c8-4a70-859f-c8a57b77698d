import fetch from 'node-fetch';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const veeqoApiKey = process.env.VEEQO_API_KEY;
const logFile = 'veeqo_archived_exploration.log';

if (!veeqoApiKey) {
  console.error('Error: VEEQO_API_KEY must be set in .env file');
  process.exit(1);
}

// Function to log both to console and file
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  fs.appendFileSync(logFile, logMessage + '\n');
}

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, description) {
  try {
    log(`\n=== ${description} ===`);
    log(`Making request to: ${endpoint}`);
    
    const response = await fetch(endpoint, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqo<PERSON><PERSON><PERSON><PERSON>
      }
    });

    log(`Response status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      log(`Error response: ${errorText}`);
      return null;
    }

    const data = await response.json();
    log(`Response data structure:`);
    
    // Log the structure of the response
    if (Array.isArray(data)) {
      log(`- Array with ${data.length} items`);
      if (data.length > 0) {
        log(`- First item keys: ${Object.keys(data[0]).join(', ')}`);
        // Log first item in detail
        log(`- First item sample:`);
        log(JSON.stringify(data[0], null, 2));
      }
    } else if (typeof data === 'object' && data !== null) {
      log(`- Object with keys: ${Object.keys(data).join(', ')}`);
      log(`- Full object:`);
      log(JSON.stringify(data, null, 2));
    }
    
    return data;
  } catch (error) {
    log(`Error making request: ${error.message}`);
    return null;
  }
}

// Function to explore different endpoints and parameters
async function exploreVeeqoAPI() {
  log('Starting Veeqo API exploration for archived listings...');
  
  // Test basic products endpoint
  await makeVeeqoRequest(
    'https://api.veeqo.com/products?page=1&page_size=5',
    'Basic products endpoint (first 5)'
  );
  
  // Test sellables endpoint
  await makeVeeqoRequest(
    'https://api.veeqo.com/sellables?page=1&page_size=5',
    'Basic sellables endpoint (first 5)'
  );
  
  // Test products with different potential status filters
  const statusFilters = [
    'archived',
    'inactive',
    'disabled',
    'deleted',
    'hidden',
    'status=archived',
    'status=inactive',
    'active=false'
  ];
  
  for (const filter of statusFilters) {
    await makeVeeqoRequest(
      `https://api.veeqo.com/products?${filter}&page=1&page_size=5`,
      `Products with filter: ${filter}`
    );
  }
  
  // Test sellables with status filters
  for (const filter of statusFilters) {
    await makeVeeqoRequest(
      `https://api.veeqo.com/sellables?${filter}&page=1&page_size=5`,
      `Sellables with filter: ${filter}`
    );
  }
  
  // Test with filters array format (like the existing SKU filter)
  const arrayFilters = [
    'filters%5Bstatus%5D%5B%5D=archived',
    'filters%5Bstatus%5D%5B%5D=inactive',
    'filters%5Bactive%5D%5B%5D=false',
    'filters%5Barchived%5D%5B%5D=true'
  ];
  
  for (const filter of arrayFilters) {
    await makeVeeqoRequest(
      `https://api.veeqo.com/products?${filter}&page=1&page_size=5`,
      `Products with array filter: ${filter}`
    );
  }
  
  for (const filter of arrayFilters) {
    await makeVeeqoRequest(
      `https://api.veeqo.com/sellables?${filter}&page=1&page_size=5`,
      `Sellables with array filter: ${filter}`
    );
  }
  
  // Test other potential endpoints
  const otherEndpoints = [
    'https://api.veeqo.com/products/archived',
    'https://api.veeqo.com/sellables/archived',
    'https://api.veeqo.com/inventory',
    'https://api.veeqo.com/listings'
  ];
  
  for (const endpoint of otherEndpoints) {
    await makeVeeqoRequest(endpoint, `Testing endpoint: ${endpoint}`);
  }
  
  log('\n=== Exploration Complete ===');
  log(`Full log saved to: ${logFile}`);
}

// Run the exploration
exploreVeeqoAPI().catch(error => {
  log(`Fatal error: ${error.message}`);
  process.exit(1);
});
