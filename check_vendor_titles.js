import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkVendorTitles() {
    console.log('Checking vendor product titles...\n');
    
    try {
        // Get some specific product titles from the vendor data
        const { data: products, error: productsError } = await supabase
            .from('it_dd_osl')
            .select('product_title, variant_title, product_vendor')
            .eq('product_vendor', 'Dynamic Discs')
            .or('product_title.ilike.%Moonshine%,product_title.ilike.%Deputy%,product_title.ilike.%BioFuzion%')
            .limit(20);
        
        if (productsError) {
            console.error('Error fetching products:', productsError);
            return;
        }
        
        console.log('Vendor product titles containing Moonshine, Deputy, or BioFuzion:');
        console.log('================================================================');
        
        products.forEach((product, index) => {
            console.log(`${index + 1}. "${product.product_title}"`);
            console.log(`   Variant: "${product.variant_title}"`);
            console.log('---');
        });
        
    } catch (error) {
        console.error('Query failed:', error);
    }
}

// Run the query
checkVendorTitles();
