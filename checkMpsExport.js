import ExcelJS from 'exceljs';
import path from 'path';

async function checkMpsExport() {
    try {
        console.log('🔍 Checking MPS export file...\n');
        
        const filePath = path.join(process.cwd(), 'data', 'external data', 'test_mps_debug_2025-07-06-02-32-35.xlsx');
        
        console.log(`📁 Reading file: ${filePath}`);
        
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(filePath);
        
        const worksheet = workbook.getWorksheet(1);
        
        console.log('📊 Checking for MPS IDs in cells after line 332...\n');
        
        // Check specific rows from our debug test (367-370)
        const rowsToCheck = [367, 368, 369, 370];
        const columnsToCheck = ['M', 'N', 'O', 'P', 'Q', 'R']; // Weight columns
        
        console.log('📋 Sample of cells checked:');
        
        let totalCells = 0;
        let cellsWithMpsIds = 0;
        let cellsWithNoMps = 0;
        let emptyCells = 0;
        
        rowsToCheck.forEach(row => {
            columnsToCheck.forEach(col => {
                const cell = worksheet.getCell(`${col}${row}`);
                const value = cell.value;
                totalCells++;
                
                let displayValue = '(empty)';
                let icon = '';
                
                if (value === null || value === undefined || value === '') {
                    emptyCells++;
                } else if (value === 'NO_MPS') {
                    cellsWithNoMps++;
                    displayValue = 'NO_MPS';
                    icon = '❌';
                } else if (typeof value === 'number' && value > 0) {
                    cellsWithMpsIds++;
                    displayValue = value.toString();
                    icon = '🆔';
                } else {
                    displayValue = value.toString();
                    icon = '❓';
                }
                
                console.log(`   ${col}${row}: ${displayValue} ${icon}`);
            });
        });
        
        console.log(`\n📊 Summary:`);
        console.log(`   • Total cells checked: ${totalCells}`);
        console.log(`   • Cells with MPS IDs: ${cellsWithMpsIds}`);
        console.log(`   • Cells with NO_MPS: ${cellsWithNoMps}`);
        console.log(`   • Empty cells: ${emptyCells}`);
        
        if (cellsWithMpsIds > 0) {
            console.log(`\n✅ SUCCESS: Found MPS IDs in the exported file!`);
            console.log(`This proves that records after line 332 are being processed and have valid MPS mappings.`);
        } else {
            console.log(`\n⚠️  No MPS IDs found in checked cells.`);
        }
        
        console.log('\n🎉 Check completed!');
        
    } catch (error) {
        console.error('❌ Error checking MPS export:', error.message);
    }
}

checkMpsExport().catch(console.error);
