// debug_trigger_creation.js - Debug trigger creation with detailed error reporting
import 'dotenv/config';
import fs from 'fs';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in environment');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

async function execSql(sql) {
  console.log('Executing SQL:', sql.substring(0, 200) + '...');
  let res = await supabase.rpc('exec_sql', { sql_query: sql });
  if (res.error) {
    console.log('Trying with sql_statement parameter...');
    res = await supabase.rpc('exec_sql', { sql_statement: sql });
  }
  return res;
}

async function main() {
  try {
    const triggerSql = fs.readFileSync('sql/triggers/t_images_enqueue_variant_ready_on_image_verified.sql', 'utf8');
    console.log('Full SQL content:');
    console.log(triggerSql);
    console.log('\n' + '='.repeat(80) + '\n');

    // Split the SQL into individual statements
    const statements = triggerSql.split(';').filter(stmt => stmt.trim() !== '');
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        console.log(`Executing statement ${i + 1}:`);
        console.log(statement);
        console.log('-'.repeat(40));
        
        let { data, error } = await execSql(statement);
        if (error) {
          console.error(`Error in statement ${i + 1}:`, error);
          console.error('Error details:', JSON.stringify(error, null, 2));
        } else {
          console.log(`Statement ${i + 1} executed successfully`);
          if (data) {
            console.log('Result:', data);
          }
        }
        console.log('\n');
      }
    }

  } catch (e) {
    console.error('Fatal error:', e?.message || e);
    console.error('Stack trace:', e?.stack);
    process.exit(1);
  }
}

main();
