import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function assessDamage() {
    try {
        console.log('🔍 Assessing damage from recent changes...\n');
        
        // 1. Check if basic parsing is still working
        console.log('1. Checking basic parsing functionality...');
        
        const { data: totalRecords, error: totalError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id', { count: 'exact', head: true });

        if (totalError) {
            console.error('❌ Error getting total records:', totalError);
            return;
        }

        console.log(`📊 Total records in database: ${totalRecords.count}`);

        // 2. Check regular products (should be majority)
        const { data: regularProducts, error: regularError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, mold_name, plastic_name')
            .gt('excel_row_hint', 200)
            .lt('excel_row_hint', 220)
            .limit(10);

        if (regularError) {
            console.error('❌ Error getting regular products:', regularError);
        } else {
            console.log(`✅ Sample regular products (rows 200-220): ${regularProducts.length} found`);
            regularProducts.forEach((record, index) => {
                console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.plastic_name} ${record.mold_name}`);
            });
        }

        // 3. Check problematic rows
        console.log('\n2. Checking problematic rows...');
        
        const problemRows = [22, 24, 25, 27, 28, 126, 131, 132, 134, 135];
        
        for (const row of problemRows) {
            const { data: rowData, error: rowError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('excel_row_hint, excel_column, mold_name, plastic_name, raw_line_type')
                .eq('excel_row_hint', row);

            if (rowError) {
                console.error(`❌ Error checking row ${row}:`, rowError);
                continue;
            }

            const shouldBeEmpty = [22, 24, 27, 126, 131, 134].includes(row);
            const shouldHaveRecords = [25, 28, 132, 135].includes(row);

            if (shouldBeEmpty && rowData.length > 0) {
                console.log(`❌ Row ${row}: Should be EMPTY but has ${rowData.length} records`);
            } else if (shouldHaveRecords && rowData.length === 0) {
                console.log(`❌ Row ${row}: Should have records but is EMPTY`);
            } else if (shouldHaveRecords && rowData.length > 0) {
                console.log(`✅ Row ${row}: Has ${rowData.length} records (correct)`);
                rowData.forEach(record => {
                    console.log(`   Col ${record.excel_column}: ${record.plastic_name} ${record.mold_name}`);
                });
            } else {
                console.log(`✅ Row ${row}: Empty (correct)`);
            }
        }

        // 4. Check if export would work
        console.log('\n3. Checking export readiness...');
        
        const { data: exportableData, error: exportError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .limit(5);

        if (exportError) {
            console.error('❌ Error getting exportable data:', exportError);
        } else {
            console.log(`✅ Found ${exportableData.length} exportable records (sample):`);
            exportableData.forEach((record, index) => {
                console.log(`   ${index + 1}. ID: ${record.id}, Row: ${record.excel_row_hint}, Col: ${record.excel_column}`);
                console.log(`      MPS: ${record.calculated_mps_id || 'NO_MPS'}`);
            });
        }

        // 5. Check recent import batch
        console.log('\n4. Checking most recent import...');
        
        const { data: recentBatch, error: batchError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('import_batch_id, import_file_hash, created_at')
            .order('created_at', { ascending: false })
            .limit(1);

        if (batchError) {
            console.error('❌ Error getting recent batch:', batchError);
        } else if (recentBatch.length > 0) {
            console.log(`✅ Most recent import:`);
            console.log(`   Batch ID: ${recentBatch[0].import_batch_id}`);
            console.log(`   File Hash: ${recentBatch[0].import_file_hash}`);
            console.log(`   Created: ${recentBatch[0].created_at}`);
        }

        // 6. Recommendations
        console.log('\n5. Damage Assessment & Recommendations...');
        
        if (totalRecords.count < 500) {
            console.log('❌ CRITICAL: Very few records - import is severely broken');
            console.log('📋 RECOMMENDATION: REVERT or START OVER');
        } else if (totalRecords.count < 900) {
            console.log('⚠️  WARNING: Fewer records than expected - import partially broken');
            console.log('📋 RECOMMENDATION: Try targeted fixes or revert');
        } else {
            console.log('✅ GOOD: Record count looks reasonable');
            console.log('📋 RECOMMENDATION: Try targeted fixes for specific issues');
        }

        // Check if the enhanced export is causing issues
        const headerRowsWithRecords = problemRows.filter(row => {
            const shouldBeEmpty = [22, 24, 27, 126, 131, 134].includes(row);
            return shouldBeEmpty; // We'll check these
        });

        console.log('\n📋 SPECIFIC ISSUES FOUND:');
        console.log('1. Row 22 getting values when it should be empty (section header)');
        console.log('2. Enhanced MPS export not working (no t_order_sheet_lines.id in files)');
        console.log('3. Email not being sent');
        
        console.log('\n🔧 RECOMMENDED APPROACH:');
        console.log('1. FIRST: Test a simple export without enhanced MPS to see if basic functionality works');
        console.log('2. THEN: Fix the header row parsing issue');
        console.log('3. FINALLY: Re-implement enhanced MPS export more carefully');
        
    } catch (error) {
        console.error('❌ Assessment failed:', error.message);
    }
}

assessDamage().catch(console.error);
