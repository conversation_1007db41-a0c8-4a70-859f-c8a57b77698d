// debugInsert.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import minimist from 'minimist';

// Parse command-line arguments
const args = minimist(process.argv.slice(2));
const tableName = args.table || 't_discs';
const recordId = args.id || 418869;

// Create a Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  db: {
    schema: 'public',
  },
  auth: {
    persistSession: false,
  },
  global: {
    headers: {
      'x-debug-query': 'true' // This might enable query logging in some Supabase setups
    }
  }
});

async function main() {
  try {
    console.log(`Debugging insert for ${tableName} with record_id=${recordId}`);
    
    // First, let's check the schema of the t_images table
    console.log('Checking t_images table schema...');
    
    const { data: columns, error: schemaError } = await supabase
      .from('t_images')
      .select('*')
      .limit(1);
      
    if (schemaError) {
      console.error(`Error fetching schema: ${schemaError.message}`);
    } else {
      console.log('Sample record from t_images:');
      console.log(JSON.stringify(columns, null, 2));
    }
    
    // Now, let's try a direct insert with minimal fields
    console.log('\nAttempting minimal insert...');
    
    const insertData = {
      table_name: tableName,
      record_id: parseInt(recordId)
    };
    
    console.log(`Insert data: ${JSON.stringify(insertData)}`);
    
    const { data: insertResult, error: insertError } = await supabase
      .from('t_images')
      .insert(insertData);
      
    if (insertError) {
      console.error(`Error inserting record: ${insertError.message}`);
      console.error(`Full error: ${JSON.stringify(insertError)}`);
    } else {
      console.log(`Insert successful: ${JSON.stringify(insertResult)}`);
    }
    
    // Try a raw SQL insert as a last resort
    console.log('\nAttempting raw SQL insert...');
    
    const { data: rawResult, error: rawError } = await supabase
      .rpc('execute_raw_sql', {
        sql: `INSERT INTO t_images (table_name, record_id, created_by, created_at) 
              VALUES ('${tableName}', ${recordId}, 'system', NOW()) 
              RETURNING id`
      });
      
    if (rawError) {
      console.error(`Raw SQL error: ${rawError.message}`);
      console.error(`Full error: ${JSON.stringify(rawError)}`);
      
      // If the RPC function doesn't exist, try a different approach
      if (rawError.message.includes('function execute_raw_sql() does not exist')) {
        console.log('\nRPC function not found, trying direct query...');
        
        // Try a direct query
        const { data: queryResult, error: queryError } = await supabase
          .from('t_images')
          .select('id')
          .eq('table_name', tableName)
          .eq('record_id', recordId);
          
        if (queryError) {
          console.error(`Query error: ${queryError.message}`);
        } else {
          console.log(`Query result: ${JSON.stringify(queryResult)}`);
        }
      }
    } else {
      console.log(`Raw SQL insert successful: ${JSON.stringify(rawResult)}`);
    }
    
    // Check if the record exists now
    console.log('\nChecking if record exists...');
    
    const { data: checkResult, error: checkError } = await supabase
      .from('t_images')
      .select('*')
      .eq('table_name', tableName)
      .eq('record_id', recordId);
      
    if (checkError) {
      console.error(`Check error: ${checkError.message}`);
    } else {
      console.log(`Check result: ${JSON.stringify(checkResult, null, 2)}`);
    }
  } catch (err) {
    console.error(`Unexpected error: ${err.message}`);
    process.exit(1);
  }
}

main();
