// processFixOslShopifyProductWo3OptionsTask.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Shopify credentials
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

// Helper: log errors to t_error_logs
async function logError(errorMessage, context) {
  try {
    const { error } = await supabase
      .from("t_error_logs")
      .insert({ error_message: errorMessage, context });
    if (error) console.error("Failed to log error:", error);
  } catch (err) {
    console.error("Exception while logging error:", err);
  }
}

// Helper: update task status
async function updateTaskStatus(taskId, status, result = null) {
  try {
    const updateData = {
      status: status,
      processed_at: new Date().toISOString()
    };

    if (status === 'completed' || status === 'error' || status === 'pending') {
      updateData.locked_at = null;
      updateData.locked_by = null;
    }

    if (result) {
      updateData.result = result;
    }

    const { error } = await supabase
      .from('t_task_queue')
      .update(updateData)
      .eq('id', taskId);

    if (error) {
      console.error(`[processFixOslShopifyProductWo3OptionsTask] Error updating task status: ${error.message}`);
      await logError(error.message, `Updating task ${taskId} status`);
      return false;
    }

    return true;
  } catch (err) {
    console.error(`[processFixOslShopifyProductWo3OptionsTask] Exception updating task status: ${err.message}`);
    await logError(err.message, `Updating task ${taskId} status`);
    return false;
  }
}

// Helper: execute Shopify GraphQL request
async function executeShopifyGraphQL(query, variables = {}) {
  try {
    const response = await fetch(shopifyEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": shopifyAccessToken
      },
      body: JSON.stringify({ query, variables })
    });

    const result = await response.json();

    if (!response.ok || result.errors) {
      throw new Error(`GraphQL error: ${JSON.stringify(result.errors || result.data)}`);
    }

    return result.data;
  } catch (error) {
    console.error(`[processFixOslShopifyProductWo3OptionsTask] GraphQL request failed:`, error);
    throw error;
  }
}

// Helper: delete Shopify product by ID
async function deleteShopifyProduct(productId) {
  try {
    console.log(`[processFixOslShopifyProductWo3OptionsTask] Deleting Shopify product: ${productId}`);

    const mutation = `
      mutation productDelete($input: ProductDeleteInput!) {
        productDelete(input: $input) {
          deletedProductId
          userErrors {
            field
            message
          }
        }
      }
    `;

    // Ensure productId is in GID format
    const gidProductId = productId.startsWith('gid://shopify/Product/') 
      ? productId 
      : `gid://shopify/Product/${productId}`;

    const variables = {
      input: {
        id: gidProductId
      }
    };

    const result = await executeShopifyGraphQL(mutation, variables);

    if (result.productDelete.userErrors && result.productDelete.userErrors.length > 0) {
      const errors = result.productDelete.userErrors.map(err => `${err.field}: ${err.message}`).join(', ');
      throw new Error(`Shopify deletion errors: ${errors}`);
    }

    console.log(`[processFixOslShopifyProductWo3OptionsTask] Successfully deleted product: ${result.productDelete.deletedProductId}`);
    
    return {
      success: true,
      deletedProductId: result.productDelete.deletedProductId
    };
  } catch (error) {
    console.error(`[processFixOslShopifyProductWo3OptionsTask] Error deleting product ${productId}:`, error);
    throw error;
  }
}

/**
 * Process fix_osl_shopify_product_wo_3_options task
 * This task fixes OSL Shopify products that don't have exactly 3 options by:
 * 1. Deleting the incorrectly created Shopify product
 * 2. Resetting all OSLs with the same mps_id to shopify_uploaded_at = null
 * 3. Resetting the original task to 'pending' status so it will retry
 */
export async function processFixOslShopifyProductWo3OptionsTask(task) {
  console.log(`[processFixOslShopifyProductWo3OptionsTask] Processing task ${task.id}`);

  try {
    // Parse the payload
    let payload;
    try {
      if (typeof task.payload === 'object' && task.payload !== null) {
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[processFixOslShopifyProductWo3OptionsTask] Error parsing payload: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to fix OSL Shopify product. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[processFixOslShopifyProductWo3OptionsTask] Parsed payload: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.mps_id || !payload.original_task_id) {
      const errMsg = `[processFixOslShopifyProductWo3OptionsTask] Missing required fields in payload: mps_id=${payload.mps_id}, original_task_id=${payload.original_task_id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to fix OSL Shopify product. Missing required fields in payload.",
        error: 'Missing mps_id or original_task_id in payload'
      });
      return;
    }

    const mpsId = payload.mps_id;
    const shopifyProductId = payload.shopify_product_id;
    const originalTaskId = payload.original_task_id;

    console.log(`[processFixOslShopifyProductWo3OptionsTask] Fixing OSL Shopify product for MPS ${mpsId}, Shopify product ${shopifyProductId}, original task ${originalTaskId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    let deletionResult = null;
    let deletionError = null;

    // Step 1: Delete the Shopify product if we have the product ID
    if (shopifyProductId) {
      try {
        deletionResult = await deleteShopifyProduct(shopifyProductId);
        console.log(`[processFixOslShopifyProductWo3OptionsTask] Successfully deleted Shopify product ${shopifyProductId}`);
      } catch (error) {
        deletionError = error.message;
        console.error(`[processFixOslShopifyProductWo3OptionsTask] Failed to delete Shopify product ${shopifyProductId}: ${error.message}`);
        // Continue with the process even if deletion fails
      }
    } else {
      console.log(`[processFixOslShopifyProductWo3OptionsTask] No Shopify product ID provided, skipping deletion`);
    }

    // Step 2: Reset all OSLs with the same mps_id to shopify_uploaded_at = null
    console.log(`[processFixOslShopifyProductWo3OptionsTask] Resetting OSLs with mps_id ${mpsId} to shopify_uploaded_at = null`);
    
    const { data: updatedOsls, error: updateOslsError } = await supabase
      .from('t_order_sheet_lines')
      .update({ 
        shopify_uploaded_at: null,
        shopify_product_uploaded_notes: `Reset due to incorrect Shopify product option structure - product deleted and will be recreated`
      })
      .eq('mps_id', mpsId)
      .select('id');

    if (updateOslsError) {
      const errMsg = `[processFixOslShopifyProductWo3OptionsTask] Error resetting OSLs: ${updateOslsError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Resetting OSLs for MPS ${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to reset OSL records after product deletion.",
        error: updateOslsError.message,
        mps_id: mpsId,
        shopify_product_id: shopifyProductId,
        deletion_result: deletionResult,
        deletion_error: deletionError
      });
      return;
    }

    const resetOslCount = updatedOsls ? updatedOsls.length : 0;
    console.log(`[processFixOslShopifyProductWo3OptionsTask] Reset ${resetOslCount} OSL records for MPS ${mpsId}`);

    // Step 3: Reset the original task to 'pending' status so it will retry
    console.log(`[processFixOslShopifyProductWo3OptionsTask] Resetting original task ${originalTaskId} to pending status`);
    
    const { error: resetTaskError } = await supabase
      .from('t_task_queue')
      .update({ 
        status: 'pending',
        locked_at: null,
        locked_by: null,
        processed_at: null,
        result: null
      })
      .eq('id', originalTaskId);

    if (resetTaskError) {
      const errMsg = `[processFixOslShopifyProductWo3OptionsTask] Error resetting original task: ${resetTaskError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Resetting task ${originalTaskId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to reset original task to pending status.",
        error: resetTaskError.message,
        mps_id: mpsId,
        shopify_product_id: shopifyProductId,
        reset_osl_count: resetOslCount,
        deletion_result: deletionResult,
        deletion_error: deletionError
      });
      return;
    }

    console.log(`[processFixOslShopifyProductWo3OptionsTask] Successfully reset original task ${originalTaskId} to pending`);

    // Mark this task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully fixed OSL Shopify product with incorrect option structure. Deleted product, reset ${resetOslCount} OSL records, and reset original task to pending.`,
      mps_id: mpsId,
      shopify_product_id: shopifyProductId,
      original_task_id: originalTaskId,
      reset_osl_count: resetOslCount,
      deletion_result: deletionResult,
      deletion_error: deletionError,
      actions_completed: [
        shopifyProductId ? 'product_deleted' : 'no_product_to_delete',
        'osls_reset',
        'original_task_reset'
      ]
    });

    console.log(`[processFixOslShopifyProductWo3OptionsTask] Successfully completed fix task ${task.id} for MPS ${mpsId}`);

  } catch (error) {
    const errMsg = `[processFixOslShopifyProductWo3OptionsTask] Unexpected error processing task ${task.id}: ${error.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to fix OSL Shopify product due to unexpected error.",
      error: error.message
    });
  }
}
