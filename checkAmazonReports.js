// checkAmazonReports.js - Check available Amazon Active Listings Reports and their import status

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Configuration
const DATA_DIR = 'C:\\Users\\<USER>\\supabase_project\\data\\external data\\Amazon Active Listings Report';
const TABLE_NAME = 'it_amaz_active_listings_report';

async function checkReports() {
  try {
    console.log('🔍 Checking Amazon Active Listings Reports...\n');

    // Check if directory exists
    if (!fs.existsSync(DATA_DIR)) {
      console.error(`❌ Directory does not exist: ${DATA_DIR}`);
      return;
    }

    // Get all files in directory
    const allFiles = fs.readdirSync(DATA_DIR);
    console.log('📁 All files in directory:');
    allFiles.forEach(file => console.log(`  ${file}`));
    console.log('');

    // Filter for Active Listings Report files
    const reportFiles = allFiles
      .filter(file => file.match(/^Active\+Listings\+Report\+\d{2}-\d{2}-\d{4}\.txt$/))
      .map(file => {
        const match = file.match(/Active\+Listings\+Report\+(\d{2})-(\d{2})-(\d{4})\.txt/);
        if (match) {
          const [, month, day, year] = match;
          const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
          return {
            filename: file,
            fullPath: path.join(DATA_DIR, file),
            date: date,
            dateString: `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`,
            displayDate: `${month}/${day}/${year}`
          };
        }
        return null;
      })
      .filter(Boolean)
      .sort((a, b) => b.date - a.date); // Sort by date descending (newest first)

    if (reportFiles.length === 0) {
      console.log('❌ No Active Listings Report files found in the directory');
      console.log('📝 Expected filename format: Active+Listings+Report+MM-DD-YYYY.txt');
      return;
    }

    console.log('📊 Found Active Listings Report files:');
    
    // Check import status for each file
    for (const file of reportFiles) {
      const { data, error } = await supabase
        .from(TABLE_NAME)
        .select('id, created_at')
        .eq('report_date', file.dateString)
        .limit(1);

      let status = '❓ Unknown';
      let importDate = '';
      
      if (error) {
        status = `❌ Error: ${error.message}`;
      } else if (!data || data.length === 0) {
        status = '✅ Ready to import';
      } else {
        status = '✅ Already imported';
        importDate = ` (imported: ${new Date(data[0].created_at).toLocaleString()})`;
      }

      console.log(`  📄 ${file.filename}`);
      console.log(`     Date: ${file.displayDate} (${file.dateString})`);
      console.log(`     Status: ${status}${importDate}`);
      console.log('');
    }

    // Get database summary
    const { data: dbData, error: dbError } = await supabase
      .from(TABLE_NAME)
      .select('report_date, created_at')
      .order('report_date', { ascending: false })
      .limit(10);

    if (dbError) {
      console.error('❌ Error getting database summary:', dbError.message);
    } else {
      console.log('💾 Recent imports in database:');
      const uniqueReports = [...new Set(dbData.map(r => r.report_date))];
      uniqueReports.forEach(reportDate => {
        const importRecord = dbData.find(r => r.report_date === reportDate);
        const importDate = new Date(importRecord.created_at).toLocaleString();
        console.log(`  📅 ${reportDate} (imported: ${importDate})`);
      });
    }

    console.log('\n💡 To import a new report:');
    console.log('1. Download the latest Active Listings Report from Amazon Seller Central');
    console.log('2. Save it to: ' + DATA_DIR);
    console.log('3. Make sure the filename follows the format: Active+Listings+Report+MM-DD-YYYY.txt');
    console.log('4. Click the import button in the admin interface');

  } catch (error) {
    console.error('❌ Error checking reports:', error.message);
  }
}

checkReports().catch(console.error);
