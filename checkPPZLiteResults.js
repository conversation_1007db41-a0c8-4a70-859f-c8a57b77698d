import http from 'http';

function checkPPZLiteResults() {
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/discraft/review-parsing-issues',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const result = JSON.parse(data);
        console.log('Parsing issues result:', JSON.stringify(result, null, 2));
        
        // Look for PP Z Lite products specifically
        if (result.success && result.unmatchedVendorProducts) {
          console.log('\n🔍 Looking for PP Z Lite products in unmatched vendor products...');
          
          const ppzLiteProducts = result.unmatchedVendorProducts.filter(product => 
            product.plastic_name === 'Elite Z Lite' && 
            product.raw_line_type && 
            product.raw_line_type.includes('PP Z Lite')
          );
          
          console.log(`\nFound ${ppzLiteProducts.length} PP Z Lite products:`);
          ppzLiteProducts.forEach(product => {
            console.log(`  ${product.mold_name}: ${product.min_weight}-${product.max_weight}g (${product.raw_weight_range})`);
          });
          
          if (ppzLiteProducts.length > 0) {
            const correctRanges = ppzLiteProducts.filter(p => 
              (p.min_weight === 141 && p.max_weight === 150) ||
              (p.min_weight === 151 && p.max_weight === 159) ||
              (p.min_weight === 160 && p.max_weight === 166)
            );
            
            console.log(`\n✅ PP Z Lite products with correct weight ranges: ${correctRanges.length}/${ppzLiteProducts.length}`);
            
            if (correctRanges.length === ppzLiteProducts.length) {
              console.log('🎉 All PP Z Lite products have correct weight ranges!');
            } else {
              console.log('❌ Some PP Z Lite products still have incorrect weight ranges');
            }
          }
        }
        
      } catch (err) {
        console.log('Raw response:', data);
      }
    });
  });

  req.on('error', (err) => {
    console.error('Error:', err.message);
  });

  req.end();
}

console.log('Checking PP Z Lite parsing results...');
checkPPZLiteResults();
