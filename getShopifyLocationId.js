// getShopifyLocationId.js - <PERSON>ript to get the Shopify location ID

import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

// Shopify GraphQL API endpoint
const SHOPIFY_GRAPHQL_URL = process.env.SHOPIFY_ENDPOINT;
const SHOPIFY_ACCESS_TOKEN = process.env.SHOPIFY_ACCESS_TOKEN;

/**
 * Execute a GraphQL query against the Shopify API
 * @param {string} query - The GraphQL query to execute
 * @param {Object} variables - Variables for the GraphQL query
 * @returns {Promise<Object>} - The query result
 */
async function executeShopifyGraphQL(query, variables = {}) {
  if (!SHOPIFY_GRAPHQL_URL || !SHOPIFY_ACCESS_TOKEN) {
    throw new Error('Shopify GraphQL URL or access token not configured in environment variables');
  }

  try {
    const response = await fetch(SHOPIFY_GRAPHQL_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': SHOPIFY_ACCESS_TOKEN
      },
      body: JSON.stringify({
        query,
        variables
      })
    });

    const result = await response.json();

    if (result.errors) {
      console.error('GraphQL Errors:', JSON.stringify(result.errors, null, 2));
      throw new Error(`GraphQL Error: ${result.errors[0].message}`);
    }

    return result.data;
  } catch (error) {
    console.error('Error executing Shopify GraphQL query:', error);
    throw error;
  }
}

/**
 * Get all locations from Shopify
 * @returns {Promise<Array>} - Array of locations
 */
async function getLocations() {
  const query = `
    query getLocations {
      locations(first: 10) {
        edges {
          node {
            id
            name
            isActive
            address {
              formatted
            }
          }
        }
      }
    }
  `;

  try {
    const result = await executeShopifyGraphQL(query);
    return result.locations.edges.map(edge => edge.node);
  } catch (error) {
    console.error('Error getting locations:', error);
    throw error;
  }
}

// Main function
async function main() {
  try {
    console.log('Fetching Shopify locations...');
    const locations = await getLocations();
    
    console.log('\nShopify Locations:');
    console.log('==================');
    
    locations.forEach(location => {
      console.log(`ID: ${location.id}`);
      console.log(`Name: ${location.name}`);
      console.log(`Active: ${location.isActive}`);
      if (location.address && location.address.formatted) {
        console.log(`Address: ${location.address.formatted}`);
      }
      console.log('------------------');
    });
    
    console.log('\nTo use a location, add the following to your .env file:');
    console.log('SHOPIFY_LOCATION_ID=<location_id>');
    
    if (locations.length > 0) {
      const primaryLocation = locations.find(loc => loc.isActive) || locations[0];
      console.log(`\nRecommended location (${primaryLocation.name}):`);
      console.log(`SHOPIFY_LOCATION_ID=${primaryLocation.id}`);
    }
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

main();
