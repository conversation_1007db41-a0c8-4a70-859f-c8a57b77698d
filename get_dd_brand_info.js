import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function getDynamicDiscsBrandInfo() {
    console.log('Getting Dynamic Discs brand information...\n');
    
    try {
        // 1. Get Dynamic Discs brand info
        const { data: brandData, error: brandError } = await supabase
            .from('t_brands')
            .select('*')
            .eq('brand', 'Dynamic Discs');
        
        if (brandError) {
            console.error('Error fetching brand data:', brandError);
            return;
        }
        
        console.log('Dynamic Discs brand data:');
        console.log(brandData);
        
        if (brandData.length === 0) {
            console.log('No Dynamic Discs brand found');
            return;
        }
        
        const ddBrandId = brandData[0].id;
        console.log(`\nDynamic Discs brand_id: ${ddBrandId}`);
        
        // 2. Get sample product titles from the imported data
        const { data: sampleTitles, error: titlesError } = await supabase
            .from('it_dd_osl')
            .select('product_title, variant_title')
            .eq('product_vendor', 'Dynamic Discs')
            .limit(20);
        
        if (titlesError) {
            console.error('Error fetching sample titles:', titlesError);
            return;
        }
        
        console.log('\nSample Dynamic Discs product titles:');
        console.log('=====================================');
        sampleTitles.forEach((item, index) => {
            console.log(`${index + 1}. Product: "${item.product_title}"`);
            console.log(`   Variant: "${item.variant_title}"`);
            console.log('---');
        });
        
        // 3. Get Dynamic Discs molds
        const { data: ddMolds, error: moldsError } = await supabase
            .from('t_molds')
            .select('mold, code')
            .eq('brand_id', ddBrandId)
            .order('mold');
        
        if (moldsError) {
            console.error('Error fetching molds:', moldsError);
        } else {
            console.log(`\nDynamic Discs molds (${ddMolds.length} total):`);
            console.log('=====================================');
            ddMolds.forEach(mold => {
                console.log(`${mold.mold} (${mold.code})`);
            });
        }
        
        // 4. Get Dynamic Discs plastics
        const { data: ddPlastics, error: plasticsError } = await supabase
            .from('t_plastics')
            .select('plastic, code')
            .eq('brand_id', ddBrandId)
            .order('plastic');
        
        if (plasticsError) {
            console.error('Error fetching plastics:', plasticsError);
        } else {
            console.log(`\nDynamic Discs plastics (${ddPlastics.length} total):`);
            console.log('=====================================');
            ddPlastics.forEach(plastic => {
                console.log(`${plastic.plastic} (${plastic.code})`);
            });
        }
        
        // 5. Get some sample stamps
        const { data: sampleStamps, error: stampsError } = await supabase
            .from('t_stamps')
            .select('stamp')
            .limit(20)
            .order('stamp');
        
        if (stampsError) {
            console.error('Error fetching stamps:', stampsError);
        } else {
            console.log(`\nSample stamps (first 20):`);
            console.log('=====================================');
            sampleStamps.forEach(stamp => {
                console.log(`${stamp.stamp}`);
            });
        }
        
    } catch (error) {
        console.error('Query failed:', error);
    }
}

// Run the query
getDynamicDiscsBrandInfo();
