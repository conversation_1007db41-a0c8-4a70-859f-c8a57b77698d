import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkVerifyImageTasks() {
  try {
    console.log('🔍 Checking verify_t_images_image tasks...\n');
    
    // Check for pending tasks
    const { data: pendingTasks, error: pendingError } = await supabase
      .from('t_task_queue')
      .select('id, status, created_at, scheduled_at, processed_at, locked_at, locked_by, parameters, result')
      .eq('task_type', 'verify_t_images_image')
      .eq('status', 'pending')
      .order('created_at', { ascending: false });
    
    if (pendingError) {
      console.error('❌ Error fetching pending tasks:', pendingError);
    } else {
      console.log(`📋 Pending verify_t_images_image tasks: ${pendingTasks?.length || 0}`);
      if (pendingTasks && pendingTasks.length > 0) {
        for (const task of pendingTasks) {
          console.log(`   - Task ${task.id}: scheduled ${task.scheduled_at}, locked: ${task.locked_at || 'null'}, locked_by: ${task.locked_by || 'null'}`);
          if (task.parameters) {
            console.log(`     Parameters: ${JSON.stringify(task.parameters)}`);
          }
        }
      }
    }
    
    // Check for processing tasks
    const { data: processingTasks, error: processingError } = await supabase
      .from('t_task_queue')
      .select('id, status, created_at, scheduled_at, processed_at, locked_at, locked_by, parameters')
      .eq('task_type', 'verify_t_images_image')
      .eq('status', 'processing')
      .order('created_at', { ascending: false });
    
    if (!processingError) {
      console.log(`\n🔄 Processing verify_t_images_image tasks: ${processingTasks?.length || 0}`);
      if (processingTasks && processingTasks.length > 0) {
        for (const task of processingTasks) {
          console.log(`   - Task ${task.id}: locked ${task.locked_at}, locked_by: ${task.locked_by}`);
        }
      }
    }
    
    // Check for tasks with processed_at but still pending status (the issue you mentioned)
    const { data: stuckTasks, error: stuckError } = await supabase
      .from('t_task_queue')
      .select('id, status, created_at, scheduled_at, processed_at, locked_at, locked_by, parameters, result')
      .eq('task_type', 'verify_t_images_image')
      .eq('status', 'pending')
      .not('processed_at', 'is', null)
      .order('created_at', { ascending: false });
    
    if (!stuckError) {
      console.log(`\n⚠️ Tasks stuck with processed_at but pending status: ${stuckTasks?.length || 0}`);
      if (stuckTasks && stuckTasks.length > 0) {
        for (const task of stuckTasks) {
          console.log(`   - Task ${task.id}: processed ${task.processed_at} but status is ${task.status}`);
          console.log(`     Locked: ${task.locked_at || 'null'}, Locked by: ${task.locked_by || 'null'}`);
          if (task.result) {
            console.log(`     Result: ${JSON.stringify(task.result)}`);
          }
        }
      }
    }
    
    // Check recent completed tasks for comparison
    const { data: recentCompleted, error: completedError } = await supabase
      .from('t_task_queue')
      .select('id, status, created_at, processed_at, result')
      .eq('task_type', 'verify_t_images_image')
      .eq('status', 'completed')
      .order('processed_at', { ascending: false })
      .limit(5);
    
    if (!completedError && recentCompleted) {
      console.log(`\n✅ Recent completed verify_t_images_image tasks: ${recentCompleted.length}`);
      for (const task of recentCompleted) {
        console.log(`   - Task ${task.id}: completed ${task.processed_at}`);
        if (task.result) {
          const result = typeof task.result === 'string' ? JSON.parse(task.result) : task.result;
          console.log(`     Result: ${result.message || JSON.stringify(result)}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

checkVerifyImageTasks();
