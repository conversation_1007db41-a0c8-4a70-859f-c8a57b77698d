// directApplyFix.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Create a Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Applying fix to trigger function...');
    
    // Read the SQL file
    const sql = fs.readFileSync('fix_trigger_timeout.sql', 'utf8');
    
    // Log the SQL for manual execution
    console.log('\nSQL to execute manually:');
    console.log(sql);
    
    console.log('\nPlease execute the above SQL in your database management tool.');
    console.log('After executing the SQL, run the following script to test the fix:');
    console.log('node testTriggerFix.js');
  } catch (err) {
    console.error(`Unexpected error: ${err.message}`);
    process.exit(1);
  }
}

main();
