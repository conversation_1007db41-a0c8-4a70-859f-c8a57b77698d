import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function getOrderSummaryData() {
    // Get ALL orderable products from the base table (same logic as updated daily automation)
    let allOrderableData = [];
    let from = 0;
    const pageSize = 1000;
    
    while (true) {
        const { data: batch, error: orderableError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .range(from, from + pageSize - 1)
            .order('excel_mapping_key');

        if (orderableError) {
            throw new Error(`Failed to query orderable data: ${orderableError.message}`);
        }

        if (batch.length === 0) break;
        
        allOrderableData = allOrderableData.concat(batch);
        from += pageSize;
        
        if (batch.length < pageSize) break;
    }

    // Get order quantities from the view
    let viewOrderData = [];
    from = 0;
    
    while (true) {
        const { data: batch, error: viewError } = await supabase
            .from('v_stats_by_osl_discraft')
            .select('excel_mapping_key, "order", mold_name, plastic_name, is_currently_available')
            .not('excel_mapping_key', 'is', null)
            .range(from, from + pageSize - 1);

        if (viewError) {
            throw new Error(`Failed to query view order data: ${viewError.message}`);
        }

        if (batch.length === 0) break;
        
        viewOrderData = viewOrderData.concat(batch);
        from += pageSize;
        
        if (batch.length < pageSize) break;
    }

    // Create a map of order data by excel_mapping_key
    const orderMap = {};
    viewOrderData.forEach(item => {
        orderMap[item.excel_mapping_key] = {
            order: item.order || 0,
            mold_name: item.mold_name,
            plastic_name: item.plastic_name,
            is_currently_available: item.is_currently_available
        };
    });

    // Combine all orderable products with their order quantities (defaulting to 0)
    const orderableData = allOrderableData.map(item => {
        const orderInfo = orderMap[item.excel_mapping_key] || {};
        return {
            excel_mapping_key: item.excel_mapping_key,
            excel_column: item.excel_column,
            excel_row_hint: item.excel_row_hint,
            order: orderInfo.order || 0,
            mold_name: orderInfo.mold_name || 'Unknown',
            plastic_name: orderInfo.plastic_name || 'Unknown',
            is_currently_available: orderInfo.is_currently_available || false
        };
    });

    return orderableData;
}

async function testExportWithDailyAutomationData() {
  try {
    console.log('🧪 Testing export with daily automation data...\n');
    
    // Get the order summary data (same as daily automation)
    console.log('1. Getting order summary data...');
    const orderData = await getOrderSummaryData();
    
    console.log(`✅ Order summary completed: ${orderData.length} records`);
    
    // Count records with orders vs 0s
    const withOrders = orderData.filter(item => item.order > 0).length;
    const withZeros = orderData.filter(item => item.order === 0).length;
    console.log(`   • Records with orders > 0: ${withOrders}`);
    console.log(`   • Records with order = 0: ${withZeros}`);
    
    // Check records after line 332
    const afterLine332 = orderData.filter(item => item.excel_row_hint > 332);
    console.log(`   • Records after line 332: ${afterLine332.length}`);
    
    // Call the export API with this data
    console.log('\n2. Calling export API...');
    const response = await fetch('http://localhost:3001/api/discraft/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        includeHeader: false,
        filename: `test_daily_automation_export_${new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0]}.xlsx`,
        orderData: orderData
      })
    });

    if (!response.ok) {
      throw new Error(`Export API returned ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Export completed successfully!');
    console.log(`📄 Filename: ${result.filename}`);
    console.log(`📁 File path: ${result.filePath}`);
    console.log(`📊 Total records processed: ${result.totalRecords}`);
    
    console.log('\n🎉 Test completed! This simulates exactly what the daily automation will do.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testExportWithDailyAutomationData().catch(console.error);
