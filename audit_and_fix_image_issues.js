import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function auditAndFixImageIssues() {
  try {
    console.log('🔍 Auditing and fixing image issues across the system...\n');
    
    // Get image configuration
    const { data: publicServerData } = await supabase
      .from('t_config')
      .select('value')
      .eq('key', 'public_image_server')
      .single();

    const { data: folderData } = await supabase
      .from('t_config')
      .select('value')
      .eq('key', 'folder_molds')
      .single();

    if (!publicServerData || !folderData) {
      console.error('❌ Could not get image configuration');
      return;
    }

    const baseImageUrl = `${publicServerData.value}/${folderData.value}`;
    console.log('📁 Base image URL:', baseImageUrl);

    // 1. Find all molds with image upload failures in their notes
    console.log('\n1️⃣ Finding molds with image upload failures...');
    const { data: moldsWithImageErrors, error: moldErrorsError } = await supabase
      .from('t_molds')
      .select('id, mold, brand_id, shopify_collection_uploaded_notes')
      .like('shopify_collection_uploaded_notes', '%Image upload failed%');

    if (moldErrorsError) {
      console.error('❌ Error fetching molds with image errors:', moldErrorsError);
      return;
    }

    console.log(`Found ${moldsWithImageErrors?.length || 0} molds with image upload failures`);

    // 2. Find all verified images for molds and check if they actually exist
    console.log('\n2️⃣ Checking verified images for actual accessibility...');
    const { data: verifiedImages, error: verifiedImagesError } = await supabase
      .from('t_images')
      .select('id, record_id, image_verified, ximage_file_namex')
      .eq('table_name', 't_molds')
      .eq('image_verified', true);

    if (verifiedImagesError) {
      console.error('❌ Error fetching verified images:', verifiedImagesError);
      return;
    }

    console.log(`Found ${verifiedImages?.length || 0} verified mold images to check`);

    let issuesFound = 0;
    let issuesFixed = 0;

    // Check each verified image
    for (const image of verifiedImages || []) {
      const imageUrl = `${baseImageUrl}/${image.record_id}.jpg`;
      
      try {
        const response = await fetch(imageUrl, { method: 'HEAD' });
        if (!response.ok) {
          console.log(`❌ Image not accessible for mold ${image.record_id}: ${imageUrl} (Status: ${response.status})`);
          issuesFound++;
          
          // Mark as unverified
          const { error: updateError } = await supabase
            .from('t_images')
            .update({ 
              image_verified: false,
              updated_at: new Date().toISOString()
            })
            .eq('id', image.id);
          
          if (updateError) {
            console.log(`   ❌ Error marking image ${image.id} as unverified: ${updateError.message}`);
          } else {
            console.log(`   ✅ Marked image ${image.id} as unverified`);
            issuesFixed++;
          }
        } else {
          console.log(`✅ Image accessible for mold ${image.record_id}: ${imageUrl}`);
        }
      } catch (fetchError) {
        console.log(`❌ Error checking image for mold ${image.record_id}: ${fetchError.message}`);
        issuesFound++;
        
        // Mark as unverified
        const { error: updateError } = await supabase
          .from('t_images')
          .update({ 
            image_verified: false,
            updated_at: new Date().toISOString()
          })
          .eq('id', image.id);
        
        if (updateError) {
          console.log(`   ❌ Error marking image ${image.id} as unverified: ${updateError.message}`);
        } else {
          console.log(`   ✅ Marked image ${image.id} as unverified`);
          issuesFixed++;
        }
      }
      
      // Small delay to avoid overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 3. Fix molds with image upload failures
    console.log('\n3️⃣ Fixing molds with image upload failures...');
    for (const mold of moldsWithImageErrors || []) {
      console.log(`🔧 Fixing mold ${mold.id} (${mold.mold})`);
      
      // Mark image as unverified
      const { error: imageUpdateError } = await supabase
        .from('t_images')
        .update({ 
          image_verified: false,
          updated_at: new Date().toISOString()
        })
        .eq('table_name', 't_molds')
        .eq('record_id', mold.id);
      
      if (imageUpdateError) {
        console.log(`   ❌ Error marking image as unverified: ${imageUpdateError.message}`);
      } else {
        console.log(`   ✅ Marked image as unverified`);
      }
      
      // Update todo with clear action
      const todoMessage = `IMAGE MISSING: Upload image to ${baseImageUrl}/${mold.id}.jpg then retry collection publishing`;
      const { error: todoUpdateError } = await supabase
        .from('t_molds')
        .update({ todo: todoMessage })
        .eq('id', mold.id);
      
      if (todoUpdateError) {
        console.log(`   ❌ Error updating todo: ${todoUpdateError.message}`);
      } else {
        console.log(`   ✅ Updated todo with action item`);
      }
    }

    // 4. Find failed tasks with "Unknown error" that might be image-related
    console.log('\n4️⃣ Finding failed tasks with "Unknown error"...');
    const { data: unknownErrorTasks, error: unknownErrorTasksError } = await supabase
      .from('t_task_queue')
      .select('id, task_type, payload, result')
      .eq('status', 'error')
      .like('result', '%Unknown error%')
      .in('task_type', ['publish_mold_collection', 'publish_collection_mold']);

    if (unknownErrorTasksError) {
      console.error('❌ Error fetching unknown error tasks:', unknownErrorTasksError);
    } else {
      console.log(`Found ${unknownErrorTasks?.length || 0} tasks with "Unknown error"`);
      
      for (const task of unknownErrorTasks || []) {
        const payload = typeof task.payload === 'string' ? JSON.parse(task.payload) : task.payload;
        const moldId = payload.id;
        
        if (moldId) {
          console.log(`🔧 Checking task ${task.id} for mold ${moldId}`);
          
          // Get the actual error from the mold record
          const { data: moldData, error: moldDataError } = await supabase
            .from('t_molds')
            .select('shopify_collection_uploaded_notes')
            .eq('id', moldId)
            .single();
          
          if (!moldDataError && moldData && moldData.shopify_collection_uploaded_notes) {
            const actualError = moldData.shopify_collection_uploaded_notes;
            
            // Update task result with actual error
            const currentResult = typeof task.result === 'string' ? JSON.parse(task.result) : task.result;
            const updatedResult = {
              ...currentResult,
              error: actualError,
              error_source: 'extracted_from_database_by_audit',
              fixed_at: new Date().toISOString(),
              original_error: currentResult.error
            };
            
            const { error: updateTaskError } = await supabase
              .from('t_task_queue')
              .update({ result: updatedResult })
              .eq('id', task.id);
            
            if (updateTaskError) {
              console.log(`   ❌ Error updating task ${task.id}: ${updateTaskError.message}`);
            } else {
              console.log(`   ✅ Updated task ${task.id} with detailed error`);
            }
          }
        }
      }
    }

    console.log('\n🎉 Audit and fix completed!');
    console.log('\n📊 Summary:');
    console.log(`   - Checked ${verifiedImages?.length || 0} verified images`);
    console.log(`   - Found ${issuesFound} image accessibility issues`);
    console.log(`   - Fixed ${issuesFixed} image verification statuses`);
    console.log(`   - Fixed ${moldsWithImageErrors?.length || 0} molds with upload failures`);
    console.log(`   - Updated ${unknownErrorTasks?.length || 0} tasks with unknown errors`);
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

auditAndFixImageIssues();
