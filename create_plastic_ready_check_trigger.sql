-- Function to enqueue a task when t_plastics is updated
CREATE OR REPLACE FUNCTION fn_enqueue_check_if_plastic_is_ready()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert a task into the task queue
    INSERT INTO t_task_queue (
        task_type,
        payload,
        status,
        scheduled_at,
        created_at
    ) VALUES (
        'check_if_plastic_is_ready',
        jsonb_build_object('id', NEW.id),
        'pending',
        NOW(),
        NOW()
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the old trigger if it exists
DROP TRIGGER IF EXISTS tr_enqueue_check_if_plastic_is_ready ON t_plastics;

-- Create the new trigger
CREATE TRIGGER tr_enqueue_check_if_plastic_is_ready
AFTER INSERT OR UPDATE OF plastic, description, code, brand_id, shopify_collection_uploaded_at, ready_button
ON t_plastics
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_check_if_plastic_is_ready();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger tr_enqueue_check_if_plastic_is_ready has been created to enqueue a task.';
END $$;
