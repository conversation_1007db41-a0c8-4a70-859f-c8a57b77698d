import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function addLocationColumn() {
  try {
    console.log('Adding location column to imported_table_veeqo_sellables_export...');
    
    const { data, error } = await supabase.rpc('exec_sql', { 
      sql_query: `
        ALTER TABLE imported_table_veeqo_sellables_export 
        ADD COLUMN IF NOT EXISTS location TEXT;
      `
    });
    
    if (error) {
      console.error('Error adding location column:', error);
    } else {
      console.log('✅ Successfully added location column to the table');
      
      // Verify the column was added
      const { data: columns, error: columnsError } = await supabase.rpc('exec_sql', { 
        sql_query: `
          SELECT column_name, data_type, is_nullable 
          FROM information_schema.columns 
          WHERE table_name = 'imported_table_veeqo_sellables_export' 
          AND table_schema = 'public'
          AND column_name = 'location';
        `
      });
      
      if (columnsError) {
        console.error('Error verifying column:', columnsError);
      } else if (columns && columns.length > 0) {
        console.log('✅ Location column verified:');
        columns.forEach(col => {
          console.log(`  ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
        });
      } else {
        console.log('❌ Location column not found after adding');
      }
    }
  } catch (err) {
    console.error('Exception:', err);
  }
}

addLocationColumn();
