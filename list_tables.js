import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM3ODY1NDEsImV4cCI6MjA0OTM2MjU0MX0.MtBXMZt7rCqXd6c9clhrNoVtfOxEilX2C8oboMceOGg';
const supabase = createClient(supabaseUrl, supabaseKey);

const listTables = async () => {
  try {
    console.log('Checking database tables...');
    
    // Try to get some data from various tables
    const tables = [
      't_discs',
      't_order_sheet_lines',
      't_mps',
      't_molds',
      't_brands',
      't_task_queue'
    ];
    
    for (const table of tables) {
      console.log(`\nChecking table: ${table}`);
      
      // Check if table exists by trying to get a count
      const { count, error: countError } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true });
        
      if (countError) {
        console.error(`Error accessing table ${table}:`, countError);
        continue;
      }
      
      console.log(`Table ${table} exists and has ${count} records.`);
      
      // Get a sample of records
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(3);
        
      if (error) {
        console.error(`Error getting sample from ${table}:`, error);
        continue;
      }
      
      console.log(`Sample records from ${table}:`, data);
    }
    
    // Try to get the schema information
    console.log('\nAttempting to get schema information...');
    try {
      const { data, error } = await supabase
        .rpc('get_schema_info');
        
      if (error) {
        console.error('Error getting schema info:', error);
      } else {
        console.log('Schema info:', data);
      }
    } catch (e) {
      console.log('RPC method get_schema_info not available:', e.message);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
};

listTables();
