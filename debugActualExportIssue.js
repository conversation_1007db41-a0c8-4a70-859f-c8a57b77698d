import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function debugActualExportIssue() {
    try {
        console.log('🔍 Debugging actual export issue...\n');
        
        // Check what records are actually being exported for rows 22, 25, 28
        console.log('1. Checking what gets exported for rows 22, 25, 28...');
        const { data: exportData, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, excel_mapping_key, is_orderable, calculated_mps_id, mold_name, plastic_name')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .in('excel_row_hint', [22, 25, 28])
            .order('excel_row_hint, excel_column');

        if (error) {
            console.error('❌ Error querying export data:', error);
            return;
        }

        console.log(`✅ Found ${exportData.length} records that would be exported:`);
        exportData.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.mold_name} | ${record.plastic_name}`);
            console.log(`      Orderable: ${record.is_orderable}, MPS: ${record.calculated_mps_id || 'NULL'}`);
            console.log(`      Mapping: ${record.excel_mapping_key}`);
            console.log('');
        });

        // Check ALL records in these rows (including non-orderable)
        console.log('2. Checking ALL records in rows 22, 25, 28...');
        const { data: allRecords, error: allError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, excel_mapping_key, is_orderable, calculated_mps_id, mold_name, plastic_name')
            .in('excel_row_hint', [22, 25, 28])
            .order('excel_row_hint, excel_column');

        if (allError) {
            console.error('❌ Error querying all records:', allError);
            return;
        }

        console.log(`✅ Found ${allRecords.length} total records:`);
        allRecords.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.mold_name} | ${record.plastic_name}`);
            console.log(`      Orderable: ${record.is_orderable}, MPS: ${record.calculated_mps_id || 'NULL'}`);
            console.log(`      Mapping: ${record.excel_mapping_key || 'NULL'}`);
            console.log('');
        });

        // Check if there are records in column B that shouldn't be there
        console.log('3. Checking for records in column B...');
        const { data: columnBRecords, error: columnBError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, excel_mapping_key, is_orderable, calculated_mps_id, mold_name, plastic_name')
            .eq('excel_column', 'B')
            .in('excel_row_hint', [22, 25, 28]);

        if (columnBError) {
            console.error('❌ Error querying column B records:', columnBError);
            return;
        }

        console.log(`✅ Found ${columnBRecords.length} records in column B:`);
        columnBRecords.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.mold_name} | ${record.plastic_name}`);
            console.log(`      Orderable: ${record.is_orderable}, MPS: ${record.calculated_mps_id || 'NULL'}`);
            console.log(`      Mapping: ${record.excel_mapping_key || 'NULL'}`);
        });

        console.log('\n🎯 Analysis:');
        console.log('   • If row 22 has orderable records, that\'s why it shows 0/NO_MPS');
        console.log('   • If rows 25/28 have records in column B instead of A, that\'s the mapping issue');
        console.log('   • The export uses whatever column is in the database records');
        
    } catch (error) {
        console.error('❌ Debug failed:', error.message);
    }
}

debugActualExportIssue().catch(console.error);
