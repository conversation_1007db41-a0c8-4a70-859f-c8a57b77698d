-- Enqueue update_veeqo_dgacc_qty when a new movement is inserted (excluding Migration)
CREATE OR REPLACE FUNCTION public.fn_enqueue_update_veeqo_dgacc_qty_on_movement()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  -- Only enqueue when not created by Migration (NULL counts as not equal)
  IF NEW.created_by IS DISTINCT FROM 'Migration' THEN
    INSERT INTO public.t_task_queue (
      task_type,
      payload,
      status,
      scheduled_at,
      created_at,
      enqueued_by
    ) VALUES (
      'update_veeqo_dgacc_qty',
      jsonb_build_object(
        'sku_code', 'DGACC' || NEW.product_variant_id,
        'accessory_id', NEW.product_variant_id,
        'movement_id', NEW.id
      ),
      'pending',
      NOW() + INTERVAL '30 seconds',
      NOW(),
      't_inventory_movements insert_trigger_' || NEW.id
    );
  END IF;

  RETURN NEW;
END;
$$;

-- Drop and recreate the trigger on t_inventory_movements
DROP TRIGGER IF EXISTS trg_enqueue_update_veeqo_dgacc_qty_on_movement ON public.t_inventory_movements;
CREATE TRIGGER trg_enqueue_update_veeqo_dgacc_qty_on_movement
AFTER INSERT ON public.t_inventory_movements
FOR EACH ROW
WHEN (NEW.created_by IS DISTINCT FROM 'Migration')
EXECUTE FUNCTION public.fn_enqueue_update_veeqo_dgacc_qty_on_movement();

-- Optional notice
DO $$ BEGIN RAISE NOTICE 'Trigger trg_enqueue_update_veeqo_dgacc_qty_on_movement created/updated.'; END $$;

