require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkUpdatedTaskStatus() {
  try {
    console.log('Checking updated task status...');
    
    // Check the task we created
    const { data: tasks, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('enqueued_by', 'test_updated_functionality')
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (taskError) {
      console.error('Error fetching task:', taskError);
      return;
    }
    
    if (!tasks || tasks.length === 0) {
      console.log('No test tasks found');
      return;
    }
    
    const task = tasks[0];
    console.log('Task ID:', task.id);
    console.log('Task status:', task.status);
    console.log('Task result:', task.result);
    console.log('Processed at:', task.processed_at);
    console.log('Locked by:', task.locked_by);
    
    // Check if the disc was updated
    const discId = task.payload.id;
    const { data: disc, error: discError } = await supabase
      .from('t_discs')
      .select('id, order_sheet_line_id, vendor_osl_id, weight, weight_mfg')
      .eq('id', discId)
      .single();
    
    if (discError) {
      console.error('Error fetching disc:', discError);
      return;
    }
    
    console.log('\nUpdated disc:', disc);
    
    if (disc.vendor_osl_id) {
      console.log('✅ SUCCESS: vendor_osl_id has been set to', disc.vendor_osl_id);
      
      // Check if it's different from order_sheet_line_id
      if (disc.vendor_osl_id !== disc.order_sheet_line_id) {
        console.log('🎯 EXCELLENT: vendor_osl_id differs from order_sheet_line_id - dual mapping working!');
      } else {
        console.log('ℹ️ vendor_osl_id same as order_sheet_line_id - both weights map to same OSL');
      }
    } else {
      console.log('⏳ vendor_osl_id is still null');
      
      if (task.status === 'pending') {
        console.log('Task is still pending - worker may not be running or may be busy');
      } else if (task.status === 'completed') {
        console.log('Task completed but vendor_osl_id not set - may have been processed by old worker version');
      } else {
        console.log('Task status:', task.status);
      }
    }
    
  } catch (err) {
    console.error('Error:', err.message);
  }
}

checkUpdatedTaskStatus();
