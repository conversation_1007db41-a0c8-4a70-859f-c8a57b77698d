-- Update the trigger function to enqueue disc ready checks for future OSL publish tasks

CREATE OR REPLACE FUNCTION public.trg_queue_publish_product_osl()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  release_date timestamp;
  scheduled_time timestamp;
  future_threshold timestamp;
BEGIN
  -- Get the release_date_online from the related t_mps record
  SELECT release_date_online INTO release_date
  FROM t_mps
  WHERE id = NEW.mps_id;

  -- Calculate the scheduled time
  IF release_date IS NOT NULL THEN
    -- Schedule for 2 minutes before the release date
    scheduled_time := release_date - INTERVAL '2 minutes';
    
    -- If the calculated time is in the past, use NOW() instead
    IF scheduled_time < NOW() THEN
      scheduled_time := NOW();
    END IF;
  ELSE
    -- If no release date, schedule for now
    scheduled_time := NOW();
  END IF;

  -- Insert the main publish_product_osl task
  INSERT INTO t_task_queue (task_type, payload, status, scheduled_at, created_at)
  VALUES (
    'publish_product_osl',
    jsonb_build_object('id', NEW.id),
    'pending',
    scheduled_time,
    NOW()
  );

  -- Check if the scheduled time is more than 30 minutes in the future
  future_threshold := NOW() + INTERVAL '30 minutes';
  
  IF scheduled_time > future_threshold THEN
    -- Enqueue a task to check discs for this future OSL publish
    INSERT INTO t_task_queue (task_type, payload, status, scheduled_at, created_at)
    VALUES (
      'check_discs_for_future_osl_publish',
      jsonb_build_object('osl_id', NEW.id, 'original_scheduled_at', scheduled_time),
      'pending',
      NOW() + INTERVAL '1 minute',
      NOW()
    );
  END IF;

  RETURN NEW;
END;
$function$;
