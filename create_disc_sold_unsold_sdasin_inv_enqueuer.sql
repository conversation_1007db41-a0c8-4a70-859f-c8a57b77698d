-- Function to enqueue a task for updating SDASIN inventory when a disc is sold or unsold
CREATE OR REPLACE FUNCTION fn_enqueue_d_sold_or_unsold_update_sdasin_inv()
RETURNS TRIGGER AS $$
BEGIN
    -- Only enqueue a task if the sold_date has changed from null to not null or vice versa
    IF (OLD.sold_date IS NULL AND NEW.sold_date IS NOT NULL) OR 
       (OLD.sold_date IS NOT NULL AND NEW.sold_date IS NULL) THEN
        
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'd_sold_or_unsold_update_sdasin_inv',
            jsonb_build_object(
                'id', NEW.id,
                'old_sold_date', OLD.sold_date,
                'new_sold_date', NEW.sold_date
            ),
            'pending',
            NOW(),
            NOW()
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the new trigger
DROP TRIGGER IF EXISTS trg_enqueue_d_sold_or_unsold_update_sdasin_inv ON t_discs;

CREATE TRIGGER trg_enqueue_d_sold_or_unsold_update_sdasin_inv
AFTER UPDATE OF sold_date ON t_discs
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_d_sold_or_unsold_update_sdasin_inv();

-- Drop the old trigger
DROP TRIGGER IF EXISTS trig_manage_inv_sdasin_on_disc_sold_unsold ON t_discs;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Disc sold/unsold SDASIN inventory update enqueuer function and trigger created.';
END $$;
