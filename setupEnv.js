// setupEnv.js - <PERSON>ript to set up environment variables for Supabase

import fs from 'fs';
import readline from 'readline';
import { createClient } from '@supabase/supabase-js';

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Check if .env file exists
const envExists = fs.existsSync('.env');

console.log(`${envExists ? '.env file exists' : '.env file does not exist'}`);

// Read current .env file if it exists
let currentEnv = {};
if (envExists) {
  const envContent = fs.readFileSync('.env', 'utf8');
  const envLines = envContent.split('\n');
  
  for (const line of envLines) {
    if (line.trim() && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      const value = valueParts.join('=');
      if (key && value) {
        currentEnv[key.trim()] = value.trim();
      }
    }
  }
  
  console.log('Current environment variables:');
  for (const key in currentEnv) {
    if (key.includes('KEY') || key.includes('SECRET')) {
      console.log(`${key}=********`);
    } else {
      console.log(`${key}=${currentEnv[key]}`);
    }
  }
}

// Ask for Supabase URL and key
function askForSupabaseUrl() {
  return new Promise((resolve) => {
    const defaultUrl = currentEnv.SUPABASE_URL || '';
    rl.question(`Enter Supabase URL ${defaultUrl ? `(current: ${defaultUrl})` : ''}: `, (answer) => {
      resolve(answer || defaultUrl);
    });
  });
}

function askForSupabaseKey() {
  return new Promise((resolve) => {
    const defaultKey = currentEnv.SUPABASE_KEY || '';
    rl.question(`Enter Supabase service role key ${defaultKey ? '(current: ********)' : ''}: `, (answer) => {
      resolve(answer || defaultKey);
    });
  });
}

// Test Supabase connection
async function testSupabaseConnection(url, key) {
  if (!url || !key) {
    console.log('Supabase URL or key is missing. Cannot test connection.');
    return false;
  }
  
  try {
    console.log('Testing connection to Supabase...');
    const supabase = createClient(url, key);
    
    // Try to list tables
    const { data, error } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .limit(1);
    
    if (error) {
      console.error(`Error connecting to Supabase: ${error.message}`);
      return false;
    }
    
    console.log('Successfully connected to Supabase!');
    return true;
  } catch (error) {
    console.error(`Error testing Supabase connection: ${error.message}`);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Get Supabase URL and key
    const supabaseUrl = await askForSupabaseUrl();
    const supabaseKey = await askForSupabaseKey();
    
    // Test connection
    const connectionSuccessful = await testSupabaseConnection(supabaseUrl, supabaseKey);
    
    if (!connectionSuccessful) {
      console.log('Connection test failed. Please check your Supabase URL and key.');
      const retry = await new Promise((resolve) => {
        rl.question('Do you want to retry with different credentials? (y/n): ', (answer) => {
          resolve(answer.toLowerCase() === 'y');
        });
      });
      
      if (retry) {
        rl.close();
        return main();
      }
    }
    
    // Update .env file
    const envContent = Object.entries({
      ...currentEnv,
      SUPABASE_URL: supabaseUrl,
      SUPABASE_KEY: supabaseKey
    }).map(([key, value]) => `${key}=${value}`).join('\n');
    
    fs.writeFileSync('.env', envContent);
    console.log('.env file updated successfully.');
    
    // Ask for DBF file path
    const defaultDbfPath = currentEnv.DBF_FILE_PATH || 'R:\\Rpro\\BRIDGE\\invdb.dbf';
    const dbfFilePath = await new Promise((resolve) => {
      rl.question(`Enter DBF file path (current: ${defaultDbfPath}): `, (answer) => {
        resolve(answer || defaultDbfPath);
      });
    });
    
    // Ask for target table name
    const defaultTableName = currentEnv.TARGET_TABLE || 'imported_table_rpro';
    const targetTable = await new Promise((resolve) => {
      rl.question(`Enter target table name (current: ${defaultTableName}): `, (answer) => {
        resolve(answer || defaultTableName);
      });
    });
    
    // Update .env file with DBF path and target table
    const updatedEnvContent = Object.entries({
      ...currentEnv,
      SUPABASE_URL: supabaseUrl,
      SUPABASE_KEY: supabaseKey,
      DBF_FILE_PATH: dbfFilePath,
      TARGET_TABLE: targetTable,
      TRUNCATE_BEFORE_IMPORT: 'true'
    }).map(([key, value]) => `${key}=${value}`).join('\n');
    
    fs.writeFileSync('.env', updatedEnvContent);
    console.log('.env file updated with DBF path and target table.');
    
    console.log('\nSetup completed successfully!');
    console.log('\nYou can now run the import with:');
    console.log(`node importDbfToSupabase.js "${dbfFilePath}" "${targetTable}" true`);
  } catch (error) {
    console.error(`Error: ${error.message}`);
  } finally {
    rl.close();
  }
}

// Run the main function
main();
