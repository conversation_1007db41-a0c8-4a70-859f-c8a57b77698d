# Veeqo Title Update System

This system automatically updates Veeqo product titles when a disc's `g_pull` field changes in the `t_discs` table.

## Overview

The system consists of:
1. **Database trigger** - Fires when `g_pull` changes on eligible discs
2. **Task queue processor** - Handles the `update_veeqo_d_title` tasks
3. **One-time sync script** - Enqueues tasks for existing discs

## Components

### 1. Database Trigger (`create_veeqo_title_update_trigger.sql`)

**Function:** `fn_enqueue_update_veeqo_d_title_task()`
**Trigger:** `trg_enqueue_update_veeqo_d_title`

**Conditions for enqueueing a task:**
- `g_pull` has actually changed (not just updated to same value)
- `shopify_uploaded_at IS NOT NULL` (disc is on Shopify/Veeqo)
- `sold_date IS NULL` (disc is unsold)

**Task payload:**
```json
{
  "id": 123456,
  "g_pull": "F Brand Mold Plastic Color Weight #123456",
  "old_g_pull": "Previous title"
}
```

### 2. Task Processor (`processUpdateVeeqoDTitleTask.js`)

**Task type:** `update_veeqo_d_title`

**Process:**
1. Gets disc ID from payload
2. Constructs SKU as `D{disc_id}` (e.g., "D424515")
3. Looks up Veeqo product ID(s) using `getVeeqoId()`
4. Updates Veeqo product title via API: `PUT /products/{id}` with `{ "product": { "title": "new_title" } }`
5. Handles multiple Veeqo products per SKU if they exist

**API Endpoint:** `https://api.veeqo.com/products/{product_id}`
**Method:** PUT
**Payload:** `{ "product": { "title": "new_title_from_g_pull" } }`

### 3. One-time Sync Script (`enqueueVeeqoTitleUpdateTasks.js`)

Enqueues `update_veeqo_d_title` tasks for existing discs that meet the criteria.

**Usage:**
```bash
# Dry run to see what would be processed
node enqueueVeeqoTitleUpdateTasks.js --dry-run

# Process all eligible discs
node enqueueVeeqoTitleUpdateTasks.js

# Process with custom settings
node enqueueVeeqoTitleUpdateTasks.js --batch-size=50 --delay=2 --limit=1000

# Process starting from a specific disc ID
node enqueueVeeqoTitleUpdateTasks.js --start-id=400000 --limit=5000
```

**Options:**
- `--dry-run` - Show what would be processed without enqueueing tasks
- `--batch-size=N` - Number of tasks per batch (default: 100)
- `--delay=N` - Seconds between batches (default: 1)
- `--start-id=N` - Start from disc ID N (default: 0)
- `--limit=N` - Limit total discs processed
- `--help` - Show usage information

## Installation

1. **Create the trigger:**
   ```bash
   node createVeeqoTitleTrigger.js
   ```

2. **The task processor is automatically included** in `taskQueueWorker.js` (already added)

3. **Run initial sync for existing discs:**
   ```bash
   # Test with a small batch first
   node enqueueVeeqoTitleUpdateTasks.js --dry-run --limit=10
   
   # Run actual sync
   node enqueueVeeqoTitleUpdateTasks.js --batch-size=100 --delay=1
   ```

## Testing

### Check for tasks:
```bash
node checkVeeqoTitleTasks.js
```

### Test the trigger manually:
```sql
-- Update a disc's g_pull to trigger the system
UPDATE t_discs 
SET g_pull = 'TEST - ' || g_pull 
WHERE id = 424515 
  AND shopify_uploaded_at IS NOT NULL 
  AND sold_date IS NULL;
```

### Test the Veeqo API directly:
```bash
node testVeeqoTitleUpdate.js
```

## Monitoring

**Task queue table:** `t_task_queue`
**Task type:** `update_veeqo_d_title`

**Check task status:**
```sql
SELECT id, payload, status, scheduled_at, created_at, enqueued_by, result
FROM t_task_queue 
WHERE task_type = 'update_veeqo_d_title'
ORDER BY created_at DESC
LIMIT 10;
```

**Task statuses:**
- `pending` - Waiting to be processed
- `processing` - Currently being processed
- `completed` - Successfully completed
- `error` - Failed with error

## Error Handling

The system handles several scenarios:
- **No Veeqo product found:** Task completes with message (disc may not be uploaded yet)
- **Multiple Veeqo products:** Updates all products with the SKU
- **Partial failures:** Reports success/failure counts
- **API errors:** Task fails with error details

## Performance

- Tasks are scheduled 5 minutes in the future to avoid overwhelming the system
- Batch processing with configurable delays
- Handles multiple Veeqo products per disc SKU
- Respects Veeqo API rate limits

## Dependencies

- `node-fetch` - For Veeqo API calls
- `getVeeqoId.js` - To find Veeqo product IDs by SKU
- Task queue worker system
- Supabase client

## Notes

- Only processes discs that are uploaded to Shopify (`shopify_uploaded_at IS NOT NULL`)
- Only processes unsold discs (`sold_date IS NULL`)
- Uses the disc's `g_pull` field as the new Veeqo product title
- Maintains audit trail in task queue with old/new titles
