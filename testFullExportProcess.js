import fetch from 'node-fetch';

async function testFullExportProcess() {
  try {
    console.log('🧪 Testing full export process with daily automation data...\n');
    
    // First get the order summary data (simulating what daily automation does)
    console.log('1. Getting order summary data...');
    const { getOrderSummary } = await import('./discraftDailyAutomation.js');
    const orderSummary = await getOrderSummary();
    
    if (!orderSummary.success) {
      throw new Error(`Order summary failed: ${orderSummary.error}`);
    }
    
    console.log(`✅ Order summary completed: ${orderSummary.detailedData.length} records`);
    
    // Now call the export API with this data (simulating what daily automation does)
    console.log('\n2. Calling export API with order summary data...');
    const response = await fetch('http://localhost:3001/api/discraft/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        includeHeader: false,
        filename: `test_full_export_process_${new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0]}.xlsx`,
        orderData: orderSummary.detailedData // Pass the same data used by daily automation
      })
    });

    if (!response.ok) {
      throw new Error(`Export API returned ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Export completed successfully!');
    console.log(`📄 Filename: ${result.filename}`);
    console.log(`📁 File path: ${result.filePath}`);
    console.log(`📊 Total records processed: ${result.totalRecords}`);
    console.log(`📋 Header included: ${result.headerIncluded}`);
    
    console.log('\n🎉 Full export process test completed! This simulates exactly what the daily automation does.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testFullExportProcess().catch(console.error);
