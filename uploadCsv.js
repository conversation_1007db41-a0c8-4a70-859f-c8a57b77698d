import { createClient } from '@supabase/supabase-js';

// 🔹 Step 1: Initialize Supabase Client
const supabase = createClient('https://aepabhlwpjfjulrjeitn.supabase.co', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzc4NjU0MSwiZXhwIjoyMDQ5MzYyNTQxfQ.FQyPTgdBP83VhuykdlZkagHADc5nBmx7-yd0JYt5aP8');

// 🔹 Step 2: Delete the Existing File (if it exists)
async function deleteExistingFile() {
    const { error } = await supabase
        .storage
        .from('exports')
        .remove(['tu_informed_export.csv']);  // File to delete

    if (error) {
        console.warn('Warning: File did not exist or could not be deleted.', error);
    } else {
        console.log('✅ Previous CSV file deleted successfully.');
    }
}

// 🔹 Step 3: Fetch CSV Data from Postgres
async function fetchCsvData() {
    const { data, error } = await supabase.rpc('f_export_tu_informed_to_csv');
    
    if (error) {
        console.error('❌ Error fetching CSV from database:', error);
        return null;
    }

    return data;
}

// 🔹 Step 4: Upload CSV to Supabase Storage
async function uploadCsv() {
    await deleteExistingFile(); // Ensure the old file is deleted before upload

    const csvContent = await fetchCsvData();
    if (!csvContent) {
        console.error('❌ No CSV content to upload.');
        return;
    }

    // Convert CSV content to a Blob
    const file = new Blob([csvContent], { type: 'text/csv' });

    // Upload file to Supabase Storage
    const { data, error } = await supabase
        .storage
        .from('exports')
        .upload('tu_informed_export.csv', file, { contentType: 'text/csv' });

    if (error) {
        console.error('❌ Error uploading file to Supabase Storage:', error);
    } else {
        console.log('✅ File uploaded successfully:', data);
	console.log("📥 Download link: https://aepabhlwpjfjulrjeitn.supabase.co/storage/v1/object/public/exports/tu_informed_export.csv");

    }
}

// 🔹 Step 5: Run the Upload Function
uploadCsv();