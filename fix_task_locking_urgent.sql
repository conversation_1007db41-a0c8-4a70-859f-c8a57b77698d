-- URGENT FIX: Prevent disc_checks_completed tasks from being processed too early
-- Only process disc_checks_completed tasks at their exact scheduled time

CREATE OR REPLACE FUNCTION lock_pending_tasks(
    worker_id TEXT,
    max_tasks INTEGER,
    task_time TIMESTAMP WITH TIME ZONE
)
RETURNS SETOF t_task_queue AS $$
DECLARE
    lock_timeout INTERVAL := INTERVAL '5 minutes';
BEGIN
    RETURN QUERY
    WITH locked_tasks AS (
        UPDATE t_task_queue
        SET
            status = 'processing',
            locked_at = task_time,
            locked_by = worker_id
        WHERE id IN (
            SELECT id
            FROM t_task_queue
            WHERE
                (
                    -- Normal pending tasks: scheduled for now or in the past
                    (status = 'pending' AND scheduled_at <= task_time)
                    OR
                    -- Future publish_product_osl tasks that need disc checks (haven't been processed yet)
                    (status = 'pending' AND task_type = 'publish_product_osl' AND scheduled_at > task_time)
                    -- NOTE: disc_checks_completed tasks are NOT included here
                    -- They should only be processed when we specifically want to publish them
                )
                AND (locked_at IS NULL OR locked_at < task_time - lock_timeout)
            ORDER BY 
                -- Priority: 1) Ready tasks, 2) Future OSL tasks needing disc checks
                CASE 
                    WHEN status = 'pending' AND scheduled_at <= task_time THEN 0  -- Ready tasks first
                    WHEN status = 'pending' AND task_type = 'publish_product_osl' AND scheduled_at > task_time THEN 1  -- Future OSL tasks needing disc checks
                    ELSE 2
                END,
                scheduled_at ASC
            LIMIT max_tasks
            FOR UPDATE SKIP LOCKED
        )
        RETURNING *
    )
    SELECT * FROM locked_tasks;
END;
$$ LANGUAGE plpgsql;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'URGENT FIX: Removed disc_checks_completed from automatic processing to prevent early publishing.';
END $$;
