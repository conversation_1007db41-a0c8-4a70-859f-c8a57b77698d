require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testFixedMatchOslToDiscs() {
  try {
    console.log('Testing fixed match_osl_to_discs task (no more vendorMatchedDiscIds error)...');
    
    // Test with OSL 18977 from our previous examples
    const testOslId = 18977;
    
    console.log(`\n=== CREATING TEST TASK ===`);
    console.log(`Creating match_osl_to_discs task for OSL ${testOslId}...`);
    
    const { data: taskData, error: taskError } = await supabase
      .from('t_task_queue')
      .insert([
        {
          task_type: 'match_osl_to_discs',
          payload: { id: testOslId },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: 'test_fixed_match_osl_to_discs'
        }
      ])
      .select();
    
    if (taskError) {
      console.error('Error creating test task:', taskError);
      return;
    }
    
    const testTaskId = taskData[0].id;
    console.log(`✅ Created test task ${testTaskId} for OSL ${testOslId}`);
    
    // Get OSL details
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, min_weight, max_weight, color_id')
      .eq('id', testOslId)
      .single();
    
    if (!oslError) {
      console.log(`\nOSL ${testOslId}: MPS ${osl.mps_id}, Weight ${osl.min_weight}-${osl.max_weight}g, Color ${osl.color_id}`);
      
      // Check if this OSL's MPS has a redirect
      const { data: mps, error: mpsError } = await supabase
        .from('t_mps')
        .select('id, order_through_mps_id')
        .eq('id', osl.mps_id)
        .single();
      
      if (!mpsError) {
        if (mps.order_through_mps_id) {
          console.log(`⚠️  MPS ${mps.id} has redirect to MPS ${mps.order_through_mps_id}`);
          console.log('   → Vendor mapping should be SKIPPED');
          console.log('   → Regular mapping should still work');
        } else {
          console.log(`✅ MPS ${mps.id} has no redirect`);
          console.log('   → Both regular and vendor mapping should work');
        }
      }
    }
    
    console.log('\n🔄 Task created and ready for processing by the worker daemon.');
    console.log('The fixed task should now:');
    console.log('1. NOT throw "vendorMatchedDiscIds is not defined" error');
    console.log('2. Handle redirect logic properly');
    console.log('3. Complete successfully with proper variable scoping');
    
    console.log('\n📋 To monitor results:');
    console.log(`
-- Check task processing (should NOT have vendorMatchedDiscIds error)
SELECT 
  id, status, 
  result->>'message' as message,
  result->>'error' as error,
  result->>'discs_matched_regular' as regular_matches,
  result->>'discs_matched_vendor' as vendor_matches,
  result->>'redirect_info' as redirect_info,
  processed_at
FROM t_task_queue 
WHERE id = ${testTaskId};
    `);
    
    console.log('\n🎯 SUCCESS INDICATORS:');
    console.log('✅ Task should complete with status = "completed"');
    console.log('✅ No "vendorMatchedDiscIds is not defined" error');
    console.log('✅ Proper redirect handling in results');
    console.log('✅ Both regular and vendor match counts reported');
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

testFixedMatchOslToDiscs();
