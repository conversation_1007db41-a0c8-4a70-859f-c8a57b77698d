require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkAllDiscsComplete() {
  try {
    console.log('Checking ALL discs in the database for vendor_osl_id status...');
    
    // Get total count of ALL discs
    const { count: totalDiscs, error: totalError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true });
    
    if (totalError) {
      console.error('Error getting total count:', totalError);
      return;
    }
    
    console.log(`Total discs in database: ${totalDiscs}`);
    
    // Get count of discs with weight_mfg data
    const { count: discsWithWeightMfg, error: weightMfgError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .not('weight_mfg', 'is', null);
    
    // Get count of discs with all required fields for vendor mapping
    const { count: discsEligibleForVendorMapping, error: eligibleError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    // Get count of discs that already have vendor_osl_id
    const { count: discsWithVendorOsl, error: vendorOslError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .not('vendor_osl_id', 'is', null);
    
    // Get count of discs that need vendor_osl_id (eligible but don't have it)
    const { count: discsNeedingVendorOsl, error: needingError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    console.log('\n=== COMPLETE DATABASE ANALYSIS ===');
    console.log(`Total discs: ${totalDiscs}`);
    console.log(`Discs with weight_mfg: ${discsWithWeightMfg}`);
    console.log(`Discs eligible for vendor mapping: ${discsEligibleForVendorMapping}`);
    console.log(`Discs with vendor_osl_id already set: ${discsWithVendorOsl}`);
    console.log(`Discs needing vendor_osl_id: ${discsNeedingVendorOsl}`);
    
    const percentageWithVendorOsl = ((discsWithVendorOsl / discsEligibleForVendorMapping) * 100).toFixed(1);
    console.log(`Percentage of eligible discs with vendor_osl_id: ${percentageWithVendorOsl}%`);
    
    if (discsNeedingVendorOsl > 0) {
      console.log(`\n🔍 Found ${discsNeedingVendorOsl} discs that need vendor_osl_id updates!`);
      console.log('This is much more than the 19 we saw earlier - let me investigate...');
      
      // Let's run the comprehensive update on ALL discs that need it
      console.log('\n=== RUNNING COMPREHENSIVE VENDOR OSL UPDATE ===');
      console.log('Processing all discs that need vendor_osl_id...');
      
      let totalProcessed = 0;
      let totalUpdated = 0;
      let totalErrors = 0;
      const batchSize = 100;
      
      while (totalProcessed < discsNeedingVendorOsl) {
        console.log(`\nProcessing batch ${Math.floor(totalProcessed / batchSize) + 1}...`);
        
        // Get next batch of discs that need vendor_osl_id
        const { data: discs, error: batchError } = await supabase
          .from('t_discs')
          .select('id, mps_id, weight_mfg, color_id, vendor_osl_id')
          .is('vendor_osl_id', null)
          .not('weight_mfg', 'is', null)
          .not('mps_id', 'is', null)
          .not('color_id', 'is', null)
          .range(0, batchSize - 1);  // Always get the first batch since we're updating them
        
        if (batchError) {
          console.error('Error getting batch:', batchError);
          break;
        }
        
        if (!discs || discs.length === 0) {
          console.log('No more discs to process');
          break;
        }
        
        console.log(`Processing ${discs.length} discs in this batch...`);
        
        for (const disc of discs) {
          try {
            // Find matching vendor OSL
            const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
              'find_matching_osl_by_mfg_weight',
              {
                mps_id_param: disc.mps_id,
                color_id_param: disc.color_id,
                weight_mfg_param: disc.weight_mfg
              }
            );
            
            if (vendorOslError) {
              console.error(`Error finding vendor OSL for disc ${disc.id}:`, vendorOslError);
              totalErrors++;
              continue;
            }
            
            const vendorOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
            
            if (vendorOslId) {
              // Update the disc with vendor_osl_id
              const { error: updateError } = await supabase
                .from('t_discs')
                .update({ vendor_osl_id: vendorOslId })
                .eq('id', disc.id);
              
              if (updateError) {
                console.error(`Error updating disc ${disc.id}:`, updateError);
                totalErrors++;
              } else {
                totalUpdated++;
                if (totalUpdated % 50 === 0) {
                  console.log(`  ✅ Updated ${totalUpdated} discs so far...`);
                }
              }
            }
            
            totalProcessed++;
            
          } catch (err) {
            console.error(`Error processing disc ${disc.id}:`, err.message);
            totalErrors++;
            totalProcessed++;
          }
        }
        
        console.log(`Batch completed. Processed: ${totalProcessed}, Updated: ${totalUpdated}, Errors: ${totalErrors}`);
        
        // Small delay between batches
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      console.log('\n=== FINAL RESULTS ===');
      console.log(`Total discs processed: ${totalProcessed}`);
      console.log(`Successfully updated: ${totalUpdated}`);
      console.log(`Errors encountered: ${totalErrors}`);
      console.log(`Discs with no matching vendor OSL: ${totalProcessed - totalUpdated - totalErrors}`);
      
      if (totalUpdated > 0) {
        console.log(`\n✅ SUCCESS! Updated ${totalUpdated} discs with vendor_osl_id mappings!`);
      }
      
    } else {
      console.log('\n✅ All eligible discs already have vendor_osl_id set!');
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

checkAllDiscsComplete();
