import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkPierceFierceProducts() {
  try {
    console.log('🔍 Checking Pierce Fierce products...\n');
    
    // Get all Pierce products with Fierce in the model
    const { data: allPierceFierce, error1 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .eq('raw_line_type', 'Pierce')
      .ilike('raw_model', '%Fierce%')
      .limit(10);
    
    if (error1) {
      console.error('Error querying Pierce Fierce products:', error1);
      return;
    }
    
    console.log(`📋 Found ${allPierceFierce.length} Pierce Fierce products:\n`);
    
    allPierceFierce.forEach((product, index) => {
      console.log(`${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
      console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
      console.log(`   Vendor Description: "${product.vendor_description || 'NULL/EMPTY'}"`);
      
      // Check if this should be Swirl (non-Hard Fierce)
      if (!product.raw_model.includes('Hard')) {
        console.log(`   🎯 SHOULD BE: Swirl | Fierce | PP Logo Stock Stamp`);
      }
      console.log('');
    });
    
    // Also check for exact "Fierce" model (not "Hard Fierce" or "Soft Fierce")
    const { data: exactFierce, error2 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .eq('raw_line_type', 'Pierce')
      .eq('raw_model', 'Fierce')
      .limit(5);
    
    if (error2) {
      console.error('Error querying exact Pierce Fierce products:', error2);
    } else {
      console.log(`\n📋 Found ${exactFierce.length} Pierce products with exact "Fierce" model:\n`);
      
      exactFierce.forEach((product, index) => {
        console.log(`${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
        console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        console.log(`   Expected: Swirl | Fierce | PP Logo Stock Stamp`);
        console.log(`   Vendor Description: "${product.vendor_description || 'NULL/EMPTY'}"`);
        console.log('');
      });
    }
    
    // Check for McBeth NEW products with vendor descriptions
    console.log('🔍 Checking McBeth NEW products with vendor descriptions...\n');
    
    const { data: mcbethNewWithDesc, error3 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .eq('raw_line_type', 'McBeth')
      .ilike('raw_model', '%NEW -%')
      .not('vendor_description', 'in', '("N/A", "", null)')
      .limit(5);
    
    if (error3) {
      console.error('Error querying McBeth NEW with descriptions:', error3);
    } else {
      console.log(`📋 Found ${mcbethNewWithDesc.length} McBeth NEW products with vendor descriptions:\n`);
      
      mcbethNewWithDesc.forEach((product, index) => {
        console.log(`${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
        console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        console.log(`   Vendor Description: "${product.vendor_description}"`);
        
        if (product.vendor_description && product.vendor_description.toLowerCase().includes('white/blank')) {
          console.log(`   🎯 SHOULD BE: ESP | ${product.mold_name} | Dye Line Blank Top Bottom`);
        }
        console.log('');
      });
    }
    
    console.log('🎉 Check completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkPierceFierceProducts().catch(console.error);
