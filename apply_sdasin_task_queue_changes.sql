-- Step 1: Create the function to match a SDASIN to all discs in a single operation
CREATE OR REPLACE FUNCTION match_sdasin_to_all_discs(
    sdasin_id_param INTEGER
)
RETURNS INTEGER AS $$
DECLARE
    sdasin_rec RECORD;
    match_count INTEGER := 0;
BEGIN
    -- Get the SDASIN record
    SELECT id, mps_id, mps_id2, min_weight, max_weight, color_id
    INTO sdasin_rec
    FROM t_sdasins
    WHERE id = sdasin_id_param;
    
    -- Delete old join records for this SDASIN
    DELETE FROM tjoin_discs_sdasins
    WHERE sdasin_id = sdasin_id_param;
    
    -- Insert new join records for matching discs
    WITH matching_discs AS (
        SELECT 
            d.id AS disc_id,
            'Matched on MPS, weight range, and color' AS reason
        FROM 
            t_discs d
        WHERE 
            (d.sold_date IS NULL OR d.sold_date > NOW() - INTERVAL '14 days')
            AND d.mps_id IS NOT NULL
            AND d.weight IS NOT NULL
            AND d.color_id IS NOT NULL
            AND (d.mps_id = sdasin_rec.mps_id OR (sdasin_rec.mps_id2 IS NOT NULL AND d.mps_id = sdasin_rec.mps_id2))
            AND d.weight >= sdasin_rec.min_weight
            AND d.weight <= sdasin_rec.max_weight
            AND (d.color_id = sdasin_rec.color_id OR sdasin_rec.color_id = 23)
    )
    INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
    SELECT 
        disc_id,
        sdasin_id_param,
        reason,
        'system'
    FROM 
        matching_discs;
    
    -- Get the number of matches
    GET DIAGNOSTICS match_count = ROW_COUNT;
    
    -- Update the SDASIN record to mark that matching has been attempted
    UPDATE t_sdasins
    SET looked_for_matching_discs_at = NOW()
    WHERE id = sdasin_id_param;
    
    RETURN match_count;
END;
$$ LANGUAGE plpgsql;

-- Step 2: Create the function to enqueue a task for finding discs to match with an updated SDASIN
CREATE OR REPLACE FUNCTION fn_enqueue_sdasin_updated_find_discs_to_match()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert a task into the task queue
    INSERT INTO t_task_queue (
        task_type,
        payload,
        status,
        scheduled_at,
        created_at
    ) VALUES (
        'sdasin_updated_find_discs_to_match',
        jsonb_build_object('id', NEW.id),
        'pending',
        NOW(),
        NOW()
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Create the new INSERT trigger
DROP TRIGGER IF EXISTS tr_enqueue_sdasin_updated_find_discs_to_match_insert ON t_sdasins;

CREATE TRIGGER tr_enqueue_sdasin_updated_find_discs_to_match_insert
AFTER INSERT ON t_sdasins
FOR EACH ROW
WHEN (
    NEW.min_weight IS NOT NULL
    AND NEW.max_weight IS NOT NULL
    AND (NEW.mps_id IS NOT NULL OR NEW.mps_id2 IS NOT NULL)
    AND NEW.color_id IS NOT NULL
)
EXECUTE FUNCTION fn_enqueue_sdasin_updated_find_discs_to_match();

-- Step 4: Create the new UPDATE trigger for min_weight, max_weight, mps_id, mps_id2, color_id
DROP TRIGGER IF EXISTS tr_enqueue_sdasin_updated_find_discs_to_match_update ON t_sdasins;

CREATE TRIGGER tr_enqueue_sdasin_updated_find_discs_to_match_update
AFTER UPDATE OF min_weight, max_weight, mps_id, mps_id2, color_id ON t_sdasins
FOR EACH ROW
WHEN (
    NEW.min_weight IS NOT NULL
    AND NEW.max_weight IS NOT NULL
    AND (NEW.mps_id IS NOT NULL OR NEW.mps_id2 IS NOT NULL)
    AND NEW.color_id IS NOT NULL
)
EXECUTE FUNCTION fn_enqueue_sdasin_updated_find_discs_to_match();

-- Step 5: Create the new UPDATE trigger for looked_for_matching_discs_at
DROP TRIGGER IF EXISTS tr_enqueue_sdasin_updated_find_discs_to_match_reset ON t_sdasins;

CREATE TRIGGER tr_enqueue_sdasin_updated_find_discs_to_match_reset
AFTER UPDATE OF looked_for_matching_discs_at ON t_sdasins
FOR EACH ROW
WHEN (
    OLD.looked_for_matching_discs_at IS NOT NULL
    AND NEW.looked_for_matching_discs_at IS NULL
)
EXECUTE FUNCTION fn_enqueue_sdasin_updated_find_discs_to_match();

-- Step 6: Drop the old triggers
DROP TRIGGER IF EXISTS tr_match_on_sdasin_in ON t_sdasins;
DROP TRIGGER IF EXISTS tr_match_on_sdasin_up ON t_sdasins;
DROP TRIGGER IF EXISTS tr_match_on_sdasin_update_looked_for ON t_sdasins;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'SDASIN update task queue changes applied successfully.';
END $$;
