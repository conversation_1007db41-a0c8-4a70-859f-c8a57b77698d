import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testSummary() {
    try {
        console.log('🧪 Testing summary function...\n');
        
        // Get all records using pagination to avoid Supabase limits
        let allRecords = [];
        let from = 0;
        const pageSize = 1000;

        while (true) {
            const { data: batch, error } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('plastic_name, mold_name, is_currently_available')
                .range(from, from + pageSize - 1);

            if (error) {
                console.warn('Could not generate summary:', error.message);
                return;
            }

            if (batch.length === 0) break;

            allRecords = allRecords.concat(batch);
            from += pageSize;

            if (batch.length < pageSize) break; // Last page
        }

        console.log(`📊 Summary query returned ${allRecords.length} records`);
        const summary = allRecords;
        
        // Get accurate total count
        const { count: totalCount, error: countError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*', { count: 'exact', head: true });
        
        console.log(`📊 Count query returned ${totalCount} records`);
        
        const byPlastic = {};
        const byMold = {};
        let availableCount = 0;
        let outOfStockCount = 0;
        
        summary.forEach(record => {
            byPlastic[record.plastic_name] = (byPlastic[record.plastic_name] || 0) + 1;
            byMold[record.mold_name] = (byMold[record.mold_name] || 0) + 1;
            
            if (record.is_currently_available) {
                availableCount++;
            } else {
                outOfStockCount++;
            }
        });
        
        console.log('\n📈 By Availability:');
        console.log(`  ✅ Available now: ${availableCount} products`);
        console.log(`  ❌ Out of stock: ${outOfStockCount} products`);
        console.log(`  📊 Total: ${countError ? summary.length : totalCount} products`);
        
        console.log('\n🎉 Summary test completed!');
        
    } catch (error) {
        console.error('❌ Error:', error);
    }
}

testSummary().catch(console.error);
