// updateDiscraftLookbackDays.js - Update the configurable lookback days for Discraft disc ordering

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function updateLookbackDays(newDays) {
    console.log(`🔧 Updating Discraft lookback days to ${newDays}...`);
    console.log('==============================================');

    try {
        // Validate input
        const days = parseInt(newDays);
        if (isNaN(days) || days < 1 || days > 365) {
            throw new Error('Days must be a number between 1 and 365');
        }

        // Update the config value
        const { error: updateError } = await supabase
            .from('t_config')
            .update({ value: days.toString() })
            .eq('key', 'discraft_disc_order_look_back_days');

        if (updateError) {
            throw new Error(`Error updating config: ${updateError.message}`);
        }

        // Verify the update
        const { data: verifyData, error: verifyError } = await supabase
            .from('t_config')
            .select('key, value')
            .eq('key', 'discraft_disc_order_look_back_days')
            .single();

        if (verifyError) {
            throw new Error(`Error verifying update: ${verifyError.message}`);
        }

        console.log('✅ Successfully updated configuration:');
        console.log(`   Key: ${verifyData.key}`);
        console.log(`   Value: ${verifyData.value} days`);
        
        console.log('\n📋 Impact:');
        console.log(`- The system will now count discs sold in the last ${verifyData.value} days`);
        console.log('- This change takes effect immediately on the next Discraft import');
        console.log('- Both the stored procedure and fallback methods will use this value');
        
        console.log('\n🎉 Update completed successfully!');

    } catch (error) {
        console.error('❌ Update failed:', error.message);
        process.exit(1);
    }
}

// Get the new days value from command line argument
const newDays = process.argv[2];

if (!newDays) {
    console.log('📋 Usage: node updateDiscraftLookbackDays.js <days>');
    console.log('');
    console.log('Examples:');
    console.log('  node updateDiscraftLookbackDays.js 45   # Set to 45 days');
    console.log('  node updateDiscraftLookbackDays.js 60   # Set to 60 days');
    console.log('  node updateDiscraftLookbackDays.js 90   # Set to 90 days');
    console.log('');
    console.log('Current configuration:');
    
    // Show current value
    supabase
        .from('t_config')
        .select('value')
        .eq('key', 'discraft_disc_order_look_back_days')
        .single()
        .then(({ data, error }) => {
            if (error) {
                console.log('❌ Could not retrieve current value');
            } else {
                console.log(`Current lookback days: ${data.value}`);
            }
            process.exit(0);
        });
} else {
    // Run the update
    updateLookbackDays(newDays)
        .then(() => {
            console.log('\n✅ Update script completed');
            process.exit(0);
        })
        .catch(error => {
            console.error('💥 Update script failed:', error);
            process.exit(1);
        });
}
