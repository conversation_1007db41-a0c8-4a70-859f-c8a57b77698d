// verify_trigger_with_rpc.js - Verify the trigger using RPC exec_sql
import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in environment');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Checking if trigger function exists using RPC...');
    
    // Check if function exists
    const functionCheckSql = `
      SELECT 
        proname as function_name,
        pg_get_function_result(oid) as return_type,
        pg_get_function_arguments(oid) as arguments
      FROM pg_proc 
      WHERE proname = 'enqueue_variant_ready_on_image_verified'
      AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');
    `;
    
    const { data: functionData, error: functionError } = await supabase.rpc('exec_sql', { sql_query: functionCheckSql });
    
    if (functionError) {
      console.error('Error checking function:', functionError);
    } else {
      if (functionData && functionData.length > 0) {
        console.log('✅ Function enqueue_variant_ready_on_image_verified exists');
        console.log('   Return type:', functionData[0].return_type);
        console.log('   Arguments:', functionData[0].arguments);
      } else {
        console.log('❌ Function enqueue_variant_ready_on_image_verified not found');
      }
    }

    console.log('\nChecking if trigger exists using RPC...');
    
    // Check if trigger exists
    const triggerCheckSql = `
      SELECT 
        t.tgname as trigger_name,
        c.relname as table_name,
        p.proname as function_name,
        pg_get_triggerdef(t.oid) as trigger_definition
      FROM pg_trigger t
      JOIN pg_class c ON t.tgrelid = c.oid
      JOIN pg_proc p ON t.tgfoid = p.oid
      WHERE t.tgname = 'trg_t_images_enqueue_variant_ready_on_image_verified'
      AND c.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');
    `;
    
    const { data: triggerData, error: triggerError } = await supabase.rpc('exec_sql', { sql_query: triggerCheckSql });
    
    if (triggerError) {
      console.error('Error checking trigger:', triggerError);
    } else {
      if (triggerData && triggerData.length > 0) {
        console.log('✅ Trigger trg_t_images_enqueue_variant_ready_on_image_verified exists');
        console.log('   Table:', triggerData[0].table_name);
        console.log('   Function:', triggerData[0].function_name);
        console.log('   Definition:', triggerData[0].trigger_definition);
      } else {
        console.log('❌ Trigger trg_t_images_enqueue_variant_ready_on_image_verified not found');
      }
    }

    console.log('\nChecking all triggers on t_images table...');
    
    // Check all triggers on t_images
    const allTriggersCheckSql = `
      SELECT 
        t.tgname as trigger_name,
        p.proname as function_name,
        CASE 
          WHEN t.tgtype & 2 = 2 THEN 'BEFORE'
          WHEN t.tgtype & 4 = 4 THEN 'AFTER'
          ELSE 'INSTEAD OF'
        END as timing,
        CASE 
          WHEN t.tgtype & 4 = 4 THEN 'INSERT'
          WHEN t.tgtype & 8 = 8 THEN 'DELETE'  
          WHEN t.tgtype & 16 = 16 THEN 'UPDATE'
          ELSE 'OTHER'
        END as event
      FROM pg_trigger t
      JOIN pg_class c ON t.tgrelid = c.oid
      JOIN pg_proc p ON t.tgfoid = p.oid
      WHERE c.relname = 't_images'
      AND c.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
      AND NOT t.tgisinternal
      ORDER BY t.tgname;
    `;
    
    const { data: allTriggersData, error: allTriggersError } = await supabase.rpc('exec_sql', { sql_query: allTriggersCheckSql });
    
    if (allTriggersError) {
      console.error('Error checking all triggers:', allTriggersError);
    } else {
      if (allTriggersData && allTriggersData.length > 0) {
        console.log('All triggers on t_images table:');
        allTriggersData.forEach(trigger => {
          console.log(`  - ${trigger.trigger_name} (${trigger.timing} ${trigger.event}) -> ${trigger.function_name}`);
        });
      } else {
        console.log('No triggers found on t_images table');
      }
    }

  } catch (e) {
    console.error('Fatal error:', e?.message || e);
    process.exit(1);
  }
}

main();
