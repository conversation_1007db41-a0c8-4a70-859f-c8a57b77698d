// check_existing_triggers.js - Check what triggers actually exist on t_images
import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in environment');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Checking what triggers actually exist on t_images table...');
    
    // Check all triggers on t_images
    const allTriggersCheckSql = `
      SELECT 
        t.tgname as trigger_name,
        p.proname as function_name,
        CASE 
          WHEN t.tgtype & 2 = 2 THEN 'BEFORE'
          WHEN t.tgtype & 4 = 4 THEN 'AFTER'
          ELSE 'INSTEAD OF'
        END as timing,
        CASE 
          WHEN t.tgtype & 4 = 4 THEN 'INSERT'
          WHEN t.tgtype & 8 = 8 THEN 'DELETE'  
          WHEN t.tgtype & 16 = 16 THEN 'UPDATE'
          ELSE 'OTHER'
        END as event,
        pg_get_triggerdef(t.oid) as full_definition
      FROM pg_trigger t
      JOIN pg_class c ON t.tgrelid = c.oid
      JOIN pg_proc p ON t.tgfoid = p.oid
      WHERE c.relname = 't_images'
      AND c.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
      AND NOT t.tgisinternal
      ORDER BY t.tgname;
    `;
    
    const { data: allTriggersData, error: allTriggersError } = await supabase.rpc('exec_sql', { sql_query: allTriggersCheckSql });
    
    if (allTriggersError) {
      console.error('Error checking all triggers:', allTriggersError);
    } else {
      if (allTriggersData && allTriggersData.length > 0) {
        console.log(`Found ${allTriggersData.length} triggers on t_images table:`);
        allTriggersData.forEach((trigger, index) => {
          console.log(`\n${index + 1}. ${trigger.trigger_name}`);
          console.log(`   Timing: ${trigger.timing}`);
          console.log(`   Event: ${trigger.event}`);
          console.log(`   Function: ${trigger.function_name}`);
          console.log(`   Definition: ${trigger.full_definition}`);
        });
      } else {
        console.log('No triggers found on t_images table');
      }
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('Checking all functions that contain "image" in their name...');
    
    const imageFunctionsCheckSql = `
      SELECT 
        proname as function_name,
        pg_get_function_result(oid) as return_type,
        pg_get_function_arguments(oid) as arguments
      FROM pg_proc 
      WHERE proname ILIKE '%image%'
      AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
      ORDER BY proname;
    `;
    
    const { data: imageFunctionsData, error: imageFunctionsError } = await supabase.rpc('exec_sql', { sql_query: imageFunctionsCheckSql });
    
    if (imageFunctionsError) {
      console.error('Error checking image functions:', imageFunctionsError);
    } else {
      if (imageFunctionsData && imageFunctionsData.length > 0) {
        console.log(`Found ${imageFunctionsData.length} functions with "image" in name:`);
        imageFunctionsData.forEach((func, index) => {
          console.log(`${index + 1}. ${func.function_name}(${func.arguments}) -> ${func.return_type}`);
        });
      } else {
        console.log('No functions with "image" in name found');
      }
    }

  } catch (e) {
    console.error('Fatal error:', e?.message || e);
    process.exit(1);
  }
}

main();
