// manualImport.js - <PERSON><PERSON><PERSON> to manually import a DBF file

import importDbfToSupabase from './importDbfToSupabase.js';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('file', {
    alias: 'f',
    description: 'Path to the DBF file',
    type: 'string',
    default: process.env.DBF_FILE_PATH || './data/daily_import.dbf'
  })
  .option('table', {
    alias: 't',
    description: 'Target table in Supabase',
    type: 'string',
    default: process.env.TARGET_TABLE || 'imported_dbf_data'
  })
  .option('truncate', {
    alias: 'c',
    description: 'Truncate table before import',
    type: 'boolean',
    default: process.env.TRUNCATE_BEFORE_IMPORT === 'true' || false
  })
  .help()
  .alias('help', 'h')
  .argv;

// Run the import
console.log('Starting manual import with the following parameters:');
console.log(`File: ${argv.file}`);
console.log(`Table: ${argv.table}`);
console.log(`Truncate: ${argv.truncate}`);

importDbfToSupabase(argv.file, argv.table, argv.truncate)
  .then(result => {
    if (result.success) {
      console.log(`Import completed successfully. Imported ${result.recordsImported} records.`);
      process.exit(0);
    } else {
      console.error(`Import failed: ${result.error}`);
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Unhandled error during import:', error);
    process.exit(1);
  });
