@echo off
REM Script to run the batch enqueue carry cost tasks with common parameters

REM Default values
set BATCH_SIZE=500
set DELAY=1000
set START_ID=0
set LIMIT=0
set DRY_RUN=false

REM Parse command line arguments
:parse_args
if "%~1"=="" goto :end_parse_args
if "%~1"=="-b" (
    set BATCH_SIZE=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--batch-size" (
    set BATCH_SIZE=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-d" (
    set DELAY=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--delay" (
    set DELAY=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-s" (
    set START_ID=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--start-id" (
    set START_ID=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="-l" (
    set LIMIT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--limit" (
    set LIMIT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--dry-run" (
    set DRY_RUN=true
    shift
    goto :parse_args
)
if "%~1"=="-h" (
    goto :show_usage
)
if "%~1"=="--help" (
    goto :show_usage
)
echo Unknown option: %~1
goto :show_usage

:show_usage
echo Usage: %0 [options]
echo Options:
echo   -b, --batch-size NUM    Number of records to process in each batch (default: 500)
echo   -d, --delay NUM         Delay in milliseconds between batches (default: 1000)
echo   -s, --start-id NUM      ID to start processing from (default: 0)
echo   -l, --limit NUM         Maximum number of records to process, 0 for all (default: 0)
echo   --dry-run               Show what would be done without actually enqueueing tasks
echo   -h, --help              Show this help message
echo.
echo Examples:
echo   %0                                  # Process all records in batches of 500
echo   %0 -b 100 -d 500                    # Process in smaller batches with less delay
echo   %0 -s 10000 -l 5000                 # Process 5000 records starting from ID 10000
echo   %0 --dry-run                        # Dry run to see what would be processed
exit /b 1

:end_parse_args

REM Build the command
set CMD=node batchEnqueueCarryCostTasks.js --batch-size %BATCH_SIZE% --delay %DELAY% --start-id %START_ID% --limit %LIMIT%

if "%DRY_RUN%"=="true" (
    set CMD=%CMD% --dry-run
)

REM Display the command
echo Running: %CMD%
echo.

REM Execute the command
%CMD%
