require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testRedirectLogicBothTasks() {
  try {
    console.log('Testing redirect logic in both match_disc_to_osl and match_osl_to_discs tasks...');
    
    // Test case: Disc 408210 should redirect from OSL 11602 (MPS 16912) to OSL 18756 (MPS 19687)
    const testDiscId = 408210;
    const initialOslId = 11602; // Should be skipped due to redirect
    const expectedFinalOslId = 18756; // Should be the final result
    
    console.log('\n=== TEST CASE SETUP ===');
    console.log(`Disc ${testDiscId} should redirect from OSL ${initialOslId} to OSL ${expectedFinalOslId}`);
    
    // Get disc details
    const { data: disc, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
      .eq('id', testDiscId)
      .single();
    
    if (discError) {
      console.error('Error getting disc:', discError);
      return;
    }
    
    console.log(`\nDisc ${testDiscId}: MPS ${disc.mps_id}, Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id}`);
    
    // Clear existing mappings for clean test
    console.log('\nClearing existing mappings for clean test...');
    await supabase
      .from('t_discs')
      .update({ 
        order_sheet_line_id: null,
        vendor_osl_id: null 
      })
      .eq('id', testDiscId);
    
    // TEST 1: match_disc_to_osl task
    console.log('\n=== TEST 1: match_disc_to_osl TASK ===');
    
    const { data: matchDiscTask, error: matchDiscError } = await supabase
      .from('t_task_queue')
      .insert([
        {
          task_type: 'match_disc_to_osl',
          payload: { 
            id: testDiscId,
            operation: 'INSERT'
          },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: 'test_redirect_logic_both_tasks'
        }
      ])
      .select();
    
    if (matchDiscError) {
      console.error('Error creating match_disc_to_osl task:', matchDiscError);
    } else {
      const taskId = matchDiscTask[0].id;
      console.log(`✅ Created match_disc_to_osl task ${taskId} for disc ${testDiscId}`);
      console.log('This task should:');
      console.log('1. Find regular OSL using redirect function → should get OSL 18756');
      console.log('2. Find vendor OSL using redirect function → should get OSL 18756');
      console.log('3. Update disc with both mappings');
    }
    
    // TEST 2: match_osl_to_discs task for OSL with redirect
    console.log('\n=== TEST 2: match_osl_to_discs TASK (OSL with redirect) ===');
    
    const { data: matchOslTask, error: matchOslError } = await supabase
      .from('t_task_queue')
      .insert([
        {
          task_type: 'match_osl_to_discs',
          payload: { id: initialOslId },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: 'test_redirect_logic_both_tasks'
        }
      ])
      .select();
    
    if (matchOslError) {
      console.error('Error creating match_osl_to_discs task:', matchOslError);
    } else {
      const taskId = matchOslTask[0].id;
      console.log(`✅ Created match_osl_to_discs task ${taskId} for OSL ${initialOslId}`);
      console.log('This task should:');
      console.log('1. Detect that OSL 11602 belongs to MPS 16912 which has redirect');
      console.log('2. Skip vendor mapping for this OSL');
      console.log('3. Still do regular mapping but note the redirect in results');
    }
    
    // TEST 3: match_osl_to_discs task for OSL without redirect
    console.log('\n=== TEST 3: match_osl_to_discs TASK (OSL without redirect) ===');
    
    const { data: matchOslNoRedirectTask, error: matchOslNoRedirectError } = await supabase
      .from('t_task_queue')
      .insert([
        {
          task_type: 'match_osl_to_discs',
          payload: { id: expectedFinalOslId },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: 'test_redirect_logic_both_tasks'
        }
      ])
      .select();
    
    if (matchOslNoRedirectError) {
      console.error('Error creating match_osl_to_discs task:', matchOslNoRedirectError);
    } else {
      const taskId = matchOslNoRedirectTask[0].id;
      console.log(`✅ Created match_osl_to_discs task ${taskId} for OSL ${expectedFinalOslId}`);
      console.log('This task should:');
      console.log('1. Detect that OSL 18756 belongs to MPS 19687 which has NO redirect');
      console.log('2. Process both regular and vendor mapping normally');
      console.log('3. Find matching discs for both mappings');
    }
    
    console.log('\n🔄 All test tasks created and ready for processing by the worker daemon.');
    
    console.log('\n📋 EXPECTED RESULTS:');
    console.log('\n1. match_disc_to_osl task:');
    console.log(`   - Disc ${testDiscId} should get order_sheet_line_id = ${expectedFinalOslId} (redirected)`);
    console.log(`   - Disc ${testDiscId} should get vendor_osl_id = ${expectedFinalOslId} (redirected)`);
    
    console.log('\n2. match_osl_to_discs task (OSL 11602 with redirect):');
    console.log('   - Should skip vendor mapping due to redirect');
    console.log('   - Should include redirect info in results');
    console.log('   - Regular mapping should still work');
    
    console.log('\n3. match_osl_to_discs task (OSL 18756 without redirect):');
    console.log('   - Should process both regular and vendor mapping');
    console.log('   - Should find matching discs normally');
    
    console.log('\n📊 To check results after processing:');
    console.log(`
-- Check disc mappings
SELECT id, order_sheet_line_id, vendor_osl_id 
FROM t_discs 
WHERE id = ${testDiscId};

-- Check task results
SELECT 
  id, 
  task_type,
  status, 
  result->>'message' as message,
  result->>'redirect_info' as redirect_info,
  processed_at
FROM t_task_queue 
WHERE enqueued_by = 'test_redirect_logic_both_tasks'
ORDER BY created_at;
    `);
    
    console.log('\n🎯 KEY SUCCESS INDICATORS:');
    console.log('✅ Disc 408210 should have both order_sheet_line_id AND vendor_osl_id = 18756');
    console.log('✅ Tasks should include redirect information in results');
    console.log('✅ OSL 11602 task should skip vendor mapping');
    console.log('✅ OSL 18756 task should process vendor mapping normally');
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

testRedirectLogicBothTasks();
