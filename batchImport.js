// batchImport.js - <PERSON>ript to import DBF data in batches

import { createClient } from '@supabase/supabase-js';
import { DBFFile } from 'dbffile';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Create a log file
const logFile = 'batch_import.log';
fs.writeFileSync(logFile, `Starting batch import at ${new Date().toISOString()}\n`);

// Load environment variables
dotenv.config();
fs.appendFileSync(logFile, `Environment variables loaded\n`);

// Get parameters
const dbfFilePath = process.argv[2] || process.env.DBF_FILE_PATH || 'R:\\Rpro\\BRIDGE\\invdb.dbf';
const targetTable = process.argv[3] || process.env.TARGET_TABLE || 'imported_table_rpro';
// Precedence: CLI arg (if provided) overrides env; otherwise fall back to env
const truncateBeforeImport = (typeof process.argv[4] !== 'undefined')
  ? (process.argv[4] === 'true')
  : (process.env.TRUNCATE_BEFORE_IMPORT === 'true');
const batchSize = parseInt(process.argv[5] || process.env.BATCH_SIZE || '1000', 10);
const startBatch = parseInt(process.argv[6] || process.env.START_BATCH || '0', 10);
const endBatch = parseInt(process.argv[7] || process.env.END_BATCH || '999999', 10);

fs.appendFileSync(logFile, `Parameters:\n`);
fs.appendFileSync(logFile, `- DBF file path: ${dbfFilePath}\n`);
fs.appendFileSync(logFile, `- Target table: ${targetTable}\n`);
fs.appendFileSync(logFile, `- Truncate before import: ${truncateBeforeImport}\n`);
fs.appendFileSync(logFile, `- Batch size: ${batchSize}\n`);
fs.appendFileSync(logFile, `- Start batch: ${startBatch}\n`);
fs.appendFileSync(logFile, `- End batch: ${endBatch}\n`);

// Check if file exists
if (!fs.existsSync(dbfFilePath)) {
  const errorMsg = `DBF file not found at path: ${dbfFilePath}`;
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

fs.appendFileSync(logFile, `DBF file exists\n`);

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
// Prefer a service role key for write access to protected tables (runs/changes), fall back to SUPABASE_KEY
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  const errorMsg = 'Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file';
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

fs.appendFileSync(logFile, `Supabase credentials found\n`);
fs.appendFileSync(logFile, `- URL: ${supabaseUrl}\n`);
fs.appendFileSync(logFile, `- Key: ${supabaseKey.substring(0, 10)}...\n`);

const supabase = createClient(supabaseUrl, supabaseKey);
fs.appendFileSync(logFile, `Supabase client created\n`);

// Track all ivno values seen in this import run (for missing-record detection)
const importedIvnos = new Set();

// Track current import run id for audit
let currentImportRunId = null;

// Ensure change log table exists (best-effort via exec_sql if available)
async function ensureChangeTableExists() {
  const createSQL = `
CREATE TABLE IF NOT EXISTS public.t_rpro_changes (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  ivno INTEGER NOT NULL,
  field_changed TEXT NOT NULL,
  was TEXT,
  is_now TEXT,
  change_date TIMESTAMPTZ NOT NULL DEFAULT now()
);
CREATE INDEX IF NOT EXISTS idx_t_rpro_changes_ivno ON public.t_rpro_changes(ivno);
`;
  try {
    const { error } = await supabase.rpc('exec_sql', { sql_query: createSQL });
    if (error) {
      fs.appendFileSync(logFile, `exec_sql unavailable or failed for t_rpro_changes (${error.message}). Proceeding assuming table exists.\n`);
    } else {
      fs.appendFileSync(logFile, `Ensured t_rpro_changes table exists.\n`);
    }
  } catch (e) {
    fs.appendFileSync(logFile, `Could not ensure t_rpro_changes exists: ${e.message}\n`);
  }
}

// Ensure import runs table exists (best-effort)
async function ensureImportRunsTableExists() {
  const createSQL = `
CREATE TABLE IF NOT EXISTS public.t_rpro_import_runs (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  started_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  finished_at TIMESTAMPTZ,
  mode TEXT NOT NULL,
  truncate_before_import BOOLEAN NOT NULL DEFAULT false,
  dbf_file_path TEXT,
  target_table TEXT,
  total_records INTEGER,
  total_batches INTEGER,
  notes TEXT
);
CREATE INDEX IF NOT EXISTS idx_t_rpro_import_runs_started_at ON public.t_rpro_import_runs(started_at DESC);
`;
  try {
    const { error } = await supabase.rpc('exec_sql', { sql_query: createSQL });
    if (error) {
      fs.appendFileSync(logFile, `exec_sql unavailable or failed for t_rpro_import_runs (${error.message}). Proceeding assuming table exists.\n`);
    } else {
      fs.appendFileSync(logFile, `Ensured t_rpro_import_runs table exists.\n`);
    }
  } catch (e) {
    fs.appendFileSync(logFile, `Could not ensure t_rpro_import_runs exists: ${e.message}\n`);
  }
}

function normalize(val) {
  if (val === undefined) return null;
  return val;
}

function valuesEqual(a, b) {
  a = normalize(a); b = normalize(b);
  if (a === b) return true;
  if (a == null && b == null) return true;
  // Compare numbers numerically when both look numeric
  const an = typeof a === 'number' ? a : (typeof a === 'string' && a.trim() !== '' && !isNaN(Number(a)) ? Number(a) : null);
  const bn = typeof b === 'number' ? b : (typeof b === 'string' && b.trim() !== '' && !isNaN(Number(b)) ? Number(b) : null);
  if (an !== null && bn !== null) return an === bn;
  // Fallback string compare
  return String(a) === String(b);
}

async function insertChangesInChunks(changes, chunkSize = 1000) {
  for (let i = 0; i < changes.length; i += chunkSize) {
    const chunk = changes.slice(i, i + chunkSize);
    const { error } = await supabase.from('t_rpro_changes').insert(chunk);
    if (error) {
      fs.appendFileSync(logFile, `ERROR inserting change-log chunk: ${error.message}\n`);
      console.error(`ERROR inserting change-log chunk: ${error.message}`);
      throw error;
    }
  }
}

// Upsert a batch and log diffs into t_rpro_changes
async function processBatchUpsertAndLog(records, batchIndex) {
  const ivnos = records.map(r => r.ivno).filter(v => v !== null && v !== undefined);
  ivnos.forEach(v => importedIvnos.add(v));

  // Fetch existing rows for these ivnos
  let existingMap = new Map();
  if (ivnos.length > 0) {
    const { data: existingRows, error } = await supabase
      .from(targetTable)
      .select('*')
      .in('ivno', ivnos);
    if (error) {
      fs.appendFileSync(logFile, `ERROR fetching existing rows for diffs: ${error.message}\n`);
      console.error(`ERROR fetching existing rows for diffs: ${error.message}`);
      throw error;
    }
    existingMap = new Map(existingRows.map(r => [r.ivno, r]));
  }

  const upserts = [];
  const changes = [];
  const nowIso = new Date().toISOString();

  // Fields to ignore for per-field change logging
  const IGNORE_FIELDS_FOR_DIFF = new Set(['id', 'imported_at', 'import_batch_id', 'ivqsacm']);

  for (const rec of records) {
    const oldRow = existingMap.get(rec.ivno);
    if (!oldRow) {
      changes.push({ ivno: rec.ivno, field_changed: 'RECORD_CREATED', was: null, is_now: JSON.stringify(rec), change_date: nowIso });
    } else {
      for (const k of Object.keys(rec)) {
        if (IGNORE_FIELDS_FOR_DIFF.has(k)) continue; // Skip non-diff and excluded fields
        const newVal = rec[k];
        const oldVal = oldRow[k];
        if (!valuesEqual(oldVal, newVal)) {
          changes.push({
            ivno: rec.ivno,
            field_changed: k,
            was: oldVal === null || oldVal === undefined ? null : JSON.stringify(oldVal),
            is_now: newVal === null || newVal === undefined ? null : JSON.stringify(newVal),
            change_date: nowIso
          });
        }
      }
    }
    upserts.push(rec);
  }

  if (changes.length > 0) {
    fs.appendFileSync(logFile, `Batch ${batchIndex + 1}: logging ${changes.length} change(s)\n`);
    await insertChangesInChunks(changes);
  }

  // Upsert the batch on ivno
  const { error: upsertError } = await supabase
    .from(targetTable)
    .upsert(upserts, { onConflict: 'ivno' });
  if (upsertError) {
    fs.appendFileSync(logFile, `ERROR upserting batch ${batchIndex + 1}: ${upsertError.message}\n`);
    console.error(`ERROR upserting batch ${batchIndex + 1}: ${upsertError.message}`);
    throw upsertError;
  }

  fs.appendFileSync(logFile, `Successfully upserted batch ${batchIndex + 1} (${upserts.length} records)\n`);
  console.log(`Successfully upserted batch ${batchIndex + 1} (${upserts.length} records)`);
  return upserts.length;
}

async function logMissingRecords() {
  // Determine total rows in targetTable
  const pageSize = 1000;
  const { count, error: countError } = await supabase
    .from(targetTable)
    .select('ivno', { count: 'exact', head: false })
    .limit(1);
  if (countError) {
    fs.appendFileSync(logFile, `ERROR getting count for missing-records check: ${countError.message}\n`);
    console.error(`ERROR getting count for missing-records check: ${countError.message}`);
    return;
  }
  const total = count || 0;
  let offset = 0;
  let changes = [];

  const nowIso = new Date().toISOString();
  while (offset < total) {
    const { data: rows, error } = await supabase
      .from(targetTable)
      .select('*')
      .range(offset, Math.min(offset + pageSize - 1, total - 1));
    if (error) {
      fs.appendFileSync(logFile, `ERROR paging rows for missing-records check: ${error.message}\n`);
      console.error(`ERROR paging rows for missing-records check: ${error.message}`);
      break;
    }
    for (const row of rows) {
      if (!importedIvnos.has(row.ivno)) {
        changes.push({ ivno: row.ivno, field_changed: 'RECORD_MISSING_IN_IMPORT', was: JSON.stringify(row), is_now: null, change_date: nowIso });
      }
    }
    if (changes.length >= 1000) {
      await insertChangesInChunks(changes);
      changes = [];
    }
    offset += pageSize;
  }
  if (changes.length > 0) {
    await insertChangesInChunks(changes);
  }
  fs.appendFileSync(logFile, `Logged missing-record changes for records not present in latest import.\n`);
}
// Function to process a batch of records
async function processBatch(records, batchIndex) {
  try {
    const batchStart = batchIndex * batchSize;
    const batchEnd = Math.min(batchStart + batchSize, records.length);
    const batch = records.slice(batchStart, batchEnd);

    if (batch.length === 0) {
      fs.appendFileSync(logFile, `Batch ${batchIndex + 1} is empty, skipping\n`);
      return 0;
    }

    fs.appendFileSync(logFile, `Processing batch ${batchIndex + 1} (records ${batchStart + 1}-${batchEnd})\n`);

    // Process records
    const processedRecords = batch.map(record => {
      const processedRecord = {};

      // Convert all field names to lowercase for Supabase compatibility
      for (const key in record) {
        const lowerKey = key.toLowerCase();

        // Handle special characters in field names
        const safeKey = lowerKey.replace(/\s/g, '_').replace(/\$/g, 'dollar');

        // Convert Buffer objects to strings
        if (Buffer.isBuffer(record[key])) {
          processedRecord[safeKey] = record[key].toString().trim();
        }
        // Convert Date objects to ISO strings
        else if (record[key] instanceof Date) {
          // Store dates as YYYY-MM-DD to match DATE columns and avoid false diffs
          processedRecord[safeKey] = record[key].toISOString().slice(0, 10);
        }
        // Handle null values
        else if (record[key] === null || record[key] === undefined) {
          processedRecord[safeKey] = null;
        }
        // Keep other values as is
        else {
          processedRecord[safeKey] = record[key];
        }
      }

      // Add metadata fields
      processedRecord.imported_at = new Date().toISOString();

      return processedRecord;
    });

    fs.appendFileSync(logFile, `Processed ${processedRecords.length} records in batch ${batchIndex + 1}\n`);

    // Insert or upsert the batch depending on mode
    if (truncateBeforeImport) {
      fs.appendFileSync(logFile, `Inserting batch ${batchIndex + 1} into ${targetTable}...\n`);
      const { error } = await supabase
        .from(targetTable)
        .insert(processedRecords);
      if (error) {
        fs.appendFileSync(logFile, `ERROR inserting batch ${batchIndex + 1}: ${error.message}\n`);
        console.error(`ERROR inserting batch ${batchIndex + 1}: ${error.message}`);
        return 0;
      }
      fs.appendFileSync(logFile, `Successfully inserted batch ${batchIndex + 1} (${processedRecords.length} records)\n`);
      console.log(`Successfully inserted batch ${batchIndex + 1} (${processedRecords.length} records)`);
      return processedRecords.length;
    } else {
      // Upsert with change logging
      return await processBatchUpsertAndLog(processedRecords, batchIndex);
    }

    if (error) {
      fs.appendFileSync(logFile, `ERROR inserting batch ${batchIndex + 1}: ${error.message}\n`);

      console.error(`ERROR inserting batch ${batchIndex + 1}: ${error.message}`);

      // Try to get more details about the error
      if (error.details) {
        fs.appendFileSync(logFile, `Error details: ${JSON.stringify(error.details)}\n`);
        console.error(`Error details: ${JSON.stringify(error.details)}`);
      }

      // Try to get the first record that might be causing issues
      if (processedRecords.length > 0) {
        fs.appendFileSync(logFile, `First record in batch: ${JSON.stringify(processedRecords[0])}\n`);
      }

      return 0;
    }

    fs.appendFileSync(logFile, `Successfully inserted batch ${batchIndex + 1} (${processedRecords.length} records)\n`);
    console.log(`Successfully inserted batch ${batchIndex + 1} (${processedRecords.length} records)`);

    return processedRecords.length;
  } catch (error) {
    fs.appendFileSync(logFile, `ERROR processing batch ${batchIndex + 1}: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`ERROR processing batch ${batchIndex + 1}: ${error.message}`);
    console.error(error.stack);
    return 0;
  }
}

// Main function
async function importDbf() {
  try {
    fs.appendFileSync(logFile, `Opening DBF file...\n`);
    const dbf = await DBFFile.open(dbfFilePath);
    fs.appendFileSync(logFile, `DBF file opened. Found ${dbf.recordCount} records.\n`);
    console.log(`DBF file opened. Found ${dbf.recordCount} records.`);

    // Truncate table if requested
    if (truncateBeforeImport) {
      fs.appendFileSync(logFile, `Truncating table ${targetTable}...\n`);
      console.log(`Truncating table ${targetTable}...`);

      try {
        const { error: truncateError } = await supabase.rpc('truncate_table', { table_name: targetTable });

        if (truncateError) {
          fs.appendFileSync(logFile, `ERROR truncating table: ${truncateError.message}\n`);
          console.error(`ERROR truncating table: ${truncateError.message}`);

          // Try an alternative approach
          fs.appendFileSync(logFile, `Trying alternative truncate approach...\n`);
          const { error: deleteError } = await supabase
            .from(targetTable)
            .delete()


            .neq('id', 0); // This will delete all records

          if (deleteError) {
            fs.appendFileSync(logFile, `ERROR with alternative truncate: ${deleteError.message}\n`);
            console.error(`ERROR with alternative truncate: ${deleteError.message}`);
          } else {
            fs.appendFileSync(logFile, `Alternative truncate successful\n`);
            console.log(`Alternative truncate successful`);
          }
        } else {
          fs.appendFileSync(logFile, `Table truncated successfully\n`);
          console.log(`Table truncated successfully`);
        }
      } catch (truncateError) {
        fs.appendFileSync(logFile, `ERROR during truncate: ${truncateError.message}\n`);
        console.error(`ERROR during truncate: ${truncateError.message}`);
      }
    }

    // Read all records
    fs.appendFileSync(logFile, `Reading all records from DBF file...\n`);
    console.log(`Reading all records from DBF file...`);
    const records = await dbf.readRecords();
    fs.appendFileSync(logFile, `Read ${records.length} records from DBF file.\n`);
    console.log(`Read ${records.length} records from DBF file.`);
    // Create an import run audit row (after reading DBF to know record count)
    await ensureImportRunsTableExists();
    const runMode = truncateBeforeImport ? 'truncate' : 'upsert';
    try {
      const { data: runRow, error: runInsertError } = await supabase
        .from('t_rpro_import_runs')
        .insert({
          mode: runMode,
          truncate_before_import: truncateBeforeImport,
          dbf_file_path: dbfFilePath,
          target_table: targetTable,
          total_records: records.length
        })
        .select()
        .single();
      if (runInsertError) {
        fs.appendFileSync(logFile, `WARN: could not insert import run row: ${runInsertError.message}\n`);
      } else {
        currentImportRunId = runRow?.id || null;
      }
    } catch (e) {
      fs.appendFileSync(logFile, `WARN: exception inserting import run row: ${e.message}\n`);
    }


    // Calculate number of batches
    const totalBatches = Math.ceil(records.length / batchSize);
    // Ensure change-log table exists when running in upsert-with-diff mode
    if (!truncateBeforeImport) {
      await ensureChangeTableExists();
    }
    fs.appendFileSync(logFile, `Total number of batches: ${totalBatches}\n`);
    console.log(`Total number of batches: ${totalBatches}`);
    // Update import run with total_batches
    if (currentImportRunId) {
      try {
        const { error: runUpdateError } = await supabase
          .from('t_rpro_import_runs')
          .update({ total_batches: totalBatches })
          .eq('id', currentImportRunId);
        if (runUpdateError) {
          fs.appendFileSync(logFile, `WARN: could not update import run total_batches: ${runUpdateError.message}\n`);
        }
      } catch (e) {
        fs.appendFileSync(logFile, `WARN: exception updating import run total_batches: ${e.message}\n`);
      }
    }


    // Process batches
    let totalImported = 0;
    const actualStartBatch = Math.max(0, Math.min(startBatch, totalBatches - 1));
    const actualEndBatch = Math.max(actualStartBatch, Math.min(endBatch, totalBatches - 1));

    fs.appendFileSync(logFile, `Processing batches ${actualStartBatch + 1} to ${actualEndBatch + 1}\n`);
    console.log(`Processing batches ${actualStartBatch + 1} to ${actualEndBatch + 1}`);

    for (let i = actualStartBatch; i <= actualEndBatch; i++) {
      const batchImported = await processBatch(records, i);
      totalImported += batchImported;

      // Add a small delay between batches to avoid overwhelming the server
      if (i < actualEndBatch) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    // After all batches, if not truncating, log records missing from this import
    if (!truncateBeforeImport) {
      await logMissingRecords();
    }

    fs.appendFileSync(logFile, `Import completed. Total records processed: ${totalImported}\n`);
    console.log(`Import completed. Total records processed: ${totalImported}`);

    // Mark import run as finished
    if (currentImportRunId) {
      try {
        const { error: runFinishError } = await supabase
          .from('t_rpro_import_runs')
          .update({ finished_at: new Date().toISOString() })
          .eq('id', currentImportRunId);
        if (runFinishError) {
          fs.appendFileSync(logFile, `WARN: could not set finished_at on import run: ${runFinishError.message}\n`);
        }
      } catch (e) {
        fs.appendFileSync(logFile, `WARN: exception setting finished_at on import run: ${e.message}\n`);
      }
    }

    return totalImported;
  } catch (error) {
    fs.appendFileSync(logFile, `ERROR importing DBF file: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`ERROR importing DBF file: ${error.message}`);
    console.error(error.stack);
    return 0;
  }
}

// Run the import
console.log(`Starting batch import of ${dbfFilePath} to ${targetTable}...`);
importDbf()
  .then(totalImported => {
    fs.appendFileSync(logFile, `Batch import process completed at ${new Date().toISOString()}\n`);
    console.log(`Batch import process completed. Total records imported: ${totalImported}`);
  })
  .catch(error => {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    console.error(`Unhandled error: ${error.message}`);
  });
