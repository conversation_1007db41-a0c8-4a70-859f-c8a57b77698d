import { createClient } from '@supabase/supabase-js';
import <PERSON> from 'papaparse';

const supabase = createClient(
    'https://aepabhlwpjfjulrjeitn.supabase.co',
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzc4NjU0MSwiZXhwIjoyMDQ5MzYyNTQxfQ.FQyPTgdBP83VhuykdlZkagHADc5nBmx7-yd0JYt5aP8'
);

async function importCsv() {
    console.log('⏳ Downloading CSV file from Supabase Storage...');
    
    // 🔹 Step 1: Download CSV from Storage
    const { data, error } = await supabase
        .storage
        .from('uploads')
        .download('from_informed_no_buy_box.csv');

    if (error) {
        console.error('❌ Error downloading CSV:', error);
        return;
    }

    console.log('✅ CSV downloaded successfully.');

    // 🔹 Step 2: Read CSV File as Text
    const csvText = await data.text();

    // 🔹 Step 3: Parse CSV Data with Advanced Options
    const { data: parsedData, errors } = Papa.parse(csvText, {
        header: true,       // Ensure first row is treated as headers
        skipEmptyLines: true, // Ignore empty rows
        dynamicTyping: true, // Convert numbers automatically
        delimiter: ",",      // Force delimiter to comma
    });

    // Handle parsing errors
    if (errors.length > 0) {
	console.warn("⚠️ CSV Parsing Warnings:", errors.slice(0, 5));
// Show only first 5 errors
    }

    console.log(`✅ CSV parsed successfully. Found ${parsedData.length} records.`);

    // 🔹 Step 4: Insert Data into Supabase Table
    const { error: insertError } = await supabase
        .from('it_infor_no_buy_box')
        .insert(parsedData);

    if (insertError) {
        console.error('❌ Error inserting data into table:', insertError);
    } else {
        console.log('✅ CSV data imported successfully!');
    }
}

// Run the import function
importCsv();
