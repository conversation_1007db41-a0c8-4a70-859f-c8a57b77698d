// scripts/move_accessory_additional_images.js
// Purpose: Move additional images for the same AccessoryID from the local archive
//          into the destination folder, naming them AccessoryIDa.jpg, AccessoryIDb.jpg, etc.
// Behavior:
//  - The first image for an ID is considered the primary (AccessoryID.jpg). If it already exists, it is not rewritten.
//  - Each additional image for the same ID is written using the next available letter suffix.
//  - When --from-archive is used, reads files from the archive path built from FileName, and on --delete-source removes the archive file after successful copy.

import fs from 'fs';
import fsp from 'fs/promises';
import path from 'path';

const DATA_FILE = path.join('data', 'external data', 'access', 'Shopify Accessory Images.txt');
const TARGET_BASE = 'https://d3f34rkxix3zin.cloudfront.net/shopify/dgaccessories/magentoproduct/';
const TARGET_UNC_DIR = '\\NANCY\\nancyv8\\Images\\DG Accessory Products\\Uploaded to AWS';
const SOURCE_ARCHIVE_ROOT = '\\NANCY\\nancyv8\\Images\\DG Accessory Products\\3 Magento Image Archive';

const argv = process.argv.slice(2);
const DO_RUN = argv.includes('--run');
const FROM_ARCHIVE = argv.includes('--from-archive');
const DELETE_SOURCE = argv.includes('--delete-source');

function log(...args) { console.log('[move_additional_images]', ...args); }
function err(...args) { console.error('[move_additional_images]', ...args); }

function stripQuotes(s) {
  if (s == null) return '';
  if (s.length >= 2 && s.startsWith('"') && s.endsWith('"')) return s.substring(1, s.length - 1);
  return s;
}

function parseTSVLine(line) {
  return line.split('\t').map(stripQuotes);
}

async function ensureDir(dir) {
  await fsp.mkdir(dir, { recursive: true });
}

async function readAllLines(filePath) {
  const raw = await fsp.readFile(filePath, 'utf8');
  return raw.replace(/\r\n/g, '\n').replace(/\r/g, '\n').split('\n');
}

function suffixFromIndex(i) {
  if (i === 0) return '';
  let n = i; // 1-based for suffixes
  let s = '';
  while (n > 0) {
    n--; // make 0-based
    s = String.fromCharCode(97 + (n % 26)) + s;
    n = Math.floor(n / 26);
  }
  return s;
}

function realUNC(p) {
  return p.startsWith('\\\\') ? p : ('\\\\' + p.replace(/^\\+/, ''));
}

(async function main() {
  log(`Reading data file: ${DATA_FILE}`);
  const lines = await readAllLines(DATA_FILE);
  if (lines.length <= 1) throw new Error('No data rows');

  const header = parseTSVLine(lines[0]);
  const idx = Object.fromEntries(header.map((name, i) => [name.toLowerCase(), i]));
  const rows = [];
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i];
    if (!line || !line.trim()) continue;
    const parts = parseTSVLine(line);
    if (parts.length < header.length) continue;
    rows.push({
      FileName: parts[idx['filename']],
      URL: parts[idx['url']],
      AccessoryID: parts[idx['accessoryid']]
    });
  }

  const candidates = rows.filter(r => r.AccessoryID && r.FileName && r.URL === TARGET_BASE);
  log(`Candidate rows: ${candidates.length}`);

  // Group by AccessoryID
  const byId = new Map();
  for (const c of candidates) {
    const k = String(c.AccessoryID).trim();
    if (!byId.has(k)) byId.set(k, []);
    byId.get(k).push(c);
  }

  await ensureDir(TARGET_UNC_DIR);

  let written = 0, skipped = 0, failures = 0, groupsWithExtras = 0;
  const failureRows = [];

  for (const [id, arr] of byId.entries()) {
    if (arr.length <= 1) continue; // no additional images
    groupsWithExtras++;

    // Primary: id.jpg (only write if it doesn't already exist)
    const primaryName = `${id}.jpg`;
    const primaryPath = realUNC(TARGET_UNC_DIR + '\\' + primaryName);

    // Process rows in order; j=0 is primary candidate, j>=1 are additional images
    for (let j = 0; j < arr.length; j++) {
      const row = arr[j];
      try {
        let outName;
        if (j === 0) {
          outName = primaryName;
          try {
            await fsp.access(primaryPath, fs.constants.F_OK);
            // primary exists -> skip writing this one (do not delete source)
            skipped++;
            continue;
          } catch {}
        } else {
          // find next available letter suffix starting at 'a' (index 1)
          let idx = 1;
          while (true) {
            const suffix = suffixFromIndex(idx);
            const candidate = `${id}${suffix}.jpg`;
            const testPath = realUNC(TARGET_UNC_DIR + '\\' + candidate);
            try {
              await fsp.access(testPath, fs.constants.F_OK);
              idx++;
              continue; // exists -> try next
            } catch {
              outName = candidate;
              break;
            }
          }
        }

        // Build source path (archive)
        if (!FROM_ARCHIVE) {
          skipped++;
          continue;
        }
        const srcPath = SOURCE_ARCHIVE_ROOT + '\\' + row.FileName.replace(/\//g, '\\');
        const realSrc = realUNC(srcPath);
        let buf;
        try {
          buf = await fsp.readFile(realSrc);
        } catch (e) {
          failures++;
          failureRows.push({ AccessoryID: id, FileName: row.FileName, URL: row.URL, error: `Source missing or unreadable: ${realSrc} (${e.message})` });
          continue;
        }

        const destPath = realUNC(TARGET_UNC_DIR + '\\' + outName);
        await fsp.writeFile(destPath, buf);
        if (DELETE_SOURCE) {
          try { await fsp.unlink(realSrc); } catch {}
        }
        written++;
      } catch (e) {
        failures++;
        failureRows.push({ AccessoryID: id, FileName: row.FileName, URL: row.URL, error: e.message });
      }
    }
  }

  if (failures) {
    await ensureDir('logs');
    const csvHeader = 'AccessoryID,FileName,URL,Error\n';
    const csvBody = failureRows.map(f => `${f.AccessoryID},"${f.FileName}",${f.URL},"${f.error.replace(/\"/g, "'")}"`).join('\n');
    await fsp.writeFile(path.join('logs', 'move_accessory_additional_images_failures.csv'), csvHeader + csvBody, 'utf8');
  }

  log(`Done. GroupsWithExtras=${groupsWithExtras}, written=${written}, skipped=${skipped}, failures=${failures}`);

  if (!DO_RUN) {
    log('Dry-run complete. Re-run with --run --from-archive [--delete-source] to execute.');
  }
})().catch(e => { err('Fatal error:', e.message); process.exit(1); });

