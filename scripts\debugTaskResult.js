import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function main() {
  try {
    const idArg = process.argv[2];
    if (!idArg) {
      console.error('Usage: node scripts/debugTaskResult.js <task_id>');
      process.exit(1);
    }
    const taskId = parseInt(idArg, 10);
    const { data, error } = await supabase
      .from('t_task_queue')
      .select('id, task_type, status, result, processed_at, created_at')
      .eq('id', taskId)
      .single();
    if (error) {
      console.error('Error fetching task:', error);
      process.exit(2);
    }
    console.log(JSON.stringify(data, null, 2));
  } catch (e) {
    console.error('Exception:', e);
    process.exit(3);
  }
}

main();

