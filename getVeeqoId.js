import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

/**
 * Get Veeqo IDs by SKU using local database table instead of Veeqo API
 * @param {string} sku - SKU of the item
 * @returns {Promise<number[]>} - Array of Veeqo product IDs or empty array if not found
 */
async function getVeeqoId(sku) {
    console.log(`🔄 getVeeqoId called for sku: ${sku}`);

    // Initialize Supabase client
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_KEY;

    if (!supabaseUrl || !supabaseKey) {
        console.error('❌ SUPABASE_URL or SUPABASE_KEY environment variables are not set');
        return [];
    }

    const supabase = createClient(supabaseUrl, supabaseKey);

    try {
        console.log(`🔍 Looking up SKU ${sku} in imported_table_veeqo_sellables_export`);

        // Query the imported table for the SKU - get ALL matching records
        const { data, error } = await supabase
            .from('imported_table_veeqo_sellables_export')
            .select('product_id')
            .eq('sku_code', sku);

        if (error) {
            console.error('❌ Error querying Supabase:', error.message);
            return [];
        }

        if (!data || data.length === 0) {
            console.log(`❌ No product found for SKU ${sku} in imported table`);

            // Try with exact match in case there are formatting differences
            const { data: exactData, error: exactError } = await supabase
                .from('imported_table_veeqo_sellables_export')
                .select('product_id')
                .eq('sku_code', sku.trim());

            if (exactError || !exactData || exactData.length === 0) {
                console.log(`❌ No exact match found for SKU ${sku} in imported table`);
                return [];
            }

            const veeqoIds = exactData.map(item => item.product_id);
            console.log(`✅ Found ${veeqoIds.length} exact matches for SKU ${sku}: Product IDs ${veeqoIds.join(', ')}`);
            return veeqoIds;
        }

        const veeqoIds = data.map(item => item.product_id);
        console.log(`✅ Found ${veeqoIds.length} matches for SKU ${sku}: Product IDs ${veeqoIds.join(', ')}`);
        return veeqoIds;

    } catch (error) {
        console.error('Error looking up Veeqo ID:', error);
        return [];
    }
}

export default getVeeqoId;
