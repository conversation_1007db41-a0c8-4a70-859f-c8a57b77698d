import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM3ODY1NDEsImV4cCI6MjA0OTM2MjU0MX0.MtBXMZt7rCqXd6c9clhrNoVtfOxEilX2C8oboMceOGg';
const supabase = createClient(supabaseUrl, supabaseKey);

const testFindMatchingOsl = async () => {
  try {
    console.log('Testing find_matching_osl function with different weight formats...');

    // Get disc record
    const { data: discRecord, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, color_id')
      .eq('id', 421349)
      .single();

    if (discError) {
      console.error('Error fetching disc record:', discError);
      return;
    }

    console.log('Disc record:', discRecord);

    // Test with different weight formats
    const testWeights = [
      { format: 'Original', value: discRecord.weight },
      { format: 'String', value: discRecord.weight.toString() },
      { format: 'Integer', value: Math.round(discRecord.weight) },
      { format: 'Float', value: parseFloat(discRecord.weight) }
    ];

    for (const test of testWeights) {
      console.log(`\nTesting with ${test.format} weight: ${test.value} (type: ${typeof test.value})`);

      try {
        const { data, error } = await supabase.rpc(
          'find_matching_osl',
          {
            mps_id_param: discRecord.mps_id,
            color_id_param: discRecord.color_id,
            weight_param: test.value
          }
        );

        if (error) {
          console.error(`Error with ${test.format} weight:`, error);
        } else {
          console.log(`Result with ${test.format} weight:`, data);
          if (data && data.length > 0) {
            console.log(`Debug info: ${data[0].debug_info}`);
            console.log(`Matched OSL ID: ${data[0].osl_id}`);
          } else {
            console.log('No results returned');
          }
        }
      } catch (e) {
        console.error(`Exception with ${test.format} weight:`, e.message);
      }
    }

    // Test with specific OSL ID
    console.log('\nChecking if OSL 16890 would match with this disc...');
    const { data: oslRecord, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, min_weight, max_weight, color_id')
      .eq('id', 16890)
      .single();

    if (oslError) {
      console.error('Error fetching OSL record:', oslError);
      return;
    }

    console.log('OSL record:', oslRecord);

    // Check if they should match
    const mpsMatch = discRecord.mps_id === oslRecord.mps_id;
    const roundedWeight = Math.round(discRecord.weight);
    const weightMatch = roundedWeight >= oslRecord.min_weight && roundedWeight <= oslRecord.max_weight;
    const colorMatch = oslRecord.color_id === 23 || discRecord.color_id === oslRecord.color_id;

    console.log(`MPS Match: ${mpsMatch} (Disc MPS: ${discRecord.mps_id}, OSL MPS: ${oslRecord.mps_id})`);
    console.log(`Weight Match: ${weightMatch} (Disc Weight: ${discRecord.weight}, Rounded: ${roundedWeight}, OSL Min: ${oslRecord.min_weight}, OSL Max: ${oslRecord.max_weight})`);
    console.log(`Color Match: ${colorMatch} (Disc Color: ${discRecord.color_id}, OSL Color: ${oslRecord.color_id})`);

    const shouldMatch = mpsMatch && weightMatch && colorMatch;
    console.log(`Should Match: ${shouldMatch}`);

  } catch (error) {
    console.error('Unexpected error:', error);
  }
};

testFindMatchingOsl();
