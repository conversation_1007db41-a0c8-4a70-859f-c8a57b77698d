// importAccessVouchers.js - Import Access vouchers TSVs into Supabase
// Source files:
//   data/external data/access/tAccVouchers.txt
//   data/external data/access/tAccVoucherLines.txt
//
// Mappings:
// tAccVouchers -> t_inventory_vouchers
//   ID -> id (PK)
//   VoucherDate -> voucher_date (TIMESTAMPTZ)
//   Shipment_ID -> shipment_id (INTEGER/BIGINT)
//   Note -> notes (TEXT)
//
// tAccVoucherLines -> t_inventory_voucher_lines
//   ID -> id (PK)
//   Voucher_ID -> voucher_id (FK to t_inventory_vouchers.id)
//   Accessory_ID -> product_variant_id (FK to t_product_variants.id)
//   Qty -> quantity (INTEGER)
//   Cost -> unit_cost (NUMERIC)
//   ExtendedCost -> ignored
//   Note -> notes (TEXT)
//   DropShipped -> dropshipped (BOOLEAN)
//   HowManyLabels -> ignored
//   PrintPrice -> ignored

import fs from 'fs';
import path from 'path';
import <PERSON> from 'papaparse';

const VOUCHERS_PATH = path.join(process.cwd(), 'data', 'external data', 'access', 'tAccVouchers.txt');
const LINES_PATH = path.join(process.cwd(), 'data', 'external data', 'access', 'tAccVoucherLines.txt');

const CHUNK_SIZE = 1000;

function toInt(v) {
  if (v === undefined || v === null || v === '') return null;
  const s = String(v).replace(/[\,\s]/g, '').trim();
  const n = Number(s);
  return Number.isFinite(n) ? Math.trunc(n) : null;
}

function toNum(v) {
  if (v === undefined || v === null || v === '') return null;
  const s = String(v).replace(/[$,\s]/g, '').trim();
  const n = Number(s);
  return Number.isFinite(n) ? n : null;
}

function toBool(v) {
  if (v === undefined || v === null || v === '') return null;
  const s = String(v).trim().toLowerCase();
  if (s === 'y' || s === 'yes' || s === 'true' || s === '1') return true;
  if (s === 'n' || s === 'no' || s === 'false' || s === '0') return false;
  return null;
}

function parseDate(v) {
  if (!v) return null;
  const d = new Date(v);
  return isNaN(d.getTime()) ? null : d;
}

function cleanTsvContent(raw) {
  const lines = raw.split(/\r?\n/);
  if (lines.length === 0) return raw;
  const header = lines[0];
  const out = [header];
  let acc = '';
  const isBalanced = (s) => {
    let inQuote = false;
    for (let i = 0; i < s.length; i++) {
      if (s[i] === '"') {
        if (s[i + 1] === '"') { i++; continue; }
        inQuote = !inQuote;
      }
    }
    return !inQuote;
  };
  for (let i = 1; i < lines.length; i++) {
    if (acc === '') acc = lines[i]; else acc += '\n' + lines[i];
    if (isBalanced(acc)) { out.push(acc); acc = ''; }
  }
  if (acc) out.push(acc);
  return out.join('\n');
}

function parseTsv(filePath) {
  const raw = fs.readFileSync(filePath, 'utf-8');
  const cleaned = cleanTsvContent(raw);
  const { data, errors } = Papa.parse(cleaned, {
    header: true,
    skipEmptyLines: true,
    delimiter: '\t',
    dynamicTyping: false,
  });
  if (errors && errors.length) {
    console.warn(`[importAccessVouchers] TSV parse warnings in ${path.basename(filePath)} (first 5):`, errors.slice(0, 5));
  }
  return data || [];
}

function chunk(arr, size) {
  const out = [];
  for (let i = 0; i < arr.length; i += size) out.push(arr.slice(i, i + size));
  return out;
}

async function logError(supabase, msg, ctx) {
  try {
    await supabase.from('t_error_logs').insert({ error_message: msg, context: ctx });
  } catch (e) {
    console.error('[importAccessVouchers] Failed to log error:', e?.message || e);
  }
}

async function ensureTablesAvailable(supabase) {
  try {
    await supabase.from('t_inventory_vouchers').select('id').limit(1);
    await supabase.from('t_inventory_voucher_lines').select('id').limit(1);
  } catch (e) {
    throw new Error('Required tables t_inventory_vouchers/t_inventory_voucher_lines not available.');
  }
}

function mapVoucherRow(r) {
  return {
    id: toInt(r['ID'] ?? r['Id'] ?? r['id']),
    voucher_date: parseDate(r['VoucherDate'] ?? r['voucher_date'] ?? r['Voucher Date']),
    shipment_id: toInt(r['Shipment_ID'] ?? r['Shipment Id'] ?? r['shipment_id']),
    notes: (r['Note'] ?? r['Notes'] ?? null) || null,
    created_by: 'Migration',
  };
}

function mapVoucherLineRow(r) {
  return {
    id: toInt(r['ID'] ?? r['Id'] ?? r['id']),
    voucher_id: toInt(r['Voucher_ID'] ?? r['Voucher Id'] ?? r['voucher_id']),
    product_variant_id: toInt(r['Accessory_ID'] ?? r['Accessory Id'] ?? r['accessory_id']),
    quantity: toInt(r['Qty'] ?? r['Quantity'] ?? r['qty']),
    unit_cost: toNum(r['Cost'] ?? r['UnitCost'] ?? r['unit_cost']),
    notes: (r['Note'] ?? r['Notes'] ?? null) || null,
    dropshipped: toBool(r['DropShipped'] ?? r['Drop Shipped'] ?? r['dropshipped']),
  };
}

export async function importAccessVouchers(supabase, opts = {}) {
  const truncate = !!opts.truncate;
  const validateOnly = !!opts.validateOnly;
  console.log('[importAccessVouchers] Starting import. truncate =', truncate, ' validateOnly =', validateOnly);

  if (!fs.existsSync(VOUCHERS_PATH)) throw new Error(`Missing file: ${VOUCHERS_PATH}`);
  if (!fs.existsSync(LINES_PATH)) throw new Error(`Missing file: ${LINES_PATH}`);

  await ensureTablesAvailable(supabase);

  // Read files
  const vouchersRaw = parseTsv(VOUCHERS_PATH);
  const linesRaw = parseTsv(LINES_PATH);
  console.log(`[importAccessVouchers] Parsed ${vouchersRaw.length} vouchers and ${linesRaw.length} lines.`);

  // Map and validate
  const voucherRecords = [];
  const lineRecords = [];
  const errors = [];

  const seenVoucherIds = new Set();
  for (const r of vouchersRaw) {
    const rec = mapVoucherRow(r);
    const ctx = { file: 'tAccVouchers', row: r };
    if (rec.id == null) { errors.push({ msg: 'voucher_missing_id', ctx }); continue; }
    if (seenVoucherIds.has(rec.id)) { errors.push({ msg: 'voucher_duplicate_id', ctx: { file: 'tAccVouchers', id: rec.id } }); continue; }
    seenVoucherIds.add(rec.id);
    if (rec.voucher_date == null) { errors.push({ msg: 'voucher_bad_date', ctx: { id: rec.id, VoucherDate: r['VoucherDate'] } }); }
    if (r['Shipment_ID'] != null && rec.shipment_id == null) { errors.push({ msg: 'voucher_bad_shipment_id', ctx: { id: rec.id, Shipment_ID: r['Shipment_ID'] } }); }
    voucherRecords.push(rec);
  }

  const seenLineIds = new Set();
  const referencedVoucherIds = new Set();
  const referencedVariantIds = new Set();
  for (const r of linesRaw) {
    const rec = mapVoucherLineRow(r);
    const ctx = { file: 'tAccVoucherLines', row: r };
    if (rec.id == null) { errors.push({ msg: 'line_missing_id', ctx }); continue; }
    if (seenLineIds.has(rec.id)) { errors.push({ msg: 'line_duplicate_id', ctx: { file: 'tAccVoucherLines', id: rec.id } }); continue; }
    seenLineIds.add(rec.id);
    if (rec.voucher_id == null) { errors.push({ msg: 'line_missing_voucher_id', ctx: { id: rec.id } }); }
    if (rec.product_variant_id == null) { errors.push({ msg: 'line_missing_product_variant_id', ctx: { id: rec.id } }); }
    if (rec.quantity == null) { errors.push({ msg: 'line_bad_quantity', ctx: { id: rec.id, Qty: r['Qty'] } }); }
    if (r['Cost'] != null && rec.unit_cost == null) { errors.push({ msg: 'line_bad_unit_cost', ctx: { id: rec.id, Cost: r['Cost'] } }); }
    if (rec.unit_cost != null && rec.unit_cost < 0) { errors.push({ msg: 'line_bad_unit_cost_negative', ctx: { id: rec.id, unit_cost: rec.unit_cost } }); }
    if (r['DropShipped'] != null && rec.dropshipped == null) { errors.push({ msg: 'line_bad_dropshipped', ctx: { id: rec.id, DropShipped: r['DropShipped'] } }); }
    if (rec.voucher_id != null) referencedVoucherIds.add(rec.voucher_id);
    if (rec.product_variant_id != null) referencedVariantIds.add(rec.product_variant_id);
    lineRecords.push(rec);
  }

  // Referential checks: voucher lines must reference vouchers present in the file
  for (const vid of referencedVoucherIds) {
    if (!seenVoucherIds.has(vid)) {
      errors.push({ msg: 'line_references_missing_voucher', ctx: { voucher_id: vid } });
    }
  }

  // Check product variants existence in DB (in batches)
  const variantIdList = Array.from(referencedVariantIds);
  const presentVariantIds = new Set();
  for (let i = 0; i < variantIdList.length; i += 1000) {
    const batch = variantIdList.slice(i, i + 1000);
    const { data, error } = await supabase.from('t_product_variants').select('id').in('id', batch);
    if (error) {
      throw new Error(`Failed to check t_product_variants: ${error.message}`);
    }
    for (const row of data || []) presentVariantIds.add(row.id);
  }
  for (const id of variantIdList) {
    if (!presentVariantIds.has(id)) errors.push({ msg: 'line_references_missing_variant', ctx: { product_variant_id: id } });
  }

  // If any errors, log and abort
  if (errors.length) {
    console.warn(`[importAccessVouchers] Validation found ${errors.length} issue(s). Logging to t_error_logs and aborting.`);
    for (const e of errors) await logError(supabase, `access_vouchers_${e.msg}`, e.ctx);
    return { success: false, errorCount: errors.length, message: 'Validation errors found. Fix data and retry.' };
  }

  // If validate-only, stop here after validation success
  if (validateOnly) {
    console.log('[importAccessVouchers] Validate-only mode: no DB writes.');
    return { success: true, vouchers: voucherRecords.length, lines: lineRecords.length, validateOnly: true };
  }

  // Truncate if requested
  if (truncate) {
    console.log('[importAccessVouchers] Clearing voucher movements and truncating voucher tables...');
    // First, clear voucher-related inventory movements
    try {
      const delMovementsSql = `DELETE FROM public.t_inventory_movements WHERE movement_type = 'voucher';`;
      let { error: delMovErr } = await supabase.rpc('exec_sql', { sql_query: delMovementsSql });
      if (delMovErr) { ({ error: delMovErr } = await supabase.rpc('exec_sql', { sql_statement: delMovementsSql })); }
      if (delMovErr) throw delMovErr;
    } catch (e) {
      console.warn('[importAccessVouchers] exec_sql delete movements failed, falling back to filtered DELETE:', e?.message || e);
      const { error: delMovFallbackErr } = await supabase.from('t_inventory_movements').delete().eq('movement_type', 'voucher');
      if (delMovFallbackErr) throw new Error(`Failed to clear voucher movements: ${delMovFallbackErr.message}`);
    }

    // Then, truncate voucher lines and vouchers
    try {
      const sql = `TRUNCATE TABLE public.t_inventory_voucher_lines, public.t_inventory_vouchers RESTART IDENTITY CASCADE;`;
      let { error } = await supabase.rpc('exec_sql', { sql_query: sql });
      if (error) { ({ error } = await supabase.rpc('exec_sql', { sql_statement: sql })); }
      if (error) throw error;
    } catch (e) {
      console.warn('[importAccessVouchers] exec_sql truncate failed, falling back to DELETEs:', e?.message || e);
      const { error: delLinesErr } = await supabase.from('t_inventory_voucher_lines').delete().neq('id', null);
      if (delLinesErr) throw new Error(`Failed to clear t_inventory_voucher_lines: ${delLinesErr.message}`);
      const { error: delVouchersErr } = await supabase.from('t_inventory_vouchers').delete().neq('id', null);
      if (delVouchersErr) throw new Error(`Failed to clear t_inventory_vouchers: ${delVouchersErr.message}`);
    }
  }

  // Insert vouchers
  console.log(`[importAccessVouchers] Inserting ${voucherRecords.length} vouchers...`);
  for (const part of chunk(voucherRecords, CHUNK_SIZE)) {
    const { error } = await supabase.from('t_inventory_vouchers').insert(part);
    if (error) throw new Error(`Failed inserting vouchers chunk: ${error.message}`);
  }

  // Insert lines
  console.log(`[importAccessVouchers] Inserting ${lineRecords.length} voucher lines...`);
  for (const part of chunk(lineRecords, CHUNK_SIZE)) {
    const { error } = await supabase.from('t_inventory_voucher_lines').insert(part);
    if (error) throw new Error(`Failed inserting voucher lines chunk: ${error.message}`);
  }

  // After successful inserts, set all imported vouchers to Complete
  try {
    console.log('[importAccessVouchers] Marking imported vouchers as Complete...');
    const importedIds = voucherRecords.map(v => v.id);
    for (const idChunk of chunk(importedIds, CHUNK_SIZE)) {
      const { error: updErr } = await supabase
        .from('t_inventory_vouchers')
        .update({ status: 'Complete' })
        .in('id', idChunk);
      if (updErr) throw updErr;
    }
  } catch (e) {
    throw new Error(`[importAccessVouchers] Failed to mark vouchers Complete: ${e.message}`);
  }

  // Reset ID sequences to MAX(id)+1 for voucher tables
  try {
    console.log('[importAccessVouchers] Resetting ID sequences for voucher tables...');
    const seqSqls = [
      "SELECT setval( pg_get_serial_sequence('t_inventory_vouchers', 'id'), COALESCE((SELECT MAX(id) FROM t_inventory_vouchers), 0) + 1, false );",
      "SELECT setval( pg_get_serial_sequence('t_inventory_voucher_lines', 'id'), COALESCE((SELECT MAX(id) FROM t_inventory_voucher_lines), 0) + 1, false );",
    ];
    for (const sql of seqSqls) {
      let { error } = await supabase.rpc('exec_sql', { sql_query: sql });
      if (error) { ({ error } = await supabase.rpc('exec_sql', { sql_statement: sql })); }
      if (error) throw error;
    }
  } catch (e) {
    throw new Error(`[importAccessVouchers] Failed to reset sequences: ${e.message}`);
  }

  console.log('[importAccessVouchers] Import complete.');
  return { success: true, vouchers: voucherRecords.length, lines: lineRecords.length };
}

export default importAccessVouchers;

// CLI usage: node importAccessVouchers.js [--truncate] [--validate]
if (process.argv[1] && /importAccessVouchers\.js$/i.test(process.argv[1])) {
  (async () => {
    const dotenvMod = await import('dotenv');
    const dotenv = dotenvMod?.default || dotenvMod;
    dotenv.config();
    const { createClient } = await import('@supabase/supabase-js');

    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_KEY;
    if (!supabaseUrl || !supabaseKey) {
      console.error('[importAccessVouchers] Missing SUPABASE_URL or SUPABASE_KEY');
      process.exit(1);
    }
    const supabase = createClient(supabaseUrl, supabaseKey);

    const truncate = process.argv.includes('--truncate');
    const validateOnly = process.argv.includes('--validate') || process.argv.includes('--dry-run');
    const result = await importAccessVouchers(supabase, { truncate, validateOnly });
    console.log(result);
    process.exit(result?.success ? 0 : 1);
  })().catch((e) => {
    console.error('[importAccessVouchers] Fatal:', e?.message || e);
    process.exit(1);
  });
}

