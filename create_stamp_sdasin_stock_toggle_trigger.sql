-- Enqueue tasks when a stamp's is_sdasin_stock toggles
-- For all related discs (via t_mps.stamp_id) enqueue match_disc_to_asins
-- For all related SDASINs (via t_sdasins.mps_id or mps_id2) enqueue sdasin_updated_find_discs_to_match

CREATE OR REPLACE FUNCTION fn_enqueue_on_stamp_stock_toggle()
RETURNS TRIGGER AS $$
BEGIN
  -- Only proceed if the value actually changed (true->false or false->true or NULL transitions)
  IF NOT (OLD.is_sdasin_stock IS DISTINCT FROM NEW.is_sdasin_stock) THEN
    RETURN NEW;
  END IF;

  -- CTE of affected MPS IDs
  WITH mps AS (
    SELECT id FROM t_mps WHERE stamp_id = NEW.id
  ),
  affected_discs AS (
    SELECT d.id AS disc_id
    FROM t_discs d
    JOIN mps ON d.mps_id = mps.id
  ),
  affected_sdasins AS (
    SELECT s.id AS sdasin_id
    FROM t_sdasins s
    JOIN mps ON (s.mps_id = mps.id OR s.mps_id2 = mps.id)
  )
  -- Enqueue disc->ASIN match tasks (avoid duplicate pending/processing tasks per disc)
  INSERT INTO t_task_queue (task_type, payload, status, scheduled_at, created_at, enqueued_by)
  SELECT 'match_disc_to_asins', jsonb_build_object('id', ad.disc_id), 'pending', NOW(), NOW(), 'stamp_stock_toggle_' || NEW.id
  FROM affected_discs ad
  WHERE NOT EXISTS (
    SELECT 1 FROM t_task_queue tq
    WHERE tq.task_type = 'match_disc_to_asins'
      AND (tq.status = 'pending' OR tq.status = 'processing')
      AND (tq.payload->>'id')::INT = ad.disc_id
  );

  -- Enqueue SDASIN->discs match tasks
  INSERT INTO t_task_queue (task_type, payload, status, scheduled_at, created_at, enqueued_by)
  SELECT 'sdasin_updated_find_discs_to_match', jsonb_build_object('id', asn.sdasin_id), 'pending', NOW(), NOW(), 'stamp_stock_toggle_' || NEW.id
  FROM affected_sdasins asn
  WHERE NOT EXISTS (
    SELECT 1 FROM t_task_queue tq
    WHERE tq.task_type = 'sdasin_updated_find_discs_to_match'
      AND (tq.status = 'pending' OR tq.status = 'processing')
      AND (tq.payload->>'id')::INT = asn.sdasin_id
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS tr_enqueue_on_stamp_stock_toggle ON t_stamps;
CREATE TRIGGER tr_enqueue_on_stamp_stock_toggle
AFTER UPDATE OF is_sdasin_stock ON t_stamps
FOR EACH ROW
WHEN (OLD.is_sdasin_stock IS DISTINCT FROM NEW.is_sdasin_stock)
EXECUTE FUNCTION fn_enqueue_on_stamp_stock_toggle();

-- Confirmation
DO $$
BEGIN
  RAISE NOTICE 'Stamp is_sdasin_stock toggle enqueuer created.';
END $$;

