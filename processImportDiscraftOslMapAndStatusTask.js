// processImportDiscraftOslMapAndStatusTask.js - Process import_discraft_osl_map_and_status tasks
import { google } from 'googleapis';
import dotenv from 'dotenv';
import ExcelJS from 'exceljs';
import fs from 'fs';
import path from 'path';
import nodemailer from 'nodemailer';

// Load environment variables
dotenv.config();

/**
 * Initialize Google Sheets API client with authentication
 * @returns {Object} - Google Sheets API client
 */
async function initializeGoogleSheetsAPI() {
    let auth;

    // Try different authentication methods
    if (process.env.GOOGLE_SERVICE_ACCOUNT_KEY) {
        // Method 1: Service Account Key from environment variable (JSON string)
        console.log('[processImportDiscraftOslMapAndStatusTask] Using service account key from environment variable');
        try {
            const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_KEY);
            auth = new google.auth.GoogleAuth({
                credentials: credentials,
                scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly']
            });
        } catch (error) {
            throw new Error(`Invalid GOOGLE_SERVICE_ACCOUNT_KEY format: ${error.message}`);
        }
    } else if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        // Method 2: Service Account Key file path
        console.log('[processImportDiscraftOslMapAndStatusTask] Using service account key file from GOOGLE_APPLICATION_CREDENTIALS');
        auth = new google.auth.GoogleAuth({
            keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS,
            scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly']
        });
    } else if (process.env.GOOGLE_API_KEY) {
        // Method 3: API Key (for public sheets only, but simpler setup)
        console.log('[processImportDiscraftOslMapAndStatusTask] Using API key authentication');
        auth = process.env.GOOGLE_API_KEY;
    } else {
        throw new Error(`Google Sheets authentication not configured.

Quick setup (choose one):

🔑 API Key (2 minutes):
   1. Get API key from Google Cloud Console
   2. Add GOOGLE_API_KEY=your-key to .env file
   3. Make sheet viewable by anyone with link

🔒 Service Account (5 minutes, more secure):
   1. Create service account in Google Cloud Console
   2. Add GOOGLE_SERVICE_ACCOUNT_KEY={"type":"service_account",...} to .env file
   3. Share sheet with service account email

See QUICK_GOOGLE_SHEETS_SETUP.md for detailed steps.
Test your setup with: node setupGoogleSheetsAPI.js`);
    }

    return google.sheets({ version: 'v4', auth });
}

/**
 * Extract spreadsheet ID from Google Sheets URL
 * @param {string} sheetsUrl - The Google Sheets URL
 * @returns {string} - The spreadsheet ID
 */
function extractSpreadsheetId(sheetsUrl) {
    const match = sheetsUrl.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
    if (!match) {
        throw new Error('Invalid Google Sheets URL format');
    }
    return match[1];
}

/**
 * Fetch data from Google Sheets using the API
 * @param {string} sheetsUrl - The Google Sheets URL
 * @param {string} sheetName - The sheet name
 * @returns {Array} - Array of row arrays
 */
async function fetchGoogleSheetsData(sheetsUrl, sheetName) {
    console.log(`[processImportDiscraftOslMapAndStatusTask] Fetching ${sheetName} sheet data using Google Sheets API...`);

    const spreadsheetId = extractSpreadsheetId(sheetsUrl);
    const sheets = await initializeGoogleSheetsAPI();

    try {
        // Get the sheet data - use a large range to capture all data
        const response = await sheets.spreadsheets.values.get({
            spreadsheetId: spreadsheetId,
            range: `${sheetName}!A1:ZZ1000`, // Get all data from the sheet (up to column ZZ, row 1000)
            valueRenderOption: 'UNFORMATTED_VALUE',
            dateTimeRenderOption: 'FORMATTED_STRING'
        });

        const values = response.data.values || [];
        console.log(`[processImportDiscraftOslMapAndStatusTask] ${sheetName} sheet has ${values.length} rows`);

        return values;
    } catch (error) {
        console.error(`[processImportDiscraftOslMapAndStatusTask] Error fetching ${sheetName} sheet:`, error);

        if (error.code === 403) {
            throw new Error(`Access denied to Google Sheet.

If using API Key:
   • Make sure the sheet is viewable by anyone with the link
   • Go to Share > Change to anyone with the link > Viewer

If using Service Account:
   • Share the sheet with your service account email
   • Give it Viewer permissions
   • Service account email looks like: <EMAIL>

Also check:
   • Google Sheets API is enabled in Google Cloud Console
   • Your credentials are correct

Run 'node setupGoogleSheetsAPI.js' to test your setup.
Original error: ${error.message}`);
        } else if (error.code === 404) {
            throw new Error(`Sheet "${sheetName}" not found. Please check:
1. The sheet name is correct (case-sensitive)
2. The spreadsheet ID is correct
3. The sheet exists in the spreadsheet

Original error: ${error.message}`);
        } else {
            throw new Error(`Failed to fetch ${sheetName} sheet: ${error.message}`);
        }
    }
}

/**
 * Convert column number to letter (1=A, 2=B, etc.)
 * @param {number} colNum - Column number (1-based)
 * @returns {string} - Column letter
 */
function columnNumberToLetter(colNum) {
    let result = '';
    while (colNum > 0) {
        colNum--;
        result = String.fromCharCode(65 + (colNum % 26)) + result;
        colNum = Math.floor(colNum / 26);
    }
    return result;
}

/**
 * Find all cells that start with 'OS' and return their positions and values
 * @param {Array} data - 2D array of sheet data
 * @returns {Array} - Array of objects with row, col, and id
 */
function findOSValues(data) {
    const osValues = [];
    
    for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
        const row = data[rowIndex];
        for (let colIndex = 0; colIndex < row.length; colIndex++) {
            const cellValue = row[colIndex];
            if (cellValue && typeof cellValue === 'string' && cellValue.startsWith('OS')) {
                // Extract the numeric part after 'OS'
                const numericPart = cellValue.substring(2);
                if (numericPart && !isNaN(numericPart)) {
                    osValues.push({
                        row: (rowIndex + 1).toString(), // 1-based row number
                        col: columnNumberToLetter(colIndex + 1), // 1-based column letter
                        id: parseInt(numericPart), // Numeric part as integer
                        originalValue: cellValue
                    });
                }
            }
        }
    }
    
    return osValues;
}

/**
 * Get status values from the 'New' sheet based on row and column positions
 * @param {Array} newSheetData - 2D array of 'New' sheet data
 * @param {Array} osValues - Array of OS values with positions
 * @returns {Array} - Array of OS values with status added
 */
function getStatusValues(newSheetData, osValues) {
    return osValues.map(osValue => {
        const rowIndex = parseInt(osValue.row) - 1; // Convert to 0-based
        const colIndex = osValue.col.charCodeAt(0) - 65; // Convert letter to 0-based index
        
        let status = 'In Stock'; // Default status
        let cellValue = null;
        
        // Check if the position exists in the new sheet data
        if (rowIndex >= 0 && rowIndex < newSheetData.length) {
            const row = newSheetData[rowIndex];
            if (colIndex >= 0 && colIndex < row.length) {
                cellValue = row[colIndex];
                
                // Determine status based on cell value
                if (cellValue === 'N/A') {
                    status = 'Not Available';
                } else if (cellValue === 'out' || cellValue === 'sold out') {
                    status = 'Out of Stock';
                } else if (cellValue === null || cellValue === undefined || cellValue === '') {
                    status = 'In Stock';
                } else {
                    // For any other value, default to In Stock
                    status = 'In Stock';
                }
            }
        }
        
        return {
            ...osValue,
            status: status,
            cellValue: cellValue
        };
    });
}

/**
 * Calculate disc counts for OSL map records
 * @param {Object} supabase - Supabase client
 * @param {Array} oslIds - Array of OSL IDs to calculate counts for
 * @returns {Object} - Results of the calculation
 */
async function calculateDiscCounts(supabase, oslIds) {
    console.log(`[calculateDiscCounts] Calculating disc counts for ${oslIds.length} OSL records...`);

    let updatedRecords = 0;
    let totalInStock = 0;
    let totalSoldLast90 = 0;
    const errors = [];

    // Process in batches to avoid overwhelming the database
    const batchSize = 50;
    for (let i = 0; i < oslIds.length; i += batchSize) {
        const batch = oslIds.slice(i, i + batchSize);
        console.log(`[calculateDiscCounts] Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(oslIds.length / batchSize)} (${batch.length} records)`);

        try {
            // Calculate counts for this batch using a single query
            const { data: countData, error: countError } = await supabase.rpc('calculate_discraft_osl_disc_counts', {
                osl_ids: batch
            });

            if (countError) {
                // If the stored procedure doesn't exist, fall back to individual queries
                if (countError.message.includes('function') || countError.message.includes('schema cache')) {
                    console.log(`[calculateDiscCounts] Stored procedure not found, using individual queries for batch`);
                    const batchResults = await calculateDiscCountsIndividually(supabase, batch);
                    updatedRecords += batchResults.updatedRecords;
                    totalInStock += batchResults.totalInStock;
                    totalSoldLast90 += batchResults.totalSoldLast90;
                    if (batchResults.errors && batchResults.errors.length > 0) {
                        errors.push(...batchResults.errors);
                    }
                } else {
                    throw countError;
                }
            } else {
                // Process the results from the stored procedure
                for (const record of countData) {
                    updatedRecords++;
                    totalInStock += record.in_stock || 0;
                    totalSoldLast90 += record.sold_last_90 || 0;
                }
            }
        } catch (error) {
            console.error(`[calculateDiscCounts] Error processing batch: ${error.message}`);
            errors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${error.message}`);
        }
    }

    return {
        updatedRecords,
        totalInStock,
        totalSoldLast90,
        errors: errors.length > 0 ? errors : null
    };
}

/**
 * Calculate disc counts individually (fallback method)
 * @param {Object} supabase - Supabase client
 * @param {Array} oslIds - Array of OSL IDs to calculate counts for
 * @returns {Object} - Results of the calculation
 */
async function calculateDiscCountsIndividually(supabase, oslIds) {
    let updatedRecords = 0;
    let totalInStock = 0;
    let totalSoldLast90 = 0;
    const errors = [];

    for (const oslId of oslIds) {
        try {
            // Count in-stock discs (sold_date is null)
            const { count: inStockCount, error: inStockError } = await supabase
                .from('t_discs')
                .select('*', { count: 'exact', head: true })
                .eq('vendor_osl_id', oslId)
                .is('sold_date', null);

            if (inStockError) {
                throw new Error(`Error counting in-stock discs: ${inStockError.message}`);
            }

            // Get lookback days from config
            const { data: configData, error: configError } = await supabase
                .from('t_config')
                .select('value')
                .eq('key', 'discraft_disc_order_look_back_days')
                .single();

            const lookbackDays = configError || !configData ? 60 : parseInt(configData.value) || 60;

            // Count discs sold in last N days (configurable) excluding our 'Fixed' workflow channel
            const lookbackDate = new Date();
            lookbackDate.setDate(lookbackDate.getDate() - lookbackDays);

            const { count: soldLast90Count, error: soldError } = await supabase
                .from('t_discs')
                .select('*', { count: 'exact', head: true })
                .eq('vendor_osl_id', oslId)
                .gte('sold_date', lookbackDate.toISOString())
                .not('sold_channel', 'eq', 'Fixed');

            if (soldError) {
                throw new Error(`Error counting sold discs: ${soldError.message}`);
            }

            // Update the OSL map record
            const { error: updateError } = await supabase
                .from('it_discraft_osl_map')
                .update({
                    in_stock: inStockCount || 0,
                    sold_last_90: soldLast90Count || 0
                })
                .eq('id', oslId);

            if (updateError) {
                throw new Error(`Error updating OSL record: ${updateError.message}`);
            }

            updatedRecords++;
            totalInStock += inStockCount || 0;
            totalSoldLast90 += soldLast90Count || 0;

        } catch (error) {
            console.error(`[calculateDiscCountsIndividually] Error processing OSL ID ${oslId}: ${error.message}`);
            errors.push(`OSL ID ${oslId}: ${error.message}`);
        }
    }

    return {
        updatedRecords,
        totalInStock,
        totalSoldLast90,
        errors
    };
}

/**
 * Email the Discraft order sheet to Galen
 * @param {string} filePath - Path to the Excel file to email
 * @param {number} totalDiscs - Total number of discs added to the order sheet
 * @returns {Object} - Results of the email operation
 */
async function emailDiscraftOrderSheet(filePath, totalDiscs, summary = {}) {
    try {
        console.log(`[emailDiscraftOrderSheet] Preparing to email ${filePath} with ${totalDiscs} total discs`);

        // Create transporter using existing email configuration
        const transporter = nodemailer.createTransport({
            host: process.env.SMTP_HOST || 'smtp.gmail.com',
            port: process.env.SMTP_PORT || 587,
            secure: false,
            auth: {
                user: process.env.EMAIL_USER,
                pass: process.env.EMAIL_PASS
            }
        });

        // Get current date for subject
        const today = new Date();
        const dateStr = today.toLocaleDateString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });

        // Email configuration
        const mailOptions = {
            from: process.env.EMAIL_USER,
            to: '<EMAIL>',
            subject: `Discraft Order Sheet — qty:${summary.qtySum ?? totalDiscs} / cells:${summary.qtyCount ?? 'N/A'} / sold(${summary.lookbackDays ?? 'N/A'}d):${summary.totalSoldLast90 ?? 'N/A'} — ${dateStr}`,
            html: `
                <h2>🎯 Discraft Order Sheet Updated</h2>
                <p><strong>Date:</strong> ${dateStr}</p>
                <p><strong>Look back period:</strong> ${summary.lookbackDays ?? 'N/A'} days</p>
                <p><strong>Total Discs Added (sum of qty in Excel):</strong> ${totalDiscs}</p>
                <p><strong>Total discs sold during lookback (sum of it_discraft_osl_map.sold_last_90):</strong> ${summary.totalSoldLast90 ?? 'N/A'}</p>
                <p><strong>Count of .qty cells updated:</strong> ${summary.qtyCount ?? 'N/A'}</p>
                <p><strong>Sum of .qty values:</strong> ${summary.qtySum ?? totalDiscs}</p>
                <p><strong>Sum of .in_stock:</strong> ${summary.inStockSum ?? 'N/A'}</p>
                <p><strong>File:</strong> ${path.basename(filePath)}</p>

                <h3>Summary:</h3>
                <ul>
                    <li>✅ Google Sheets data imported and processed</li>
                    <li>✅ Disc inventory counts calculated</li>
                    <li>✅ Excel order form updated with current quantities</li>
                    <li>✅ Header information filled out with today's date</li>
                </ul>

                <p>The attached Excel file is ready for submission to Discraft.</p>

                <p><em>This email was generated automatically by the Discraft OSL import process.</em></p>
            `,
            attachments: [
                {
                    filename: path.basename(filePath),
                    path: filePath
                }
            ]
        };

        // Send the email
        const info = await transporter.sendMail(mailOptions);

        console.log(`[emailDiscraftOrderSheet] Email sent successfully: ${info.messageId}`);

        return {
            success: true,
            messageId: info.messageId,
            recipient: '<EMAIL>',
            subject: mailOptions.subject,
            totalDiscs: totalDiscs
        };

    } catch (error) {
        console.error(`[emailDiscraftOrderSheet] Error sending email: ${error.message}`);
        return {
            success: false,
            error: error.message,
            totalDiscs: totalDiscs
        };
    }
}

/**
 * Update the Discraft Excel file with qty values from it_discraft_osl_map
 * @param {Object} supabase - Supabase client
 * @returns {Object} - Results of the Excel update
 */
async function updateDiscraftExcelFile(supabase) {
    const excelFilePath = 'C:\\Users\\<USER>\\supabase_project\\data\\external data\\discraftstock.xlsx';

    try {
        // Check if the Excel file exists
        if (!fs.existsSync(excelFilePath)) {
            throw new Error(`Excel file not found: ${excelFilePath}`);
        }

        console.log(`[updateDiscraftExcelFile] Creating timestamped copy of template...`);

        // Build timestamped destination path and copy template first
        const now = new Date();
        const timestamp = now.toISOString()
            .replace(/:/g, '-')
            .replace(/\./g, '-')
            .replace('T', '_')
            .substring(0, 19); // Remove milliseconds and timezone
        const directory = path.dirname(excelFilePath);
        const filename = path.basename(excelFilePath, '.xlsx');
        const timestampedFilePath = path.join(directory, `${filename}_${timestamp}.xlsx`);

        fs.copyFileSync(excelFilePath, timestampedFilePath);
        console.log(`[updateDiscraftExcelFile] Working file: ${timestampedFilePath}`);

        // Load the Excel workbook FROM THE COPY
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(timestampedFilePath);

        // Get the first worksheet (assuming the data is in the first sheet)
        const worksheet = workbook.worksheets[0];
        if (!worksheet) {
            throw new Error('No worksheets found in the Excel file');
        }

        console.log(`[updateDiscraftExcelFile] Working with worksheet: ${worksheet.name}`);

        // Get all OSL map records in chunks to avoid the 1000-row cap
        const pageSize = 1000;
        const oslMapData = [];
        for (let from = 0; ; from += pageSize) {
            const to = from + pageSize - 1;
            const { data, error } = await supabase
                .from('it_discraft_osl_map')
                .select('id, row, col, qty')
                .order('id', { ascending: true })
                .range(from, to);
            if (error) {
                throw new Error(`Failed to fetch OSL map data (range ${from}-${to}): ${error.message}`);
            }
            if (!data || data.length === 0) break;
            oslMapData.push(...data);
            if (data.length < pageSize) break;
        }

        const nonZeroQty = oslMapData.filter(r => r.qty !== null && r.qty !== 0 && r.row && r.col);
        console.log(`[updateDiscraftExcelFile] Loaded ${oslMapData.length} OSL records; ${nonZeroQty.length} with non-zero qty and valid positions`);

        let updatedCells = 0;
        let totalDiscsAdded = 0;
        let nonZeroQtyCount = 0;
        const updateLog = [];

        // Fill out the header information first
        console.log(`[updateDiscraftExcelFile] Filling out header information...`);

        // Fill out today's date in D14 (order date)
        const today = new Date();
        const formattedDate = `${today.getMonth() + 1}/${today.getDate()}/${today.getFullYear()}`;
        const orderDateCell = worksheet.getCell('D14');
        orderDateCell.value = formattedDate;
        console.log(`[updateDiscraftExcelFile] Set order date (D14): ${formattedDate}`);

        // Fill out the complete header information with the exact cell references provided
        // Company Name (row 4)
        worksheet.getCell('C4').value = 'DZDiscs';

        // Billing Address (row 6)
        worksheet.getCell('C6').value = '811 E 23rd St Suite E';

        // City, State, Zip (row 7)
        worksheet.getCell('B7').value = 'Lawrence';
        worksheet.getCell('H7').value = 'KS';
        worksheet.getCell('L7').value = '66046';

        // Shipping Address (row 4)
        worksheet.getCell('Q4').value = 'Same';

        // Phone (row 9)
        worksheet.getCell('C9').value = '(*************';

        // Email (row 10)
        worksheet.getCell('D10').value = '<EMAIL>';

        // Contact Name (row 11)
        worksheet.getCell('D11').value = 'Galen Adams';

        // Updated date in top right (row 3)
        const formattedUpdateDate = `${String(today.getMonth() + 1).padStart(2, '0')}/${String(today.getDate()).padStart(2, '0')}/${String(today.getFullYear()).slice(-2)}`;
        worksheet.getCell('Z3').value = `updated: ${formattedUpdateDate}`;

        console.log(`[updateDiscraftExcelFile] Filled out complete header information with correct cell references`);

        // Update each cell in the Excel file (ONLY write non-zero qty)
        for (const record of nonZeroQty) {
            try {
                const rowNumber = parseInt(record.row);
                const columnLetter = record.col;
                const qtyValue = record.qty; // write only actual qtys being ordered

                // Convert column letter to column number (A=1, B=2, etc.)
                const columnNumber = columnLetter.split('').reduce((result, char) => {
                    return result * 26 + (char.charCodeAt(0) - 'A'.charCodeAt(0) + 1);
                }, 0);

                // Get the cell and update its value
                const cell = worksheet.getCell(rowNumber, columnNumber);
                const oldValue = Number(cell.value) || 0;
                cell.value = qtyValue;

                updatedCells++;
                totalDiscsAdded += qtyValue; // Sum only non-zero qty for email consistency
                nonZeroQtyCount += 1;
                updateLog.push({
                    oslId: record.id,
                    position: `${columnLetter}${rowNumber}`,
                    oldValue: oldValue,
                    newValue: qtyValue
                });

                console.log(`[updateDiscraftExcelFile] Updated cell ${columnLetter}${rowNumber}: ${oldValue} → ${qtyValue} (OSL ID: ${record.id})`);

            } catch (cellError) {
                console.error(`[updateDiscraftExcelFile] Error updating cell for OSL ID ${record.id}: ${cellError.message}`);
            }
        }

        // Save the updated workbook back to the timestamped copy (do NOT touch template)
        console.log(`[updateDiscraftExcelFile] Saving updated file: ${timestampedFilePath}`);
        await workbook.xlsx.writeFile(timestampedFilePath);

        // Email the timestamped file
        console.log(`[updateDiscraftExcelFile] Emailing timestamped file...`);
        // Build summary stats for email
        // 1) lookback days
        const { data: cfg, error: cfgErr } = await supabase
            .from('t_config')
            .select('value')
            .eq('key', 'discraft_disc_order_look_back_days')
            .single();
        const lookbackDays = cfgErr || !cfg ? 60 : parseInt(cfg.value) || 60;

        // 2) sum sold_last_90 during lookback (field name preserved) — fetch all rows in chunks
        async function fetchAllColumnSums(column) {
            const pageSize = 1000;
            let sum = 0;
            for (let from = 0; ; from += pageSize) {
                const to = from + pageSize - 1;
                const { data, error } = await supabase
                    .from('it_discraft_osl_map')
                    .select(column)
                    .order('id', { ascending: true })
                    .range(from, to);
                if (error) throw new Error(`Failed to fetch ${column} (range ${from}-${to}): ${error.message}`);
                if (!data || data.length === 0) break;
                if (column === 'sold_last_90') {
                    sum += data.reduce((s, r) => s + (r.sold_last_90 || 0), 0);
                } else if (column === 'in_stock') {
                    sum += data.reduce((s, r) => s + (r.in_stock || 0), 0);
                }
                if (data.length < pageSize) break;
            }
            return sum;
        }

        const totalSoldLast90 = await fetchAllColumnSums('sold_last_90');

        // 3) count of qty and sum of qty from the same dataset used to write Excel
        const qtyCount = nonZeroQtyCount;
        const qtySum = totalDiscsAdded;

        // 4) sum of in_stock (chunked)
        const inStockSum = await fetchAllColumnSums('in_stock');

        const emailResult = await emailDiscraftOrderSheet(timestampedFilePath, totalDiscsAdded, {
            lookbackDays,
            totalSoldLast90,
            qtyCount,
            qtySum,
            inStockSum
        });

        return {
            success: true,
            message: `Updated ${updatedCells} cells in Excel file and <NAME_EMAIL>`,
            updatedCells: updatedCells,
            totalDiscsAdded: totalDiscsAdded,
            recordsProcessed: oslMapData.length,
            originalFile: excelFilePath,
            timestampedFile: timestampedFilePath,
            emailResult: emailResult,
            updateLog: updateLog.slice(0, 10) // Include first 10 updates for logging
        };

    } catch (error) {
        console.error(`[updateDiscraftExcelFile] Error: ${error.message}`);
        return {
            success: false,
            error: error.message,
            updatedCells: 0,
            recordsProcessed: 0
        };
    }
}

/**
 * Process a import_discraft_osl_map_and_status task
 * @param {Object} task - The task object from the task queue
 * @param {Object} options - Options including supabase client and helper functions
 * @returns {Promise<void>}
 */
export async function processImportDiscraftOslMapAndStatusTask(task, { supabase, updateTaskStatus, logError }) {
    console.log(`[processImportDiscraftOslMapAndStatusTask] Processing task ${task.id} of type ${task.task_type}`);

    const startTime = new Date();

    try {
        // Update task to 'processing' status
        await updateTaskStatus(task.id, 'processing');

        // Parse the payload
        let payload;
        try {
            if (typeof task.payload === 'object' && task.payload !== null) {
                payload = task.payload;
            } else if (typeof task.payload === 'string') {
                payload = JSON.parse(task.payload);
            } else {
                throw new Error(`Unsupported payload type: ${typeof task.payload}`);
            }
        } catch (err) {
            throw new Error(`Failed to parse payload: ${err.message}`);
        }

        console.log(`[processImportDiscraftOslMapAndStatusTask] Parsed payload: ${JSON.stringify(payload)}`);

        // Use the hardcoded Google Sheets URL
        const googleSheetsUrl = 'https://docs.google.com/spreadsheets/d/1x0C8UmI-ukFP0r2rCM9wNipQ53XakFv30wQiYwJYgC8/edit?gid=1907003661#gid=1907003661';

        console.log(`[processImportDiscraftOslMapAndStatusTask] Fetching Map sheet data...`);
        
        // Step 1: Fetch the 'Map' sheet data
        const mapSheetData = await fetchGoogleSheetsData(googleSheetsUrl, 'Map');
        console.log(`[processImportDiscraftOslMapAndStatusTask] Map sheet has ${mapSheetData.length} rows`);

        // Step 2: Find all OS values in the Map sheet
        const osValues = findOSValues(mapSheetData);
        console.log(`[processImportDiscraftOslMapAndStatusTask] Found ${osValues.length} OS values in Map sheet`);

        if (osValues.length === 0) {
            throw new Error('No OS values found in the Map sheet');
        }

        // Step 3: Fetch the 'New' sheet data
        console.log(`[processImportDiscraftOslMapAndStatusTask] Fetching New sheet data...`);
        const newSheetData = await fetchGoogleSheetsData(googleSheetsUrl, 'New');
        console.log(`[processImportDiscraftOslMapAndStatusTask] New sheet has ${newSheetData.length} rows`);

        // Step 4: Get status values from the New sheet
        const osValuesWithStatus = getStatusValues(newSheetData, osValues);
        console.log(`[processImportDiscraftOslMapAndStatusTask] Processed status for ${osValuesWithStatus.length} OS values`);

        // Step 5: Clear row/col/status for all existing records (no full truncate)
        console.log(`[processImportDiscraftOslMapAndStatusTask] Clearing row/col/status on existing it_discraft_osl_map records...`);
        const { error: clearError } = await supabase
            .from('it_discraft_osl_map')
            .update({ row: null, col: null, status: null })
            .neq('id', 0); // Update all rows

        if (clearError) {
            throw new Error(`Failed to clear existing rows: ${clearError.message}`);
        }

        // Step 6: Upsert the new/updated data (IDs are stable; row/col/status may change)
        console.log(`[processImportDiscraftOslMapAndStatusTask] Upserting ${osValuesWithStatus.length} records (id, row, col, status)...`);

        const upsertData = osValuesWithStatus.map(item => ({
            id: item.id,
            row: item.row,
            col: item.col,
            status: item.status
        }));

        const { error: upsertError } = await supabase
            .from('it_discraft_osl_map')
            .upsert(upsertData, { onConflict: 'id' });

        if (upsertError) {
            throw new Error(`Failed to upsert map data: ${upsertError.message}`);
        }

        // Step 7: For IDs not present on the new sheet (still null row/col), mark Not Available
        // Do NOT touch qty here; let DB defaults/triggers handle it
        console.log(`[processImportDiscraftOslMapAndStatusTask] Marking missing IDs as Not Available...`);
        const { error: markMissingError } = await supabase
            .from('it_discraft_osl_map')
            .update({ status: 'Not Available' })
            .is('row', null)
            .is('col', null);

        if (markMissingError) {
            throw new Error(`Failed to mark missing IDs: ${markMissingError.message}`);
        }

        // Step 7: Calculate disc counts for each OSL map record (ALL IDs, not just those on the sheet)
        const { data: allIdsData, error: allIdsErr } = await supabase
            .from('it_discraft_osl_map')
            .select('id');
        if (allIdsErr) {
            throw new Error(`Failed to fetch all OSL IDs: ${allIdsErr.message}`);
        }
        const allIds = (allIdsData || []).map(r => r.id);
        console.log(`[processImportDiscraftOslMapAndStatusTask] Calculating disc counts for ${allIds.length} OSL records...`);

        const discCountResults = await calculateDiscCounts(supabase, allIds);

        console.log(`[processImportDiscraftOslMapAndStatusTask] Updated disc counts for ${discCountResults.updatedRecords} records`);

        // Step 8: Update Excel file with qty values
        console.log(`[processImportDiscraftOslMapAndStatusTask] Updating Excel file with qty values...`);

        const excelUpdateResults = await updateDiscraftExcelFile(supabase);

        console.log(`[processImportDiscraftOslMapAndStatusTask] Excel update completed: ${excelUpdateResults.message}`);

        const endTime = new Date();
        const duration = endTime - startTime;

        const result = {
            success: true,
            message: `Successfully imported ${osValuesWithStatus.length} Discraft OSL map records, calculated disc counts, and updated Excel file`,
            recordsProcessed: osValuesWithStatus.length,
            duration: `${duration}ms`,
            summary: {
                totalRecords: osValuesWithStatus.length,
                statusBreakdown: {
                    'In Stock': osValuesWithStatus.filter(item => item.status === 'In Stock').length,
                    'Out of Stock': osValuesWithStatus.filter(item => item.status === 'Out of Stock').length,
                    'Not Available': osValuesWithStatus.filter(item => item.status === 'Not Available').length
                },
                discCounts: discCountResults,
                excelUpdate: excelUpdateResults
            }
        };

        console.log(`[processImportDiscraftOslMapAndStatusTask] Task completed successfully: ${JSON.stringify(result)}`);
        await updateTaskStatus(task.id, 'completed', result);

    } catch (error) {
        const endTime = new Date();
        const duration = endTime - startTime;
        
        const errorResult = {
            success: false,
            error: error.message,
            duration: `${duration}ms`
        };

        console.error(`[processImportDiscraftOslMapAndStatusTask] Task failed: ${error.message}`);
        await logError(`Import Discraft OSL map task failed: ${error.message}`, `Task ${task.id}`);
        await updateTaskStatus(task.id, 'error', errorResult);
    }
}

export default processImportDiscraftOslMapAndStatusTask;
