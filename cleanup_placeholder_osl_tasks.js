// cleanup_placeholder_osl_tasks.js
// Find and clean up incorrectly queued publish_product_osl tasks for placeholder OSLs (max_weight < 10g)

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function cleanupPlaceholderOSLTasks() {
  try {
    console.log('🔍 Finding publish_product_osl tasks with status=error for OSLs with max_weight < 10g...');
    
    // Find error tasks for publish_product_osl
    const { data: errorTasks, error: tasksError } = await supabase
      .from('t_task_queue')
      .select('id, payload, created_at, processed_at')
      .eq('task_type', 'publish_product_osl')
      .eq('status', 'error');
    
    if (tasksError) {
      console.error('❌ Error fetching tasks:', tasksError);
      return;
    }
    
    console.log(`📋 Found ${errorTasks.length} error tasks for publish_product_osl`);
    
    if (errorTasks.length === 0) {
      console.log('✅ No error tasks found to clean up');
      return;
    }
    
    // Extract OSL IDs from task payloads and check their max_weight
    const placeholderTasks = [];
    
    for (const task of errorTasks) {
      try {
        const payload = typeof task.payload === 'string' ? JSON.parse(task.payload) : task.payload;
        const oslId = payload.id;
        
        if (!oslId) {
          console.log(`⚠️  Task ${task.id}: No OSL ID found in payload`);
          continue;
        }
        
        // Get OSL details
        const { data: oslData, error: oslError } = await supabase
          .from('t_order_sheet_lines')
          .select('id, max_weight, ready_button, ready')
          .eq('id', oslId)
          .single();
        
        if (oslError) {
          console.log(`⚠️  Task ${task.id}: Error fetching OSL ${oslId}:`, oslError.message);
          continue;
        }
        
        if (!oslData) {
          console.log(`⚠️  Task ${task.id}: OSL ${oslId} not found`);
          continue;
        }
        
        // Check if this is a placeholder OSL (max_weight < 10g)
        if (oslData.max_weight < 10) {
          placeholderTasks.push({
            taskId: task.id,
            oslId: oslId,
            maxWeight: oslData.max_weight,
            currentReadyButton: oslData.ready_button,
            currentReady: oslData.ready,
            createdAt: task.created_at,
            processedAt: task.processed_at
          });
        }
        
      } catch (err) {
        console.log(`⚠️  Task ${task.id}: Error processing:`, err.message);
      }
    }
    
    console.log(`\n🎯 Found ${placeholderTasks.length} tasks for placeholder OSLs (max_weight < 10g):`);
    
    if (placeholderTasks.length === 0) {
      console.log('✅ No placeholder OSL tasks found to clean up');
      return;
    }
    
    // Display the tasks that will be cleaned up
    placeholderTasks.forEach(task => {
      console.log(`📝 Task ${task.taskId}: OSL ${task.oslId} (${task.maxWeight}g) - ready_button: ${task.currentReadyButton}, ready: ${task.currentReady}`);
    });
    
    console.log(`\n🧹 Starting cleanup process...`);
    
    let deletedTasks = 0;
    let updatedOSLs = 0;
    let errors = 0;
    
    for (const task of placeholderTasks) {
      try {
        console.log(`\n🔧 Processing Task ${task.taskId} for OSL ${task.oslId}...`);
        
        // 1. Delete the task
        const { error: deleteError } = await supabase
          .from('t_task_queue')
          .delete()
          .eq('id', task.taskId);
        
        if (deleteError) {
          console.error(`❌ Error deleting task ${task.taskId}:`, deleteError.message);
          errors++;
          continue;
        }
        
        console.log(`✅ Deleted task ${task.taskId}`);
        deletedTasks++;
        
        // 2. Update OSL: set ready_button = FALSE and ready = FALSE
        const { error: updateError } = await supabase
          .from('t_order_sheet_lines')
          .update({
            ready_button: false,
            ready: false,
            todo: `Placeholder OSL (max_weight ${task.maxWeight}g < 10g) - not ready for publishing`
          })
          .eq('id', task.oslId);
        
        if (updateError) {
          console.error(`❌ Error updating OSL ${task.oslId}:`, updateError.message);
          errors++;
          continue;
        }
        
        console.log(`✅ Updated OSL ${task.oslId}: ready_button=false, ready=false`);
        updatedOSLs++;
        
      } catch (err) {
        console.error(`❌ Error processing task ${task.taskId}:`, err.message);
        errors++;
      }
    }
    
    console.log(`\n📊 Cleanup Summary:`);
    console.log(`✅ Tasks deleted: ${deletedTasks}`);
    console.log(`✅ OSLs updated: ${updatedOSLs}`);
    console.log(`❌ Errors: ${errors}`);
    
    if (errors === 0) {
      console.log(`\n🎉 Cleanup completed successfully!`);
    } else {
      console.log(`\n⚠️  Cleanup completed with ${errors} errors. Please review the error messages above.`);
    }
    
  } catch (error) {
    console.error('❌ Unexpected error during cleanup:', error.message);
  }
}

// Run the cleanup
cleanupPlaceholderOSLTasks();
