-- Add locking columns to t_task_queue table
ALTER TABLE t_task_queue
ADD COLUMN IF NOT EXISTS locked_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS locked_by TEXT;

-- Create an index on status and scheduled_at for better performance
CREATE INDEX IF NOT EXISTS idx_task_queue_status_scheduled
ON t_task_queue(status, scheduled_at);

-- Create an index on locked_at for better performance
CREATE INDEX IF NOT EXISTS idx_task_queue_locked_at
ON t_task_queue(locked_at);

-- Create a function to atomically lock and return pending tasks
CREATE OR REPLACE FUNCTION lock_pending_tasks(
    worker_id TEXT,
    max_tasks INTEGER,
    task_time TIMESTAMP WITH TIME ZONE
)
RETURNS SETOF t_task_queue AS $$
DECLARE
    lock_timeout INTERVAL := INTERVAL '5 minutes';
BEGIN
    RETURN QUERY
    WITH locked_tasks AS (
        UPDATE t_task_queue
        SET
            status = 'processing',
            locked_at = task_time,
            locked_by = worker_id
        WHERE id IN (
            SELECT id
            FROM t_task_queue
            WHERE
                status = 'pending'
                AND scheduled_at <= task_time
                AND (locked_at IS NULL OR locked_at < task_time - lock_timeout)
            ORDER BY scheduled_at ASC
            LIMIT max_tasks
            FOR UPDATE SKIP LOCKED
        )
        RETURNING *
    )
    SELECT * FROM locked_tasks;
END;
$$ LANGUAGE plpgsql;

-- Create a function to release locks on tasks that have been processing for too long
CREATE OR REPLACE FUNCTION release_stale_task_locks(
    timeout_minutes INTEGER DEFAULT 30
)
RETURNS INTEGER AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    UPDATE t_task_queue
    SET
        status = 'pending',
        locked_at = NULL,
        locked_by = NULL
    WHERE
        status = 'processing'
        AND locked_at < NOW() - (timeout_minutes * INTERVAL '1 minute')
        AND processed_at IS NULL;

    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    RETURN affected_rows;
END;
$$ LANGUAGE plpgsql;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Task locking system has been set up.';
END $$;
