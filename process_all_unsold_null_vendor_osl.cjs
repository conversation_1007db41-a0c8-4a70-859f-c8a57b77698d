require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function processAllUnsoldNullVendorOsl() {
  try {
    console.log('Processing ALL unsold discs with null vendor_osl_id...');
    
    // Get current count of unsold discs with null vendor_osl_id
    const { count: totalUnsoldNull, error: countError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('vendor_osl_id', null)
      .is('sold_date', null)  // Unsold discs only
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (countError) {
      console.error('Error getting count:', countError);
      return;
    }
    
    console.log(`Found ${totalUnsoldNull} unsold discs with null vendor_osl_id to process`);
    
    if (totalUnsoldNull === 0) {
      console.log('✅ No unsold discs remaining to process!');
      return;
    }
    
    // Process ALL unsold discs in batches
    let totalProcessed = 0;
    let totalUpdated = 0;
    let totalErrors = 0;
    let totalNoMatch = 0;
    const batchSize = 50; // Smaller batches for better tracking
    
    console.log(`\nProcessing ${totalUnsoldNull} unsold discs in batches of ${batchSize}...`);
    console.log('This will find ALL missed vendor OSL mappings for unsold inventory.\n');
    
    while (totalProcessed < totalUnsoldNull) {
      const batchNumber = Math.floor(totalProcessed / batchSize) + 1;
      console.log(`Unsold Batch ${batchNumber}:`);
      
      // Get next batch of unsold discs with null vendor_osl_id
      const { data: discs, error: batchError } = await supabase
        .from('t_discs')
        .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
        .is('vendor_osl_id', null)
        .is('sold_date', null)  // Unsold discs only
        .not('weight_mfg', 'is', null)
        .not('mps_id', 'is', null)
        .not('color_id', 'is', null)
        .range(0, batchSize - 1);  // Always get first batch since we're updating them
      
      if (batchError) {
        console.error('Error getting batch:', batchError);
        break;
      }
      
      if (!discs || discs.length === 0) {
        console.log('  No more unsold discs to process');
        break;
      }
      
      console.log(`  Processing ${discs.length} unsold discs...`);
      
      let batchUpdated = 0;
      let batchErrors = 0;
      let batchNoMatch = 0;
      
      for (const disc of discs) {
        try {
          // Test the vendor OSL function for this unsold disc
          const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
            'find_matching_osl_by_mfg_weight',
            {
              mps_id_param: disc.mps_id,
              color_id_param: disc.color_id,
              weight_mfg_param: disc.weight_mfg
            }
          );
          
          if (vendorOslError) {
            console.error(`    Error finding vendor OSL for disc ${disc.id}:`, vendorOslError);
            batchErrors++;
            totalErrors++;
            continue;
          }
          
          const vendorOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
          
          if (vendorOslId) {
            // This unsold disc SHOULD have a vendor OSL mapping!
            console.log(`    🎯 UNSOLD MATCH FOUND: Disc ${disc.id} → OSL ${vendorOslId}`);
            
            // Update the disc with vendor_osl_id
            const { error: updateError } = await supabase
              .from('t_discs')
              .update({ vendor_osl_id: vendorOslId })
              .eq('id', disc.id);
            
            if (updateError) {
              console.error(`      Error updating disc ${disc.id}:`, updateError);
              batchErrors++;
              totalErrors++;
            } else {
              batchUpdated++;
              totalUpdated++;
              
              // Show mapping comparison for unsold discs
              if (vendorOslId !== disc.order_sheet_line_id) {
                console.log(`      Different mappings: regular OSL ${disc.order_sheet_line_id}, vendor OSL ${vendorOslId}`);
              } else {
                console.log(`      Same OSL for both mappings: ${vendorOslId}`);
              }
              
              // Show disc details for significant finds
              if (batchUpdated <= 5) {
                console.log(`      Details: MPS ${disc.mps_id}, Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id}`);
              }
            }
          } else {
            // No match found - this is expected for some discs
            batchNoMatch++;
            totalNoMatch++;
          }
          
          totalProcessed++;
          
        } catch (err) {
          console.error(`    Error processing disc ${disc.id}:`, err.message);
          batchErrors++;
          totalErrors++;
          totalProcessed++;
        }
      }
      
      console.log(`  Batch ${batchNumber} complete: ${batchUpdated} updated, ${batchNoMatch} no match, ${batchErrors} errors`);
      console.log(`  Running totals: ${totalUpdated} updated, ${totalNoMatch} no match, ${totalErrors} errors, ${totalProcessed} processed\n`);
      
      // Progress update every 10 batches
      if (batchNumber % 10 === 0) {
        const percentComplete = ((totalProcessed / totalUnsoldNull) * 100).toFixed(1);
        console.log(`🔄 Progress: ${percentComplete}% complete (${totalProcessed}/${totalUnsoldNull})`);
        console.log(`   Found ${totalUpdated} missed vendor mappings for unsold discs so far!\n`);
      }
      
      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    console.log('\n=== UNSOLD DISCS PROCESSING COMPLETE ===');
    console.log(`Total unsold discs processed: ${totalProcessed}`);
    console.log(`Missed vendor mappings found and updated: ${totalUpdated}`);
    console.log(`Unsold discs with no vendor OSL match: ${totalNoMatch}`);
    console.log(`Errors encountered: ${totalErrors}`);
    
    if (totalUpdated > 0) {
      console.log(`\n🎉 SUCCESS! Found and updated ${totalUpdated} missed vendor OSL mappings for unsold discs!`);
      
      // Get final statistics for unsold discs
      const { count: finalUnsoldVendorOslCount, error: finalError } = await supabase
        .from('t_discs')
        .select('*', { count: 'exact', head: true })
        .not('vendor_osl_id', 'is', null)
        .is('sold_date', null);  // Unsold discs
      
      const { count: finalUnsoldNullCount, error: finalNullError } = await supabase
        .from('t_discs')
        .select('*', { count: 'exact', head: true })
        .is('vendor_osl_id', null)
        .is('sold_date', null)  // Unsold discs
        .not('weight_mfg', 'is', null)
        .not('mps_id', 'is', null)
        .not('color_id', 'is', null);
      
      if (!finalError && !finalNullError) {
        console.log(`\n📊 FINAL UNSOLD DISCS STATISTICS:`);
        console.log(`Unsold discs with vendor_osl_id: ${finalUnsoldVendorOslCount}`);
        console.log(`Remaining unsold discs with null vendor_osl_id: ${finalUnsoldNullCount}`);
        console.log(`Improvement for unsold inventory: +${totalUpdated} new vendor mappings`);
        
        const totalUnsoldEligible = finalUnsoldVendorOslCount + finalUnsoldNullCount;
        const newSuccessRate = ((finalUnsoldVendorOslCount / totalUnsoldEligible) * 100).toFixed(1);
        console.log(`New unsold discs vendor mapping success rate: ${newSuccessRate}%`);
      }
      
      console.log('\n✅ Unsold inventory dual mapping system is now much more complete!');
    } else {
      console.log('\n✅ No additional matches found for unsold discs - all possible vendor mappings already exist.');
      console.log(`The remaining ${totalNoMatch} unsold discs correctly have no vendor OSL matches.`);
    }
    
    // Summary
    console.log('\n🎯 UNSOLD INVENTORY DUAL MAPPING SUMMARY:');
    console.log(`This process focused specifically on unsold discs (sold_date IS NULL)`);
    console.log(`Found ${totalUpdated} additional vendor mappings for active inventory`);
    console.log('🚀 Unsold inventory dual mapping optimization complete!');
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

processAllUnsoldNullVendorOsl();
