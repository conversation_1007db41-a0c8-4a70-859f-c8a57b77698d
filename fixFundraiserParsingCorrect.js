import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

// Enhanced parsing function for fundraiser items
function parseFundraiserItem(rawModel) {
    // Handle "Fundraiser - Ben <PERSON> Z Jawbreaker Thrasher (msrp $19.99, cost $10.00)"
    if (rawModel && rawModel.includes('Fundraiser - <PERSON>')) {
        const match = rawModel.match(/Fundraiser - <PERSON> (.+?) (Thrasher|Buzzz|[A-Z][a-z]+)\s*\(/);
        if (match) {
            const plasticPart = match[1].trim(); // "Z Jawbreaker" or "Big Z"
            const moldName = match[2]; // "Thrasher" or "Buzzz"
            
            let plasticName = 'Unknown';
            if (plasticPart.includes('Z Jawbreaker')) {
                plasticName = 'Elite Z Jawbreaker';
            } else if (plasticPart.includes('Big Z')) {
                plasticName = 'Big Z Collection';
            }
            
            return {
                mold_name: moldName,
                plastic_name: plasticName,
                stamp_name: 'Live Free - Be Happy - Funky Ben Askren Fundraiser'
            };
        }
    }
    
    return null;
}

async function fixFundraiserParsingCorrect() {
    try {
        console.log('🔧 Fixing fundraiser section parsing (CORRECTED)...\n');
        
        // 1. First, mark the header row (22) as not orderable
        console.log('1. Fixing header row (22)...');
        const { data: headerRows, error: headerError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .update({ 
                is_orderable: false,
                excel_column: null,
                excel_mapping_key: null
            })
            .eq('excel_row_hint', 22)
            .eq('raw_line_type', 'LIMITED RELEASE - Fundraiser for Ben Askren')
            .select();

        if (headerError) {
            console.error('❌ Error fixing header row:', headerError);
        } else {
            console.log(`✅ Fixed ${headerRows?.length || 0} header records`);
        }

        // 2. Delete all the incorrectly mapped weight column records for fundraiser items
        console.log('\n2. Cleaning up incorrectly mapped weight column records...');
        
        const { data: deletedRecords, error: deleteError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .delete()
            .in('excel_row_hint', [25, 28])
            .in('excel_column', ['L', 'M', 'N', 'O', 'P', 'Q', 'R'])
            .eq('raw_line_type', 'SPECIAL')
            .select();

        if (deleteError) {
            console.error('❌ Error deleting incorrect records:', deleteError);
        } else {
            console.log(`✅ Deleted ${deletedRecords?.length || 0} incorrectly mapped records`);
        }

        // 3. Create the correct fundraiser records in column A
        console.log('\n3. Creating correct fundraiser records...');
        
        // Get unique fundraiser items from the raw_model field
        const { data: uniqueFundraisers, error: uniqueError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('raw_model, excel_row_hint')
            .eq('raw_line_type', 'SPECIAL')
            .ilike('raw_model', '%Fundraiser - Ben Askren%')
            .limit(2); // Should be 2 unique items

        if (uniqueError) {
            console.error('❌ Error getting unique fundraisers:', uniqueError);
            return;
        }

        console.log(`✅ Found ${uniqueFundraisers.length} unique fundraiser items`);

        // Create correct records for each fundraiser
        for (const fundraiser of uniqueFundraisers) {
            console.log(`\nProcessing: ${fundraiser.raw_model}`);
            
            // Parse the fundraiser item
            const parsed = parseFundraiserItem(fundraiser.raw_model);
            if (!parsed) {
                console.log('❌ Could not parse fundraiser item');
                continue;
            }

            console.log(`✅ Parsed: ${parsed.plastic_name} ${parsed.mold_name} (${parsed.stamp_name})`);

            // Create the correct record in column A
            const newRecord = {
                mold_name: parsed.mold_name,
                plastic_name: parsed.plastic_name,
                min_weight: 150, // Default weight range for fundraiser items
                max_weight: 180,
                color_name: 'Varies',
                stamp_name: parsed.stamp_name,
                vendor_product_code: `${parsed.plastic_name}_${parsed.mold_name}_150-180`.replace(/\s+/g, '_'),
                is_orderable: true,
                is_currently_available: true,
                cost_price: 10.00, // From the description
                excel_mapping_key: `SPECIAL|${fundraiser.raw_model}|Order Qty`,
                excel_row_hint: fundraiser.excel_row_hint,
                excel_column: 'A', // Correct column for fundraiser items
                raw_line_type: 'SPECIAL',
                raw_model: fundraiser.raw_model,
                raw_weight_range: 'Order Qty'
            };

            const { error: insertError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .insert(newRecord);

            if (insertError) {
                console.error(`❌ Error inserting record for ${fundraiser.raw_model}:`, insertError);
            } else {
                console.log(`✅ Created correct record for ${fundraiser.raw_model}`);
            }
        }

        // 4. Verify the fixes
        console.log('\n4. Verifying fixes...');
        
        const { data: verifyData, error: verifyError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, mold_name, plastic_name, stamp_name, is_orderable, raw_model')
            .gte('excel_row_hint', 22)
            .lte('excel_row_hint', 30)
            .order('excel_row_hint, excel_column');

        if (verifyError) {
            console.error('❌ Error verifying fixes:', verifyError);
        } else {
            console.log(`✅ Verification complete - ${verifyData.length} records found:`);
            verifyData.forEach((record, index) => {
                console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.mold_name} | ${record.plastic_name} | Orderable: ${record.is_orderable}`);
                if (record.raw_model && record.raw_model.includes('Fundraiser')) {
                    console.log(`      Raw Model: ${record.raw_model}`);
                }
            });
        }

        // 5. Test MPS matching for the new fundraiser records
        console.log('\n5. Testing MPS matching for fundraiser records...');
        
        const { data: fundraiserMpsTest, error: mpsError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('mold_name, plastic_name, stamp_name, calculated_mps_id')
            .eq('excel_column', 'A')
            .ilike('raw_model', '%Fundraiser - Ben Askren%');

        if (mpsError) {
            console.error('❌ Error testing MPS matching:', mpsError);
        } else {
            console.log(`✅ Fundraiser MPS test results:`);
            fundraiserMpsTest.forEach((record, index) => {
                console.log(`   ${index + 1}. ${record.plastic_name} ${record.mold_name} (${record.stamp_name})`);
                console.log(`      MPS ID: ${record.calculated_mps_id || 'NOT MATCHED'}`);
            });
        }

        console.log('\n🎉 Fundraiser parsing fixes completed!');
        console.log('\n📋 Summary of changes:');
        console.log('   • Row 22 header records marked as not orderable');
        console.log('   • Incorrect weight column records (L-R) deleted');
        console.log('   • Correct fundraiser records created in column A');
        console.log('   • Proper mold/plastic/stamp parsing applied');
        
    } catch (error) {
        console.error('❌ Fix failed:', error.message);
    }
}

fixFundraiserParsingCorrect().catch(console.error);
