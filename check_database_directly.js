// check_database_directly.js - Check database directly using Supabase client
import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in environment');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Checking database connection...');
    
    // Test basic connection
    const { data: testData, error: testError } = await supabase
      .from('t_images')
      .select('id')
      .limit(1);
    
    if (testError) {
      console.error('Connection test failed:', testError);
      return;
    }
    
    console.log('✅ Database connection successful');
    
    // Check if we can query system tables
    console.log('\nTrying to query pg_proc directly...');
    const { data: procData, error: procError } = await supabase
      .from('pg_proc')
      .select('proname')
      .eq('proname', 'enqueue_variant_ready_on_image_verified')
      .limit(1);
    
    if (procError) {
      console.log('Cannot query pg_proc directly:', procError.message);
    } else {
      console.log('pg_proc query result:', procData);
    }
    
    // Check if we can query pg_trigger directly
    console.log('\nTrying to query pg_trigger directly...');
    const { data: triggerData, error: triggerError } = await supabase
      .from('pg_trigger')
      .select('tgname')
      .eq('tgname', 'trg_t_images_enqueue_variant_ready_on_image_verified')
      .limit(1);
    
    if (triggerError) {
      console.log('Cannot query pg_trigger directly:', triggerError.message);
    } else {
      console.log('pg_trigger query result:', triggerData);
    }
    
    // Try using information_schema
    console.log('\nTrying to query information_schema.routines...');
    const { data: routineData, error: routineError } = await supabase
      .from('information_schema.routines')
      .select('routine_name')
      .eq('routine_name', 'enqueue_variant_ready_on_image_verified')
      .limit(1);
    
    if (routineError) {
      console.log('Cannot query information_schema.routines:', routineError.message);
    } else {
      console.log('information_schema.routines query result:', routineData);
    }
    
    // Check what RPC functions are available
    console.log('\nChecking available RPC functions...');
    try {
      const { data: rpcTest1, error: rpcError1 } = await supabase.rpc('exec_sql', { sql_query: 'SELECT 1' });
      console.log('exec_sql with sql_query:', rpcError1 ? 'NOT AVAILABLE' : 'AVAILABLE');
    } catch (e) {
      console.log('exec_sql with sql_query: ERROR -', e.message);
    }
    
    try {
      const { data: rpcTest2, error: rpcError2 } = await supabase.rpc('execute_sql', { sql: 'SELECT 1' });
      console.log('execute_sql with sql:', rpcError2 ? 'NOT AVAILABLE' : 'AVAILABLE');
    } catch (e) {
      console.log('execute_sql with sql: ERROR -', e.message);
    }

  } catch (e) {
    console.error('Fatal error:', e?.message || e);
    process.exit(1);
  }
}

main();
