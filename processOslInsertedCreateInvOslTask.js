// processOslInsertedCreateInvOslTask.js - Process osl_inserted_create_inv_osl tasks
import { createClient } from '@supabase/supabase-js';

// Function to process an osl_inserted_create_inv_osl task
export default async function processOslInsertedCreateInvOslTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processOslInsertedCreateInvOslTask.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[processOslInsertedCreateInvOslTask.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[processOslInsertedCreateInvOslTask.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[processOslInsertedCreateInvOslTask.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[processOslInsertedCreateInvOslTask.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[processOslInsertedCreateInvOslTask.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process OSL inserted create inv_osl task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[processOslInsertedCreateInvOslTask.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process OSL inserted create inv_osl task. Missing OSL id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const oslId = payload.id;
    console.log(`[processOslInsertedCreateInvOslTask.js] Creating inv_osl record for OSL id=${oslId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Check if the record already exists
    const { data: existingRecord, error: checkError } = await supabase
      .from('t_inv_osl')
      .select('id')
      .eq('id', oslId)
      .maybeSingle();

    if (checkError) {
      const errMsg = `[processOslInsertedCreateInvOslTask.js] Error checking for existing record: ${checkError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Checking for existing record for OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check for existing inv_osl record. Database error.",
        error: checkError.message
      });
      return;
    }

    if (existingRecord) {
      console.log(`[processOslInsertedCreateInvOslTask.js] Record already exists for OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `Success! t_inv_osl record already exists for OSL id=${oslId}.`,
        osl_id: oslId
      });
      return;
    }

    // Insert a new record into t_inv_osl
    console.log(`[processOslInsertedCreateInvOslTask.js] Inserting new record into t_inv_osl for OSL id=${oslId}`);
    const { error: insertError } = await supabase
      .from('t_inv_osl')
      .insert({
        id: oslId,
        available_quantity: 0
      });

    if (insertError) {
      const errMsg = `[processOslInsertedCreateInvOslTask.js] Error inserting t_inv_osl record: ${insertError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Inserting t_inv_osl record for OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to insert t_inv_osl record. Database error.",
        error: insertError.message
      });
      return;
    }

    console.log(`[processOslInsertedCreateInvOslTask.js] Successfully inserted t_inv_osl record for OSL id=${oslId}`);
    await updateTaskStatus(task.id, 'completed', {
      message: `Success! New t_inv_osl record created for OSL id=${oslId} with available_quantity=0.`,
      osl_id: oslId
    });
  } catch (err) {
    const errMsg = `[processOslInsertedCreateInvOslTask.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process OSL inserted create inv_osl task due to an unexpected error.",
      error: err.message
    });
  }
}
