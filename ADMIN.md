# Task Queue Admin Interface

A web-based admin interface for managing and monitoring the task queue system.

## Overview

This admin interface provides a user-friendly way to:

1. Enqueue image verification tasks
2. View pending and scheduled tasks
3. Run the worker process (once or as a daemon)
4. Monitor the worker status and output

## Getting Started

### Prerequisites

- Node.js installed
- Supabase project set up with the required tables
- Environment variables configured in a `.env` file

### Running the Admin Interface

1. Start the admin server:

```bash
node adminServer.js
```

The server will automatically try to use port 3000. If that port is already in use, it will try the next available port (3001, 3002, etc.).

2. Open your browser and navigate to the URL shown in the console output, which will be something like:

```
http://localhost:3000/admin.html
```

You can also specify a custom port by setting the PORT environment variable:

```bash
PORT=8080 node adminServer.js
```

## Features

### Dashboard

The dashboard provides an overview of the task queue system, including:

- Worker status (running or stopped)
- Number of pending tasks ready to process
- Number of future scheduled tasks
- Last run time

### Tasks

The Tasks tab allows you to:

- Enqueue new image verification tasks
- Specify a delay for scheduled execution
- View pending and future scheduled tasks

### Worker

The Worker tab allows you to:

- Run the worker once to process pending tasks
- Run the worker as a daemon that continuously checks for new tasks
- Stop the worker daemon
- View the worker output in real-time

### Documentation

The Documentation tab displays the README content for reference.

## API Endpoints

The admin server provides the following API endpoints:

- `POST /api/enqueue-task`: Enqueues a new task
- `GET /api/tasks`: Gets pending and future scheduled tasks
- `GET /api/worker/status`: Gets the worker status and output
- `POST /api/worker/run-once`: Runs the worker once
- `POST /api/worker/start-daemon`: Starts the worker daemon
- `POST /api/worker/stop-daemon`: Stops the worker daemon

## Troubleshooting

If you encounter issues:

1. Make sure the admin server is running
2. Check that your environment variables are correctly set
3. Verify that the Supabase connection is working
4. Check the browser console for JavaScript errors
