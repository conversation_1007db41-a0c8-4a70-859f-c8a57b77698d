// imageWatcher.js
import chokidar from 'chokidar';
import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Folder to watch
const watchFolder = process.env.WATCH_FOLDER || './test_images';
const bucketName = process.env.S3_BUCKET || 'images';

// Supported image extensions
const supportedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];

// Create a log file
const logFile = path.join(process.cwd(), 'image_watcher.log');
const logMessage = (message) => {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] ${message}\n`;
  console.log(logEntry.trim());
  fs.appendFileSync(logFile, logEntry);
};

// Initialize watcher
logMessage(`[imageWatcher.js] Starting image watcher...`);
logMessage(`[imageWatcher.js] Watching folder: ${watchFolder}`);

// Make sure the watch folder exists
if (!fs.existsSync(watchFolder)) {
  fs.mkdirSync(watchFolder, { recursive: true });
  logMessage(`[imageWatcher.js] Created watch folder: ${watchFolder}`);
} else {
  logMessage(`[imageWatcher.js] Watch folder exists: ${watchFolder}`);
  // List files in the watch folder
  const files = fs.readdirSync(watchFolder);
  logMessage(`[imageWatcher.js] Files in watch folder: ${files.join(', ') || 'none'}`);
}

const watcher = chokidar.watch(watchFolder, {
  persistent: true,
  ignoreInitial: false, // Set to true if you don't want to process existing files
  awaitWriteFinish: {
    stabilityThreshold: 2000, // Wait 2 seconds after last change
    pollInterval: 100 // Poll every 100ms
  },
  alwaysStat: true,
  usePolling: true, // Use polling for better compatibility
  interval: 1000, // Poll every 1 second
  depth: 1 // Only watch files in the root of the watch folder
});

// Debug watcher events
watcher.on('all', (event, path) => {
  logMessage(`[imageWatcher.js] Watcher event: ${event} - ${path}`);
});

// Handle new files
watcher.on('add', async (filePath) => {
  try {
    const ext = path.extname(filePath).toLowerCase();

    // Check if it's an image file
    if (!supportedExtensions.includes(ext)) {
      logMessage(`[imageWatcher.js] Skipping non-image file: ${filePath}`);
      return;
    }

    logMessage(`[imageWatcher.js] New image detected: ${filePath}`);

    // Read file
    const fileBuffer = fs.readFileSync(filePath);
    const fileName = path.basename(filePath);

    // Upload to Supabase Storage
    try {
      logMessage(`[imageWatcher.js] Uploading ${fileName} to S3...`);

      // Check if Supabase is properly configured
      if (!supabaseUrl || !supabaseKey) {
        logMessage(`[imageWatcher.js] Supabase configuration missing. URL: ${supabaseUrl ? 'OK' : 'Missing'}, Key: ${supabaseKey ? 'OK' : 'Missing'}`);
        logMessage(`[imageWatcher.js] Simulating upload for ${fileName} (no actual upload)`);
        logMessage(`[imageWatcher.js] Successfully simulated upload for ${fileName}`);
        return;
      }

      const { data, error } = await supabase.storage
        .from(bucketName)
        .upload(`uploads/${fileName}`, fileBuffer, {
          contentType: `image/${ext.substring(1)}`, // Remove the dot from extension
          upsert: true
        });

      if (error) {
        logMessage(`[imageWatcher.js] Error uploading ${fileName}: ${error.message}`);
        logMessage(`[imageWatcher.js] Full error: ${JSON.stringify(error)}`);
      } else {
        logMessage(`[imageWatcher.js] Successfully uploaded ${fileName} to S3`);

        // Insert a record in the database
        try {
          const { error: dbError } = await supabase
            .from('t_images')
            .insert({
              file_name: fileName,
              storage_path: `uploads/${fileName}`,
              uploaded_at: new Date().toISOString()
            });

          if (dbError) {
            logMessage(`[imageWatcher.js] Error inserting record: ${dbError.message}`);
            logMessage(`[imageWatcher.js] Full DB error: ${JSON.stringify(dbError)}`);
          } else {
            logMessage(`[imageWatcher.js] Successfully inserted record for ${fileName}`);
          }
        } catch (dbErr) {
          logMessage(`[imageWatcher.js] Exception inserting record: ${dbErr.message}`);
        }
      }
    } catch (uploadErr) {
      logMessage(`[imageWatcher.js] Exception during upload process: ${uploadErr.message}`);
    }

    // Optionally, move the original file to a processed folder
    // const processedFolder = process.env.PROCESSED_FOLDER;
    // if (processedFolder) {
    //   if (!fs.existsSync(processedFolder)) {
    //     fs.mkdirSync(processedFolder, { recursive: true });
    //   }
    //   fs.renameSync(filePath, path.join(processedFolder, fileName));
    //   logMessage(`[imageWatcher.js] Moved ${fileName} to processed folder`);
    // }
  } catch (err) {
    logMessage(`[imageWatcher.js] Exception processing ${filePath}: ${err.message}`);
  }
});

// Handle errors
watcher.on('error', (error) => {
  logMessage(`[imageWatcher.js] Watcher error: ${error}`);
});

// Log when ready
watcher.on('ready', () => {
  logMessage('[imageWatcher.js] Initial scan complete. Watching for new files...');
});

// Handle process termination
process.on('SIGINT', () => {
  logMessage('[imageWatcher.js] Stopping image watcher...');
  watcher.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  logMessage('[imageWatcher.js] Stopping image watcher...');
  watcher.close();
  process.exit(0);
});
