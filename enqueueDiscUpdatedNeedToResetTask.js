// enqueueDiscUpdatedNeedToResetTask.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Create a Supabase client using environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Enqueue a disc_updated_need_to_reset task
 * @param {number} discId - The ID of the disc that was updated
 * @param {string|null} soldDate - The current sold_date of the disc (optional, will be fetched if not provided)
 * @param {Date} scheduledAt - Optional scheduled time (defaults to 5 minutes from now)
 * @returns {Promise<Object>} The created task record
 */
export async function enqueueDiscUpdatedNeedToResetTask(discId, soldDate = null, scheduledAt = null) {
  if (!discId) {
    throw new Error('discId is required');
  }

  // If soldDate not provided, fetch it from the database
  if (soldDate === null) {
    const { data: discData, error } = await supabase
      .from('t_discs')
      .select('sold_date')
      .eq('id', discId)
      .single();

    if (error) {
      throw new Error(`Failed to fetch disc data: ${error.message}`);
    }

    soldDate = discData?.sold_date || null;
  }

  const now = new Date();
  const defaultScheduledAt = new Date(now.getTime() + 5 * 60 * 1000); // 5 minutes from now

  const task = {
    task_type: 'disc_updated_need_to_reset',
    payload: {
      id: discId,
      sold_date: soldDate
    },
    status: 'pending',
    scheduled_at: (scheduledAt || defaultScheduledAt).toISOString(),
    created_at: now.toISOString(),
    enqueued_by: `enqueueDiscUpdatedNeedToResetTask_${discId}`
  };
  
  console.log(`[enqueueDiscUpdatedNeedToResetTask] Enqueueing disc update reset task for disc ID: ${discId}`);
  
  const { data, error } = await supabase
    .from('t_task_queue')
    .insert(task)
    .select()
    .single();
  
  if (error) {
    console.error(`[enqueueDiscUpdatedNeedToResetTask] Error enqueueing task:`, error);
    throw error;
  }
  
  console.log(`[enqueueDiscUpdatedNeedToResetTask] Successfully enqueued task ${data.id} for disc ${discId}`);
  return data;
}

// If this script is run directly, enqueue a task with the provided disc ID
if (process.argv[1] && process.argv[1].endsWith('enqueueDiscUpdatedNeedToResetTask.js')) {
  const discId = process.argv[2];
  
  if (!discId) {
    console.error('Usage: node enqueueDiscUpdatedNeedToResetTask.js <disc_id>');
    process.exit(1);
  }
  
  enqueueDiscUpdatedNeedToResetTask(parseInt(discId))
    .then(task => {
      console.log('Task enqueued successfully:', task);
      process.exit(0);
    })
    .catch(error => {
      console.error('Error enqueueing task:', error);
      process.exit(1);
    });
}
