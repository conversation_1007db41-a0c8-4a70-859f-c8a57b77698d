require('dotenv').config();
const fs = require('fs');
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function runBulkUpdate() {
  try {
    console.log('Starting bulk vendor_osl_id update using SQL...');
    
    // First, check current state
    console.log('\n=== CHECKING CURRENT STATE ===');
    const { data: beforeStats, error: beforeError } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT 
            COUNT(*) as total_discs_needing_update,
            COUNT(CASE WHEN weight_mfg IS NOT NULL THEN 1 END) as discs_with_weight_mfg,
            COUNT(CASE WHEN mps_id IS NOT NULL THEN 1 END) as discs_with_mps_id,
            COUNT(CASE WHEN color_id IS NOT NULL THEN 1 END) as discs_with_color_id
        FROM t_discs 
        WHERE vendor_osl_id IS NULL;
      `
    });
    
    if (beforeError) {
      console.error('Error checking current state:', beforeError);
      return;
    }
    
    console.log('Before update stats:', beforeStats);
    
    // Perform the bulk update
    console.log('\n=== PERFORMING BULK UPDATE ===');
    const updateSql = `
      UPDATE t_discs 
      SET vendor_osl_id = (
          SELECT osl.id
          FROM t_order_sheet_lines osl
          WHERE osl.mps_id = t_discs.mps_id
            AND (osl.color_id = t_discs.color_id OR osl.color_id = 23)
            AND ROUND(t_discs.weight_mfg) >= osl.min_weight
            AND ROUND(t_discs.weight_mfg) <= osl.max_weight
          LIMIT 1
      )
      WHERE vendor_osl_id IS NULL
        AND weight_mfg IS NOT NULL
        AND mps_id IS NOT NULL
        AND color_id IS NOT NULL
        AND EXISTS (
          SELECT 1
          FROM t_order_sheet_lines osl
          WHERE osl.mps_id = t_discs.mps_id
            AND (osl.color_id = t_discs.color_id OR osl.color_id = 23)
            AND ROUND(t_discs.weight_mfg) >= osl.min_weight
            AND ROUND(t_discs.weight_mfg) <= osl.max_weight
        );
    `;
    
    const { data: updateResult, error: updateError } = await supabase.rpc('exec_sql', {
      sql_query: updateSql
    });
    
    if (updateError) {
      console.error('Error performing bulk update:', updateError);
      return;
    }
    
    console.log('Bulk update completed successfully');
    
    // Check results
    console.log('\n=== CHECKING RESULTS ===');
    const { data: afterStats, error: afterError } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT 
            COUNT(*) as total_discs,
            COUNT(CASE WHEN vendor_osl_id IS NOT NULL THEN 1 END) as discs_with_vendor_osl_id,
            COUNT(CASE WHEN vendor_osl_id IS NULL THEN 1 END) as discs_without_vendor_osl_id,
            COUNT(CASE WHEN vendor_osl_id IS NOT NULL AND vendor_osl_id != order_sheet_line_id THEN 1 END) as discs_with_different_mappings
        FROM t_discs 
        WHERE weight_mfg IS NOT NULL 
          AND mps_id IS NOT NULL 
          AND color_id IS NOT NULL;
      `
    });
    
    if (afterError) {
      console.error('Error checking results:', afterError);
      return;
    }
    
    console.log('After update stats:', afterStats);
    
    // Calculate and display summary
    if (beforeStats && afterStats && beforeStats.length > 0 && afterStats.length > 0) {
      const before = beforeStats[0];
      const after = afterStats[0];
      
      console.log('\n=== SUMMARY ===');
      console.log(`Total discs that needed updating: ${before.total_discs_needing_update}`);
      console.log(`Discs now with vendor_osl_id: ${after.discs_with_vendor_osl_id}`);
      console.log(`Discs still without vendor_osl_id: ${after.discs_without_vendor_osl_id}`);
      console.log(`Discs with different regular vs vendor mappings: ${after.discs_with_different_mappings}`);
      
      if (after.discs_with_different_mappings > 0) {
        console.log('\n🎯 SUCCESS: Found discs with different regular vs vendor OSL mappings!');
        console.log('This confirms the dual mapping functionality is working correctly.');
      }
      
      console.log('\n✅ Bulk update completed successfully!');
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

// Add confirmation
console.log('This will perform a bulk SQL update of ALL discs with null vendor_osl_id values.');
console.log('This is much faster than the batch approach but cannot be easily stopped once started.');
console.log('\nPress Enter to continue or Ctrl+C to cancel...');

process.stdin.once('data', () => {
  runBulkUpdate();
});
