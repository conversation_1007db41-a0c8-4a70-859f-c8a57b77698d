import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function main() {
  const { data, error } = await supabase
    .from('t_task_queue')
    .select('id, task_type, status, processed_at, result')
    .eq('task_type', 'import_mvp_osl_map_and_status')
    .order('id', { ascending: false })
    .limit(5);
  if (error) { console.error(error); process.exit(1); }
  console.log(JSON.stringify(data, null, 2));
}

main();

