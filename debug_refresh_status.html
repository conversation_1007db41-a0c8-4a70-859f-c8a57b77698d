<!DOCTYPE html>
<html>
<head>
    <title>Debug RefreshStatus</title>
</head>
<body>
    <h1>Debug RefreshStatus Function</h1>
    
    <div>
        <button onclick="testRefreshStatus()">Test RefreshStatus</button>
        <div id="output"></div>
    </div>
    
    <div>
        <p><strong>Pending Tasks:</strong> <span id="pendingTasksCount">Loading...</span></p>
        <p><strong>Future Scheduled Tasks:</strong> <span id="futureTasksCount">Loading...</span></p>
        <p><strong>Last Run:</strong> <span id="lastRunTime">Unknown</span></p>
    </div>

    <script>
        function testRefreshStatus() {
            const output = document.getElementById('output');
            output.innerHTML = 'Testing refreshStatus...<br>';
            
            console.log('Starting refreshStatus test');
            
            // First, fetch worker status
            fetch('/api/worker/status')
                .then(response => {
                    console.log('Response received:', response.status);
                    output.innerHTML += `✅ Response status: ${response.status}<br>`;
                    return response.json();
                })
                .then(data => {
                    console.log('Data received:', data);
                    output.innerHTML += `✅ Data received: ${JSON.stringify(data)}<br>`;
                    
                    // Update the elements
                    const pendingElement = document.getElementById('pendingTasksCount');
                    const futureElement = document.getElementById('futureTasksCount');
                    const lastRunElement = document.getElementById('lastRunTime');
                    
                    if (pendingElement) {
                        pendingElement.textContent = data.pendingTasksCount || 0;
                        output.innerHTML += `✅ Updated pendingTasksCount to: ${data.pendingTasksCount}<br>`;
                    } else {
                        output.innerHTML += `❌ pendingTasksCount element not found<br>`;
                    }
                    
                    if (futureElement) {
                        futureElement.textContent = data.futureTasksCount || 0;
                        output.innerHTML += `✅ Updated futureTasksCount to: ${data.futureTasksCount}<br>`;
                    } else {
                        output.innerHTML += `❌ futureTasksCount element not found<br>`;
                    }
                    
                    if (lastRunElement) {
                        if (data.lastRunTime) {
                            const formattedTime = new Date(data.lastRunTime).toLocaleString();
                            lastRunElement.textContent = formattedTime;
                            output.innerHTML += `✅ Updated lastRunTime to: ${formattedTime}<br>`;
                        } else {
                            lastRunElement.textContent = 'Never';
                            output.innerHTML += `✅ Updated lastRunTime to: Never<br>`;
                        }
                    } else {
                        output.innerHTML += `❌ lastRunTime element not found<br>`;
                    }
                })
                .catch(error => {
                    console.error('Error fetching worker status:', error);
                    output.innerHTML += `❌ Error: ${error.message}<br>`;
                });
        }
        
        // Auto-run on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, running test');
            testRefreshStatus();
        });
    </script>
</body>
</html>
