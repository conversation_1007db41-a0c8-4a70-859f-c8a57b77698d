// enqueueGenerateTitlePullHandleTask.js
// Script to enqueue a generate_disc_title_pull_and_handle task
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';

dotenv.config();

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('id', {
    describe: 'Disc ID to generate title, pull, and handle for',
    type: 'number',
    demandOption: true
  })
  .option('delay', {
    describe: 'Delay in minutes before the task should be executed',
    type: 'number',
    default: 0
  })
  .help()
  .alias('help', 'h')
  .argv;

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or key in environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function enqueueTask() {
  try {
    const discId = argv.id;
    const delayMinutes = argv.delay;
    
    console.log(`Enqueueing generate_disc_title_pull_and_handle task for disc ID ${discId} with delay ${delayMinutes} minutes...`);
    
    // Calculate scheduled time
    let scheduledAt = new Date();
    if (delayMinutes > 0) {
      scheduledAt = new Date(scheduledAt.getTime() + delayMinutes * 60000);
    }
    
    // Create the task in the queue
    const { data, error } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'generate_disc_title_pull_and_handle',
        payload: { id: discId },
        status: 'pending',
        scheduled_at: scheduledAt.toISOString(),
        created_at: new Date().toISOString()
      })
      .select();
    
    if (error) {
      console.error(`Error creating task: ${error.message}`);
      process.exit(1);
    }
    
    console.log(`Task created successfully with ID ${data[0].id}`);
    console.log(`Scheduled for: ${scheduledAt.toISOString()}`);
    console.log(`Payload: ${JSON.stringify(data[0].payload)}`);
    
  } catch (err) {
    console.error(`Unexpected error: ${err.message}`);
    process.exit(1);
  }
}

enqueueTask();
