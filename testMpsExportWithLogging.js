import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testMpsExportWithLogging() {
  try {
    console.log('🧪 Testing MPS export with detailed logging...\n');
    
    // Get a small sample of data for testing - focus on rows 367-370
    console.log('1. Getting sample data for rows 367-370...');
    const { data: sampleData, error } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id, mold_name, plastic_name')
      .gte('excel_row_hint', 367)
      .lte('excel_row_hint', 370)
      .eq('is_orderable', true)
      .not('excel_mapping_key', 'is', null)
      .order('excel_row_hint, excel_column');

    if (error) {
      throw new Error(`Failed to query sample data: ${error.message}`);
    }

    console.log(`✅ Got ${sampleData.length} sample records`);
    console.log('Sample data:');
    sampleData.forEach((record, index) => {
      console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: MPS=${record.calculated_mps_id} | ${record.mold_name}`);
    });

    // Create MPS data for export
    console.log('\n2. Creating MPS export data...');
    const mpsData = sampleData.map(item => ({
      excel_mapping_key: item.excel_mapping_key,
      excel_column: item.excel_column,
      excel_row_hint: item.excel_row_hint,
      order: item.calculated_mps_id || 'NO_MPS', // This should show the MPS ID
      mold_name: item.mold_name,
      plastic_name: item.plastic_name
    }));

    console.log('MPS export data:');
    mpsData.forEach((record, index) => {
      console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: order=${record.order} | ${record.mold_name}`);
    });

    // Call the export API
    console.log('\n3. Calling export API...');
    const timestamp = new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0];
    
    const response = await fetch('http://localhost:3001/api/discraft/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        includeHeader: false,
        filename: `test_mps_debug_${timestamp}.xlsx`,
        orderData: mpsData
      })
    });

    if (!response.ok) {
      throw new Error(`Export API returned ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Export completed!');
    console.log(`📄 Filename: ${result.filename}`);
    console.log(`📁 File path: ${result.filePath}`);
    console.log(`📊 Records processed: ${result.totalRecords}`);
    
    console.log('\n🎯 Now check the exported file to see if the MPS IDs appear correctly in rows 367-370!');
    console.log(`File location: ${result.filePath}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testMpsExportWithLogging().catch(console.error);
