// testValidRecord.js - Test a completely valid RPRO record
import { createClient } from '@supabase/supabase-js';
import { processCheckIfRproIsReadyTask } from './processCheckIfRproIsReadyTask.js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

// Mock functions for testing
async function updateTaskStatus(taskId, status, result = null) {
  console.log(`📝 Mock updateTaskStatus: Task ${taskId} -> ${status}`);
  if (result && result.issues && result.issues.length > 0) {
    console.log(`   Issues found: ${result.issues.length}`);
    result.issues.forEach(issue => console.log(`     - ${issue}`));
  } else if (result && result.ready) {
    console.log(`   ✅ Record is ready: ${result.ready}`);
  }
  return true;
}

async function logError(message, context) {
  console.log(`❌ Mock logError: ${message} (Context: ${context})`);
  return true;
}

async function testValidRecord() {
  try {
    console.log('🧪 Testing completely valid RPRO record...');
    console.log('==========================================');

    // Get a sample record
    const { data: sampleRecord, error: sampleError } = await supabase
      .from('imported_table_rpro')
      .select('id, ivno')
      .limit(1)
      .single();

    if (sampleError) {
      console.error('❌ Error fetching sample record:', sampleError.message);
      return;
    }

    // Store original values for restoration
    const { data: originalRecord, error: origError } = await supabase
      .from('imported_table_rpro')
      .select('*')
      .eq('id', sampleRecord.id)
      .single();

    if (origError) {
      console.error('❌ Error fetching original record:', origError.message);
      return;
    }

    console.log(`📋 Testing with record ID: ${sampleRecord.id}, IVNO: ${sampleRecord.ivno}`);

    // Set up a completely valid record
    console.log('\n🔧 Setting up valid record values...');
    await supabase
      .from('imported_table_rpro')
      .update({
        // Basic fields
        ivqtylaw: 5,           // In stock
        ivaux3: 'A1-B2',       // Bin section
        ivaux2: '9 White Square Big', // Correct image
        
        // Pricing fields - all valid
        ivprcbtlis: 30,        // List Price
        ivprcbt_dollar: 25,    // Regular Price
        ivprcbtsal: 20,        // Sale Price (< Regular)
        ivprcbtliv: 25,        // Live Price (= Regular since Sale Price != 0, but we'll test with Sale = 0)
        ivprcws_1: 18,         // Wholesale 1 Price
        ivprcws_2: 16.2,       // Wholesale 2 Price (90% of Wholesale 1)
        ivavgcd: 12,           // Carrying Cost
        ivprcmsrp: 35,         // MSRP
        ivprcmap: 22,          // MAP (< MSRP and < Live Price)
        
        todo: null
      })
      .eq('id', sampleRecord.id);

    console.log('✅ Valid values set');

    // Test the handler
    const mockTask = {
      id: 999999,
      task_type: 'check_if_rpro_is_ready',
      payload: { id: sampleRecord.id }
    };

    console.log('\n🚀 Running task handler...');
    await processCheckIfRproIsReadyTask(mockTask, { supabase, updateTaskStatus, logError });

    // Check the result
    const { data: result, error: resultError } = await supabase
      .from('imported_table_rpro')
      .select('todo')
      .eq('id', sampleRecord.id)
      .single();

    if (resultError) {
      console.error('❌ Error fetching result:', resultError.message);
      return;
    }

    console.log('\n📊 Final Result:');
    console.log(`  Todo: ${result.todo}`);

    if (result.todo && result.todo.includes('No Issues Found')) {
      console.log('🎉 SUCCESS: Record passed all validation checks!');
    } else {
      console.log('❌ FAILED: Record still has issues');
    }

    // Test with Sale Price = 0 (Live Price must equal Regular Price)
    console.log('\n🧪 Testing with Sale Price = 0...');
    await supabase
      .from('imported_table_rpro')
      .update({
        ivprcbtsal: 0,         // Sale Price = 0
        ivprcbtliv: 25,        // Live Price must = Regular Price
        todo: null
      })
      .eq('id', sampleRecord.id);

    const mockTask2 = {
      id: 999998,
      task_type: 'check_if_rpro_is_ready',
      payload: { id: sampleRecord.id }
    };

    await processCheckIfRproIsReadyTask(mockTask2, { supabase, updateTaskStatus, logError });

    const { data: result2, error: resultError2 } = await supabase
      .from('imported_table_rpro')
      .select('todo')
      .eq('id', sampleRecord.id)
      .single();

    if (resultError2) {
      console.error('❌ Error fetching result 2:', resultError2.message);
      return;
    }

    console.log('\n📊 Result with Sale Price = 0:');
    console.log(`  Todo: ${result2.todo}`);

    if (result2.todo && result2.todo.includes('No Issues Found')) {
      console.log('🎉 SUCCESS: Record passed all validation checks with Sale Price = 0!');
    } else {
      console.log('❌ FAILED: Record still has issues with Sale Price = 0');
    }

    // Restore original values
    console.log('\n🔄 Restoring original values...');
    const restoreFields = {};
    Object.keys(originalRecord).forEach(key => {
      if (key !== 'id') {
        restoreFields[key] = originalRecord[key];
      }
    });

    await supabase
      .from('imported_table_rpro')
      .update(restoreFields)
      .eq('id', sampleRecord.id);

    console.log('✅ Original values restored');
    console.log('\n🎉 Valid record test completed!');

  } catch (err) {
    console.error('❌ Exception:', err.message);
    console.error(err.stack);
  }
}

testValidRecord();
