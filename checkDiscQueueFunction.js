// checkDiscQueueFunction.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Create a Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Checking fn_handle_disc_queue function...');
    
    // First, create the execute_sql function if it doesn't exist
    const createFunctionSql = `
      CREATE OR REPLACE FUNCTION execute_sql(sql TEXT)
      RETURNS JSONB
      LANGUAGE plpgsql
      AS $$
      DECLARE
        v_result JSONB;
        v_record RECORD;
      BEGIN
        EXECUTE sql INTO v_record;
        v_result := to_jsonb(v_record);
        RETURN v_result;
      EXCEPTION WHEN OTHERS THEN
        RETURN jsonb_build_object(
          'error', SQLERRM,
          'detail', SQLSTATE,
          'sql', sql
        );
      END;
      $$;
    `;
    
    try {
      await supabase.rpc('execute_sql', { sql: createFunctionSql });
      console.log('execute_sql function created or updated');
    } catch (err) {
      console.error(`Error creating execute_sql function: ${err.message}`);
    }
    
    // Now, get the fn_handle_disc_queue function definition
    const { data, error } = await supabase.rpc('execute_sql', {
      sql: `
        SELECT 
          routine_name,
          routine_definition
        FROM 
          information_schema.routines
        WHERE 
          routine_schema = 'public'
          AND routine_name = 'fn_handle_disc_queue'
          AND routine_type = 'FUNCTION'
      `
    });
    
    if (error) {
      console.error(`Error getting function: ${error.message}`);
      return;
    }
    
    if (!data || Object.keys(data).length === 0) {
      console.log('fn_handle_disc_queue function not found');
      return;
    }
    
    console.log('fn_handle_disc_queue function definition:');
    console.log(JSON.stringify(data, null, 2));
    
    // Also check for recent errors in t_error_logs
    console.log('\nChecking recent errors in t_error_logs...');
    
    const { data: errorLogs, error: errorLogsError } = await supabase
      .from('t_error_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(10);
      
    if (errorLogsError) {
      console.error(`Error getting error logs: ${errorLogsError.message}`);
      return;
    }
    
    if (!errorLogs || errorLogs.length === 0) {
      console.log('No recent error logs found');
      return;
    }
    
    console.log('Recent error logs:');
    errorLogs.forEach(log => {
      console.log(`\nID: ${log.id}`);
      console.log(`Error Message: ${log.error_message}`);
      console.log(`Context: ${log.context}`);
      console.log(`Created At: ${log.created_at}`);
    });
  } catch (err) {
    console.error(`Unexpected error: ${err.message}`);
    process.exit(1);
  }
}

main();
