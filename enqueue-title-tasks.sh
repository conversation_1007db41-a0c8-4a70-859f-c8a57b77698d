#!/bin/bash
# <PERSON>ript to run the batch enqueue title tasks with common parameters

# Default values
BATCH_SIZE=500
DELAY=1000
START_ID=0
LIMIT=0
DRY_RUN=false

# Display usage information
function show_usage {
  echo "Usage: $0 [options]"
  echo "Options:"
  echo "  -b, --batch-size NUM    Number of records to process in each batch (default: 500)"
  echo "  -d, --delay NUM         Delay in milliseconds between batches (default: 1000)"
  echo "  -s, --start-id NUM      ID to start processing from (default: 0)"
  echo "  -l, --limit NUM         Maximum number of records to process, 0 for all (default: 0)"
  echo "  --dry-run               Show what would be done without actually enqueueing tasks"
  echo "  -h, --help              Show this help message"
  echo ""
  echo "Examples:"
  echo "  $0                                  # Process all records in batches of 500"
  echo "  $0 -b 100 -d 500                    # Process in smaller batches with less delay"
  echo "  $0 -s 10000 -l 5000                 # Process 5000 records starting from ID 10000"
  echo "  $0 --dry-run                        # Dry run to see what would be processed"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case "$1" in
    -b|--batch-size)
      BATCH_SIZE="$2"
      shift 2
      ;;
    -d|--delay)
      DELAY="$2"
      shift 2
      ;;
    -s|--start-id)
      START_ID="$2"
      shift 2
      ;;
    -l|--limit)
      LIMIT="$2"
      shift 2
      ;;
    --dry-run)
      DRY_RUN=true
      shift
      ;;
    -h|--help)
      show_usage
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      show_usage
      exit 1
      ;;
  esac
done

# Build the command
CMD="node batchEnqueueTitleTasks.js --batch-size $BATCH_SIZE --delay $DELAY --start-id $START_ID --limit $LIMIT"

if [ "$DRY_RUN" = true ]; then
  CMD="$CMD --dry-run"
fi

# Display the command
echo "Running: $CMD"
echo ""

# Execute the command
eval $CMD
