// verify_t_images_trigger.js - Verify the t_images variant ready trigger was created
import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in environment');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

async function execSql(sql) {
  let res = await supabase.rpc('exec_sql', { sql_query: sql });
  if (res.error) {
    res = await supabase.rpc('exec_sql', { sql_statement: sql });
  }
  return res;
}

async function main() {
  try {
    console.log('Checking if trigger function exists...');
    const functionCheckSql = `
      SELECT proname, prosrc 
      FROM pg_proc 
      WHERE proname = 'enqueue_variant_ready_on_image_verified';
    `;
    
    let { data: functionData, error: functionError } = await execSql(functionCheckSql);
    if (functionError) {
      console.error('Error checking function:', functionError.message);
      process.exit(1);
    }
    
    if (functionData && functionData.length > 0) {
      console.log('✅ Function enqueue_variant_ready_on_image_verified exists');
    } else {
      console.log('❌ Function enqueue_variant_ready_on_image_verified not found');
    }

    console.log('\nChecking if trigger exists...');
    const triggerCheckSql = `
      SELECT tgname, tgrelid::regclass as table_name, tgfoid::regproc as function_name
      FROM pg_trigger 
      WHERE tgname = 'trg_t_images_enqueue_variant_ready_on_image_verified';
    `;
    
    let { data: triggerData, error: triggerError } = await execSql(triggerCheckSql);
    if (triggerError) {
      console.error('Error checking trigger:', triggerError.message);
      process.exit(1);
    }
    
    if (triggerData && triggerData.length > 0) {
      console.log('✅ Trigger trg_t_images_enqueue_variant_ready_on_image_verified exists');
      console.log('   Table:', triggerData[0].table_name);
      console.log('   Function:', triggerData[0].function_name);
    } else {
      console.log('❌ Trigger trg_t_images_enqueue_variant_ready_on_image_verified not found');
    }

    console.log('\nChecking all triggers on t_images table...');
    const allTriggersCheckSql = `
      SELECT tgname, tgfoid::regproc as function_name, tgtype
      FROM pg_trigger 
      WHERE tgrelid = 't_images'::regclass
      ORDER BY tgname;
    `;
    
    let { data: allTriggersData, error: allTriggersError } = await execSql(allTriggersCheckSql);
    if (allTriggersError) {
      console.error('Error checking all triggers:', allTriggersError.message);
      process.exit(1);
    }
    
    if (allTriggersData && allTriggersData.length > 0) {
      console.log('All triggers on t_images table:');
      allTriggersData.forEach(trigger => {
        console.log(`  - ${trigger.tgname} -> ${trigger.function_name}`);
      });
    } else {
      console.log('No triggers found on t_images table');
    }

  } catch (e) {
    console.error('Fatal error:', e?.message || e);
    process.exit(1);
  }
}

main();
