require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testFixedOrderThroughMpsIdWorker() {
  try {
    console.log('Testing fixed order_through_mps_id_updated worker with proper old_data...');
    
    // Create a test task manually to verify the fix
    const testMpsId = 16912; // Use the example MPS
    
    console.log(`\n=== CREATING TEST TASK ===`);
    console.log(`Creating order_through_mps_id_updated task for MPS ${testMpsId}...`);
    
    const { data: taskData, error: taskError } = await supabase
      .from('t_task_queue')
      .insert([
        {
          task_type: 'order_through_mps_id_updated',
          payload: { id: testMpsId },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: 'test_fixed_order_through_mps_id_worker'
        }
      ])
      .select();
    
    if (taskError) {
      console.error('Error creating test task:', taskError);
      return;
    }
    
    const testTaskId = taskData[0].id;
    console.log(`✅ Created test task ${testTaskId} for MPS ${testMpsId}`);
    
    // Check how many discs are related to this MPS
    const { data: relatedDiscs, error: discsError } = await supabase
      .from('t_discs')
      .select('id, vendor_osl_id', { count: 'exact' })
      .eq('mps_id', testMpsId)
      .limit(5);
    
    if (!discsError) {
      const discCount = relatedDiscs ? relatedDiscs.length : 0;
      console.log(`\nFound ${discCount} discs related to MPS ${testMpsId}`);
      
      if (discCount > 0) {
        console.log('Example discs:');
        relatedDiscs.forEach(disc => {
          console.log(`  Disc ${disc.id}: vendor_osl_id = ${disc.vendor_osl_id || 'NULL'}`);
        });
      }
    }
    
    console.log('\n🔄 Task created and ready for processing by the worker daemon.');
    console.log('The fixed worker should now:');
    console.log('1. Find all discs related to MPS 16912');
    console.log('2. Set their vendor_osl_id to null');
    console.log('3. Enqueue match_disc_to_osl tasks WITH proper old_data');
    console.log('4. The spawned tasks should NOT fail with "Missing old_data" error');
    
    console.log('\n📋 To monitor results:');
    console.log(`
-- Check the main task processing
SELECT 
  id, status, 
  result->>'message' as message,
  result->>'discs_found' as discs_found,
  result->>'tasks_enqueued' as tasks_enqueued,
  processed_at
FROM t_task_queue 
WHERE id = ${testTaskId};

-- Check spawned match_disc_to_osl tasks (should have old_data now)
SELECT 
  id, status, payload,
  result->>'message' as message,
  result->>'error' as error,
  processed_at
FROM t_task_queue 
WHERE task_type = 'match_disc_to_osl'
  AND enqueued_by = 'order_through_mps_id_updated_mps_${testMpsId}'
ORDER BY created_at DESC
LIMIT 5;
    `);
    
    console.log('\n🎯 SUCCESS INDICATORS:');
    console.log('✅ Main task should complete successfully');
    console.log('✅ Spawned match_disc_to_osl tasks should have old_data in payload');
    console.log('✅ No "Missing old_data" errors in spawned tasks');
    console.log('✅ Discs should get proper vendor OSL mappings with redirect logic');
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

testFixedOrderThroughMpsIdWorker();
