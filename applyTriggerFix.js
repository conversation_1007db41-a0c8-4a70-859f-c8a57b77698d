// applyTriggerFix.js - A script to apply the fix to the trigger function
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Create a Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to create the execute_sql function if it doesn't exist
async function createExecuteSqlFunction() {
  console.log('Creating execute_sql function...');

  try {
    // Try to create the function directly
    const createFunctionSql = `
      CREATE OR REPLACE FUNCTION execute_sql(sql TEXT)
      RETURNS JSONB
      LANGUAGE plpgsql
      AS $$
      DECLARE
        v_result JSONB;
        v_record RECORD;
      BEGIN
        EXECUTE sql INTO v_record;
        v_result := to_jsonb(v_record);
        RETURN v_result;
      EXCEPTION WHEN OTHERS THEN
        RETURN jsonb_build_object(
          'error', SQLERRM,
          'detail', SQLSTATE,
          'sql', sql
        );
      END;
      $$;
    `;

    // Use a direct query to create the function
    const { error } = await supabase
      .from('_temp_sql_execution')
      .select('*')
      .limit(1)
      .then(() => {
        // This is just to get access to the database
        // The actual function creation happens in the SQL function
        return supabase.rpc('create_execute_sql_function', { sql: createFunctionSql });
      });

    if (error) {
      console.error(`Error creating execute_sql function: ${error.message}`);
      console.error('Please create the function manually using the SQL in fix_trigger_function.sql');
      return false;
    }

    console.log('execute_sql function created successfully');
    return true;
  } catch (err) {
    console.error(`Error creating execute_sql function: ${err.message}`);
    console.error('Please create the function manually using the SQL in fix_trigger_function.sql');
    return false;
  }
}

async function main() {
  try {
    console.log('Applying fix to trigger function...');

    // Read the SQL file
    const sql = fs.readFileSync('fix_trigger_timeout.sql', 'utf8');

    // Execute the SQL
    const { error } = await supabase.rpc('execute_sql', { sql });

    if (error) {
      if (error.message.includes('function execute_sql() does not exist')) {
        console.log('The execute_sql function does not exist. Attempting to create it...');

        const created = await createExecuteSqlFunction();
        if (created) {
          // Try again
          console.log('Retrying the fix application...');
          return main();
        } else {
          console.error('Failed to create execute_sql function. Please create it manually.');
          return;
        }
      } else {
        console.error(`Error executing SQL: ${error.message}`);
      }
    } else {
      console.log('Fix applied successfully!');

      // Test the fix by inserting a record
      console.log('\nTesting the fix by inserting a record...');

      const insertData = {
        table_name: 't_discs',
        record_id: 999999
      };

      console.log(`Insert data: ${JSON.stringify(insertData)}`);

      const { data: insertResult, error: insertError } = await supabase
        .from('t_images')
        .insert(insertData);

      if (insertError) {
        if (insertError.message.includes('t_images_table_name_check')) {
          console.log('Insert failed due to table_name constraint, but this is expected for the test.');
          console.log('The fix has been applied successfully!');
        } else {
          console.error(`Error inserting record: ${insertError.message}`);
          console.error(`Full error: ${JSON.stringify(insertError)}`);
        }
      } else {
        console.log(`Insert successful: ${JSON.stringify(insertResult)}`);
      }
    }
  } catch (err) {
    console.error(`Unexpected error: ${err.message}`);
    process.exit(1);
  }
}

main();
