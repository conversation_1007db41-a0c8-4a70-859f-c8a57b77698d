import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key:', supabaseKey ? 'Found' : 'Missing');

async function importDynamicDiscsData() {
    console.log('Starting Dynamic Discs data import...');

    // Define the data files
    const dataDir = path.join(process.cwd(), 'data', 'external data');
    const files = ['ddp1.txt', 'ddp2.txt', 'ddp3.txt', 'ddp4.txt', 'ddp5.txt'];

    console.log('Data directory:', dataDir);
    console.log('Looking for files:', files);

    let totalRecords = 0;
    let totalProducts = 0;

    try {
        // Test connection first
        console.log('Testing Supabase connection...');
        const { data: testData, error: testError } = await supabase
            .from('test_dd_data')
            .select('id')
            .limit(1);

        if (testError) {
            console.error('Error connecting to Supabase or table does not exist:', testError);
            console.log('Please make sure you have created the test_dd_data table first using create_test_dd_data_table.sql');
            return;
        }

        console.log('Connection successful. Table exists and is accessible.');

        // Clear existing data
        console.log('Clearing existing test data...');
        const { error: deleteError } = await supabase
            .from('test_dd_data')
            .delete()
            .neq('id', 0); // Delete all records

        if (deleteError) {
            console.error('Error clearing existing data:', deleteError);
            return;
        }

        console.log('Existing data cleared successfully.');
        
        // Process each file
        for (const fileName of files) {
            const filePath = path.join(dataDir, fileName);
            
            if (!fs.existsSync(filePath)) {
                console.log(`File ${fileName} not found, skipping...`);
                continue;
            }
            
            console.log(`Processing ${fileName}...`);
            
            try {
                // Read and parse JSON file
                const fileContent = fs.readFileSync(filePath, 'utf8');
                const jsonData = JSON.parse(fileContent);
                
                if (!jsonData.products || !Array.isArray(jsonData.products)) {
                    console.log(`No products found in ${fileName}, skipping...`);
                    continue;
                }
                
                console.log(`Found ${jsonData.products.length} products in ${fileName}`);
                totalProducts += jsonData.products.length;
                
                // Process products in batches
                const batchSize = 100;
                const products = jsonData.products;
                
                for (let i = 0; i < products.length; i += batchSize) {
                    const batch = products.slice(i, i + batchSize);
                    const records = [];
                    
                    // Convert products to database records
                    for (const product of batch) {
                        const productRecords = convertProductToRecords(product, fileName);
                        records.push(...productRecords);
                    }
                    
                    if (records.length > 0) {
                        // Insert batch into database
                        const { error: insertError } = await supabase
                            .from('test_dd_data')
                            .insert(records);
                        
                        if (insertError) {
                            console.error(`Error inserting batch from ${fileName}:`, insertError);
                            console.error('Sample record:', JSON.stringify(records[0], null, 2));
                        } else {
                            totalRecords += records.length;
                            console.log(`Inserted ${records.length} records from ${fileName} (batch ${Math.floor(i/batchSize) + 1})`);
                        }
                    }
                }
                
            } catch (fileError) {
                console.error(`Error processing ${fileName}:`, fileError.message);
            }
        }
        
        console.log('\n=== IMPORT SUMMARY ===');
        console.log(`Total products processed: ${totalProducts}`);
        console.log(`Total records inserted: ${totalRecords}`);
        
        // Get some statistics
        const { data: stats, error: statsError } = await supabase
            .from('test_dd_data')
            .select('product_vendor, product_product_type, variant_available')
            .limit(1000);
        
        if (!statsError && stats) {
            const vendors = [...new Set(stats.map(r => r.product_vendor))];
            const productTypes = [...new Set(stats.map(r => r.product_product_type))];
            const availableCount = stats.filter(r => r.variant_available).length;
            
            console.log(`Unique vendors: ${vendors.length} (${vendors.join(', ')})`);
            console.log(`Product types: ${productTypes.length} (${productTypes.join(', ')})`);
            console.log(`Available variants: ${availableCount}/${stats.length}`);
        }
        
    } catch (error) {
        console.error('Import failed:', error);
    }
}

function convertProductToRecords(product, sourceFile) {
    const records = [];
    
    // Extract product-level data
    const productData = {
        product_id: product.id,
        product_title: product.title,
        product_handle: product.handle,
        product_body_html: product.body_html,
        product_published_at: product.published_at ? new Date(product.published_at).toISOString() : null,
        product_created_at: product.created_at ? new Date(product.created_at).toISOString() : null,
        product_updated_at: product.updated_at ? new Date(product.updated_at).toISOString() : null,
        product_vendor: product.vendor,
        product_product_type: product.product_type,
        product_tags: product.tags || [],
        product_available: product.available,
        source_file: sourceFile
    };
    
    // Extract options data
    const options = product.options || [];
    const optionsData = {
        option1_name: options[0]?.name || null,
        option1_values: options[0]?.values || [],
        option2_name: options[1]?.name || null,
        option2_values: options[1]?.values || [],
        option3_name: options[2]?.name || null,
        option3_values: options[2]?.values || []
    };
    
    // Process each variant
    if (product.variants && Array.isArray(product.variants)) {
        for (const variant of product.variants) {
            const record = {
                ...productData,
                ...optionsData,
                
                // Variant data
                variant_id: variant.id,
                variant_title: variant.title,
                variant_option1: variant.option1,
                variant_option2: variant.option2,
                variant_option3: variant.option3,
                variant_sku: variant.sku,
                variant_requires_shipping: variant.requires_shipping,
                variant_taxable: variant.taxable,
                variant_available: variant.available,
                variant_price: variant.price ? parseFloat(variant.price) : null,
                variant_grams: variant.grams,
                variant_compare_at_price: variant.compare_at_price ? parseFloat(variant.compare_at_price) : null,
                variant_position: variant.position,
                variant_created_at: variant.created_at ? new Date(variant.created_at).toISOString() : null,
                variant_updated_at: variant.updated_at ? new Date(variant.updated_at).toISOString() : null,
                
                // Featured image data
                variant_featured_image_id: variant.featured_image?.id || null,
                variant_featured_image_src: variant.featured_image?.src || null,
                variant_featured_image_width: variant.featured_image?.width || null,
                variant_featured_image_height: variant.featured_image?.height || null
            };
            
            records.push(record);
        }
    } else {
        // Product without variants - create a single record
        const record = {
            ...productData,
            ...optionsData,
            
            // No variant data
            variant_id: null,
            variant_title: null,
            variant_option1: null,
            variant_option2: null,
            variant_option3: null,
            variant_sku: null,
            variant_requires_shipping: null,
            variant_taxable: null,
            variant_available: null,
            variant_price: null,
            variant_grams: null,
            variant_compare_at_price: null,
            variant_position: null,
            variant_created_at: null,
            variant_updated_at: null,
            variant_featured_image_id: null,
            variant_featured_image_src: null,
            variant_featured_image_width: null,
            variant_featured_image_height: null
        };
        
        records.push(record);
    }
    
    return records;
}

// Run the import
console.log('Script starting...');
console.log('Running import function...');

importDynamicDiscsData()
    .then(() => {
        console.log('Import completed successfully');
        process.exit(0);
    })
    .catch(error => {
        console.error('Import failed:', error);
        console.error('Error stack:', error.stack);
        process.exit(1);
    });

export { importDynamicDiscsData };
