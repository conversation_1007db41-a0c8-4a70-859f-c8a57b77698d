import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

/**
 * Clean up Keepa notes to contain only the product title
 */
async function cleanKeepaNotesToTitleOnly() {
  try {
    console.log('Finding Keepa records with detailed notes...');
    
    // Find records with Keepa notes that contain "Title:" line
    const { data: recordsToClean, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, asin, notes')
      .like('notes', '%Competitor product discovered via Keepa%')
      .like('notes', '%Title:%');
    
    if (fetchError) {
      console.error('Error fetching records to clean:', fetchError);
      return;
    }
    
    if (!recordsToClean || recordsToClean.length === 0) {
      console.log('No Keepa records found that need cleaning.');
      return;
    }
    
    console.log(`Found ${recordsToClean.length} Keepa records that need cleaning.`);
    
    let cleanedCount = 0;
    let errorCount = 0;
    let skippedCount = 0;
    
    for (const record of recordsToClean) {
      try {
        // Extract just the title from the notes
        const titleMatch = record.notes.match(/Title:\s*(.+?)(?:\n|$)/);
        
        if (titleMatch && titleMatch[1]) {
          const titleOnly = titleMatch[1].trim();
          
          // Update the record with just the title
          const { error: updateError } = await supabase
            .from('t_sdasins')
            .update({ notes: titleOnly })
            .eq('id', record.id);
          
          if (updateError) {
            console.error(`Error updating ASIN ${record.asin}:`, updateError);
            errorCount++;
          } else {
            console.log(`Cleaned ASIN ${record.asin}: "${titleOnly.substring(0, 60)}..."`);
            cleanedCount++;
          }
        } else {
          console.log(`Skipped ASIN ${record.asin}: Could not extract title`);
          skippedCount++;
        }
        
      } catch (error) {
        console.error(`Error processing ASIN ${record.asin}:`, error);
        errorCount++;
      }
    }
    
    console.log(`\nCleaning summary:`);
    console.log(`- Records cleaned: ${cleanedCount}`);
    console.log(`- Records skipped: ${skippedCount}`);
    console.log(`- Errors: ${errorCount}`);
    
  } catch (error) {
    console.error('Error in cleanKeepaNotesToTitleOnly:', error);
  }
}

/**
 * Main function
 */
async function main() {
  console.log('=== Keepa Notes Cleanup ===');
  console.log(`Started at: ${new Date().toISOString()}\n`);
  
  try {
    await cleanKeepaNotesToTitleOnly();
    console.log('\n=== Cleanup Complete ===');
  } catch (error) {
    console.error('Fatal error in main process:', error);
    process.exit(1);
  }
}

// Run the script
main();
