-- Drop the view if it exists
DROP VIEW IF EXISTS v_duplicate_veeqo_skus_details;

-- Create a view to show the detailed records for duplicate SKU codes
CREATE VIEW v_duplicate_veeqo_skus_details AS
SELECT
    v.sku_code,
    v.occurrence_count,
    e.product_id,
    e.product_title,
    e.variant_title,
    e.sales_price,
    e.cost_price,
    e.brand,
    e.upc_code,
    e.image_url,
    e.country_origin_code,
    e.weight,
    e.weight_unit,
    e.min_reorder_level,
    e.quantity_to_reorder,
    e.max_reorder_level,
    e.width,
    e.depth,
    e.height,
    e.dimensions_unit,
    e.tax_rate,
    e.estimated_delivery,
    e.tags,
    e.product_properties,
    e.variant_options,
    e.tariff_code,
    e.hazmat,
    e.product_hazmat,
    e.qty_on_hand,
    e.total_qty,
    e.total_stock_value
FROM
    v_duplicate_veeqo_skus v
JOIN
    imported_table_veeqo_sellables_export e ON v.sku_code = e.sku_code
ORDER BY
    v.occurrence_count DESC,
    v.sku_code,
    e.product_title;

-- Comment explaining the view
COMMENT ON VIEW v_duplicate_veeqo_skus_details IS 'Shows the detailed records for SKU codes that have duplicates in the imported_table_veeqo_sellables_export table.';
