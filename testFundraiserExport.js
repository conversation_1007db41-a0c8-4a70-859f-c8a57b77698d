import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testFundraiserExport() {
    try {
        console.log('🧪 Testing fundraiser export...\n');
        
        // 1. Check current fundraiser records
        console.log('1. Checking current fundraiser records...');
        const { data: fundraiserRecords, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, mold_name, plastic_name, stamp_name, calculated_mps_id, excel_mapping_key')
            .eq('excel_column', 'A')
            .ilike('raw_model', '%Fundraiser - Ben <PERSON>ren%')
            .order('excel_row_hint');

        if (error) {
            console.error('❌ Error querying fundraiser records:', error);
            return;
        }

        console.log(`✅ Found ${fundraiserRecords.length} fundraiser records:`);
        fundraiserRecords.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.plastic_name} ${record.mold_name}`);
            console.log(`      Stamp: ${record.stamp_name}`);
            console.log(`      MPS ID: ${record.calculated_mps_id || 'NOT MATCHED'}`);
            console.log(`      Mapping: ${record.excel_mapping_key}`);
            console.log('');
        });

        if (fundraiserRecords.length === 0) {
            console.log('❌ No fundraiser records found - cannot test export');
            return;
        }

        // 2. Test order quantity export
        console.log('2. Testing order quantity export...');
        
        // Get all orderable data for export test
        const { data: allOrderableData, error: orderableError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null);

        if (orderableError) {
            console.error('❌ Error getting orderable data:', orderableError);
            return;
        }

        console.log(`✅ Found ${allOrderableData.length} total orderable records`);

        // Create test order data (with 0 quantities for testing)
        const orderData = allOrderableData.map(item => ({
            ...item,
            order: 0 // Test with 0 quantities
        }));

        // Set fundraiser items to have test quantities
        const fundraiserOrderData = orderData.map(item => {
            if (item.excel_column === 'A' && item.excel_row_hint === 25) {
                return { ...item, order: 5 }; // Test quantity for Thrasher
            }
            if (item.excel_column === 'A' && item.excel_row_hint === 28) {
                return { ...item, order: 3 }; // Test quantity for Buzzz
            }
            return item;
        });

        console.log('Setting test quantities: Thrasher=5, Buzzz=3');

        // Test export
        const timestamp = new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0];
        
        const response = await fetch('http://localhost:3001/api/discraft/export', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                includeHeader: false,
                filename: `test_fundraiser_export_${timestamp}.xlsx`,
                orderData: fundraiserOrderData
            })
        });

        if (!response.ok) {
            throw new Error(`Export API returned ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('✅ Order quantity export completed!');
        console.log(`📄 Filename: ${result.filename}`);
        console.log(`📊 Records processed: ${result.totalRecords}`);

        // 3. Test MPS export
        console.log('\n3. Testing MPS export...');
        
        const mpsData = allOrderableData.map(item => ({
            ...item,
            order: item.calculated_mps_id || 'NO_MPS'
        }));

        const mpsResponse = await fetch('http://localhost:3001/api/discraft/export', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                includeHeader: false,
                filename: `test_fundraiser_mps_${timestamp}.xlsx`,
                orderData: mpsData
            })
        });

        if (!mpsResponse.ok) {
            throw new Error(`MPS export API returned ${mpsResponse.status}: ${mpsResponse.statusText}`);
        }

        const mpsResult = await mpsResponse.json();
        console.log('✅ MPS export completed!');
        console.log(`📄 Filename: ${mpsResult.filename}`);
        console.log(`📊 Records processed: ${mpsResult.totalRecords}`);

        console.log('\n🎯 Test Results:');
        console.log('   • Fundraiser records are in column A (correct)');
        console.log('   • Order quantities should appear in column A for rows 25 and 28');
        console.log('   • MPS IDs should appear in column A for rows 25 and 28');
        console.log('   • Row 22 should NOT have any orderable items (header fixed)');
        
        console.log(`\n📁 Check these files:`);
        console.log(`   Order quantities: ${result.filePath}`);
        console.log(`   MPS verification: ${mpsResult.filePath}`);
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testFundraiserExport().catch(console.error);
