// fix_remaining_placeholder_osls.js
// Fix the remaining placeholder OSLs that still have incorrect ready_button/ready status

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function fixRemainingPlaceholderOSLs() {
  try {
    console.log('🔍 Finding remaining placeholder OSLs that need fixing...');
    
    // Find OSLs with max_weight < 10g that don't have both ready_button=false AND ready=false
    const { data: oslsToFix, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, max_weight, ready_button, ready, todo')
      .lt('max_weight', 10)
      .or('ready_button.eq.true,ready.eq.true');
    
    if (oslError) {
      console.error('❌ Error fetching OSLs:', oslError);
      return;
    }
    
    console.log(`📋 Found ${oslsToFix.length} placeholder OSLs that need fixing:`);
    
    if (oslsToFix.length === 0) {
      console.log('✅ No OSLs need fixing');
      return;
    }
    
    // Display the OSLs that will be fixed
    oslsToFix.forEach(osl => {
      console.log(`📝 OSL ${osl.id} (${osl.max_weight}g): ready_button=${osl.ready_button}, ready=${osl.ready}`);
    });
    
    console.log(`\n🧹 Starting fix process...`);
    
    let updatedOSLs = 0;
    let errors = 0;
    
    for (const osl of oslsToFix) {
      try {
        console.log(`🔧 Fixing OSL ${osl.id} (${osl.max_weight}g)...`);
        
        // Update OSL: set ready_button = FALSE and ready = FALSE
        const { error: updateError } = await supabase
          .from('t_order_sheet_lines')
          .update({
            ready_button: false,
            ready: false,
            todo: `Placeholder OSL (max_weight ${osl.max_weight}g < 10g) - not ready for publishing`
          })
          .eq('id', osl.id);
        
        if (updateError) {
          console.error(`❌ Error updating OSL ${osl.id}:`, updateError.message);
          errors++;
          continue;
        }
        
        console.log(`✅ Fixed OSL ${osl.id}: ready_button=false, ready=false`);
        updatedOSLs++;
        
      } catch (err) {
        console.error(`❌ Error processing OSL ${osl.id}:`, err.message);
        errors++;
      }
    }
    
    console.log(`\n📊 Fix Summary:`);
    console.log(`✅ OSLs updated: ${updatedOSLs}`);
    console.log(`❌ Errors: ${errors}`);
    
    if (errors === 0) {
      console.log(`\n🎉 All remaining placeholder OSLs fixed successfully!`);
    } else {
      console.log(`\n⚠️  Fix completed with ${errors} errors. Please review the error messages above.`);
    }
    
    // Final verification
    console.log('\n🔍 Final verification...');
    const { data: finalCheck, error: finalError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, max_weight, ready_button, ready')
      .lt('max_weight', 10)
      .or('ready_button.eq.true,ready.eq.true');
    
    if (finalError) {
      console.error('❌ Error in final verification:', finalError);
      return;
    }
    
    if (finalCheck.length === 0) {
      console.log('✅ Final verification PASSED: All placeholder OSLs are now correctly set to not ready');
    } else {
      console.log(`❌ Final verification FAILED: ${finalCheck.length} OSLs still need fixing:`);
      finalCheck.forEach(osl => {
        console.log(`   OSL ${osl.id} (${osl.max_weight}g): ready_button=${osl.ready_button}, ready=${osl.ready}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Unexpected error during fix:', error.message);
  }
}

// Run the fix
fixRemainingPlaceholderOSLs();
