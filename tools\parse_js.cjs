const fs = require('fs');
const txt = fs.readFileSync('admin.html','utf8');
const start = txt.indexOf('<script>');
const end = txt.lastIndexOf('</script>');
const js = txt.slice(start + '<script>'.length, end);
try {
  // Wrap as an IIFE to emulate script context
  const wrapped = '(function(){\n' + js.replace(/\\/g,'\\\\') + '\n})();';
  new Function(wrapped);
  console.log('Parse OK');
} catch (e) {
  console.log('Parse error:', e.message);
  if (e.stack) console.log(e.stack.split('\n')[0]);
}

