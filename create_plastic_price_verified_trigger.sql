-- Function to enqueue a task when price_cost_verified_at is updated
CREATE OR REPLACE FUNCTION fn_enqueue_plastic_price_verified_task()
RETURNS TRIGGER AS $$
BEGIN
    -- Only enqueue a task if price_cost_verified_at has been updated and is within the last 30 days
    IF OLD.price_cost_verified_at IS DISTINCT FROM NEW.price_cost_verified_at 
       AND NEW.price_cost_verified_at IS NOT NULL
       AND NEW.price_cost_verified_at > (NOW() - INTERVAL '30 days') THEN
        
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'plastic_price_verified_work_through_mps_to_find_osls_and_discs_to_upload',
            jsonb_build_object('id', NEW.id),
            'pending',
            NOW(),
            NOW()
        );
        
        -- Log the task creation
        INSERT INTO t_error_logs(error_message, created_at, context, created_by)
        VALUES (
            'Enqueued plastic_price_verified_work_through_mps_to_find_osls_and_discs_to_upload task for Plastic ID ' || NEW.id,
            NOW(),
            'fn_enqueue_plastic_price_verified_task',
            'trigger'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS tr_enqueue_plastic_price_verified_task ON t_plastics;

CREATE TRIGGER tr_enqueue_plastic_price_verified_task
AFTER UPDATE OF price_cost_verified_at ON t_plastics
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_plastic_price_verified_task();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Plastic price verified task enqueuer function and trigger created.';
END $$;
