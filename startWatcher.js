// startWatcher.js
import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the watcher script
const watcherScript = path.join(__dirname, 'imageWatcher.js');

// Create a log file
const logFile = path.join(__dirname, 'watcher_service.log');
const logMessage = (message) => {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] ${message}\n`;
  console.log(logEntry.trim());
  fs.appendFileSync(logFile, logEntry);
};

// Start the watcher process
logMessage('Starting image watcher service...');

const watcherProcess = spawn('node', [watcherScript], {
  detached: true,
  stdio: 'ignore'
});

// Detach the process so it runs independently
watcherProcess.unref();

logMessage(`Image watcher service started with PID: ${watcherProcess.pid}`);
logMessage('You can close this terminal window. The watcher will continue running in the background.');

// Exit this process
process.exit(0);
