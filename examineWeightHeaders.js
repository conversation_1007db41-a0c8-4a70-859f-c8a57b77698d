import XLSX from 'xlsx';

const inputFile = 'data/external data/discraftstock.xlsx';

try {
  console.log('Examining weight headers in Discraft Excel file...\n');
  
  const workbook = XLSX.readFile(inputFile);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
  
  console.log(`Excel file has ${data.length} rows`);
  
  // Look for weight header rows around the PP Z Lite section (around row 152-158)
  console.log('\n--- Examining rows 150-160 for weight headers ---');
  for (let i = 150; i <= 160; i++) {
    if (data[i]) {
      console.log(`Row ${i + 1}:`, data[i].slice(0, 20)); // Show first 20 columns
    }
  }
  
  // Look specifically at the header row for PP Z Lite section
  console.log('\n--- Looking for weight headers in PP Z Lite section ---');
  for (let i = 150; i <= 160; i++) {
    const row = data[i];
    if (row && row[0] === 'Line' && row[3] === 'Model') {
      console.log(`\nFound header row at ${i + 1}:`);
      for (let col = 0; col < row.length; col++) {
        if (row[col]) {
          const columnLetter = String.fromCharCode(65 + col); // A, B, C, etc.
          console.log(`  Column ${columnLetter} (${col}): "${row[col]}"`);
        }
      }
      
      // Look specifically at columns L, M, N, O, P, Q, R (indices 11-17)
      console.log('\nWeight columns (L-R):');
      for (let col = 11; col <= 17; col++) {
        const columnLetter = String.fromCharCode(65 + col);
        const value = row[col];
        console.log(`  Column ${columnLetter} (${col}): "${value || 'empty'}"`);
      }
    }
  }
  
  // Also check the data rows to see what's actually in the weight columns
  console.log('\n--- Sample data from PP Z Lite rows ---');
  for (let i = 152; i <= 158; i++) {
    const row = data[i];
    if (row && row[0] && row[0].includes('PP Z Lite')) {
      console.log(`\nRow ${i + 1} (${row[0]} ${row[3]}):`);
      for (let col = 11; col <= 17; col++) {
        const columnLetter = String.fromCharCode(65 + col);
        const value = row[col];
        console.log(`  Column ${columnLetter}: "${value || 'empty'}"`);
      }
    }
  }
  
} catch (err) {
  console.error('Error:', err.message);
}
