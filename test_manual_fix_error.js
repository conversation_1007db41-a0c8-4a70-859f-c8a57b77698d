// test_manual_fix_error.js
// Create a test task for OSL 18500 to test the improved error extraction

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function createTestTask() {
  try {
    console.log('Creating test task for OSL 18500...');
    
    const { data, error } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'publish_product_osl',
        payload: { id: 18500 },
        status: 'pending',
        scheduled_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        enqueued_by: 'debug_error_extraction'
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error creating task:', error);
      return;
    }
    
    console.log('✅ Created test task:', data.id);
    console.log('This should now extract the MANUAL FIX REQUIRED error message properly');
    console.log('Run the worker to process this task');
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

createTestTask();
