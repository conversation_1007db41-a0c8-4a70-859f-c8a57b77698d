const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!supabaseUrl || !supabaseKey || !veeqoApiKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

console.log('🔍 VEEQO LISTINGS EXPLORER');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqo<PERSON><PERSON>Key
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to get SDASIN record by ID
async function getSdasinRecord(sdasinId) {
  console.log(`🔍 Getting SDASIN record for ID: ${sdasinId}`);
  
  const { data, error } = await supabase
    .from('t_sdasins')
    .select('id, fbm_sku, fbm_uploaded_at, asin, parent_asin, veeqo_id')
    .eq('id', sdasinId)
    .single();
  
  if (error) {
    console.error(`❌ Error fetching SDASIN record: ${error.message}`);
    return null;
  }
  
  return data;
}

// Function to find Veeqo product by SKU and explore its structure
async function exploreVeeqoProductBySku(sku) {
  console.log(`\n🔍 Exploring Veeqo product structure for SKU: ${sku}`);
  
  // First check the imported table
  const { data: importedData, error: importedError } = await supabase
    .from('imported_table_veeqo_sellables_export')
    .select('product_id, sku_code, product_title')
    .eq('sku_code', sku)
    .limit(1);
  
  if (importedError) {
    console.error(`❌ Error querying imported Veeqo table: ${importedError.message}`);
    return null;
  }
  
  if (!importedData || importedData.length === 0) {
    console.log(`❌ SKU ${sku} not found in imported Veeqo table`);
    return null;
  }
  
  const productId = importedData[0].product_id;
  console.log(`✅ Found product ID: ${productId} for SKU: ${sku}`);
  
  // Get full product details from Veeqo API
  console.log(`📋 Getting full product details from Veeqo API...`);
  const productResult = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!productResult.success) {
    console.error(`❌ Failed to get product details: ${productResult.error}`);
    return null;
  }
  
  const product = productResult.data;
  
  console.log(`\n📊 PRODUCT STRUCTURE ANALYSIS:`);
  console.log(`Product ID: ${product.id}`);
  console.log(`Product Title: ${product.title}`);
  console.log(`Created: ${product.created_at}`);
  console.log(`Updated: ${product.updated_at}`);
  
  // Analyze sellables (variants)
  if (product.sellables && product.sellables.length > 0) {
    console.log(`\n🏷️  SELLABLES (${product.sellables.length}):`);
    product.sellables.forEach((sellable, index) => {
      console.log(`  ${index + 1}. Sellable ID: ${sellable.id}`);
      console.log(`     SKU: ${sellable.sku_code}`);
      console.log(`     Title: ${sellable.title}`);
      console.log(`     Price: ${sellable.price}`);
      console.log(`     Stock: ${sellable.available_stock_level_at_all_warehouses || 0}`);
      console.log('');
    });
  }
  
  // Analyze channel products (listings)
  if (product.channel_products && product.channel_products.length > 0) {
    console.log(`\n📺 CHANNEL PRODUCTS/LISTINGS (${product.channel_products.length}):`);
    product.channel_products.forEach((channelProduct, index) => {
      console.log(`  ${index + 1}. Channel Product ID: ${channelProduct.id}`);
      console.log(`     Channel: ${channelProduct.channel?.name || 'Unknown'} (${channelProduct.channel?.short_name || 'N/A'})`);
      console.log(`     Channel Type: ${channelProduct.channel?.type_code || 'Unknown'}`);
      console.log(`     Status: ${channelProduct.status}`);
      console.log(`     Remote ID: ${channelProduct.remote_id}`);
      console.log(`     Remote Title: ${channelProduct.remote_title}`);
      
      if (channelProduct.channel_sellables && channelProduct.channel_sellables.length > 0) {
        console.log(`     Channel Sellables (${channelProduct.channel_sellables.length}):`);
        channelProduct.channel_sellables.forEach((channelSellable, csIndex) => {
          console.log(`       ${csIndex + 1}. Channel Sellable ID: ${channelSellable.id}`);
          console.log(`          Remote SKU: ${channelSellable.remote_sku}`);
          console.log(`          Remote Price: ${channelSellable.remote_price}`);
          console.log(`          Status: ${channelSellable.status || 'N/A'}`);
        });
      }
      console.log('');
    });
  }
  
  return {
    product,
    productId,
    channelProducts: product.channel_products || [],
    sellables: product.sellables || []
  };
}

// Function to test different deletion endpoints
async function testDeletionEndpoints(productData, sku) {
  console.log(`\n🧪 TESTING DELETION ENDPOINTS FOR SKU: ${sku}`);
  console.log('='.repeat(60));
  
  const { product, channelProducts } = productData;
  
  if (channelProducts.length === 0) {
    console.log('❌ No channel products found to test deletion on');
    return;
  }
  
  // Test different possible endpoints (DRY RUN - just check what endpoints exist)
  const testEndpoints = [
    `https://api.veeqo.com/channel_products/${channelProducts[0].id}`,
    `https://api.veeqo.com/products/${product.id}/channel_products/${channelProducts[0].id}`,
    `https://api.veeqo.com/channels/${channelProducts[0].channel?.id}/products/${channelProducts[0].id}`,
    `https://api.veeqo.com/listings/${channelProducts[0].id}`,
    `https://api.veeqo.com/products/${product.id}/listings/${channelProducts[0].id}`
  ];
  
  console.log(`🎯 Testing endpoints for Channel Product ID: ${channelProducts[0].id}`);
  console.log(`   Channel: ${channelProducts[0].channel?.name || 'Unknown'}`);
  console.log(`   Status: ${channelProducts[0].status}`);
  console.log(`   Remote ID: ${channelProducts[0].remote_id}`);
  
  for (const endpoint of testEndpoints) {
    console.log(`\n🔍 Testing GET ${endpoint}...`);
    const result = await makeVeeqoRequest(endpoint, 'GET');
    
    if (result.success) {
      console.log(`   ✅ GET successful - endpoint exists`);
      console.log(`   📊 Response keys: ${Object.keys(result.data).join(', ')}`);
    } else {
      console.log(`   ❌ GET failed: ${result.error}`);
    }
  }
  
  // Test if we can update the channel product status
  console.log(`\n🔄 Testing UPDATE operations...`);
  const updateEndpoint = `https://api.veeqo.com/channel_products/${channelProducts[0].id}`;
  
  // Try to get current status first
  const currentResult = await makeVeeqoRequest(updateEndpoint, 'GET');
  if (currentResult.success) {
    console.log(`✅ Current channel product status: ${currentResult.data.status}`);
    
    // Test what happens if we try to update to 'inactive' or 'deleted'
    const testStatuses = ['inactive', 'deleted', 'disabled', 'removed'];
    
    for (const testStatus of testStatuses) {
      console.log(`🧪 Testing status update to '${testStatus}' (DRY RUN)...`);
      // We won't actually make this call, just show what we would try
      console.log(`   Would try: PUT ${updateEndpoint} with { "channel_product": { "status": "${testStatus}" } }`);
    }
  }
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🛠️  USAGE:
  node exploreVeeqoListings.cjs <sdasin_id>

📝 EXAMPLE:
  node exploreVeeqoListings.cjs 46948

This tool will:
1. Get the SDASIN record and its FBM SKU
2. Find the corresponding Veeqo product
3. Analyze the product structure (sellables, channel products/listings)
4. Test different API endpoints for deletion
`);
    return;
  }
  
  const sdasinId = parseInt(args[0]);
  
  try {
    // Get SDASIN record
    const sdasin = await getSdasinRecord(sdasinId);
    if (!sdasin) {
      console.error('❌ SDASIN record not found');
      return;
    }
    
    console.log(`📋 SDASIN Record:`);
    console.log(`   ID: ${sdasin.id}`);
    console.log(`   FBM SKU: ${sdasin.fbm_sku}`);
    console.log(`   FBM Uploaded At: ${sdasin.fbm_uploaded_at}`);
    console.log(`   ASIN: ${sdasin.asin}`);
    console.log(`   Veeqo ID: ${sdasin.veeqo_id}`);
    
    if (!sdasin.fbm_sku) {
      console.error('❌ No FBM SKU found for this SDASIN');
      return;
    }
    
    // Explore Veeqo product structure
    const productData = await exploreVeeqoProductBySku(sdasin.fbm_sku);
    if (!productData) {
      console.error('❌ Could not find or analyze Veeqo product');
      return;
    }
    
    // Test deletion endpoints
    await testDeletionEndpoints(productData, sdasin.fbm_sku);
    
    console.log(`\n💡 SUMMARY:`);
    console.log(`- Found Veeqo product ID: ${productData.productId}`);
    console.log(`- Product has ${productData.channelProducts.length} channel product(s)/listing(s)`);
    console.log(`- Product has ${productData.sellables.length} sellable(s)/variant(s)`);
    console.log(`- To delete a listing, you likely need to DELETE the channel_product`);
    console.log(`- To delete the entire product, you would DELETE the product itself`);
    
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the main function
main();
