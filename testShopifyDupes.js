import findDuplicates from './findDuplicateShopifyProducts.js';
import deleteProducts from './deleteDuplicateShopifyProducts.js';

async function testFindDuplicates() {
  console.log('Testing findDuplicateShopifyProducts...');
  
  try {
    const result = await findDuplicates();
    console.log('Find duplicates result:', result);
    
    if (result.success) {
      console.log(`✅ Successfully found ${result.totalFound} duplicate products`);
      
      if (result.duplicates.length > 0) {
        console.log('\nFirst few duplicates:');
        result.duplicates.slice(0, 5).forEach((product, index) => {
          console.log(`${index + 1}. ${product.handle} - ${product.title}`);
        });
      }
    } else {
      console.log('❌ Find duplicates failed:', result.error);
    }
    
    return result;
  } catch (error) {
    console.error('❌ Error testing find duplicates:', error);
    return { success: false, error: error.message };
  }
}

async function testDeleteProducts(productIds) {
  console.log(`\nTesting deleteDuplicateShopifyProducts with ${productIds.length} products...`);
  
  try {
    const result = await deleteProducts(productIds);
    console.log('Delete products result:', result);
    
    if (result.success) {
      console.log(`✅ Successfully processed ${result.totalProcessed} products`);
      console.log(`   Deleted: ${result.deletedCount}`);
      console.log(`   Errors: ${result.errorCount}`);
      
      if (result.errors.length > 0) {
        console.log('\nErrors:');
        result.errors.forEach(error => console.log(`   - ${error}`));
      }
    } else {
      console.log('❌ Delete products failed:', result.error);
    }
    
    return result;
  } catch (error) {
    console.error('❌ Error testing delete products:', error);
    return { success: false, error: error.message };
  }
}

async function main() {
  console.log('🧪 Testing Shopify Duplicate Products Scripts\n');
  
  // Test finding duplicates
  const findResult = await testFindDuplicates();
  
  // Only test deletion if we have some duplicates and user confirms
  if (findResult.success && findResult.duplicates.length > 0) {
    console.log('\n⚠️  Note: Deletion testing is disabled by default to prevent accidental deletions.');
    console.log('To test deletion, uncomment the deletion test code in this script.');
    
    // Uncomment the lines below to test deletion (BE CAREFUL!)
    /*
    const testIds = findResult.duplicates.slice(0, 1).map(p => p.id); // Only test with 1 product
    console.log(`\nWould test deletion with product ID: ${testIds[0]}`);
    // const deleteResult = await testDeleteProducts(testIds);
    */
  }
  
  console.log('\n🏁 Testing completed!');
}

// Run the test
main().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});
