-- Function to enqueue a task for checking if an OSL is ready
CREATE OR REPLACE FUNCTION fn_enqueue_check_if_osl_is_ready()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert a task into the task queue, ignoring duplicates enforced by the unique index
    BEGIN
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'check_if_osl_is_ready',
            jsonb_build_object('id', NEW.id),
            'pending',
            NOW(),
            NOW()
        );
    EXCEPTION WHEN unique_violation THEN
        -- Another pending/processing check_if_osl_is_ready for this OSL already exists; skip
        NULL;
    END;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the old trigger if it exists
DROP TRIGGER IF EXISTS tr_enqueue_check_if_osl_is_ready ON t_order_sheet_lines;

-- Create the new trigger
CREATE TRIGGER tr_enqueue_check_if_osl_is_ready
AFTER INSERT OR UPDATE OF mps_id, min_weight, max_weight, color_id, ready_button
ON t_order_sheet_lines
FOR EACH ROW
WHEN (NEW.shopify_uploaded_at IS NULL)
EXECUTE FUNCTION fn_enqueue_check_if_osl_is_ready();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger tr_enqueue_check_if_osl_is_ready has been created to enqueue a task.';
END $$;
