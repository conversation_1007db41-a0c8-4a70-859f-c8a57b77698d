-- Function to enqueue a task for matching a disc to SDASINs
CREATE OR REPLACE FUNCTION fn_enqueue_match_disc_to_asins()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert a task into the task queue
    INSERT INTO t_task_queue (
        task_type,
        payload,
        status,
        scheduled_at,
        created_at,
        enqueued_by
    ) VALUES (
        'match_disc_to_asins',
        jsonb_build_object('id', NEW.id),
        'pending',
        NOW(),
        NOW(),
        't_discs insert_trigger_' || NEW.id
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the new INSERT trigger
DROP TRIGGER IF EXISTS tr_enqueue_match_disc_to_asins_insert ON t_discs;

CREATE TRIGGER tr_enqueue_match_disc_to_asins_insert
AFTER INSERT ON t_discs
FOR EACH ROW
WHEN (NEW.mps_id IS NOT NULL AND NEW.weight IS NOT NULL AND NEW.color_id IS NOT NULL)
EXECUTE FUNCTION fn_enqueue_match_disc_to_asins();

-- Create the new UPDATE trigger for mps_id, weight, color_id
DROP TRIGGER IF EXISTS tr_enqueue_match_disc_to_asins_update ON t_discs;

CREATE TRIGGER tr_enqueue_match_disc_to_asins_update
AFTER UPDATE OF mps_id, weight, color_id ON t_discs
FOR EACH ROW
WHEN (
  NEW.mps_id IS NOT NULL
  AND NEW.weight IS NOT NULL
  AND NEW.color_id IS NOT NULL
  AND NOT OLD.sold_date IS DISTINCT FROM NEW.sold_date
)
EXECUTE FUNCTION fn_enqueue_match_disc_to_asins();

-- Create the new UPDATE trigger for looked_for_matching_sdasin_at
DROP TRIGGER IF EXISTS tr_enqueue_match_disc_to_asins_reset ON t_discs;

CREATE TRIGGER tr_enqueue_match_disc_to_asins_reset
AFTER UPDATE OF looked_for_matching_sdasin_at ON t_discs
FOR EACH ROW
WHEN (
  OLD.looked_for_matching_sdasin_at IS NOT NULL
  AND NEW.looked_for_matching_sdasin_at IS NULL
)
EXECUTE FUNCTION fn_enqueue_match_disc_to_asins();

-- Drop the old triggers
DROP TRIGGER IF EXISTS tr_match_on_disc_in ON t_discs;
DROP TRIGGER IF EXISTS tr_match_on_disc_up ON t_discs;
DROP TRIGGER IF EXISTS tr_match_on_disc_update_looked_for ON t_discs;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Disc-to-SDASIN matching enqueuer function and triggers created.';
END $$;
