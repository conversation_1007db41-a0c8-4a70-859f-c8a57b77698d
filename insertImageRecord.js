// insertImageRecord.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import minimist from 'minimist';

// Parse command-line arguments
const args = minimist(process.argv.slice(2));
const tableName = args.table || 't_discs';
const recordId = args.id;

if (!recordId) {
  console.error('Missing required --id parameter');
  process.exit(1);
}

// Create a Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log(`Inserting t_images record for ${tableName} with record_id=${recordId}`);
    
    // First, check if the record already exists
    const { data: existingRecord, error: checkError } = await supabase
      .from('t_images')
      .select('id')
      .eq('table_name', tableName)
      .eq('record_id', recordId)
      .maybeSingle();
      
    if (checkError) {
      console.error(`Error checking for existing record: ${checkError.message}`);
      console.error(`Full error: ${JSON.stringify(checkError)}`);
      process.exit(1);
    }
    
    if (existingRecord) {
      console.log(`Record already exists for ${tableName} with record_id=${recordId}`);
      process.exit(0);
    }
    
    // Try using a raw SQL query
    const { data, error } = await supabase.rpc('debug_t_images_schema');
    
    if (error) {
      console.error(`Error getting schema: ${error.message}`);
      console.error(`Full error: ${JSON.stringify(error)}`);
    } else {
      console.log(`Schema info: ${JSON.stringify(data)}`);
    }
    
    // Try direct insert with SQL
    const { data: insertResult, error: insertError } = await supabase
      .rpc('insert_t_images_record', {
        p_table_name: tableName,
        p_record_id: parseInt(recordId),
        p_created_by: 'system'
      });
      
    if (insertError) {
      console.error(`Error inserting record: ${insertError.message}`);
      console.error(`Full error: ${JSON.stringify(insertError)}`);
      process.exit(1);
    }
    
    console.log(`Record inserted successfully: ${JSON.stringify(insertResult)}`);
  } catch (err) {
    console.error(`Unexpected error: ${err.message}`);
    process.exit(1);
  }
}

main();
