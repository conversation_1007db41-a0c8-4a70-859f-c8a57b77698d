import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkAllVendors() {
    console.log('Checking all vendors in it_dd_osl table...\n');
    
    try {
        // Get all unique vendors
        const { data: vendors, error: vendorsError } = await supabase
            .from('it_dd_osl')
            .select('product_vendor')
            .not('product_vendor', 'is', null);
        
        if (vendorsError) {
            console.error('Error fetching vendors:', vendorsError);
            return;
        }
        
        // Count by vendor
        const vendorCounts = {};
        vendors.forEach(row => {
            vendorCounts[row.product_vendor] = (vendorCounts[row.product_vendor] || 0) + 1;
        });
        
        console.log('Vendors in it_dd_osl table:');
        console.log('=====================================');
        Object.entries(vendorCounts)
            .sort((a, b) => b[1] - a[1])
            .forEach(([vendor, count]) => {
                console.log(`${vendor}: ${count} records`);
            });
        
        // Get matching brands from t_brands
        console.log('\nMatching brands in t_brands table:');
        console.log('=====================================');
        
        for (const vendor of Object.keys(vendorCounts)) {
            const { data: brandData, error: brandError } = await supabase
                .from('t_brands')
                .select('id, brand, code')
                .eq('brand', vendor);
            
            if (brandError) {
                console.error(`Error fetching brand for ${vendor}:`, brandError);
                continue;
            }
            
            if (brandData.length > 0) {
                const brand = brandData[0];
                console.log(`${vendor} → Brand ID: ${brand.id}, Code: ${brand.code}`);
                
                // Get mold and plastic counts for this brand
                const { data: moldCount, error: moldError } = await supabase
                    .from('t_molds')
                    .select('*', { count: 'exact', head: true })
                    .eq('brand_id', brand.id);
                
                const { data: plasticCount, error: plasticError } = await supabase
                    .from('t_plastics')
                    .select('*', { count: 'exact', head: true })
                    .eq('brand_id', brand.id);
                
                if (!moldError && !plasticError) {
                    console.log(`  → ${moldCount || 0} molds, ${plasticCount || 0} plastics`);
                }
            } else {
                console.log(`${vendor} → NOT FOUND in t_brands`);
            }
        }
        
        // Get sample product titles for each vendor
        console.log('\nSample product titles by vendor:');
        console.log('=====================================');
        
        for (const vendor of Object.keys(vendorCounts)) {
            const { data: sampleTitles, error: titlesError } = await supabase
                .from('it_dd_osl')
                .select('product_title')
                .eq('product_vendor', vendor)
                .eq('product_product_type', 'Discs')
                .limit(3);
            
            if (!titlesError && sampleTitles.length > 0) {
                console.log(`\n${vendor}:`);
                sampleTitles.forEach((item, index) => {
                    console.log(`  ${index + 1}. "${item.product_title}"`);
                });
            }
        }
        
    } catch (error) {
        console.error('Query failed:', error);
    }
}

// Run the query
checkAllVendors();
