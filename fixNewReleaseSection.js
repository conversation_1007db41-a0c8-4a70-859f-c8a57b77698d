import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

// Enhanced parsing function for NEW RELEASE items
function parseNewReleaseItem(description) {
    // Handle "Classic reissue - ESP FLX Drone (msrp $19.99, cost tier pricing)"
    if (description.includes('Classic reissue - ESP FLX Drone')) {
        return {
            mold_name: 'Drone',
            plastic_name: 'ESP FLX',
            stamp_name: 'Classic Reissue'
        };
    }
    
    // Handle "Fuzed Scorch w/ Flame Pattern Concept Art (msrp $24.99, cost $13.00)"
    if (description.includes('Fuzed Scorch w/ Flame Pattern')) {
        return {
            mold_name: 'Scorch',
            plastic_name: 'Elite Z FuZed Line with Saw Pattern',
            stamp_name: 'Flame Pattern Concept Art'
        };
    }
    
    return null;
}

async function fixNewReleaseSection() {
    try {
        console.log('🔧 Fixing NEW RELEASE section...\n');
        
        // 1. Delete ALL existing NEW RELEASE records
        console.log('1. Cleaning up existing NEW RELEASE records...');
        
        // Delete by row numbers
        const { data: deletedByRow, error: deleteRowError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .delete()
            .in('excel_row_hint', [126, 131, 132, 134, 135])
            .select();

        if (deleteRowError) {
            console.error('❌ Error deleting by row:', deleteRowError);
        } else {
            console.log(`✅ Deleted ${deletedByRow?.length || 0} records by row numbers`);
        }

        // Delete by content (in case there are any stragglers)
        const { data: deletedByContent, error: deleteContentError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .delete()
            .or('raw_model.ilike.%Classic reissue%,raw_model.ilike.%Fuzed Scorch%,raw_line_type.ilike.%NEW RELEASE%')
            .select();

        if (deleteContentError) {
            console.error('❌ Error deleting by content:', deleteContentError);
        } else {
            console.log(`✅ Deleted ${deletedByContent?.length || 0} additional records by content`);
        }

        // 2. Create the correct NEW RELEASE records
        console.log('\n2. Creating correct NEW RELEASE records...');
        
        const batchId = uuidv4();
        console.log(`Using batch ID: ${batchId}`);
        
        // Based on the Excel analysis:
        // Row 132, Column E has the Classic reissue product description
        // Row 132, Column B is where the order qty/MPS should go
        // Row 135, Column E has the Fuzed Scorch product description  
        // Row 135, Column B is where the order qty/MPS should go
        
        const newReleaseRecords = [
            {
                mold_name: 'Drone',
                plastic_name: 'ESP FLX',
                min_weight: 170,
                max_weight: 177,
                color_name: 'Varies',
                stamp_name: 'Classic Reissue',
                vendor_product_code: 'ESP_FLX_Drone_170-177',
                is_orderable: true,
                is_currently_available: false, // sold out
                cost_price: null, // tier pricing
                excel_mapping_key: 'SPECIAL|Classic reissue - ESP FLX Drone (msrp $19.99, cost tier pricing)|Order Qty',
                excel_row_hint: 132,
                excel_column: 'B',  // COLUMN B is where order qty goes!
                raw_line_type: 'SPECIAL',
                raw_model: 'Classic reissue - ESP FLX Drone (msrp $19.99, cost tier pricing)',
                raw_weight_range: 'Order Qty',
                vendor_description: 'Classic reissue - ESP FLX Drone (msrp $19.99, cost tier pricing)',
                calculated_mps_id: null, // Will need MPS record
                import_file_hash: 'manual_fix_new_release',
                import_batch_id: batchId
            },
            {
                mold_name: 'Scorch',
                plastic_name: 'Elite Z FuZed Line with Saw Pattern',
                min_weight: 170,
                max_weight: 177,
                color_name: 'Varies',
                stamp_name: 'Flame Pattern Concept Art',
                vendor_product_code: 'Elite_Z_FuZed_Scorch_170-177',
                is_orderable: true,
                is_currently_available: false, // sold out
                cost_price: 13.00,
                excel_mapping_key: 'SPECIAL|Fuzed Scorch w/ Flame Pattern Concept Art  (msrp $24.99, cost $13.00)|Order Qty',
                excel_row_hint: 135,
                excel_column: 'B',  // COLUMN B is where order qty goes!
                raw_line_type: 'SPECIAL',
                raw_model: 'Fuzed Scorch w/ Flame Pattern Concept Art  (msrp $24.99, cost $13.00)',
                raw_weight_range: 'Order Qty',
                vendor_description: 'Fuzed Scorch w/ Flame Pattern Concept Art  (msrp $24.99, cost $13.00)',
                calculated_mps_id: null, // Will need MPS record
                import_file_hash: 'manual_fix_new_release',
                import_batch_id: batchId
            }
        ];

        for (const record of newReleaseRecords) {
            console.log(`Creating: ${record.plastic_name} ${record.mold_name}`);
            console.log(`   Row ${record.excel_row_hint}, Column ${record.excel_column} (where order qty goes)`);
            console.log(`   Stamp: ${record.stamp_name}`);
            
            const { data: insertedData, error: insertError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .insert(record)
                .select();

            if (insertError) {
                console.error(`❌ Error inserting ${record.mold_name}:`, insertError);
            } else {
                console.log(`✅ Created ${record.mold_name} record`);
                console.log(`   Database ID: ${insertedData[0]?.id}`);
            }
        }

        // 3. Verify the records
        console.log('\n3. Verifying created records...');
        
        const { data: verifyRecords, error: verifyError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, mold_name, plastic_name, stamp_name, calculated_mps_id, is_orderable')
            .in('excel_row_hint', [132, 135])
            .order('excel_row_hint');

        if (verifyError) {
            console.error('❌ Error verifying records:', verifyError);
        } else {
            console.log(`✅ Found ${verifyRecords.length} NEW RELEASE records:`);
            verifyRecords.forEach(record => {
                console.log(`   Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.plastic_name} ${record.mold_name}`);
                console.log(`      Stamp: ${record.stamp_name}`);
                console.log(`      Orderable: ${record.is_orderable}, MPS: ${record.calculated_mps_id || 'NO_MPS'}`);
                console.log('');
            });
        }

        // 4. Check what will be exported
        console.log('4. Testing export data...');
        
        const { data: exportTest, error: exportTestError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, calculated_mps_id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .in('excel_row_hint', [131, 132, 134, 135]);

        if (exportTestError) {
            console.error('❌ Error testing export:', exportTestError);
        } else {
            console.log(`✅ Export test: ${exportTest.length} records will be exported from NEW RELEASE section:`);
            exportTest.forEach(record => {
                console.log(`   Row ${record.excel_row_hint}, Col ${record.excel_column}: MPS ${record.calculated_mps_id || 'NO_MPS'}`);
            });
        }

        console.log('\n🎉 NEW RELEASE section fix completed!');
        console.log('\n📋 Expected results in next order:');
        console.log('   • Row 131: EMPTY (header, not parsed)');
        console.log('   • Row 132, Column B: NO_MPS (Classic reissue Drone needs MPS record)');
        console.log('   • Row 134: EMPTY (header, not parsed)');
        console.log('   • Row 135, Column B: NO_MPS (Fuzed Scorch needs MPS record)');
        console.log('\n   Column E contains the product descriptions (parsed but not exported to)');
        console.log('   Column B is where the order quantities and MPS IDs go');
        
    } catch (error) {
        console.error('❌ Fix failed:', error.message);
    }
}

fixNewReleaseSection().catch(console.error);
