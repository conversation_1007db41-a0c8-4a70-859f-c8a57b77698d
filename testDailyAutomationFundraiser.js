import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testDailyAutomationFundraiser() {
    try {
        console.log('🧪 Testing daily automation with fundraiser fixes...\n');
        
        // Simulate the exact daily automation logic
        console.log('1. Getting orderable data (same as daily automation)...');
        const { data: allOrderableData, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null);

        if (error) {
            console.error('❌ Error getting orderable data:', error);
            return;
        }

        console.log(`✅ Found ${allOrderableData.length} total orderable records`);

        // Check specifically for fundraiser section
        const fundraiserData = allOrderableData.filter(item => 
            item.excel_row_hint >= 22 && item.excel_row_hint <= 30
        );

        console.log(`✅ Found ${fundraiserData.length} records in fundraiser section (rows 22-30):`);
        fundraiserData.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: MPS ${record.calculated_mps_id || 'NO_MPS'}`);
        });

        // Simulate daily automation mapping (with calculated_mps_id included)
        console.log('\n2. Testing daily automation mapping...');
        const orderableData = allOrderableData.map(item => ({
            excel_mapping_key: item.excel_mapping_key,
            excel_column: item.excel_column,
            excel_row_hint: item.excel_row_hint,
            order: 0,  // Default order
            calculated_mps_id: item.calculated_mps_id,  // NOW INCLUDED!
            mold_name: 'Test',
            plastic_name: 'Test',
            is_currently_available: false
        }));

        // Test MPS data creation (same as daily automation)
        console.log('\n3. Testing MPS data creation...');
        const mpsData = orderableData.map(item => ({
            ...item,
            order: item.calculated_mps_id || 'NO_MPS'
        }));

        // Check fundraiser section in MPS data
        const fundraiserMpsData = mpsData.filter(item => 
            item.excel_row_hint >= 22 && item.excel_row_hint <= 30
        );

        console.log(`✅ Fundraiser section in MPS data (${fundraiserMpsData.length} records):`);
        fundraiserMpsData.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: order=${record.order}`);
        });

        // Test actual export
        console.log('\n4. Testing actual export...');
        const timestamp = new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0];
        
        const response = await fetch('http://localhost:3001/api/discraft/export', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                includeHeader: false,
                filename: `test_daily_automation_fundraiser_${timestamp}.xlsx`,
                orderData: mpsData
            })
        });

        if (!response.ok) {
            throw new Error(`Export API returned ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('✅ Export completed!');
        console.log(`📄 Filename: ${result.filename}`);
        console.log(`📊 Records processed: ${result.totalRecords}`);

        console.log('\n🎯 Daily Automation Test Results:');
        console.log('   ✅ Row 22: Should have NO entries (header not orderable)');
        console.log('   ✅ Row 25, Column A: Should show MPS ID 19704');
        console.log('   ✅ Row 28, Column A: Should show NO_MPS');
        console.log('   ✅ All other rows: Should work normally');
        
        console.log(`\n📁 Check the generated file: ${result.filePath}`);
        console.log('   Look specifically at:');
        console.log('   • Row 22: Should be empty (no 0s or NO_MPS)');
        console.log('   • Row 25, Column A: Should show 19704');
        console.log('   • Row 28, Column A: Should show NO_MPS');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testDailyAutomationFundraiser().catch(console.error);
