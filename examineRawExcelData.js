import XLSX from 'xlsx';
import path from 'path';

async function examineRawExcelData() {
  try {
    console.log('🔍 Examining raw Excel data around lines 269-279...\n');
    
    const filePath = path.join(process.cwd(), 'data', 'external data', 'discraftstock.xlsx');
    console.log(`📁 Reading file: ${filePath}`);
    
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    console.log(`📊 Sheet: ${sheetName}`);
    
    // Examine rows 269-279 (Excel rows, so 1-based)
    for (let row = 269; row <= 279; row++) {
      console.log(`\n📋 Row ${row}:`);
      
      // Check columns A through AA (1-27)
      const rowData = {};
      for (let col = 1; col <= 27; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row - 1, c: col - 1 });
        const cell = worksheet[cellAddress];
        const value = cell ? cell.v : '';
        
        if (value && value !== '') {
          const colLetter = XLSX.utils.encode_col(col - 1);
          rowData[colLetter] = value;
        }
      }
      
      // Display the row data
      if (Object.keys(rowData).length > 0) {
        Object.entries(rowData).forEach(([col, value]) => {
          console.log(`   ${col}: "${value}"`);
        });
      } else {
        console.log('   (empty row)');
      }
      
      // Check specifically for McBeth NEW patterns
      const lineType = rowData.A || '';
      const model = rowData.B || '';
      
      if (lineType.includes('McBeth') && model.includes('NEW')) {
        console.log(`   🎯 FOUND McBeth NEW: "${lineType}" | "${model}"`);
        
        // Check all columns for vendor description patterns
        Object.entries(rowData).forEach(([col, value]) => {
          if (value.toString().toLowerCase().includes('white') || 
              value.toString().toLowerCase().includes('blank') ||
              value.toString().toLowerCase().includes('stamp')) {
            console.log(`   🔍 POTENTIAL VENDOR DESC in ${col}: "${value}"`);
          }
        });
      }
    }
    
    console.log('\n🎉 Raw Excel examination completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

examineRawExcelData().catch(console.error);
