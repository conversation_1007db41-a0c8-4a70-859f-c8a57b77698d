require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkDiscsWithRegularButNoVendorOsl() {
  try {
    console.log('Checking discs that have regular OSL mapping but no vendor OSL mapping...');
    
    // Find discs that have order_sheet_line_id but no vendor_osl_id
    const { count: discrepancyCount, error: countError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .not('order_sheet_line_id', 'is', null)
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (countError) {
      console.error('Error getting count:', countError);
      return;
    }
    
    console.log(`Found ${discrepancyCount} discs with regular OSL but no vendor OSL`);
    
    if (discrepancyCount === 0) {
      console.log('✅ No discrepancies found - all discs with regular OSL mappings also have vendor OSL mappings (or correctly have no vendor mapping).');
      
      // Let's also check the overall status
      await checkOverallStatus();
      return;
    }
    
    // Get sample of these discrepancy discs
    const { data: discrepancyDiscs, error: sampleError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
      .not('order_sheet_line_id', 'is', null)
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null)
      .limit(10);
    
    if (sampleError) {
      console.error('Error getting sample:', sampleError);
      return;
    }
    
    console.log(`\nInvestigating ${discrepancyDiscs.length} sample discrepancy discs:`);
    
    let shouldHaveVendorMatches = 0;
    
    for (const disc of discrepancyDiscs) {
      console.log(`\n=== Disc ${disc.id} ===`);
      console.log(`MPS: ${disc.mps_id}, Weight: ${disc.weight}g, Weight MFG: ${disc.weight_mfg}g, Color: ${disc.color_id}`);
      console.log(`Regular OSL: ${disc.order_sheet_line_id}, Vendor OSL: ${disc.vendor_osl_id}`);
      
      // Test the vendor OSL function
      const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
        'find_matching_osl_by_mfg_weight',
        {
          mps_id_param: disc.mps_id,
          color_id_param: disc.color_id,
          weight_mfg_param: disc.weight_mfg
        }
      );
      
      if (vendorOslError) {
        console.error('Error calling function:', vendorOslError);
        continue;
      }
      
      const foundOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
      
      if (foundOslId) {
        console.log(`🎯 DISCREPANCY FOUND! Should have vendor OSL: ${foundOslId}`);
        shouldHaveVendorMatches++;
        
        // Update this disc
        const { error: updateError } = await supabase
          .from('t_discs')
          .update({ vendor_osl_id: foundOslId })
          .eq('id', disc.id);
        
        if (updateError) {
          console.error('Error updating disc:', updateError);
        } else {
          console.log(`✅ Updated disc ${disc.id} with vendor_osl_id: ${foundOslId}`);
        }
      } else {
        console.log(`✅ Correctly has no vendor OSL match`);
        
        // Show why there's no vendor match but there is a regular match
        const roundedWeight = Math.round(disc.weight_mfg);
        console.log(`Regular weight ${disc.weight}g maps to OSL ${disc.order_sheet_line_id}`);
        console.log(`Vendor weight ${disc.weight_mfg}g (rounded: ${roundedWeight}g) has no matching OSL`);
      }
    }
    
    if (shouldHaveVendorMatches > 0) {
      console.log(`\n🔍 Found ${shouldHaveVendorMatches} discs that should have vendor matches!`);
      console.log('Processing all discrepancy discs...');
      
      // Process all discrepancy discs
      await processDiscrepancyDiscs(discrepancyCount);
    } else {
      console.log('\n✅ All discrepancy discs correctly have no vendor matches.');
    }
    
    await checkOverallStatus();
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

async function processDiscrepancyDiscs(totalCount) {
  try {
    console.log(`\n=== PROCESSING ${totalCount} DISCREPANCY DISCS ===`);
    
    let totalProcessed = 0;
    let totalUpdated = 0;
    const batchSize = 100;
    
    while (totalProcessed < totalCount) {
      const { data: discs, error: batchError } = await supabase
        .from('t_discs')
        .select('id, mps_id, weight_mfg, color_id')
        .not('order_sheet_line_id', 'is', null)
        .is('vendor_osl_id', null)
        .not('weight_mfg', 'is', null)
        .not('mps_id', 'is', null)
        .not('color_id', 'is', null)
        .range(0, batchSize - 1);
      
      if (batchError || !discs || discs.length === 0) {
        break;
      }
      
      for (const disc of discs) {
        const { data: vendorOslData } = await supabase.rpc(
          'find_matching_osl_by_mfg_weight',
          {
            mps_id_param: disc.mps_id,
            color_id_param: disc.color_id,
            weight_mfg_param: disc.weight_mfg
          }
        );
        
        const vendorOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
        
        if (vendorOslId) {
          await supabase
            .from('t_discs')
            .update({ vendor_osl_id: vendorOslId })
            .eq('id', disc.id);
          totalUpdated++;
        }
        
        totalProcessed++;
      }
      
      console.log(`Processed: ${totalProcessed}, Updated: ${totalUpdated}`);
    }
    
    console.log(`\n✅ Discrepancy processing complete: ${totalUpdated} additional updates`);
  } catch (err) {
    console.error('Error processing discrepancies:', err.message);
  }
}

async function checkOverallStatus() {
  try {
    console.log('\n=== OVERALL STATUS CHECK ===');
    
    // Total discs
    const { count: totalDiscs } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true });
    
    // Discs with weight_mfg
    const { count: withWeightMfg } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .not('weight_mfg', 'is', null);
    
    // Discs with vendor_osl_id
    const { count: withVendorOsl } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .not('vendor_osl_id', 'is', null);
    
    // Discs eligible for vendor mapping
    const { count: eligible } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    // Discs still needing vendor_osl_id
    const { count: stillNeed } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    console.log(`Total discs: ${totalDiscs}`);
    console.log(`Discs with weight_mfg: ${withWeightMfg}`);
    console.log(`Discs eligible for vendor mapping: ${eligible}`);
    console.log(`Discs with vendor_osl_id: ${withVendorOsl}`);
    console.log(`Discs still needing vendor_osl_id: ${stillNeed}`);
    
    const successRate = ((withVendorOsl / eligible) * 100).toFixed(1);
    console.log(`Success rate: ${successRate}%`);
    
    console.log('\n🎯 DUAL MAPPING SYSTEM STATUS:');
    if (successRate > 95) {
      console.log('✅ EXCELLENT: Dual mapping system is working very well!');
    } else if (successRate > 85) {
      console.log('✅ GOOD: Dual mapping system is working well.');
    } else {
      console.log('⚠️ NEEDS IMPROVEMENT: Some discs may need attention.');
    }
    
  } catch (err) {
    console.error('Error checking overall status:', err.message);
  }
}

checkDiscsWithRegularButNoVendorOsl();
