// taskQueueWorkerDaemon.js - A daemon that continuously runs the task queue worker
import dotenv from 'dotenv';
dotenv.config();

import { processTaskQueue } from './taskQueueWorker.js';
import { enqueueWorkerStatusTask } from './enqueueWorkerStatusTask.js';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client for shutdown handlers
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY; // Using SUPABASE_KEY instead of SUPABASE_SERVICE_ROLE_KEY
const supabase = createClient(supabaseUrl, supabaseKey);

// Flag to track if worker is currently running
let isWorkerRunning = false;

// Function to run the worker script
async function runWorker() {
  // Don't start a new worker if one is already running
  if (isWorkerRunning) {
    console.log(`[taskQueueWorkerDaemon.js] Worker is already running, skipping this run`);
    return;
  }

  isWorkerRunning = true;
  console.log(`[taskQueueWorkerDaemon.js] Running worker at ${new Date().toISOString()}`);

  try {
    // Call the processTaskQueue function directly
    await processTaskQueue();
    console.log(`[taskQueueWorkerDaemon.js] Worker completed successfully`);
  } catch (err) {
    console.error(`[taskQueueWorkerDaemon.js] Error running worker: ${err.message}`);
  } finally {
    isWorkerRunning = false; // Reset the flag when the worker completes
  }
}

// Function to schedule the next hourly status update
function scheduleHourlyStatusUpdate() {
  const now = new Date();
  const nextHour = new Date(now);

  // Set to the next hour
  nextHour.setHours(now.getHours() + 1);
  nextHour.setMinutes(0);
  nextHour.setSeconds(0);
  nextHour.setMilliseconds(0);

  // Calculate milliseconds until next hour
  const msUntilNextHour = nextHour - now;

  console.log(`[taskQueueWorkerDaemon.js] Scheduling next status update at ${nextHour.toISOString()} (in ${Math.round(msUntilNextHour / 1000)} seconds)`);

  // Schedule the status update
  setTimeout(async () => {
    try {
      // Enqueue the status update task
      await enqueueWorkerStatusTask();

      // Schedule the next update
      scheduleHourlyStatusUpdate();
    } catch (err) {
      console.error(`[taskQueueWorkerDaemon.js] Error in hourly status update: ${err.message}`);
      // Still try to schedule the next update even if this one failed
      scheduleHourlyStatusUpdate();
    }
  }, msUntilNextHour);
}

// Parse command line arguments
const args = process.argv.slice(2);
const intervalArg = args.find(arg => arg.startsWith('--interval='));
const interval = intervalArg ? parseInt(intervalArg.split('=')[1], 10) : 15; // Default to 15 seconds

console.log(`[taskQueueWorkerDaemon.js] Starting worker daemon with interval of ${interval} seconds`);

// Run the worker immediately
runWorker();

// Then run it every X seconds
const intervalId = setInterval(runWorker, interval * 1000);

// Schedule the first hourly status update
scheduleHourlyStatusUpdate();

// Also enqueue a status update immediately on startup
enqueueWorkerStatusTask().catch(err => {
  console.error(`[taskQueueWorkerDaemon.js] Error enqueueing initial status update: ${err.message}`);
});

// Handle process termination
process.on('SIGINT', async () => {
  console.log('[taskQueueWorkerDaemon.js] Received SIGINT, shutting down...');
  clearInterval(intervalId);

  try {
    // Enqueue a final status update indicating shutdown
    const now = new Date();

    // Format the date in CST (Central Standard Time)
    const cstOptions = {
      timeZone: 'America/Chicago',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    };
    const cstTimeString = now.toLocaleString('en-US', cstOptions);

    const { data, error } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'worker_daemon_shutdown',
        status: 'complete',
        payload: JSON.stringify("Shutdown Status"),
        scheduled_at: now.toISOString(),
        created_at: now.toISOString(),
        processed_at: now.toISOString(),
        result: JSON.stringify(`Worker Daemon is shutting down at ${cstTimeString} (CST) due to SIGINT`)
      })
      .select();

    if (error) {
      console.error(`[taskQueueWorkerDaemon.js] Error enqueueing shutdown status: ${error.message}`);
    } else {
      console.log(`[taskQueueWorkerDaemon.js] Enqueued shutdown status with ID ${data[0].id}`);
    }
  } catch (err) {
    console.error(`[taskQueueWorkerDaemon.js] Exception while enqueueing shutdown status: ${err.message}`);
  }

  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('[taskQueueWorkerDaemon.js] Received SIGTERM, shutting down...');
  clearInterval(intervalId);

  try {
    // Enqueue a final status update indicating shutdown
    const now = new Date();

    // Format the date in CST (Central Standard Time)
    const cstOptions = {
      timeZone: 'America/Chicago',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    };
    const cstTimeString = now.toLocaleString('en-US', cstOptions);

    const { data, error } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'worker_daemon_shutdown',
        status: 'complete',
        payload: JSON.stringify("Shutdown Status"),
        scheduled_at: now.toISOString(),
        created_at: now.toISOString(),
        processed_at: now.toISOString(),
        result: JSON.stringify(`Worker Daemon is shutting down at ${cstTimeString} (CST) due to SIGTERM`)
      })
      .select();

    if (error) {
      console.error(`[taskQueueWorkerDaemon.js] Error enqueueing shutdown status: ${error.message}`);
    } else {
      console.log(`[taskQueueWorkerDaemon.js] Enqueued shutdown status with ID ${data[0].id}`);
    }
  } catch (err) {
    console.error(`[taskQueueWorkerDaemon.js] Exception while enqueueing shutdown status: ${err.message}`);
  }

  process.exit(0);
});

console.log('[taskQueueWorkerDaemon.js] Worker daemon started, will run every', interval, 'seconds');
