import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

async function main() {
  // Initialize the Supabase client.
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_KEY;
  const supabase = createClient(supabaseUrl, supabaseKey);

  console.log('Starting OSL to Veeqo reconciliation...');

  let offset = 0;
  const batchSize = 50;
  let hasMoreRecords = true;
  const tasks = [];
  let totalProcessed = 0;
  let skippedRecords = 0;

  // Process records in batches of 50 until we've gone through the entire list
  while (hasMoreRecords) {
    // Query the view v_reconcile_osl_to_veeqo for records that need processing
    const { data: reconcileRecords, error: reconcileError } = await supabase
      .from('v_reconcile_osl_to_veeqo')
      .select('*')
      .range(offset, offset + batchSize - 1);

    if (reconcileError) {
      console.error('Error fetching v_reconcile_osl_to_veeqo:', reconcileError);
      break;
    }

    if (!reconcileRecords || reconcileRecords.length === 0) {
      console.log('No more records to process.');
      hasMoreRecords = false;
      break;
    }

    console.log(`Processing batch of ${reconcileRecords.length} record(s) (offset: ${offset})`);
    totalProcessed += reconcileRecords.length;

    // Enqueue tasks for each OSL that needs reconciliation
    for (const record of reconcileRecords) {
      const oslId = record.osl_id;
      const availableQuantity = record.available_quantity;

      // Skip if available_quantity is undefined or null
      if (availableQuantity === undefined || availableQuantity === null) {
        console.error(`Skipping OSL ID ${oslId}: available_quantity is undefined or null`);
        skippedRecords++;
        continue;
      }

      // Enqueue a task to update the Veeqo quantity for this OSL
      const { data: task, error: taskError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'update_veeqo_osl_qty',
          payload: {
            id: oslId,
            available_quantity: availableQuantity
          },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (taskError) {
        console.error(`Error enqueueing task for OSL ID ${oslId}:`, taskError);
      } else {
        console.log(`Successfully enqueued task for OSL ID ${oslId}: Task ID ${task.id}`);
        tasks.push({ id: task.id, osl_id: oslId });
      }
    }

    // Update offset for the next batch
    offset += batchSize;

    // If we got fewer records than the batch size, we've reached the end
    if (reconcileRecords.length < batchSize) {
      hasMoreRecords = false;
    }
  }

  console.log(`\nReconciliation complete!`);
  console.log(`Total records processed: ${totalProcessed}`);
  console.log(`Records skipped (missing available_quantity): ${skippedRecords}`);
  console.log(`Total tasks enqueued: ${tasks.length}`);

  // Return the tasks and summary for the API response
  return {
    tasks,
    summary: {
      totalProcessed,
      skippedRecords,
      tasksEnqueued: tasks.length
    }
  };
}

// If this script is run directly (not imported), execute the main function
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .then(tasks => {
      console.log('Tasks enqueued:', tasks);
      process.exit(0);
    })
    .catch(err => {
      console.error('Error:', err);
      process.exit(1);
    });
}

// Export the main function for use in other modules
export default main;
