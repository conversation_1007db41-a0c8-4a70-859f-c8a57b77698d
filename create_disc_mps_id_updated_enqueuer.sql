-- Function to enqueue a task when a disc's mps_id is updated
CREATE OR REPLACE FUNCTION fn_enqueue_disc_updated_need_to_reset()
RETURNS TRIGGER AS $$
BEGIN
    -- Only enqueue a task if the mps_id has actually changed
    IF OLD.mps_id IS DISTINCT FROM NEW.mps_id THEN
        
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at,
            enqueued_by
        ) VALUES (
            'disc_updated_need_to_reset',
            jsonb_build_object(
                'id', NEW.id,
                'sold_date', NEW.sold_date,
                'old_mps_id', OLD.mps_id,
                'new_mps_id', NEW.mps_id
            ),
            'pending',
            NOW() + INTERVAL '5 minutes',
            NOW(),
            't_discs mps_id_update_trigger_' || NEW.id
        );
        
        -- Log the trigger action
        RAISE NOTICE 'Enqueued disc_updated_need_to_reset task for disc_id=% due to mps_id change from % to %', 
                     NEW.id, OLD.mps_id, NEW.mps_id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trg_enqueue_disc_updated_need_to_reset ON t_discs;

CREATE TRIGGER trg_enqueue_disc_updated_need_to_reset
AFTER UPDATE OF mps_id ON t_discs
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_disc_updated_need_to_reset();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Disc mps_id update enqueuer function and trigger created successfully.';
    RAISE NOTICE 'Trigger will fire when mps_id field is updated on t_discs table.';
    RAISE NOTICE 'Tasks will be scheduled 5 minutes in the future to allow for any immediate processing.';
END $$;
