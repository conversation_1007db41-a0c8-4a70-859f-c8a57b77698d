import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function fixFundraiserCorrectStructure() {
    try {
        console.log('🔧 Fixing fundraiser with CORRECT structure...\n');
        
        // 1. Delete ALL existing fundraiser-related records
        console.log('1. Cleaning up ALL existing fundraiser records...');
        
        // Delete by row numbers
        const { data: deletedByRow, error: deleteRowError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .delete()
            .in('excel_row_hint', [22, 24, 25, 27, 28])
            .select();

        if (deleteRowError) {
            console.error('❌ Error deleting by row:', deleteRowError);
        } else {
            console.log(`✅ Deleted ${deletedByRow?.length || 0} records by row numbers`);
        }

        // Delete by content (in case there are any stragglers)
        const { data: deletedByContent, error: deleteContentError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .delete()
            .or('raw_model.ilike.%Ben Askren%,plastic_name.ilike.%Fundraiser%,raw_line_type.ilike.%Fundraiser%')
            .select();

        if (deleteContentError) {
            console.error('❌ Error deleting by content:', deleteContentError);
        } else {
            console.log(`✅ Deleted ${deletedByContent?.length || 0} additional records by content`);
        }

        // 2. Create the correct fundraiser records
        console.log('\n2. Creating correct fundraiser records...');
        
        const batchId = uuidv4();
        console.log(`Using batch ID: ${batchId}`);
        
        // Based on the Excel analysis:
        // Row 25, Column E has the Thrasher product description
        // Row 25, Column B is where the order qty/MPS should go
        // Row 28, Column E has the Buzzz product description  
        // Row 28, Column B is where the order qty/MPS should go
        
        const fundraiserRecords = [
            {
                mold_name: 'Thrasher',
                plastic_name: 'Elite Z Jawbreaker',
                min_weight: 150,
                max_weight: 180,
                color_name: 'Varies',
                stamp_name: 'Live Free - Be Happy - Funky Ben Askren Fundraiser',
                vendor_product_code: 'Elite_Z_Jawbreaker_Thrasher_150-180',
                is_orderable: true,
                is_currently_available: true,
                cost_price: 10.00,
                excel_mapping_key: 'SPECIAL|Fundraiser - Ben Askren Z Jawbreaker Thrasher (msrp $19.99, cost $10.00)|Order Qty',
                excel_row_hint: 25,
                excel_column: 'B',  // COLUMN B is where order qty goes!
                raw_line_type: 'SPECIAL',
                raw_model: 'Fundraiser - Ben Askren Z Jawbreaker Thrasher (msrp $19.99, cost $10.00)',
                raw_weight_range: 'Order Qty',
                vendor_description: 'Fundraiser - Ben Askren Z Jawbreaker Thrasher (msrp $19.99, cost $10.00)',
                calculated_mps_id: 19704,
                import_file_hash: 'manual_fix_correct',
                import_batch_id: batchId
            },
            {
                mold_name: 'Buzzz',
                plastic_name: 'Big Z Collection',
                min_weight: 150,
                max_weight: 180,
                color_name: 'Varies',
                stamp_name: 'Live Free - Be Happy - Funky Ben Askren Fundraiser',
                vendor_product_code: 'Big_Z_Collection_Buzzz_150-180',
                is_orderable: true,
                is_currently_available: true,
                cost_price: 10.00,
                excel_mapping_key: 'SPECIAL|Fundraiser - Ben Askren Big Z Buzzz  (msrp $19.99, cost $10.00)|Order Qty',
                excel_row_hint: 28,
                excel_column: 'B',  // COLUMN B is where order qty goes!
                raw_line_type: 'SPECIAL',
                raw_model: 'Fundraiser - Ben Askren Big Z Buzzz  (msrp $19.99, cost $10.00)',
                raw_weight_range: 'Order Qty',
                vendor_description: 'Fundraiser - Ben Askren Big Z Buzzz  (msrp $19.99, cost $10.00)',
                calculated_mps_id: null, // Will show NO_MPS
                import_file_hash: 'manual_fix_correct',
                import_batch_id: batchId
            }
        ];

        for (const record of fundraiserRecords) {
            console.log(`Creating: ${record.plastic_name} ${record.mold_name}`);
            console.log(`   Row ${record.excel_row_hint}, Column ${record.excel_column} (where order qty goes)`);
            console.log(`   MPS ID: ${record.calculated_mps_id || 'NO_MPS'}`);
            
            const { data: insertedData, error: insertError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .insert(record)
                .select();

            if (insertError) {
                console.error(`❌ Error inserting ${record.mold_name}:`, insertError);
            } else {
                console.log(`✅ Created ${record.mold_name} record`);
                console.log(`   Database ID: ${insertedData[0]?.id}`);
            }
        }

        // 3. Verify the records
        console.log('\n3. Verifying created records...');
        
        const { data: verifyRecords, error: verifyError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, mold_name, plastic_name, calculated_mps_id, is_orderable, excel_mapping_key')
            .in('excel_row_hint', [25, 28])
            .order('excel_row_hint');

        if (verifyError) {
            console.error('❌ Error verifying records:', verifyError);
        } else {
            console.log(`✅ Found ${verifyRecords.length} fundraiser records:`);
            verifyRecords.forEach(record => {
                console.log(`   Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.plastic_name} ${record.mold_name}`);
                console.log(`      Orderable: ${record.is_orderable}, MPS: ${record.calculated_mps_id || 'NO_MPS'}`);
                console.log(`      Mapping: ${record.excel_mapping_key}`);
                console.log('');
            });
        }

        // 4. Check what will be exported
        console.log('4. Testing export data...');
        
        const { data: exportTest, error: exportTestError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, calculated_mps_id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .in('excel_row_hint', [22, 24, 25, 27, 28]);

        if (exportTestError) {
            console.error('❌ Error testing export:', exportTestError);
        } else {
            console.log(`✅ Export test: ${exportTest.length} records will be exported from fundraiser section:`);
            exportTest.forEach(record => {
                console.log(`   Row ${record.excel_row_hint}, Col ${record.excel_column}: MPS ${record.calculated_mps_id || 'NO_MPS'}`);
            });
        }

        console.log('\n🎉 Fundraiser structure fix completed!');
        console.log('\n📋 Expected results in next order:');
        console.log('   • Row 22: EMPTY (header, not parsed)');
        console.log('   • Row 24: EMPTY (header, not parsed)');
        console.log('   • Row 25, Column B: 19704 (Thrasher MPS ID)');
        console.log('   • Row 27: EMPTY (header, not parsed)');
        console.log('   • Row 28, Column B: NO_MPS (Buzzz needs MPS record)');
        console.log('\n   Column E contains the product descriptions (parsed but not exported to)');
        console.log('   Column B is where the order quantities and MPS IDs go');
        
    } catch (error) {
        console.error('❌ Fix failed:', error.message);
    }
}

fixFundraiserCorrectStructure().catch(console.error);
