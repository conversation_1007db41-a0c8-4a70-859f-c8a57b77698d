-- Drop the existing trigger
DROP TRIGGER IF EXISTS trg_queue_generate_disc_title_pull_and_handle ON t_discs;

-- Create the updated trigger with location field included
CREATE TRIGGER trg_queue_generate_disc_title_pull_and_handle
AFTER INSERT
OR UPDATE OF mps_id,
    weight,
    color_id,
    color_modifier,
    location ON t_discs
FOR EACH ROW
EXECUTE FUNCTION fn_queue_generate_disc_title_pull_and_handle();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger trg_queue_generate_disc_title_pull_and_handle has been updated to include the location field.';
END $$;
