-- Fix the failed task by updating its status back to 'pending'
-- This will allow the worker to pick it up again with the fixed code

UPDATE t_task_queue
SET status = 'pending',
    locked_at = NULL,
    locked_by = NULL,
    result = NULL,
    processed_at = NULL
WHERE task_type = 'd_sold_or_unsold_update_sdasin_inv'
AND status = 'error'
AND result->>'error_count' = '1'
AND result->>'errors' LIKE '%supabase.raw is not a function%';

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Failed tasks have been reset to pending status.';
END $$;
