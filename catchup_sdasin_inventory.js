// catchup_sdasin_inventory.js
// Script to catch up on missing t_inv_sdasin records and enqueue matching tasks
// This handles the case where t_sdasins records were added but t_inv_sdasin records weren't created

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Configuration
const BATCH_SIZE = 100;
const DRY_RUN = process.argv.includes('--dry-run');

console.log(`Starting SDASIN inventory catchup script...`);
console.log(`Batch size: ${BATCH_SIZE}`);
console.log(`Dry run mode: ${DRY_RUN}`);

async function findMissingSdasinInventoryRecords() {
  console.log('\n=== Finding missing t_inv_sdasin records ===');
  
  const { data: missingSdasins, error } = await supabase
    .from('t_sdasins')
    .select('id')
    .not('id', 'in', 
      supabase
        .from('t_inv_sdasin')
        .select('id')
    );

  if (error) {
    console.error('Error finding missing SDASIN inventory records:', error);
    throw error;
  }

  console.log(`Found ${missingSdasins.length} SDASIN records missing inventory records`);
  return missingSdasins.map(s => s.id);
}

async function createMissingInventoryRecords(sdasinIds) {
  console.log('\n=== Creating missing t_inv_sdasin records ===');

  if (DRY_RUN) {
    console.log(`[DRY RUN] Would create ${sdasinIds.length} t_inv_sdasin records`);
    return sdasinIds.length;
  }

  let totalCreated = 0;

  // Process in batches
  for (let i = 0; i < sdasinIds.length; i += BATCH_SIZE) {
    const batch = sdasinIds.slice(i, i + BATCH_SIZE);
    console.log(`Processing batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(sdasinIds.length / BATCH_SIZE)} (${batch.length} records)`);

    // Calculate initial inventory quantities based on existing disc matches
    const recordsToInsert = [];

    for (const sdasinId of batch) {
      // Count unsold discs currently matched to this SDASIN
      const { data: matchedDiscs, error: countError } = await supabase
        .from('tjoin_discs_sdasins')
        .select('disc_id, t_discs!inner(sold_date)')
        .eq('sdasin_id', sdasinId);

      if (countError) {
        console.error(`Error counting matched discs for SDASIN ${sdasinId}:`, countError);
        throw countError;
      }

      // Count only unsold discs
      const unsoldCount = matchedDiscs.filter(match => match.t_discs.sold_date === null).length;

      recordsToInsert.push({
        id: sdasinId,
        available_quantity: unsoldCount,
        created_by: 'catchup_script',
        notes: `Initial quantity calculated from ${matchedDiscs.length} total matches (${unsoldCount} unsold)`
      });
    }

    const { error } = await supabase
      .from('t_inv_sdasin')
      .insert(recordsToInsert);

    if (error) {
      console.error(`Error creating inventory records for batch starting at index ${i}:`, error);
      throw error;
    }

    totalCreated += batch.length;
    const totalQty = recordsToInsert.reduce((sum, record) => sum + record.available_quantity, 0);
    console.log(`Created ${batch.length} inventory records with ${totalQty} total quantity (total: ${totalCreated}/${sdasinIds.length})`);
  }

  console.log(`Successfully created ${totalCreated} t_inv_sdasin records`);
  return totalCreated;
}

async function enqueueSdasinMatchingTasks(sdasinIds) {
  console.log('\n=== Enqueuing sdasin_updated_find_discs_to_match tasks ===');
  
  if (DRY_RUN) {
    console.log(`[DRY RUN] Would enqueue ${sdasinIds.length} sdasin_updated_find_discs_to_match tasks`);
    return sdasinIds.length;
  }

  let totalEnqueued = 0;
  const now = new Date().toISOString();
  
  // Process in batches
  for (let i = 0; i < sdasinIds.length; i += BATCH_SIZE) {
    const batch = sdasinIds.slice(i, i + BATCH_SIZE);
    console.log(`Enqueuing batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(sdasinIds.length / BATCH_SIZE)} (${batch.length} tasks)`);
    
    const tasksToInsert = batch.map((id, index) => ({
      task_type: 'sdasin_updated_find_discs_to_match',
      payload: { id: id },
      status: 'pending',
      scheduled_at: new Date(Date.now() + (index * 1000)).toISOString(), // Stagger by 1 second each
      created_at: now,
      enqueued_by: 'catchup_sdasin_inventory_script'
    }));

    const { error } = await supabase
      .from('t_task_queue')
      .insert(tasksToInsert);

    if (error) {
      console.error(`Error enqueuing tasks for batch starting at index ${i}:`, error);
      throw error;
    }

    totalEnqueued += batch.length;
    console.log(`Enqueued ${batch.length} tasks (total: ${totalEnqueued}/${sdasinIds.length})`);
  }

  console.log(`Successfully enqueued ${totalEnqueued} sdasin_updated_find_discs_to_match tasks`);
  return totalEnqueued;
}

async function showPreview(sdasinIds) {
  console.log('\n=== Preview of operations ===');

  // Show a sample of the SDASIN IDs that will be processed
  const sampleSize = Math.min(10, sdasinIds.length);
  const sample = sdasinIds.slice(0, sampleSize);

  console.log(`Sample SDASIN IDs to process (showing first ${sampleSize} of ${sdasinIds.length}):`);
  console.log(sample.join(', '));

  if (sdasinIds.length > sampleSize) {
    console.log(`... and ${sdasinIds.length - sampleSize} more`);
  }

  // Get some statistics about existing matches
  const { data: matchStats, error } = await supabase
    .from('tjoin_discs_sdasins')
    .select('sdasin_id, t_discs!inner(sold_date)')
    .in('sdasin_id', sample);

  if (!error && matchStats) {
    const totalMatches = matchStats.length;
    const unsoldMatches = matchStats.filter(m => m.t_discs.sold_date === null).length;
    console.log(`Sample shows ${totalMatches} total disc matches, ${unsoldMatches} unsold`);
  }
}

async function main() {
  try {
    const startTime = Date.now();

    // Step 1: Find missing inventory records
    const missingSdasinIds = await findMissingSdasinInventoryRecords();

    if (missingSdasinIds.length === 0) {
      console.log('\n✅ No missing t_inv_sdasin records found. All caught up!');
      return;
    }

    // Show preview of what will be processed
    await showPreview(missingSdasinIds);

    // Step 2: Create missing inventory records
    const createdCount = await createMissingInventoryRecords(missingSdasinIds);

    // Step 3: Enqueue matching tasks for all the SDASIN IDs
    const enqueuedCount = await enqueueSdasinMatchingTasks(missingSdasinIds);
    
    const endTime = Date.now();
    const durationSec = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log('\n=== Summary ===');
    console.log(`Missing SDASIN inventory records found: ${missingSdasinIds.length}`);
    console.log(`t_inv_sdasin records created: ${createdCount}`);
    console.log(`sdasin_updated_find_discs_to_match tasks enqueued: ${enqueuedCount}`);
    console.log(`Total processing time: ${durationSec} seconds`);
    
    if (DRY_RUN) {
      console.log('\n⚠️  This was a DRY RUN. No changes were made.');
      console.log('Run without --dry-run to actually perform the operations.');
    } else {
      console.log('\n✅ Catchup completed successfully!');
      console.log('The task queue worker will now process the enqueued tasks to update inventory counts.');
    }

  } catch (error) {
    console.error('\n❌ Error during catchup process:', error);
    process.exit(1);
  }
}

// Handle command line execution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { main };
