require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function debugDisc243539Osl11374() {
  try {
    console.log('Debugging disc 243539 and OSL 11374...');
    
    // Get disc details
    const { data: disc, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .eq('id', 243539)
      .single();
    
    if (discError) {
      console.error('Error getting disc:', discError);
      return;
    }
    
    console.log('\n=== DISC 243539 DETAILS ===');
    console.log(`MPS ID: ${disc.mps_id}`);
    console.log(`Weight: ${disc.weight}g`);
    console.log(`Weight MFG: ${disc.weight_mfg}g`);
    console.log(`Color ID: ${disc.color_id}`);
    console.log(`Sold date: ${disc.sold_date || 'NULL (unsold)'}`);
    console.log(`Current order_sheet_line_id: ${disc.order_sheet_line_id}`);
    console.log(`Current vendor_osl_id: ${disc.vendor_osl_id}`);
    
    // Get OSL 11374 details
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, color_id, min_weight, max_weight')
      .eq('id', 11374)
      .single();
    
    if (oslError) {
      console.error('Error getting OSL:', oslError);
      return;
    }
    
    console.log('\n=== OSL 11374 DETAILS ===');
    console.log(`MPS ID: ${osl.mps_id}`);
    console.log(`Color ID: ${osl.color_id}`);
    console.log(`Weight range: ${osl.min_weight}-${osl.max_weight}g`);
    
    // Check matching criteria
    console.log('\n=== MATCHING CRITERIA ANALYSIS ===');
    
    const mpsMatch = disc.mps_id === osl.mps_id;
    console.log(`1. MPS ID match (${disc.mps_id} === ${osl.mps_id}): ${mpsMatch ? '✅' : '❌'}`);
    
    const colorMatch = disc.color_id === osl.color_id || osl.color_id === 23;
    console.log(`2. Color match (${disc.color_id} === ${osl.color_id} OR ${osl.color_id} === 23): ${colorMatch ? '✅' : '❌'}`);
    
    if (disc.weight_mfg !== null) {
      const roundedWeightMfg = Math.round(disc.weight_mfg);
      const weightMfgMatch = roundedWeightMfg >= osl.min_weight && roundedWeightMfg <= osl.max_weight;
      console.log(`3. Weight MFG match (ROUND(${disc.weight_mfg}) = ${roundedWeightMfg} >= ${osl.min_weight} AND <= ${osl.max_weight}): ${weightMfgMatch ? '✅' : '❌'}`);
      
      const allCriteriaMatch = mpsMatch && colorMatch && weightMfgMatch;
      console.log(`\nALL CRITERIA MATCH: ${allCriteriaMatch ? '✅' : '❌'}`);
      
      if (allCriteriaMatch) {
        console.log('\n🎯 THIS DISC SHOULD MATCH OSL 11374!');
        
        // Test our function directly
        console.log('\nTesting find_matching_osl_by_mfg_weight function...');
        const { data: functionResult, error: functionError } = await supabase.rpc(
          'find_matching_osl_by_mfg_weight',
          {
            mps_id_param: disc.mps_id,
            color_id_param: disc.color_id,
            weight_mfg_param: disc.weight_mfg
          }
        );
        
        if (functionError) {
          console.error('Function error:', functionError);
        } else {
          console.log('Function result:', functionResult);
          
          const foundOslId = functionResult && functionResult.length > 0 ? functionResult[0].osl_id : null;
          
          if (foundOslId === 11374) {
            console.log('✅ Function correctly returns OSL 11374');
          } else if (foundOslId) {
            console.log(`⚠️ Function returns different OSL: ${foundOslId}`);
            
            // Get details of the OSL the function returned
            const { data: returnedOsl, error: returnedOslError } = await supabase
              .from('t_order_sheet_lines')
              .select('id, mps_id, color_id, min_weight, max_weight')
              .eq('id', foundOslId)
              .single();
            
            if (!returnedOslError) {
              console.log(`Returned OSL ${foundOslId} details: MPS ${returnedOsl.mps_id}, color ${returnedOsl.color_id}, weight ${returnedOsl.min_weight}-${returnedOsl.max_weight}g`);
              console.log('The function uses LIMIT 1, so it returns the first match found.');
              
              // Check if OSL 11374 would also match
              const osl11374AlsoMatches = disc.mps_id === osl.mps_id && 
                                         (disc.color_id === osl.color_id || osl.color_id === 23) &&
                                         roundedWeightMfg >= osl.min_weight && roundedWeightMfg <= osl.max_weight;
              console.log(`OSL 11374 would also match: ${osl11374AlsoMatches ? '✅' : '❌'}`);
              
              // Check which OSL comes first in the query order
              console.log('\nChecking query order to see why function returns different OSL...');
              const { data: allMatches, error: allMatchesError } = await supabase
                .from('t_order_sheet_lines')
                .select('id, min_weight, max_weight, color_id')
                .eq('mps_id', disc.mps_id)
                .in('color_id', [disc.color_id, 23])
                .gte('max_weight', roundedWeightMfg)
                .lte('min_weight', roundedWeightMfg)
                .order('id');
              
              if (!allMatchesError && allMatches) {
                console.log('All matching OSLs (ordered by ID):');
                allMatches.forEach(match => {
                  const isTarget = match.id === 11374;
                  const isReturned = match.id === foundOslId;
                  console.log(`  ${isTarget ? '🎯' : isReturned ? '🔄' : '  '} OSL ${match.id}: ${match.min_weight}-${match.max_weight}g, color ${match.color_id}`);
                });
              }
            }
          } else {
            console.log('❌ Function returns no match - this indicates an issue!');
          }
        }
        
        // Try to update this disc manually with the function result
        if (!disc.vendor_osl_id && functionResult && functionResult.length > 0) {
          const vendorOslId = functionResult[0].osl_id;
          console.log(`\nAttempting to update disc 243539 with vendor_osl_id: ${vendorOslId}...`);
          const { error: updateError } = await supabase
            .from('t_discs')
            .update({ vendor_osl_id: vendorOslId })
            .eq('id', 243539);
          
          if (updateError) {
            console.error('Error updating disc:', updateError);
          } else {
            console.log(`✅ Successfully updated disc 243539 with vendor_osl_id: ${vendorOslId}`);
          }
        } else if (disc.vendor_osl_id) {
          console.log(`Disc already has vendor_osl_id: ${disc.vendor_osl_id}`);
        }
        
      } else {
        console.log('\n❌ Criteria do not match - this explains why there is no mapping.');
        
        if (!mpsMatch) {
          console.log(`   - MPS mismatch: disc has ${disc.mps_id}, OSL requires ${osl.mps_id}`);
        }
        if (!colorMatch) {
          console.log(`   - Color mismatch: disc has color ${disc.color_id}, OSL requires ${osl.color_id} (or 23 for any)`);
        }
        if (disc.weight_mfg !== null) {
          const roundedWeightMfg = Math.round(disc.weight_mfg);
          const weightMfgMatch = roundedWeightMfg >= osl.min_weight && roundedWeightMfg <= osl.max_weight;
          if (!weightMfgMatch) {
            console.log(`   - Weight mismatch: disc weight_mfg ${disc.weight_mfg}g (rounded: ${roundedWeightMfg}g) outside range ${osl.min_weight}-${osl.max_weight}g`);
          }
        }
      }
    } else {
      console.log('3. Weight MFG is null - cannot match');
    }
    
    // Show all available OSLs for this disc
    console.log('\n=== ALL AVAILABLE OSLs FOR THIS DISC ===');
    const { data: availableOsls, error: availableError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, color_id, min_weight, max_weight')
      .eq('mps_id', disc.mps_id)
      .in('color_id', [disc.color_id, 23])
      .order('min_weight');
    
    if (!availableError && availableOsls) {
      console.log(`Found ${availableOsls.length} OSLs for MPS ${disc.mps_id} with colors ${disc.color_id} or 23:`);
      availableOsls.forEach(availableOsl => {
        const roundedWeight = disc.weight_mfg ? Math.round(disc.weight_mfg) : 'N/A';
        const matches = disc.weight_mfg && roundedWeight >= availableOsl.min_weight && roundedWeight <= availableOsl.max_weight;
        const isTarget = availableOsl.id === 11374;
        console.log(`  ${isTarget ? '🎯' : '  '} OSL ${availableOsl.id}: ${availableOsl.min_weight}-${availableOsl.max_weight}g, color ${availableOsl.color_id}, matches weight ${roundedWeight}g? ${matches ? '✅' : '❌'}`);
      });
    }
    
    // Check if this disc was processed in recent runs
    console.log('\n=== PROCESSING STATUS ===');
    if (disc.sold_date) {
      console.log('This is a SOLD disc - it would not have been processed in the unsold inventory optimization.');
      console.log('Sold discs were excluded from the recent processing runs.');
    } else {
      console.log('This is an UNSOLD disc - it should have been processed in the recent unsold inventory optimization.');
      if (disc.vendor_osl_id) {
        console.log(`It has vendor_osl_id: ${disc.vendor_osl_id} - it was successfully processed.`);
      } else {
        console.log('It still has null vendor_osl_id - it may have been missed or had no matches.');
      }
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

debugDisc243539Osl11374();
