// downloadInnova.js
import axios from 'axios';
import fs from 'fs';
import path from 'path';

export async function downloadFile() {
  // URL to download the Excel file from
  const url = 'https://dealer.innovadiscs.com/orderforms/orderform.xlsx';
  
  // Ensure the data folder exists
  const dataFolder = path.resolve(process.cwd(), 'data');
  if (!fs.existsSync(dataFolder)) {
    fs.mkdirSync(dataFolder, { recursive: true });
    console.log(`Created folder: ${dataFolder}`);
  }

  // Save the file as "innovaorderform.xlsx" in the data folder
  const filePath = path.join(dataFolder, 'innovaorderform.xlsx');

  console.log(`Downloading file to: ${filePath}`);
  const response = await axios({
    method: 'GET',
    url: url,
    responseType: 'stream'
  });

  // Create a writable stream and pipe the data into it
  const writer = fs.createWriteStream(filePath);
  response.data.pipe(writer);

  // Return a promise that resolves when the file is fully written
  return new Promise((resolve, reject) => {
    writer.on('finish', () => {
      console.log('Download completed.');
      resolve();
    });
    writer.on('error', reject);
  });
}
