import { createClient } from '@supabase/supabase-js';
import <PERSON> from 'papaparse';

const supabase = createClient(
  'https://aepabhlwpjfjulrjeitn.supabase.co',
  // Service role key
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzc4NjU0MSwiZXhwIjoyMDQ5MzYyNTQxfQ.FQyPTgdBP83VhuykdlZkagHADc5nBmx7-yd0JYt5aP8'
);

async function upsertTStamps() {
  console.log('⏳ Downloading CSV file from Supabase Storage...');

  // 1️⃣ Download the CSV from Supabase Storage
  const { data: downloadData, error: downloadError } = await supabase
    .storage
    .from('uploads')
    .download('supabase08_t_stamps.csv');

  if (downloadError) {
    console.error('❌ Error downloading CSV:', downloadError);
    return;
  }
  console.log('✅ CSV downloaded successfully.');

  // 2️⃣ Parse the CSV
  const csvText = await downloadData.text();
  const { data: parsedData, errors: parseErrors } = Papa.parse(csvText, {
    header: true,
    skipEmptyLines: true,
    dynamicTyping: true,
    delimiter: ',',
  });

  if (parseErrors.length > 0) {
    console.warn('⚠️ CSV Parsing Warnings:', parseErrors.slice(0, 5)); // Show first 5
  }
  console.log(`✅ CSV parsed successfully. Found ${parsedData.length} records.`);

  // 3️⃣ Upsert the data into t_stamps
  //    onConflict: 'id' tells Supabase to "update if id exists, otherwise insert"
  const { data: upserted, error: upsertError } = await supabase
    .from('t_stamps')
    .upsert(parsedData, { onConflict: 'id' })   // <— KEY PART
    // If you want the updated rows returned, chain .select(...)
    .select('id, stamp, description, is_vendor_stock, is_sdasin_stock, player_id, tags, created_at, created_by');

  if (upsertError) {
    console.error('❌ Error upserting data into t_stamps:', upsertError);
  } else {
    console.log('✅ CSV data upserted successfully into t_stamps!');
    // Show a few returned rows just to confirm
    console.log(upserted.slice(0, 3));
  }
}

// Run it
upsertTStamps();
