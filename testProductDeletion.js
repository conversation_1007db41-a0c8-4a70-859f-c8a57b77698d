// testProductDeletion.js - Test product deletion specifically
import dotenv from 'dotenv';
dotenv.config();

import { deleteVariantFromShopify } from './processDeleteVariantFromShopifyTask.js';

async function testProductDeletion() {
  console.log('🧪 Testing Product Deletion After Variant Deletion');
  console.log('==================================================');

  try {
    // Test with a disc SKU - you'll need to replace this with an actual SKU
    // that exists in your Shopify store and is the only variant in its product
    const testSku = 'D401744'; // Replace with a real SKU for testing
    const reason = 'Testing product deletion after variant deletion';

    console.log(`\n🎯 Testing deletion of SKU: ${testSku}`);
    console.log(`📝 Reason: ${reason}`);
    console.log(`⚠️  WARNING: This will actually delete the variant and product from Shopify!`);
    
    // Uncomment the line below to actually run the test
    // const result = await deleteVariantFromShopify(testSku, reason);
    
    console.log('\n❌ Test is commented out to prevent accidental deletion');
    console.log('To run the test:');
    console.log('1. Replace testSku with a real SKU that exists in Shopify');
    console.log('2. Ensure the SKU is the ONLY variant in its product');
    console.log('3. Uncomment the deleteVariantFromShopify call above');
    console.log('4. Run the test');
    
    return;
    
    // The rest of this code will run if you uncomment the deletion call above
    console.log('\n📊 Deletion Results:');
    console.log('====================');
    console.log(`✅ Success: ${result.success}`);
    console.log(`🔍 Found: ${result.found}`);
    console.log(`📄 Message: ${result.message}`);
    
    if (result.found) {
      console.log(`\n📦 Product Details:`);
      console.log(`   Product Title: ${result.productTitle}`);
      console.log(`   Product ID: ${result.productId}`);
      
      console.log(`\n🔄 Deletion Details:`);
      console.log(`   Was Only Variant: ${result.wasOnlyVariant}`);
      console.log(`   Product Also Deleted: ${result.productDeleted}`);
      
      if (result.productDeletionError) {
        console.log(`   ❌ Product Deletion Error: ${result.productDeletionError}`);
      }
      
      if (result.wasOnlyVariant && !result.productDeleted) {
        console.log('\n⚠️  ISSUE: Variant was deleted but product was not!');
        console.log('This means there is an empty product shell in Shopify.');
      } else if (result.wasOnlyVariant && result.productDeleted) {
        console.log('\n✅ SUCCESS: Both variant and product were deleted!');
        console.log('No empty product shell remains.');
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testProductDeletion()
  .then(() => {
    console.log('\n🏁 Test script completed');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Test script failed:', error);
    process.exit(1);
  });
