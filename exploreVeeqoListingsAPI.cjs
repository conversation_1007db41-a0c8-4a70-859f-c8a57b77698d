const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
require('dotenv').config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ Missing VEEQO_API_KEY');
  process.exit(1);
}

console.log('🔍 VEEQO LISTINGS API EXPLORER');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to explore different API endpoints for listings
async function exploreListingsEndpoints() {
  console.log('🔍 Exploring different Veeqo API endpoints for listings...\n');
  
  const endpointsToTry = [
    'https://api.veeqo.com/listings',
    'https://api.veeqo.com/channel_products',
    'https://api.veeqo.com/sellables',
    'https://api.veeqo.com/products?page=1&page_size=5',
    'https://api.veeqo.com/channels'
  ];
  
  for (const endpoint of endpointsToTry) {
    console.log(`📡 Testing: ${endpoint}`);
    const result = await makeVeeqoRequest(endpoint);
    
    if (result.success) {
      console.log(`   ✅ Success! Response type: ${Array.isArray(result.data) ? 'Array' : 'Object'}`);
      
      if (Array.isArray(result.data)) {
        console.log(`   📊 Found ${result.data.length} items`);
        if (result.data.length > 0) {
          console.log(`   🔑 First item keys: ${Object.keys(result.data[0]).join(', ')}`);
          
          // Look for items with SKU matching our pattern
          const matchingItems = result.data.filter(item => 
            (item.sku_code && item.sku_code.includes('DISC_')) ||
            (item.remote_sku && item.remote_sku.includes('DISC_')) ||
            (item.sellables && item.sellables.some(s => s.sku_code && s.sku_code.includes('DISC_')))
          );
          
          if (matchingItems.length > 0) {
            console.log(`   🎯 Found ${matchingItems.length} items with DISC_ SKUs`);
            console.log(`   📋 Sample matching item:`, JSON.stringify(matchingItems[0], null, 2));
          }
        }
      } else {
        console.log(`   🔑 Object keys: ${Object.keys(result.data).join(', ')}`);
      }
    } else {
      console.log(`   ❌ Failed: ${result.error}`);
    }
    console.log('');
  }
}

// Function to search for a specific SKU across different endpoints
async function searchForSku(sku) {
  console.log(`\n🔍 Searching for SKU: ${sku} across different endpoints...\n`);
  
  // Try products endpoint with search
  console.log(`📡 Searching products for SKU: ${sku}`);
  const productsResult = await makeVeeqoRequest(`https://api.veeqo.com/products?page=1&page_size=50`);
  
  if (productsResult.success && Array.isArray(productsResult.data)) {
    const matchingProducts = productsResult.data.filter(product => {
      // Check if any sellable has the matching SKU
      if (product.sellables) {
        return product.sellables.some(sellable => sellable.sku_code === sku);
      }
      return false;
    });
    
    if (matchingProducts.length > 0) {
      console.log(`   ✅ Found ${matchingProducts.length} matching product(s)`);
      
      matchingProducts.forEach((product, index) => {
        console.log(`\n   📋 Product ${index + 1}:`);
        console.log(`      ID: ${product.id}`);
        console.log(`      Title: ${product.title}`);
        console.log(`      Status: ${product.status || 'N/A'}`);
        console.log(`      Created: ${product.created_at}`);
        
        // Show sellables
        if (product.sellables) {
          const matchingSellables = product.sellables.filter(s => s.sku_code === sku);
          console.log(`      Matching Sellables (${matchingSellables.length}):`);
          matchingSellables.forEach(sellable => {
            console.log(`         - Sellable ID: ${sellable.id}, SKU: ${sellable.sku_code}, Price: ${sellable.price}`);
          });
        }
        
        // Show channel products (listings)
        if (product.channel_products) {
          console.log(`      Channel Products/Listings (${product.channel_products.length}):`);
          product.channel_products.forEach(cp => {
            console.log(`         - Channel Product ID: ${cp.id}`);
            console.log(`           Channel: ${cp.channel?.name || 'Unknown'} (${cp.channel?.short_name || 'N/A'})`);
            console.log(`           Status: ${cp.status}`);
            console.log(`           Remote ID: ${cp.remote_id}`);
            console.log(`           Remote Title: ${cp.remote_title}`);
          });
        }
      });
      
      return matchingProducts;
    } else {
      console.log(`   ❌ No products found with SKU: ${sku}`);
    }
  } else {
    console.log(`   ❌ Failed to search products: ${productsResult.error}`);
  }
  
  return [];
}

// Function to test deletion endpoints for a specific item
async function testDeletionEndpoints(productId, channelProductId) {
  console.log(`\n🧪 Testing deletion endpoints for Product ID: ${productId}, Channel Product ID: ${channelProductId}`);
  
  const deleteEndpoints = [
    `https://api.veeqo.com/channel_products/${channelProductId}`,
    `https://api.veeqo.com/products/${productId}/channel_products/${channelProductId}`,
    `https://api.veeqo.com/listings/${channelProductId}`,
    `https://api.veeqo.com/products/${productId}/listings/${channelProductId}`
  ];
  
  for (const endpoint of deleteEndpoints) {
    console.log(`\n🔍 Testing GET ${endpoint} (to see if endpoint exists)...`);
    const getResult = await makeVeeqoRequest(endpoint, 'GET');
    
    if (getResult.success) {
      console.log(`   ✅ GET successful - endpoint exists and returns data`);
      console.log(`   📊 Response keys: ${Object.keys(getResult.data).join(', ')}`);
      
      // Show current status
      if (getResult.data.status) {
        console.log(`   📋 Current status: ${getResult.data.status}`);
      }
      
      console.log(`\n🗑️  Testing DELETE ${endpoint} (DRY RUN - not actually deleting)...`);
      console.log(`   💡 Would send: DELETE ${endpoint}`);
      console.log(`   ⚠️  Use 'delete-single' command to actually delete`);
      
    } else {
      console.log(`   ❌ GET failed: ${getResult.error}`);
    }
  }
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🛠️  USAGE:
  node exploreVeeqoListingsAPI.cjs <command> [options]

📋 COMMANDS:
  explore                     - Explore different Veeqo API endpoints
  search <sku>               - Search for a specific SKU (e.g., DISC_46948)
  test-delete <product_id> <channel_product_id>  - Test deletion endpoints

📝 EXAMPLES:
  node exploreVeeqoListingsAPI.cjs explore
  node exploreVeeqoListingsAPI.cjs search DISC_46948
  node exploreVeeqoListingsAPI.cjs test-delete 123456 789012
`);
    return;
  }
  
  const command = args[0];
  
  try {
    switch (command) {
      case 'explore':
        await exploreListingsEndpoints();
        break;
        
      case 'search':
        if (args.length < 2) {
          console.error('❌ Please provide a SKU to search for');
          return;
        }
        const sku = args[1];
        const matchingProducts = await searchForSku(sku);
        
        if (matchingProducts.length > 0 && matchingProducts[0].channel_products && matchingProducts[0].channel_products.length > 0) {
          const product = matchingProducts[0];
          const channelProduct = product.channel_products[0];
          console.log(`\n💡 To test deletion, run:`);
          console.log(`node exploreVeeqoListingsAPI.cjs test-delete ${product.id} ${channelProduct.id}`);
        }
        break;
        
      case 'test-delete':
        if (args.length < 3) {
          console.error('❌ Please provide product ID and channel product ID');
          return;
        }
        const productId = args[1];
        const channelProductId = args[2];
        await testDeletionEndpoints(productId, channelProductId);
        break;
        
      default:
        console.error(`❌ Unknown command: ${command}`);
        console.log('Use no arguments to see usage instructions.');
    }
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the main function
main();
