import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function getTableStructure() {
  try {
    console.log('Checking table structures...\n');

    // Get a sample record from t_sdasins to see the structure
    const { data: sdasinSample, error: sdasinError } = await supabase
      .from('t_sdasins')
      .select('*')
      .limit(1);
    
    if (sdasinError) {
      console.error('Error fetching t_sdasins:', sdasinError);
    } else if (sdasinSample && sdasinSample.length > 0) {
      console.log('t_sdasins columns:');
      console.log(Object.keys(sdasinSample[0]).sort());
      console.log('\nSample t_sdasins record:');
      console.log(sdasinSample[0]);
    }

    // Get sample brands
    const { data: brands, error: brandsError } = await supabase
      .from('t_brands')
      .select('id, brand')
      .limit(5);
    
    if (brandsError) {
      console.error('Error fetching t_brands:', brandsError);
    } else {
      console.log('\nt_brands sample:');
      console.log(brands);
    }

    // Get sample molds
    const { data: molds, error: moldsError } = await supabase
      .from('t_molds')
      .select('id, mold')
      .limit(5);
    
    if (moldsError) {
      console.error('Error fetching t_molds:', moldsError);
    } else {
      console.log('\nt_molds sample:');
      console.log(molds);
    }

    // Get sample plastics
    const { data: plastics, error: plasticsError } = await supabase
      .from('t_plastics')
      .select('id, plastic')
      .limit(5);
    
    if (plasticsError) {
      console.error('Error fetching t_plastics:', plasticsError);
    } else {
      console.log('\nt_plastics sample:');
      console.log(plastics);
    }

    // Check for some sample notes data
    const { data: notesData, error: notesError } = await supabase
      .from('t_sdasins')
      .select('id, notes')
      .not('notes', 'is', null)
      .limit(10);
    
    if (notesError) {
      console.error('Error fetching notes:', notesError);
    } else {
      console.log('\nSample notes from t_sdasins:');
      notesData.forEach(record => {
        console.log(`ID ${record.id}: ${record.notes}`);
      });
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

getTableStructure();
