// googleSheetsImporter.js - Import disc data from Google Sheets to t_discs table

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fetch from 'node-fetch';
import { parse } from 'csv-parse/sync';
import { google } from 'googleapis';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Sheet names used for workflow
const ENTER_SHEET_NAME = 'Enter';
const COMPLETE_SHEET_NAME = 'Complete';


/**
 * Initialize Google Sheets API client with write access
 */
async function initializeGoogleSheetsAPIForWrite() {
    let auth;
    if (process.env.GOOGLE_SERVICE_ACCOUNT_KEY) {
        try {
            const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_KEY);
            auth = new google.auth.GoogleAuth({ credentials, scopes: ['https://www.googleapis.com/auth/spreadsheets'] });
        } catch (e) {
            // Fall back to using as path to key file if JSON parse fails
            auth = new google.auth.GoogleAuth({ keyFile: process.env.GOOGLE_SERVICE_ACCOUNT_KEY, scopes: ['https://www.googleapis.com/auth/spreadsheets'] });
        }
    } else if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        auth = new google.auth.GoogleAuth({ keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS, scopes: ['https://www.googleapis.com/auth/spreadsheets'] });
    } else {
        // API key cannot write; we will throw to allow caller to handle gracefully
        throw new Error('Google Sheets write auth not configured. Set GOOGLE_SERVICE_ACCOUNT_KEY or GOOGLE_APPLICATION_CREDENTIALS for write access.');
    }
    return google.sheets({ version: 'v4', auth });
}

function extractSpreadsheetId(sheetsUrl) {
    const m = sheetsUrl.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
    if (!m) throw new Error('Invalid Google Sheets URL');
    return m[1];
}

async function getSheetIdByName(sheets, spreadsheetId, sheetName) {
    const resp = await sheets.spreadsheets.get({
        spreadsheetId,
        fields: 'sheets(properties(sheetId,title))'
    });
    const sheet = resp.data.sheets?.find(s => s.properties?.title === sheetName);
    return sheet?.properties?.sheetId ?? null;
}

async function batchWriteNewIds(sheets, spreadsheetId, sheetName, entries /* [{row, id}] */) {
    if (!entries || entries.length === 0) return { updatedCells: 0 };
    const data = entries.map(e => ({
        range: `${sheetName}!M${e.row}:M${e.row}`,
        values: [[e.id]]
    }));
    const resp = await sheets.spreadsheets.values.batchUpdate({
        spreadsheetId,
        requestBody: {
            valueInputOption: 'RAW',
            data
        }
    });
    return { updatedCells: resp.data.totalUpdatedCells || 0 };
}

async function fetchSheetAllValues(sheets, spreadsheetId, sheetName) {
    const resp = await sheets.spreadsheets.values.get({
        spreadsheetId,
        range: `${sheetName}!A1:ZZ10000`,
        valueRenderOption: 'UNFORMATTED_VALUE',
        dateTimeRenderOption: 'FORMATTED_STRING'
    });
    return resp.data.values || [];
}

async function appendRows(sheets, spreadsheetId, sheetName, rows /* Array<Array<any>> */) {
    if (!rows || rows.length === 0) return { updates: { updatedRows: 0 } };
    const resp = await sheets.spreadsheets.values.append({
        spreadsheetId,
        range: `${sheetName}!A1`,
        valueInputOption: 'RAW',
        insertDataOption: 'INSERT_ROWS',
        requestBody: { values: rows }
    });
    return resp.data || { updates: { updatedRows: rows.length } };
}

async function deleteRowsByIndices(sheets, spreadsheetId, sheetId, rowNumbers /* 1-based row numbers */) {
    if (!rowNumbers || rowNumbers.length === 0) return { replies: [] };
    // Sort descending to keep indices valid as we delete
    const sorted = [...new Set(rowNumbers)].sort((a, b) => b - a);
    const requests = sorted.map(rowNum => ({
        deleteDimension: {
            range: {
                sheetId,
                dimension: 'ROWS',
                startIndex: rowNum - 1, // inclusive
                endIndex: rowNum // exclusive
            }
        }
    }));
    const resp = await sheets.spreadsheets.batchUpdate({
        spreadsheetId,
        requestBody: { requests }
    });
    return resp.data;
}

/**
 * Convert Google Sheets URL to CSV export URL
 * @param {string} sheetsUrl - The Google Sheets URL
 * @returns {string} - The CSV export URL
 */
function convertToCSVUrl(sheetsUrl) {
    // Extract the spreadsheet ID from the URL
    const match = sheetsUrl.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
    if (!match) {
        throw new Error('Invalid Google Sheets URL format');
    }

    const spreadsheetId = match[1];

    // Extract gid if present
    const gidMatch = sheetsUrl.match(/[#&]gid=([0-9]+)/);
    const gid = gidMatch ? gidMatch[1] : '0';

    return `https://docs.google.com/spreadsheets/d/${spreadsheetId}/export?format=csv&gid=${gid}`;
}

/**
 * Fetch data from Google Sheets as CSV
 * @param {string} sheetsUrl - The Google Sheets URL
 * @returns {Array} - Array of row objects
 */
async function fetchGoogleSheetsData(sheetsUrl) {
    const csvUrl = convertToCSVUrl(sheetsUrl);

    const response = await fetch(csvUrl);
    if (!response.ok) {
        throw new Error(`Failed to fetch Google Sheets data: ${response.status} ${response.statusText}`);
    }

    const csvData = await response.text();

    // Parse CSV data
    const records = parse(csvData, {
        columns: true,
        skip_empty_lines: true,
        trim: true
    });

    return records;
}

/**
 * Check if a record is empty (all required fields are empty)
 * @param {Object} record - The record to check
 * @returns {boolean} - True if the record is empty
 */
function isEmptyRecord(record) {
    // Check if ALL the required fields are empty - if so, it's an empty row to skip
    const requiredFields = ['shipment_id', 'mps_id', 'color', 'weight_mfg', 'weight_scale', 'grade', 'description', 'location'];
    return requiredFields.every(field => !record[field] || record[field].toString().trim() === '');
}

/**
 * Validate a single disc record
 * @param {Object} record - The disc record to validate
 * @param {number} rowIndex - The row index for error reporting
 * @param {Object} lookupData - Lookup data for validation
 * @returns {Object} - Validation result
 */
async function validateDiscRecord(record, rowIndex, lookupData) {
    const errors = [];
    const warnings = [];

    // Skip empty records
    if (isEmptyRecord(record)) {
        return {
            isValid: false,
            isEmpty: true,
            errors: [],
            warnings: [],
            row: rowIndex + 2
        };
    }

    // Check ALL required fields (color_modifier can be null)
    const requiredFields = [
        { field: 'shipment_id', name: 'shipment_id' },
        { field: 'mps_id', name: 'mps_id' },
        { field: 'color', name: 'color' },
        { field: 'weight_mfg', name: 'weight_mfg' },
        { field: 'weight_scale', name: 'weight_scale' },
        { field: 'grade', name: 'grade' },
        { field: 'description', name: 'description' }
        // location is intentionally not required; we always set it to 'BS' on import
    ];

    // Check that all required fields are present and not empty
    for (const { field, name } of requiredFields) {
        if (!record[field] || record[field].toString().trim() === '') {
            errors.push(`${name} is required and cannot be blank`);
        }
    }

    // If basic required field validation failed, don't do further validation
    if (errors.length > 0) {
        return {
            isValid: false,
            isEmpty: false,
            errors,
            warnings,
            row: rowIndex + 2
        };
    }

    // Validate shipment_id exists in database
    const shipmentId = parseInt(record.shipment_id.toString().trim());
    if (isNaN(shipmentId)) {
        errors.push(`shipment_id '${record.shipment_id}' is not a valid number`);
    } else if (!lookupData.shipments.has(shipmentId)) {
        console.log(`DEBUG: shipment_id ${shipmentId} not found in set of ${lookupData.shipments.size} shipments`);
        errors.push(`shipment_id ${shipmentId} does not exist in t_shipments`);
    }

    // Validate mps_id exists in database
    const mpsId = parseInt(record.mps_id.toString().trim());
    if (isNaN(mpsId)) {
        errors.push(`mps_id '${record.mps_id}' is not a valid number`);
    } else if (!lookupData.mps.has(mpsId)) {
        console.log(`DEBUG: mps_id ${mpsId} not found in set of ${lookupData.mps.size} MPS records`);
        errors.push(`mps_id ${mpsId} does not exist in t_mps`);
    }

    // Validate color exists in database
    const colorName = record.color.toString().trim().toLowerCase();
    const colorId = lookupData.colors.get(colorName);
    if (!colorId) {
        errors.push(`color '${record.color}' does not exist in t_colors`);
    }

    // Validate data types and constraints (all fields are now required)
    const weight = parseFloat(record.weight_scale);
    if (isNaN(weight) || weight <= 0) {
        errors.push('weight_scale must be a positive number');
    }

    const weightMfg = parseFloat(record.weight_mfg);
    if (isNaN(weightMfg) || weightMfg <= 0) {
        errors.push('weight_mfg must be a positive number');
    }

    const grade = parseInt(record.grade);
    if (isNaN(grade) || grade < -10 || grade > 10) {
        errors.push('grade must be an integer between -10 and 10');
    }

    return {
        isValid: errors.length === 0,
        errors,
        warnings,
        row: rowIndex + 2 // +2 because CSV parsing starts at 0 and row 1 is header
    };
}

/**
 * Load lookup data for validation
 * @returns {Object} - Lookup data maps
 */
async function loadLookupData() {
    console.log('Loading shipments...');
    // Load shipments - get ALL records with explicit range to bypass default limits
    let allShipments = [];
    let from = 0;
    const pageSize = 1000;

    while (true) {
        const { data: shipments, error: shipmentsError } = await supabase
            .from('t_shipments')
            .select('id')
            .order('id')
            .range(from, from + pageSize - 1);

        if (shipmentsError) {
            throw new Error(`Failed to load shipments: ${shipmentsError.message}`);
        }

        if (!shipments || shipments.length === 0) break;

        allShipments = allShipments.concat(shipments);

        if (shipments.length < pageSize) break; // Last page
        from += pageSize;
    }

    console.log(`Loaded ${allShipments.length} shipments. Range: ${allShipments[0]?.id} to ${allShipments[allShipments.length-1]?.id}`);

    console.log('Loading MPS records...');
    // Load MPS records - get ALL records with explicit range
    let allMps = [];
    from = 0;

    while (true) {
        const { data: mps, error: mpsError } = await supabase
            .from('t_mps')
            .select('id')
            .order('id')
            .range(from, from + pageSize - 1);

        if (mpsError) {
            throw new Error(`Failed to load MPS records: ${mpsError.message}`);
        }

        if (!mps || mps.length === 0) break;

        allMps = allMps.concat(mps);

        if (mps.length < pageSize) break; // Last page
        from += pageSize;
    }

    console.log(`Loaded ${allMps.length} MPS records. Range: ${allMps[0]?.id} to ${allMps[allMps.length-1]?.id}`);

    console.log('Loading colors...');
    // Load colors (should be small enough to not need pagination)
    const { data: colors, error: colorsError } = await supabase
        .from('t_colors')
        .select('id, color')
        .order('id');

    if (colorsError) {
        throw new Error(`Failed to load colors: ${colorsError.message}`);
    }
    console.log(`Loaded ${colors.length} colors:`, colors.map(c => c.color).join(', '));

    const lookupData = {
        shipments: new Set(allShipments.map(s => s.id)),
        mps: new Set(allMps.map(m => m.id)),
        colors: new Map(colors.map(c => [c.color.toLowerCase(), c.id]))
    };

    // Verify the sets contain the expected data
    console.log('Shipment Set size:', lookupData.shipments.size);
    console.log('MPS Set size:', lookupData.mps.size);
    console.log('Colors Map size:', lookupData.colors.size);

    // Check if our test IDs are in the sets
    console.log('DEBUG: Does shipment 1992 exist?', lookupData.shipments.has(1992));
    console.log('DEBUG: Does shipment 1997 exist?', lookupData.shipments.has(1997));
    console.log('DEBUG: Does MPS 19214 exist?', lookupData.mps.has(19214));
    console.log('DEBUG: Does MPS 19652 exist?', lookupData.mps.has(19652));

    return lookupData;
}

/**
 * Transform validated record for database insertion
 * @param {Object} record - The original record
 * @param {Object} lookupData - Lookup data for mapping
 * @returns {Object} - Transformed record for database
 */
function transformRecordForDB(record, lookupData) {
    const transformed = {
        shipment_id: parseInt(record.shipment_id),
        mps_id: parseInt(record.mps_id),
        color_id: lookupData.colors.get(record.color.toLowerCase()),
        weight: parseFloat(record.weight_scale),  // weight_scale -> weight (required)
        weight_mfg: parseFloat(record.weight_mfg),   // weight_mfg -> weight_mfg (required)
        grade: parseInt(record.grade),  // required
        description: record.description,  // required
        location: 'BS',  // force BS regardless of sheet value
        created_by: 'google_sheets_import'
    };

    // Add optional fields if present
    if (record.color_modifier) transformed.color_modifier = record.color_modifier;
    if (record.notes) transformed.notes = record.notes;

    return transformed;
}

/**
 * Main import function
 * @param {string} googleSheetsUrl - The Google Sheets URL
 * @param {boolean} validateOnly - If true, only validate without importing
 * @returns {Object} - Import results
 */
export async function importDiscsFromGoogleSheets(googleSheetsUrl, validateOnly = false) {
    try {
        console.log('Fetching data from Google Sheets...');
        const records = await fetchGoogleSheetsData(googleSheetsUrl);

        console.log('Loading lookup data...');
        const lookupData = await loadLookupData();



        console.log('Validating records...');
        const validationResults = {
            totalRecords: records.length,
            validRecords: 0,
            invalidRecords: 0,
            emptyRecords: 0,
            errors: []
        };

        const validRecords = [];
        const recordsWithRowNumbers = []; // Track original row positions

        for (let i = 0; i < records.length; i++) {
            const validation = await validateDiscRecord(records[i], i, lookupData);

            if (validation.isEmpty) {
                validationResults.emptyRecords++;
                // Track empty records with their row numbers
                recordsWithRowNumbers.push({
                    originalRowIndex: i,
                    originalRow: i + 2, // +2 because row 1 is header
                    record: null,
                    status: 'empty'
                });
                continue;
            } else if (validation.isValid) {
                validationResults.validRecords++;
                validRecords.push(records[i]);
                // Track valid records with their row numbers
                recordsWithRowNumbers.push({
                    originalRowIndex: i,
                    originalRow: i + 2, // +2 because row 1 is header
                    record: records[i],
                    status: 'valid'
                });
            } else {
                validationResults.invalidRecords++;
                validation.errors.forEach(error => {
                    validationResults.errors.push({
                        row: validation.row,
                        message: error
                    });
                });
                // Track invalid records with their row numbers
                recordsWithRowNumbers.push({
                    originalRowIndex: i,
                    originalRow: i + 2, // +2 because row 1 is header
                    record: records[i],
                    status: 'invalid'
                });
            }
        }

        const result = {
            success: true,
            message: validateOnly ? 'Validation completed' : 'Import completed',
            validationResults
        };

        // If validation only, return early
        if (validateOnly) {
            return result;
        }

        // Import valid records sequentially to maintain order
        if (validRecords.length > 0) {
            console.log(`Importing ${validRecords.length} valid records sequentially to maintain order...`);

            // Create an array to hold results in original sheet order
            const orderedResults = new Array(recordsWithRowNumbers.length);
            let importedCount = 0;

            // Process records in original order
            for (let i = 0; i < recordsWithRowNumbers.length; i++) {
                const rowInfo = recordsWithRowNumbers[i];

                if (rowInfo.status === 'valid') {
                    const transformedRecord = transformRecordForDB(rowInfo.record, lookupData);

                    console.log(`Inserting record from row ${rowInfo.originalRow} (${importedCount + 1}/${validRecords.length})...`);

                    const { data: insertedRecord, error: insertError } = await supabase
                        .from('t_discs')
                        .insert([transformedRecord])
                        .select('id')
                        .single();

                    if (insertError) {
                        throw new Error(`Failed to insert record from row ${rowInfo.originalRow}: ${insertError.message}`);
                    }

                    orderedResults[i] = insertedRecord.id;
                    importedCount++;
                } else {
                    // For empty/invalid rows, put null in the results array
                    orderedResults[i] = null;
                }
            }

            console.log(`Successfully imported ${importedCount} records in original sheet order`);

            result.importResults = {
                recordsImported: importedCount,
                newIds: orderedResults, // This maintains the original order with nulls for skipped rows
                orderedResults: orderedResults.map((id, index) => ({
                    originalRow: recordsWithRowNumbers[index].originalRow,
                    newId: id,
                    status: recordsWithRowNumbers[index].status
                }))
            };
        } else {
            result.importResults = {
                recordsImported: 0,
                newIds: [],
                orderedResults: []
            };
        }

            // After successful import, write back new IDs to Enter!M and move rows to Complete
            try {
                const sheets = await initializeGoogleSheetsAPIForWrite();
                const spreadsheetId = extractSpreadsheetId(googleSheetsUrl);

                // Collect rows and IDs for valid/imported records
                const idWrites = [];
                const rowsToMove = [];
                for (let i = 0; i < recordsWithRowNumbers.length; i++) {
                    const rowInfo = recordsWithRowNumbers[i];
                    const newId = result.importResults.newIds[i];
                    if (rowInfo.status === 'valid' && newId) {
                        idWrites.push({ row: rowInfo.originalRow, id: newId });
                        rowsToMove.push(rowInfo.originalRow);
                    }
                }

                let wroteIdCells = 0;
                if (idWrites.length > 0) {
                    const writeRes = await batchWriteNewIds(sheets, spreadsheetId, ENTER_SHEET_NAME, idWrites);
                    wroteIdCells = writeRes.updatedCells || 0;
                }

                // Fetch Enter sheet values to build rows to append
                const enterValues = await fetchSheetAllValues(sheets, spreadsheetId, ENTER_SHEET_NAME);
                const rowsToAppend = rowsToMove
                    .map(rn => enterValues?.[rn - 1]) // rows are 1-based; array is 0-based
                    .filter(r => Array.isArray(r) && r.length > 0);

                let movedRows = 0;
                if (rowsToAppend.length > 0) {
                    await appendRows(sheets, spreadsheetId, COMPLETE_SHEET_NAME, rowsToAppend);
                    // Delete rows from Enter sheet after appending
                    const enterSheetId = await getSheetIdByName(sheets, spreadsheetId, ENTER_SHEET_NAME);
                    if (enterSheetId != null) {
                        await deleteRowsByIndices(sheets, spreadsheetId, enterSheetId, rowsToMove);
                    }
                    movedRows = rowsToAppend.length;
                }

                result.sheetsUpdate = {
                    success: true,
                    wroteIdCells,
                    movedRows,
                    note: `Wrote IDs to ${ENTER_SHEET_NAME}!M and moved ${movedRows} row(s) to ${ COMPLETE_SHEET_NAME }`
                };
            } catch (sheetErr) {
                // Don't fail the import if sheet write-back fails; report it in the result
                result.sheetsUpdate = {
                    success: false,
                    error: sheetErr.message
                };
                console.warn('[googleSheetsImporter] Sheet write-back/move skipped/failure:', sheetErr.message);
            }


        return result;

    } catch (error) {
        console.error('Import error:', error);
        return {
            success: false,
            error: error.message,
            details: error.stack
        };
    }
}

export default importDiscsFromGoogleSheets;
