-- Function to insert a record into t_images
CREATE OR R<PERSON>LACE FUNCTION insert_t_images_record(
  p_table_name TEXT,
  p_record_id INTEGER,
  p_created_by TEXT DEFAULT 'system'
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_result JSONB;
  v_id INTEGER;
BEGIN
  -- Insert the record
  INSERT INTO t_images (
    table_name,
    record_id,
    created_by,
    created_at
  )
  VALUES (
    p_table_name,
    p_record_id,
    p_created_by,
    NOW()
  )
  RETURNING id INTO v_id;
  
  -- Return the result
  v_result := jsonb_build_object(
    'success', TRUE,
    'id', v_id,
    'table_name', p_table_name,
    'record_id', p_record_id
  );
  
  RETURN v_result;
EXCEPTION WHEN OTHERS THEN
  v_result := jsonb_build_object(
    'success', FALSE,
    'error', SQLERRM,
    'detail', SQLSTATE
  );
  
  RETURN v_result;
END;
$$;

-- Function to debug the t_images schema
CREATE OR REPLACE FUNCTION debug_t_images_schema()
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_result JSONB;
BEGIN
  -- Get column information
  SELECT jsonb_agg(jsonb_build_object(
    'column_name', column_name,
    'data_type', data_type,
    'is_nullable', is_nullable
  ))
  INTO v_result
  FROM information_schema.columns
  WHERE table_name = 't_images'
  AND table_schema = 'public';
  
  RETURN v_result;
END;
$$;
