import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testSpecificRecordParsing() {
  try {
    console.log('Testing parsing for record 61554...\n');

    // Get reference data
    const { data: brands, error: brandsError } = await supabase
      .from('t_brands')
      .select('id, brand');
    
    const { data: molds, error: moldsError } = await supabase
      .from('t_molds')
      .select('id, mold');
    
    const { data: plastics, error: plasticsError } = await supabase
      .from('t_plastics')
      .select('id, plastic');

    if (brandsError || moldsError || plasticsError) {
      console.error('Error loading reference data');
      return;
    }

    // Create lookup maps
    const brandMap = new Map();
    brands.sort((a, b) => b.brand.length - a.brand.length)
      .forEach(brand => brandMap.set(brand.brand.toLowerCase(), brand));

    const moldMap = new Map();
    molds.sort((a, b) => b.mold.length - a.mold.length)
      .forEach(mold => moldMap.set(mold.mold.toLowerCase(), mold));

    const plasticMap = new Map();
    plastics.sort((a, b) => b.plastic.length - a.plastic.length)
      .forEach(plastic => plasticMap.set(plastic.plastic.toLowerCase(), plastic));

    // Get the specific record
    const { data: record, error: recordError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes, color_id')
      .eq('id', 61554)
      .single();

    if (recordError) {
      console.error('Error getting record:', recordError);
      return;
    }

    console.log('Record details:');
    console.log('ID:', record.id);
    console.log('Notes:', record.notes);
    console.log('Raw notes:', record.raw_notes || 'NULL');
    console.log('Color ID:', record.color_id || 'NULL');
    console.log('');

    // Test the parsing function on this record
    const parsed = parseNotes(record.notes, brandMap, moldMap, plasticMap, record.color_id);
    
    console.log('Parsing result:');
    console.log('Has updates:', parsed.hasUpdates);
    console.log('Brand:', parsed.brand || 'NULL');
    console.log('Mold:', parsed.mold || 'NULL');
    console.log('Plastic:', parsed.plastic || 'NULL');
    console.log('Min weight:', parsed.minWeight || 'NULL');
    console.log('Max weight:', parsed.maxWeight || 'NULL');
    console.log('Color ID:', parsed.colorId || 'NULL');
    console.log('Remaining notes:', parsed.remainingNotes);
    console.log('');

    if (parsed.hasUpdates) {
      console.log('This record should be updated. Let\'s update it now...');
      
      const updateData = {};
      if (!record.raw_notes) updateData.raw_notes = record.notes;
      if (parsed.brand) updateData.parsed_brand = parsed.brand;
      if (parsed.mold) updateData.parsed_mold = parsed.mold;
      if (parsed.plastic) updateData.parsed_plastic = parsed.plastic;
      if (parsed.minWeight) updateData.parsed_min_weight = parsed.minWeight;
      if (parsed.maxWeight) updateData.parsed_max_weight = parsed.maxWeight;
      if (parsed.colorId !== null) updateData.color_id = parsed.colorId;
      if (parsed.remainingNotes !== record.notes) updateData.notes = parsed.remainingNotes;

      console.log('Update data:', updateData);

      const { error: updateError } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', record.id);

      if (updateError) {
        console.error('Update error:', updateError);
      } else {
        console.log('Record updated successfully!');
      }
    } else {
      console.log('No updates needed for this record.');
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

function parseNotes(notes, brandMap, moldMap, plasticMap, currentColorId = null) {
  if (!notes || notes.startsWith('XXXX')) {
    return { hasUpdates: false };
  }

  // Check for pack listings first
  const packPatterns = [/\d+\s*pack/i, /\d+\s*Pack/i, /\d+-pack/i, /\d+-Pack/i];
  for (const pattern of packPatterns) {
    if (pattern.test(notes)) {
      return { 
        hasUpdates: true, 
        remainingNotes: 'XXXX Pack',
        brand: null,
        mold: null,
        plastic: null,
        minWeight: null,
        maxWeight: null,
        colorId: null
      };
    }
  }

  const result = {
    brand: null,
    mold: null,
    plastic: null,
    minWeight: null,
    maxWeight: null,
    colorId: null,
    remainingNotes: notes,
    hasUpdates: false
  };

  let workingNotes = notes;

  // Handle color_id for "Colors Will Vary" and "Colors May Vary"
  if (!currentColorId) {
    const colorPhrases = ['Colors Will Vary', 'Colors May Vary'];
    for (const phrase of colorPhrases) {
      const regex = new RegExp(escapeRegex(phrase), 'gi');
      if (regex.test(workingNotes)) {
        result.colorId = 23;
        workingNotes = workingNotes.replace(regex, '').trim();
        result.hasUpdates = true;
        break;
      }
    }
  }

  // Remove filler text phrases (simplified list for testing)
  const fillerPhrases = [
    'Mid-Range Golf Disc',
    'Golf Disc',
    'Disc Golf'
  ];

  for (const phrase of fillerPhrases) {
    const regex = new RegExp(escapeRegex(phrase), 'gi');
    if (regex.test(workingNotes)) {
      workingNotes = workingNotes.replace(regex, '').trim();
      result.hasUpdates = true;
    }
  }

  // Extract weight ranges
  const weightMatch = workingNotes.match(/(\d{3})-(\d{3})g?/);
  if (weightMatch) {
    result.minWeight = parseInt(weightMatch[1]);
    result.maxWeight = parseInt(weightMatch[2]);
    workingNotes = workingNotes.replace(weightMatch[0], '').trim();
    result.hasUpdates = true;
  }

  // Try to find brand matches
  for (const [brandName, brandData] of brandMap) {
    const brandRegex = new RegExp(`\\b${escapeRegex(brandName)}\\b`, 'gi');
    if (brandRegex.test(workingNotes)) {
      result.brand = brandData.brand;
      workingNotes = workingNotes.replace(brandRegex, '').trim();
      result.hasUpdates = true;
      break;
    }
  }

  // Try to find mold matches
  for (const [moldName, moldData] of moldMap) {
    if (moldName.length >= 3) {
      const moldRegex = new RegExp(`\\b${escapeRegex(moldName)}\\b`, 'gi');
      if (moldRegex.test(workingNotes)) {
        result.mold = moldData.mold;
        workingNotes = workingNotes.replace(moldRegex, '').trim();
        result.hasUpdates = true;
        break;
      }
    }
  }

  // Try to find plastic matches
  for (const [plasticName, plasticData] of plasticMap) {
    if (plasticName.length >= 3) {
      const plasticRegex = new RegExp(`\\b${escapeRegex(plasticName)}\\b`, 'gi');
      if (plasticRegex.test(workingNotes)) {
        result.plastic = plasticData.plastic;
        workingNotes = workingNotes.replace(plasticRegex, '').trim();
        result.hasUpdates = true;
        break;
      }
    }
  }

  // Clean up remaining notes
  result.remainingNotes = workingNotes
    .replace(/\s+/g, ' ')
    .replace(/^\s*[-|,]\s*/, '')
    .replace(/\s*[-|,]\s*$/, '')
    .trim();

  return result;
}

function escapeRegex(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

testSpecificRecordParsing();
