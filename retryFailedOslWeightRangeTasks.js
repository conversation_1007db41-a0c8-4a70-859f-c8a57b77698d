import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Get failed OSL weight range tasks that had "not found" errors
 */
async function getFailedOslTasks() {
  try {
    console.log('📊 Querying failed fix_osl_weight_range tasks...');
    
    const { data: tasks, error } = await supabase
      .from('t_task_queue')
      .select('id, payload, result')
      .eq('task_type', 'fix_osl_weight_range')
      .eq('status', 'failed')
      .like('result', '%not found in Shopify%');

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    console.log(`📊 Found ${tasks.length} failed OSL weight range tasks with "not found" errors`);
    return tasks;
  } catch (error) {
    console.error(`❌ Error querying failed OSL tasks:`, error.message);
    throw error;
  }
}

/**
 * Reset failed OSL weight range tasks to pending status
 */
async function retryFailedOslTasks(dryRun = false) {
  try {
    const failedTasks = await getFailedOslTasks();
    
    if (failedTasks.length === 0) {
      console.log('ℹ️ No failed OSL weight range tasks found to retry');
      return;
    }

    console.log(`🚀 ${dryRun ? 'DRY RUN: Would retry' : 'Retrying'} ${failedTasks.length} failed OSL weight range tasks...`);
    
    const now = new Date();
    const scheduledAt = new Date(now.getTime() + 2 * 60 * 1000); // Schedule 2 minutes in future
    
    // Show sample of tasks that will be retried
    console.log('\nSample failed tasks:');
    failedTasks.slice(0, 5).forEach((task, index) => {
      const result = typeof task.result === 'string' ? JSON.parse(task.result) : task.result;
      console.log(`${index + 1}. Task ${task.id}: OSL ${result.osl_id || 'unknown'}, SKU ${result.sku || 'unknown'}`);
    });
    
    if (dryRun) {
      console.log(`\n🧪 DRY RUN: Would update ${failedTasks.length} tasks to pending status`);
      console.log(`📅 Would schedule them to run at: ${scheduledAt.toISOString()}`);
      return;
    }

    // Update tasks in batches
    const BATCH_SIZE = 100;
    let totalUpdated = 0;
    
    for (let i = 0; i < failedTasks.length; i += BATCH_SIZE) {
      const batch = failedTasks.slice(i, i + BATCH_SIZE);
      const taskIds = batch.map(task => task.id);
      
      const { error: updateError } = await supabase
        .from('t_task_queue')
        .update({
          status: 'pending',
          scheduled_at: scheduledAt.toISOString(),
          locked_at: null,
          locked_by: null,
          processed_at: null,
          result: null,
          error: null,
          fails: 0
        })
        .in('id', taskIds);

      if (updateError) {
        console.error(`❌ Error updating batch ${Math.floor(i / BATCH_SIZE) + 1}:`, updateError);
        throw updateError;
      }

      totalUpdated += batch.length;
      console.log(`✅ Updated batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(failedTasks.length / BATCH_SIZE)} (${batch.length} tasks, ${totalUpdated} total)`);
    }
    
    console.log(`🎉 Successfully reset ${totalUpdated} failed OSL weight range tasks to pending`);
    console.log(`📅 Tasks scheduled to retry at: ${scheduledAt.toISOString()}`);
    console.log(`\nℹ️ The updated task processor will now mark OSL products as not uploaded if they're not found in Shopify`);
    
  } catch (error) {
    console.error(`❌ Error retrying failed OSL tasks:`, error.message);
    throw error;
  }
}

/**
 * Show statistics about failed OSL tasks
 */
async function showFailedTaskStats() {
  try {
    console.log('\n📈 Failed OSL Task Statistics:');
    
    // Get all failed OSL tasks
    const { data: allFailedTasks, error: allError } = await supabase
      .from('t_task_queue')
      .select('result')
      .eq('task_type', 'fix_osl_weight_range')
      .eq('status', 'failed');

    if (allError) {
      throw new Error(`Database error: ${allError.message}`);
    }

    console.log(`📊 Total failed fix_osl_weight_range tasks: ${allFailedTasks.length}`);
    
    // Count different error types
    let notFoundErrors = 0;
    let otherErrors = 0;
    
    for (const task of allFailedTasks) {
      const result = typeof task.result === 'string' ? JSON.parse(task.result) : task.result;
      if (result.error && result.error.includes('not found in Shopify')) {
        notFoundErrors++;
      } else {
        otherErrors++;
      }
    }
    
    console.log(`📊 "Not found in Shopify" errors: ${notFoundErrors}`);
    console.log(`📊 Other errors: ${otherErrors}`);
    
  } catch (error) {
    console.error(`❌ Error getting task statistics:`, error.message);
  }
}

// Main execution
async function main() {
  try {
    // Check for dry run flag
    const dryRun = process.argv.includes('--dry-run');
    const showStats = process.argv.includes('--stats');
    
    if (dryRun) {
      console.log('🧪 Running in DRY RUN mode - no tasks will be updated');
    }
    
    if (showStats) {
      await showFailedTaskStats();
      return;
    }
    
    await retryFailedOslTasks(dryRun);
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
