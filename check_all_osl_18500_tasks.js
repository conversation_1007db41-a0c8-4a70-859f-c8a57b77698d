// check_all_osl_18500_tasks.js
// Check all tasks for OSL 18500

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkAllOSL18500Tasks() {
  try {
    console.log('Checking all tasks for OSL 18500...');
    
    const { data: tasks, error } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('task_type', 'publish_product_osl')
      .contains('payload', { id: 18500 })
      .order('created_at');
    
    if (error) {
      console.error('Error fetching tasks:', error);
      return;
    }
    
    console.log(`Found ${tasks.length} tasks for OSL 18500:`);
    
    tasks.forEach(task => {
      console.log(`\n📋 Task ${task.id}:`);
      console.log('Status:', task.status);
      console.log('Created:', task.created_at);
      console.log('Processed:', task.processed_at);
      console.log('Enqueued By:', task.enqueued_by);
      
      if (task.result) {
        const result = typeof task.result === 'string' ? JSON.parse(task.result) : task.result;
        console.log('Message:', result.message);
        console.log('Error:', result.error);
        
        if (result.error && result.error.includes('MANUAL FIX REQUIRED')) {
          console.log('🎯 FOUND: This task has the MANUAL FIX REQUIRED error!');
        }
      }
    });
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

checkAllOSL18500Tasks();
