import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function simpleDamageCheck() {
    try {
        console.log('🔍 Simple damage check...\n');
        
        // 1. Basic connection test
        console.log('1. Testing database connection...');
        
        const { data: testData, error: testError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id')
            .limit(1);

        if (testError) {
            console.error('❌ Database connection failed:', testError);
            console.log('\n📋 CRITICAL ISSUE: Database connection is broken');
            console.log('🔧 RECOMMENDATION: Check Supabase connection and credentials');
            return;
        }

        console.log('✅ Database connection working');

        // 2. Count total records
        console.log('\n2. Counting total records...');
        
        const { data: allRecords, error: allError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id');

        if (allError) {
            console.error('❌ Error getting records:', allError);
            return;
        }

        console.log(`📊 Total records: ${allRecords.length}`);

        // 3. Check row 22 specifically
        console.log('\n3. Checking row 22 (the problem row)...');
        
        const { data: row22, error: row22Error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, excel_column, mold_name, raw_line_type')
            .eq('excel_row_hint', 22);

        if (row22Error) {
            console.error('❌ Error checking row 22:', row22Error);
        } else {
            console.log(`Row 22 has ${row22.length} records:`);
            row22.forEach((record, index) => {
                console.log(`   ${index + 1}. ID: ${record.id}, Col: ${record.excel_column}, Type: ${record.raw_line_type}`);
            });
        }

        // 4. Check if we have any good records
        console.log('\n4. Checking for normal product records...');
        
        const { data: normalRecords, error: normalError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, mold_name, plastic_name')
            .gt('excel_row_hint', 300)
            .limit(5);

        if (normalError) {
            console.error('❌ Error getting normal records:', normalError);
        } else {
            console.log(`✅ Found ${normalRecords.length} normal product records (sample):`);
            normalRecords.forEach((record, index) => {
                console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.plastic_name} ${record.mold_name}`);
            });
        }

        // 5. Quick assessment
        console.log('\n📋 QUICK ASSESSMENT:');
        
        if (allRecords.length < 100) {
            console.log('❌ CRITICAL: Very few records - import is completely broken');
            console.log('🔧 RECOMMENDATION: REVERT ALL CHANGES and start over');
        } else if (allRecords.length < 800) {
            console.log('⚠️  WARNING: Import partially working but many records missing');
            console.log('🔧 RECOMMENDATION: REVERT to working version');
        } else {
            console.log('✅ MODERATE: Import working but has specific issues');
            console.log('🔧 RECOMMENDATION: Try targeted fixes');
        }

        if (row22.length > 0) {
            console.log('❌ CONFIRMED: Row 22 header issue exists');
        }

        console.log('\n🎯 IMMEDIATE ACTIONS NEEDED:');
        console.log('1. Decide: Revert or fix?');
        console.log('2. If fixing: Start with disabling enhanced MPS export');
        console.log('3. Fix header row parsing');
        console.log('4. Test basic export functionality');
        
    } catch (error) {
        console.error('❌ Simple check failed:', error.message);
        console.log('\n📋 CRITICAL: Even basic database operations are failing');
        console.log('🔧 RECOMMENDATION: REVERT ALL CHANGES immediately');
    }
}

simpleDamageCheck().catch(console.error);
