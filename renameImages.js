// renameImages.js - <PERSON><PERSON><PERSON> to rename images with sequential numbering (idempotent)
// Preserves original order based on file creation date
// Also copies renamed files to a local backup location and uploads to S3
import fs from 'fs/promises';
import path from 'path';
import { existsSync, createReadStream } from 'fs';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import dotenv from 'dotenv';
dotenv.config();

// Default configuration values
const DEFAULT_CONFIG = {
  START_INDEX: 1,
  PADDING: 4, // Number of digits for the index (e.g., 0001)
  EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
  BACKUP_PREFIX: 'backup_',
  SORT_BY: 'name', // Options: 'created' (file creation date), 'modified' (modification date), 'name' (filename)
  LOCAL_BACKUP_ENABLED: true,
  S3_UPLOAD_ENABLED: true,
  S3_BUCKET: 'paintball',
  S3_REGION: 'us-east-1',
  S3_ACCESS_KEY_ID: process.env.S3_ACCESS_KEY_ID || '********************',
  S3_SECRET_ACCESS_KEY: process.env.S3_SECRET_ACCESS_KEY || 'TqWuZ8ipsWVUsRgnSbe8/C9FS6cxXTt8lRJ95AQq',
  EXECUTE_RENAME: true // Always execute when called programmatically
};

// Initialize S3 client
const s3Client = new S3Client({
  region: DEFAULT_CONFIG.S3_REGION,
  credentials: {
    accessKeyId: DEFAULT_CONFIG.S3_ACCESS_KEY_ID,
    secretAccessKey: DEFAULT_CONFIG.S3_SECRET_ACCESS_KEY
  }
});

/**
 * Rename and upload images for a specific folder
 * @param {string} folderId - The folder ID to process
 * @param {Object} options - Optional configuration overrides
 * @returns {Promise<Object>} - Result object with success status and counts
 */
export async function renameAndUploadImages(folderId, options = {}) {
  // Combine default config with any provided options
  const config = { ...DEFAULT_CONFIG, ...options };

  // Set up folder-specific paths
  const PRIMARY_SOURCE_DIR = `G:\\My Drive\\Images\\Discs\\${folderId}`;
  const BACKUP_SOURCE_DIR = `\\\\NANCY\\nancyv8\\Images\\DG Discs\\Uploaded to AWS\\${folderId}`;
  const PREFIX = folderId;
  const S3_PREFIX = `shopify/discs/${folderId}/`;

  // Track results for reporting back to the task queue
  const result = {
    success: false,
    folderId: folderId,
    primaryDirExists: false,
    backupDirExists: false,
    primaryRenamedCount: 0,
    backupRenamedCount: 0,
    localBackupCount: 0,
    s3UploadCount: 0,
    firstRenamedFile: null,
    lastRenamedFile: null,
    highestExistingIndex: 0,
    startIndexUsed: 0,
    error: null
  };

  try {
    // We'll now focus on moving files from primary to backup, then processing only the backup
    const allRenamedFiles = [];
    let primaryDirProcessed = false;
    let backupDirProcessed = false;
    let filesMoved = 0;
    let filesConflicted = 0;

    // First, check if either directory exists
    let primaryDirExists = false;
    let backupDirExists = false;

    try {
      await fs.access(PRIMARY_SOURCE_DIR);
      primaryDirExists = true;
      result.primaryDirExists = true;
      console.log(`Primary directory exists: ${PRIMARY_SOURCE_DIR}`);
    } catch (error) {
      console.log(`Primary directory does not exist or can't be accessed: ${PRIMARY_SOURCE_DIR}`);
    }

    // Check if backup directory exists, create if not
    try {
      await fs.access(BACKUP_SOURCE_DIR);
      backupDirExists = true;
      result.backupDirExists = true;
      console.log(`Backup directory exists: ${BACKUP_SOURCE_DIR}`);
    } catch (error) {
      console.log(`Backup directory does not exist, creating it: ${BACKUP_SOURCE_DIR}`);
      try {
        await fs.mkdir(BACKUP_SOURCE_DIR, { recursive: true });
        backupDirExists = true;
        result.backupDirExists = true;
        console.log(`Created backup directory: ${BACKUP_SOURCE_DIR}`);
      } catch (mkdirError) {
        console.error(`Error creating backup directory: ${mkdirError.message}`);
      }
    }

    // If backup directory couldn't be created, return early
    if (!backupDirExists) {
      const errMsg = `Cannot proceed: Backup directory (${BACKUP_SOURCE_DIR}) does not exist and could not be created.`;
      console.error(errMsg);
      result.success = false;
      result.error = errMsg;
      return result;
    }

    // If primary directory exists, move files to backup
    if (primaryDirExists) {
      console.log(`\nMoving files from PRIMARY to BACKUP directory...`);

      try {
        // Get all files in the primary directory
        const primaryFiles = await fs.readdir(PRIMARY_SOURCE_DIR);
        const imageFiles = primaryFiles.filter(file => {
          // Skip directories and non-image files
          if (existsSync(path.join(PRIMARY_SOURCE_DIR, file)) &&
              (file.startsWith(config.BACKUP_PREFIX) || !path.extname(file))) {
            return false;
          }
          const ext = path.extname(file).toLowerCase();
          return config.EXTENSIONS.includes(ext);
        });

        console.log(`Found ${imageFiles.length} image files in primary directory`);

        // Get existing files in backup directory to check for conflicts
        const backupFiles = await fs.readdir(BACKUP_SOURCE_DIR);
        const backupFileSet = new Set(backupFiles);

        // Move each file from primary to backup
        for (const file of imageFiles) {
          const sourcePath = path.join(PRIMARY_SOURCE_DIR, file);
          let destFileName = file;
          let destPath = path.join(BACKUP_SOURCE_DIR, destFileName);

          // Check for conflicts
          if (backupFileSet.has(destFileName)) {
            // Conflict: prefix with g_ to indicate it came from Google Drive
            destFileName = `g_${file}`;
            destPath = path.join(BACKUP_SOURCE_DIR, destFileName);
            console.log(`Conflict detected for ${file}, renaming to ${destFileName}`);
            filesConflicted++;
          }

          try {
            // Move the file (copy then delete)
            await fs.copyFile(sourcePath, destPath);
            await fs.unlink(sourcePath);
            console.log(`Moved: ${file} -> ${destPath}`);
            filesMoved++;
          } catch (moveError) {
            console.error(`Error moving file ${file}: ${moveError.message}`);
          }
        }

        console.log(`\nFile move completed: ${filesMoved} moved, ${filesConflicted} renamed due to conflicts`);
        result.filesMoved = filesMoved;
        result.filesConflicted = filesConflicted;

      } catch (error) {
        console.error(`Error processing primary directory: ${error.message}`);
      }
    } else {
      console.log(`\nSkipping primary directory (not accessible), proceeding with backup directory only`);
    }

    // Now we only need to determine the indices in the backup directory

    // Function to find the highest index and gaps in a directory
    async function findHighestIndex(dirPath) {
      try {
        const files = await fs.readdir(dirPath);
        let maxIndex = 0;
        const existingIndices = new Set();

        for (const file of files) {
          // Skip directories and non-image files
          if (existsSync(path.join(dirPath, file)) &&
              (!path.extname(file) || file.startsWith(config.BACKUP_PREFIX))) {
            continue;
          }

          // Check if the file matches our naming pattern
          const regex = new RegExp(`^${PREFIX}-(\\d+)\\.[a-zA-Z]+$`);
          const match = file.match(regex);

          if (match && match[1]) {
            const index = parseInt(match[1], 10);
            if (!isNaN(index)) {
              existingIndices.add(index);
              if (index > maxIndex) {
                maxIndex = index;
              }
            }
          }
        }

        // Find the first available index (either the first gap or maxIndex + 1)
        let firstAvailableIndex = 1; // Start from 1
        while (existingIndices.has(firstAvailableIndex)) {
          firstAvailableIndex++;
        }

        console.log(`Existing indices: ${Array.from(existingIndices).sort((a, b) => a - b).join(', ')}`);
        console.log(`First available index: ${firstAvailableIndex}`);
        console.log(`Highest existing index: ${maxIndex}`);

        return {
          maxIndex,
          firstAvailableIndex,
          existingIndices: Array.from(existingIndices).sort((a, b) => a - b)
        };
      } catch (error) {
        console.error(`Error finding highest index in ${dirPath}:`, error.message);
        return {
          maxIndex: 0,
          firstAvailableIndex: 1,
          existingIndices: []
        };
      }
    }

    // Check backup directory for existing indices
    let indexInfo = {
      maxIndex: 0,
      firstAvailableIndex: 1,
      existingIndices: []
    };

    if (backupDirExists) {
      indexInfo = await findHighestIndex(BACKUP_SOURCE_DIR);
    }

    // Set the starting index for new files - use the first available index
    const startIndex = indexInfo.firstAvailableIndex;
    console.log(`Using start index: ${startIndex} for new files`);

    // Store the indices in the result
    result.highestExistingIndex = indexInfo.maxIndex;
    result.firstAvailableIndex = indexInfo.firstAvailableIndex;
    result.existingIndices = indexInfo.existingIndices;
    result.startIndexUsed = startIndex;

    // Update config with the new start index
    const updatedConfig = { ...config, START_INDEX: startIndex };

    // Process only the backup directory
    if (backupDirExists) {
      console.log(`\nProcessing BACKUP directory: ${BACKUP_SOURCE_DIR}`);
      const backupResult = await processDirectory(BACKUP_SOURCE_DIR, PREFIX, updatedConfig);

      if (backupResult.renamedFiles.length > 0) {
        allRenamedFiles.push(...backupResult.renamedFiles);

        // Update first and last renamed files
        result.firstRenamedFile = backupResult.firstRenamedFile;
        result.lastRenamedFile = backupResult.lastRenamedFile;
        result.backupRenamedCount = backupResult.renamedCount;
      }

      backupDirProcessed = true;
    }

    // If no directories were processed, return early
    if (!primaryDirProcessed && !backupDirProcessed) {
      console.log('\nNo directories were processed.');
      result.success = true;
      result.message = "No directories were processed.";
      return result;
    }

    // If no files were renamed in either directory, return early
    if (allRenamedFiles.length === 0) {
      console.log('\nNo files were renamed in any directory.');
      result.success = true;
      result.message = "No files were renamed.";
      return result;
    }

    // Helper function to process a directory
    async function processDirectory(sourceDir, prefix, config) {
      console.log(`Processing directory: ${sourceDir}`);

      const result = {
        renamedCount: 0,
        renamedFiles: [],
        firstRenamedFile: null,
        lastRenamedFile: null
      };

      try {
        // Get all files in the directory
        const files = await fs.readdir(sourceDir);
        console.log(`Found ${files.length} files in directory ${sourceDir}`);

        // Check for existing backup directories
        const backupDirs = files.filter(file =>
          file.startsWith(config.BACKUP_PREFIX) && existsSync(path.join(sourceDir, file))
        );
        console.log(`Found ${backupDirs.length} existing backup directories in ${sourceDir}`);

        // Filter for image files only (excluding backup directories)
        let imageFiles = files.filter(file => {
          // Skip directories, especially backup directories
          if (existsSync(path.join(sourceDir, file)) &&
              (file.startsWith(config.BACKUP_PREFIX) || !path.extname(file))) {
            return false;
          }
          const ext = path.extname(file).toLowerCase();
          return config.EXTENSIONS.includes(ext);
        });
        console.log(`Found ${imageFiles.length} image files in ${sourceDir}`);

        // Get file stats for sorting
        const fileDetails = [];
        for (const file of imageFiles) {
          const filePath = path.join(sourceDir, file);
          const stats = await fs.stat(filePath);

          fileDetails.push({
            name: file,
            path: filePath,
            created: stats.birthtime,
            modified: stats.mtime
          });
        }

        // Sort files based on the selected method
        if (config.SORT_BY === 'created') {
          console.log('Sorting by file creation date...');
          fileDetails.sort((a, b) => a.created - b.created);
        } else if (config.SORT_BY === 'modified') {
          console.log('Sorting by file modification date...');
          fileDetails.sort((a, b) => a.modified - b.modified);
        } else {
          console.log('Sorting alphabetically by filename...');
          fileDetails.sort((a, b) => a.name.localeCompare(b.name));
        }

        // Update imageFiles array with the sorted order
        imageFiles = fileDetails.map(detail => detail.name);

        // First, check which files are already correctly named with the pattern PREFIX-NNNN.ext
        const correctlyNamedFiles = [];
        const filesToRename = [];
        const regex = new RegExp(`^${prefix}-(\\d+)\\.[a-zA-Z]+$`);

        for (const file of imageFiles) {
          const match = file.match(regex);
          if (match) {
            // This file already follows the naming pattern
            correctlyNamedFiles.push({
              name: file,
              index: parseInt(match[1], 10),
              path: path.join(sourceDir, file)
            });
          } else {
            // This file needs to be renamed
            filesToRename.push({
              name: file,
              path: path.join(sourceDir, file)
            });
          }
        }

        console.log(`Found ${correctlyNamedFiles.length} already correctly named files`);
        console.log(`Found ${filesToRename.length} files that need to be renamed`);

        // Sort correctly named files by their index
        correctlyNamedFiles.sort((a, b) => a.index - b.index);

        // Preview the renaming operations
        console.log('\nRenaming Preview for directory:', sourceDir);
        console.log('------------------');

        const renameOperations = [];
        let changesNeeded = false;

        // Only process files that need to be renamed
        if (filesToRename.length > 0) {
          // Sort files to be renamed based on the selected method
          const fileDetails = [];
          for (const file of filesToRename) {
            const stats = await fs.stat(file.path);
            fileDetails.push({
              name: file.name,
              path: file.path,
              created: stats.birthtime,
              modified: stats.mtime
            });
          }

          if (config.SORT_BY === 'created') {
            console.log('Sorting files to rename by file creation date...');
            fileDetails.sort((a, b) => a.created - b.created);
          } else if (config.SORT_BY === 'modified') {
            console.log('Sorting files to rename by file modification date...');
            fileDetails.sort((a, b) => a.modified - b.modified);
          } else {
            console.log('Sorting files to rename alphabetically by filename...');
            fileDetails.sort((a, b) => a.name.localeCompare(b.name));
          }

          // Start renaming from the next available index
          let nextIndex = config.START_INDEX;

          // Process each file that needs to be renamed
          for (let i = 0; i < fileDetails.length; i++) {
            const oldFile = fileDetails[i].name;
            const oldPath = fileDetails[i].path;

            // Get file extension
            const ext = path.extname(oldFile).toLowerCase();

            // Create new filename with padded index
            const paddedIndex = nextIndex.toString().padStart(config.PADDING, '0');
            const newFile = `${prefix}-${paddedIndex}${ext}`;
            const newPath = path.join(sourceDir, newFile);

            console.log(`${oldFile} -> ${newFile}`);
            renameOperations.push({ oldPath, newPath });
            changesNeeded = true;

            nextIndex++;
          }
        }

        if (!changesNeeded) {
          console.log(`\nAll files in ${sourceDir} are already correctly named. No changes needed.`);
          return result;
        }

        // When called from the task queue, we always execute the rename
        const executeRename = config.EXECUTE_RENAME;

        if (executeRename) {
          console.log(`\nExecuting rename operations in ${sourceDir}...`);

          // Create a backup directory for the original files
          let backupDir;
          if (backupDirs.length === 0) {
            // Create a backup directory only if one doesn't already exist
            backupDir = path.join(sourceDir, config.BACKUP_PREFIX + Date.now());
            await fs.mkdir(backupDir);
            console.log(`Created backup directory: ${backupDir}`);

            // Copy files to backup directory first
            for (const { oldPath } of renameOperations) {
              const fileName = path.basename(oldPath);
              const backupPath = path.join(backupDir, fileName);
              await fs.copyFile(oldPath, backupPath);
            }
            console.log('Backup completed');
          } else {
            console.log(`Using existing backup directory: ${backupDirs[0]}`);
          }

          // Perform the renaming
          let successCount = 0;
          let errorCount = 0;

          // First, check for potential conflicts (files that would be overwritten)
          const tempSuffix = '.temp_' + Date.now();
          const conflictResolutionNeeded = new Set();

          // First pass: rename files that would be overwritten to temporary names
          for (const { oldPath, newPath } of renameOperations) {
            if (existsSync(newPath) && oldPath !== newPath) {
              const tempPath = newPath + tempSuffix;
              try {
                await fs.rename(newPath, tempPath);
                conflictResolutionNeeded.add(newPath);
                console.log(`Temporarily renamed ${newPath} to ${tempPath} to avoid conflict`);
              } catch (error) {
                console.error(`Error handling conflict for ${newPath}: ${error.message}`);
                errorCount++;
              }
            }
          }

          // Second pass: perform the actual renaming
          for (const { oldPath, newPath } of renameOperations) {
            try {
              // Skip if source and destination are the same
              if (oldPath === newPath) {
                console.log(`Skipping ${oldPath} (already correctly named)`);
                continue;
              }

              await fs.rename(oldPath, newPath);
              successCount++;

              // Track the first and last renamed files
              const newFileName = path.basename(newPath);
              if (result.firstRenamedFile === null) {
                result.firstRenamedFile = newFileName;
              }
              result.lastRenamedFile = newFileName;

              // Add to the list of renamed files
              result.renamedFiles.push({ name: newFileName, path: newPath });
            } catch (error) {
              console.error(`Error renaming ${oldPath}: ${error.message}`);
              errorCount++;
            }
          }

          // Third pass: handle any temporary files that need to be restored
          for (const conflictPath of conflictResolutionNeeded) {
            const tempPath = conflictPath + tempSuffix;
            if (existsSync(tempPath)) {
              try {
                // Find a new name for this file
                let counter = 1;
                let newPath;
                do {
                  newPath = `${conflictPath}.conflict_${counter++}`;
                } while (existsSync(newPath));

                await fs.rename(tempPath, newPath);
                console.log(`Renamed conflict file to ${newPath}`);
              } catch (error) {
                console.error(`Error resolving conflict for ${tempPath}: ${error.message}`);
                errorCount++;
              }
            }
          }

          console.log(`\nRenaming completed in ${sourceDir}: ${successCount} successful, ${errorCount} failed`);
          result.renamedCount = successCount;

          // Log the first and last renamed files
          if (successCount > 0 && result.firstRenamedFile && result.lastRenamedFile) {
            console.log(`First renamed file in ${sourceDir}: ${result.firstRenamedFile}`);
            console.log(`Last renamed file in ${sourceDir}: ${result.lastRenamedFile}`);
          }
        } else {
          console.log(`\nRename operation was not executed in ${sourceDir}. Set EXECUTE_RENAME = true to perform the actual renaming.`);
        }
      } catch (error) {
        console.error(`Error processing directory ${sourceDir}:`, error.message);
      }

      return result;
    }

    // Now process the upload step for all renamed files
    console.log(`\nProcessing upload step for ${allRenamedFiles.length} renamed files...`);

    // Upload to S3 if enabled
    if (config.S3_UPLOAD_ENABLED && allRenamedFiles.length > 0) {
      console.log(`\nUploading ${allRenamedFiles.length} files to S3...`);
      const s3Result = await uploadToS3(allRenamedFiles.map(f => ({ name: f.name, path: f.path })), S3_PREFIX, config.S3_BUCKET);
      result.s3UploadCount = s3Result.successCount;
    }

    // Set success flag if we got this far
    result.success = true;

    // Set total renamed count (now just the backup directory count)
    result.renamedCount = result.backupRenamedCount;

  } catch (error) {
    console.error('Error:', error.message);
    result.error = error.message;
  }

  return result;
}



/**
 * Upload renamed files to S3 bucket
 * @param {Array} files - Array of file objects with name and path properties
 * @param {string} s3Prefix - S3 key prefix
 * @param {string} bucket - S3 bucket name
 * @returns {Promise<Object>} - Result with success counts
 */
async function uploadToS3(files, s3Prefix, bucket) {
  console.log(`\nUploading ${files.length} files to S3 bucket: ${bucket}/${s3Prefix}`);

  const result = {
    successCount: 0,
    errorCount: 0
  };

  try {
    // Upload each file
    for (const file of files) {
      try {
        const fileContent = await fs.readFile(file.path);
        const s3Key = s3Prefix + file.name;

        const params = {
          Bucket: bucket,
          Key: s3Key,
          Body: fileContent,
          ContentType: getContentType(file.name)
        };

        const command = new PutObjectCommand(params);
        await s3Client.send(command);

        console.log(`Uploaded to S3: ${s3Key}`);
        result.successCount++;
      } catch (error) {
        console.error(`Error uploading ${file.name} to S3: ${error.message}`);
        result.errorCount++;
      }
    }

    console.log(`\nS3 upload completed: ${result.successCount} successful, ${result.errorCount} failed`);
  } catch (error) {
    console.error(`Error during S3 upload: ${error.message}`);
  }

  return result;
}

/**
 * Get content type based on file extension
 * @param {string} filename - Name of the file
 * @returns {string} - Content type
 */
function getContentType(filename) {
  const ext = path.extname(filename).toLowerCase();

  switch (ext) {
    case '.jpg':
    case '.jpeg':
      return 'image/jpeg';
    case '.png':
      return 'image/png';
    case '.gif':
      return 'image/gif';
    case '.webp':
      return 'image/webp';
    default:
      return 'application/octet-stream';
  }
}

// If this script is run directly (not imported), execute with command line arguments
if (import.meta.url === `file://${process.argv[1]}`) {
  // Check for folder ID argument
  const args = process.argv.slice(2);
  const folderIdArg = args.find(arg => arg.startsWith('--folder='));

  if (folderIdArg) {
    const folderId = folderIdArg.split('=')[1];
    console.log(`Running script for folder ID: ${folderId}`);

    // Set execute to false by default when run as a script (for safety)
    renameAndUploadImages(folderId, { EXECUTE_RENAME: false })
      .then(result => {
        console.log('Script completed with result:', result);
        process.exit(result.success ? 0 : 1);
      })
      .catch(err => {
        console.error('Error:', err);
        process.exit(1);
      });
  } else {
    console.error('Error: No folder ID specified. Use --folder=XXXXX argument.');
    process.exit(1);
  }
}
