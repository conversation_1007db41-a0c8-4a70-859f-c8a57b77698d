-- Dedupe and enforce uniqueness for check_if_osl_is_ready tasks per OSL
-- 1) Optional: preflight - see duplicates currently pending/processing
--    Run this select to inspect before running the cleanup
--
-- SELECT payload->>'id' AS osl_id,
--        COUNT(*) AS cnt,
--        ARRAY_AGG(id ORDER BY scheduled_at, id) AS task_ids
-- FROM t_task_queue
-- WHERE task_type = 'check_if_osl_is_ready'
--   AND status IN ('pending','processing')
-- GROUP BY 1
-- HAVING COUNT(*) > 1
-- ORDER BY cnt DESC;

-- 2) Cleanup: mark duplicates as cancelled, keeping the earliest scheduled_at per OSL
--    (Tie-break on id). This avoids hard deletes and allows the unique index to be created.
WITH ranked AS (
  SELECT id,
         ROW_NUMBER() OVER (
           PARTITION BY payload->>'id'
           ORDER BY scheduled_at ASC, id ASC
         ) AS rn
  FROM t_task_queue
  WHERE task_type = 'check_if_osl_is_ready'
    AND status IN ('pending','processing')
)
UPDATE t_task_queue t
SET status = 'cancelled',
    processed_at = NOW(),
    locked_at = NULL,
    locked_by = NULL,
    result = COALESCE(t.result, '{}'::jsonb) || jsonb_build_object(
      'message', 'Cancelled as duplicate by unique-enforcement migration',
      'cancelled_at', NOW(),
      'reason', 'duplicate pending/processing check_if_osl_is_ready for same OSL'
    )
FROM ranked r
WHERE t.id = r.id
  AND r.rn > 1;

-- 3) Create partial unique index preventing future duplicates while active
--    Note: Use CONCURRENTLY when running outside a transaction.
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS uq_tq_check_osl_ready_active
ON t_task_queue (task_type, (payload->>'id'))
WHERE task_type = 'check_if_osl_is_ready'
  AND status IN ('pending','processing');

-- 4) (Optional) Validate behavior with a quick test (should raise unique_violation)
-- BEGIN; -- only if not using CONCURRENTLY above
-- INSERT INTO t_task_queue (task_type, payload, status, scheduled_at, created_at)
-- VALUES ('check_if_osl_is_ready', '{"id": 12345}', 'pending', NOW(), NOW());
-- INSERT INTO t_task_queue (task_type, payload, status, scheduled_at, created_at)
-- VALUES ('check_if_osl_is_ready', '{"id": 12345}', 'pending', NOW(), NOW()); -- should fail
-- ROLLBACK;

