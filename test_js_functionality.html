<!DOCTYPE html>
<html>
<head>
    <title>Test JS Functionality</title>
</head>
<body>
    <h1>JavaScript Functionality Test</h1>
    
    <div>
        <button id="testBtn">Test Button</button>
        <div id="testOutput">Click the button to test</div>
    </div>

    <script>
        console.log('Script loading...');
        
        // Test basic functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            
            const btn = document.getElementById('testBtn');
            const output = document.getElementById('testOutput');
            
            if (btn && output) {
                btn.addEventListener('click', function() {
                    output.textContent = 'Button clicked! JavaScript is working.';
                    console.log('Button clicked');
                });
                console.log('Event listener added');
            } else {
                console.log('Elements not found');
            }
        });
        
        console.log('Script loaded');
    </script>
</body>
</html>
