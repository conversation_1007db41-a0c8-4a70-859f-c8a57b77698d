-- Function to enqueue a task when t_mps.ready changes from false to true
CREATE OR REPLACE FUNCTION fn_enqueue_mps_is_ready_so_publish_task()
RETURNS TRIGGER AS $$
DECLARE
    scheduled_time timestamp;
BEGIN
    -- Only enqueue a task if ready has changed from false to true
    IF OLD.ready = FALSE AND NEW.ready = TRUE THEN
        -- Determine the scheduled time based on embargo_until
        IF NEW.embargo_until IS NOT NULL THEN
            scheduled_time := NEW.embargo_until;

            -- If embargo_until is in the past, use NOW() instead
            IF scheduled_time < NOW() THEN
                scheduled_time := NOW();
            END IF;
        ELSE
            scheduled_time := NOW();
        END IF;

        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'mps_is_ready_so_publish',
            jsonb_build_object('id', NEW.id),
            'pending',
            scheduled_time,
            NOW()
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE OR REPLACE TRIGGER trg_mps_is_ready_so_publish
AFTER UPDATE OF ready ON t_mps
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_mps_is_ready_so_publish_task();
