# Informed Repricer Integration

This module provides automated integration with Informed Repricer, downloading and importing reports at scheduled times.

## Overview

The Informed Repricer integration consists of the following components:

1. **Report Downloader**: Downloads reports from Informed Repricer API
2. **Report Importer**: Imports the downloaded reports into Supabase tables
3. **Scheduler**: Schedules the download and import process at specified times
4. **Task Queue Integration**: Integrates with the task queue system for reliable processing
5. **Admin Interface**: Provides a user interface for monitoring and managing the process

## Reports

The integration handles three types of reports:

1. **All Fields** - Imported to `it_infor_all_fields`
2. **Competition Landscape** - Imported to `it_infor_competition_landscape`
3. **No Buy Box** - Imported to `it_infor_no_buy_box`

## Schedule

The reports are downloaded and imported automatically at the following times (CST):

- 6:30 AM
- 12:30 PM
- 6:30 PM

## Files

- `informedReportDownloader.js` - Downloads reports from Informed Repricer API
- `informedReportImporter.js` - Imports reports into Supabase tables
- `informedScheduler.js` - Schedules the download and import process
- `informedTaskHandler.js` - Handles task queue integration
- `informedApiHandler.js` - Provides API endpoints for the admin interface
- `fn_truncate_table.sql` - SQL function to truncate tables
- `fn_enqueue_informed_tasks.sql` - SQL function to enqueue Informed tasks
- `fn_create_informed_scheduler_trigger.sql` - SQL function to create a scheduler trigger

## Task Types

The integration adds the following task types to the task queue system:

- `download_informed_reports` - Downloads reports from Informed Repricer
- `import_informed_reports` - Imports downloaded reports into Supabase tables
- `run_informed_process` - Runs the full process (download and import)

## Installation

1. Install the required dependencies:

```bash
npm install
```

2. Create the SQL functions:

```bash
psql -U your_username -d your_database -f fn_truncate_table.sql
psql -U your_username -d your_database -f fn_enqueue_informed_tasks.sql
psql -U your_username -d your_database -f fn_create_informed_scheduler_trigger.sql
```

3. Start the admin server:

```bash
npm run start-admin
```

4. Open the admin interface in your browser:

```
http://localhost:3001/admin.html
```

5. Navigate to the "Informed" tab to manage the process.

## Manual Operation

You can manually trigger the process using the buttons in the admin interface:

- **Download Reports from Informed** - Downloads the reports from Informed Repricer
- **Import Reports to Supabase** - Imports the downloaded reports into Supabase tables
- **Run Full Process** - Runs both download and import steps

## Scheduler

The scheduler can be enabled or disabled using the buttons in the admin interface:

- **Enable Scheduler** - Enables automatic scheduling
- **Disable Scheduler** - Disables automatic scheduling

## Troubleshooting

If you encounter issues with the Informed Repricer integration:

1. Check the admin interface for error messages
2. Verify that the API key is valid
3. Check the report files in the `data/external data/informed` directory
4. Verify that the Supabase tables exist and have the correct schema
5. Check the task queue for any failed tasks

## API Endpoints

The integration adds the following API endpoints to the admin server:

- `GET /api/informed/status` - Gets the status of the reports
- `POST /api/informed/download-reports` - Downloads the reports
- `POST /api/informed/import-reports` - Imports the reports
- `POST /api/informed/run-full-process` - Runs the full process
- `GET /api/informed/scheduler-status` - Gets the scheduler status
- `POST /api/informed/enable-scheduler` - Enables the scheduler
- `POST /api/informed/disable-scheduler` - Disables the scheduler
