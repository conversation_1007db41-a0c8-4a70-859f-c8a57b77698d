// Task handler for publish_product_variant_only
// Adds a single product variant to an existing Shopify product

const productsEndpoint = 'https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/products.json';
const shopifyAccessToken = '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';

// Helper function to convert lbs to grams
function lbsToGrams(lbs) {
  if (lbs == null || isNaN(lbs)) return null;
  return Math.round(lbs * 453.592);
}

// Helper function to sanitize Shopify handle
function sanitizeHandle(handle) {
  if (!handle) return null;
  return handle
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
}



// Helper function to create Shopify variant
async function createShopifyVariant(productId, variantData) {
  const base = productsEndpoint.split('/products.json')[0];
  const url = `${base}/products/${productId}/variants.json`;
  const resp = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken
    },
    body: JSON.stringify({ variant: variantData })
  });
  if (!resp.ok) {
    const errorText = await resp.text();
    throw new Error(`Failed to create Shopify variant: ${resp.status} ${resp.statusText} - ${errorText}`);
  }
  const json = await resp.json();
  return json?.variant;
}

async function getShopifyProduct(productId, handle = null) {
  if (handle) {
    // Look up by handle using GraphQL
    const query = `
      query getProductByHandle($handle: String!) {
        productByHandle(handle: $handle) {
          id
          handle
          title
          variants(first: 250) {
            edges {
              node {
                id
                sku
              }
            }
          }
          images(first: 250) {
            edges {
              node {
                id
                src
              }
            }
          }
        }
      }
    `;
    
    const graphqlResponse = await fetch('https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/graphql.json', {
      method: 'POST',
      headers: {
        'X-Shopify-Access-Token': shopifyAccessToken,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ query, variables: { handle } })
    });

    if (!graphqlResponse.ok) {
      throw new Error(`Failed to query Shopify GraphQL: ${graphqlResponse.status} ${graphqlResponse.statusText}`);
    }

    const graphqlData = await graphqlResponse.json();
    if (graphqlData.errors) {
      throw new Error(`GraphQL errors: ${JSON.stringify(graphqlData.errors)}`);
    }

    const product = graphqlData.data?.productByHandle;
    if (!product) {
      return null; // Product not found
    }

    // Convert GraphQL format to REST format for consistency
    return {
      id: product.id.replace('gid://shopify/Product/', ''),
      handle: product.handle,
      title: product.title,
      variants: product.variants.edges.map(edge => ({
        id: edge.node.id.replace('gid://shopify/ProductVariant/', ''),
        sku: edge.node.sku
      })),
      images: product.images.edges.map(edge => ({
        id: edge.node.id.replace('gid://shopify/ProductImage/', ''),
        src: edge.node.src
      }))
    };
  } else {
    // Look up by ID using REST
    const base = productsEndpoint.split('/products.json')[0];
    const url = `${base}/products/${productId}.json`;
    const resp = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      }
    });
    if (!resp.ok) {
      const t = await resp.text();
      throw new Error(`GET product ${productId} failed: ${resp.status} ${resp.statusText} - ${t}`);
    }
    const j = await resp.json();
    return j?.product;
  }
}

async function updateVariantImage(variantId, imageId) {
  const base = productsEndpoint.split('/products.json')[0];
  const url = `${base}/variants/${variantId}.json`;
  const resp = await fetch(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken
    },
    body: JSON.stringify({
      variant: {
        id: variantId,
        image_id: imageId
      }
    })
  });
  if (!resp.ok) {
    const errorText = await resp.text();
    throw new Error(`Failed to update variant image: ${resp.status} ${resp.statusText} - ${errorText}`);
  }
  return await resp.json();
}

export default async function processPublishProductVariantOnlyTask(task, { supabase, updateTaskStatus, logError }) {
  try {
    // Parse payload
    let payload;
    if (typeof task.payload === 'string') {
      payload = JSON.parse(task.payload);
    } else {
      payload = task.payload;
    }

    const variantId = payload.id || payload.variant_id;
    if (!variantId) {
      await updateTaskStatus(task.id, 'failed', { message: 'Missing variant id in payload (expected id or variant_id)' });
      return;
    }

    // Fetch the variant and related product
    const { data: variant, error: variantError } = await supabase
      .from('t_product_variants')
      .select(`
        id, op1_name, op1_value, op2_name, op2_value, op3_name, op3_value, 
        price, "UPC", msrp, shopify_weight_lbs, stock_quantity, order_cost, 
        color_id, todo, uploaded_to_shopify_at, product_id,
        t_products!inner (
          id, name, description, brand_id, category_id, 
          shopify_handle, shopify_product_template
        )
      `)
      .eq('id', variantId)
      .maybeSingle();

    if (variantError) {
      await updateTaskStatus(task.id, 'failed', { message: `Database error: ${variantError.message}` });
      return;
    }

    if (!variant) {
      await updateTaskStatus(task.id, 'failed', { message: `Variant ${variantId} not found` });
      return;
    }

    // Check if variant is ready to publish
    if (!variant.todo || variant.todo.toLowerCase() !== 'ready to publish') {
      await updateTaskStatus(task.id, 'completed', {
        message: 'Variant not ready to publish',
        variant_todo: variant.todo
      });
      return;
    }

    const product = variant.t_products;

    // Find the existing Shopify product using the product's shopify_handle
    if (!product.shopify_handle) {
      await updateTaskStatus(task.id, 'failed', {
        message: 'Product has no shopify_handle. Cannot find existing Shopify product.'
      });
      return;
    }

    let existingShopifyProduct = null;
    try {
      existingShopifyProduct = await getShopifyProduct(null, product.shopify_handle);
      if (!existingShopifyProduct) {
        await updateTaskStatus(task.id, 'failed', {
          message: `No Shopify product found with handle '${product.shopify_handle}'. Use publish_product_accessory to create the product first.`
        });
        return;
      }
    } catch (e) {
      await updateTaskStatus(task.id, 'failed', {
        message: `Failed to fetch Shopify product with handle '${product.shopify_handle}': ${e.message}`
      });
      return;
    }

    const shopifyProductId = existingShopifyProduct.id;

    // Check if this variant's SKU already exists in the Shopify product
    const variantSku = `DGACC${variant.id}`;
    const existingVariant = existingShopifyProduct.variants?.find(v => v.sku === variantSku);
    if (existingVariant) {
      await updateTaskStatus(task.id, 'completed', {
        message: 'Variant already exists in Shopify product',
        variant_id: variantId,
        shopify_product_id: shopifyProductId,
        shopify_variant_id: existingVariant.id,
        existing_sku: variantSku
      });
      return;
    }

    // Build variant for Shopify
    const optionValues = [variant.op1_value, variant.op2_value, variant.op3_value].filter(v => v);
    const shopifyVariant = {
      sku: variantSku,
      price: variant.price != null ? String(variant.price) : undefined,
      compare_at_price: variant.msrp != null ? String(variant.msrp) : undefined,
      barcode: variant.UPC || undefined,
      weight: lbsToGrams(variant.shopify_weight_lbs),
      weight_unit: 'g',
      inventory_management: 'shopify',
      inventory_policy: 'deny',
      fulfillment_service: 'manual',
      requires_shipping: true,
      inventory_quantity: (variant.stock_quantity ?? 0),
      option1: optionValues[0] || null,
      option2: optionValues[1] || null,
      option3: optionValues[2] || null
    };

    // Create the variant on the existing Shopify product
    const createdVariant = await createShopifyVariant(shopifyProductId, shopifyVariant);
    const createdVariantId = createdVariant?.id;

    // Set inventory quantity explicitly using inventory levels API
    if (createdVariantId && variant.stock_quantity != null && variant.stock_quantity > 0) {
      try {
        // First get the inventory item ID from the variant
        const base = productsEndpoint.split('/products.json')[0];
        const variantUrl = `${base}/variants/${createdVariantId}.json`;
        const variantResp = await fetch(variantUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': shopifyAccessToken
          }
        });

        if (variantResp.ok) {
          const variantData = await variantResp.json();
          const inventoryItemId = variantData.variant?.inventory_item_id;

          if (inventoryItemId) {
            // Get the primary location ID
            const locationsUrl = `${base}/locations.json`;
            const locationsResp = await fetch(locationsUrl, {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json',
                'X-Shopify-Access-Token': shopifyAccessToken
              }
            });

            if (locationsResp.ok) {
              const locationsData = await locationsResp.json();
              const primaryLocation = locationsData.locations?.find(loc => loc.primary) || locationsData.locations?.[0];

              if (primaryLocation) {
                // Set inventory level
                const inventoryUrl = `${base}/inventory_levels/set.json`;
                const inventoryResp = await fetch(inventoryUrl, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'X-Shopify-Access-Token': shopifyAccessToken
                  },
                  body: JSON.stringify({
                    location_id: primaryLocation.id,
                    inventory_item_id: inventoryItemId,
                    available: variant.stock_quantity
                  })
                });

                if (!inventoryResp.ok) {
                  const errorText = await inventoryResp.text();
                  console.warn(`[processPublishProductVariantOnlyTask] Failed to set inventory: ${inventoryResp.status} ${inventoryResp.statusText} - ${errorText}`);
                }
              }
            }
          }
        }
      } catch (e) {
        console.warn(`[processPublishProductVariantOnlyTask] Failed to set inventory quantity: ${e.message}`);
      }
    }

    // Associate variant image if available
    if (createdVariantId) {
      // Fetch image config
      let publicImageServer, folderProductVariants;
      try {
        const { data: cfg, error: cfgErr } = await supabase
          .from('t_config')
          .select('key, value')
          .in('key', ['public_image_server', 'folder_product_variants']);
        if (!cfgErr && Array.isArray(cfg)) {
          publicImageServer = cfg.find(r => r.key === 'public_image_server')?.value;
          folderProductVariants = cfg.find(r => r.key === 'folder_product_variants')?.value;
        }
      } catch {}

      if (publicImageServer && folderProductVariants) {
        try {
          // First, upload the image to the product if it doesn't exist
          const imageUrl = `${publicImageServer}/${folderProductVariants}/${variant.id}.jpg`;

          // Check if image already exists in product
          const updatedProduct = await getShopifyProduct(shopifyProductId);
          const existingImage = updatedProduct?.images?.find(img => img.src === imageUrl);

          let imageId = existingImage?.id;

          if (!imageId) {
            // Upload new image to product
            const base = productsEndpoint.split('/products.json')[0];
            const imageUploadUrl = `${base}/products/${shopifyProductId}/images.json`;
            const imageResp = await fetch(imageUploadUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-Shopify-Access-Token': shopifyAccessToken
              },
              body: JSON.stringify({
                image: { src: imageUrl }
              })
            });

            if (imageResp.ok) {
              const imageData = await imageResp.json();
              imageId = imageData.image?.id;
            }
          }

          // Associate image with variant
          if (imageId) {
            await updateVariantImage(createdVariantId, imageId);
          }
        } catch (e) {
          console.warn(`[processPublishProductVariantOnlyTask] Failed to associate variant image: ${e.message}`);
        }
      }
    }

    // Stamp uploaded flags
    const nowIso = new Date().toISOString();
    try {
      const { error: varUpdErr } = await supabase
        .from('t_product_variants')
        .update({ uploaded_to_shopify_at: nowIso })
        .eq('id', variantId);
      if (varUpdErr) console.warn(`[processPublishProductVariantOnlyTask] Failed to stamp variant ${variantId}: ${varUpdErr.message}`);
    } catch {}

    // Complete
    await updateTaskStatus(task.id, 'completed', {
      message: 'Product variant added to existing Shopify product',
      variant_id: variantId,
      shopify_product_id: shopifyProductId,
      shopify_variant_id: createdVariantId,
      variant_sku: variantSku
    });

  } catch (error) {
    console.error('[processPublishProductVariantOnlyTask] Error:', error);
    await updateTaskStatus(task.id, 'failed', {
      message: 'Failed to publish product variant',
      error: error.message
    });
  }
}
