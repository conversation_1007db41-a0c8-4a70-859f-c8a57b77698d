import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

async function main() {
  // Initialize Supabase client.
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_KEY;
  const supabase = createClient(supabaseUrl, supabaseKey);

  // Get total count of matching records.
  const { count: totalCount, error: countError } = await supabase
    .from('imported_table_veeqo_sellables_export')
    .select('*', { count: 'exact', head: true })
    .ilike('variant_title', '% (D#%');

  if (countError) {
    console.error('Error counting matching records:', countError);
    process.exit(1);
  }
  
  console.log(`Total matching Veeqo records: ${totalCount}`);

  // Query 500 matching records.
  const { data: records, error } = await supabase
    .from('imported_table_veeqo_sellables_export')
    .select('*')
    .ilike('variant_title', '% (D#%')
    .limit(500);

  if (error) {
    console.error('Error querying imported_table_veeqo_sellables_export:', error);
    process.exit(1);
  }

  if (!records || records.length === 0) {
    console.log('No matching records found.');
    process.exit(0);
  }

  console.log(`Found ${records.length} record(s) to process out of ${totalCount} total.`);

  const veeqoEndpoint = process.env.VEEQO_ENDPOINT || 'https://api.veeqo.com/sellables';
  const veeqoApiKey = 'Vqt/16c3acd642be598d3ca079590a8aae87';

  let processed = 0;
  for (const record of records) {
    processed++;
    console.log(`\nProcessing record ${processed} of ${totalCount}.`);
    
    const productId = record.product_id;
    const variantTitle = record.variant_title;
    const skuCode = record.sku_code;
    console.log(`Product ID: ${productId}`);
    console.log(`Variant Title: ${variantTitle}`);
    console.log(`Veeqo sku_code: ${skuCode}`);

    // Build DELETE URL.
    const deleteUrl = `${veeqoEndpoint}/${productId}`;
    console.log(`Calling DELETE on: ${deleteUrl}`);

    try {
      const response = await fetch(deleteUrl, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': veeqoApiKey
        }
      });

      if (!response.ok) {
        const responseText = await response.text();
        console.error(`Error deleting product ${productId} from Veeqo: ${response.status} - ${responseText}`);
        continue;
      } else {
        console.log(`Successfully deleted product ${productId} from Veeqo.`);
      }
    } catch (err) {
      console.error('Error calling Veeqo API:', err);
      continue;
    }

    // Update t_discs: for any record with veeqo_id equal to productId,
    // set veeqo_id to null and update veeqo_id_notes.
    const nowTimestamp = new Date().toISOString();
    const updateNote = nowTimestamp + " Bag and card variant veeqo_id deleted.";
    const { data: updatedDiscs, error: updateDiscError } = await supabase
      .from('t_discs')
      .update({ veeqo_id: null, veeqo_id_notes: updateNote })
      .eq('veeqo_id', productId)
      .select('id');

    if (updateDiscError) {
      console.error(`Error updating t_discs for veeqo_id ${productId}:`, updateDiscError);
    } else if (updatedDiscs && updatedDiscs.length > 0) {
      updatedDiscs.forEach(disc => {
        console.log(`Updated t_discs record id: ${disc.id}`);
      });
    } else {
      console.log(`No matching t_discs record found for veeqo_id ${productId}`);
    }

    // Delete the record from imported_table_veeqo_sellables_export.
    const { error: deleteError } = await supabase
      .from('imported_table_veeqo_sellables_export')
      .delete()
      .eq('product_id', productId);

    if (deleteError) {
      console.error(`Error deleting record from imported_table_veeqo_sellables_export for product_id ${productId}:`, deleteError);
    } else {
      console.log(`Deleted record from imported_table_veeqo_sellables_export for product_id ${productId}`);
    }
  }

  process.exit(0);
}

main();
