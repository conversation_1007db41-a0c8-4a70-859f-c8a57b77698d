import puppeteer from 'puppeteer';
import fs from 'fs';

async function scrapeAllProducts() {
    console.log('Starting product scraper for discgolfdistribution.com...');
    
    const browser = await puppeteer.launch({ 
        headless: false,
        defaultViewport: null,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    try {
        console.log('Browser launched, creating new page...');
        const page = await browser.newPage();
        
        // Set user agent
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        console.log('Navigating to login page...');
        await page.goto('https://discgolfdistribution.com/account/login', { waitUntil: 'networkidle2' });
        
        // Wait for login form and fill credentials
        console.log('Waiting for login form...');
        await page.waitForSelector('input[name="customer[email]"]', { timeout: 30000 });
        console.log('Found email field, typing email...');
        await page.type('input[name="customer[email]"]', '<EMAIL>');
        console.log('Typing password...');
        await page.type('input[name="customer[password]"]', 'Sdisplatgun9!');
        
        // Submit login form
        console.log('Submitting login form...');
        await page.click('input[type="submit"], button[type="submit"]');
        console.log('Waiting for navigation after login...');
        await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 30000 });
        
        console.log('Logged in successfully');
        
        let allProducts = [];
        let pageNum = 1;
        let hasMoreProducts = true;
        
        while (hasMoreProducts) {
            console.log(`Fetching page ${pageNum}...`);
            
            try {
                // Use the main products.json endpoint
                const jsonUrl = `https://discgolfdistribution.com/products.json?page=${pageNum}&limit=250`;
                const response = await page.goto(jsonUrl, { waitUntil: 'networkidle2' });
                
                if (response.status() === 200) {
                    const content = await page.content();
                    const jsonMatch = content.match(/<pre[^>]*>(.*?)<\/pre>/s);
                    
                    if (jsonMatch) {
                        const jsonData = JSON.parse(jsonMatch[1]);
                        
                        if (jsonData.products && jsonData.products.length > 0) {
                            console.log(`Found ${jsonData.products.length} products on page ${pageNum}`);
                            allProducts.push(...jsonData.products);
                            pageNum++;
                        } else {
                            hasMoreProducts = false;
                            console.log('No more products found');
                        }
                    } else {
                        throw new Error('Could not parse JSON response');
                    }
                } else {
                    throw new Error(`HTTP ${response.status()}`);
                }
            } catch (error) {
                console.log(`Error on page ${pageNum}: ${error.message}`);
                hasMoreProducts = false;
            }
            
            // Add delay to be respectful
            await page.waitForTimeout(1000);
        }
        
        console.log(`Total products found: ${allProducts.length}`);
        
        // Filter for Dynamic Discs products
        const dynamicDiscsProducts = allProducts.filter(product => {
            const vendor = product.vendor?.toLowerCase() || '';
            const title = product.title?.toLowerCase() || '';
            const tags = product.tags || [];
            
            return vendor.includes('dynamic') || 
                   title.includes('dynamic') ||
                   tags.some(tag => tag.toLowerCase().includes('dynamic'));
        });
        
        console.log(`Found ${dynamicDiscsProducts.length} Dynamic Discs products`);
        
        // Save all products
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const allProductsFilename = `all_products_${timestamp}.json`;
        fs.writeFileSync(allProductsFilename, JSON.stringify(allProducts, null, 2));
        console.log(`All products saved to ${allProductsFilename}`);
        
        // Save Dynamic Discs products
        const dynamicDiscsFilename = `dynamic_discs_products_${timestamp}.json`;
        fs.writeFileSync(dynamicDiscsFilename, JSON.stringify(dynamicDiscsProducts, null, 2));
        console.log(`Dynamic Discs products saved to ${dynamicDiscsFilename}`);
        
        // Save as CSV for easier viewing
        const csvFilename = `dynamic_discs_products_${timestamp}.csv`;
        const csvContent = convertToCSV(dynamicDiscsProducts);
        fs.writeFileSync(csvFilename, csvContent);
        console.log(`Dynamic Discs products also saved to ${csvFilename}`);
        
        // Print summary
        console.log('\n=== SUMMARY ===');
        console.log(`Total products scraped: ${allProducts.length}`);
        console.log(`Dynamic Discs products: ${dynamicDiscsProducts.length}`);
        
        if (dynamicDiscsProducts.length > 0) {
            console.log('\nSample Dynamic Discs products:');
            dynamicDiscsProducts.slice(0, 5).forEach((product, index) => {
                console.log(`${index + 1}. ${product.title} - ${product.vendor} - $${product.variants?.[0]?.price || 'N/A'}`);
            });
        }
        
        return { allProducts, dynamicDiscsProducts };
        
    } catch (error) {
        console.error('Error during scraping:', error);
        console.error('Error stack:', error.stack);
        
        // Take a screenshot for debugging
        try {
            const pages = await browser.pages();
            if (pages.length > 0) {
                await pages[0].screenshot({ path: 'error_screenshot.png', fullPage: true });
                console.log('Error screenshot saved as error_screenshot.png');
            }
        } catch (screenshotError) {
            console.error('Could not take screenshot:', screenshotError.message);
        }
        
        throw error;
    } finally {
        console.log('Closing browser...');
        await browser.close();
    }
}

function convertToCSV(products) {
    if (products.length === 0) return '';
    
    // Define the columns we want in the CSV
    const headers = [
        'id', 'title', 'vendor', 'product_type', 'created_at', 'updated_at',
        'published_at', 'available', 'tags', 'variant_id', 'variant_title',
        'variant_price', 'variant_sku', 'variant_inventory_quantity',
        'variant_available', 'variant_weight', 'variant_option1', 'variant_option2', 'variant_option3'
    ];
    
    const csvRows = [headers.join(',')];
    
    products.forEach(product => {
        // Handle products with multiple variants
        if (product.variants && product.variants.length > 0) {
            product.variants.forEach(variant => {
                const row = [
                    product.id || '',
                    `"${(product.title || '').replace(/"/g, '""')}"`,
                    `"${(product.vendor || '').replace(/"/g, '""')}"`,
                    `"${(product.product_type || '').replace(/"/g, '""')}"`,
                    product.created_at || '',
                    product.updated_at || '',
                    product.published_at || '',
                    product.available || false,
                    `"${(product.tags || []).join('; ').replace(/"/g, '""')}"`,
                    variant.id || '',
                    `"${(variant.title || '').replace(/"/g, '""')}"`,
                    variant.price || '',
                    `"${(variant.sku || '').replace(/"/g, '""')}"`,
                    variant.inventory_quantity || '',
                    variant.available || false,
                    variant.weight || '',
                    `"${(variant.option1 || '').replace(/"/g, '""')}"`,
                    `"${(variant.option2 || '').replace(/"/g, '""')}"`,
                    `"${(variant.option3 || '').replace(/"/g, '""')}"`
                ];
                csvRows.push(row.join(','));
            });
        } else {
            // Product without variants
            const row = [
                product.id || '',
                `"${(product.title || '').replace(/"/g, '""')}"`,
                `"${(product.vendor || '').replace(/"/g, '""')}"`,
                `"${(product.product_type || '').replace(/"/g, '""')}"`,
                product.created_at || '',
                product.updated_at || '',
                product.published_at || '',
                product.available || false,
                `"${(product.tags || []).join('; ').replace(/"/g, '""')}"`,
                '', '', '', '', '', '', '', '', '', ''
            ];
            csvRows.push(row.join(','));
        }
    });
    
    return csvRows.join('\n');
}

// Run the scraper
if (import.meta.url === `file://${process.argv[1]}`) {
    scrapeAllProducts()
        .then(result => {
            console.log('Scraping completed successfully');
            process.exit(0);
        })
        .catch(error => {
            console.error('Scraping failed:', error);
            process.exit(1);
        });
}

export { scrapeAllProducts };
