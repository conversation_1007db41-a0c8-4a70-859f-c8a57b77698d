// diagnosticMigration.js
// <PERSON>ript to diagnose why no records are being found for migration
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Create a Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('=== DIAGNOSTIC QUERIES ===');
    
    // Check 1: How many discs have null image_verified_notes?
    console.log('\nChecking how many discs have null image_verified_notes...');
    const { data: discsWithoutNotes, error: discsError } = await supabase
      .from('t_discs')
      .select('id')
      .is('image_verified_notes', null)
      .limit(10);
      
    if (discsError) {
      console.error(`Error fetching discs without notes: ${discsError.message}`);
    } else {
      console.log(`Found ${discsWithoutNotes.length} discs with null image_verified_notes.`);
      if (discsWithoutNotes.length > 0) {
        console.log('Sample disc IDs:', discsWithoutNotes.map(d => d.id).join(', '));
      }
    }
    
    // Check 2: How many t_images records are there with table_name = 't_discs'?
    console.log('\nChecking how many t_images records have table_name = t_discs...');
    const { data: imageRecords, error: imagesError } = await supabase
      .from('t_images')
      .select('record_id')
      .eq('table_name', 't_discs')
      .limit(10);
      
    if (imagesError) {
      console.error(`Error fetching t_images records: ${imagesError.message}`);
    } else {
      console.log(`Found ${imageRecords.length} t_images records with table_name = t_discs.`);
      if (imageRecords.length > 0) {
        console.log('Sample record_ids:', imageRecords.map(i => i.record_id).join(', '));
      }
    }
    
    // Check 3: Try a direct query to find discs that have both conditions
    if (discsWithoutNotes.length > 0 && imageRecords.length > 0) {
      console.log('\nChecking for discs that have both conditions...');
      const discIds = discsWithoutNotes.map(d => d.id);
      const recordIds = imageRecords.map(i => i.record_id);
      
      // Find the intersection
      const intersection = discIds.filter(id => recordIds.includes(id));
      console.log(`Found ${intersection.length} discs that satisfy both conditions.`);
      if (intersection.length > 0) {
        console.log('Sample intersection IDs:', intersection.join(', '));
      }
      
      // Try a direct query
      const { data: matchingDiscs, error: matchingError } = await supabase
        .from('t_discs')
        .select('id')
        .is('image_verified_notes', null)
        .in('id', recordIds)
        .limit(10);
        
      if (matchingError) {
        console.error(`Error fetching matching discs: ${matchingError.message}`);
      } else {
        console.log(`Direct query found ${matchingDiscs.length} matching discs.`);
        if (matchingDiscs.length > 0) {
          console.log('Sample matching disc IDs:', matchingDiscs.map(d => d.id).join(', '));
        }
      }
    }
    
    // Check 4: Check if there are any discs with image_verified_notes containing 'Migrated from t_images'
    console.log('\nChecking for discs with migration notes...');
    const { data: migratedDiscs, error: migratedError } = await supabase
      .from('t_discs')
      .select('id')
      .ilike('image_verified_notes', '%Migrated from t_images%')
      .limit(10);
      
    if (migratedError) {
      console.error(`Error fetching migrated discs: ${migratedError.message}`);
    } else {
      console.log(`Found ${migratedDiscs.length} discs with migration notes.`);
      if (migratedDiscs.length > 0) {
        console.log('Sample migrated disc IDs:', migratedDiscs.map(d => d.id).join(', '));
      }
    }
    
    // Check 5: Check the column names to ensure they match what we expect
    console.log('\nChecking column names in t_images...');
    const { data: imagesSample, error: imagesSampleError } = await supabase
      .from('t_images')
      .select('*')
      .limit(1);
      
    if (imagesSampleError) {
      console.error(`Error fetching t_images sample: ${imagesSampleError.message}`);
    } else if (imagesSample.length > 0) {
      const columns = Object.keys(imagesSample[0]);
      console.log('t_images columns:', columns);
      
      // Check for the specific columns we need
      const requiredColumns = ['image_verified', 'image_verified_at', 'image_verified_notes', 'updated_by'];
      const missingColumns = requiredColumns.filter(col => !columns.includes(col));
      
      if (missingColumns.length > 0) {
        console.error(`Missing columns in t_images: ${missingColumns.join(', ')}`);
      } else {
        console.log('All required t_images columns found.');
      }
    }
    
    console.log('\nChecking column names in t_discs...');
    const { data: discsSample, error: discsSampleError } = await supabase
      .from('t_discs')
      .select('*')
      .limit(1);
      
    if (discsSampleError) {
      console.error(`Error fetching t_discs sample: ${discsSampleError.message}`);
    } else if (discsSample.length > 0) {
      const columns = Object.keys(discsSample[0]);
      console.log('t_discs columns:', columns);
      
      // Check for the specific columns we need
      const requiredColumns = ['image_verified', 'image_verified_at', 'image_verified_by', 'image_verified_notes'];
      const missingColumns = requiredColumns.filter(col => !columns.includes(col));
      
      if (missingColumns.length > 0) {
        console.error(`Missing columns in t_discs: ${missingColumns.join(', ')}`);
      } else {
        console.log('All required t_discs columns found.');
      }
    }
    
    console.log('\nDiagnostic complete!');
    
  } catch (err) {
    console.error(`Unexpected error: ${err.message}`);
    console.error(err.stack);
  }
}

main();
