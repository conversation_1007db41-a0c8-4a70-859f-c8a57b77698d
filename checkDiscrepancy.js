import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

console.log('🔧 Connecting to Supabase...');
console.log('URL:', process.env.SUPABASE_URL);
console.log('Key length:', process.env.SUPABASE_KEY?.length || 0);

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkDiscrepancy() {
  try {
    console.log('🔍 Analyzing order quantity discrepancy...\n');
    
    // Get total with order > 0 (email calculation)
    const { data: allOrders, error: allError } = await supabase
      .from('v_stats_by_osl_discraft')
      .select('"order", excel_row_hint, excel_column, mold_name, plastic_name')
      .gt('"order"', 0);
    
    if (allError) {
      console.error('❌ Error:', allError);
      return;
    }
    
    const totalOrderQuantity = allOrders.reduce((sum, item) => sum + (item.order || 0), 0);
    const recordsWithMapping = allOrders.filter(item => item.excel_row_hint && item.excel_column);
    const exportOrderQuantity = recordsWithMapping.reduce((sum, item) => sum + (item.order || 0), 0);
    
    console.log('📊 DISCREPANCY ANALYSIS:');
    console.log('='.repeat(50));
    console.log(`Total records with order > 0: ${allOrders.length}`);
    console.log(`Total order quantity (EMAIL): ${totalOrderQuantity} discs`);
    console.log(`Records with Excel mapping: ${recordsWithMapping.length}`);
    console.log(`Export order quantity (EXCEL): ${exportOrderQuantity} discs`);
    console.log(`❌ MISSING FROM EXPORT: ${totalOrderQuantity - exportOrderQuantity} discs`);
    console.log('='.repeat(50));
    
    // Show records missing mapping
    const missingMapping = allOrders.filter(item => !item.excel_row_hint || !item.excel_column);
    console.log(`\n🚨 Records missing Excel mapping: ${missingMapping.length}`);
    
    if (missingMapping.length > 0) {
      console.log('\n📋 Sample missing mapping records:');
      missingMapping.slice(0, 10).forEach((record, i) => {
        console.log(`  ${i+1}. ${record.plastic_name} ${record.mold_name} - Order: ${record.order}, Row: ${record.excel_row_hint || 'NULL'}, Col: ${record.excel_column || 'NULL'}`);
      });
      
      const missingOrderTotal = missingMapping.reduce((sum, item) => sum + (item.order || 0), 0);
      console.log(`\n💡 Total order quantity in missing records: ${missingOrderTotal} discs`);
    }
    
    // Check for changing data
    console.log('\n🔄 Checking for data volatility...');
    
    // Run the same query again after a short delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const { data: allOrders2, error: allError2 } = await supabase
      .from('v_stats_by_osl_discraft')
      .select('"order"')
      .gt('"order"', 0);
    
    if (!allError2) {
      const totalOrderQuantity2 = allOrders2.reduce((sum, item) => sum + (item.order || 0), 0);
      console.log(`Second query total: ${totalOrderQuantity2} discs`);
      
      if (totalOrderQuantity !== totalOrderQuantity2) {
        console.log(`⚠️  DATA IS CHANGING! Difference: ${Math.abs(totalOrderQuantity - totalOrderQuantity2)} discs`);
        console.log('This explains why the email totals keep changing between runs.');
      } else {
        console.log('✅ Data is stable between queries.');
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkDiscrepancy().catch(console.error);
