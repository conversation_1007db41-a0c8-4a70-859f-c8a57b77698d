// checkTask295531.js
// Check task 295531 (publish_mold_collection) and investigate why collection might not be visible on Shopify

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkTask295531() {
  try {
    console.log('🔍 Checking task 295531 (publish_mold_collection)...\n');
    
    // Check the task details
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 295531)
      .single();
    
    if (taskError) {
      console.error('❌ Error fetching task 295531:', taskError);
      return;
    }
    
    if (!task) {
      console.log('❌ Task 295531 not found');
      return;
    }
    
    console.log('📋 Task Details:');
    console.log(`   Task ID: ${task.id}`);
    console.log(`   Task Type: ${task.task_type}`);
    console.log(`   Status: ${task.status}`);
    console.log(`   Payload: ${JSON.stringify(task.payload)}`);
    console.log(`   Created At: ${task.created_at}`);
    console.log(`   Scheduled At: ${task.scheduled_at}`);
    console.log(`   Processed At: ${task.processed_at}`);
    console.log(`   Enqueued By: ${task.enqueued_by}`);
    
    if (task.result) {
      console.log(`   Result: ${JSON.stringify(task.result, null, 2)}`);
    }
    
    // Extract mold ID from payload
    let moldId;
    if (typeof task.payload === 'object' && task.payload.id) {
      moldId = task.payload.id;
    } else if (typeof task.payload === 'string') {
      try {
        const parsed = JSON.parse(task.payload);
        moldId = parsed.id;
      } catch (e) {
        console.log('❌ Could not parse payload to get mold ID');
        return;
      }
    }
    
    if (!moldId) {
      console.log('❌ Could not extract mold ID from payload');
      return;
    }
    
    console.log(`\n🥏 Checking mold record for ID: ${moldId}...`);
    
    // Check the mold record
    const { data: mold, error: moldError } = await supabase
      .from('t_molds')
      .select(`
        *,
        t_brands!inner(
          id,
          brand,
          shopify_collection_created_at
        )
      `)
      .eq('id', moldId)
      .single();
    
    if (moldError) {
      console.error('❌ Error fetching mold record:', moldError);
      return;
    }
    
    if (!mold) {
      console.log('❌ Mold record not found');
      return;
    }
    
    console.log('📋 Mold Details:');
    console.log(`   Mold ID: ${mold.id}`);
    console.log(`   Mold Name: ${mold.mold}`);
    console.log(`   Brand: ${mold.t_brands.brand}`);
    console.log(`   Type: ${mold.type}`);
    console.log(`   Code: ${mold.code}`);
    console.log(`   Speed/Glide/Turn/Fade: ${mold.speed}/${mold.glide}/${mold.turn}/${mold.fade}`);
    console.log(`   Description: ${mold.description ? mold.description.substring(0, 100) + '...' : 'None'}`);
    console.log(`   Embargo Until: ${mold.embargo_until || 'None'}`);
    console.log(`   Shopify Collection Created At: ${mold.shopify_collection_created_at || 'NOT SET'}`);
    console.log(`   Shopify Collection Upload Notes: ${mold.shopify_collection_uploaded_notes || 'None'}`);
    console.log(`   Brand Collection Created At: ${mold.t_brands.shopify_collection_created_at || 'NOT SET'}`);
    
    // Check for verified images
    const { data: images, error: imageError } = await supabase
      .from('t_images')
      .select('id, image_verified')
      .eq('table_name', 't_molds')
      .eq('record_id', moldId);
    
    if (imageError) {
      console.error('❌ Error fetching images:', imageError);
    } else {
      const verifiedImages = images.filter(img => img.image_verified);
      console.log(`   Images: ${images.length} total, ${verifiedImages.length} verified`);
    }
    
    // Check if there are any related follow-up tasks
    console.log(`\n🔗 Checking for related follow-up tasks...`);
    
    const { data: followUpTasks, error: followUpError } = await supabase
      .from('t_task_queue')
      .select('id, task_type, status, created_at, result')
      .eq('task_type', 'mold_collection_published')
      .contains('payload', { id: moldId })
      .order('created_at', { ascending: false })
      .limit(5);
    
    if (followUpError) {
      console.error('❌ Error fetching follow-up tasks:', followUpError);
    } else if (followUpTasks.length > 0) {
      console.log(`   Found ${followUpTasks.length} mold_collection_published tasks:`);
      followUpTasks.forEach(task => {
        console.log(`   - Task ${task.id}: ${task.status} (${task.created_at})`);
        if (task.result) {
          console.log(`     Result: ${JSON.stringify(task.result)}`);
        }
      });
    } else {
      console.log('   No mold_collection_published tasks found');
    }
    
    // Analysis and recommendations
    console.log(`\n📊 Analysis:`);
    
    if (task.status === 'completed' && mold.shopify_collection_created_at) {
      console.log('✅ Task shows completed AND mold record has shopify_collection_created_at timestamp');
      console.log('   This suggests the collection was successfully created in Shopify');
      console.log('   If you cannot see it on the store, possible reasons:');
      console.log('   1. Collection might be empty (no products with matching tags and inventory > 0)');
      console.log('   2. Collection might be unpublished or have visibility restrictions');
      console.log('   3. Collection handle might be different than expected');
      console.log('   4. Shopify admin cache might need refreshing');
    } else if (task.status === 'completed' && !mold.shopify_collection_created_at) {
      console.log('⚠️  ISSUE: Task shows completed but mold record does NOT have shopify_collection_created_at');
      console.log('   This suggests the publishCollectionMold.js process exited with code 0 but failed to update the database');
      console.log('   The collection was likely NOT created in Shopify');
    } else if (task.status !== 'completed') {
      console.log(`❌ Task status is "${task.status}", not completed`);
      if (task.result && task.result.error) {
        console.log(`   Error: ${task.result.error}`);
      }
    }
    
    // Generate collection handle for verification
    if (mold.t_brands.brand && mold.mold && mold.type) {
      const handle = generateMoldHandle(mold.t_brands.brand, mold.mold, mold.type);
      console.log(`\n🔗 Expected Collection Handle: ${handle}`);
      console.log(`   Check Shopify at: /admin/collections/${handle}`);
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

// Helper function to generate mold handle (copied from publishCollectionMold.js logic)
function generateMoldHandle(brand, mold, type) {
  // Convert to lowercase and replace spaces/special chars with hyphens
  const brandPart = brand.toLowerCase().replace(/[^a-z0-9]/g, '-');
  const moldPart = mold.toLowerCase().replace(/[^a-z0-9]/g, '-');
  const typePart = type.toLowerCase().replace(/[^a-z0-9]/g, '-');
  
  // Remove multiple consecutive hyphens and trim
  const handle = `${brandPart}-${moldPart}-${typePart}`
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
  
  return handle;
}

checkTask295531();
