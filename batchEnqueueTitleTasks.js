// batchEnqueueTitleTasks.js
// Script to enqueue tasks for generating title, pull, and handle for existing records
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';

dotenv.config();

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('batch-size', {
    alias: 'b',
    describe: 'Number of records to process in each batch',
    type: 'number',
    default: 500
  })
  .option('delay', {
    alias: 'd',
    describe: 'Delay in milliseconds between batches',
    type: 'number',
    default: 1000
  })
  .option('start-id', {
    alias: 's',
    describe: 'ID to start processing from',
    type: 'number',
    default: 0
  })
  .option('limit', {
    alias: 'l',
    describe: 'Maximum number of records to process (0 for all)',
    type: 'number',
    default: 0
  })
  .option('dry-run', {
    describe: 'Show what would be done without actually enqueueing tasks',
    type: 'boolean',
    default: false
  })
  .help()
  .alias('help', 'h')
  .argv;

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or key in environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function enqueueTasksInBatches() {
  const BATCH_SIZE = argv.batchSize;
  const DELAY_MS = argv.delay;
  const START_ID = argv.startId;
  const MAX_RECORDS = argv.limit;
  const DRY_RUN = argv.dryRun;

  let startId = START_ID;
  let totalEnqueued = 0;
  let hasMoreRecords = true;

  console.log('Starting batch enqueue process...');
  console.log(`Batch size: ${BATCH_SIZE}, Delay: ${DELAY_MS}ms, Start ID: ${START_ID}`);
  if (MAX_RECORDS > 0) {
    console.log(`Will process at most ${MAX_RECORDS} records`);
  }
  if (DRY_RUN) {
    console.log('DRY RUN MODE: No tasks will actually be enqueued');
  }

  while (hasMoreRecords) {
    // Check if we've reached the maximum number of records to process
    if (MAX_RECORDS > 0 && totalEnqueued >= MAX_RECORDS) {
      console.log(`Reached maximum number of records to process (${MAX_RECORDS})`);
      break;
    }

    // Calculate how many records to fetch in this batch
    const batchLimit = MAX_RECORDS > 0
      ? Math.min(BATCH_SIZE, MAX_RECORDS - totalEnqueued)
      : BATCH_SIZE;

    if (batchLimit <= 0) {
      break;
    }

    // Get a batch of records that need updating
    const { data: discs, error } = await supabase
      .from('t_discs')
      .select('id')
      .or('g_title.is.null,g_pull.is.null,g_handle.is.null')
      .gt('id', startId)
      .order('id')
      .limit(batchLimit);

    if (error) {
      console.error('Error fetching discs:', error);
      break;
    }

    if (!discs || discs.length === 0) {
      hasMoreRecords = false;
      console.log('No more records to process.');
      break;
    }

    console.log(`Processing batch of ${discs.length} records starting from ID ${startId}`);

    if (!DRY_RUN) {
      // Enqueue tasks for this batch
      const now = new Date().toISOString();
      const tasks = discs.map(disc => ({
        task_type: 'generate_disc_title_pull_and_handle',
        payload: { id: disc.id },
        status: 'pending',
        scheduled_at: now,
        created_at: now
      }));

      // Split into smaller chunks to avoid potential issues with large inserts
      const CHUNK_SIZE = 100;
      for (let i = 0; i < tasks.length; i += CHUNK_SIZE) {
        const chunk = tasks.slice(i, i + CHUNK_SIZE);

        const { error: insertError } = await supabase
          .from('t_task_queue')
          .insert(chunk);

        if (insertError) {
          console.error(`Error enqueueing tasks chunk ${i / CHUNK_SIZE + 1}:`, insertError);
          console.error('Stopping process due to error');
          return;
        }

        console.log(`Successfully enqueued chunk ${i / CHUNK_SIZE + 1} of ${Math.ceil(tasks.length / CHUNK_SIZE)} (${chunk.length} tasks)`);
      }
    } else {
      console.log(`DRY RUN: Would have enqueued ${discs.length} tasks`);
    }

    totalEnqueued += discs.length;
    console.log(`Progress: ${totalEnqueued} tasks enqueued so far`);

    // Update startId for the next batch
    startId = discs[discs.length - 1].id;

    // Add a delay between batches to avoid overwhelming the database
    if (DELAY_MS > 0 && hasMoreRecords) {
      console.log(`Waiting ${DELAY_MS}ms before next batch...`);
      await new Promise(resolve => setTimeout(resolve, DELAY_MS));
    }
  }

  console.log(`Batch enqueue process completed. Total tasks enqueued: ${totalEnqueued}`);
}

// Run the main function
enqueueTasksInBatches().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
