CREATE OR REPLACE FUNCTION public.fn_enqueue_new_t_discs_record()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
DECLARE
  task_id integer;
BEGIN
  -- Insert a task into the task queue
  INSERT INTO t_task_queue (
    task_type,
    payload,
    status,
    scheduled_at,
    created_at,
    enqueued_by
  )
  VALUES (
    'new_t_discs_record',
    jsonb_build_object('id', NEW.id),
    'pending',
    NOW(),
    NOW(),
    't_discs insert_trigger_' || NEW.id
  )
  RETURNING id INTO task_id;

  RAISE NOTICE 'Enqueued new_t_discs_record task (id: %) for disc id %', task_id, NEW.id;
  
  RETURN NEW;
END;
$function$;
