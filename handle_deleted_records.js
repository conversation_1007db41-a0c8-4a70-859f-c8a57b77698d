import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function handleDeletedRecords() {
  try {
    console.log('Adding XXXX to records containing "deleted"...\n');

    // Find records that contain "deleted" but don't already start with XXXX
    const { data: deletedRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .like('notes', '%deleted%')
      .not('notes', 'like', 'XXXX%');

    if (fetchError) {
      console.error('Error fetching deleted records:', fetchError);
      return;
    }

    console.log(`Found ${deletedRecords?.length || 0} records containing "deleted" that need XXXX prefix`);

    let updatedCount = 0;

    for (const record of deletedRecords || []) {
      const updateData = {
        notes: `XXXX ${record.notes}`
      };

      // Store original notes in raw_notes if not already stored
      if (!record.raw_notes) {
        updateData.raw_notes = record.notes;
      }

      const { error: updateError } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', record.id);

      if (updateError) {
        console.error(`Error updating record ${record.id}:`, updateError);
      } else {
        updatedCount++;
        if (updatedCount <= 10) { // Show first 10 updates
          console.log(`  Updated ID ${record.id}: XXXX ${record.notes.substring(0, 50)}...`);
        }
      }
    }

    console.log(`\nSuccessfully updated ${updatedCount} records with "deleted" to have XXXX prefix`);

    // Verify the update
    console.log('\nVerifying update...');
    const { count: remainingCount, error: verifyError } = await supabase
      .from('t_sdasins')
      .select('*', { count: 'exact', head: true })
      .like('notes', '%deleted%')
      .not('notes', 'like', 'XXXX%');

    if (!verifyError) {
      console.log(`Remaining records with "deleted" that don't start with XXXX: ${remainingCount}`);
    }

    console.log('\nDeleted records handling completed successfully!');

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the script
handleDeletedRecords();
