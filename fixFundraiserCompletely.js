import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function fixFundraiserCompletely() {
    try {
        console.log('🔧 Fixing fundraiser section completely...\n');
        
        // 1. First, mark ALL row 22 records as not orderable
        console.log('1. Marking row 22 header records as not orderable...');
        const { data: headerUpdated, error: headerError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .update({ 
                is_orderable: false
            })
            .eq('excel_row_hint', 22)
            .select();

        if (headerError) {
            console.error('❌ Error updating header records:', headerError);
        } else {
            console.log(`✅ Updated ${headerUpdated?.length || 0} header records to not orderable`);
        }

        // 2. Delete any existing fundraiser records in rows 25 and 28 (cleanup)
        console.log('\n2. Cleaning up any existing fundraiser records in rows 25 and 28...');
        const { data: deletedRecords, error: deleteError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .delete()
            .in('excel_row_hint', [25, 28])
            .select();

        if (deleteError) {
            console.error('❌ Error deleting existing records:', deleteError);
        } else {
            console.log(`✅ Deleted ${deletedRecords?.length || 0} existing records from rows 25 and 28`);
        }

        // 3. Create the correct fundraiser records in column A
        console.log('\n3. Creating correct fundraiser records in column A...');
        
        const fundraiserRecords = [
            {
                mold_name: 'Thrasher',
                plastic_name: 'Elite Z Jawbreaker',
                min_weight: 150,
                max_weight: 180,
                color_name: 'Varies',
                stamp_name: 'Live Free - Be Happy - Funky Ben Askren Fundraiser',
                vendor_product_code: 'Elite_Z_Jawbreaker_Thrasher_150-180',
                is_orderable: true,
                is_currently_available: true,
                cost_price: 10.00,
                excel_mapping_key: 'SPECIAL|Fundraiser - Ben Askren Z Jawbreaker Thrasher (msrp $19.99, cost $10.00)|Order Qty',
                excel_row_hint: 25,
                excel_column: 'A',
                raw_line_type: 'SPECIAL',
                raw_model: 'Fundraiser - Ben Askren Z Jawbreaker Thrasher (msrp $19.99, cost $10.00)',
                raw_weight_range: 'Order Qty',
                vendor_description: '',
                calculated_mps_id: 19704 // Known MPS ID
            },
            {
                mold_name: 'Buzzz',
                plastic_name: 'Big Z Collection',
                min_weight: 150,
                max_weight: 180,
                color_name: 'Varies',
                stamp_name: 'Live Free - Be Happy - Funky Ben Askren Fundraiser',
                vendor_product_code: 'Big_Z_Collection_Buzzz_150-180',
                is_orderable: true,
                is_currently_available: true,
                cost_price: 10.00,
                excel_mapping_key: 'SPECIAL|Fundraiser - Ben Askren Big Z Buzzz  (msrp $19.99, cost $10.00)|Order Qty',
                excel_row_hint: 28,
                excel_column: 'A',
                raw_line_type: 'SPECIAL',
                raw_model: 'Fundraiser - Ben Askren Big Z Buzzz  (msrp $19.99, cost $10.00)',
                raw_weight_range: 'Order Qty',
                vendor_description: '',
                calculated_mps_id: null // Will show NO_MPS until you add the MPS record
            }
        ];

        for (const record of fundraiserRecords) {
            console.log(`Creating: ${record.plastic_name} ${record.mold_name} in row ${record.excel_row_hint}`);
            
            const { error: insertError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .insert(record);

            if (insertError) {
                console.error(`❌ Error inserting ${record.mold_name}:`, insertError);
            } else {
                console.log(`✅ Created ${record.mold_name} record`);
            }
        }

        // 4. Verify the fixes
        console.log('\n4. Verifying fixes...');
        
        // Check row 22 orderable status
        const { data: row22Check, error: row22Error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_column, is_orderable')
            .eq('excel_row_hint', 22);

        if (row22Error) {
            console.error('❌ Error checking row 22:', row22Error);
        } else {
            const orderableRow22 = row22Check.filter(r => r.is_orderable);
            console.log(`✅ Row 22: ${orderableRow22.length} orderable records (should be 0)`);
        }

        // Check column A records
        const { data: columnACheck, error: columnAError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, mold_name, plastic_name, calculated_mps_id')
            .eq('excel_column', 'A')
            .in('excel_row_hint', [25, 28]);

        if (columnAError) {
            console.error('❌ Error checking column A:', columnAError);
        } else {
            console.log(`✅ Column A: ${columnACheck.length} fundraiser records (should be 2)`);
            columnACheck.forEach(record => {
                console.log(`   Row ${record.excel_row_hint}: ${record.plastic_name} ${record.mold_name} (MPS: ${record.calculated_mps_id || 'NO_MPS'})`);
            });
        }

        // Check what daily automation will export
        const { data: exportCheck, error: exportError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, calculated_mps_id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .in('excel_row_hint', [22, 25, 28]);

        if (exportError) {
            console.error('❌ Error checking export data:', exportError);
        } else {
            console.log(`\n✅ Daily automation will export ${exportCheck.length} records from rows 22, 25, 28:`);
            exportCheck.forEach(record => {
                console.log(`   Row ${record.excel_row_hint}, Col ${record.excel_column}: MPS ${record.calculated_mps_id || 'NO_MPS'}`);
            });
        }

        console.log('\n🎉 Fundraiser fix completed!');
        console.log('\n📋 Expected results in next order:');
        console.log('   • Row 22: NO entries (header not orderable)');
        console.log('   • Row 25, Column A: Thrasher with MPS ID 19704');
        console.log('   • Row 28, Column A: Buzzz with NO_MPS (you can add MPS record)');
        
    } catch (error) {
        console.error('❌ Fix failed:', error.message);
    }
}

fixFundraiserCompletely().catch(console.error);
