// processCheckIfRproIsReadyTask.js - Task handler for check_if_rpro_is_ready
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

/**
 * Process a check_if_rpro_is_ready task
 * Checks RPRO records for readiness issues and updates the todo field
 * 
 * @param {Object} task - Task object from the queue
 * @param {Object} context - Context object with supabase, updateTaskStatus, logError
 * @returns {Promise<void>}
 */
export async function processCheckIfRproIsReadyTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processCheckIfRproIsReadyTask] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[processCheckIfRproIsReadyTask] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[processCheckIfRproIsReadyTask] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[processCheckIfRproIsReadyTask] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[processCheckIfRproIsReadyTask] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[processCheckIfRproIsReadyTask] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[processCheckIfRproIsReadyTask] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[processCheckIfRproIsReadyTask] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if RPRO is ready. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[processCheckIfRproIsReadyTask] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[processCheckIfRproIsReadyTask] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if RPRO is ready. Missing RPRO id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const rproId = payload.id;

    console.log(`[processCheckIfRproIsReadyTask] Checking if RPRO record with id=${rproId} is ready`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Fetch the RPRO record
    console.log(`[processCheckIfRproIsReadyTask] Fetching RPRO record for id=${rproId}...`);
    const { data: rproRecord, error: fetchError } = await supabase
      .from('imported_table_rpro')
      .select('id, ivno, ivqtylaw, ivaux3, todo')
      .eq('id', rproId)
      .maybeSingle();

    if (fetchError) {
      const errMsg = `[processCheckIfRproIsReadyTask] Error fetching RPRO record: ${fetchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching RPRO record for id=${rproId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if RPRO is ready. Error fetching RPRO record.",
        error: fetchError.message
      });
      return;
    }

    if (!rproRecord) {
      const errMsg = `[processCheckIfRproIsReadyTask] RPRO record not found for id=${rproId}`;
      console.error(errMsg);
      await logError(errMsg, `RPRO record not found for id=${rproId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check if RPRO is ready. RPRO record not found.",
        error: 'RPRO record not found'
      });
      return;
    }

    console.log(`[processCheckIfRproIsReadyTask] Retrieved RPRO record: ${JSON.stringify(rproRecord)}`);

    // Check for issues
    const issues = [];

    // Issue 1: In stock items (ivqtylaw > 0) must have a bin section (ivaux3 not null)
    const quantity = parseFloat(rproRecord.ivqtylaw) || 0;
    const binSection = rproRecord.ivaux3;

    if (quantity > 0 && (!binSection || binSection.trim() === '')) {
      issues.push(`In stock item (qty: ${quantity}) missing bin section (ivaux3)`);
    }

    // Determine the todo message
    let todoMessage;
    if (issues.length === 0) {
      todoMessage = `No Issues Found on ${new Date().toISOString()}`;
    } else {
      todoMessage = issues.join('; ');
    }

    console.log(`[processCheckIfRproIsReadyTask] Issues found: ${issues.length}`);
    console.log(`[processCheckIfRproIsReadyTask] Todo message: ${todoMessage}`);

    // Update the RPRO record with the todo message
    const { error: updateError } = await supabase
      .from('imported_table_rpro')
      .update({ todo: todoMessage })
      .eq('id', rproId);

    if (updateError) {
      const errMsg = `[processCheckIfRproIsReadyTask] Error updating RPRO record: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating RPRO record for id=${rproId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update RPRO record with readiness check results.",
        error: updateError.message
      });
      return;
    }

    console.log(`[processCheckIfRproIsReadyTask] Successfully updated RPRO record ${rproId} with todo: ${todoMessage}`);

    // Mark the task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully checked RPRO readiness for record ${rproId}`,
      rpro_id: rproId,
      ivno: rproRecord.ivno,
      issues_found: issues.length,
      issues: issues,
      todo: todoMessage,
      ready: issues.length === 0
    });

  } catch (err) {
    const errMsg = `[processCheckIfRproIsReadyTask] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to check if RPRO is ready due to an unexpected error.",
      error: err.message
    });
  }
}

export default processCheckIfRproIsReadyTask;
