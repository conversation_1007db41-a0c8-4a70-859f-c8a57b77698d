const http = require('http');

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000 // 10 second timeout
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(body));
        } catch (e) {
          resolve(body);
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testCurrentAutoSelect() {
  try {
    console.log('Testing current auto-select behavior...');
    
    const autoSelectResult = await makeRequest('/api/b2f/auto-select', 'POST', { maxOsls: 10 });
    console.log('Auto-select result:');
    console.log('Success:', autoSelectResult.success);
    console.log('Message:', autoSelectResult.message);
    
    if (autoSelectResult.selectedDiscs) {
      console.log('\nSelected discs by OSL:');
      autoSelectResult.selectedDiscs.forEach(osl => {
        const miniFlag = osl.isMiniMold ? ' 🏆 MINI MOLD' : '';
        const moldInfo = osl.moldName ? ' (' + osl.moldName + ')' : '';
        console.log('\n- ' + osl.osl + moldInfo + miniFlag);
        console.log('  Needed: ' + osl.needed + ', Selected: ' + osl.selected);
        
        if (osl.discs && osl.discs.length > 0) {
          osl.discs.slice(0, 2).forEach(disc => { // Show first 2 discs
            console.log('  • ' + disc.disc + ' (Grade: ' + disc.grade + ')');
          });
          if (osl.discs.length > 2) {
            console.log('  • ... and ' + (osl.discs.length - 2) + ' more discs');
          }
        }
      });
    }
    
    if (autoSelectResult.summary) {
      console.log('\nSummary:');
      console.log('- Total OSLs processed:', autoSelectResult.summary.totalOsls);
      console.log('- Total discs selected:', autoSelectResult.summary.totalDiscs);
      console.log('- Successful updates:', autoSelectResult.summary.successfulUpdates);
      console.log('- Errors:', autoSelectResult.summary.errors);
    }
    
    if (autoSelectResult.error) {
      console.log('\nError:', autoSelectResult.error);
    }
    
  } catch (err) {
    console.error('Error:', err.message);
  }
}

testCurrentAutoSelect();
