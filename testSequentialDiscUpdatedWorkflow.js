// testSequentialDiscUpdatedWorkflow.js - Test the new sequential disc updated workflow
import dotenv from 'dotenv';
dotenv.config();

import { enqueueDiscUpdatedNeedToResetTask } from './enqueueDiscUpdatedNeedToResetTask.js';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testSequentialWorkflow() {
  console.log('🧪 Testing Sequential Disc Updated Workflow');
  console.log('============================================');

  try {
    // Use a test disc ID - replace with actual disc that exists and is uploaded to Shopify
    const testDiscId = 401746; // Replace with actual disc ID
    console.log(`\n🎯 Testing sequential workflow for disc ID: ${testDiscId}`);

    // Check current disc status
    const { data: discData, error: discError } = await supabase
      .from('t_discs')
      .select('id, sold_date, shopify_uploaded_at, mps_id')
      .eq('id', testDiscId)
      .single();

    if (discError) {
      console.error('❌ Error fetching disc data:', discError.message);
      return;
    }

    if (!discData) {
      console.error('❌ Disc not found');
      return;
    }

    console.log(`\n📊 Current Disc Status:`);
    console.log(`   Disc ID: ${discData.id}`);
    console.log(`   MPS ID: ${discData.mps_id}`);
    console.log(`   Sold Date: ${discData.sold_date || 'null'}`);
    console.log(`   Shopify Uploaded: ${discData.shopify_uploaded_at || 'null'}`);

    // Enqueue the master task
    console.log('\n🚀 Enqueueing disc_updated_need_to_reset task...');
    const masterTask = await enqueueDiscUpdatedNeedToResetTask(testDiscId, discData.sold_date);
    console.log(`✅ Master task enqueued: ID ${masterTask.id}`);
    console.log(`   Scheduled for: ${masterTask.scheduled_at}`);
    console.log(`   Enqueued by: ${masterTask.enqueued_by}`);

    console.log('\n📋 Expected Sequential Workflow:');
    console.log('================================');
    console.log('1. ⏰ Master task processes in 5 minutes');
    console.log('2. 🎯 Intelligent task enqueueing based on sold status:');

    if (!discData.sold_date) {
      console.log('   📌 Disc is NOT sold:');
      console.log('   - disc_updated_sell_it (immediate)');
      console.log('   - disc_updated_delete_from_shopify (2 min delay)');
    } else {
      console.log('   ⚡ Disc is ALREADY sold - OPTIMIZED:');
      console.log('   - disc_updated_sell_it (SKIPPED)');
      console.log('   - disc_updated_delete_from_shopify (immediate - no delay!)');
    }

    console.log('');
    console.log('3. 🔗 Sequential chain (each task enqueues next on success):');
    console.log('   - disc_updated_delete_from_shopify → disc_updated_reset');
    console.log('   - disc_updated_reset → disc_updated_unsell');
    console.log('   - disc_updated_unsell → new_t_discs_record');
    console.log('');
    console.log('4. 🛡️ Strict success criteria:');
    console.log('   - Shopify deletion must delete BOTH variant AND product (if last variant)');
    console.log('   - If any task fails, workflow stops at that point');
    console.log('   - Each task reports next_task_enqueued status');
    console.log('');
    console.log('5. ⚡ Optimization benefits:');
    console.log('   - Faster processing for already-sold discs');
    console.log('   - No unnecessary task enqueueing');
    console.log('   - Immediate Shopify deletion when possible');

    console.log('\n🔍 Monitoring Instructions:');
    console.log('===========================');
    console.log('1. Watch the task queue worker logs');
    console.log('2. Check task results for next_task_enqueued field');
    console.log('3. Verify workflow stops if Shopify deletion fails');
    console.log('4. Confirm tasks run in exact sequence');

    console.log('\n📊 SQL Query to Monitor Progress:');
    console.log('=================================');
    console.log(`SELECT task_type, status, created_at, scheduled_at, enqueued_by,`);
    console.log(`       result->>'next_task_enqueued' as next_enqueued,`);
    console.log(`       result->>'message' as message`);
    console.log(`FROM t_task_queue`);
    console.log(`WHERE enqueued_by LIKE '%_${testDiscId}'`);
    console.log(`   OR (task_type = 'disc_updated_need_to_reset' AND payload->>'id' = '${testDiscId}')`);
    console.log(`ORDER BY created_at;`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testSequentialWorkflow()
  .then(() => {
    console.log('\n🏁 Test setup completed');
    console.log('Monitor the task queue worker to see the sequential workflow in action!');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Test setup failed:', error);
    process.exit(1);
  });
