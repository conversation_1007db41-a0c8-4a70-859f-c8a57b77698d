// testRproTaskHandlerWithInStock.js - Test the task handler with an in-stock item
import { createClient } from '@supabase/supabase-js';
import { processCheckIfRproIsReadyTask } from './processCheckIfRproIsReadyTask.js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

// Mock functions for testing
async function updateTaskStatus(taskId, status, result = null) {
  console.log(`📝 Mock updateTaskStatus: Task ${taskId} -> ${status}`);
  if (result) {
    console.log(`   Result: ${JSON.stringify(result, null, 2)}`);
  }
  return true;
}

async function logError(message, context) {
  console.log(`❌ Mock logError: ${message} (Context: ${context})`);
  return true;
}

async function testWithInStockItem() {
  try {
    console.log('🧪 Testing with in-stock item (qty > 0)...');
    console.log('==========================================');

    // Find a record with quantity > 0
    const { data: inStockRecord, error: stockError } = await supabase
      .from('imported_table_rpro')
      .select('id, ivno, ivqtylaw, ivaux3, todo')
      .gt('ivqtylaw', 0)
      .limit(1)
      .single();

    if (stockError || !inStockRecord) {
      console.log('ℹ️  No in-stock records found, creating a test scenario...');
      
      // Get any record and temporarily modify it for testing
      const { data: anyRecord, error: anyError } = await supabase
        .from('imported_table_rpro')
        .select('id, ivno, ivqtylaw, ivaux3, todo')
        .limit(1)
        .single();

      if (anyError) {
        console.error('❌ Error fetching any record:', anyError.message);
        return;
      }

      console.log('\n🔧 Creating test scenario with temporary data...');
      
      // Test scenario 1: In stock with no bin section (should have issue)
      console.log('\n📋 Test 1: In stock item with no bin section');
      console.log('============================================');
      
      // Temporarily set quantity > 0 and clear bin section
      const { error: updateError1 } = await supabase
        .from('imported_table_rpro')
        .update({ 
          ivqtylaw: 5,
          ivaux3: null,
          todo: null 
        })
        .eq('id', anyRecord.id);

      if (updateError1) {
        console.error('❌ Error updating record for test:', updateError1.message);
        return;
      }

      // Test the handler
      const mockTask1 = {
        id: 999998,
        task_type: 'check_if_rpro_is_ready',
        payload: { id: anyRecord.id }
      };

      console.log('🚀 Running task handler...');
      await processCheckIfRproIsReadyTask(mockTask1, {
        supabase,
        updateTaskStatus,
        logError
      });

      // Check result
      const { data: result1, error: resultError1 } = await supabase
        .from('imported_table_rpro')
        .select('id, ivno, ivqtylaw, ivaux3, todo')
        .eq('id', anyRecord.id)
        .single();

      if (resultError1) {
        console.error('❌ Error fetching result:', resultError1.message);
        return;
      }

      console.log('\n📊 Result 1:');
      console.log(`  Qty: ${result1.ivqtylaw}, Bin: ${result1.ivaux3 || 'null'}`);
      console.log(`  Todo: ${result1.todo}`);
      
      if (result1.todo && result1.todo.includes('missing bin section')) {
        console.log('✅ Correctly identified missing bin section issue');
      } else {
        console.log('❌ Failed to identify missing bin section issue');
      }

      // Test scenario 2: In stock with bin section (should be ready)
      console.log('\n📋 Test 2: In stock item with bin section');
      console.log('=========================================');
      
      // Set bin section
      const { error: updateError2 } = await supabase
        .from('imported_table_rpro')
        .update({ 
          ivqtylaw: 5,
          ivaux3: 'A1-B2',
          todo: null 
        })
        .eq('id', anyRecord.id);

      if (updateError2) {
        console.error('❌ Error updating record for test 2:', updateError2.message);
        return;
      }

      // Test the handler
      const mockTask2 = {
        id: 999997,
        task_type: 'check_if_rpro_is_ready',
        payload: { id: anyRecord.id }
      };

      console.log('🚀 Running task handler...');
      await processCheckIfRproIsReadyTask(mockTask2, {
        supabase,
        updateTaskStatus,
        logError
      });

      // Check result
      const { data: result2, error: resultError2 } = await supabase
        .from('imported_table_rpro')
        .select('id, ivno, ivqtylaw, ivaux3, todo')
        .eq('id', anyRecord.id)
        .single();

      if (resultError2) {
        console.error('❌ Error fetching result 2:', resultError2.message);
        return;
      }

      console.log('\n📊 Result 2:');
      console.log(`  Qty: ${result2.ivqtylaw}, Bin: ${result2.ivaux3 || 'null'}`);
      console.log(`  Todo: ${result2.todo}`);
      
      if (result2.todo && result2.todo.includes('No Issues Found')) {
        console.log('✅ Correctly identified as ready (no issues)');
      } else {
        console.log('❌ Failed to identify as ready');
      }

      // Restore original values
      console.log('\n🔄 Restoring original values...');
      const { error: restoreError } = await supabase
        .from('imported_table_rpro')
        .update({ 
          ivqtylaw: anyRecord.ivqtylaw,
          ivaux3: anyRecord.ivaux3,
          todo: anyRecord.todo 
        })
        .eq('id', anyRecord.id);

      if (restoreError) {
        console.error('❌ Error restoring original values:', restoreError.message);
      } else {
        console.log('✅ Original values restored');
      }

    } else {
      console.log('📋 Found in-stock record to test with:');
      console.log(`  ID: ${inStockRecord.id}, IVNO: ${inStockRecord.ivno}`);
      console.log(`  Qty: ${inStockRecord.ivqtylaw}, Bin: ${inStockRecord.ivaux3 || 'null'}`);
      console.log(`  Current Todo: ${inStockRecord.todo || 'null'}`);

      // Test with the actual in-stock record
      const mockTask = {
        id: 999996,
        task_type: 'check_if_rpro_is_ready',
        payload: { id: inStockRecord.id }
      };

      console.log('\n🚀 Running task handler...');
      await processCheckIfRproIsReadyTask(mockTask, {
        supabase,
        updateTaskStatus,
        logError
      });

      // Check result
      const { data: result, error: resultError } = await supabase
        .from('imported_table_rpro')
        .select('id, ivno, ivqtylaw, ivaux3, todo')
        .eq('id', inStockRecord.id)
        .single();

      if (resultError) {
        console.error('❌ Error fetching result:', resultError.message);
        return;
      }

      console.log('\n📊 Result:');
      console.log(`  Qty: ${result.ivqtylaw}, Bin: ${result.ivaux3 || 'null'}`);
      console.log(`  Todo: ${result.todo}`);
    }

    console.log('\n🎉 In-stock item test completed!');

  } catch (err) {
    console.error('❌ Exception:', err.message);
    console.error(err.stack);
  }
}

testWithInStockItem();
