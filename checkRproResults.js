// checkRproResults.js - Check the results of RPRO readiness checks
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkResults() {
  try {
    console.log('🔍 Checking RPRO readiness check results...');
    console.log('==========================================');

    // Get summary statistics
    const { data: allRecords, error: allError } = await supabase
      .from('imported_table_rpro')
      .select('id, ivno, ivqtylaw, ivaux3, todo')
      .not('todo', 'is', null);

    if (allError) {
      console.error('❌ Error fetching records:', allError.message);
      return;
    }

    console.log(`📊 Total records with todo field populated: ${allRecords.length}`);

    // Count ready vs not ready
    const readyRecords = allRecords.filter(r => r.todo && r.todo.includes('No Issues Found'));
    const notReadyRecords = allRecords.filter(r => r.todo && !r.todo.includes('No Issues Found'));

    console.log(`✅ Ready records: ${readyRecords.length}`);
    console.log(`⚠️  Not ready records: ${notReadyRecords.length}`);

    // Show some examples of not ready records
    if (notReadyRecords.length > 0) {
      console.log('\n📋 Sample not ready records:');
      console.log('============================');
      notReadyRecords.slice(0, 10).forEach(record => {
        console.log(`  IVNO: ${record.ivno}, Qty: ${record.ivqtylaw}, Bin: ${record.ivaux3 || 'null'}`);
        console.log(`    Issue: ${record.todo}`);
        console.log('');
      });
    }

    // Show some examples of ready records
    if (readyRecords.length > 0) {
      console.log('\n✅ Sample ready records:');
      console.log('========================');
      readyRecords.slice(0, 5).forEach(record => {
        console.log(`  IVNO: ${record.ivno}, Qty: ${record.ivqtylaw}, Bin: ${record.ivaux3 || 'null'}`);
        console.log(`    Status: ${record.todo}`);
        console.log('');
      });
    }

    // Check for specific issue types
    const binIssues = notReadyRecords.filter(r => r.todo && r.todo.includes('missing bin section'));
    console.log(`🗂️  Records with bin section issues: ${binIssues.length}`);

    if (binIssues.length > 0) {
      console.log('\n📦 Sample bin section issues:');
      console.log('=============================');
      binIssues.slice(0, 5).forEach(record => {
        console.log(`  IVNO: ${record.ivno}, Qty: ${record.ivqtylaw}, Bin: ${record.ivaux3 || 'null'}`);
      });
    }

  } catch (err) {
    console.error('❌ Exception:', err.message);
  }
}

checkResults();
