// temp.js
import dotenv from 'dotenv';
dotenv.config();

import pkg from 'pg';
const { Client } = pkg;

// Use the SUPABASE_URL environment variable as the connection string
const connectionString = process.env.SUPABASE_URL;
if (!connectionString) {
  console.error('Missing SUPABASE_URL in .env file');
  process.exit(1);
}

const client = new Client({
  connectionString,
});

async function runChunkedUpsert() {
  const query = `
DO $$
DECLARE
  rec_count INTEGER;
BEGIN
  LOOP
    WITH chunk AS (
      DELETE FROM public.temp20250227
      WHERE id IN (
        SELECT id FROM public.temp20250227 ORDER BY id LIMIT 500
      )
      RETURNING id, sort1, sort2
    )
    INSERT INTO public.t_order_sheet_lines (id, sort1, sort2)
    SELECT id, sort1, sort2
    FROM chunk
    ON CONFLICT (id)
    DO UPDATE SET
      sort1 = EXCLUDED.sort1,
      sort2 = EXCLUDED.sort2;

    GET DIAGNOSTICS rec_count = ROW_COUNT;
    EXIT WHEN rec_count = 0;
  END LOOP;
END $$;
`;

  try {
    console.log('Starting chunked upsert process...');
    await client.connect();
    await client.query(query);
    console.log('Upsert completed successfully.');
  } catch (error) {
    console.error('Error during upsert:', error);
  } finally {
    await client.end();
  }
}

runChunkedUpsert();
