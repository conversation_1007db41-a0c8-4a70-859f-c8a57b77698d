-- Enqueue product readiness check on updates to readiness-related fields
CREATE OR REPLACE FUNCTION public.enqueue_check_if_product_is_ready_on_update()
RETURNS TRIGGER AS $$
BEGIN
  IF (OLD.brand_id IS DISTINCT FROM NEW.brand_id)
     OR (OLD.shopify_handle IS DISTINCT FROM NEW.shopify_handle)
     OR (OLD.description IS DISTINCT FROM NEW.description)
     OR (OLD.notes IS DISTINCT FROM NEW.notes)
  THEN
    INSERT INTO public.t_task_queue (task_type, payload, scheduled_at, enqueued_by)
    VALUES (
      'check_if_product_is_ready',
      jsonb_build_object('id', NEW.id),
      NOW(),
      'trigger:t_products.readiness_fields'
    );
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop and recreate trigger
DROP TRIGGER IF EXISTS trg_t_products_readiness_updated ON public.t_products;
CREATE TRIGGER trg_t_products_readiness_updated
AFTER UPDATE OF brand_id, shopify_handle, description, notes ON public.t_products
FOR EACH ROW
WHEN ((OLD.brand_id IS DISTINCT FROM NEW.brand_id)
   OR (OLD.shopify_handle IS DISTINCT FROM NEW.shopify_handle)
   OR (OLD.description IS DISTINCT FROM NEW.description)
   OR (OLD.notes IS DISTINCT FROM NEW.notes))
EXECUTE FUNCTION public.enqueue_check_if_product_is_ready_on_update();

