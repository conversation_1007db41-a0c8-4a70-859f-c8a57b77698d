// createReconcileTable.js - <PERSON><PERSON>t to create and populate the reconciliation table

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Create a log file
const logFile = 'reconcile_table.log';
fs.writeFileSync(logFile, `Starting reconciliation table creation at ${new Date().toISOString()}\n`);

// Load environment variables
dotenv.config();
fs.appendFileSync(logFile, `Environment variables loaded\n`);

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  const errorMsg = 'Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file';
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

fs.appendFileSync(logFile, `Supabase credentials found\n`);
const supabase = createClient(supabaseUrl, supabaseKey);
fs.appendFileSync(logFile, `Supabase client created\n`);

// Read the SQL file
const sqlFilePath = './create_reconcile_table.sql';
fs.appendFileSync(logFile, `Reading SQL file: ${sqlFilePath}\n`);

if (!fs.existsSync(sqlFilePath)) {
  const errorMsg = `SQL file not found: ${sqlFilePath}`;
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
fs.appendFileSync(logFile, `SQL file read successfully\n`);

// Function to execute SQL
async function executeSql(sql) {
  try {
    fs.appendFileSync(logFile, `Executing SQL...\n`);
    console.log('Executing SQL...');
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
    
    if (error) {
      fs.appendFileSync(logFile, `Error executing SQL: ${error.message}\n`);
      console.error(`Error executing SQL: ${error.message}`);
      
      // Try to execute the SQL in smaller chunks
      fs.appendFileSync(logFile, `Trying to execute SQL in smaller chunks...\n`);
      console.log('Trying to execute SQL in smaller chunks...');
      
      // Split the SQL into statements
      const statements = sql.split(';').filter(stmt => stmt.trim() !== '');
      
      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i].trim() + ';';
        fs.appendFileSync(logFile, `Executing statement ${i + 1} of ${statements.length}...\n`);
        console.log(`Executing statement ${i + 1} of ${statements.length}...`);
        
        const { error: stmtError } = await supabase.rpc('exec_sql', { sql_query: statement });
        
        if (stmtError) {
          fs.appendFileSync(logFile, `Error executing statement ${i + 1}: ${stmtError.message}\n`);
          console.error(`Error executing statement ${i + 1}: ${stmtError.message}`);
        } else {
          fs.appendFileSync(logFile, `Statement ${i + 1} executed successfully\n`);
          console.log(`Statement ${i + 1} executed successfully`);
        }
      }
      
      return false;
    }
    
    fs.appendFileSync(logFile, `SQL executed successfully\n`);
    console.log('SQL executed successfully');
    return true;
  } catch (error) {
    fs.appendFileSync(logFile, `Error executing SQL: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`Error executing SQL: ${error.message}`);
    console.error(error.stack);
    return false;
  }
}

// Function to check if the reconciliation table exists
async function checkTableExists() {
  try {
    fs.appendFileSync(logFile, `Checking if reconciliation table exists...\n`);
    console.log('Checking if reconciliation table exists...');
    
    // Try to select from the table
    const { data, error } = await supabase
      .from('reconcile_rpro_counts_to_veeqo')
      .select('id')
      .limit(1);
    
    if (error) {
      fs.appendFileSync(logFile, `Error checking table: ${error.message}\n`);
      console.error(`Error checking table: ${error.message}`);
      return false;
    }
    
    fs.appendFileSync(logFile, `Reconciliation table exists\n`);
    console.log('Reconciliation table exists');
    return true;
  } catch (error) {
    fs.appendFileSync(logFile, `Error checking table: ${error.message}\n`);
    console.error(`Error checking table: ${error.message}`);
    return false;
  }
}

// Function to refresh the reconciliation data
async function refreshReconcileData() {
  try {
    fs.appendFileSync(logFile, `Refreshing reconciliation data...\n`);
    console.log('Refreshing reconciliation data...');
    
    // Call the refresh function
    const { error } = await supabase.rpc('refresh_reconcile_rpro_counts_to_veeqo');
    
    if (error) {
      fs.appendFileSync(logFile, `Error refreshing data: ${error.message}\n`);
      console.error(`Error refreshing data: ${error.message}`);
      return false;
    }
    
    fs.appendFileSync(logFile, `Reconciliation data refreshed successfully\n`);
    console.log('Reconciliation data refreshed successfully');
    return true;
  } catch (error) {
    fs.appendFileSync(logFile, `Error refreshing data: ${error.message}\n`);
    console.error(`Error refreshing data: ${error.message}`);
    return false;
  }
}

// Function to get reconciliation statistics
async function getReconcileStats() {
  try {
    fs.appendFileSync(logFile, `Getting reconciliation statistics...\n`);
    console.log('Getting reconciliation statistics...');
    
    // Get total count
    const { data: totalData, error: totalError } = await supabase
      .from('reconcile_rpro_counts_to_veeqo')
      .select('id', { count: 'exact' });
    
    if (totalError) {
      fs.appendFileSync(logFile, `Error getting total count: ${totalError.message}\n`);
      console.error(`Error getting total count: ${totalError.message}`);
      return;
    }
    
    const totalCount = totalData.length;
    
    // Get count of discrepancies
    const { data: discrepancyData, error: discrepancyError } = await supabase
      .from('reconcile_rpro_counts_to_veeqo')
      .select('id', { count: 'exact' })
      .not('quantity_difference', 'eq', 0);
    
    if (discrepancyError) {
      fs.appendFileSync(logFile, `Error getting discrepancy count: ${discrepancyError.message}\n`);
      console.error(`Error getting discrepancy count: ${discrepancyError.message}`);
      return;
    }
    
    const discrepancyCount = discrepancyData.length;
    
    // Get count of RPRO having more
    const { data: rproMoreData, error: rproMoreError } = await supabase
      .from('reconcile_rpro_counts_to_veeqo')
      .select('id', { count: 'exact' })
      .gt('quantity_difference', 0);
    
    if (rproMoreError) {
      fs.appendFileSync(logFile, `Error getting RPRO more count: ${rproMoreError.message}\n`);
      console.error(`Error getting RPRO more count: ${rproMoreError.message}`);
      return;
    }
    
    const rproMoreCount = rproMoreData.length;
    
    // Get count of Veeqo having more
    const { data: veeqoMoreData, error: veeqoMoreError } = await supabase
      .from('reconcile_rpro_counts_to_veeqo')
      .select('id', { count: 'exact' })
      .lt('quantity_difference', 0);
    
    if (veeqoMoreError) {
      fs.appendFileSync(logFile, `Error getting Veeqo more count: ${veeqoMoreError.message}\n`);
      console.error(`Error getting Veeqo more count: ${veeqoMoreError.message}`);
      return;
    }
    
    const veeqoMoreCount = veeqoMoreData.length;
    
    // Log and display statistics
    const stats = {
      totalCount,
      discrepancyCount,
      rproMoreCount,
      veeqoMoreCount,
      matchingCount: totalCount - discrepancyCount
    };
    
    fs.appendFileSync(logFile, `Reconciliation statistics: ${JSON.stringify(stats, null, 2)}\n`);
    console.log('Reconciliation statistics:');
    console.log(`Total records: ${stats.totalCount}`);
    console.log(`Records with discrepancies: ${stats.discrepancyCount}`);
    console.log(`Records where RPRO has more: ${stats.rproMoreCount}`);
    console.log(`Records where Veeqo has more: ${stats.veeqoMoreCount}`);
    console.log(`Records with matching quantities: ${stats.matchingCount}`);
    
    return stats;
  } catch (error) {
    fs.appendFileSync(logFile, `Error getting statistics: ${error.message}\n`);
    console.error(`Error getting statistics: ${error.message}`);
  }
}

// Main function
async function main() {
  try {
    // Check if the table exists
    const tableExists = await checkTableExists();
    
    if (!tableExists) {
      // Execute the SQL to create the table and populate it
      const sqlExecuted = await executeSql(sqlContent);
      
      if (!sqlExecuted) {
        fs.appendFileSync(logFile, `Failed to create reconciliation table\n`);
        console.error('Failed to create reconciliation table');
        return;
      }
    } else {
      // Refresh the reconciliation data
      const dataRefreshed = await refreshReconcileData();
      
      if (!dataRefreshed) {
        fs.appendFileSync(logFile, `Failed to refresh reconciliation data\n`);
        console.error('Failed to refresh reconciliation data');
        return;
      }
    }
    
    // Get reconciliation statistics
    await getReconcileStats();
    
    fs.appendFileSync(logFile, `Reconciliation table creation/refresh completed\n`);
    console.log('Reconciliation table creation/refresh completed');
  } catch (error) {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`Unhandled error: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the main function
console.log('Starting reconciliation table creation/refresh...');
main()
  .then(() => {
    fs.appendFileSync(logFile, `Process completed at ${new Date().toISOString()}\n`);
    console.log('Process completed.');
  })
  .catch(error => {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    console.error(`Unhandled error: ${error.message}`);
  });
