require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testUpdatedMatchOslToDiscs() {
  try {
    console.log('Testing updated match_osl_to_discs task with dual mapping...');
    
    // Use OSL 18977 from the previous example
    const oslId = 18977;
    
    console.log(`\n=== BEFORE TEST - OSL ${oslId} STATUS ===`);
    
    // Check current OSL details
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, min_weight, max_weight, color_id, vendor_id')
      .eq('id', oslId)
      .single();
    
    if (oslError) {
      console.error('Error getting OSL:', oslError);
      return;
    }
    
    console.log(`OSL ${oslId}: MPS ${osl.mps_id}, Weight ${osl.min_weight}-${osl.max_weight}g, Color ${osl.color_id}`);
    
    // Check current disc mappings
    const { data: currentRegularMappings, error: regError } = await supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, color_id, sold_date')
      .eq('order_sheet_line_id', oslId);
    
    const { data: currentVendorMappings, error: vendorError } = await supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, color_id, sold_date')
      .eq('vendor_osl_id', oslId);
    
    if (!regError && !vendorError) {
      console.log(`Current regular mappings: ${currentRegularMappings.length}`);
      console.log(`Current vendor mappings: ${currentVendorMappings.length}`);
    }
    
    // Clear existing mappings for clean test
    console.log('\nClearing existing mappings for clean test...');
    await supabase
      .from('t_discs')
      .update({ order_sheet_line_id: null })
      .eq('order_sheet_line_id', oslId);
    
    await supabase
      .from('t_discs')
      .update({ vendor_osl_id: null })
      .eq('vendor_osl_id', oslId);
    
    // Create a new match_osl_to_discs task
    console.log(`\n=== CREATING TEST TASK ===`);
    const { data: taskData, error: taskError } = await supabase
      .from('t_task_queue')
      .insert([
        {
          task_type: 'match_osl_to_discs',
          payload: { id: oslId },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: 'test_dual_mapping_osl_to_discs'
        }
      ])
      .select();
    
    if (taskError) {
      console.error('Error creating test task:', taskError);
      return;
    }
    
    const testTaskId = taskData[0].id;
    console.log(`✅ Created test task ${testTaskId} for OSL ${oslId}`);
    
    console.log('\n🔄 Task created and ready for processing by the worker daemon.');
    console.log('The updated match_osl_to_discs task should now:');
    console.log('1. Find discs matching using regular weight → update order_sheet_line_id');
    console.log('2. Find discs matching using manufacturer weight → update vendor_osl_id');
    console.log('3. Report both types of matches in the task result');
    
    console.log('\n📋 To check results after processing:');
    console.log(`
SELECT 
  id, 
  status, 
  result->>'message' as message,
  result->>'discs_matched_regular' as regular_matches,
  result->>'discs_matched_vendor' as vendor_matches,
  processed_at
FROM t_task_queue 
WHERE id = ${testTaskId};
    `);
    
    console.log('\nTo see the actual disc mappings:');
    console.log(`
-- Regular mappings (order_sheet_line_id)
SELECT id, weight, weight_mfg, color_id, sold_date 
FROM t_discs 
WHERE order_sheet_line_id = ${oslId};

-- Vendor mappings (vendor_osl_id)  
SELECT id, weight, weight_mfg, color_id, sold_date 
FROM t_discs 
WHERE vendor_osl_id = ${oslId};
    `);
    
    // Show expected matches for reference
    console.log('\n=== EXPECTED MATCHES (for reference) ===');
    
    // Regular weight matches
    const { data: expectedRegular, error: expRegError } = await supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, color_id, sold_date')
      .eq('mps_id', osl.mps_id)
      .gte('weight', osl.min_weight)
      .lte('weight', osl.max_weight)
      .in('color_id', osl.color_id === 23 ? [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23] : [osl.color_id, 23])
      .is('sold_date', null)
      .limit(5);
    
    if (!expRegError) {
      console.log(`Expected regular matches: ${expectedRegular.length}`);
      expectedRegular.forEach(disc => {
        console.log(`  Disc ${disc.id}: Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id}`);
      });
    }
    
    // Vendor weight matches
    const { data: expectedVendor, error: expVendorError } = await supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, color_id, sold_date')
      .eq('mps_id', osl.mps_id)
      .not('weight_mfg', 'is', null)
      .in('color_id', osl.color_id === 23 ? [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23] : [osl.color_id, 23])
      .is('sold_date', null)
      .limit(10);
    
    if (!expVendorError) {
      const filteredVendor = expectedVendor.filter(disc => {
        const roundedWeightMfg = Math.round(disc.weight_mfg);
        return roundedWeightMfg >= osl.min_weight && roundedWeightMfg <= osl.max_weight;
      });
      
      console.log(`Expected vendor matches: ${filteredVendor.length}`);
      filteredVendor.forEach(disc => {
        const roundedWeightMfg = Math.round(disc.weight_mfg);
        console.log(`  Disc ${disc.id}: Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g (rounded: ${roundedWeightMfg}g), Color ${disc.color_id}`);
      });
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

testUpdatedMatchOslToDiscs();
