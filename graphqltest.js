import dotenv from 'dotenv';
dotenv.config();
import fetch from 'node-fetch';

const shopifyGraphQLEndpoint = process.env.SHOPIFY_GRAPHQL_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

if (!shopifyGraphQLEndpoint || !shopifyAccessToken) {
  console.error("Missing Shopify GraphQL endpoint or access token in environment variables.");
  process.exit(1);
}

// Replace with the global ID of the product you want to update.
const graphqlProductId = "gid://shopify/Product/8723341705404";

// Replace with the exact standardized product type you want to set.
const newStandardizedProductType = "Sporting Goods > Outdoor Recreation > Outdoor Games > Disc Golf";

const mutation = `
  mutation productUpdate($input: ProductInput!) {
    productUpdate(input: $input) {
      product {
        id
        title
        standardizedProductType {
          value
        }
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// By setting productType to an empty string, we remove the legacy field so that only standardizedProductType is applied.
const variables = {
  input: {
    id: graphqlProductId,
    standardizedProductType: { value: newStandardizedProductType }
  }
};

fetch(shopifyGraphQLEndpoint, {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    "X-Shopify-Access-Token": shopifyAccessToken
  },
  body: JSON.stringify({ query: mutation, variables })
})
  .then(response => response.json())
  .then(result => {
    console.log("DEBUG: Full GraphQL update response:", JSON.stringify(result, null, 2));
    if (result.errors) {
      console.error("GraphQL top-level errors:", result.errors);
    } else if (
      result.data &&
      result.data.productUpdate &&
      result.data.productUpdate.userErrors &&
      result.data.productUpdate.userErrors.length > 0
    ) {
      console.warn("WARNING: Category update returned user errors:", result.data.productUpdate.userErrors);
    } else if (result.data && result.data.productUpdate && result.data.productUpdate.product) {
      console.log("INFO: Successfully updated product category:", result.data.productUpdate.product.standardizedProductType.value);
    } else {
      console.error("Unexpected GraphQL response structure:", result);
    }
  })
  .catch(err => {
    console.error("Error updating product category:", err);
  });
