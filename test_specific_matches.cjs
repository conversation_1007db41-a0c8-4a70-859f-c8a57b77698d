require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testSpecificMatches() {
  try {
    console.log('Testing specific discs that should have vendor OSL matches...');
    
    // Test the specific discs we found
    const testCases = [
      { id: 424349, weight_mfg: 165, expected_osl: 1244 },
      { id: 421561, weight_mfg: 165, expected_osl: 18483 },
      { id: 423030, weight_mfg: 175, expected_osl: 17275 },
      { id: 424366, weight_mfg: 175, expected_osl: 1446 },
      { id: 423053, weight_mfg: 175, expected_osl: 17275 }
    ];
    
    for (const testCase of testCases) {
      console.log(`\n=== Testing Disc ${testCase.id} ===`);
      
      // Get the full disc record
      const { data: disc, error: discError } = await supabase
        .from('t_discs')
        .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
        .eq('id', testCase.id)
        .single();
      
      if (discError) {
        console.error(`Error getting disc ${testCase.id}:`, discError);
        continue;
      }
      
      console.log(`Disc details: MPS ${disc.mps_id}, weight ${disc.weight}g, weight_mfg ${disc.weight_mfg}g, color ${disc.color_id}`);
      console.log(`Current mappings: order_sheet_line_id=${disc.order_sheet_line_id}, vendor_osl_id=${disc.vendor_osl_id}`);
      
      // Test our function
      console.log('Testing find_matching_osl_by_mfg_weight function...');
      const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
        'find_matching_osl_by_mfg_weight',
        {
          mps_id_param: disc.mps_id,
          color_id_param: disc.color_id,
          weight_mfg_param: disc.weight_mfg
        }
      );
      
      if (vendorOslError) {
        console.error('Error calling function:', vendorOslError);
        continue;
      }
      
      console.log('Function result:', vendorOslData);
      
      const foundOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
      
      if (foundOslId) {
        console.log(`✅ Function found OSL: ${foundOslId}`);
        
        if (foundOslId === testCase.expected_osl) {
          console.log(`🎯 PERFECT! Matches expected OSL ${testCase.expected_osl}`);
        } else {
          console.log(`⚠️ Different from expected OSL ${testCase.expected_osl}`);
        }
        
        // Update the disc
        if (!disc.vendor_osl_id) {
          console.log('Updating disc with vendor_osl_id...');
          const { error: updateError } = await supabase
            .from('t_discs')
            .update({ vendor_osl_id: foundOslId })
            .eq('id', disc.id);
          
          if (updateError) {
            console.error('Error updating disc:', updateError);
          } else {
            console.log(`✅ Successfully updated disc ${disc.id} with vendor_osl_id: ${foundOslId}`);
          }
        } else {
          console.log(`Disc already has vendor_osl_id: ${disc.vendor_osl_id}`);
        }
      } else {
        console.log(`❌ Function returned no match`);
        
        // Let's debug why
        console.log('Debugging: checking OSL details...');
        const { data: oslDetails, error: oslError } = await supabase
          .from('t_order_sheet_lines')
          .select('id, min_weight, max_weight, color_id, mps_id')
          .eq('id', testCase.expected_osl)
          .single();
        
        if (!oslError && oslDetails) {
          console.log(`Expected OSL ${testCase.expected_osl} details:`, oslDetails);
          
          const roundedWeight = Math.round(disc.weight_mfg);
          const mpsMatch = oslDetails.mps_id === disc.mps_id;
          const colorMatch = oslDetails.color_id === disc.color_id || oslDetails.color_id === 23;
          const weightMatch = roundedWeight >= oslDetails.min_weight && roundedWeight <= oslDetails.max_weight;
          
          console.log(`Matching criteria:`);
          console.log(`  MPS match (${oslDetails.mps_id} === ${disc.mps_id}): ${mpsMatch}`);
          console.log(`  Color match (${oslDetails.color_id} === ${disc.color_id} OR ${oslDetails.color_id} === 23): ${colorMatch}`);
          console.log(`  Weight match (${roundedWeight} >= ${oslDetails.min_weight} AND ${roundedWeight} <= ${oslDetails.max_weight}): ${weightMatch}`);
        }
      }
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

testSpecificMatches();
