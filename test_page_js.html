<!DOCTYPE html>
<html>
<head>
    <title>Test Page JavaScript</title>
</head>
<body>
    <h1>Testing Admin Page JavaScript</h1>
    
    <div>
        <button onclick="testAPI()">Test API Calls</button>
        <div id="output"></div>
    </div>

    <script>
        async function testAPI() {
            const output = document.getElementById('output');
            output.innerHTML = 'Testing...<br>';
            
            try {
                // Test worker status
                const response = await fetch('/api/worker/status');
                const data = await response.json();
                
                output.innerHTML += `✅ Worker Status: ${data.status}<br>`;
                output.innerHTML += `✅ Pending Tasks: ${data.pendingTasksCount}<br>`;
                output.innerHTML += `✅ Future Tasks: ${data.futureTasksCount}<br>`;
                
                // Test tasks by type
                const response2 = await fetch('/api/tasks/by-type');
                const data2 = await response2.json();
                
                output.innerHTML += `✅ Tasks by type loaded<br>`;
                output.innerHTML += `✅ API calls working correctly!<br>`;
                
            } catch (error) {
                output.innerHTML += `❌ Error: ${error.message}<br>`;
            }
        }
        
        console.log('Test page JavaScript loaded successfully');
    </script>
</body>
</html>
