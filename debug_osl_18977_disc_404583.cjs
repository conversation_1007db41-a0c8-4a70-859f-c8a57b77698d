require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function debugOsl18977Disc404583() {
  try {
    console.log('Debugging why OSL 18977 should match disc 404583...');
    
    // Get OSL details
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, min_weight, max_weight, color_id, vendor_id')
      .eq('id', 18977)
      .single();
    
    if (oslError) {
      console.error('Error getting OSL:', oslError);
      return;
    }
    
    console.log('\n=== OSL 18977 DETAILS ===');
    console.log(`MPS ID: ${osl.mps_id}`);
    console.log(`Weight Range: ${osl.min_weight}-${osl.max_weight}g`);
    console.log(`Color ID: ${osl.color_id}`);
    console.log(`Vendor ID: ${osl.vendor_id}`);
    
    // Get disc details
    const { data: disc, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .eq('id', 404583)
      .single();
    
    if (discError) {
      console.error('Error getting disc:', discError);
      return;
    }
    
    console.log('\n=== DISC 404583 DETAILS ===');
    console.log(`MPS ID: ${disc.mps_id}`);
    console.log(`Weight: ${disc.weight}g`);
    console.log(`Weight MFG: ${disc.weight_mfg}g`);
    console.log(`Color ID: ${disc.color_id}`);
    console.log(`Sold Date: ${disc.sold_date || 'NULL (unsold)'}`);
    console.log(`Current order_sheet_line_id: ${disc.order_sheet_line_id}`);
    console.log(`Current vendor_osl_id: ${disc.vendor_osl_id}`);
    
    // Check matching criteria
    console.log('\n=== MATCHING CRITERIA ANALYSIS ===');
    
    const mpsMatch = disc.mps_id === osl.mps_id;
    console.log(`1. MPS ID match (${disc.mps_id} === ${osl.mps_id}): ${mpsMatch ? '✅' : '❌'}`);
    
    const colorMatch = disc.color_id === osl.color_id || osl.color_id === 23;
    console.log(`2. Color match (${disc.color_id} === ${osl.color_id} OR ${osl.color_id} === 23): ${colorMatch ? '✅' : '❌'}`);
    
    const soldMatch = disc.sold_date === null;
    console.log(`3. Unsold disc (sold_date IS NULL): ${soldMatch ? '✅' : '❌'}`);
    
    // Regular weight matching
    const regularWeightMatch = disc.weight >= osl.min_weight && disc.weight <= osl.max_weight;
    console.log(`4. Regular weight match (${disc.weight} >= ${osl.min_weight} AND <= ${osl.max_weight}): ${regularWeightMatch ? '✅' : '❌'}`);
    
    // Manufacturer weight matching
    if (disc.weight_mfg !== null) {
      const roundedWeightMfg = Math.round(disc.weight_mfg);
      const vendorWeightMatch = roundedWeightMfg >= osl.min_weight && roundedWeightMfg <= osl.max_weight;
      console.log(`5. Vendor weight match (ROUND(${disc.weight_mfg}) = ${roundedWeightMfg} >= ${osl.min_weight} AND <= ${osl.max_weight}): ${vendorWeightMatch ? '✅' : '❌'}`);
      
      const regularAllMatch = mpsMatch && colorMatch && soldMatch && regularWeightMatch;
      const vendorAllMatch = mpsMatch && colorMatch && soldMatch && vendorWeightMatch;
      
      console.log(`\nREGULAR MAPPING MATCH: ${regularAllMatch ? '✅' : '❌'}`);
      console.log(`VENDOR MAPPING MATCH: ${vendorAllMatch ? '✅' : '❌'}`);
      
      if (regularAllMatch) {
        console.log('\n🎯 This disc SHOULD be matched for regular OSL mapping (order_sheet_line_id)!');
      }
      
      if (vendorAllMatch) {
        console.log('\n🎯 This disc SHOULD be matched for vendor OSL mapping (vendor_osl_id)!');
      }
      
      if (!regularAllMatch && !vendorAllMatch) {
        console.log('\n❌ This disc should NOT match - criteria not met.');
        
        if (!mpsMatch) console.log('   - MPS mismatch');
        if (!colorMatch) console.log('   - Color mismatch');
        if (!soldMatch) console.log('   - Disc is sold');
        if (!regularWeightMatch) console.log('   - Regular weight outside range');
        if (!vendorWeightMatch) console.log('   - Vendor weight outside range');
      }
    } else {
      console.log('5. Weight MFG is null - cannot do vendor weight matching');
      
      const regularAllMatch = mpsMatch && colorMatch && soldMatch && regularWeightMatch;
      console.log(`\nREGULAR MAPPING MATCH: ${regularAllMatch ? '✅' : '❌'}`);
      
      if (regularAllMatch) {
        console.log('\n🎯 This disc SHOULD be matched for regular OSL mapping (order_sheet_line_id)!');
      }
    }
    
    // Test the queries that the match_osl_to_discs task would use
    console.log('\n=== TESTING TASK QUERIES ===');
    
    // Test regular weight query
    console.log('Testing regular weight query...');
    let regularQuery = supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
      .eq('mps_id', osl.mps_id)
      .gte('weight', osl.min_weight)
      .lte('weight', osl.max_weight)
      .is('sold_date', null);
    
    if (osl.color_id !== 23) {
      regularQuery = regularQuery.eq('color_id', osl.color_id);
    }
    
    const { data: regularQueryResult, error: regularQueryError } = await regularQuery.eq('id', 404583);
    
    if (regularQueryError) {
      console.log('❌ Regular query error:', regularQueryError.message);
    } else {
      console.log(`✅ Regular query result: ${regularQueryResult.length} discs found`);
      if (regularQueryResult.length > 0) {
        console.log('   Found disc 404583 in regular query!');
      }
    }
    
    // Test vendor weight query
    console.log('\nTesting vendor weight query...');
    let vendorQuery = supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
      .eq('mps_id', osl.mps_id)
      .not('weight_mfg', 'is', null)
      .is('sold_date', null);
    
    if (osl.color_id !== 23) {
      vendorQuery = vendorQuery.eq('color_id', osl.color_id);
    }
    
    const { data: vendorQueryResult, error: vendorQueryError } = await vendorQuery.eq('id', 404583);
    
    if (vendorQueryError) {
      console.log('❌ Vendor query error:', vendorQueryError.message);
    } else {
      console.log(`✅ Vendor query result: ${vendorQueryResult.length} discs found`);
      if (vendorQueryResult.length > 0) {
        const disc = vendorQueryResult[0];
        const roundedWeightMfg = Math.round(disc.weight_mfg);
        const inRange = roundedWeightMfg >= osl.min_weight && roundedWeightMfg <= osl.max_weight;
        console.log(`   Found disc 404583 in vendor query! Weight MFG ${disc.weight_mfg}g (rounded: ${roundedWeightMfg}g), in range: ${inRange}`);
      }
    }
    
    // Check the task result for task 305298
    console.log('\n=== CHECKING TASK 305298 RESULT ===');
    const { data: taskResult, error: taskError } = await supabase
      .from('t_task_queue')
      .select('id, status, result, processed_at')
      .eq('id', 305298)
      .single();
    
    if (taskError) {
      console.log('❌ Error getting task result:', taskError.message);
    } else {
      console.log(`Task 305298 status: ${taskResult.status}`);
      if (taskResult.result) {
        console.log('Task result:', taskResult.result);
      }
      if (taskResult.processed_at) {
        console.log(`Processed at: ${taskResult.processed_at}`);
      } else {
        console.log('Task not yet processed');
      }
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

debugOsl18977Disc404583();
