import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkTotalRecords() {
  try {
    console.log('🔍 Checking total records in database...\n');
    
    // Get total count
    const { count, error: countError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('Error getting count:', countError);
      return;
    }
    
    console.log(`📊 Total records in database: ${count}`);
    
    // Get records after line 332 (Excel row 333+)
    const { data: afterLine332, error: afterError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('excel_row_hint, raw_line_type, raw_model, plastic_name, mold_name')
      .gte('excel_row_hint', 333)
      .order('excel_row_hint')
      .limit(20);
    
    if (afterError) {
      console.error('Error getting records after line 332:', afterError);
      return;
    }
    
    console.log(`\n📋 Records after line 332 (first 20):`);
    if (afterLine332.length === 0) {
      console.log('   ❌ NO RECORDS FOUND after line 332!');
    } else {
      console.log(`   ✅ Found ${afterLine332.length} records (showing first 20):`);
      afterLine332.forEach((record, index) => {
        console.log(`   ${index + 1}. Row ${record.excel_row_hint}: ${record.raw_line_type} | ${record.raw_model} → ${record.plastic_name} | ${record.mold_name}`);
      });
    }
    
    // Check the highest row number
    const { data: maxRow, error: maxError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('excel_row_hint')
      .order('excel_row_hint', { ascending: false })
      .limit(1);
    
    if (maxError) {
      console.error('Error getting max row:', maxError);
    } else if (maxRow.length > 0) {
      console.log(`\n📈 Highest Excel row number in database: ${maxRow[0].excel_row_hint}`);
    }
    
    // Check records around line 332
    const { data: aroundLine332, error: aroundError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('excel_row_hint, raw_line_type, raw_model, plastic_name, mold_name')
      .gte('excel_row_hint', 325)
      .lte('excel_row_hint', 340)
      .order('excel_row_hint');
    
    if (aroundError) {
      console.error('Error getting records around line 332:', aroundError);
    } else {
      console.log(`\n📋 Records around line 332 (rows 325-340):`);
      if (aroundLine332.length === 0) {
        console.log('   ❌ NO RECORDS FOUND in this range!');
      } else {
        aroundLine332.forEach((record, index) => {
          console.log(`   ${index + 1}. Row ${record.excel_row_hint}: ${record.raw_line_type} | ${record.raw_model} → ${record.plastic_name} | ${record.mold_name}`);
        });
      }
    }
    
    console.log('\n🎉 Check completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkTotalRecords().catch(console.error);
