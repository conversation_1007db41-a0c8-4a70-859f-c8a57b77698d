import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkMcBethNewProducts() {
  try {
    console.log('🔍 Checking McBeth NEW products...\n');
    
    // Get McBeth NEW products
    const { data: mcbethNewProducts, error } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .eq('raw_line_type', 'McBeth')
      .ilike('raw_model', '%NEW -%')
      .limit(10);
    
    if (error) {
      console.error('Error querying McBeth NEW products:', error);
      return;
    }
    
    if (mcbethNewProducts.length === 0) {
      console.log('⚪ No McBeth NEW products found');
      return;
    }
    
    console.log(`📋 Found ${mcbethNewProducts.length} McBeth NEW products:\n`);
    
    mcbethNewProducts.forEach((product, index) => {
      console.log(`${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
      console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
      console.log(`   Vendor Description: "${product.vendor_description || 'NULL/EMPTY'}"`);
      
      // Check if vendor description contains the white/blank pattern
      if (product.vendor_description && product.vendor_description.toLowerCase().includes('white/blank')) {
        console.log(`   🎯 SHOULD BE: ESP | ${product.mold_name} | Dye Line Blank Top Bottom`);
      }
      console.log('');
    });
    
    // Also check Pierce Fierce products (non-Hard)
    console.log('🔍 Checking Pierce Fierce products (non-Hard)...\n');
    
    const { data: pierceFierceProducts, error: pierceError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .eq('raw_line_type', 'Pierce')
      .ilike('raw_model', '%Fierce%')
      .not('raw_model', 'ilike', '%Hard%')
      .limit(5);
    
    if (pierceError) {
      console.error('Error querying Pierce Fierce products:', pierceError);
    } else if (pierceFierceProducts.length > 0) {
      console.log(`📋 Found ${pierceFierceProducts.length} Pierce Fierce (non-Hard) products:\n`);
      
      pierceFierceProducts.forEach((product, index) => {
        console.log(`${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
        console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        console.log(`   Expected: Swirl | Fierce | PP Logo Stock Stamp`);
        console.log(`   Vendor Description: "${product.vendor_description || 'NULL/EMPTY'}"`);
        console.log('');
      });
    } else {
      console.log('⚪ No Pierce Fierce (non-Hard) products found');
    }
    
    console.log('🎉 Check completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkMcBethNewProducts().catch(console.error);
