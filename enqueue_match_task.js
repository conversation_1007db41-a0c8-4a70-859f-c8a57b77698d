import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with your actual credentials
const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM3ODY1NDEsImV4cCI6MjA0OTM2MjU0MX0.MtBXMZt7rCqXd6c9clhrNoVtfOxEilX2C8oboMceOGg';
const supabase = createClient(supabaseUrl, supabaseKey);

const enqueueMatchTask = async () => {
  try {
    console.log('Attempting to enqueue a match_disc_to_osl task for disc 421349...');
    
    // Create a task in the task queue
    const { data, error } = await supabase
      .from('t_task_queue')
      .insert([
        {
          task_type: 'match_disc_to_osl',
          payload: { id: 421349, operation: 'UPDATE' },
          status: 'pending',
          scheduled_at: new Date(),
          created_at: new Date()
        }
      ])
      .select();
      
    if (error) {
      console.error('Error enqueueing task:', error);
      return;
    }
    
    console.log('Task enqueued successfully:', data);
    
    // Check if the task was created
    if (data && data.length > 0) {
      const taskId = data[0].id;
      console.log(`Task ID: ${taskId}`);
      
      // Wait a moment and then check the task status
      console.log('Waiting 5 seconds to check task status...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      const { data: taskData, error: taskError } = await supabase
        .from('t_task_queue')
        .select('*')
        .eq('id', taskId)
        .single();
        
      if (taskError) {
        console.error('Error checking task status:', taskError);
        return;
      }
      
      console.log('Task status:', taskData.status);
      console.log('Task result:', taskData.result);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
};

enqueueMatchTask();
