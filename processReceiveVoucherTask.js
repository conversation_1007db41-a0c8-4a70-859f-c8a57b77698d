// processReceiveVoucherTask.js
// Handle receive_voucher task: mark voucher Complete; DB trigger will create movements

export default async function processReceiveVoucherTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processReceiveVoucherTask] Processing task ${task.id}`);

  try {
    // Parse payload
    const payload = typeof task.payload === 'string' ? JSON.parse(task.payload) : (task.payload || {});
    const voucherId = payload.voucher_id ?? payload.id;

    if (!voucherId) {
      throw new Error('Missing voucher_id in payload');
    }

    await updateTaskStatus(task.id, 'processing');

    // Fetch voucher header
    const { data: voucher, error: voucherErr } = await supabase
      .from('t_inventory_vouchers')
      .select('id, voucher_date, notes, status')
      .eq('id', voucherId)
      .maybeSingle();

    if (voucherErr) {
      throw new Error(`Error fetching voucher ${voucherId}: ${voucherErr.message}`);
    }
    if (!voucher) {
      throw new Error(`Voucher ${voucherId} not found`);
    }

    // Mark voucher Complete first (to let any DB triggers run synchronously), then we'll replace movements idempotently
    {
      const { error: updErr } = await supabase
        .from('t_inventory_vouchers')
        .update({ status: 'Complete' })
        .eq('id', voucherId);
      if (updErr) {
        throw new Error(`Failed to set voucher ${voucherId} status to Complete: ${updErr.message}`);
      }
    }

    // Movements are created by DB trigger on voucher status change to 'Complete'
    const inserted = null;

    await updateTaskStatus(task.id, 'completed', {
      message: `Voucher ${voucherId} marked Complete; movements handled by DB trigger`,
      voucher_id: voucherId,
    });
  } catch (err) {
    console.error('[processReceiveVoucherTask] Error:', err);
    await logError(err.message, 'processReceiveVoucherTask');
    await updateTaskStatus(task.id, 'error', { message: err.message });
  }
}

