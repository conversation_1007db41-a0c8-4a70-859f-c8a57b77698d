// check_db_functions.js
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDatabaseFunctions() {
  try {
    console.log('Checking database functions...');
    
    // List all functions in the database
    const { data, error } = await supabase.rpc('list_functions');
    
    if (error) {
      console.error('Error listing functions:', error);
      return;
    }
    
    console.log('Available functions:');
    console.log(data);
    
    // Try to call the specific functions
    try {
      const { data: pendingTasks, error: pendingError } = await supabase.rpc('get_pending_tasks_by_type');
      
      if (pendingError) {
        console.error('Error calling get_pending_tasks_by_type:', pendingError);
      } else {
        console.log('get_pending_tasks_by_type works!', pendingTasks);
      }
    } catch (err) {
      console.error('Exception calling get_pending_tasks_by_type:', err);
    }
    
  } catch (err) {
    console.error('Exception checking database functions:', err);
  }
}

checkDatabaseFunctions();
