// createRproTable.js - <PERSON>ript to create the imported_table_rpro table

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

// Create a log file
const logFile = 'create_rpro_table.log';
fs.writeFileSync(logFile, `Starting table creation at ${new Date().toISOString()}\n`);

// Load environment variables
dotenv.config();
fs.appendFileSync(logFile, `Environment variables loaded\n`);

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  const errorMsg = 'Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file';
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

fs.appendFileSync(logFile, `Supabase credentials found\n`);
const supabase = createClient(supabaseUrl, supabaseKey);
fs.appendFileSync(logFile, `Supabase client created\n`);

// SQL to create the table with proper column names
const createTableSQL = `
CREATE TABLE IF NOT EXISTS public.imported_table_rpro (
  id SERIAL PRIMARY KEY,
  ivno INTEGER,
  ivalu VARCHAR(255),
  ivvc VARCHAR(50),
  ivdcs VARCHAR(50),
  ivdesc2 VARCHAR(255),
  ivdesc1 VARCHAR(255),
  ivattr VARCHAR(255),
  ivsize VARCHAR(255),
  ivupc VARCHAR(50),
  ivkityp INTEGER,
  ivdesc3 VARCHAR(255),
  ivqohcm NUMERIC(10,2),
  ivqsacm NUMERIC(10,2),
  ivqsalaw NUMERIC(10,2),
  ivqsatop NUMERIC(10,2),
  ivdesc4 VARCHAR(255),
  ivapd NUMERIC(10,2),
  ivavgcd NUMERIC(10,3),
  ivudnam VARCHAR(255),
  ivaux3 VARCHAR(255),
  ivaux4 VARCHAR(255),
  ivqtylaw NUMERIC(10,2),
  ivqtytop NUMERIC(10,2),
  ivisid VARCHAR(50),
  ivssid VARCHAR(50),
  ivprcdzlis NUMERIC(10,2),
  ivprcdz_dollar NUMERIC(10,2),
  ivprcdzsal NUMERIC(10,2),
  ivprcdzliv NUMERIC(10,2),
  ivprcbtlis NUMERIC(10,2),
  ivprcbt_dollar NUMERIC(10,2),
  ivprcbtsal NUMERIC(10,2),
  ivprcbtliv NUMERIC(10,2),
  ivprcpplis NUMERIC(10,2),
  ivprcpp_dollar NUMERIC(10,2),
  ivprcppsal NUMERIC(10,2),
  ivprcppliv NUMERIC(10,2),
  ivprcmsrp NUMERIC(10,2),
  ivprcmap NUMERIC(10,2),
  ivprccase NUMERIC(10,2),
  ivprcws_1 NUMERIC(10,2),
  ivprcws_2 NUMERIC(10,2),
  ivldr DATE,
  ivaux2 VARCHAR(255),
  ivaux5 VARCHAR(50),
  ivaux6 VARCHAR(255),
  ivlstcd NUMERIC(10,2),
  ivrpnlaw NUMERIC(10,2),
  ivrpxlaw NUMERIC(10,2),
  ivaux1 VARCHAR(255),
  ivaux8 VARCHAR(255),
  ivmsc4 INTEGER,
  ivdisdt DATE,
  ivmsc1 INTEGER,
  ivilmdt DATE,
  ivilmtm VARCHAR(50),
  ivmsc3 INTEGER,
  ivuddt DATE,
  ivvnld INTEGER,
  ivvnlcd INTEGER,
  ivmsc2 INTEGER,
  imported_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  import_batch_id UUID NOT NULL DEFAULT gen_random_uuid()
);

-- Create the truncate_table function if it doesn't exist
CREATE OR REPLACE FUNCTION public.truncate_table(table_name text)
RETURNS void AS $$
BEGIN
  EXECUTE 'TRUNCATE TABLE ' || quote_ident(table_name) || ' RESTART IDENTITY CASCADE';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
`;

// Function to create the table
async function createTable() {
  try {
    fs.appendFileSync(logFile, `Attempting to create table...\n`);
    console.log('Attempting to create table...');
    
    // Execute the SQL directly
    const { error } = await supabase.rpc('exec_sql', { sql_query: createTableSQL });
    
    if (error) {
      fs.appendFileSync(logFile, `Error executing SQL: ${error.message}\n`);
      console.error(`Error executing SQL: ${error.message}`);
      
      // Try to create the exec_sql function first
      fs.appendFileSync(logFile, `Trying to create exec_sql function first...\n`);
      console.log('Trying to create exec_sql function first...');
      
      const createFunctionSQL = `
      CREATE OR REPLACE FUNCTION public.exec_sql(sql_query text)
      RETURNS void AS $$
      BEGIN
        EXECUTE sql_query;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
      `;
      
      // Try to execute a simple query to check connection
      const { data: testData, error: testError } = await supabase
        .from('_test_connection')
        .select('*')
        .limit(1);
      
      if (testError) {
        fs.appendFileSync(logFile, `Test query error: ${testError.message}\n`);
        console.error(`Test query error: ${testError.message}`);
      } else {
        fs.appendFileSync(logFile, `Test query successful\n`);
        console.log('Test query successful');
      }
      
      // Try to create the table using the REST API
      fs.appendFileSync(logFile, `Trying to create table using SQL Editor...\n`);
      console.log('Trying to create table using SQL Editor...');
      
      fs.appendFileSync(logFile, `Please run the following SQL in the Supabase SQL Editor:\n\n${createTableSQL}\n`);
      console.log('Please run the following SQL in the Supabase SQL Editor:');
      console.log(createTableSQL);
      
      return false;
    }
    
    fs.appendFileSync(logFile, `Table created successfully\n`);
    console.log('Table created successfully');
    return true;
  } catch (error) {
    fs.appendFileSync(logFile, `Error creating table: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`Error creating table: ${error.message}`);
    console.error(error.stack);
    return false;
  }
}

// Function to check if the table exists
async function checkTableExists() {
  try {
    fs.appendFileSync(logFile, `Checking if table exists...\n`);
    console.log('Checking if table exists...');
    
    // Try to select from the table
    const { data, error } = await supabase
      .from('imported_table_rpro')
      .select('id')
      .limit(1);
    
    if (error) {
      fs.appendFileSync(logFile, `Error checking table: ${error.message}\n`);
      console.error(`Error checking table: ${error.message}`);
      return false;
    }
    
    fs.appendFileSync(logFile, `Table exists\n`);
    console.log('Table exists');
    return true;
  } catch (error) {
    fs.appendFileSync(logFile, `Error checking table: ${error.message}\n`);
    console.error(`Error checking table: ${error.message}`);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Check if the table exists
    const tableExists = await checkTableExists();
    
    if (!tableExists) {
      // Create the table
      const tableCreated = await createTable();
      
      if (!tableCreated) {
        fs.appendFileSync(logFile, `Failed to create table\n`);
        console.error('Failed to create table');
        return;
      }
    }
    
    fs.appendFileSync(logFile, `Table check/creation completed\n`);
    console.log('Table check/creation completed');
  } catch (error) {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`Unhandled error: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the main function
console.log('Starting table check/creation...');
main()
  .then(() => {
    fs.appendFileSync(logFile, `Process completed at ${new Date().toISOString()}\n`);
    console.log('Process completed.');
  })
  .catch(error => {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    console.error(`Unhandled error: ${error.message}`);
  });
