// processDiscEntryRecords.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function processRecords() {
  try {
    // Step 1: Synchronize t_discs.id sequence to prevent duplicate key errors
    // Get the maximum id currently in t_discs.
    const { data: maxData, error: maxError } = await supabase
      .from('t_discs')
      .select('id')
      .order('id', { ascending: false })
      .limit(1);
    if (maxError) throw maxError;

    const maxId = maxData && maxData.length > 0 ? maxData[0].id : 0;
    const newSequenceValue = Math.max(maxId, 0) + 1;

    // Sequence update section is commented out for now.
    // If you have an RPC function to update the sequence, you can call it as follows:
    //
    // const { error: seqError } = await supabase.rpc('sync_t_discs_sequence', { new_val: newSequenceValue });
    // if (seqError) throw seqError;
    //
    // Otherwise, ensure the sequence is handled by another mechanism.

    // Step 2: Insert up to 50 records from t_discs_entry into t_discs, ordered by id
    const { data: entries, error: entriesError } = await supabase
      .from('t_discs_entry')
      .select('*')
      .is('new_t_discs_id', null)
      .order('id', { ascending: true })
      .limit(50);
    if (entriesError) throw entriesError;

    for (const rec of entries) {
      // Insert record into t_discs and return the new id
      const { data: insertData, error: insertError } = await supabase
        .from('t_discs')
        .insert({
          shipment_id: rec.shipment_id,
          mps_id: rec.mps_id,
          color_modifier: rec.color_modifier,
          color_id: rec.color_id,
          weight: rec.weight,
          location: rec.location,
          description: rec.description,
          created_by: rec.created_by,
          grade: rec.grade // New field added here
        })
        .select('id'); // Return the inserted id
      if (insertError) {
        console.error(`Error inserting record for t_discs_entry id: ${rec.id}`, insertError);
        continue;
      }
      const new_t_discs_id_value = insertData[0].id;

      // Update the corresponding t_discs_entry record with the new t_discs id
      const { error: updateError } = await supabase
        .from('t_discs_entry')
        .update({ new_t_discs_id: new_t_discs_id_value })
        .eq('id', rec.id);
      if (updateError) {
        console.error(`Error updating t_discs_entry id: ${rec.id}`, updateError);
      } else {
        console.log(`Processed t_discs_entry id=${rec.id}, new t_discs id=${new_t_discs_id_value}`);
      }
    }
  } catch (err) {
    console.error('Processing failed:', err);
  }
}

// Run the function
processRecords();
