// cleanupOldShopifyCollections.js
// Script to find and clean up old Shopify collections for MPS records

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY; // Using the service role key
const supabase = createClient(supabaseUrl, supabaseKey);

// Shopify credentials
const shopifyDomain = 'dzdiscs-new-releases.myshopify.com';
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

// Function to find MPS records with specific notes
async function findOldMpsRecords() {
  // Direct query using Supabase query builder
  const { data, error } = await supabase
    .from('t_mps')
    .select('id, plastic_id, mold_id, stamp_id, vendor_status, shopify_collection_uploaded_at, notes')
    .eq('notes', 'could not find on Shopify on 5/5/2025 - g')
    .order('id', { ascending: true });

  if (error) {
    console.error('Error executing SQL query:', error);
    return [];
  }

  console.log(`Found ${data.length} MPS records with note 'could not find on Shopify on 5/5/2025 - g'`);
  return data;
}

// Function to get MPS details including g_handle
async function getMpsDetails(mpsId) {
  const { data, error } = await supabase
    .from('t_mps')
    .select('id, g_handle')
    .eq('id', mpsId)
    .single();

  if (error) {
    console.error(`Error fetching MPS details for ID ${mpsId}:`, error);
    return null;
  }

  return data;
}

// Function to check if a Shopify collection exists and if it has products
async function checkShopifyCollection(handle) {
  try {
    // First, check if the collection exists
    const collectionResponse = await fetch(
      `https://${shopifyDomain}/admin/api/2024-01/smart_collections.json?handle=${handle}`,
      {
        headers: {
          'X-Shopify-Access-Token': shopifyAccessToken,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!collectionResponse.ok) {
      console.error(`Error checking collection: ${collectionResponse.status} ${collectionResponse.statusText}`);
      return { exists: false, isEmpty: true, collectionId: null };
    }

    const collectionData = await collectionResponse.json();

    if (!collectionData.smart_collections || collectionData.smart_collections.length === 0) {
      return { exists: false, isEmpty: true, collectionId: null };
    }

    const collection = collectionData.smart_collections[0];
    const collectionId = collection.id;

    // Now check if the collection has products
    const productsResponse = await fetch(
      `https://${shopifyDomain}/admin/api/2024-01/products.json?collection_id=${collectionId}&limit=1`,
      {
        headers: {
          'X-Shopify-Access-Token': shopifyAccessToken,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!productsResponse.ok) {
      console.error(`Error checking products: ${productsResponse.status} ${productsResponse.statusText}`);
      return { exists: true, isEmpty: true, collectionId }; // Assume empty if we can't check
    }

    const productsData = await productsResponse.json();
    const isEmpty = !productsData.products || productsData.products.length === 0;

    return { exists: true, isEmpty, collectionId };
  } catch (error) {
    console.error(`Error checking Shopify collection:`, error);
    return { exists: false, isEmpty: true, collectionId: null };
  }
}

// Function to delete a Shopify collection
async function deleteShopifyCollection(collectionId) {
  try {
    const response = await fetch(
      `https://${shopifyDomain}/admin/api/2024-01/smart_collections/${collectionId}.json`,
      {
        method: 'DELETE',
        headers: {
          'X-Shopify-Access-Token': shopifyAccessToken,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      console.error(`Error deleting collection: ${response.status} ${response.statusText}`);
      return false;
    }

    return true;
  } catch (error) {
    console.error(`Error deleting Shopify collection:`, error);
    return false;
  }
}

// Function to update an MPS record
async function updateMpsRecord(mpsId, updates) {
  const { error } = await supabase
    .from('t_mps')
    .update(updates)
    .eq('id', mpsId);

  if (error) {
    console.error(`Error updating MPS record ${mpsId}:`, error);
    return false;
  }

  return true;
}

// Main function to process all records
async function processOldMpsRecords() {
  const records = await findOldMpsRecords();

  if (records.length === 0) {
    console.log('No records to process');
    return;
  }

  console.log(`Processing ${records.length} records...`);

  for (const record of records) {
    console.log(`\nProcessing MPS ID: ${record.id}`);

    // Get MPS details including g_handle
    const mpsDetails = await getMpsDetails(record.id);
    if (!mpsDetails) {
      console.log(`Could not get details for MPS ID ${record.id}, skipping`);
      continue;
    }

    // Use the g_handle from the database
    const handle = mpsDetails.g_handle;
    if (!handle) {
      console.log(`No g_handle found for MPS ID ${record.id}, skipping`);
      continue;
    }

    console.log(`Checking Shopify collection with handle: ${handle}`);

    // Check if collection exists and if it's empty
    const { exists, isEmpty, collectionId } = await checkShopifyCollection(handle);

    let updates = {
      ready_button: false
    };

    if (!exists) {
      console.log(`Collection does not exist on Shopify`);
      updates.notes = `could not find on Shopify on 5/5/2025 - g`;
      updates.shopify_collection_uploaded_at = null;
    } else if (isEmpty) {
      console.log(`Collection exists but is empty, deleting...`);
      const deleted = await deleteShopifyCollection(collectionId);

      if (deleted) {
        console.log(`Successfully deleted collection ${collectionId}`);
        updates.notes = `auto deleted from Shopify on 5/5/2025 - g`;
        updates.shopify_collection_uploaded_at = null;
      } else {
        console.log(`Failed to delete collection ${collectionId}`);
        updates.notes = `failed to delete from Shopify on 5/5/2025 - g`;
      }
    } else {
      console.log(`Collection exists and has products, not deleting`);
      updates.notes = `collection not empty on Shopifiy on 5/5/2025 - g`;
      // Keep shopify_collection_uploaded_at as is
    }

    // Update the MPS record
    console.log(`Updating MPS record ${record.id} with:`, updates);
    const updated = await updateMpsRecord(record.id, updates);

    if (updated) {
      console.log(`Successfully updated MPS record ${record.id}`);
    } else {
      console.log(`Failed to update MPS record ${record.id}`);
    }
  }

  console.log('\nProcessing complete!');
}

// Run the script
processOldMpsRecords().catch(error => {
  console.error('Error running script:', error);
  process.exit(1);
});
