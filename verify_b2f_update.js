// Verify the results of the B2F location update
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Create a Supabase client using environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyB2fUpdate() {
  console.log('Verifying B2F location update results...');

  try {
    // Check the task result
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 306188)
      .single();

    if (taskError) {
      console.error('Error getting task:', taskError);
      return;
    }

    console.log('\n=== TASK 306188 RESULT ===');
    console.log(`Status: ${task.status}`);
    console.log(`Result:`, task.result);
    console.log(`Processed At: ${task.processed_at}`);

    // Check if there are any discs still with 'B2F 08-02' location
    const { data: remainingB2f, error: remainingError } = await supabase
      .from('t_discs')
      .select('id, location')
      .eq('location', 'B2F 08-02')
      .limit(5);

    if (remainingError) {
      console.error('Error checking remaining B2F 08-02 discs:', remainingError);
    } else {
      console.log(`\nRemaining discs with 'B2F 08-02' location: ${remainingB2f?.length || 0}`);
      if (remainingB2f && remainingB2f.length > 0) {
        remainingB2f.forEach(disc => {
          console.log(`  Disc ID ${disc.id}: location = '${disc.location}'`);
        });
      }
    }

    // Check how many discs now have 'FS' location (sample)
    const { data: fsDiscs, error: fsError } = await supabase
      .from('t_discs')
      .select('id, location')
      .eq('location', 'FS')
      .limit(10);

    if (fsError) {
      console.error('Error checking FS discs:', fsError);
    } else {
      console.log(`\nSample of discs with 'FS' location (showing first 10):`);
      if (fsDiscs && fsDiscs.length > 0) {
        fsDiscs.forEach(disc => {
          console.log(`  Disc ID ${disc.id}: location = '${disc.location}'`);
        });
      }
    }

    // Check for any other B2F locations
    const { data: otherB2f, error: otherB2fError } = await supabase
      .from('t_discs')
      .select('location')
      .like('location', 'B2F%')
      .limit(10);

    if (otherB2fError) {
      console.error('Error checking other B2F locations:', otherB2fError);
    } else {
      console.log(`\nOther B2F locations found:`);
      if (otherB2f && otherB2f.length > 0) {
        // Group by location
        const locationCounts = {};
        otherB2f.forEach(disc => {
          locationCounts[disc.location] = (locationCounts[disc.location] || 0) + 1;
        });
        
        Object.entries(locationCounts).forEach(([location, count]) => {
          console.log(`  '${location}': ${count} disc(s)`);
        });
      } else {
        console.log('  No other B2F locations found');
      }
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the verification
verifyB2fUpdate();
