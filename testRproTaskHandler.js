// testRproTaskHandler.js - Test the check_if_rpro_is_ready task handler
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testTaskHandler() {
  try {
    console.log('🧪 Testing check_if_rpro_is_ready task handler...');
    console.log('==================================================');

    // Get a few sample RPRO records to test with
    const { data: sampleRecords, error: sampleError } = await supabase
      .from('imported_table_rpro')
      .select('id, ivno, ivqtylaw, ivaux3')
      .limit(3);

    if (sampleError) {
      console.error('❌ Error fetching sample records:', sampleError.message);
      return;
    }

    if (!sampleRecords || sampleRecords.length === 0) {
      console.log('❌ No sample records found');
      return;
    }

    console.log(`📋 Found ${sampleRecords.length} sample records to test with:`);
    sampleRecords.forEach(record => {
      console.log(`  ID: ${record.id}, IVNO: ${record.ivno}, Qty: ${record.ivqtylaw}, Bin: ${record.ivaux3 || 'null'}`);
    });

    // Clear any existing todo values for these records
    console.log('\n🧹 Clearing existing todo values...');
    const { error: clearError } = await supabase
      .from('imported_table_rpro')
      .update({ todo: null })
      .in('id', sampleRecords.map(r => r.id));

    if (clearError) {
      console.error('❌ Error clearing todo values:', clearError.message);
      return;
    }

    console.log('✅ Todo values cleared');

    // Enqueue check_if_rpro_is_ready tasks for these records
    console.log('\n📤 Enqueueing check_if_rpro_is_ready tasks...');
    
    const tasks = sampleRecords.map((record, index) => ({
      task_type: 'check_if_rpro_is_ready',
      payload: { id: record.id },
      status: 'pending',
      scheduled_at: new Date(Date.now() + (index * 2000)).toISOString(), // 2 seconds apart
      created_at: new Date().toISOString(),
      enqueued_by: 'test_rpro_task_handler'
    }));

    const { data: enqueuedTasks, error: enqueueError } = await supabase
      .from('t_task_queue')
      .insert(tasks)
      .select('id, task_type, payload, scheduled_at');

    if (enqueueError) {
      console.error('❌ Error enqueueing tasks:', enqueueError.message);
      return;
    }

    console.log(`✅ Successfully enqueued ${enqueuedTasks.length} tasks:`);
    enqueuedTasks.forEach(task => {
      console.log(`  Task ID: ${task.id}, RPRO ID: ${task.payload.id}, Scheduled: ${task.scheduled_at}`);
    });

    console.log('\n⏳ Waiting for tasks to be processed by the worker...');
    console.log('   (Make sure the worker is running: node taskQueueWorker.js)');
    
    // Wait a bit and then check the results
    await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds

    console.log('\n🔍 Checking task results...');
    
    // Check task statuses
    const { data: taskStatuses, error: statusError } = await supabase
      .from('t_task_queue')
      .select('id, status, result, processed_at')
      .in('id', enqueuedTasks.map(t => t.id));

    if (statusError) {
      console.error('❌ Error checking task statuses:', statusError.message);
      return;
    }

    console.log('\n📊 Task Results:');
    console.log('================');
    taskStatuses.forEach(task => {
      console.log(`  Task ID: ${task.id}`);
      console.log(`    Status: ${task.status}`);
      console.log(`    Processed: ${task.processed_at || 'Not yet'}`);
      if (task.result) {
        console.log(`    Result: ${JSON.stringify(task.result, null, 2)}`);
      }
      console.log('');
    });

    // Check updated todo values
    const { data: updatedRecords, error: updateError } = await supabase
      .from('imported_table_rpro')
      .select('id, ivno, ivqtylaw, ivaux3, todo')
      .in('id', sampleRecords.map(r => r.id));

    if (updateError) {
      console.error('❌ Error checking updated records:', updateError.message);
      return;
    }

    console.log('\n📋 Updated RPRO Records:');
    console.log('========================');
    updatedRecords.forEach(record => {
      console.log(`  IVNO: ${record.ivno}`);
      console.log(`    Qty: ${record.ivqtylaw}, Bin: ${record.ivaux3 || 'null'}`);
      console.log(`    Todo: ${record.todo || 'null'}`);
      console.log('');
    });

    // Summary
    const completedTasks = taskStatuses.filter(t => t.status === 'completed').length;
    const errorTasks = taskStatuses.filter(t => t.status === 'error').length;
    const pendingTasks = taskStatuses.filter(t => t.status === 'pending').length;

    console.log('\n🎉 Test Summary:');
    console.log('================');
    console.log(`✅ Completed tasks: ${completedTasks}`);
    console.log(`❌ Error tasks: ${errorTasks}`);
    console.log(`⏳ Pending tasks: ${pendingTasks}`);

    if (completedTasks === enqueuedTasks.length) {
      console.log('\n🎊 All tasks completed successfully!');
      console.log('✅ The check_if_rpro_is_ready task handler is working correctly.');
    } else if (pendingTasks > 0) {
      console.log('\n⏳ Some tasks are still pending. Make sure the worker is running.');
    } else if (errorTasks > 0) {
      console.log('\n⚠️  Some tasks had errors. Check the task results above.');
    }

  } catch (err) {
    console.error('❌ Exception:', err.message);
  }
}

testTaskHandler();
