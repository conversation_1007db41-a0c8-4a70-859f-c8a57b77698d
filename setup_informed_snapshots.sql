-- Setup script for Informed Repricer Snapshots
-- This script creates all necessary database objects for snapshot functionality

\echo 'Setting up Informed Repricer Snapshots...'

-- 1. Create the rpt_informed table
\echo 'Creating rpt_informed table...'
\i create_rpt_informed_table.sql

-- 2. Create the snapshot function
\echo 'Creating snapshot function...'
\i fn_create_informed_snapshot.sql

-- 3. Create the trigger for automatic snapshots
\echo 'Creating automatic snapshot trigger...'
\i create_informed_snapshot_trigger.sql

-- 4. Test the setup by creating a test snapshot (if data exists)
\echo 'Testing snapshot creation...'
DO $$
DECLARE
    v_record_count INTEGER;
    v_test_result RECORD;
BEGIN
    -- Check if we have data in it_infor_all_fields
    SELECT COUNT(*) INTO v_record_count FROM public.it_infor_all_fields;
    
    IF v_record_count > 0 THEN
        RAISE NOTICE 'Found % records in it_infor_all_fields - creating test snapshot', v_record_count;
        
        -- Create a test snapshot
        SELECT * INTO v_test_result 
        FROM public.fn_create_informed_snapshot('Initial setup test snapshot') 
        LIMIT 1;
        
        RAISE NOTICE 'Test snapshot created with ID: %', v_test_result.snapshot_id;
        RAISE NOTICE 'Test snapshot metrics: FBM=%, FBA=%, Total=%', 
            v_test_result.active_fbm_listing_count,
            v_test_result.active_fba_listing_count,
            v_test_result.total_active_listings;
    ELSE
        RAISE NOTICE 'No data found in it_infor_all_fields - skipping test snapshot';
        RAISE NOTICE 'Import some Informed data first, then snapshots will be created automatically';
    END IF;
END;
$$;

-- 5. Show current snapshot status
\echo 'Current snapshot status:'
SELECT 
    COUNT(*) as total_snapshots,
    MAX(snapshot_date) as latest_snapshot_date,
    MAX(total_active_listings) as latest_total_listings
FROM public.rpt_informed;

\echo 'Informed Repricer Snapshots setup complete!'
\echo ''
\echo 'Summary:'
\echo '- Table: rpt_informed (stores snapshot data)'
\echo '- Function: fn_create_informed_snapshot() (creates manual snapshots)'
\echo '- Trigger: tr_informed_snapshot_after_import (automatic snapshots after imports)'
\echo '- API Endpoints: /api/informed/create-snapshot, /api/informed/snapshots, /api/informed/snapshot-statistics'
\echo '- Admin Interface: New "Snapshot Management" section in Informed tab'
\echo ''
\echo 'Next steps:'
\echo '1. Import some Informed data to test automatic snapshot creation'
\echo '2. Use the admin interface to create manual snapshots'
\echo '3. View snapshot history and statistics'
