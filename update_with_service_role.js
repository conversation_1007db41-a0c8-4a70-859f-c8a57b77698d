import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with service role key
// Replace 'YOUR_SERVICE_ROLE_KEY' with your actual service role key
const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = 'YOUR_SERVICE_ROLE_KEY'; // Replace this with your service role key
const supabase = createClient(supabaseUrl, supabaseKey);

const updateWithServiceRole = async () => {
  try {
    console.log('Checking disc and order sheet line with service role...');
    
    // Check if disc exists
    const { data: discData, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, color_id, sold_date, order_sheet_line_id')
      .eq('id', 421349)
      .single();
      
    if (discError) {
      console.error('Error fetching disc:', discError);
      return;
    }
    
    console.log('Disc found:', discData);
    
    // Check if order sheet line exists
    const { data: oslData, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, min_weight, max_weight, color_id')
      .eq('id', 16890)
      .single();
      
    if (oslError) {
      console.error('Error fetching order sheet line:', oslError);
      return;
    }
    
    console.log('Order sheet line found:', oslData);
    
    // Check if they should match
    const mpsMatch = discData.mps_id === oslData.mps_id;
    const weightMatch = discData.weight >= oslData.min_weight && discData.weight <= oslData.max_weight;
    const colorMatch = oslData.color_id === 23 || discData.color_id === oslData.color_id;
    const notSold = discData.sold_date === null;
    
    console.log(`MPS Match: ${mpsMatch} (Disc MPS: ${discData.mps_id}, OSL MPS: ${oslData.mps_id})`);
    console.log(`Weight Match: ${weightMatch} (Disc Weight: ${discData.weight}, OSL Min: ${oslData.min_weight}, OSL Max: ${oslData.max_weight})`);
    console.log(`Color Match: ${colorMatch} (Disc Color: ${discData.color_id}, OSL Color: ${oslData.color_id})`);
    console.log(`Not Sold: ${notSold} (Sold Date: ${discData.sold_date})`);
    
    const shouldMatch = mpsMatch && weightMatch && colorMatch && notSold;
    console.log(`Should Match: ${shouldMatch}`);
    
    // Update the disc
    console.log('\nUpdating disc 421349 with order_sheet_line_id 16890...');
    const { data: updateData, error: updateError } = await supabase
      .from('t_discs')
      .update({ order_sheet_line_id: 16890 })
      .eq('id', 421349)
      .select();
      
    if (updateError) {
      console.error('Error updating disc:', updateError);
      return;
    }
    
    console.log('Disc updated successfully:', updateData);
    
    // Verify the update
    const { data: verifyData, error: verifyError } = await supabase
      .from('t_discs')
      .select('id, order_sheet_line_id')
      .eq('id', 421349)
      .single();
      
    if (verifyError) {
      console.error('Error verifying update:', verifyError);
      return;
    }
    
    console.log('Verification:', verifyData);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
};

updateWithServiceRole();
