import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM3ODY1NDEsImV4cCI6MjA0OTM2MjU0MX0.MtBXMZt7rCqXd6c9clhrNoVtfOxEilX2C8oboMceOGg';
const supabase = createClient(supabaseUrl, supabaseKey);

const checkDatabase = async () => {
  try {
    console.log('Checking database connection...');
    console.log('Supabase URL:', supabaseUrl);
    console.log('Using anon key:', supabaseKey.substring(0, 10) + '...');
    
    // Check if we can connect to the database
    const { data: tableData, error: tableError } = await supabase
      .from('t_discs')
      .select('id')
      .limit(1);
      
    if (tableError) {
      console.error('Error connecting to database:', tableError);
      return;
    }
    
    console.log('Successfully connected to database. Found t_discs table.');
    
    // Check the highest disc ID
    const { data: maxDiscData, error: maxDiscError } = await supabase
      .from('t_discs')
      .select('id')
      .order('id', { ascending: false })
      .limit(1);
      
    if (maxDiscError) {
      console.error('Error getting max disc ID:', maxDiscError);
      return;
    }
    
    if (maxDiscData && maxDiscData.length > 0) {
      console.log('Highest disc ID in database:', maxDiscData[0].id);
    } else {
      console.log('No discs found in database.');
    }
    
    // Check if disc 421349 exists
    const { count: discCount, error: discCountError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .eq('id', 421349);
      
    if (discCountError) {
      console.error('Error checking if disc 421349 exists:', discCountError);
      return;
    }
    
    console.log(`Disc 421349 exists: ${discCount > 0}`);
    
    // Check if order sheet line 16890 exists
    const { count: oslCount, error: oslCountError } = await supabase
      .from('t_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .eq('id', 16890);
      
    if (oslCountError) {
      console.error('Error checking if order sheet line 16890 exists:', oslCountError);
      return;
    }
    
    console.log(`Order sheet line 16890 exists: ${oslCount > 0}`);
    
    // Check for any discs with ID close to 421349
    console.log('\nChecking for discs with IDs close to 421349...');
    const { data: nearbyDiscs, error: nearbyError } = await supabase
      .from('t_discs')
      .select('id')
      .gte('id', 421340)
      .lte('id', 421360)
      .order('id');
      
    if (nearbyError) {
      console.error('Error checking for nearby discs:', nearbyError);
      return;
    }
    
    if (nearbyDiscs && nearbyDiscs.length > 0) {
      console.log('Found these disc IDs in the range 421340-421360:');
      nearbyDiscs.forEach(disc => console.log(disc.id));
    } else {
      console.log('No discs found in the range 421340-421360.');
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
};

checkDatabase();
