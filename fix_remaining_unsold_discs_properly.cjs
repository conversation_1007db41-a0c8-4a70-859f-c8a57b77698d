require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function fixRemainingUnsoldDiscsProper() {
  try {
    console.log('Getting ALL remaining unsold discs with null vendor_osl_id and processing them properly...');
    
    // Get ALL remaining unsold discs with null vendor_osl_id in one query
    const { data: allRemainingDiscs, error: fetchError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .is('vendor_osl_id', null)
      .is('sold_date', null)  // Unsold discs only
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null)
      .order('id');  // Order by ID for consistent processing
    
    if (fetchError) {
      console.error('Error getting all remaining discs:', fetchError);
      return;
    }
    
    console.log(`Found ${allRemainingDiscs.length} remaining unsold discs to check`);
    
    if (allRemainingDiscs.length === 0) {
      console.log('✅ No remaining unsold discs with null vendor_osl_id!');
      return;
    }
    
    // Process each disc individually
    let totalProcessed = 0;
    let totalUpdated = 0;
    let totalErrors = 0;
    let totalNoMatch = 0;
    
    console.log('\nProcessing each disc individually...\n');
    
    for (const disc of allRemainingDiscs) {
      totalProcessed++;
      
      try {
        console.log(`${totalProcessed}/${allRemainingDiscs.length}: Checking disc ${disc.id} (MPS ${disc.mps_id}, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id})`);
        
        // Test the vendor OSL function for this specific disc
        const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
          'find_matching_osl_by_mfg_weight',
          {
            mps_id_param: disc.mps_id,
            color_id_param: disc.color_id,
            weight_mfg_param: disc.weight_mfg
          }
        );
        
        if (vendorOslError) {
          console.error(`  ❌ Error finding vendor OSL: ${vendorOslError.message}`);
          totalErrors++;
          continue;
        }
        
        const vendorOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
        
        if (vendorOslId) {
          // This disc SHOULD have a vendor OSL mapping!
          console.log(`  🎯 MATCH FOUND: Should map to OSL ${vendorOslId}`);
          
          // Update the disc with vendor_osl_id
          const { error: updateError } = await supabase
            .from('t_discs')
            .update({ vendor_osl_id: vendorOslId })
            .eq('id', disc.id);
          
          if (updateError) {
            console.error(`  ❌ Error updating disc: ${updateError.message}`);
            totalErrors++;
          } else {
            totalUpdated++;
            console.log(`  ✅ Updated with vendor_osl_id: ${vendorOslId}`);
            
            // Show mapping comparison
            if (vendorOslId !== disc.order_sheet_line_id) {
              console.log(`     Different mappings: regular OSL ${disc.order_sheet_line_id}, vendor OSL ${vendorOslId}`);
            } else {
              console.log(`     Same OSL for both mappings: ${vendorOslId}`);
            }
          }
        } else {
          // No match found - this is expected for some discs
          totalNoMatch++;
          console.log(`  ❌ No vendor OSL match found`);
        }
        
        // Progress update every 50 discs
        if (totalProcessed % 50 === 0) {
          console.log(`\n--- Progress: ${totalProcessed}/${allRemainingDiscs.length} processed, ${totalUpdated} updated, ${totalNoMatch} no match, ${totalErrors} errors ---\n`);
        }
        
      } catch (err) {
        console.error(`  ❌ Error processing disc ${disc.id}: ${err.message}`);
        totalErrors++;
      }
    }
    
    console.log('\n=== FINAL RESULTS ===');
    console.log(`Total remaining discs processed: ${totalProcessed}`);
    console.log(`Additional matches found and updated: ${totalUpdated}`);
    console.log(`Discs with no vendor OSL match: ${totalNoMatch}`);
    console.log(`Errors encountered: ${totalErrors}`);
    
    if (totalUpdated > 0) {
      console.log(`\n🎉 SUCCESS! Found and updated ${totalUpdated} additional missed vendor OSL mappings!`);
      
      // Get final statistics for unsold discs
      const { count: finalUnsoldVendorOslCount, error: finalError } = await supabase
        .from('t_discs')
        .select('*', { count: 'exact', head: true })
        .not('vendor_osl_id', 'is', null)
        .is('sold_date', null);  // Unsold discs
      
      const { count: finalUnsoldNullCount, error: finalNullError } = await supabase
        .from('t_discs')
        .select('*', { count: 'exact', head: true })
        .is('vendor_osl_id', null)
        .is('sold_date', null)  // Unsold discs
        .not('weight_mfg', 'is', null)
        .not('mps_id', 'is', null)
        .not('color_id', 'is', null);
      
      if (!finalError && !finalNullError) {
        console.log(`\n📊 FINAL UNSOLD DISCS STATISTICS:`);
        console.log(`Unsold discs with vendor_osl_id: ${finalUnsoldVendorOslCount}`);
        console.log(`Remaining unsold discs with null vendor_osl_id: ${finalUnsoldNullCount}`);
        console.log(`This round improvement: +${totalUpdated} new vendor mappings`);
        
        const totalUnsoldEligible = finalUnsoldVendorOslCount + finalUnsoldNullCount;
        const finalSuccessRate = ((finalUnsoldVendorOslCount / totalUnsoldEligible) * 100).toFixed(1);
        console.log(`Final unsold discs vendor mapping success rate: ${finalSuccessRate}%`);
      }
      
      console.log('\n✅ Unsold inventory dual mapping system is now properly complete!');
    } else {
      console.log('\n✅ No additional matches found - all remaining discs correctly have no vendor OSL matches.');
      console.log(`The ${totalNoMatch} remaining unsold discs have manufacturer weights that fall outside all OSL weight ranges.`);
    }
    
    console.log('\n🎯 PROPER PROCESSING COMPLETE:');
    console.log('Used single query to get ALL remaining records, then processed each one individually.');
    console.log('No more batch processing issues - every remaining disc was checked exactly once.');
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

fixRemainingUnsoldDiscsProper();
