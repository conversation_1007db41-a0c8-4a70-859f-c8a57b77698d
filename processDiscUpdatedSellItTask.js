// Function to process a disc_updated_sell_it task
async function processDiscUpdatedSellItTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to sell disc. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to sell disc. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;
    const currentSoldDate = payload.sold_date || null;
    console.log(`[taskQueueWorker.js] Processing sell task for disc id=${discId}, current sold_date=${currentSoldDate}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Only sell the disc if it's not already sold
    if (currentSoldDate) {
      console.log(`[taskQueueWorker.js] Disc ${discId} is already sold (sold_date=${currentSoldDate}), skipping sell operation`);
      await updateTaskStatus(task.id, 'completed', {
        message: `Disc ${discId} is already sold, no action needed.`,
        disc_id: discId,
        current_sold_date: currentSoldDate,
        action: 'skipped'
      });
      return;
    }

    // Update the disc record to set sold_date to now and sold_channel to 'Fixed'
    const now = new Date();
    const updateData = {
      sold_date: now.toISOString(),
      sold_channel: 'Fixed'
    };

    console.log(`[taskQueueWorker.js] Updating disc ${discId} with: ${JSON.stringify(updateData)}`);

    const { error: updateError } = await supabase
      .from('t_discs')
      .update(updateData)
      .eq('id', discId);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating disc record: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to sell disc. Database error when updating disc record.",
        error: updateError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully sold disc ${discId}`);
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully sold disc ${discId}. Set sold_date to ${now.toISOString()} and sold_channel to 'Fixed'.`,
      disc_id: discId,
      sold_date: now.toISOString(),
      sold_channel: 'Fixed'
    });

  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to sell disc due to an unexpected error.",
      error: err.message
    });
  }
}

export default processDiscUpdatedSellItTask;
