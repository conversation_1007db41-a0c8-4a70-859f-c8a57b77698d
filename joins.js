import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzc4NjU0MSwiZXhwIjoyMDQ5MzYyNTQxfQ.FQyPTgdBP83VhuykdlZkagHADc5nBmx7-yd0JYt5aP8';
const supabase = createClient(supabaseUrl, supabaseKey);



let callCount = 0;

async function callRpc() {
  const { data, error } = await supabase.rpc('f_apply_matching_to_all_sdasins');

  if (error) {
    console.error('Error calling function:', error);
    // Optionally, decide whether to continue or break out.
  } else {
    callCount++;
    if (data === null) {
      console.log(`No more records to process. Stopped after ${callCount} calls (expected ${callCount * 5} records processed).`);
      return; // Stop calling
    } else {
      console.log(`Call ${callCount}: Processed ${data * 1} records (expected ${callCount * 5} records if 5 per call).`);
    }
  }

  // Immediately call the function again.
  callRpc();
}

// Start the process.
callRpc();
