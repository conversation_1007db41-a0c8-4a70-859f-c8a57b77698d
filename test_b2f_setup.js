// Test script to verify B2F setup and create missing view
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

console.log('🔍 Testing B2F Setup...');
console.log(`Supabase URL: ${supabaseUrl}`);
console.log(`Supabase Key: ${supabaseKey ? 'Present' : 'Missing'}`);

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  console.log('\n📡 Testing Supabase connection...');
  
  try {
    // Test basic connection
    const { data, error } = await supabase
      .from('t_discs')
      .select('count(*)', { count: 'exact', head: true });
    
    if (error) {
      console.error('❌ Connection failed:', error.message);
      return false;
    }
    
    console.log('✅ Connection successful');
    console.log(`📊 Found ${data} discs in database`);
    return true;
  } catch (err) {
    console.error('❌ Connection error:', err.message);
    return false;
  }
}

async function checkViewExists() {
  console.log('\n🔍 Checking if v_b2f_pick_slim view exists...');
  
  try {
    const { data, error } = await supabase
      .from('v_b2f_pick_slim')
      .select('count(*)', { count: 'exact', head: true });
    
    if (error) {
      console.log('❌ View does not exist:', error.message);
      return false;
    }
    
    console.log('✅ View exists');
    console.log(`📊 Found ${data} B2F candidates`);
    return true;
  } catch (err) {
    console.log('❌ View check error:', err.message);
    return false;
  }
}

async function createView() {
  console.log('\n🛠️ Creating v_b2f_pick_slim view...');
  
  const viewSQL = `
    CREATE OR REPLACE VIEW public.v_b2f_pick_slim AS
    SELECT
      d.g_pull as disc,
      osl.g_code as osl,
      vs.discs_sold_last_30_days_retail + vs.discs_sold_last_30_days_dz as osl_sold_last_30_dz_plus_retail
    FROM
      t_discs d
      JOIN t_order_sheet_lines osl ON d.order_sheet_line_id = osl.id
      JOIN v_stats_by_osl vs ON osl.id = vs.id
    WHERE
      vs.discs_in_stock_bs > 0
      AND (vs.discs_in_stock_fs + vs.discs_in_stock_b2f) = 0
      AND d.location = 'BS'::text
      AND d.sold_date IS NULL
    ORDER BY
      d.order_sheet_line_id,
      d.g_pull;
  `;
  
  try {
    const { error } = await supabase.rpc('exec_sql', { sql: viewSQL });
    
    if (error) {
      console.error('❌ Failed to create view:', error.message);
      console.log('\n📝 Please run this SQL manually in Supabase SQL Editor:');
      console.log(viewSQL);
      return false;
    }
    
    console.log('✅ View created successfully');
    return true;
  } catch (err) {
    console.error('❌ Error creating view:', err.message);
    console.log('\n📝 Please run this SQL manually in Supabase SQL Editor:');
    console.log(viewSQL);
    return false;
  }
}

async function testB2FEndpoints() {
  console.log('\n🧪 Testing B2F API endpoints...');
  
  try {
    // Test count endpoint
    const countResponse = await fetch('http://localhost:3001/api/b2f/count');
    if (countResponse.ok) {
      const countData = await countResponse.json();
      console.log('✅ Count endpoint working:', countData);
    } else {
      console.log('❌ Count endpoint failed:', countResponse.status);
    }
    
    // Test records endpoint
    const recordsResponse = await fetch('http://localhost:3001/api/b2f/records');
    if (recordsResponse.ok) {
      const recordsData = await recordsResponse.json();
      console.log('✅ Records endpoint working, found', recordsData.records?.length || 0, 'records');
    } else {
      console.log('❌ Records endpoint failed:', recordsResponse.status);
    }
    
  } catch (err) {
    console.log('❌ API test failed - make sure adminServer.js is running on port 3001');
    console.log('   Run: node adminServer.js');
  }
}

async function main() {
  console.log('🚀 Starting B2F Setup Test\n');
  
  // Test connection
  const connected = await testConnection();
  if (!connected) {
    console.log('\n❌ Setup failed - fix connection first');
    process.exit(1);
  }
  
  // Check if view exists
  const viewExists = await checkViewExists();
  if (!viewExists) {
    // Try to create view
    const created = await createView();
    if (created) {
      // Test again
      await checkViewExists();
    }
  }
  
  // Test API endpoints
  await testB2FEndpoints();
  
  console.log('\n🎉 B2F Setup Test Complete!');
  console.log('\n📋 Next steps:');
  console.log('1. Make sure adminServer.js is running: node adminServer.js');
  console.log('2. Open http://localhost:3001/admin.html');
  console.log('3. Click the B2F tab');
  console.log('4. Test the workflow');
}

main().catch(console.error);
