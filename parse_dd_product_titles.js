import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Cache for lookup data
let moldsCache = null;
let plasticsCache = null;
let stampsCache = null;

async function loadLookupData() {
    console.log('Loading lookup data...');
    
    // Load Dynamic Discs molds (brand_id = 1)
    const { data: molds, error: moldsError } = await supabase
        .from('t_molds')
        .select('mold, code')
        .eq('brand_id', 1);
    
    if (moldsError) {
        throw new Error(`Error loading molds: ${moldsError.message}`);
    }
    
    // Load Dynamic Discs plastics (brand_id = 1)
    const { data: plastics, error: plasticsError } = await supabase
        .from('t_plastics')
        .select('plastic, code')
        .eq('brand_id', 1);
    
    if (plasticsError) {
        throw new Error(`Error loading plastics: ${plasticsError.message}`);
    }
    
    // Load all stamps
    const { data: stamps, error: stampsError } = await supabase
        .from('t_stamps')
        .select('stamp');
    
    if (stampsError) {
        throw new Error(`Error loading stamps: ${stampsError.message}`);
    }
    
    moldsCache = molds.map(m => m.mold);
    plasticsCache = plastics.map(p => p.plastic);
    stampsCache = stamps.map(s => s.stamp);
    
    console.log(`Loaded ${moldsCache.length} molds, ${plasticsCache.length} plastics, ${stampsCache.length} stamps`);
}

function parseProductTitle(title) {
    if (!title || typeof title !== 'string') {
        return { mold: null, plastic: null, stamp: null };
    }
    
    const originalTitle = title;
    title = title.trim();
    
    // Skip non-disc products
    if (title.includes('DyeMax') || title.includes('Set') || title.includes('Overweight') || 
        title.includes('Mystery') || title.includes('Mini')) {
        return { mold: null, plastic: null, stamp: null };
    }
    
    let foundMold = null;
    let foundPlastic = null;
    let foundStamp = null;
    
    // Find mold - look for exact matches
    for (const mold of moldsCache) {
        if (title.includes(mold)) {
            foundMold = mold;
            break;
        }
    }
    
    // Find plastic - look for exact matches, prioritize longer matches
    const sortedPlastics = [...plasticsCache].sort((a, b) => b.length - a.length);
    for (const plastic of sortedPlastics) {
        if (title.includes(plastic)) {
            foundPlastic = plastic;
            break;
        }
    }
    
    // Extract stamp - everything after the mold, excluding common patterns
    if (foundMold && foundPlastic) {
        // Pattern: "Plastic Mold - Stamp" or "Plastic Mold Stamp"
        const moldIndex = title.indexOf(foundMold);
        const afterMold = title.substring(moldIndex + foundMold.length).trim();
        
        if (afterMold.startsWith(' - ')) {
            // Format: "Plastic Mold - Stamp"
            foundStamp = afterMold.substring(3).trim();
        } else if (afterMold.startsWith(' ')) {
            // Format: "Plastic Mold Stamp"
            foundStamp = afterMold.trim();
        }
        
        // Clean up stamp
        if (foundStamp) {
            // Remove common suffixes
            foundStamp = foundStamp.replace(/\s*\(.*?\)\s*$/, ''); // Remove parentheses at end
            foundStamp = foundStamp.trim();
            
            // If no stamp found or it's empty, set to 'Stock'
            if (!foundStamp || foundStamp === '') {
                foundStamp = 'Stock';
            }
        } else {
            foundStamp = 'Stock';
        }
    }
    
    return {
        mold: foundMold,
        plastic: foundPlastic,
        stamp: foundStamp,
        original_title: originalTitle
    };
}

async function updateParsedFields() {
    console.log('Starting product title parsing...');
    
    try {
        // Load lookup data
        await loadLookupData();
        
        // Get all Dynamic Discs disc records
        console.log('Fetching Dynamic Discs disc records...');
        const { data: records, error: fetchError } = await supabase
            .from('it_dd_osl')
            .select('id, product_title, product_vendor, product_product_type')
            .eq('product_vendor', 'Dynamic Discs')
            .eq('product_product_type', 'Discs');
        
        if (fetchError) {
            console.error('Error fetching records:', fetchError);
            return;
        }
        
        console.log(`Found ${records.length} Dynamic Discs disc records`);
        
        // Parse titles and prepare updates
        const updates = [];
        const parseStats = {
            total: records.length,
            parsed_mold: 0,
            parsed_plastic: 0,
            parsed_stamp: 0,
            failed: 0,
            samples: []
        };
        
        for (const record of records) {
            const parsed = parseProductTitle(record.product_title);
            
            const update = {
                id: record.id,
                parsed_mold: parsed.mold,
                parsed_plastic: parsed.plastic,
                parsed_stamp: parsed.stamp
            };
            
            updates.push(update);
            
            // Update statistics
            if (parsed.mold) parseStats.parsed_mold++;
            if (parsed.plastic) parseStats.parsed_plastic++;
            if (parsed.stamp) parseStats.parsed_stamp++;
            if (!parsed.mold && !parsed.plastic && !parsed.stamp) parseStats.failed++;
            
            // Collect samples for review
            if (parseStats.samples.length < 10) {
                parseStats.samples.push({
                    title: record.product_title,
                    mold: parsed.mold,
                    plastic: parsed.plastic,
                    stamp: parsed.stamp
                });
            }
        }
        
        console.log(`\nParsing complete:`);
        console.log(`- Records with mold: ${parseStats.parsed_mold}`);
        console.log(`- Records with plastic: ${parseStats.parsed_plastic}`);
        console.log(`- Records with stamp: ${parseStats.parsed_stamp}`);
        console.log(`- Failed to parse: ${parseStats.failed}`);
        
        // Show sample results
        console.log('\nSample parsing results:');
        console.log('=====================================');
        parseStats.samples.forEach((sample, index) => {
            console.log(`${index + 1}. "${sample.title}"`);
            console.log(`   Mold: ${sample.mold || 'NOT FOUND'}`);
            console.log(`   Plastic: ${sample.plastic || 'NOT FOUND'}`);
            console.log(`   Stamp: ${sample.stamp || 'NOT FOUND'}`);
            console.log('---');
        });
        
        if (updates.length === 0) {
            console.log('No updates to perform.');
            return;
        }
        
        // Update records in batches
        console.log(`\nUpdating ${updates.length} records...`);
        const batchSize = 100;
        let updatedCount = 0;
        
        for (let i = 0; i < updates.length; i += batchSize) {
            const batch = updates.slice(i, i + batchSize);
            
            // Update each record in the batch
            for (const update of batch) {
                const { error: updateError } = await supabase
                    .from('it_dd_osl')
                    .update({
                        parsed_mold: update.parsed_mold,
                        parsed_plastic: update.parsed_plastic,
                        parsed_stamp: update.parsed_stamp
                    })
                    .eq('id', update.id);
                
                if (updateError) {
                    console.error(`Error updating record ${update.id}:`, updateError);
                } else {
                    updatedCount++;
                }
            }
            
            console.log(`Updated batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(updates.length/batchSize)} (${updatedCount}/${updates.length} records)`);
        }
        
        console.log(`\nUpdate complete! Updated ${updatedCount} records.`);
        
        // Verify results
        console.log('\nVerifying results...');
        const { data: verifyData, error: verifyError } = await supabase
            .from('it_dd_osl')
            .select('product_title, parsed_mold, parsed_plastic, parsed_stamp')
            .eq('product_vendor', 'Dynamic Discs')
            .eq('product_product_type', 'Discs')
            .not('parsed_mold', 'is', null)
            .limit(10);
        
        if (!verifyError && verifyData) {
            console.log('\nSample parsed results:');
            verifyData.forEach(row => {
                console.log(`"${row.product_title}" → ${row.parsed_plastic} | ${row.parsed_mold} | ${row.parsed_stamp}`);
            });
        }
        
        // Get final statistics
        const { data: finalStats, error: statsError } = await supabase
            .from('it_dd_osl')
            .select('parsed_mold, parsed_plastic, parsed_stamp')
            .eq('product_vendor', 'Dynamic Discs')
            .eq('product_product_type', 'Discs');
        
        if (!statsError && finalStats) {
            const moldCount = finalStats.filter(r => r.parsed_mold).length;
            const plasticCount = finalStats.filter(r => r.parsed_plastic).length;
            const stampCount = finalStats.filter(r => r.parsed_stamp).length;
            
            console.log(`\nFinal statistics:`);
            console.log(`- Total disc records: ${finalStats.length}`);
            console.log(`- Records with parsed mold: ${moldCount}`);
            console.log(`- Records with parsed plastic: ${plasticCount}`);
            console.log(`- Records with parsed stamp: ${stampCount}`);
        }
        
    } catch (error) {
        console.error('Script failed:', error);
    }
}

// Test the parsing function with sample data
function testParsing() {
    console.log('Testing product title parsing function...\n');
    
    const testCases = [
        'BioFuzion Defender - Chris Clemons 2023',
        'Classic Swirl Judge - Judgement Day',
        'Lucid Moonshine Deputy - Timehop',
        'Lucid Vandal HSCo Tesseract',
        'Classic Orbit Judge',
        'Lucid-Ice Glimmer Bounty',
        'Prime Truth',
        'Fuzion Escape',
        'Mystery Misprint - Prime|Classic Mini'
    ];
    
    // Mock cache for testing
    moldsCache = ['Defender', 'Judge', 'Deputy', 'Vandal', 'Bounty', 'Truth', 'Escape'];
    plasticsCache = ['BioFuzion', 'Classic Swirl', 'Lucid Moonshine', 'Lucid', 'Classic Orbit', 'Lucid-Ice Glimmer', 'Prime', 'Fuzion'];
    stampsCache = ['Stock'];
    
    testCases.forEach(testCase => {
        const result = parseProductTitle(testCase);
        console.log(`"${testCase}"`);
        console.log(`  → Plastic: ${result.plastic || 'NOT FOUND'}`);
        console.log(`  → Mold: ${result.mold || 'NOT FOUND'}`);
        console.log(`  → Stamp: ${result.stamp || 'NOT FOUND'}`);
        console.log('---');
    });
    
    console.log('\n');
}

// Run the script
console.log('Dynamic Discs Product Title Parser\n');

// Run tests first
testParsing();

// Then run the actual update
updateParsedFields()
    .then(() => {
        console.log('\nProduct title parsing completed successfully');
        process.exit(0);
    })
    .catch(error => {
        console.error('Product title parsing failed:', error);
        process.exit(1);
    });

export { parseProductTitle, updateParsedFields };
