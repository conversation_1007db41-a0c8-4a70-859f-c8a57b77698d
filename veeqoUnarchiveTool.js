import fetch from 'node-fetch';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('Error: VEEQO_API_KEY must be set in .env file');
  process.exit(1);
}

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}` };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to get archived products with stock
async function getArchivedProductsWithStock(maxProducts = 100) {
  console.log('🔍 Finding archived products with available stock...\n');
  
  const archivedWithStock = [];
  let page = 1;
  let totalChecked = 0;
  
  while (archivedWithStock.length < maxProducts && page <= 20) {
    console.log(`📄 Checking page ${page}...`);
    
    const result = await makeVeeqoRequest(`https://api.veeqo.com/products?page=${page}&page_size=50`);
    
    if (!result.success || !Array.isArray(result.data) || result.data.length === 0) {
      console.log(`No more products found on page ${page}`);
      break;
    }
    
    const products = result.data;
    totalChecked += products.length;
    
    for (const product of products) {
      // Check if product is archived (all channels pulled)
      const isArchived = product.channel_products && 
                        product.channel_products.length > 0 && 
                        product.channel_products.every(cp => cp.status === 'pulled');
      
      if (isArchived) {
        // Check if product has stock
        let totalStock = 0;
        if (product.sellables && product.sellables.length > 0) {
          totalStock = product.sellables.reduce((sum, sellable) => {
            return sum + (sellable.available_stock_level_at_all_warehouses || 0);
          }, 0);
        }
        
        if (totalStock > 0) {
          archivedWithStock.push({
            id: product.id,
            title: product.title,
            stock: totalStock,
            channel_products: product.channel_products,
            sellables: product.sellables,
            created_at: product.created_at
          });
        }
      }
    }
    
    console.log(`Found ${archivedWithStock.length} archived products with stock so far...`);
    
    if (products.length < 50) break; // Last page
    page++;
  }
  
  console.log(`\n📊 Checked ${totalChecked} products, found ${archivedWithStock.length} archived products with stock\n`);
  return archivedWithStock;
}

// Function to unarchive a product by updating channel status
async function unarchiveProduct(productId) {
  console.log(`🔄 Unarchiving product ${productId}...`);
  
  // First get the product details
  const productResult = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!productResult.success) {
    console.error(`❌ Failed to get product: ${productResult.error}`);
    return false;
  }
  
  const product = productResult.data;
  console.log(`📋 Product: "${product.title}"`);
  
  if (!product.channel_products || product.channel_products.length === 0) {
    console.log(`⚠️  Product has no channel products to unarchive`);
    return false;
  }
  
  const pulledChannels = product.channel_products.filter(cp => cp.status === 'pulled');
  
  if (pulledChannels.length === 0) {
    console.log(`✅ Product is already active (not archived)`);
    return true;
  }
  
  console.log(`📤 Found ${pulledChannels.length} pulled channel(s), attempting to reactivate...`);
  
  let successCount = 0;
  
  for (const channelProduct of pulledChannels) {
    try {
      console.log(`🔄 Reactivating on channel ${channelProduct.channel?.short_name || 'Unknown'}...`);
      
      // Try different possible endpoints for updating channel product status
      const possibleEndpoints = [
        `https://api.veeqo.com/channel_products/${channelProduct.id}`,
        `https://api.veeqo.com/products/${productId}/channel_products/${channelProduct.id}`,
        `https://api.veeqo.com/channels/${channelProduct.channel?.id}/products/${channelProduct.id}`
      ];
      
      let updated = false;
      
      for (const endpoint of possibleEndpoints) {
        // Try different status values
        const statusOptions = ['active', 'live', 'published', 'enabled'];
        
        for (const status of statusOptions) {
          const updateData = {
            channel_product: {
              status: status
            }
          };
          
          console.log(`   Trying ${endpoint} with status: ${status}`);
          const result = await makeVeeqoRequest(endpoint, 'PUT', updateData);
          
          if (result.success) {
            console.log(`   ✅ Successfully updated with status: ${status}`);
            updated = true;
            successCount++;
            break;
          } else {
            console.log(`   ❌ Failed: ${result.error}`);
          }
        }
        
        if (updated) break;
      }
      
      if (!updated) {
        console.log(`   ⚠️  Could not find working endpoint for channel product ${channelProduct.id}`);
        console.log(`   💡 Manual action may be required in Veeqo interface`);
      }
      
    } catch (error) {
      console.error(`❌ Error updating channel product: ${error.message}`);
    }
  }
  
  if (successCount > 0) {
    console.log(`✅ Successfully reactivated ${successCount}/${pulledChannels.length} channels`);
    return true;
  } else {
    console.log(`❌ Failed to reactivate any channels automatically`);
    console.log(`💡 You may need to manually reactivate this product in the Veeqo interface`);
    return false;
  }
}

// Function to display products and get user selection
function displayProductsForSelection(products) {
  console.log('📋 ARCHIVED PRODUCTS WITH STOCK:');
  console.log('='.repeat(80));
  
  products.forEach((product, index) => {
    console.log(`${index + 1}. ID: ${product.id} | Stock: ${product.stock} units`);
    console.log(`   Title: ${product.title}`);
    console.log(`   Created: ${new Date(product.created_at).toLocaleDateString()}`);
    console.log('');
  });
}

// Main interactive function
async function main() {
  console.log('🗂️  VEEQO UNARCHIVE TOOL');
  console.log('='.repeat(50));
  console.log('This tool helps you find and unarchive Veeqo products that have stock available.\n');
  
  try {
    // Get archived products with stock
    const archivedProducts = await getArchivedProductsWithStock(50);
    
    if (archivedProducts.length === 0) {
      console.log('✅ No archived products with stock found!');
      return;
    }
    
    // Display products
    displayProductsForSelection(archivedProducts);
    
    // Save to file for reference
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `archived_products_with_stock_${timestamp}.json`;
    fs.writeFileSync(filename, JSON.stringify(archivedProducts, null, 2));
    console.log(`💾 Saved product list to: ${filename}\n`);
    
    console.log('🛠️  NEXT STEPS:');
    console.log('1. To unarchive a specific product, run:');
    console.log('   node -e "import(\'./veeqoUnarchiveTool.js\').then(m => m.unarchiveProduct(PRODUCT_ID))"');
    console.log('');
    console.log('2. Or modify this script to unarchive products automatically');
    console.log('');
    console.log('3. Example products to consider unarchiving:');
    
    // Show top 5 by stock
    const topByStock = archivedProducts
      .sort((a, b) => b.stock - a.stock)
      .slice(0, 5);
    
    topByStock.forEach(product => {
      console.log(`   - ID: ${product.id} (${product.stock} units) - ${product.title.substring(0, 60)}...`);
    });
    
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
  }
}

// Export functions
export { getArchivedProductsWithStock, unarchiveProduct };

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
