-- Drop the existing trigger
DROP TRIGGER IF EXISTS tr_enqueue_mps_price_verified_task ON t_mps;

-- Update the function to handle both cost_price_reviewed_at and shopify_collection_uploaded_at updates
CREATE OR REPLACE FUNCTION fn_enqueue_mps_price_verified_task()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if cost_price_reviewed_at has been updated OR shopify_collection_uploaded_at has been updated from null to not null
    IF (OLD.cost_price_reviewed_at IS DISTINCT FROM NEW.cost_price_reviewed_at) OR
       (OLD.shopify_collection_uploaded_at IS NULL AND NEW.shopify_collection_uploaded_at IS NOT NULL) THEN

        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'mps_price_verified_try_upload_osls',
            jsonb_build_object('id', NEW.id),
            'pending',
            NOW(),
            NOW()
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a new trigger that fires on updates to either cost_price_reviewed_at or shopify_collection_uploaded_at
CREATE TRIGGER tr_enqueue_mps_price_verified_task
AFTER UPDATE OF cost_price_reviewed_at, shopify_collection_uploaded_at ON t_mps
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_mps_price_verified_task();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'MPS price verified task enqueuer function and trigger updated to handle shopify_collection_uploaded_at changes.';
END $$;
