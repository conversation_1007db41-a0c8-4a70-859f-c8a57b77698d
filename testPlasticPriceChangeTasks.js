/**
 * Test script for plastic retail price change task processors
 * 
 * This script demonstrates how to enqueue and test the new task types:
 * - plastic_retail_price_change
 * - update_disc_variant_price_on_shopify
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Test enqueueing a plastic_retail_price_change task
 * @param {number} plasticId - The plastic ID to test with
 */
async function testPlasticRetailPriceChangeTask(plasticId) {
  console.log(`\n🧪 Testing plastic_retail_price_change task for plastic ID: ${plasticId}`);
  
  try {
    // First, let's check what plastic we're working with
    const { data: plastic, error: plasticError } = await supabase
      .from('t_plastics')
      .select('id, plastic, val_retail_price')
      .eq('id', plasticId)
      .single();

    if (plasticError) {
      console.error('❌ Error fetching plastic:', plasticError.message);
      return;
    }

    if (!plastic) {
      console.error(`❌ Plastic with ID ${plasticId} not found`);
      return;
    }

    console.log(`📋 Plastic: ${plastic.plastic}, Current retail price: $${plastic.val_retail_price}`);

    // Check how many discs would be affected
    const { data: affectedDiscs, error: discsError } = await supabase
      .from('t_discs')
      .select(`
        id,
        mps_id,
        t_mps!inner (
          id,
          plastic_id,
          val_override_retail_price
        )
      `)
      .is('sold_date', null)
      .not('shopify_uploaded_at', 'is', null)
      .eq('t_mps.plastic_id', plasticId)
      .is('t_mps.val_override_retail_price', null);

    if (discsError) {
      console.error('❌ Error fetching affected discs:', discsError.message);
      return;
    }

    console.log(`📊 Found ${affectedDiscs.length} discs that would be affected by this price change`);

    if (affectedDiscs.length > 0) {
      console.log(`📝 Sample affected disc IDs: ${affectedDiscs.slice(0, 5).map(d => d.id).join(', ')}${affectedDiscs.length > 5 ? '...' : ''}`);
    }

    // Also check how many OSLs would be affected
    const { data: affectedOsls, error: oslsError } = await supabase
      .from('t_order_sheet_lines')
      .select(`
        id,
        mps_id,
        t_mps!inner (
          id,
          plastic_id,
          val_override_retail_price
        )
      `)
      .not('shopify_uploaded_at', 'is', null)
      .eq('t_mps.plastic_id', plasticId)
      .is('t_mps.val_override_retail_price', null);

    if (oslsError) {
      console.error('❌ Error fetching affected OSLs:', oslsError.message);
      return;
    }

    console.log(`📊 Found ${affectedOsls.length} OSLs that would be affected by this price change`);

    if (affectedOsls.length > 0) {
      console.log(`📝 Sample affected OSL IDs: ${affectedOsls.slice(0, 5).map(o => o.id).join(', ')}${affectedOsls.length > 5 ? '...' : ''}`);
    }

    // Enqueue the task
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'plastic_retail_price_change',
        payload: { id: plasticId },
        status: 'pending',
        scheduled_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        enqueued_by: 'testPlasticPriceChangeTasks.js'
      })
      .select()
      .single();

    if (taskError) {
      console.error('❌ Error enqueueing task:', taskError.message);
      return;
    }

    console.log(`✅ Successfully enqueued plastic_retail_price_change task with ID: ${task.id}`);
    console.log(`⏰ Task scheduled at: ${task.scheduled_at}`);
    
    return task.id;

  } catch (error) {
    console.error('❌ Error in testPlasticRetailPriceChangeTask:', error.message);
  }
}

/**
 * Test enqueueing an update_disc_variant_price_on_shopify task for a disc
 * @param {number} discId - The disc ID to test with
 * @param {number} newRetailPrice - The new retail price to set
 */
async function testUpdateDiscVariantPriceTask(discId, newRetailPrice) {
  console.log(`\n🧪 Testing update_disc_variant_price_on_shopify task for disc ID: ${discId}, new price: $${newRetailPrice}`);
  
  try {
    // First, let's check what disc we're working with
    const { data: disc, error: discError } = await supabase
      .from('t_discs')
      .select(`
        id,
        mps_id,
        sold_date,
        shopify_uploaded_at,
        t_mps (
          id,
          plastic_id,
          val_override_retail_price,
          t_plastics (
            plastic,
            val_retail_price
          )
        )
      `)
      .eq('id', discId)
      .single();

    if (discError) {
      console.error('❌ Error fetching disc:', discError.message);
      return;
    }

    if (!disc) {
      console.error(`❌ Disc with ID ${discId} not found`);
      return;
    }

    console.log(`📋 Disc ID: ${disc.id}`);
    console.log(`📋 Plastic: ${disc.t_mps?.t_plastics?.plastic || 'Unknown'}`);
    console.log(`📋 Current plastic retail price: $${disc.t_mps?.t_plastics?.val_retail_price || 'Unknown'}`);
    console.log(`📋 MPS override price: ${disc.t_mps?.val_override_retail_price ? '$' + disc.t_mps.val_override_retail_price : 'None'}`);
    console.log(`📋 Sold: ${disc.sold_date ? 'Yes' : 'No'}`);
    console.log(`📋 Uploaded to Shopify: ${disc.shopify_uploaded_at ? 'Yes' : 'No'}`);
    console.log(`📋 Generated SKU: D${disc.id}`);

    // Enqueue the task
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'update_disc_variant_price_on_shopify',
        payload: {
          id: discId,
          item_type: 'disc',
          new_retail_price: newRetailPrice
        },
        status: 'pending',
        scheduled_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        enqueued_by: 'testPlasticPriceChangeTasks.js'
      })
      .select()
      .single();

    if (taskError) {
      console.error('❌ Error enqueueing task:', taskError.message);
      return;
    }

    console.log(`✅ Successfully enqueued update_disc_variant_price_on_shopify task with ID: ${task.id}`);
    console.log(`⏰ Task scheduled at: ${task.scheduled_at}`);
    
    return task.id;

  } catch (error) {
    console.error('❌ Error in testUpdateDiscVariantPriceTask:', error.message);
  }
}

/**
 * Test enqueueing an update_disc_variant_price_on_shopify task for an OSL
 * @param {number} oslId - The OSL ID to test with
 * @param {number} newRetailPrice - The new retail price to set
 */
async function testUpdateOslVariantPriceTask(oslId, newRetailPrice) {
  console.log(`\n🧪 Testing update_disc_variant_price_on_shopify task for OSL ID: ${oslId}, new price: $${newRetailPrice}`);

  try {
    // First, let's check what OSL we're working with
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select(`
        id,
        mps_id,
        min_weight,
        max_weight,
        color_id,
        shopify_uploaded_at,
        t_mps (
          id,
          plastic_id,
          val_override_retail_price,
          t_plastics (
            plastic,
            val_retail_price
          )
        ),
        t_colors (
          color
        )
      `)
      .eq('id', oslId)
      .single();

    if (oslError) {
      console.error('❌ Error fetching OSL:', oslError.message);
      return;
    }

    if (!osl) {
      console.error(`❌ OSL with ID ${oslId} not found`);
      return;
    }

    console.log(`📋 OSL ID: ${osl.id}`);
    console.log(`📋 Weight Range: ${osl.min_weight}g - ${osl.max_weight}g`);
    console.log(`📋 Color: ${osl.t_colors?.color || 'Unknown'}`);
    console.log(`📋 Plastic: ${osl.t_mps?.t_plastics?.plastic || 'Unknown'}`);
    console.log(`📋 Current plastic retail price: $${osl.t_mps?.t_plastics?.val_retail_price || 'Unknown'}`);
    console.log(`📋 MPS override price: ${osl.t_mps?.val_override_retail_price ? '$' + osl.t_mps.val_override_retail_price : 'None'}`);
    console.log(`📋 Uploaded to Shopify: ${osl.shopify_uploaded_at ? 'Yes' : 'No'}`);
    console.log(`📋 Generated SKU: OS${osl.id}`);

    // Enqueue the task
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'update_disc_variant_price_on_shopify',
        payload: {
          id: oslId,
          item_type: 'osl',
          new_retail_price: newRetailPrice
        },
        status: 'pending',
        scheduled_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        enqueued_by: 'testPlasticPriceChangeTasks.js'
      })
      .select()
      .single();

    if (taskError) {
      console.error('❌ Error enqueueing task:', taskError.message);
      return;
    }

    console.log(`✅ Successfully enqueued update_disc_variant_price_on_shopify task with ID: ${task.id}`);
    console.log(`⏰ Task scheduled at: ${task.scheduled_at}`);

    return task.id;

  } catch (error) {
    console.error('❌ Error in testUpdateOslVariantPriceTask:', error.message);
  }
}

/**
 * Monitor task progress
 * @param {number} taskId - The task ID to monitor
 */
async function monitorTask(taskId) {
  console.log(`\n👀 Monitoring task ID: ${taskId}`);
  
  try {
    const { data: task, error } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', taskId)
      .single();

    if (error) {
      console.error('❌ Error fetching task:', error.message);
      return;
    }

    console.log(`📊 Task Status: ${task.status}`);
    console.log(`📊 Task Type: ${task.task_type}`);
    console.log(`📊 Created: ${task.created_at}`);
    console.log(`📊 Updated: ${task.updated_at}`);
    
    if (task.completed_at) {
      console.log(`📊 Completed: ${task.completed_at}`);
    }
    
    if (task.result) {
      console.log(`📊 Result:`, JSON.stringify(task.result, null, 2));
    }

  } catch (error) {
    console.error('❌ Error in monitorTask:', error.message);
  }
}

/**
 * Main test function
 */
async function main() {
  console.log('🚀 Starting plastic price change task tests...');
  
  // Example usage - replace these with actual IDs from your database
  const testPlasticId = 1; // Replace with a real plastic ID
  const testDiscId = 100000; // Replace with a real disc ID that's uploaded to Shopify
  const testOslId = 16560; // Replace with a real OSL ID that's uploaded to Shopify
  const testNewPrice = 19.99;

  console.log('\n📝 Instructions:');
  console.log('1. Replace testPlasticId with a real plastic ID from your t_plastics table');
  console.log('2. Replace testDiscId with a real disc ID that has shopify_uploaded_at set');
  console.log('3. Replace testOslId with a real OSL ID that has shopify_uploaded_at set');
  console.log('4. Make sure your task queue worker is running to process these tasks');
  console.log('5. Monitor the tasks using the provided task IDs');

  // Test plastic retail price change task
  const plasticTaskId = await testPlasticRetailPriceChangeTask(testPlasticId);

  // Test individual disc price update task
  const discTaskId = await testUpdateDiscVariantPriceTask(testDiscId, testNewPrice);

  // Test individual OSL price update task
  const oslTaskId = await testUpdateOslVariantPriceTask(testOslId, testNewPrice);

  // Monitor the tasks (you can run this separately later)
  if (plasticTaskId) {
    setTimeout(() => monitorTask(plasticTaskId), 5000);
  }

  if (discTaskId) {
    setTimeout(() => monitorTask(discTaskId), 5000);
  }

  if (oslTaskId) {
    setTimeout(() => monitorTask(oslTaskId), 5000);
  }

  console.log('\n✅ Test tasks enqueued. Check your task queue worker logs for processing details.');
}

// Run the tests
main().catch(console.error);
