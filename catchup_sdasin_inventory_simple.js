// catchup_sdasin_inventory_simple.js
// Simple script to catch up on missing t_inv_sdasin records using the existing reconcile function
// This is faster and uses the existing database function for consistency

import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

console.log('Environment loaded, starting script...');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Configuration
const BATCH_SIZE = 50;
const DRY_RUN = process.argv.includes('--dry-run');

console.log(`Starting SDASIN inventory catchup script (simple version)...`);
console.log(`Batch size: ${BATCH_SIZE}`);
console.log(`Dry run mode: ${DRY_RUN}`);

async function testConnection() {
  console.log('\n=== Testing Supabase connection ===');
  try {
    const { data, error } = await supabase
      .from('t_sdasins')
      .select('id')
      .limit(1);

    if (error) {
      console.error('Connection test failed:', error);
      throw error;
    }

    console.log('✅ Supabase connection successful');
    return true;
  } catch (error) {
    console.error('❌ Supabase connection failed:', error);
    throw error;
  }
}

async function findMissingSdasinInventoryRecords() {
  console.log('\n=== Finding missing t_inv_sdasin records ===');

  try {
    console.log('Fetching all SDASIN records...');
    // Get all SDASIN IDs
    const { data: allSdasins, error: sdasinError } = await supabase
      .from('t_sdasins')
      .select('id')
      .order('id');

    if (sdasinError) {
      console.error('Error fetching SDASIN records:', sdasinError);
      throw sdasinError;
    }

    console.log(`Found ${allSdasins.length} total SDASIN records`);

    console.log('Fetching existing inventory records...');
    // Get all existing inventory IDs
    const { data: existingInventory, error: invError } = await supabase
      .from('t_inv_sdasin')
      .select('id');

    if (invError) {
      console.error('Error fetching inventory records:', invError);
      throw invError;
    }

    console.log(`Found ${existingInventory.length} existing inventory records`);

    // Find missing IDs
    const existingIds = new Set(existingInventory.map(inv => inv.id));
    const missingSdasinIds = allSdasins
      .filter(sdasin => !existingIds.has(sdasin.id))
      .map(sdasin => sdasin.id);

    console.log(`Found ${missingSdasinIds.length} SDASIN records missing inventory records`);
    return missingSdasinIds;
  } catch (error) {
    console.error('Error in findMissingSdasinInventoryRecords:', error);
    throw error;
  }
}

async function runReconcileFunction() {
  console.log('\n=== Running reconcile_inv_sdasin_quantities function ===');
  
  if (DRY_RUN) {
    console.log('[DRY RUN] Would run reconcile_inv_sdasin_quantities() function');
    return true;
  }

  const { error } = await supabase.rpc('reconcile_inv_sdasin_quantities');

  if (error) {
    console.error('Error running reconcile function:', error);
    throw error;
  }

  console.log('Successfully ran reconcile_inv_sdasin_quantities() function');
  return true;
}

async function enqueueSdasinMatchingTasks(sdasinIds) {
  console.log('\n=== Enqueuing sdasin_updated_find_discs_to_match tasks ===');
  
  if (DRY_RUN) {
    console.log(`[DRY RUN] Would enqueue ${sdasinIds.length} sdasin_updated_find_discs_to_match tasks`);
    return sdasinIds.length;
  }

  let totalEnqueued = 0;
  const now = new Date().toISOString();
  
  // Process in batches to avoid overwhelming the task queue
  for (let i = 0; i < sdasinIds.length; i += BATCH_SIZE) {
    const batch = sdasinIds.slice(i, i + BATCH_SIZE);
    console.log(`Enqueuing batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(sdasinIds.length / BATCH_SIZE)} (${batch.length} tasks)`);
    
    // Stagger tasks by 2 seconds each to avoid overwhelming the worker
    const tasksToInsert = batch.map((id, index) => ({
      task_type: 'sdasin_updated_find_discs_to_match',
      payload: { id: id },
      status: 'pending',
      scheduled_at: new Date(Date.now() + (i + index) * 2000).toISOString(), // 2 second stagger
      created_at: now,
      enqueued_by: 'catchup_sdasin_inventory_simple_script'
    }));

    const { error } = await supabase
      .from('t_task_queue')
      .insert(tasksToInsert);

    if (error) {
      console.error(`Error enqueuing tasks for batch starting at index ${i}:`, error);
      throw error;
    }

    totalEnqueued += batch.length;
    console.log(`Enqueued ${batch.length} tasks (total: ${totalEnqueued}/${sdasinIds.length})`);
    
    // Small delay between batches to be gentle on the database
    if (i + BATCH_SIZE < sdasinIds.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  console.log(`Successfully enqueued ${totalEnqueued} sdasin_updated_find_discs_to_match tasks`);
  return totalEnqueued;
}

async function verifyResults() {
  console.log('\n=== Verifying results ===');
  
  // Check if there are still missing records
  const missingSdasinIds = await findMissingSdasinInventoryRecords();
  
  if (missingSdasinIds.length === 0) {
    console.log('✅ All t_sdasins now have corresponding t_inv_sdasin records');
  } else {
    console.log(`⚠️  Still ${missingSdasinIds.length} missing t_inv_sdasin records`);
  }

  // Check pending tasks
  const { data: pendingTasks, error } = await supabase
    .from('t_task_queue')
    .select('id')
    .eq('task_type', 'sdasin_updated_find_discs_to_match')
    .eq('status', 'pending');

  if (!error) {
    console.log(`📋 ${pendingTasks.length} sdasin_updated_find_discs_to_match tasks pending in queue`);
  }

  return missingSdasinIds.length;
}

async function main() {
  try {
    const startTime = Date.now();

    // Step 0: Test connection
    await testConnection();

    // Step 1: Find missing inventory records
    const missingSdasinIds = await findMissingSdasinInventoryRecords();
    
    if (missingSdasinIds.length === 0) {
      console.log('\n✅ No missing t_inv_sdasin records found. All caught up!');
      return;
    }

    console.log(`\n📊 Will process ${missingSdasinIds.length} SDASIN records`);
    console.log(`Sample IDs: ${missingSdasinIds.slice(0, 10).join(', ')}${missingSdasinIds.length > 10 ? '...' : ''}`);

    // Step 2: Use the existing reconcile function to create missing inventory records
    await runReconcileFunction();
    
    // Step 3: Enqueue matching tasks for all the SDASIN IDs
    const enqueuedCount = await enqueueSdasinMatchingTasks(missingSdasinIds);
    
    // Step 4: Verify results
    const stillMissing = await verifyResults();
    
    const endTime = Date.now();
    const durationSec = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log('\n=== Summary ===');
    console.log(`Missing SDASIN inventory records found: ${missingSdasinIds.length}`);
    console.log(`Reconcile function executed: ${!DRY_RUN ? 'Yes' : 'No (dry run)'}`);
    console.log(`sdasin_updated_find_discs_to_match tasks enqueued: ${enqueuedCount}`);
    console.log(`Records still missing after reconcile: ${stillMissing}`);
    console.log(`Total processing time: ${durationSec} seconds`);
    
    if (DRY_RUN) {
      console.log('\n⚠️  This was a DRY RUN. No changes were made.');
      console.log('Run without --dry-run to actually perform the operations.');
    } else {
      console.log('\n✅ Catchup completed successfully!');
      console.log('The task queue worker will now process the enqueued tasks to update inventory counts.');
      
      if (stillMissing > 0) {
        console.log(`⚠️  Note: ${stillMissing} records are still missing. You may need to investigate these manually.`);
      }
    }

  } catch (error) {
    console.error('\n❌ Error during catchup process:', error);
    process.exit(1);
  }
}

// Handle command line execution
console.log('Checking if script is run directly...');
console.log('import.meta.url:', import.meta.url);
console.log('process.argv[1]:', process.argv[1]);

// More robust check for direct execution
const isMainModule = process.argv[1] && (
  import.meta.url === `file://${process.argv[1]}` ||
  import.meta.url.endsWith(process.argv[1]) ||
  process.argv[1].endsWith('catchup_sdasin_inventory_simple.js')
);

console.log('isMainModule:', isMainModule);

if (isMainModule) {
  console.log('Running main function...');
  main().catch(console.error);
} else {
  console.log('Script imported as module, not running main()');
}

export { main };
