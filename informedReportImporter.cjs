/**
 * Informed Report Importer
 *
 * This script imports CSV reports from the local file system into Supabase tables.
 * It handles three report types:
 * 1. All Fields -> it_infor_all_fields
 * 2. Competition Landscape -> it_infor_competition_landscape
 * 3. No Buy Box -> it_infor_no_buy_box
 */

const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const { createClient } = require('@supabase/supabase-js');
const dotenv = require('dotenv');
const { REPORT_TYPES } = require('./informedReportDownloader.cjs');

// Load environment variables
dotenv.config();

// Constants
const REPORTS_DIR = path.join(__dirname, 'data', 'external data', 'informed');
const BATCH_SIZE = 500; // Number of records to insert in a single batch

// Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzc4NjU0MSwiZXhwIjoyMDQ5MzYyNTQxfQ.FQyPTgdBP83VhuykdlZkagHADc5nBmx7-yd0JYt5aP8';

console.log(`Using Supabase URL: ${supabaseUrl}`);
console.log(`Using Supabase Key: ${supabaseKey.substring(0, 10)}...`);

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Parse a CSV file and return an array of records
 * @param {string} filePath - Path to the CSV file
 * @returns {Promise<Array>} - Array of parsed records
 */
function parseCSV(filePath) {
    return new Promise((resolve, reject) => {
        const results = [];

        fs.createReadStream(filePath)
            .pipe(csv())
            .on('data', (data) => results.push(data))
            .on('end', () => {
                resolve(results);
            })
            .on('error', (error) => {
                reject(error);
            });
    });
}

/**
 * Truncate a table and insert new records
 * @param {string} tableName - Name of the table to truncate and insert into
 * @param {Array} records - Array of records to insert
 * @returns {Promise<Object>} - Result of the operation
 */
async function truncateAndInsert(tableName, records) {
    console.log(`Truncating table ${tableName} and inserting ${records.length} records`);

    try {
        // Try to truncate the table using the RPC function
        try {
            const { error: truncateError } = await supabase.rpc('truncate_table', { table_name: tableName });

            if (truncateError) {
                console.warn(`Warning: RPC truncate_table failed for ${tableName}:`, truncateError);
                console.log(`Attempting to truncate table ${tableName} using DELETE FROM instead...`);

                // If the RPC function fails, try using DELETE FROM instead
                const { error: deleteError } = await supabase.from(tableName).delete();

                if (deleteError) {
                    console.error(`Error deleting from table ${tableName}:`, deleteError);
                    return {
                        success: false,
                        tableName,
                        error: deleteError.message
                    };
                }
            }
        } catch (truncateError) {
            console.warn(`Warning: RPC truncate_table failed for ${tableName}:`, truncateError);
            console.log(`Attempting to truncate table ${tableName} using DELETE FROM instead...`);

            // If the RPC function fails, try using DELETE FROM instead
            const { error: deleteError } = await supabase.from(tableName).delete();

            if (deleteError) {
                console.error(`Error deleting from table ${tableName}:`, deleteError);
                return {
                    success: false,
                    tableName,
                    error: deleteError.message
                };
            }
        }

        // Insert records in batches
        let insertedCount = 0;

        for (let i = 0; i < records.length; i += BATCH_SIZE) {
            const batch = records.slice(i, i + BATCH_SIZE);
            const { error: insertError } = await supabase.from(tableName).insert(batch);

            if (insertError) {
                console.error(`Error inserting batch into ${tableName}:`, insertError);
                return {
                    success: false,
                    tableName,
                    error: insertError.message,
                    recordsInserted: insertedCount
                };
            }

            insertedCount += batch.length;
            console.log(`Inserted ${insertedCount}/${records.length} records into ${tableName}`);
        }

        return {
            success: true,
            tableName,
            recordCount: insertedCount
        };
    } catch (error) {
        console.error(`Error in truncateAndInsert for ${tableName}:`, error);
        return {
            success: false,
            tableName,
            error: error.message
        };
    }
}

/**
 * Import a single report into its corresponding table
 * @param {Object} report - Report configuration object
 * @returns {Promise<Object>} - Result of the import operation
 */
async function importReport(report) {
    const filePath = path.join(REPORTS_DIR, report.filename);

    console.log(`Importing ${report.type} report from ${filePath} to ${report.tableName}`);

    try {
        // Check if file exists
        if (!fs.existsSync(filePath)) {
            return {
                success: false,
                report: report.type,
                error: `File not found: ${filePath}`
            };
        }

        // Parse CSV
        const records = await parseCSV(filePath);

        if (records.length === 0) {
            return {
                success: false,
                report: report.type,
                error: 'No records found in CSV file'
            };
        }

        // Check if we can connect to Supabase
        try {
            const { data, error } = await supabase.from(report.tableName).select('*', { count: 'exact', head: true });

            if (error) {
                console.error(`Error connecting to Supabase table ${report.tableName}:`, error);
                return {
                    success: false,
                    report: report.type,
                    error: `Error connecting to Supabase: ${error.message}`
                };
            }

            console.log(`Successfully connected to Supabase table ${report.tableName}`);
        } catch (connectionError) {
            console.error(`Error connecting to Supabase:`, connectionError);
            return {
                success: false,
                report: report.type,
                error: `Error connecting to Supabase: ${connectionError.message}`
            };
        }

        // Truncate and insert
        const result = await truncateAndInsert(report.tableName, records);

        return {
            success: result.success,
            report: report.type,
            recordCount: result.recordCount,
            error: result.error
        };
    } catch (error) {
        console.error(`Error importing ${report.type} report:`, error);
        return {
            success: false,
            report: report.type,
            error: error.message
        };
    }
}

/**
 * Import all reports into their corresponding tables
 * @returns {Promise<Array>} - Results of all import operations
 */
async function importAllReports() {
    const results = [];

    for (const report of REPORT_TYPES) {
        try {
            const result = await importReport(report);
            results.push(result);
        } catch (error) {
            results.push({
                success: false,
                report: report.type,
                error: error.message
            });
        }
    }

    return results;
}

// Export functions for use in other modules
module.exports = {
    importAllReports,
    importReport
};

// If this script is run directly, import all reports
if (require.main === module) {
    importAllReports()
        .then(results => {
            console.log('Import results:', results);
            process.exit(0);
        })
        .catch(error => {
            console.error('Error importing reports:', error);
            process.exit(1);
        });
}
