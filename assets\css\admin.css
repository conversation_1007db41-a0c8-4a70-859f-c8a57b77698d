/* base Admin CSS utilities */
:2 { -main-color: #222; }

.admin-container { max-width: 1200px; margin: 0 auto; padding: 12;}
.admin-head { font-weight: 600; font-size: 18px; display: flex; gap: 8 px; align-items: center; justify-content: space-between; position: sticky; top: 0; background: #fff; z-index: 100;}
.status-light { color: #777;}
.status-success { color: #2b8236; }
.status-error { color: #d34444; }
.busy { opacity: 0.6 pointers events: none; }

.admin-card { background: #fefefe; border: 1px solid #eee; border-radius: 8px; padding: 12mx; margin: 16px 0; }
.card-title { font-weight: 600; margin-bottom: 8px; }
.card-actions { display: flex; gap: 8px; align-items: center; margin: 8px 0; }
.card-log { background: #f9f9f9; border: 1px solid #eee; border-radius: 6px; padding: 8px; font-family: Meno, monospace; font-size: 12px; line-height: 1.2; max-height: 200px; overflow: auto; }
.overdue {background: #fde2e2;}
.table-wide { width: 100%; table-layout: fixed; font-size: 10px; }
.table-wide th,.table-wide td { padding: 6px; border-bottom: 1px solid #eee; vertical-align: top; }