// apply_t_images_variant_ready_trigger_v2.js - Apply t_images variant ready trigger using execute_sql
import 'dotenv/config';
import fs from 'fs';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in environment');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

async function execSql(sql) {
  // Try different RPC function names used in this repo
  let res = await supabase.rpc('exec_sql', { sql_query: sql });
  if (res.error) {
    res = await supabase.rpc('execute_sql', { sql });
  }
  if (res.error) {
    res = await supabase.rpc('exec_sql', { sql_statement: sql });
  }
  return res;
}

async function main() {
  try {
    const triggerSql = fs.readFileSync('sql/triggers/t_images_enqueue_variant_ready_on_image_verified.sql', 'utf8');

    console.log('Applying t_images variant ready trigger using multiple RPC attempts...');
    let { data, error } = await execSql(triggerSql);
    if (error) {
      console.error('Error applying trigger SQL:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      
      // Try splitting into function and trigger parts
      console.log('\nTrying to split into function and trigger parts...');
      
      const functionPart = triggerSql.substring(
        triggerSql.indexOf('CREATE OR REPLACE FUNCTION'),
        triggerSql.indexOf('$$ LANGUAGE plpgsql;') + '$$ LANGUAGE plpgsql;'.length
      );
      
      const triggerPart = triggerSql.substring(
        triggerSql.indexOf('DROP TRIGGER IF EXISTS')
      );
      
      console.log('Executing function part...');
      let { error: funcError } = await execSql(functionPart);
      if (funcError) {
        console.error('Function creation error:', funcError);
      } else {
        console.log('Function created successfully');
        
        console.log('Executing trigger part...');
        let { error: trigError } = await execSql(triggerPart);
        if (trigError) {
          console.error('Trigger creation error:', trigError);
        } else {
          console.log('Trigger created successfully');
        }
      }
      
    } else {
      console.log('Applied trigger successfully.');
      if (data) {
        console.log('Result:', data);
      }
    }

    console.log('Done.');
  } catch (e) {
    console.error('Fatal error:', e?.message || e);
    process.exit(1);
  }
}

main();
