// testRproValidationRules.js - Test the new image and pricing validation rules
import { createClient } from '@supabase/supabase-js';
import { processCheckIfRproIsReadyTask } from './processCheckIfRproIsReadyTask.js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

// Mock functions for testing
async function updateTaskStatus(taskId, status, result = null) {
  console.log(`📝 Mock updateTaskStatus: Task ${taskId} -> ${status}`);
  if (result && result.issues && result.issues.length > 0) {
    console.log(`   Issues found: ${result.issues.length}`);
    result.issues.forEach(issue => console.log(`     - ${issue}`));
  }
  return true;
}

async function logError(message, context) {
  console.log(`❌ Mock logError: ${message} (Context: ${context})`);
  return true;
}

async function testValidationRules() {
  try {
    console.log('🧪 Testing RPRO validation rules (image + pricing)...');
    console.log('====================================================');

    // Get a sample record to use for testing
    const { data: sampleRecord, error: sampleError } = await supabase
      .from('imported_table_rpro')
      .select(`
        id, ivno, ivqtylaw, ivaux3, ivaux2,
        ivprcbtlis, ivprcbt_dollar, ivprcbtsal, ivprcbtliv,
        ivprcws_1, ivprcws_2, ivavgcd, ivprcmsrp, ivprcmap
      `)
      .limit(1)
      .single();

    if (sampleError) {
      console.error('❌ Error fetching sample record:', sampleError.message);
      return;
    }

    console.log('📋 Original record:');
    console.log(`  ID: ${sampleRecord.id}, IVNO: ${sampleRecord.ivno}`);
    console.log(`  Qty: ${sampleRecord.ivqtylaw}, Bin: ${sampleRecord.ivaux3 || 'null'}, Image: ${sampleRecord.ivaux2 || 'null'}`);
    console.log(`  Prices: List=${sampleRecord.ivprcbtlis}, Regular=${sampleRecord.ivprcbt_dollar}, Sale=${sampleRecord.ivprcbtsal}, Live=${sampleRecord.ivprcbtliv}`);

    // Store original values for restoration
    const originalValues = { ...sampleRecord };

    // Test 1: In-stock item with missing image
    console.log('\n🧪 Test 1: In-stock item with missing image');
    console.log('============================================');
    
    await supabase
      .from('imported_table_rpro')
      .update({
        ivqtylaw: 5,
        ivaux3: 'A1',
        ivaux2: 'wrong image',
        todo: null
      })
      .eq('id', sampleRecord.id);

    const mockTask1 = {
      id: 999991,
      task_type: 'check_if_rpro_is_ready',
      payload: { id: sampleRecord.id }
    };

    await processCheckIfRproIsReadyTask(mockTask1, { supabase, updateTaskStatus, logError });

    // Test 2: Out-of-stock item with missing image
    console.log('\n🧪 Test 2: Out-of-stock item with missing image');
    console.log('===============================================');
    
    await supabase
      .from('imported_table_rpro')
      .update({
        ivqtylaw: 0,
        ivaux2: 'wrong image',
        todo: null
      })
      .eq('id', sampleRecord.id);

    const mockTask2 = {
      id: 999992,
      task_type: 'check_if_rpro_is_ready',
      payload: { id: sampleRecord.id }
    };

    await processCheckIfRproIsReadyTask(mockTask2, { supabase, updateTaskStatus, logError });

    // Test 3: Pricing validation - Regular Price = 0
    console.log('\n🧪 Test 3: Pricing validation - Regular Price = 0');
    console.log('==================================================');
    
    await supabase
      .from('imported_table_rpro')
      .update({
        ivqtylaw: 5,
        ivaux2: '9 White Square Big',
        ivaux3: 'A1',
        ivprcbt_dollar: 0, // Regular Price = 0 (should fail)
        ivprcbtliv: 10,
        todo: null
      })
      .eq('id', sampleRecord.id);

    const mockTask3 = {
      id: 999993,
      task_type: 'check_if_rpro_is_ready',
      payload: { id: sampleRecord.id }
    };

    await processCheckIfRproIsReadyTask(mockTask3, { supabase, updateTaskStatus, logError });

    // Test 4: Pricing validation - Live Price = 0
    console.log('\n🧪 Test 4: Pricing validation - Live Price = 0');
    console.log('===============================================');
    
    await supabase
      .from('imported_table_rpro')
      .update({
        ivprcbt_dollar: 20, // Regular Price
        ivprcbtliv: 0, // Live Price = 0 (should fail)
        todo: null
      })
      .eq('id', sampleRecord.id);

    const mockTask4 = {
      id: 999994,
      task_type: 'check_if_rpro_is_ready',
      payload: { id: sampleRecord.id }
    };

    await processCheckIfRproIsReadyTask(mockTask4, { supabase, updateTaskStatus, logError });

    // Test 5: Pricing validation - Sale Price >= Regular Price
    console.log('\n🧪 Test 5: Pricing validation - Sale Price >= Regular Price');
    console.log('===========================================================');
    
    await supabase
      .from('imported_table_rpro')
      .update({
        ivprcbt_dollar: 20, // Regular Price
        ivprcbtsal: 25, // Sale Price > Regular Price (should fail)
        ivprcbtliv: 20,
        todo: null
      })
      .eq('id', sampleRecord.id);

    const mockTask5 = {
      id: 999995,
      task_type: 'check_if_rpro_is_ready',
      payload: { id: sampleRecord.id }
    };

    await processCheckIfRproIsReadyTask(mockTask5, { supabase, updateTaskStatus, logError });

    // Test 6: Valid record (should pass all checks)
    console.log('\n🧪 Test 6: Valid record (should pass all checks)');
    console.log('=================================================');
    
    await supabase
      .from('imported_table_rpro')
      .update({
        ivqtylaw: 5,
        ivaux3: 'A1',
        ivaux2: '9 White Square Big',
        ivprcbtlis: 25,
        ivprcbt_dollar: 20,
        ivprcbtsal: 15,
        ivprcbtliv: 20,
        ivprcws_1: 15,
        ivprcws_2: 13.5, // 90% of wholesale 1
        ivavgcd: 10,
        ivprcmsrp: 30,
        ivprcmap: 25,
        todo: null
      })
      .eq('id', sampleRecord.id);

    const mockTask6 = {
      id: 999996,
      task_type: 'check_if_rpro_is_ready',
      payload: { id: sampleRecord.id }
    };

    await processCheckIfRproIsReadyTask(mockTask6, { supabase, updateTaskStatus, logError });

    // Test 7: Multiple pricing issues
    console.log('\n🧪 Test 7: Multiple pricing issues');
    console.log('===================================');
    
    await supabase
      .from('imported_table_rpro')
      .update({
        ivqtylaw: 0, // Out of stock
        ivaux2: 'wrong image',
        ivprcbt_dollar: 0, // Regular Price = 0
        ivprcbtliv: 0, // Live Price = 0
        ivprcws_2: 5, // Wholesale 2 not 90% of wholesale 1
        todo: null
      })
      .eq('id', sampleRecord.id);

    const mockTask7 = {
      id: 999997,
      task_type: 'check_if_rpro_is_ready',
      payload: { id: sampleRecord.id }
    };

    await processCheckIfRproIsReadyTask(mockTask7, { supabase, updateTaskStatus, logError });

    // Restore original values
    console.log('\n🔄 Restoring original values...');
    await supabase
      .from('imported_table_rpro')
      .update(originalValues)
      .eq('id', sampleRecord.id);

    console.log('✅ Original values restored');
    console.log('\n🎉 All validation rule tests completed!');

  } catch (err) {
    console.error('❌ Exception:', err.message);
    console.error(err.stack);
  }
}

testValidationRules();
