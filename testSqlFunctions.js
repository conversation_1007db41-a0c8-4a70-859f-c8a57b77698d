// testSqlFunctions.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import minimist from 'minimist';

// Parse command-line arguments
const args = minimist(process.argv.slice(2));
const tableName = args.table || 't_discs';
const recordId = args.id;
const action = args.action || 'debug'; // debug, insert, delete

if (!recordId) {
  console.error('Missing required --id parameter');
  process.exit(1);
}

// Create a Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log(`Testing SQL functions for ${tableName} with record_id=${recordId}, action=${action}`);
    
    if (action === 'debug' || action === 'all') {
      console.log('Running debug function...');
      const { data: debugResult, error: debugError } = await supabase
        .rpc('debug_insert_t_images', {
          p_table_name: tableName,
          p_record_id: parseInt(recordId),
          p_created_by: 'system'
        });
        
      if (debugError) {
        console.error(`Debug error: ${debugError.message}`);
        console.error(`Full error: ${JSON.stringify(debugError)}`);
      } else {
        console.log(`Debug result: ${JSON.stringify(debugResult, null, 2)}`);
      }
    }
    
    if (action === 'insert' || action === 'all') {
      console.log('Running insert function...');
      const { data: insertResult, error: insertError } = await supabase
        .rpc('direct_insert_t_images', {
          p_table_name: tableName,
          p_record_id: parseInt(recordId),
          p_created_by: 'system'
        });
        
      if (insertError) {
        console.error(`Insert error: ${insertError.message}`);
        console.error(`Full error: ${JSON.stringify(insertError)}`);
      } else {
        console.log(`Insert result: ${JSON.stringify(insertResult, null, 2)}`);
      }
    }
    
    if (action === 'delete' || action === 'all') {
      console.log('Running delete function...');
      const { data: deleteResult, error: deleteError } = await supabase
        .rpc('direct_delete_t_images', {
          p_table_name: tableName,
          p_record_id: parseInt(recordId)
        });
        
      if (deleteError) {
        console.error(`Delete error: ${deleteError.message}`);
        console.error(`Full error: ${JSON.stringify(deleteError)}`);
      } else {
        console.log(`Delete result: ${JSON.stringify(deleteResult, null, 2)}`);
      }
    }
    
    if (action === 'raw' || action === 'all') {
      console.log('Running raw SQL function...');
      const { data: rawResult, error: rawError } = await supabase
        .rpc('execute_sql', {
          sql: `SELECT * FROM t_images WHERE table_name = '${tableName}' AND record_id = ${recordId}`
        });
        
      if (rawError) {
        console.error(`Raw SQL error: ${rawError.message}`);
        console.error(`Full error: ${JSON.stringify(rawError)}`);
      } else {
        console.log(`Raw SQL result: ${JSON.stringify(rawResult, null, 2)}`);
      }
    }
  } catch (err) {
    console.error(`Unexpected error: ${err.message}`);
    process.exit(1);
  }
}

main();
