import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

async function main() {
  // Initialize Supabase client.
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_KEY;
  const supabase = createClient(supabaseUrl, supabaseKey);

  // Compute the timestamp for three hours ago.
  const threeHoursAgo = new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString();
  console.log(`INFO: Three hours ago: ${threeHoursAgo}`);

  // Query the view v_reconcile_osl_to_veeqo for 1000 records where:
  //   - veeqo_qty_updated_at is null OR
  //   - veeqo_qty_updated_at is older than three hours.
  const { data: reconcileRecords, error: reconcileError } = await supabase
    .from('v_reconcile_osl_to_veeqo')
    .select('*')
    .or(`veeqo_qty_updated_at.is.null,veeqo_qty_updated_at.lt.${threeHoursAgo}`)
    .limit(1000);

  if (reconcileError) {
    console.error('Error fetching v_reconcile_osl_to_veeqo:', reconcileError);
    process.exit(1);
  }

  if (!reconcileRecords || reconcileRecords.length === 0) {
    console.log('No records found in v_reconcile_osl_to_veeqo that match the condition.');
    process.exit(0);
  }

  console.log(`INFO: Found ${reconcileRecords.length} record(s) to process.`);

  // Process each record.
  for (const record of reconcileRecords) {
    const productId = record.product_id;
    console.log(`Processing product_id: ${productId}`);

    // Look up the corresponding record in t_order_sheet_lines where veeqo_id equals productId.
    const { data: orderSheetRecord, error: orderSheetError } = await supabase
      .from('t_order_sheet_lines')
      .select('id')
      .eq('veeqo_id', productId)
      .maybeSingle();

    if (orderSheetError) {
      console.error(`Error fetching t_order_sheet_lines for product_id ${productId}:`, orderSheetError);
      continue;
    }

    if (!orderSheetRecord) {
      console.log(`No t_order_sheet_lines record found for product_id ${productId}`);
      continue;
    }

    const orderSheetId = orderSheetRecord.id;
    console.log(`Found t_order_sheet_lines record with id: ${orderSheetId}`);

    // Look up the matching record in t_inv_osl using the orderSheetId.
    const { data: invRecord, error: invError } = await supabase
      .from('t_inv_osl')
      .select('id')
      .eq('id', orderSheetId)
      .maybeSingle();

    if (invError) {
      console.error(`Error fetching t_inv_osl for orderSheetId ${orderSheetId}:`, invError);
      continue;
    }

    if (!invRecord) {
      console.log(`No t_inv_osl record found for orderSheetId ${orderSheetId}`);
      continue;
    }

    // Update t_inv_osl: set edit_this_to_refresh_veeqo_qty to the current timestamp.
    const nowTimestamp = new Date().toISOString();
    const { error: updateError } = await supabase
      .from('t_inv_osl')
      .update({ edit_this_to_refresh_veeqo_qty: nowTimestamp })
      .eq('id', orderSheetId);

    if (updateError) {
      console.error(`Error updating t_inv_osl for orderSheetId ${orderSheetId}:`, updateError);
    } else {
      console.log(`Successfully updated t_inv_osl record ${orderSheetId} with edit_this_to_refresh_veeqo_qty: ${nowTimestamp}`);
    }
  }

  // Stop after processing 5 records.
  process.exit(0);
}

main();
