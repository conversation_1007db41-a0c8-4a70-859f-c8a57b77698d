-- Backfill: enqueue check_if_product_variant_is_ready for variants not uploaded to Shopify
-- Schedules immediately, avoids duplicate pending/processing tasks per variant id

DO $$
DECLARE
  v_enqueued_count integer := 0;
BEGIN
  INSERT INTO public.t_task_queue (
    task_type,
    payload,
    status,
    scheduled_at,
    created_at,
    enqueued_by
  )
  SELECT
    'check_if_product_variant_is_ready',
    jsonb_build_object('id', pv.id),
    'pending',
    NOW(),
    NOW(),
    'catchup:check_if_product_variant_is_ready'
  FROM public.t_product_variants pv
  WHERE pv.uploaded_to_shopify_at IS NULL
    AND NOT EXISTS (
      SELECT 1
      FROM public.t_task_queue tq
      WHERE tq.task_type = 'check_if_product_variant_is_ready'
        AND (tq.status = 'pending' OR tq.status = 'processing')
        AND (tq.payload->>'id')::INT = pv.id
    );

  GET DIAGNOSTICS v_enqueued_count = ROW_COUNT;
  RAISE NOTICE 'Enqueued % check_if_product_variant_is_ready tasks', v_enqueued_count;
END $$;

