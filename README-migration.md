# Image Verification Data Migration

This script migrates image verification data from the `t_images` table to the `t_discs` table.

## Idempotent Migration

The script is designed to be truly idempotent:

1. It uses a JOIN query to find only discs that have both:
   - `t_discs.image_verified_notes` is null
   - A matching record in `t_images` with `table_name = 't_discs'`
2. It double-checks each disc before updating to ensure notes are still null
3. It adds "Migrated from t_images on [timestamp]" to the notes field
4. This ensures that running the script multiple times will only process new records

## Efficient Query Strategy

The script uses two different approaches to find records to migrate:

1. **Primary Method**: A direct JOIN query that finds discs without notes that have matching t_images records
2. **Fallback Method**: If the JOIN query fails (e.g., if the execute_sql RPC isn't available), it falls back to separate queries

## Test Run Instructions

The current version of the script is set up as a test run that will only process 10 records. This allows you to verify that the migration works correctly before running it on the entire dataset.

### Prerequisites

1. Make sure you have Node.js installed
2. Make sure you have a `.env` file with your Supabase credentials:
   ```
   SUPABASE_URL=your_supabase_url
   SUPABASE_KEY=your_supabase_key
   ```

### Running the Test Migration

1. Install dependencies:
   ```
   npm install
   ```

2. Run the test migration:
   ```
   node migrateImageVerificationData.js
   ```

3. Check the console output to verify that:
   - The column verification passed
   - The records were migrated successfully
   - The verification shows the updated records

## Full Migration

Once you've verified that the test migration works correctly, you can modify the script to process all records:

1. Open `migrateImageVerificationData.js`
2. Remove the `.limit(10)` from the query that fetches records
3. Update the batch size if needed (currently set to 10 for the test run)
4. Run the full migration:
   ```
   node migrateImageVerificationData.js
   ```

## Troubleshooting

If you encounter any issues:

1. **Column name mismatches**: The script checks for required columns in both tables. If there are any mismatches, it will warn you but continue with the migration. Check the column names and update the script if needed.

2. **Database connection issues**: Make sure your `.env` file has the correct Supabase credentials.

3. **Permissions issues**: Make sure your Supabase key has the necessary permissions to read from `t_images` and update `t_discs`.

4. **Data type issues**: If there are data type mismatches between the columns in `t_images` and `t_discs`, you may need to modify the script to handle the conversion.

## Verification

After running the migration, you can verify that the data was migrated correctly by:

1. Checking the console output, which shows a sample of updated records
2. Querying the database directly to compare the data in `t_images` and `t_discs`
