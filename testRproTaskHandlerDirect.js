// testRproTaskHandlerDirect.js - Test the check_if_rpro_is_ready task handler directly
import { createClient } from '@supabase/supabase-js';
import { processCheckIfRproIsReadyTask } from './processCheckIfRproIsReadyTask.js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

// Mock functions for testing
async function updateTaskStatus(taskId, status, result = null) {
  console.log(`📝 Mock updateTaskStatus: Task ${taskId} -> ${status}`);
  if (result) {
    console.log(`   Result: ${JSON.stringify(result, null, 2)}`);
  }
  return true;
}

async function logError(message, context) {
  console.log(`❌ Mock logError: ${message} (Context: ${context})`);
  return true;
}

async function testTaskHandlerDirect() {
  try {
    console.log('🧪 Testing check_if_rpro_is_ready task handler directly...');
    console.log('========================================================');

    // Get a sample RPRO record
    const { data: sampleRecord, error: sampleError } = await supabase
      .from('imported_table_rpro')
      .select('id, ivno, ivqtylaw, ivaux3, todo')
      .limit(1)
      .single();

    if (sampleError) {
      console.error('❌ Error fetching sample record:', sampleError.message);
      return;
    }

    console.log('📋 Sample record to test with:');
    console.log(`  ID: ${sampleRecord.id}, IVNO: ${sampleRecord.ivno}`);
    console.log(`  Qty: ${sampleRecord.ivqtylaw}, Bin: ${sampleRecord.ivaux3 || 'null'}`);
    console.log(`  Current Todo: ${sampleRecord.todo || 'null'}`);

    // Clear the todo field first
    console.log('\n🧹 Clearing todo field...');
    const { error: clearError } = await supabase
      .from('imported_table_rpro')
      .update({ todo: null })
      .eq('id', sampleRecord.id);

    if (clearError) {
      console.error('❌ Error clearing todo:', clearError.message);
      return;
    }

    // Create a mock task
    const mockTask = {
      id: 999999,
      task_type: 'check_if_rpro_is_ready',
      payload: { id: sampleRecord.id }
    };

    console.log('\n🚀 Running task handler directly...');
    console.log('===================================');

    // Run the task handler
    await processCheckIfRproIsReadyTask(mockTask, {
      supabase,
      updateTaskStatus,
      logError
    });

    console.log('\n🔍 Checking results...');
    
    // Check the updated record
    const { data: updatedRecord, error: updateError } = await supabase
      .from('imported_table_rpro')
      .select('id, ivno, ivqtylaw, ivaux3, todo')
      .eq('id', sampleRecord.id)
      .single();

    if (updateError) {
      console.error('❌ Error fetching updated record:', updateError.message);
      return;
    }

    console.log('\n📋 Updated record:');
    console.log(`  ID: ${updatedRecord.id}, IVNO: ${updatedRecord.ivno}`);
    console.log(`  Qty: ${updatedRecord.ivqtylaw}, Bin: ${updatedRecord.ivaux3 || 'null'}`);
    console.log(`  Updated Todo: ${updatedRecord.todo || 'null'}`);

    // Analyze the result
    console.log('\n📊 Analysis:');
    console.log('============');
    
    if (updatedRecord.todo) {
      if (updatedRecord.todo.includes('No Issues Found')) {
        console.log('✅ Record marked as ready (no issues found)');
        
        // Check if this is correct
        const quantity = parseFloat(updatedRecord.ivqtylaw) || 0;
        const binSection = updatedRecord.ivaux3;
        const shouldHaveIssue = quantity > 0 && (!binSection || binSection.trim() === '');
        
        if (shouldHaveIssue) {
          console.log('⚠️  WARNING: Record should have had an issue but was marked as ready');
          console.log(`   Quantity: ${quantity} > 0 but bin section is: ${binSection || 'null'}`);
        } else {
          console.log('✅ Correct: Record has no issues');
          if (quantity <= 0) {
            console.log(`   Reason: Quantity (${quantity}) is not > 0, so bin section not required`);
          } else {
            console.log(`   Reason: Quantity (${quantity}) > 0 and bin section (${binSection}) is present`);
          }
        }
      } else {
        console.log('⚠️  Record marked as not ready with issues:');
        console.log(`   Issues: ${updatedRecord.todo}`);
        
        // Check if this is correct
        const quantity = parseFloat(updatedRecord.ivqtylaw) || 0;
        const binSection = updatedRecord.ivaux3;
        const shouldHaveIssue = quantity > 0 && (!binSection || binSection.trim() === '');
        
        if (shouldHaveIssue) {
          console.log('✅ Correct: Record correctly identified issues');
        } else {
          console.log('⚠️  WARNING: Record marked as having issues but should be ready');
        }
      }
    } else {
      console.log('❌ Todo field was not updated - task handler may have failed');
    }

    console.log('\n🎉 Direct test completed!');

  } catch (err) {
    console.error('❌ Exception:', err.message);
    console.error(err.stack);
  }
}

testTaskHandlerDirect();
