// investigate_osl.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function investigateOSL() {
  // Get the OSL details
  const { data: oslData, error: oslError } = await supabase
    .from('t_order_sheet_lines')
    .select('*')
    .eq('id', 18698)
    .single();
  
  if (oslError) {
    console.error('OSL Error:', oslError);
    return;
  }
  
  console.log('OSL Details:');
  console.log('ID:', oslData.id);
  console.log('MPS ID:', oslData.mps_id);
  console.log('Color ID:', oslData.color_id);
  console.log('Min Weight:', oslData.min_weight);
  console.log('Max Weight:', oslData.max_weight);
  
  // Get the MPS details
  const { data: mpsData, error: mpsError } = await supabase
    .from('t_mps')
    .select('*, t_molds!inner(mold, brand_id, t_brands!inner(brand)), t_plastics!inner(plastic), t_stamps!inner(stamp)')
    .eq('id', oslData.mps_id)
    .single();
  
  if (mpsError) {
    console.error('MPS Error:', mpsError);
    return;
  }
  
  console.log('\nMPS Details:');
  console.log('Brand:', mpsData.t_molds.t_brands.brand);
  console.log('Plastic:', mpsData.t_plastics.plastic);
  console.log('Mold:', mpsData.t_molds.mold);
  console.log('Stamp:', mpsData.t_stamps.stamp);
  
  // Get the color details
  const { data: colorData, error: colorError } = await supabase
    .from('t_colors')
    .select('*')
    .eq('id', oslData.color_id)
    .single();
  
  if (colorError) {
    console.error('Color Error:', colorError);
    return;
  }
  
  console.log('\nColor Details:');
  console.log('Color:', colorData.color);
  
  // Generate the handle that would be used
  function generateMPSHandle(brand, plastic, mold, stamp) {
    let base = `${brand}-${plastic}-${mold}-${stamp}`.toLowerCase();
    base = base
      .replace(/ /g, '-')
      .replace(/'/g, '')
      .replace(/\//g, '')
      .replace(/\./g, '-')
      .replace(/&/g, '-')
      .replace(/\(/g, '')
      .replace(/\)/g, '')
      .replace(/"/g, '')
      .replace(/%/g, '')
      .replace(/#/g, '')
      .replace(/-\$/g, '');
    while (base.includes('--')) {
      base = base.replace(/--/g, '-');
    }
    return base;
  }
  
  const handle = generateMPSHandle(
    mpsData.t_molds.t_brands.brand,
    mpsData.t_plastics.plastic,
    mpsData.t_molds.mold,
    mpsData.t_stamps.stamp
  );
  
  console.log('\nGenerated Handle:', handle);
  console.log('\nOption Values that would be used:');
  console.log('Option 1 (Weight Range):', `${oslData.min_weight}g-${oslData.max_weight}g`);
  console.log('Option 2 (Mold):', mpsData.t_molds.mold?.trim() || 'Unknown Mold');
  console.log('Option 3 (Color):', colorData.color?.trim() || 'Unknown Color');
}

investigateOSL().catch(console.error);
