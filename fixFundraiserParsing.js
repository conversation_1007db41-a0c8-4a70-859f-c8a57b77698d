import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

// Enhanced parsing function for fundraiser items
function parseFundraiserItem(description) {
    // Handle "Fundraiser - Ben <PERSON> Z Jawbreaker Thrasher (msrp $19.99, cost $10.00)"
    if (description.includes('Fundraiser - <PERSON>')) {
        const match = description.match(/Fundraiser - <PERSON> (.+?) (Thrasher|Buzzz|[A-Z][a-z]+)\s*\(/);
        if (match) {
            const plasticPart = match[1].trim(); // "Z Jawbreaker" or "Big Z"
            const moldName = match[2]; // "Thrasher" or "Buzzz"
            
            let plasticName = 'Unknown';
            if (plasticPart.includes('Z Jawbreaker')) {
                plasticName = 'Elite Z Jawbreaker';
            } else if (plasticPart.includes('Big Z')) {
                plasticName = 'Big Z Collection';
            }
            
            return {
                mold_name: moldName,
                plastic_name: plasticName,
                stamp_name: 'Live Free - Be Happy - Funky Ben Askren Fundraiser'
            };
        }
    }
    
    return null;
}

async function fixFundraiserParsing() {
    try {
        console.log('🔧 Fixing fundraiser section parsing...\n');
        
        // 1. First, mark the header row (22) as not orderable
        console.log('1. Fixing header row (22)...');
        const { data: headerRows, error: headerError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .update({ 
                is_orderable: false,
                excel_column: null,
                excel_mapping_key: null
            })
            .eq('excel_row_hint', 22)
            .ilike('vendor_description', '%LIMITED RELEASE - Fundraiser for Ben Askren%')
            .select();

        if (headerError) {
            console.error('❌ Error fixing header row:', headerError);
        } else {
            console.log(`✅ Fixed ${headerRows?.length || 0} header records`);
        }

        // 2. Fix the fundraiser items in rows 25 and 28
        console.log('\n2. Fixing fundraiser items...');
        
        // Get the current fundraiser records
        const { data: fundraiserRecords, error: fundraiserError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*')
            .in('excel_row_hint', [25, 28])
            .ilike('vendor_description', '%Fundraiser - Ben Askren%');

        if (fundraiserError) {
            console.error('❌ Error querying fundraiser records:', fundraiserError);
            return;
        }

        console.log(`✅ Found ${fundraiserRecords.length} fundraiser records to fix`);

        // Process each fundraiser record
        for (const record of fundraiserRecords) {
            console.log(`\nProcessing: ${record.vendor_description}`);
            
            // Parse the fundraiser item
            const parsed = parseFundraiserItem(record.vendor_description);
            if (!parsed) {
                console.log('❌ Could not parse fundraiser item');
                continue;
            }

            console.log(`✅ Parsed: ${parsed.plastic_name} ${parsed.mold_name} (${parsed.stamp_name})`);

            // Update the record with correct parsing and column mapping
            const updates = {
                mold_name: parsed.mold_name,
                plastic_name: parsed.plastic_name,
                stamp_name: parsed.stamp_name,
                excel_column: 'A', // Fundraiser items go in column A
                excel_mapping_key: `SPECIAL|${record.vendor_description}|Order Qty`,
                raw_line_type: 'SPECIAL',
                raw_model: record.vendor_description
            };

            const { error: updateError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .update(updates)
                .eq('id', record.id);

            if (updateError) {
                console.error(`❌ Error updating record ${record.id}:`, updateError);
            } else {
                console.log(`✅ Updated record ${record.id}`);
            }
        }

        // 3. Delete the incorrectly mapped weight column records for fundraiser items
        console.log('\n3. Cleaning up incorrectly mapped weight column records...');
        
        const { data: deletedRecords, error: deleteError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .delete()
            .in('excel_row_hint', [25, 28])
            .in('excel_column', ['L', 'M', 'N', 'O', 'P', 'Q', 'R'])
            .ilike('vendor_description', '%Fundraiser - Ben Askren%')
            .select();

        if (deleteError) {
            console.error('❌ Error deleting incorrect records:', deleteError);
        } else {
            console.log(`✅ Deleted ${deletedRecords?.length || 0} incorrectly mapped records`);
        }

        // 4. Verify the fixes
        console.log('\n4. Verifying fixes...');
        
        const { data: verifyData, error: verifyError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, mold_name, plastic_name, stamp_name, is_orderable, vendor_description')
            .gte('excel_row_hint', 22)
            .lte('excel_row_hint', 30)
            .order('excel_row_hint, excel_column');

        if (verifyError) {
            console.error('❌ Error verifying fixes:', verifyError);
        } else {
            console.log(`✅ Verification complete - ${verifyData.length} records found:`);
            verifyData.forEach((record, index) => {
                console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.mold_name} | ${record.plastic_name} | Orderable: ${record.is_orderable}`);
            });
        }

        console.log('\n🎉 Fundraiser parsing fixes completed!');
        
    } catch (error) {
        console.error('❌ Fix failed:', error.message);
    }
}

fixFundraiserParsing().catch(console.error);
