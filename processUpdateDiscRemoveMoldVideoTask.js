// processUpdateDiscRemoveMoldVideoTask.js
// Update a Shopify product for a disc to remove video template and clear videourl metafield

import fetch from 'node-fetch';

const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

async function shopifyGraphQLRequest(query, variables = {}) {
  const response = await fetch(shopifyEndpoint, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', 'X-Shopify-Access-Token': shopifyAccessToken },
    body: JSON.stringify({ query, variables })
  });
  const result = await response.json();
  if (result.errors) {
    throw new Error(`Shopify GraphQL errors: ${JSON.stringify(result.errors)}`);
  }
  return result.data;
}

async function findProductByDiscId(discId) {
  const sku = `D${discId}`;
  const query = `
    query getVariantBySku($query: String!) {
      productVariants(first: 1, query: $query) {
        edges { node { id sku product { id handle } } }
      }
    }
  `;
  const data = await shopifyGraphQLRequest(query, { query: `sku:${sku}` });
  const edge = data?.productVariants?.edges?.[0];
  if (!edge) return null;
  const productGid = edge.node.product.id; // gid://shopify/Product/123
  const numericId = productGid.split('/').pop();
  const handle = edge.node.product.handle;
  return { productGid, productId: numericId, handle };
}

async function updateProductTemplate(productId, templateSuffix) {
  const productsRest = shopifyEndpoint.replace('graphql.json', 'products');
  const url = `${productsRest}/${productId}.json`;
  const payload = { product: { id: productId, template_suffix: templateSuffix } };
  const res = await fetch(url, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json', 'X-Shopify-Access-Token': shopifyAccessToken },
    body: JSON.stringify(payload)
  });
  if (!res.ok) {
    const text = await res.text();
    throw new Error(`Failed to update product template: ${res.status} ${text}`);
  }
}

async function removeVideoUrlMetafield(productGid) {
  // There is no delete-by-key GraphQL shortcut; do a list query for the specific key then delete it
  const listQuery = `
    query getVideoMetafield($ownerId: ID!) {
      node(id: $ownerId) {
        ... on Product { metafields(first: 10, namespace: "my_fields", keys: ["videourl"]) { edges { node { id } } } }
      }
    }
  `;
  const listData = await shopifyGraphQLRequest(listQuery, { ownerId: productGid });
  const edges = listData?.node?.metafields?.edges || [];
  if (!edges.length) return; // nothing to delete

  const ids = edges.map(e => e.node.id);
  const mutation = `
    mutation metafieldsDelete($metafields: [MetafieldsDeleteInput!]!) {
      metafieldsDelete(metafields: $metafields) {
        deletedMetafields { id }
        userErrors { field message }
      }
    }
  `;
  const vars = { metafields: ids.map(id => ({ id })) };
  const delData = await shopifyGraphQLRequest(mutation, vars);
  const errs = delData?.metafieldsDelete?.userErrors;
  if (errs && errs.length) {
    throw new Error(`metafieldsDelete userErrors: ${JSON.stringify(errs)}`);
  }
}

export default async function processUpdateDiscRemoveMoldVideoTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processUpdateDiscRemoveMoldVideoTask] Processing task ${task.id}`);
  try {
    const payload = typeof task.payload === 'string' ? JSON.parse(task.payload) : task.payload || {};
    const discId = payload.id;
    if (!discId) throw new Error('Missing disc id');

    await updateTaskStatus(task.id, 'processing');

    // Ensure disc is uploaded and unsold
    const { data: disc, error: discError } = await supabase
      .from('t_discs')
      .select('id, sold_date, shopify_uploaded_at')
      .eq('id', discId)
      .maybeSingle();
    if (discError) throw new Error(`DB error fetching disc: ${discError.message}`);
    if (!disc || disc.sold_date !== null || disc.shopify_uploaded_at == null) {
      await updateTaskStatus(task.id, 'completed', { message: 'Disc not eligible (not uploaded or sold)', disc_id: discId });
      return;
    }

    const product = await findProductByDiscId(discId);
    if (!product) throw new Error('Shopify product not found for disc');

    await updateProductTemplate(product.productId, 'disc-with-image');
    await removeVideoUrlMetafield(product.productGid);

    await updateTaskStatus(task.id, 'completed', {
      message: 'Reverted product template and removed videourl metafield',
      disc_id: discId,
      product_id: product.productId
    });
  } catch (err) {
    console.error('[processUpdateDiscRemoveMoldVideoTask] Error:', err);
    await logError(err.message, 'processUpdateDiscRemoveMoldVideoTask');
    await updateTaskStatus(task.id, 'error', { message: err.message });
  }
}

