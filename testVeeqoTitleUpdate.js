import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ VEEQO_API_KEY environment variable is not set');
  process.exit(1);
}

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    console.log(`📡 Making ${method} request to: ${endpoint}`);
    if (data) {
      console.log(`📦 Request payload: ${JSON.stringify(data, null, 2)}`);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to search for a product by SKU
async function searchProductBySku(sku) {
  console.log(`🔍 Searching for product with SKU: ${sku}`);
  
  // Try different search approaches
  const searchEndpoints = [
    `https://api.veeqo.com/products?query=${sku}`,
    `https://api.veeqo.com/sellables?filters%5Bsku_code%5D%5B%5D="${sku}"`,
    `https://api.veeqo.com/products?page=1&page_size=50`
  ];
  
  for (const endpoint of searchEndpoints) {
    console.log(`\n📡 Trying endpoint: ${endpoint}`);
    const result = await makeVeeqoRequest(endpoint);
    
    if (result.success) {
      if (Array.isArray(result.data)) {
        // Filter results to find matching SKU
        const matchingProducts = result.data.filter(product => {
          if (product.sellables) {
            return product.sellables.some(sellable => sellable.sku_code === sku);
          }
          return false;
        });
        
        if (matchingProducts.length > 0) {
          console.log(`✅ Found ${matchingProducts.length} matching product(s)`);
          return matchingProducts;
        } else {
          console.log(`ℹ️  No products found with SKU ${sku} in this endpoint`);
        }
      } else {
        console.log(`ℹ️  Endpoint returned non-array result`);
      }
    } else {
      console.log(`❌ Endpoint failed: ${result.error}`);
    }
  }
  
  return [];
}

// Function to get product details by ID
async function getProductById(productId) {
  console.log(`\n🔍 Getting product details for ID: ${productId}`);
  
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (result.success) {
    console.log(`✅ Product found: "${result.data.title}"`);
    return result.data;
  } else {
    console.log(`❌ Failed to get product: ${result.error}`);
    return null;
  }
}

// Function to update product title
async function updateProductTitle(productId, newTitle) {
  console.log(`\n🔄 Attempting to update product ${productId} title to: "${newTitle}"`);
  
  // Try different update approaches
  const updateAttempts = [
    {
      endpoint: `https://api.veeqo.com/products/${productId}`,
      method: 'PUT',
      data: { product: { title: newTitle } }
    },
    {
      endpoint: `https://api.veeqo.com/products/${productId}`,
      method: 'PATCH',
      data: { product: { title: newTitle } }
    },
    {
      endpoint: `https://api.veeqo.com/products/${productId}`,
      method: 'PUT',
      data: { title: newTitle }
    },
    {
      endpoint: `https://api.veeqo.com/products/${productId}`,
      method: 'PATCH',
      data: { title: newTitle }
    }
  ];
  
  for (const attempt of updateAttempts) {
    console.log(`\n📡 Trying ${attempt.method} with payload structure: ${Object.keys(attempt.data).join(', ')}`);
    const result = await makeVeeqoRequest(attempt.endpoint, attempt.method, attempt.data);
    
    if (result.success) {
      console.log(`✅ Successfully updated product title!`);
      console.log(`📋 Updated product: "${result.data.title}"`);
      return result.data;
    } else {
      console.log(`❌ Failed: ${result.error}`);
    }
  }
  
  console.log(`❌ All update attempts failed`);
  return null;
}

// Main function to test title update
async function testTitleUpdate() {
  console.log('🧪 VEEQO PRODUCT TITLE UPDATE TEST');
  console.log('='.repeat(50));
  
  const targetSku = 'D424515';
  const newTitle = 'F Innova Star Roc with Burst Logo Stock Stamp - 179g - Dark Pink Dark Pink / 178.95g';
  
  try {
    // Step 1: Search for the product
    console.log(`\n📋 Step 1: Searching for SKU ${targetSku}`);
    const products = await searchProductBySku(targetSku);
    
    if (products.length === 0) {
      console.log(`❌ No products found with SKU ${targetSku}`);
      console.log(`\nℹ️  Let's try to find any product to test with...`);
      
      // Get first few products to test with
      const allProductsResult = await makeVeeqoRequest('https://api.veeqo.com/products?page=1&page_size=5');
      if (allProductsResult.success && allProductsResult.data.length > 0) {
        console.log(`\n📋 Found ${allProductsResult.data.length} products to test with:`);
        allProductsResult.data.forEach((product, index) => {
          console.log(`${index + 1}. ID: ${product.id}, Title: "${product.title}"`);
        });
        
        // Use the first product for testing
        const testProduct = allProductsResult.data[0];
        console.log(`\n🧪 Using product ${testProduct.id} for testing...`);
        
        // Get current title and create a test title
        const currentTitle = testProduct.title;
        const testTitle = `TEST - ${currentTitle}`;
        
        console.log(`📋 Current title: "${currentTitle}"`);
        console.log(`📋 Test title: "${testTitle}"`);
        
        // Try to update the title
        const updateResult = await updateProductTitle(testProduct.id, testTitle);
        
        if (updateResult) {
          console.log(`\n✅ Title update test successful!`);
          
          // Revert the title back
          console.log(`\n🔄 Reverting title back to original...`);
          await updateProductTitle(testProduct.id, currentTitle);
        }
      }
      return;
    }
    
    // Step 2: Get detailed product information
    console.log(`\n📋 Step 2: Getting detailed product information`);
    const product = products[0];
    const productDetails = await getProductById(product.id);
    
    if (!productDetails) {
      console.log(`❌ Could not get product details`);
      return;
    }
    
    console.log(`📋 Current title: "${productDetails.title}"`);
    console.log(`📋 New title: "${newTitle}"`);
    
    // Step 3: Update the title
    console.log(`\n📋 Step 3: Updating product title`);
    const updateResult = await updateProductTitle(product.id, newTitle);
    
    if (updateResult) {
      console.log(`\n🎉 SUCCESS! Product title has been updated.`);
      console.log(`📋 Product ID: ${product.id}`);
      console.log(`📋 Old title: "${productDetails.title}"`);
      console.log(`📋 New title: "${updateResult.title}"`);
    } else {
      console.log(`\n❌ FAILED to update product title.`);
    }
    
  } catch (error) {
    console.error(`❌ Error during test: ${error.message}`);
  }
}

// Run the test
testTitleUpdate().catch(console.error);
