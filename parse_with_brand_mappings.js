import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Cache for lookup data
let brandsCache = {};
let allMoldsCache = []; // All molds across all brands
let allPlasticsCache = []; // All plastics across all brands
let moldsCache = {}; // Molds by brand_id
let plasticsCache = {}; // Plastics by brand_id
let stampsCache = [];

// Brand mappings for vendor names that don't match exactly
const brandMappings = {
    'Westside Discs': 'Westside',
    'Dynamic Discs': 'Dynamic Discs',
    'Latitude 64': 'Latitude 64',
    'Discmania': 'Discmania',
    'Kastaplast': 'Kastaplast'
    // Add more mappings as needed
};

// Plastic name mappings
const plasticMappings = {
    'Gold': 'Gold Line',
    'Tournament': 'Tournament' // Westside plastic
    // Add more mappings as needed
};

async function loadLookupData() {
    console.log('Loading lookup data...');
    
    // Load all brands
    const { data: brands, error: brandsError } = await supabase
        .from('t_brands')
        .select('id, brand');
    
    if (brandsError) {
        throw new Error(`Error loading brands: ${brandsError.message}`);
    }
    
    // Create brand lookup
    brands.forEach(brand => {
        brandsCache[brand.brand] = brand.id;
    });
    
    // Load all molds with their brand_id
    const { data: molds, error: moldsError } = await supabase
        .from('t_molds')
        .select('mold, brand_id');
    
    if (moldsError) {
        throw new Error(`Error loading molds: ${moldsError.message}`);
    }
    
    // Store all molds and group by brand_id
    allMoldsCache = molds.map(m => ({ mold: m.mold, brand_id: m.brand_id }));
    molds.forEach(mold => {
        if (!moldsCache[mold.brand_id]) {
            moldsCache[mold.brand_id] = [];
        }
        moldsCache[mold.brand_id].push(mold.mold);
    });
    
    // Load all plastics with their brand_id
    const { data: plastics, error: plasticsError } = await supabase
        .from('t_plastics')
        .select('plastic, brand_id');
    
    if (plasticsError) {
        throw new Error(`Error loading plastics: ${plasticsError.message}`);
    }
    
    // Store all plastics and group by brand_id
    allPlasticsCache = plastics.map(p => ({ plastic: p.plastic, brand_id: p.brand_id }));
    plastics.forEach(plastic => {
        if (!plasticsCache[plastic.brand_id]) {
            plasticsCache[plastic.brand_id] = [];
        }
        plasticsCache[plastic.brand_id].push(plastic.plastic);
    });
    
    // Load all stamps (not brand-specific)
    const { data: stamps, error: stampsError } = await supabase
        .from('t_stamps')
        .select('stamp');
    
    if (stampsError) {
        throw new Error(`Error loading stamps: ${stampsError.message}`);
    }
    
    stampsCache = stamps.map(s => s.stamp);
    
    console.log(`Loaded ${brands.length} brands, ${molds.length} molds, ${plastics.length} plastics, ${stampsCache.length} stamps`);
}

function findMoldAcrossBrands(moldName) {
    // First try exact match
    for (const moldData of allMoldsCache) {
        if (moldData.mold === moldName) {
            return moldData;
        }
    }
    
    // Then try partial match
    for (const moldData of allMoldsCache) {
        if (moldData.mold.includes(moldName) || moldName.includes(moldData.mold)) {
            return moldData;
        }
    }
    
    return null;
}

function findPlasticAcrossBrands(plasticName) {
    // Apply plastic mappings first
    const mappedPlastic = plasticMappings[plasticName] || plasticName;
    
    // First try exact match with mapped name
    for (const plasticData of allPlasticsCache) {
        if (plasticData.plastic === mappedPlastic) {
            return plasticData;
        }
    }
    
    // Then try exact match with original name
    for (const plasticData of allPlasticsCache) {
        if (plasticData.plastic === plasticName) {
            return plasticData;
        }
    }
    
    // Then try partial match
    for (const plasticData of allPlasticsCache) {
        if (plasticData.plastic.includes(mappedPlastic) || mappedPlastic.includes(plasticData.plastic)) {
            return plasticData;
        }
    }
    
    return null;
}

function parseVariantTitle(variantTitle, primaryBrandId) {
    if (!variantTitle || typeof variantTitle !== 'string') {
        return { mold: null, plastic: null, stamp: null };
    }
    
    const title = variantTitle.trim();
    
    // Skip default titles
    if (title === 'Default Title' || title === 'Assorted') {
        return { mold: null, plastic: null, stamp: null };
    }
    
    let foundMold = null;
    let foundPlastic = null;
    let foundStamp = 'Stock'; // Default stamp
    
    // Remove type information in parentheses
    const cleanTitle = title.replace(/\s*\([^)]*\)\s*$/, '').trim();
    
    // Split into potential parts
    const parts = cleanTitle.split(/\s+/);
    
    // Try to find plastic and mold across all brands
    let plasticData = null;
    let moldData = null;
    
    // Look for plastic first (usually first word)
    for (let i = 0; i < parts.length; i++) {
        const potentialPlastic = parts.slice(0, i + 1).join(' ');
        plasticData = findPlasticAcrossBrands(potentialPlastic);
        if (plasticData) {
            foundPlastic = plasticData.plastic;
            break;
        }
    }
    
    // Look for mold (usually after plastic)
    for (let i = 1; i < parts.length; i++) {
        for (let j = i; j < parts.length; j++) {
            const potentialMold = parts.slice(i, j + 1).join(' ');
            moldData = findMoldAcrossBrands(potentialMold);
            if (moldData) {
                foundMold = moldData.mold;
                break;
            }
        }
        if (moldData) break;
    }
    
    // Extract stamp - everything that's not plastic or mold
    if (foundMold && foundPlastic) {
        let remainingTitle = cleanTitle;
        remainingTitle = remainingTitle.replace(foundPlastic, '').trim();
        remainingTitle = remainingTitle.replace(foundMold, '').trim();
        
        // Clean up remaining text
        remainingTitle = remainingTitle.replace(/^[-\s]+|[-\s]+$/g, '').trim();
        
        if (remainingTitle && remainingTitle !== '') {
            foundStamp = remainingTitle;
        }
    }
    
    return {
        mold: foundMold,
        plastic: foundPlastic,
        stamp: foundStamp
    };
}

function parseProductTitle(productTitle, primaryBrandId) {
    if (!productTitle || typeof productTitle !== 'string') {
        return { mold: null, plastic: null, stamp: null };
    }
    
    const title = productTitle.trim();
    
    // Skip non-disc products
    if (title.includes('Set') || title.includes('Mystery') || title.includes('Box') || 
        title.includes('DyeMax')) {
        return { mold: null, plastic: null, stamp: null };
    }
    
    // Use similar cross-brand logic as variant parsing
    return parseVariantTitle(title, primaryBrandId);
}

async function updateAllParsedFieldsWithMappings() {
    console.log('Starting product parsing with brand mappings...');
    
    try {
        // Load lookup data
        await loadLookupData();
        
        // Get all disc records
        console.log('Fetching all disc records...');
        const { data: records, error: fetchError } = await supabase
            .from('it_dd_osl')
            .select('id, product_title, variant_title, product_vendor, product_product_type')
            .eq('product_product_type', 'Discs');
        
        if (fetchError) {
            console.error('Error fetching records:', fetchError);
            return;
        }
        
        console.log(`Found ${records.length} disc records`);
        
        // Parse titles and prepare updates
        const updates = [];
        const parseStats = {
            total: records.length,
            parsed_mold: 0,
            parsed_plastic: 0,
            parsed_stamp: 0,
            failed: 0,
            by_vendor: {},
            samples: []
        };
        
        for (const record of records) {
            // Apply brand mapping
            const mappedBrand = brandMappings[record.product_vendor] || record.product_vendor;
            const brandId = brandsCache[mappedBrand];
            
            if (!brandId && record.product_vendor !== 'Assorted') {
                console.log(`Warning: Brand not found for vendor "${record.product_vendor}" (mapped to "${mappedBrand}")`);
                continue;
            }
            
            // Skip Assorted products for now
            if (record.product_vendor === 'Assorted') {
                continue;
            }
            
            // Try parsing variant title first (for DyeMax products)
            let parsed = parseVariantTitle(record.variant_title, brandId);
            
            // If that fails, try product title
            if (!parsed.mold && !parsed.plastic) {
                parsed = parseProductTitle(record.product_title, brandId);
            }
            
            const update = {
                id: record.id,
                parsed_mold: parsed.mold,
                parsed_plastic: parsed.plastic,
                parsed_stamp: parsed.stamp
            };
            
            updates.push(update);
            
            // Update statistics
            if (parsed.mold) parseStats.parsed_mold++;
            if (parsed.plastic) parseStats.parsed_plastic++;
            if (parsed.stamp) parseStats.parsed_stamp++;
            if (!parsed.mold && !parsed.plastic && !parsed.stamp) parseStats.failed++;
            
            // Track by vendor
            if (!parseStats.by_vendor[record.product_vendor]) {
                parseStats.by_vendor[record.product_vendor] = { total: 0, parsed: 0 };
            }
            parseStats.by_vendor[record.product_vendor].total++;
            if (parsed.mold || parsed.plastic) {
                parseStats.by_vendor[record.product_vendor].parsed++;
            }
            
            // Collect samples for review
            if (parseStats.samples.length < 20) {
                parseStats.samples.push({
                    vendor: record.product_vendor,
                    product_title: record.product_title,
                    variant_title: record.variant_title,
                    mold: parsed.mold,
                    plastic: parsed.plastic,
                    stamp: parsed.stamp
                });
            }
        }
        
        console.log(`\nParsing complete:`);
        console.log(`- Records with mold: ${parseStats.parsed_mold}`);
        console.log(`- Records with plastic: ${parseStats.parsed_plastic}`);
        console.log(`- Records with stamp: ${parseStats.parsed_stamp}`);
        console.log(`- Failed to parse: ${parseStats.failed}`);
        
        // Show vendor breakdown
        console.log('\nParsing success by vendor:');
        console.log('=====================================');
        Object.entries(parseStats.by_vendor).forEach(([vendor, stats]) => {
            const percentage = ((stats.parsed / stats.total) * 100).toFixed(1);
            console.log(`${vendor}: ${stats.parsed}/${stats.total} (${percentage}%)`);
        });
        
        // Show sample results
        console.log('\nSample parsing results:');
        console.log('=====================================');
        parseStats.samples.forEach((sample, index) => {
            console.log(`${index + 1}. [${sample.vendor}] "${sample.product_title}"`);
            console.log(`   Variant: "${sample.variant_title}"`);
            console.log(`   → Plastic: ${sample.plastic || 'NOT FOUND'}`);
            console.log(`   → Mold: ${sample.mold || 'NOT FOUND'}`);
            console.log(`   → Stamp: ${sample.stamp || 'NOT FOUND'}`);
            console.log('---');
        });
        
        if (updates.length === 0) {
            console.log('No updates to perform.');
            return;
        }
        
        // Update records in batches
        console.log(`\nUpdating ${updates.length} records...`);
        const batchSize = 100;
        let updatedCount = 0;
        
        for (let i = 0; i < updates.length; i += batchSize) {
            const batch = updates.slice(i, i + batchSize);
            
            for (const update of batch) {
                const { error: updateError } = await supabase
                    .from('it_dd_osl')
                    .update({
                        parsed_mold: update.parsed_mold,
                        parsed_plastic: update.parsed_plastic,
                        parsed_stamp: update.parsed_stamp
                    })
                    .eq('id', update.id);
                
                if (updateError) {
                    console.error(`Error updating record ${update.id}:`, updateError);
                } else {
                    updatedCount++;
                }
            }
            
            console.log(`Updated batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(updates.length/batchSize)} (${updatedCount}/${updates.length} records)`);
        }
        
        console.log(`\nUpdate complete! Updated ${updatedCount} records.`);
        
    } catch (error) {
        console.error('Script failed:', error);
    }
}

// Run the script
console.log('Multi-Vendor Product Parser with Brand Mappings\n');

updateAllParsedFieldsWithMappings()
    .then(() => {
        console.log('\nProduct parsing completed successfully');
        process.exit(0);
    })
    .catch(error => {
        console.error('Product parsing failed:', error);
        process.exit(1);
    });

export { parseVariantTitle, parseProductTitle, updateAllParsedFieldsWithMappings };
