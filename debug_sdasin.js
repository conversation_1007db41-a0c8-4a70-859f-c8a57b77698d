// debug_sdasin.js - Very simple debug version

import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

console.log('Script started');

async function step1() {
  console.log('Step 1: Testing connection...');
  const { data, error } = await supabase.from('t_sdasins').select('id').limit(1);
  if (error) throw error;
  console.log('✅ Connection works, sample:', data[0]);
}

async function step2() {
  console.log('Step 2: Counting SDASIN records...');
  const { count, error } = await supabase.from('t_sdasins').select('*', { count: 'exact', head: true });
  if (error) throw error;
  console.log(`✅ Found ${count} SDASIN records`);
  return count;
}

async function step3() {
  console.log('Step 3: Counting inventory records...');
  const { count, error } = await supabase.from('t_inv_sdasin').select('*', { count: 'exact', head: true });
  if (error) throw error;
  console.log(`✅ Found ${count} inventory records`);
  return count;
}

async function run() {
  try {
    await step1();
    const sdasinCount = await step2();
    const invCount = await step3();
    
    const missing = sdasinCount - invCount;
    console.log(`\n📊 Summary:`);
    console.log(`SDASIN records: ${sdasinCount}`);
    console.log(`Inventory records: ${invCount}`);
    console.log(`Missing: ${missing}`);
    
    if (missing > 0) {
      console.log(`\n🔧 Need to create ${missing} inventory records`);
    } else {
      console.log(`\n✅ All records are present!`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

console.log('About to run...');
run();
console.log('Run called, waiting for completion...');
