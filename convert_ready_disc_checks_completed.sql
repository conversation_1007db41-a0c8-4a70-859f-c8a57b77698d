-- Function to convert disc_checks_completed tasks back to pending when their time arrives
-- This should be called periodically (maybe every minute) to activate ready tasks

CREATE OR REPLACE FUNCTION activate_ready_disc_checks_completed_tasks()
RETURNS INTEGER AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    -- Convert disc_checks_completed tasks back to pending when their scheduled time has arrived
    UPDATE t_task_queue
    SET 
        status = 'pending',
        locked_at = NULL,
        locked_by = NULL
    WHERE 
        status = 'disc_checks_completed'
        AND scheduled_at <= NOW()
        AND task_type = 'publish_product_osl';
    
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    
    IF affected_rows > 0 THEN
        RAISE NOTICE 'Activated % disc_checks_completed tasks that are now ready to publish', affected_rows;
    END IF;
    
    RETURN affected_rows;
END;
$$ LANGUAGE plpgsql;

-- Test the function (this will activate any ready tasks immediately)
SELECT activate_ready_disc_checks_completed_tasks() as activated_tasks;
