import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('Error: VEEQO_API_KEY must be set in .env file');
  process.exit(1);
}

console.log('🔍 Testing Veeqo Archive Manager...\n');

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint) {
  try {
    console.log(`Making request to: ${endpoint}`);
    
    const response = await fetch(endpoint, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    });
    
    console.log(`Response status: ${response.status}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API Error: ${errorText}`);
      return null;
    }
    
    const data = await response.json();
    console.log(`Received ${Array.isArray(data) ? data.length : 1} items`);
    return data;
  } catch (error) {
    console.error(`Request error: ${error.message}`);
    return null;
  }
}

async function testArchiveDetection() {
  console.log('📋 Getting first 10 products to analyze...\n');
  
  const products = await makeVeeqoRequest('https://api.veeqo.com/products?page=1&page_size=10');
  
  if (!products || !Array.isArray(products)) {
    console.error('Failed to get products');
    return;
  }
  
  console.log(`\n📊 Analyzing ${products.length} products:\n`);
  
  let archivedCount = 0;
  
  products.forEach((product, index) => {
    console.log(`${index + 1}. Product ID: ${product.id}`);
    console.log(`   Title: ${product.title}`);
    console.log(`   Requires Review: ${product.requires_review}`);
    console.log(`   Active Channels: ${product.active_channels ? product.active_channels.length : 0}`);
    
    if (product.channel_products && product.channel_products.length > 0) {
      console.log(`   Channel Products: ${product.channel_products.length}`);
      product.channel_products.forEach(cp => {
        console.log(`     - ${cp.channel?.short_name || 'Unknown'}: ${cp.status}`);
      });
      
      // Check if all channels are pulled (archived)
      const allPulled = product.channel_products.every(cp => cp.status === 'pulled');
      if (allPulled) {
        console.log(`   🗂️  STATUS: ARCHIVED (all channels pulled)`);
        archivedCount++;
      } else {
        console.log(`   ✅ STATUS: ACTIVE`);
      }
    } else {
      console.log(`   Channel Products: 0`);
      console.log(`   🗂️  STATUS: ARCHIVED (no channel products)`);
      archivedCount++;
    }
    
    console.log('');
  });
  
  console.log(`📈 SUMMARY: ${archivedCount}/${products.length} products are archived\n`);
  
  if (archivedCount > 0) {
    console.log('🛠️  To unarchive a product, you would need to:');
    console.log('1. Update the channel_product status from "pulled" to "active"');
    console.log('2. Use the Veeqo API endpoint for channel products');
    console.log('3. Or manually update through the Veeqo interface\n');
  }
}

// Run the test
testArchiveDetection().catch(error => {
  console.error(`Fatal error: ${error.message}`);
  process.exit(1);
});
