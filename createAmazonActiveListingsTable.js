// createAmazonActiveListingsTable.js - Script to create the it_amaz_active_listings_report table

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

// Create a log file
const logFile = 'create_amazon_active_listings_table.log';
fs.writeFileSync(logFile, `Starting table creation at ${new Date().toISOString()}\n`);

// Load environment variables
dotenv.config();
fs.appendFileSync(logFile, `Environment variables loaded\n`);

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  const errorMsg = 'Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file';
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

fs.appendFileSync(logFile, `Supabase credentials found\n`);
const supabase = createClient(supabaseUrl, supabaseKey);
fs.appendFileSync(logFile, `Supabase client created\n`);

// Function to check if the table exists
async function checkTableExists() {
  try {
    fs.appendFileSync(logFile, `Checking if table exists...\n`);
    console.log('Checking if table exists...');
    
    const { data, error } = await supabase
      .from('it_amaz_active_listings_report')
      .select('id')
      .limit(1);
    
    if (error) {
      if (error.code === 'PGRST116' || error.message.includes('does not exist')) {
        fs.appendFileSync(logFile, `Table does not exist\n`);
        console.log('Table does not exist');
        return false;
      } else {
        fs.appendFileSync(logFile, `Error checking table: ${error.message}\n`);
        console.error(`Error checking table: ${error.message}`);
        return false;
      }
    }
    
    fs.appendFileSync(logFile, `Table exists\n`);
    console.log('Table exists');
    return true;
  } catch (error) {
    fs.appendFileSync(logFile, `Exception checking table: ${error.message}\n`);
    console.error(`Exception checking table: ${error.message}`);
    return false;
  }
}

// Function to create the table
async function createTable() {
  try {
    fs.appendFileSync(logFile, `Reading SQL file...\n`);
    console.log('Reading SQL file...');
    
    // Read the SQL file
    const createTableSQL = fs.readFileSync('create_amazon_active_listings_table.sql', 'utf8');
    
    fs.appendFileSync(logFile, `Attempting to create table...\n`);
    console.log('Attempting to create table...');
    
    // Split SQL into individual statements and execute them
    const statements = createTableSQL.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim() + ';';
      
      // Skip comments and empty statements
      if (statement.startsWith('--') || statement.trim() === ';') {
        continue;
      }
      
      fs.appendFileSync(logFile, `Executing statement ${i + 1} of ${statements.length}...\n`);
      console.log(`Executing statement ${i + 1} of ${statements.length}...`);
      
      const { error } = await supabase.rpc('exec_sql', { sql_query: statement });
      
      if (error) {
        fs.appendFileSync(logFile, `Error executing statement ${i + 1}: ${error.message}\n`);
        console.error(`Error executing statement ${i + 1}: ${error.message}`);
        console.error(`Statement was: ${statement}`);
        
        // Continue with other statements even if one fails
        continue;
      } else {
        fs.appendFileSync(logFile, `Statement ${i + 1} executed successfully\n`);
        console.log(`Statement ${i + 1} executed successfully`);
      }
    }
    
    fs.appendFileSync(logFile, `Table creation completed\n`);
    console.log('Table creation completed');
    return true;
    
  } catch (error) {
    fs.appendFileSync(logFile, `Error creating table: ${error.message}\n`);
    console.error(`Error creating table: ${error.message}`);
    
    if (error.message.includes('exec_sql')) {
      fs.appendFileSync(logFile, `exec_sql function may not exist. Please run create_exec_sql_function.sql first\n`);
      console.log('exec_sql function may not exist. Please run create_exec_sql_function.sql first');
      console.log('Or run the following SQL in the Supabase SQL Editor:');
      console.log(fs.readFileSync('create_amazon_active_listings_table.sql', 'utf8'));
    }
    
    return false;
  }
}

// Function to verify the table was created successfully
async function verifyTable() {
  try {
    fs.appendFileSync(logFile, `Verifying table creation...\n`);
    console.log('Verifying table creation...');
    
    // Try to get table info
    const { data, error } = await supabase
      .from('it_amaz_active_listings_report')
      .select('*')
      .limit(0); // Just get structure, no data
    
    if (error) {
      fs.appendFileSync(logFile, `Error verifying table: ${error.message}\n`);
      console.error(`Error verifying table: ${error.message}`);
      return false;
    }
    
    fs.appendFileSync(logFile, `Table verified successfully\n`);
    console.log('Table verified successfully');
    return true;
  } catch (error) {
    fs.appendFileSync(logFile, `Exception verifying table: ${error.message}\n`);
    console.error(`Exception verifying table: ${error.message}`);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Check if the table exists
    const tableExists = await checkTableExists();
    
    if (!tableExists) {
      // Create the table
      const tableCreated = await createTable();
      
      if (!tableCreated) {
        fs.appendFileSync(logFile, `Failed to create table\n`);
        console.error('Failed to create table');
        return;
      }
      
      // Verify the table was created
      const tableVerified = await verifyTable();
      
      if (!tableVerified) {
        fs.appendFileSync(logFile, `Failed to verify table creation\n`);
        console.error('Failed to verify table creation');
        return;
      }
    } else {
      console.log('Table already exists, skipping creation');
    }
    
    fs.appendFileSync(logFile, `Table setup completed successfully\n`);
    console.log('Table setup completed successfully');
  } catch (error) {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`Unhandled error: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the main function if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .then(() => {
      console.log('Script completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

export { main as createAmazonActiveListingsTable };
