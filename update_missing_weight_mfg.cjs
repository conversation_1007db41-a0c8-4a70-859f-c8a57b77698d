require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function updateMissingWeightMfg() {
  try {
    console.log('Finding discs where weight_mfg is null but weight exists...');
    
    // Get count of discs that need weight_mfg populated
    const { count: needWeightMfg, error: countError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('weight_mfg', null)
      .not('weight', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (countError) {
      console.error('Error getting count:', countError);
      return;
    }
    
    console.log(`Found ${needWeightMfg} discs that need weight_mfg populated from weight`);
    
    if (needWeightMfg === 0) {
      console.log('No discs need weight_mfg updates. Exiting.');
      return;
    }
    
    console.log('\n=== STEP 1: POPULATE MISSING weight_mfg VALUES ===');
    console.log('Setting weight_mfg = weight for discs where weight_mfg is null...');
    
    // Use SQL to bulk update weight_mfg = weight where weight_mfg is null
    const updateWeightMfgSql = `
      UPDATE t_discs 
      SET weight_mfg = weight
      WHERE weight_mfg IS NULL
        AND weight IS NOT NULL
        AND mps_id IS NOT NULL
        AND color_id IS NOT NULL;
    `;
    
    const { error: updateWeightMfgError } = await supabase.rpc('exec_sql', {
      sql_query: updateWeightMfgSql
    });
    
    if (updateWeightMfgError) {
      console.error('Error updating weight_mfg:', updateWeightMfgError);
      return;
    }
    
    console.log('✅ Successfully populated weight_mfg values!');
    
    // Verify the update
    const { count: remainingNullWeightMfg, error: verifyError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('weight_mfg', null)
      .not('weight', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (!verifyError) {
      console.log(`Remaining discs with null weight_mfg: ${remainingNullWeightMfg}`);
      console.log(`Successfully updated: ${needWeightMfg - remainingNullWeightMfg} discs`);
    }
    
    console.log('\n=== STEP 2: FIND VENDOR OSL MAPPINGS FOR NEWLY POPULATED DISCS ===');
    
    // Now find all discs that have weight_mfg but no vendor_osl_id
    const { count: needVendorOsl, error: vendorCountError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (vendorCountError) {
      console.error('Error getting vendor OSL count:', vendorCountError);
      return;
    }
    
    console.log(`Found ${needVendorOsl} discs that need vendor_osl_id mappings`);
    
    if (needVendorOsl === 0) {
      console.log('All discs already have vendor_osl_id mappings!');
      return;
    }
    
    // Process in batches to find vendor OSL mappings
    let totalProcessed = 0;
    let totalUpdated = 0;
    let totalErrors = 0;
    const batchSize = 100;
    
    console.log(`Processing ${needVendorOsl} discs in batches of ${batchSize}...`);
    
    while (totalProcessed < needVendorOsl) {
      console.log(`\nProcessing batch ${Math.floor(totalProcessed / batchSize) + 1}...`);
      
      // Get next batch of discs that need vendor_osl_id
      const { data: discs, error: batchError } = await supabase
        .from('t_discs')
        .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
        .is('vendor_osl_id', null)
        .not('weight_mfg', 'is', null)
        .not('mps_id', 'is', null)
        .not('color_id', 'is', null)
        .range(0, batchSize - 1);  // Always get the first batch since we're updating them
      
      if (batchError) {
        console.error('Error getting batch:', batchError);
        break;
      }
      
      if (!discs || discs.length === 0) {
        console.log('No more discs to process');
        break;
      }
      
      console.log(`Processing ${discs.length} discs in this batch...`);
      
      for (const disc of discs) {
        try {
          // Find matching vendor OSL using weight_mfg
          const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
            'find_matching_osl_by_mfg_weight',
            {
              mps_id_param: disc.mps_id,
              color_id_param: disc.color_id,
              weight_mfg_param: disc.weight_mfg
            }
          );
          
          if (vendorOslError) {
            console.error(`Error finding vendor OSL for disc ${disc.id}:`, vendorOslError);
            totalErrors++;
            continue;
          }
          
          const vendorOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
          
          if (vendorOslId) {
            // Update the disc with vendor_osl_id
            const { error: updateError } = await supabase
              .from('t_discs')
              .update({ vendor_osl_id: vendorOslId })
              .eq('id', disc.id);
            
            if (updateError) {
              console.error(`Error updating disc ${disc.id}:`, updateError);
              totalErrors++;
            } else {
              totalUpdated++;
              if (totalUpdated % 100 === 0) {
                console.log(`  ✅ Updated ${totalUpdated} discs so far...`);
              }
            }
          }
          
          totalProcessed++;
          
        } catch (err) {
          console.error(`Error processing disc ${disc.id}:`, err.message);
          totalErrors++;
          totalProcessed++;
        }
      }
      
      console.log(`Batch completed. Processed: ${totalProcessed}, Updated: ${totalUpdated}, Errors: ${totalErrors}`);
      
      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    console.log('\n=== FINAL RESULTS ===');
    console.log(`Step 1 - Populated weight_mfg: ${needWeightMfg - (remainingNullWeightMfg || 0)} discs`);
    console.log(`Step 2 - Total discs processed for vendor OSL: ${totalProcessed}`);
    console.log(`Step 2 - Successfully updated with vendor_osl_id: ${totalUpdated}`);
    console.log(`Step 2 - Errors encountered: ${totalErrors}`);
    console.log(`Step 2 - Discs with no matching vendor OSL: ${totalProcessed - totalUpdated - totalErrors}`);
    
    // Final summary
    const { count: finalVendorOslCount, error: finalError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .not('vendor_osl_id', 'is', null);
    
    if (!finalError) {
      console.log(`\n🎯 FINAL SUMMARY:`);
      console.log(`Total discs with vendor_osl_id: ${finalVendorOslCount}`);
      console.log(`New vendor mappings created: ${totalUpdated}`);
    }
    
    if (totalUpdated > 0) {
      console.log(`\n✅ SUCCESS! Created ${totalUpdated} new vendor OSL mappings!`);
      console.log('The dual mapping system now covers significantly more discs.');
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

updateMissingWeightMfg();
