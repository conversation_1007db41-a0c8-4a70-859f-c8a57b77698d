import ExcelJS from 'exceljs';

const originalFile = 'data/external data/discraftstock.xlsx';
const outputFile = 'data/external data/discraftstock_exceljs_test.xlsx';

async function testExcelJS() {
  try {
    console.log('Testing ExcelJS formatting preservation...\n');
    
    // Read the original file
    console.log('Reading original file with ExcelJS...');
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(originalFile);
    
    const worksheet = workbook.getWorksheet(1); // First worksheet
    
    // Check a few cells that we know have formatting
    const testCells = ['Q132', 'P178'];
    
    console.log('Original cell properties:');
    testCells.forEach(cellAddress => {
      const cell = worksheet.getCell(cellAddress);
      console.log(`--- Cell ${cellAddress} ---`);
      console.log('Value:', cell.value);
      console.log('Type:', cell.type);
      console.log('Style:', JSON.stringify(cell.style, null, 2));
      console.log('');
    });
    
    // Modify one of the cells
    console.log('Modifying cell Q132...');
    const cellQ132 = worksheet.getCell('Q132');
    const originalStyle = { ...cellQ132.style }; // Copy the style
    
    cellQ132.value = 5; // Change value to a number
    cellQ132.style = originalStyle; // Try to preserve the style
    
    // Check the cell after modification
    console.log('After modification:');
    console.log('Value:', cellQ132.value);
    console.log('Type:', cellQ132.type);
    console.log('Style:', JSON.stringify(cellQ132.style, null, 2));
    
    // Save the file
    console.log('\nSaving file...');
    await workbook.xlsx.writeFile(outputFile);
    
    console.log(`File saved as: ${outputFile}`);
    
    // Read it back to check if formatting was preserved
    console.log('\nReading back the saved file...');
    const workbook2 = new ExcelJS.Workbook();
    await workbook2.xlsx.readFile(outputFile);
    const worksheet2 = workbook2.getWorksheet(1);
    
    const cellQ132_readback = worksheet2.getCell('Q132');
    console.log('After save/reload:');
    console.log('Value:', cellQ132_readback.value);
    console.log('Type:', cellQ132_readback.type);
    console.log('Style:', JSON.stringify(cellQ132_readback.style, null, 2));
    
  } catch (err) {
    console.error('Error:', err.message);
  }
}

testExcelJS();
