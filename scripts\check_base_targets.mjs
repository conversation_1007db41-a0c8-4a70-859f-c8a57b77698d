import fs from 'fs';
import path from 'path';

const TSV_PATH = path.join('data', 'external data', 'access', 'Shopify Accessory Images.txt');
const TARGET_DIR = "\\\\NANCY\\nancyv8\\Images\\DG Accessory Products\\Uploaded to AWS";
const SOURCE_ARCHIVE = "\\\\NANCY\\nancyv8\\Images\\DG Accessory Products\\3 Magento Image Archive";
const BASE = 'https://d3f34rkxix3zin.cloudfront.net/shopify/dgaccessories/magentoproduct/';

const raw = fs.readFileSync(TSV_PATH, 'utf8');
const lines = raw.split(/\r?\n/).filter(Boolean);
const header = lines.shift().split('\t').map(s=> s.replace(/^\"|\"$/g,''));
const idx = (k)=> header.indexOf(k);
const URL_IDX = idx('URL');
const ACC_IDX = idx('AccessoryID');
const FN_IDX = idx('FileName');

let total=0, targetExists=0, sourceExists=0, bothExists=0, neitherExists=0;
let sampleMissingTargets = [];

for (const line of lines){
  const cols = line.split('\t').map(s=> s.replace(/^\"|\"$/g,''));
  if (cols[URL_IDX] !== BASE) continue;
  total++;
  const id = cols[ACC_IDX];
  const fileName = cols[FN_IDX];
  const target = path.win32.join(TARGET_DIR, id + '.jpg');
  const source = path.win32.join(SOURCE_ARCHIVE, ...fileName.split('/'));
  const t = fs.existsSync(target);
  const s = fs.existsSync(source);
  if (t) targetExists++;
  if (s) sourceExists++;
  if (t && s) bothExists++;
  if (!t && !s) {
    neitherExists++;
    if (sampleMissingTargets.length < 10) sampleMissingTargets.push({id, fileName});
  }
}

console.log({ total, targetExists, sourceExists, bothExists, neitherExists, sampleMissingTargets });

