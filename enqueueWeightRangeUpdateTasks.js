import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Calculate weight range tag based on disc weight
 * @param {number} weight - The disc weight
 * @returns {string} - The weight range tag
 */
function calculateWeightRangeTag(weight) {
  // Round to nearest 0.5 for proper range assignment
  const roundedWeight = Math.round(weight * 2) / 2;

  if (roundedWeight >= 10 && roundedWeight <= 49.5) {
    return 'wt_rng_10-49';
  } else if (roundedWeight >= 50 && roundedWeight <= 99.5) {
    return 'wt_rng_50-99';
  } else if (roundedWeight >= 100 && roundedWeight <= 119.5) {
    return 'wt_rng_100-119';
  } else if (roundedWeight >= 120 && roundedWeight <= 139.5) {
    return 'wt_rng_120-139';
  } else if (roundedWeight >= 140 && roundedWeight <= 149.5) {
    return 'wt_rng_140-149';
  } else if (roundedWeight >= 150 && roundedWeight <= 159.5) {
    return 'wt_rng_150-159';
  } else if (roundedWeight >= 160 && roundedWeight <= 169.5) {
    return 'wt_rng_160-169';
  } else if (roundedWeight >= 170 && roundedWeight <= 174.5) {
    return 'wt_rng_170-174';
  } else if (roundedWeight >= 175 && roundedWeight <= 180.5) {
    return 'wt_rng_175-180';
  } else if (roundedWeight >= 181 && roundedWeight <= 200) {
    return 'wt_rng_181-200';
  } else if (roundedWeight >= 201 && roundedWeight <= 249) {
    return 'wt_rng_201-249';
  } else {
    // For weights outside all ranges, return null to skip
    return null;
  }
}

/**
 * Get all discs that need weight range tag updates
 */
async function getEligibleDiscs() {
  try {
    console.log('📊 Querying eligible discs for weight range tag updates...');

    // First get the total count
    const { count, error: countError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .not('shopify_uploaded_at', 'is', null)
      .is('sold_date', null)
      .not('weight', 'is', null);

    if (countError) {
      throw new Error(`Database count error: ${countError.message}`);
    }

    console.log(`📊 Total eligible discs found: ${count}`);

    // Fetch all records in batches
    const allDiscs = [];
    const batchSize = 1000;
    let offset = 0;

    while (offset < count) {
      console.log(`📊 Fetching batch ${Math.floor(offset / batchSize) + 1} (${offset + 1}-${Math.min(offset + batchSize, count)} of ${count})`);

      const { data: batch, error } = await supabase
        .from('t_discs')
        .select('id, weight')
        .not('shopify_uploaded_at', 'is', null)
        .is('sold_date', null)
        .not('weight', 'is', null)
        .order('id')
        .range(offset, offset + batchSize - 1);

      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }

      allDiscs.push(...batch);
      offset += batchSize;

      // Break if we got fewer records than expected (end of data)
      if (batch.length < batchSize) {
        break;
      }
    }

    console.log(`📊 Successfully fetched ${allDiscs.length} discs that need weight range tag updates`);
    return allDiscs;
  } catch (error) {
    console.error(`❌ Error querying eligible discs:`, error.message);
    throw error;
  }
}

/**
 * Enqueue fix_weight_range tasks for all eligible discs
 */
async function enqueueWeightRangeTasks(dryRun = false) {
  try {
    const discs = await getEligibleDiscs();
    
    if (discs.length === 0) {
      console.log('ℹ️ No eligible discs found for weight range updates');
      return;
    }

    console.log(`🚀 ${dryRun ? 'DRY RUN: Would enqueue' : 'Enqueueing'} ${discs.length} fix_weight_range tasks...`);
    
    const now = new Date();
    const scheduledAt = new Date(now.getTime() + 5 * 60 * 1000); // Schedule 5 minutes in future
    
    // Prepare tasks for batch insert
    const tasks = [];
    let validDiscs = 0;
    let skippedDiscs = 0;
    
    for (const disc of discs) {
      const weightRangeTag = calculateWeightRangeTag(disc.weight);
      
      if (weightRangeTag === null) {
        console.log(`⚠️ Skipping disc ${disc.id} with weight ${disc.weight}g (outside standard ranges)`);
        skippedDiscs++;
        continue;
      }
      
      tasks.push({
        task_type: 'fix_weight_range',
        payload: {
          disc_id: disc.id,
          weight: disc.weight,
          expected_weight_range_tag: weightRangeTag
        },
        status: 'pending',
        scheduled_at: scheduledAt.toISOString(),
        created_at: now.toISOString(),
        enqueued_by: `weight_range_update_batch_${now.getTime()}`
      });
      
      validDiscs++;
    }
    
    console.log(`✅ Prepared ${validDiscs} tasks for discs with valid weight ranges`);
    console.log(`⚠️ Skipped ${skippedDiscs} discs with weights outside standard ranges`);
    
    if (dryRun) {
      console.log('🧪 DRY RUN: Tasks prepared but not enqueued');
      console.log('Sample task:', JSON.stringify(tasks[0], null, 2));
      return;
    }

    // Insert tasks in batches to avoid potential issues with large inserts
    const BATCH_SIZE = 100;
    let totalEnqueued = 0;
    
    for (let i = 0; i < tasks.length; i += BATCH_SIZE) {
      const batch = tasks.slice(i, i + BATCH_SIZE);
      
      const { error: insertError } = await supabase
        .from('t_task_queue')
        .insert(batch);

      if (insertError) {
        console.error(`❌ Error enqueueing batch ${Math.floor(i / BATCH_SIZE) + 1}:`, insertError);
        throw insertError;
      }

      totalEnqueued += batch.length;
      console.log(`✅ Enqueued batch ${Math.floor(i / BATCH_SIZE) + 1} of ${Math.ceil(tasks.length / BATCH_SIZE)} (${batch.length} tasks, ${totalEnqueued} total)`);
    }
    
    console.log(`🎉 Successfully enqueued ${totalEnqueued} fix_weight_range tasks`);
    console.log(`📅 Tasks scheduled to start at: ${scheduledAt.toISOString()}`);
    
  } catch (error) {
    console.error(`❌ Error enqueueing weight range tasks:`, error.message);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    // Check for dry run flag
    const dryRun = process.argv.includes('--dry-run');
    
    if (dryRun) {
      console.log('🧪 Running in DRY RUN mode - no tasks will be enqueued');
    }
    
    await enqueueWeightRangeTasks(dryRun);
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
