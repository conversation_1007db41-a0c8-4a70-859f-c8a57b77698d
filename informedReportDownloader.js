/**
 * Informed Report Downloader
 *
 * This script downloads reports from Informed Repricer API and saves them to the local file system.
 * It handles three report types:
 * 1. All Fields
 * 2. Competition Landscape
 * 3. Listings That Do Not Have The BuyBox
 */

import fs from 'fs';
import path from 'path';
import axios from 'axios';
import { createClient } from '@supabase/supabase-js';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Constants
const API_KEY = 'Us79BiL2il9qXJTWPWhfbAgXn5GfYMFFaFiFtiCH';
const BASE_URL = 'https://api.informed.co';
const REPORTS_DIR = path.join(__dirname, 'data', 'external data', 'informed');
const REPORT_TYPES = [
    {
        type: 'All_Fields',
        endpoint: '/reports/requestReport?reportType=All_Fields',
        filename: 'from_informed_all_fields.csv',
        tableName: 'it_infor_all_fields'
    },
    {
        type: 'Competition_Landscape',
        endpoint: '/reports/requestReport?reportType=Competition_Landscape',
        filename: 'from_informed_competition_landscape.csv',
        tableName: 'it_infor_competition_landscape'
    },
    {
        type: 'No_Buy_Box',
        endpoint: '/reports/requestReport?reportType=Listings_That_Do_Not_Have_The_BuyBox',
        filename: 'from_informed_no_buy_box.csv',
        tableName: 'it_infor_no_buy_box'
    }
];

// Supabase client
const supabaseUrl = 'https://lfcmwwwvzuqzpadtxrui.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxmY213d3d2enVxenBhZHR4cnVpIiwicm9sZSI6ImFub24iLCJpYXQiOjE2NTk0NjQwMDAsImV4cCI6MTk3NTA0MDAwMH0.QALxAHPdnpJpGLVWJ7-STYnCQgQlrwlAKQQV0Qb3DT8';
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Ensure the reports directory exists
 */
function ensureReportsDirectory() {
    if (!fs.existsSync(REPORTS_DIR)) {
        fs.mkdirSync(REPORTS_DIR, { recursive: true });
        console.log(`Created directory: ${REPORTS_DIR}`);
    }
}

/**
 * Download a report from Informed API
 * @param {Object} report - Report configuration object
 * @returns {Promise<Object>} - Result of the download operation
 */
async function downloadReport(report) {
    const url = `${BASE_URL}${report.endpoint}`;
    const filePath = path.join(REPORTS_DIR, report.filename);

    console.log(`Downloading ${report.type} report from ${url}`);

    try {
        const response = await axios({
            method: 'GET',
            url: url,
            headers: {
                'x-api-key': API_KEY,
                'Accept': 'text/csv'
            },
            responseType: 'stream'
        });

        const writer = fs.createWriteStream(filePath);

        return new Promise((resolve, reject) => {
            response.data.pipe(writer);

            writer.on('finish', () => {
                console.log(`Successfully downloaded ${report.type} report to ${filePath}`);
                resolve({
                    success: true,
                    report: report.type,
                    filePath,
                    message: `Successfully downloaded ${report.type} report`
                });
            });

            writer.on('error', (err) => {
                console.error(`Error writing ${report.type} report to file:`, err);
                reject({
                    success: false,
                    report: report.type,
                    error: err.message
                });
            });
        });
    } catch (error) {
        console.error(`Error downloading ${report.type} report:`, error.message);
        return {
            success: false,
            report: report.type,
            error: error.message
        };
    }
}

/**
 * Download all reports from Informed
 * @returns {Promise<Array>} - Results of all download operations
 */
async function downloadAllReports() {
    ensureReportsDirectory();

    const results = [];

    for (const report of REPORT_TYPES) {
        try {
            const result = await downloadReport(report);
            results.push(result);
        } catch (error) {
            results.push({
                success: false,
                report: report.type,
                error: error.message
            });
        }
    }

    return results;
}

/**
 * Get the status of all reports
 * @returns {Promise<Array>} - Status of all reports
 */
async function getReportsStatus() {
    const status = [];

    for (const report of REPORT_TYPES) {
        const filePath = path.join(REPORTS_DIR, report.filename);
        let fileExists = false;
        let lastModified = null;
        let fileSize = 0;

        try {
            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                fileExists = true;
                lastModified = stats.mtime;
                fileSize = stats.size;
            }

            // Get record count from database
            const { count, error } = await supabase
                .from(report.tableName)
                .select('*', { count: 'exact', head: true });

            status.push({
                name: report.type,
                filename: report.filename,
                exists: fileExists,
                lastUpdated: lastModified,
                fileSize,
                recordCount: count || 0,
                status: fileExists && fileSize > 0 ? 'OK' : 'Missing',
                tableName: report.tableName
            });
        } catch (error) {
            console.error(`Error getting status for ${report.type}:`, error);
            status.push({
                name: report.type,
                filename: report.filename,
                exists: fileExists,
                lastUpdated: lastModified,
                fileSize,
                recordCount: 0,
                status: 'Error',
                error: error.message,
                tableName: report.tableName
            });
        }
    }

    return status;
}

// Export functions for use in other modules
export {
    downloadAllReports,
    getReportsStatus,
    REPORT_TYPES
};

// If this script is run directly, download all reports
if (import.meta.url === `file://${process.argv[1]}`) {
    downloadAllReports()
        .then(results => {
            console.log('Download results:', results);
            process.exit(0);
        })
        .catch(error => {
            console.error('Error downloading reports:', error);
            process.exit(1);
        });
}
