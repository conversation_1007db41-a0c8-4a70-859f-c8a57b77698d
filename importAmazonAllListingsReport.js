// Import Amazon All Listings Report (truncate and replace)
// File: data/external data/All Listings Report.txt

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const TABLE_NAME = 'it_amaz_all_listings_report';
const DATA_FILE_PATH = path.join(__dirname, 'data', 'external data', 'All Listings Report.txt');

// Map report headers to DB column names
function mapHeader(header) {
  const mapping = {
    'seller-sku': 'seller_sku',
    'asin1': 'asin1',
    'item-name': 'item_name',
    'listing-id': 'listing_id',
    'price': 'price',
    'quantity': 'quantity',
    'product-id-type': 'product_id_type',
    'item-note': 'item_note',
    'expedited-shipping': 'expedited_shipping',
    'product-id': 'product_id',
    'fulfillment-channel': 'fulfillment_channel',
    'merchant-shipping-group': 'merchant_shipping_group',
    'status': 'status',
  };
  return mapping[header] || header.replace(/[^a-z0-9]/gi, '_').toLowerCase();
}

function toNumber(val) {
  if (val === undefined || val === null) return null;
  const s = String(val).trim();
  if (!s) return null;
  const n = Number(s);
  return Number.isFinite(n) ? n : null;
}

function toInt(val) {
  if (val === undefined || val === null) return null;
  const s = String(val).trim();
  if (!s) return null;
  const n = parseInt(s, 10);
  return Number.isFinite(n) ? n : null;
}

function toText(val) {
  if (val === undefined || val === null) return null;
  const s = String(val).trim();
  return s === '' ? null : s;
}

// Ensure the destination table exists; create it if missing (one-time)
async function ensureTableExists(supabase) {
  try {
    const { data, error } = await supabase.from(TABLE_NAME).select('id').limit(1);
    if (!error) return true; // table exists
    // If error but not a missing-table error, still try to create below
  } catch (_) {
    // continue to create
  }

  const createSQL = `
  CREATE TABLE IF NOT EXISTS public.${TABLE_NAME} (
    id BIGSERIAL PRIMARY KEY,
    seller_sku TEXT,
    asin1 TEXT,
    item_name TEXT,
    listing_id TEXT,
    price NUMERIC(10,2),
    quantity INTEGER,
    product_id_type TEXT,
    item_note TEXT,
    expedited_shipping TEXT,
    product_id TEXT,
    fulfillment_channel TEXT,
    merchant_shipping_group TEXT,
    status TEXT,
    imported_at TIMESTAMPTZ NOT NULL DEFAULT now()
  );`;

  // Try to execute via exec_sql helper function
  const { error: execErr } = await supabase.rpc('exec_sql', { sql_query: createSQL });
  if (execErr) {
    throw new Error(`Failed to create ${TABLE_NAME}: ${execErr.message}`);
  }
  return true;
}

export async function importAmazonAllListingsReport(supabase) {
  console.log('[importAmazonAllListingsReport] Starting import...');

  // Ensure file exists
  if (!fs.existsSync(DATA_FILE_PATH)) {
    throw new Error(`File not found: ${DATA_FILE_PATH}`);
  }

  // Ensure table exists (one-time)
  await ensureTableExists(supabase);

  // Read file
  const raw = fs.readFileSync(DATA_FILE_PATH, 'utf-8');
  const lines = raw.split(/\r?\n/).filter(l => l.trim().length > 0);
  if (lines.length < 2) {
    throw new Error('Report file appears empty or missing data rows');
  }

  // Parse header
  const headerFields = lines[0].split('\t').map(h => h.trim());
  const dbHeaders = headerFields.map(mapHeader);

  // Build rows
  const rows = [];
  let skipped = 0;

  for (let i = 1; i < lines.length; i++) {
    const parts = lines[i].split('\t');
    if (parts.length === 0 || (parts.length === 1 && parts[0].trim() === '')) {
      skipped++;
      continue;
    }

    const row = {};
    for (let c = 0; c < headerFields.length; c++) {
      const header = headerFields[c];
      const dbCol = dbHeaders[c];
      const val = parts[c] !== undefined ? parts[c] : '';

      switch (dbCol) {
        case 'price':
          row[dbCol] = toNumber(val);
          break;
        case 'quantity':
          row[dbCol] = toInt(val);
          break;
        default:
          row[dbCol] = toText(val);
      }
    }
    rows.push(row);
  }

  if (rows.length === 0) {
    throw new Error('No valid data rows to import');
  }

  // Truncate existing data (delete all)
  console.log('[importAmazonAllListingsReport] Truncating existing data...');
  const { error: truncErr } = await supabase.from(TABLE_NAME).delete().neq('id', 0);
  if (truncErr) {
    throw new Error(`Failed to truncate existing data: ${truncErr.message}`);
  }

  // Insert in chunks
  const chunkSize = 1000;
  let total = 0;
  for (let i = 0; i < rows.length; i += chunkSize) {
    const chunk = rows.slice(i, i + chunkSize);
    const { error } = await supabase.from(TABLE_NAME).insert(chunk);
    if (error) {
      throw new Error(`Failed to import chunk starting at row ${i + 1}: ${error.message}`);
    }
    total += chunk.length;
  }

  const message = `Imported ${total} records into ${TABLE_NAME} (truncate and replace)`;
  console.log('[importAmazonAllListingsReport] ' + message);

  return {
    success: true,
    message,
    importCount: total,
    skippedCount: skipped,
    table: TABLE_NAME,
    file: DATA_FILE_PATH,
  };
}

