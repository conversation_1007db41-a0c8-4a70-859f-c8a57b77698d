// testKennethUpdate.js - Test that '<PERSON>' is used instead of 'Ken'
import { createClient } from '@supabase/supabase-js';
import { processCheckIfRproIsReadyTask } from './processCheckIfRproIsReadyTask.js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

// Mock functions for testing
async function updateTaskStatus(taskId, status, result = null) {
  console.log(`📝 Mock updateTaskStatus: Task ${taskId} -> ${status}`);
  if (result && result.issues && result.issues.length > 0) {
    console.log(`   Issues found: ${result.issues.length}`);
    result.issues.forEach(issue => console.log(`     - ${issue}`));
  }
  return true;
}

async function logError(message, context) {
  console.log(`❌ Mock logError: ${message} (Context: ${context})`);
  return true;
}

async function testKennethUpdate() {
  try {
    console.log('🧪 Testing Kenneth name update...');
    console.log('=================================');

    // Get a sample record
    const { data: sampleRecord, error: sampleError } = await supabase
      .from('imported_table_rpro')
      .select('id, ivno')
      .limit(1)
      .single();

    if (sampleError) {
      console.error('❌ Error fetching sample record:', sampleError.message);
      return;
    }

    // Store original values for restoration
    const { data: originalRecord, error: origError } = await supabase
      .from('imported_table_rpro')
      .select('*')
      .eq('id', sampleRecord.id)
      .single();

    if (origError) {
      console.error('❌ Error fetching original record:', origError.message);
      return;
    }

    console.log(`📋 Testing with record ID: ${sampleRecord.id}, IVNO: ${sampleRecord.ivno}`);

    // Test 1: In-stock item with image issue
    console.log('\n🧪 Test 1: In-stock item with image issue');
    console.log('=========================================');
    
    await supabase
      .from('imported_table_rpro')
      .update({
        ivqtylaw: 5,           // In stock
        ivaux3: 'A1',          // Has bin section
        ivaux2: 'wrong image', // Wrong image
        ivprcbt_dollar: 20,    // Valid regular price
        ivprcbtliv: 25,        // Valid live price
        ivprcws_1: 15,         // Valid wholesale 1
        todo: null
      })
      .eq('id', sampleRecord.id);

    const mockTask1 = {
      id: 999991,
      task_type: 'check_if_rpro_is_ready',
      payload: { id: sampleRecord.id }
    };

    await processCheckIfRproIsReadyTask(mockTask1, { supabase, updateTaskStatus, logError });

    // Test 2: In-stock item with pricing issue
    console.log('\n🧪 Test 2: In-stock item with pricing issue');
    console.log('===========================================');
    
    await supabase
      .from('imported_table_rpro')
      .update({
        ivqtylaw: 5,                    // In stock
        ivaux3: 'A1',                   // Has bin section
        ivaux2: '9 White Square Big',   // Correct image
        ivprcbt_dollar: 0,              // Invalid regular price (0)
        ivprcbtliv: 25,                 // Valid live price
        ivprcws_1: 15,                  // Valid wholesale 1
        todo: null
      })
      .eq('id', sampleRecord.id);

    const mockTask2 = {
      id: 999992,
      task_type: 'check_if_rpro_is_ready',
      payload: { id: sampleRecord.id }
    };

    await processCheckIfRproIsReadyTask(mockTask2, { supabase, updateTaskStatus, logError });

    // Test 3: Out-of-stock item with image issue (should not have Kenneth prefix)
    console.log('\n🧪 Test 3: Out-of-stock item with image issue');
    console.log('=============================================');
    
    await supabase
      .from('imported_table_rpro')
      .update({
        ivqtylaw: 0,           // Out of stock
        ivaux2: 'wrong image', // Wrong image
        ivprcbt_dollar: 20,    // Valid regular price
        ivprcbtliv: 25,        // Valid live price
        ivprcws_1: 15,         // Valid wholesale 1
        todo: null
      })
      .eq('id', sampleRecord.id);

    const mockTask3 = {
      id: 999993,
      task_type: 'check_if_rpro_is_ready',
      payload: { id: sampleRecord.id }
    };

    await processCheckIfRproIsReadyTask(mockTask3, { supabase, updateTaskStatus, logError });

    // Restore original values
    console.log('\n🔄 Restoring original values...');
    const restoreFields = {};
    Object.keys(originalRecord).forEach(key => {
      if (key !== 'id') {
        restoreFields[key] = originalRecord[key];
      }
    });

    await supabase
      .from('imported_table_rpro')
      .update(restoreFields)
      .eq('id', sampleRecord.id);

    console.log('✅ Original values restored');
    console.log('\n🎉 Kenneth name update test completed!');
    console.log('✅ Verified that in-stock issues are prefixed with "Kenneth -"');
    console.log('✅ Verified that out-of-stock issues do not have the prefix');

  } catch (err) {
    console.error('❌ Exception:', err.message);
    console.error(err.stack);
  }
}

testKennethUpdate();
