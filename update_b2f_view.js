// Update the B2F view to include MPS release date information
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

async function updateB2FView() {
  console.log('🔍 Testing current v_b2f_pick_slim view structure...');

  try {

    // Test the current view structure
    console.log('🧪 Testing current view...');
    const { data: testData, error: testError } = await supabase
      .from('v_b2f_pick_slim')
      .select('*')
      .limit(3);

    if (testError) {
      console.error('❌ Error testing view:', testError);
      return;
    }

    console.log('✅ Current view structure:');
    if (testData && testData.length > 0) {
      console.log('Available fields:', Object.keys(testData[0]));
      testData.forEach((record, index) => {
        console.log(`  ${index + 1}. Disc: ${record.disc}, OSL: ${record.osl}`);
        console.log(`     Sales: ${record.osl_sold_last_30_dz_plus_retail}`);
        console.log(`     All fields:`, record);
      });
    } else {
      console.log('No records found in view');
    }

  } catch (err) {
    console.error('❌ Exception updating view:', err);
  }
}

updateB2FView();
