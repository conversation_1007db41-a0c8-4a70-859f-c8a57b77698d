// Test that existing parsing still works after fundraiser changes

// Copy the updated functions from fullDiscraftImport.js
function standardizePlasticName(rawPlastic, rawModel, vendorDescription = '') {
    if (!rawPlastic && !rawModel) return 'Unknown';

    const plastic = rawPlastic?.toString().trim() || '';
    const model = rawModel?.toString().trim() || '';

    // Handle fundraiser items first
    if (model.includes('Fundraiser - <PERSON>')) {
        const match = model.match(/Fundraiser - <PERSON> (.+?) (Thrasher|Buzzz|[A-Z][a-z]+)\s*\(/);
        if (match) {
            const plasticPart = match[1].trim(); // "Z Jawbreaker" or "Big Z"
            if (plasticPart.includes('Z Jawbreaker')) {
                return 'Elite Z Jawbreaker';
            } else if (plasticPart.includes('Big Z')) {
                return 'Big Z Collection';
            }
        }
        return 'Unknown'; // Fallback for unrecognized fundraiser items
    }

    // Handle special signature series first
    if (plastic.includes('<PERSON><PERSON><PERSON>')) {
        return 'Elite Z FLX Confetti';
    }

    if (plastic.includes('Anthony Barela Signature Series Z')) {
        return 'Elite Z';
    }

    // Standard mappings
    const plasticMappings = {
        'Z': 'Elite Z',
        'Z Lite': 'Elite Z Lite',
        'ESP': 'ESP',
        'Jawbreaker': 'Jawbreaker',
        'X': 'Elite X',
        'McBeth': 'Putter Line Hard',
        'Pierce': 'Putter Line Hard',
        'Hard': 'Putter Line Hard',
        'Soft': 'Putter Line Soft'
    };

    return plasticMappings[plastic] || plastic || 'Unknown';
}

function extractMoldName(modelStr) {
    if (!modelStr) return 'Unknown';
    
    // Remove common prefixes and suffixes
    let mold = modelStr
        .replace(/^(Fly Dye Z|Z Glo|Z Lite|Z|ESP|Jawbreaker|X)\s+/i, '')
        .replace(/\s+(Discontinued|Retired stamp|NEW).*$/i, '')
        .replace(/^(PS|RW|PP)\s+(Z|ESP)\s+/i, '')
        .replace(/^Soft\s+/i, '') // Remove "Soft" prefix
        .replace(/^Hard\s+/i, '') // Remove "Hard" prefix
        .replace(/^Big Z\s+/i, '') // Remove "Big Z" prefix
        .trim();

    return mold || 'Unknown';
}

function parseMoldAndStamp(rawModel, rawPlastic, vendorDescription = '') {
    const model = rawModel?.toString().trim() || '';
    const plastic = rawPlastic?.toString().trim() || '';
    const vendorDesc = vendorDescription?.toString().trim() || '';

    // Handle fundraiser items first
    if (model.includes('Fundraiser - Ben Askren')) {
        const match = model.match(/Fundraiser - Ben Askren (.+?) (Thrasher|Buzzz|[A-Z][a-z]+)\s*\(/);
        if (match) {
            const moldName = match[2]; // "Thrasher" or "Buzzz"
            return {
                mold_name: moldName,
                stamp_name: 'Live Free - Be Happy - Funky Ben Askren Fundraiser'
            };
        }
    }

    // Handle McBeth signature lines
    if (plastic === 'McBeth') {
        if (model && model.includes('NEW -')) {
            const moldName = extractMoldName(model.replace('NEW - ', ''));
            return {
                mold_name: moldName,
                stamp_name: 'Dye Line Blank Top Bottom'
            };
        }
        return {
            mold_name: extractMoldName(model.replace(/^Z\s+/, '')), // Remove Z prefix if present
            stamp_name: 'PM Logo Stock Stamp'
        };
    }

    // Handle Pierce signature lines
    if (plastic === 'Pierce') {
        if (model && model.includes('NEW -')) {
            const moldName = extractMoldName(model.replace('NEW - ', ''));
            return {
                mold_name: moldName,
                stamp_name: 'Dye Line Blank Top Bottom'
            };
        }
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'PP Logo Stock Stamp'
        };
    }

    // Handle Hard putter line parsing
    if (plastic === 'Hard') {
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'Stock'
        };
    }

    // Handle Soft putter line parsing
    if (plastic === 'Soft') {
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'Stock'
        };
    }

    // Default parsing
    return {
        mold_name: extractMoldName(model),
        stamp_name: 'Stock'
    };
}

// Test existing parsing cases to make sure they still work
console.log('🧪 Testing that existing parsing still works after fundraiser changes...\n');

const existingTestCases = [
    {
        name: 'Regular Z plastic',
        rawPlastic: 'Z',
        rawModel: 'Buzzz',
        expected: { plastic: 'Elite Z', mold: 'Buzzz', stamp: 'Stock' }
    },
    {
        name: 'McBeth signature',
        rawPlastic: 'McBeth',
        rawModel: 'Luna',
        expected: { plastic: 'Putter Line Hard', mold: 'Luna', stamp: 'PM Logo Stock Stamp' }
    },
    {
        name: 'McBeth NEW product',
        rawPlastic: 'McBeth',
        rawModel: 'NEW - Kratos',
        expected: { plastic: 'Putter Line Hard', mold: 'Kratos', stamp: 'Dye Line Blank Top Bottom' }
    },
    {
        name: 'Pierce signature',
        rawPlastic: 'Pierce',
        rawModel: 'Fierce',
        expected: { plastic: 'Putter Line Hard', mold: 'Fierce', stamp: 'PP Logo Stock Stamp' }
    },
    {
        name: 'Hard putter line',
        rawPlastic: 'Hard',
        rawModel: 'Challenger',
        expected: { plastic: 'Putter Line Hard', mold: 'Challenger', stamp: 'Stock' }
    },
    {
        name: 'Soft putter line',
        rawPlastic: 'Soft',
        rawModel: 'Roach',
        expected: { plastic: 'Putter Line Soft', mold: 'Roach', stamp: 'Stock' }
    },
    {
        name: 'ESP plastic',
        rawPlastic: 'ESP',
        rawModel: 'Nuke',
        expected: { plastic: 'ESP', mold: 'Nuke', stamp: 'Stock' }
    }
];

let passCount = 0;
let failCount = 0;

existingTestCases.forEach((testCase, index) => {
    console.log(`${index + 1}. Testing: ${testCase.name}`);
    console.log(`   Input: rawPlastic="${testCase.rawPlastic}", rawModel="${testCase.rawModel}"`);
    
    const plastic = standardizePlasticName(testCase.rawPlastic, testCase.rawModel);
    const { mold_name, stamp_name } = parseMoldAndStamp(testCase.rawModel, testCase.rawPlastic);
    
    console.log(`   Output: plastic="${plastic}", mold="${mold_name}", stamp="${stamp_name}"`);
    console.log(`   Expected: plastic="${testCase.expected.plastic}", mold="${testCase.expected.mold}", stamp="${testCase.expected.stamp}"`);
    
    const plasticMatch = plastic === testCase.expected.plastic;
    const moldMatch = mold_name === testCase.expected.mold;
    const stampMatch = stamp_name === testCase.expected.stamp;
    
    if (plasticMatch && moldMatch && stampMatch) {
        console.log('   ✅ PASS\n');
        passCount++;
    } else {
        console.log('   ❌ FAIL');
        if (!plasticMatch) console.log(`      Plastic mismatch: got "${plastic}", expected "${testCase.expected.plastic}"`);
        if (!moldMatch) console.log(`      Mold mismatch: got "${mold_name}", expected "${testCase.expected.mold}"`);
        if (!stampMatch) console.log(`      Stamp mismatch: got "${stamp_name}", expected "${testCase.expected.stamp}"`);
        console.log('');
        failCount++;
    }
});

console.log(`🎉 Existing parsing verification completed!`);
console.log(`✅ Passed: ${passCount}/${existingTestCases.length}`);
console.log(`❌ Failed: ${failCount}/${existingTestCases.length}`);

if (failCount === 0) {
    console.log('\n🎯 All existing parsing still works correctly after fundraiser changes!');
} else {
    console.log('\n⚠️  Some existing parsing was broken by fundraiser changes - needs fixing!');
}
