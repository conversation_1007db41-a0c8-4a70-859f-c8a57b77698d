import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function run() {
  const discId = 426619;
  const sdasinId = 35390;
  console.log(`Debugging match for disc ${discId} vs SDASIN ${sdasinId}`);

  const { data: disc, error: discErr } = await supabase
    .from('t_discs')
    .select('id, mps_id, weight, color_id, sold_date')
    .eq('id', discId)
    .single();
  if (discErr) {
    console.error('Error fetching disc:', discErr);
    return;
  }

  const { data: sdasin, error: sdasinErr } = await supabase
    .from('t_sdasins')
    .select('id, mps_id, mps_id2, min_weight, max_weight, color_id')
    .eq('id', sdasinId)
    .single();
  if (sdasinErr) {
    console.error('Error fetching SDASIN:', sdasinErr);
    return;
  }

  console.log('Disc:', disc);
  console.log('SDASIN:', sdasin);

  const mpsMatch = disc.mps_id === sdasin.mps_id || disc.mps_id === sdasin.mps_id2;
  const weightMatch = disc.weight != null && sdasin.min_weight != null && sdasin.max_weight != null
    ? (Math.round(disc.weight) >= sdasin.min_weight && Math.round(disc.weight) <= sdasin.max_weight)
    : false;
  const colorMatch = disc.color_id != null && sdasin.color_id != null
    ? (disc.color_id === sdasin.color_id || sdasin.color_id === 23)
    : false;
  const fourteenDaysAgo = new Date();
  fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);
  const notOldSold = !disc.sold_date || (new Date(disc.sold_date) > fourteenDaysAgo);

  console.log('\nCriteria:');
  console.log(`- MPS match: ${mpsMatch} (disc.mps_id=${disc.mps_id}, sdasin.mps_id=${sdasin.mps_id}, mps_id2=${sdasin.mps_id2})`);
  console.log(`- Weight in range (rounded): ${weightMatch} (round(disc.weight)=${Math.round(disc.weight)}, range=[${sdasin.min_weight}, ${sdasin.max_weight}])`);
  console.log(`- Color match (23 wildcard): ${colorMatch} (disc.color_id=${disc.color_id}, sdasin.color_id=${sdasin.color_id})`);
  console.log(`- Disc not sold >14d ago: ${notOldSold} (sold_date=${disc.sold_date})`);

  const { data: reasonRows, error: reasonErr } = await supabase
    .rpc('check_disc_sdasin_match', { disc_id_param: discId, sdasin_id_param: sdasinId });
  if (reasonErr) {
    console.error('Error from check_disc_sdasin_match:', reasonErr);
  } else {
    console.log('\ncheck_disc_sdasin_match result:', reasonRows);
  }

  const { data: join, error: joinErr } = await supabase
    .from('tjoin_discs_sdasins')
    .select('*')
    .eq('disc_id', discId)
    .eq('sdasin_id', sdasinId)
    .order('created_at', { ascending: false })
    .limit(5);
  if (joinErr) {
    console.error('Join query error:', joinErr);
  } else {
    console.log('\nExisting join rows:', join);
  }
}

run().catch((e) => {
  console.error('Unexpected error:', e);
  process.exit(1);
});

