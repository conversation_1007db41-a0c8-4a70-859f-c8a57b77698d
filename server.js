import { exec } from 'child_process';
import express from 'express';
import { downloadFile } from './downloadInnova.js';
import { importData } from './importInnovaOrderTable.js';
import { WebSocketServer } from 'ws';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import os from 'os';
import qrcode from 'qrcode-terminal';
import { updatePrices } from './updateShopifyPrices.js';
import getVeeqoId from './getVeeqoId.js';
import { updateVeeqoQuantity } from './updateVeeqoQuantity.js';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Initialize Express app
const app = express();
app.use(express.json());

// Endpoint to trigger Veeqo quantity update with table lookup
app.post('/trigger-veeqo-qty-update', async (req, res) => {
  const { table, id, qty } = req.body;
  console.log(`🔹 /trigger-veeqo-qty-update received: table=${table}, id=${id}, qty=${qty}`);

  if (!table || !id || qty === undefined) {
    return res.status(400).json({ error: 'Missing table, id, or qty' });
  }

  try {
    let veeqo_id, sku;
    let actualTable = table; // The actual table to update (might be different from input table)

    // Handle different table structures
    if (table === 't_discs') {
      // For t_discs, construct SKU as "D" + id and get veeqo_id directly
      sku = `D${id}`;
      actualTable = 't_discs';

      // Log the query we're about to make
      console.log(`🔍 Querying t_discs for id=${id}`);

      const { data, error } = await supabase
        .from('t_discs')
        .select('veeqo_id, sold_date')
        .eq('id', id)
        .single();

      if (error) {
        console.error(`❌ Error fetching t_discs record: ${error.message}`);
        return res.status(500).json({ error: `Database query failed: ${error.message}` });
      }

      if (!data) {
        console.log(`⚠️ Disc record not found for id=${id}`);
        return res.status(404).json({ error: 'Disc record not found' });
      }

      // Log what we found
      console.log(`✅ Found disc record: veeqo_id=${data.veeqo_id}, sold_date=${data.sold_date}`);

      veeqo_id = data.veeqo_id;

      // Check if the disc is already marked as sold
      if (data.sold_date) {
        console.log(`ℹ️ Disc ${id} is already marked as sold (${data.sold_date}). Proceeding with qty update.`);
      }
    }
    else if (table === 't_inv_osl') {
      // For t_inv_osl, we need to get veeqo_id from t_order_sheet_lines
      // Construct SKU as "OS" + id
      sku = `OS${id}`;
      actualTable = 't_order_sheet_lines';
      const { data, error } = await supabase
        .from('t_order_sheet_lines')
        .select('veeqo_id')
        .eq('id', id)
        .single();

      if (error) {
        console.error(`❌ Error fetching t_order_sheet_lines record: ${error.message}`);
        return res.status(500).json({ error: `Database query failed: ${error.message}` });
      }

      if (!data) {
        return res.status(404).json({ error: 'Order sheet line record not found' });
      }

      veeqo_id = data.veeqo_id;
    }
    else if (table === 't_inv_sdasin') {
      // For t_inv_sdasin, we need to get veeqo_id from t_sdasins
      console.log(`🔍 Querying t_sdasins for id=${id} to get veeqo_id`);

      // Use maybeSingle() instead of single() to avoid errors when multiple or no records are found
      const { data, error } = await supabase
        .from('t_sdasins')
        .select('veeqo_id')
        .eq('id', id)
        .maybeSingle();

      if (error) {
        console.error(`❌ Error fetching t_sdasins record: ${error.message}`);
        return res.status(500).json({ error: `Database query failed: ${error.message}` });
      }

      if (!data) {
        console.log(`⚠️ SDASIN record not found for id=${id}`);

        // Try to get the record from t_inv_sdasin directly
        console.log(`🔍 Trying to get record directly from t_inv_sdasin for id=${id}`);

        const { data: invData, error: invError } = await supabase
          .from('t_inv_sdasin')
          .select('*')
          .eq('id', id)
          .maybeSingle();

        if (invError) {
          console.error(`❌ Error fetching t_inv_sdasin record: ${invError.message}`);
          return res.status(500).json({ error: `Database query failed: ${invError.message}` });
        }

        if (!invData) {
          console.log(`⚠️ Record not found in t_inv_sdasin for id=${id}`);
          return res.status(404).json({ error: 'Record not found in t_inv_sdasin' });
        }

        // Generate SKU as "Disc_" + id according to the schema
        sku = `Disc_${id}`;
        console.log(`✅ Generated SKU: ${sku} for t_inv_sdasin id=${id}`);

        // We don't have a veeqo_id yet, but we'll try to get it later
        actualTable = 't_sdasins'; // Set the actual table for potential updates
      }

      // Generate SKU as "Disc_" + id according to the schema
      sku = `Disc_${id}`;

      // Log what we found
      console.log(`✅ Found SDASIN record: veeqo_id=${data.veeqo_id}, generated sku=${sku}`);

      veeqo_id = data.veeqo_id;
      actualTable = 't_sdasins'; // Set the actual table for potential updates
    }
    else {
      // For other tables, use standard approach
      actualTable = table;
      const { data, error } = await supabase
        .from(table)
        .select('veeqo_id, sku')
        .eq('id', id)
        .single();

      if (error) {
        console.error(`❌ Error fetching record: ${error.message}`);
        return res.status(500).json({ error: `Database query failed: ${error.message}` });
      }

      if (!data) {
        return res.status(404).json({ error: 'Record not found' });
      }

      veeqo_id = data.veeqo_id;
      sku = data.sku;
    }

    // Step 2 - If no veeqo_id, get it now
    if (!veeqo_id && sku) {
      console.log(`🔄 No veeqo_id, trying to fetch for ${table} id ${id} with SKU ${sku}`);
      veeqo_id = await getVeeqoId(sku);

      if (veeqo_id) {
        console.log(`✅ Found Veeqo ID ${veeqo_id} for SKU ${sku}`);

        // Update the database with the found veeqo_id if possible
        try {
          await supabase
            .from(actualTable)
            .update({ veeqo_id })
            .eq('id', id);
          console.log(`✅ Updated ${actualTable} with Veeqo ID ${veeqo_id}`);
        } catch (updateErr) {
          console.error(`⚠️ Could not update ${actualTable} with Veeqo ID: ${updateErr.message}`);
          // Continue anyway since we have the veeqo_id
        }
      } else {
        console.log(`⚠️ Could not find veeqo_id for SKU ${sku}`);
        return res.status(404).json({ error: `Could not find veeqo_id for SKU ${sku}` });
      }
    }

    if (!veeqo_id) {
      console.log(`⚠️ No veeqo_id available for ${table} id ${id}. Skipping Veeqo quantity update.`);
      return res.json({ status: 'Skipped', message: 'No veeqo_id available', table, id, qty });
    }

    console.log(`🔄 About to update Veeqo quantity for ID ${veeqo_id} to ${qty}`);

    // Step 3 - Perform quantity update with a timeout
    try {
      // Set a timeout for the Veeqo API call
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Veeqo API call timed out after 4 seconds')), 4000)
      );

      // Race the actual API call against the timeout
      const result = await Promise.race([
        updateVeeqoQuantity(veeqo_id, qty, actualTable, id),
        timeoutPromise
      ]);

      if (result.success) {
        console.log(`✅ Successfully updated Veeqo quantity for ${table} id ${id}`);

        // If this is a disc with qty=0, ensure sold_date is set
        if (table === 't_discs' && qty === 0) {
          console.log(`🔄 Disc quantity set to 0, updating sold_date`);

          try {
            const soldDate = new Date().toISOString();

            // Use a separate, dedicated update operation to ensure it commits properly
            const { data, error } = await supabase
              .from('t_discs')
              .update({ sold_date: soldDate })
              .eq('id', id)
              .select('id, sold_date'); // Request the updated record back to confirm

            if (error) {
              console.error(`❌ Failed to update sold_date: ${error.message}`);
            } else {
              console.log(`✅ Updated sold_date to ${soldDate} for disc ${id}. Confirmed update:`, data);
            }

            // Wait a moment to ensure the update is fully processed
            await new Promise(resolve => setTimeout(resolve, 500));

            // Double-check that the update was committed
            const { data: checkData, error: checkError } = await supabase
              .from('t_discs')
              .select('sold_date')
              .eq('id', id)
              .single();

            if (checkError) {
              console.error(`❌ Error verifying sold_date update: ${checkError.message}`);
            } else if (checkData && checkData.sold_date) {
              console.log(`✅ Verified sold_date is set to ${checkData.sold_date}`);
            } else {
              console.error(`⚠️ Verification failed: sold_date is still null after update`);
            }

            return res.json({
              status: 'Success',
              veeqo_id,
              qty,
              sold_date: soldDate,
              sold_date_updated: !error
            });
          } catch (updateErr) {
            console.error(`❌ Exception during sold_date update: ${updateErr.message}`);
            return res.json({
              status: 'Success',
              veeqo_id,
              qty,
              sold_date_error: updateErr.message
            });
          }
        }

        return res.json({ status: 'Success', veeqo_id, qty });
      } else {
        console.error(`❌ Failed to update Veeqo quantity: ${result.error}`);

        // Check if the error is about product not found
        if (result.error.includes('not found in Veeqo')) {
          console.log(`🔄 Product not found in Veeqo. Removing veeqo_id and updating notes in ${actualTable}`);

          try {
            // Update the record to remove veeqo_id and add a note
            // But don't touch sold_date - let the webhook handle that independently
            await supabase
              .from(actualTable)
              .update({
                veeqo_id: null,
                veeqo_id_notes: `Product ID ${veeqo_id} not found in Veeqo on ${new Date().toISOString()}`
              })
              .eq('id', id);

            console.log(`✅ Updated ${actualTable} record: removed veeqo_id and added note`);

            return res.status(404).json({
              status: 'Updated',
              message: `Product not found in Veeqo. Removed veeqo_id from ${actualTable}.`,
              error: result.error
            });
          } catch (updateErr) {
            console.error(`❌ Failed to update ${actualTable} record: ${updateErr.message}`);
            // Continue to return the original error
          }
        }

        return res.status(500).json({ error: result.error });
      }
    } catch (timeoutError) {
      console.error(`⏱️ ${timeoutError.message}`);
      return res.status(504).json({ error: timeoutError.message });
    }
  } catch (err) {
    console.error(`❌ Unexpected error in /trigger-veeqo-qty-update: ${err.message}`);
    return res.status(500).json({ error: err.message });
  }
});

const PORT = 3000; // You can change this if needed


// --- __dirname fix ---
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// --- Static serving ---
app.use('/camera', express.static(path.join(__dirname, 'public', 'camera')));

// Handle CORS Preflight Requests
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.sendStatus(200); // Send 200 OK for OPTIONS requests
  }

  next(); // Move on to other middleware
});


function getLocalIPAddress() {
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
        for (const iface of interfaces[name]) {
            if (iface.family === 'IPv4' && !iface.internal) {
                return iface.address;
            }
        }
    }
    return 'localhost';
}


// end point to trigger reconcile osl to veeqo
app.post('/reconcile_d_to_veeqo', (req, res) => {
  console.log(`🔹 /reconcile_inv_osl_to_veeqo webhook triggered.`);

  // Execute the reconcileInvOslToVeeqo.js script
  exec('node reconcileDToVeeqo.js', (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return res.status(500).send('Script execution failed');
    }
    if (stderr) {
      console.error(`⚠️ Stderr: ${stderr}`);
    }
    console.log(`✅ Output: ${stdout}`);
    res.send('Script executed successfully');
  });
});



// end point to trigger processDiscEntryRecords.js
app.post('/process_disc_entry_records', (req, res) => {
  console.log(`🔹 /process_disc_entry_records webhook triggered.`);

  // Execute the processDiscEntryRecords.js script
  exec('node processDiscEntryRecords.js', (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return res.status(500).send('Script execution failed');
    }
    if (stderr) {
      console.error(`⚠️ Stderr: ${stderr}`);
    }
    console.log(`✅ Output: ${stdout}`);
    res.send('Script executed successfully');
  });
});







// end point to trigger delete bag and card variations
app.post('/delete_bag_and_card_veeqo_variations', (req, res) => {
  console.log(`🔹 /delete_bag_and_card_veeqo_variations webhook triggered.`);

  // Execute the deleteBagCardVariationsFromVeeqo.js script
  exec('node deleteBagCardVariationsFromVeeqo.js', (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return res.status(500).send('Script execution failed');
    }
    if (stderr) {
      console.error(`⚠️ Stderr: ${stderr}`);
    }
    console.log(`✅ Output: ${stdout}`);
    res.send('Script executed successfully');
  });
});




//
app.post('/download-and-import-innova-osl', async (req, res) => {
  // Log the request with a timestamp
  console.log(`[${new Date().toISOString()}] Webhook endpoint '/download-and-import-innova-osl' triggered.`);

  try {
    // Optional: Validate an authentication header or token if needed
    // if (req.headers['x-auth-token'] !== process.env.WEBHOOK_TOKEN) {
    //   return res.status(401).send('Unauthorized');
    // }

    // Step 1: Download the latest file
    console.log(`[${new Date().toISOString()}] Starting file download...`);
    await downloadFile();
    console.log(`[${new Date().toISOString()}] File download completed.`);

    // Step 2: Import the data into Supabase
    console.log(`[${new Date().toISOString()}] Starting data import into Supabase...`);
    await importData();
    console.log(`[${new Date().toISOString()}] Data import completed successfully.`);

    // Respond with a success message
    res.status(200).send('Download and import completed successfully.');
  } catch (error) {
    // Log the error with a timestamp for easier debugging
    console.error(`[${new Date().toISOString()}] Error during download/import process:`, error);
    res.status(500).send(`Error: ${error.message}`);
  }
});


// New endpoint to generate MPS new release tags
app.post('/generate-mps-new-release-tags', (req, res) => {
  console.log(`🔹 /generate-mps-new-release-tags webhook triggered.`);

  // Execute the generateMPSRetailTags.js script
  exec('node generateMPSRetailTags.js', (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return res.status(500).send('Script execution failed');
    }
    if (stderr) {
      console.error(`⚠️ Stderr: ${stderr}`);
    }
    console.log(`✅ Output: ${stdout}`);
    res.send('MPS new release retail tags generated successfully.');
  });
});



//
app.post('/veeqo-update', async (req, res) => {
  const { table, id, sku } = req.body;
  console.log(`🔄 /veeqo-update webhook: table=${table} id=${id} sku=${sku}`);

  // Check if all required parameters are present
  if (!table || !id || !sku) {
    console.error(`❌ Missing required parameters: table=${table}, id=${id}, sku=${sku}`);
    return res.status(400).json({ error: 'Missing required parameters' });
  }

  const productId = await getVeeqoId(sku);

  // Log the exact table name for debugging
  console.log(`📋 Received table name: "${table}" (type: ${typeof table})`);

  // Normalize table name (trim whitespace)
  const normalizedTable = String(table).trim();

  // Define allowed tables - now including t_sdasins and t_order_sheet_lines
  const allowedTables = [
    't_discs',
    't_inv_sdasin',
    't_inv_osl',
    't_sdasins',
    't_order_sheet_lines'
  ];

  // Check if normalized table name is in the allowed list
  if (!allowedTables.includes(normalizedTable)) {
    console.error(`❌ Invalid table name: "${table}" not in allowed list: ${allowedTables.join(', ')}`);

    try {
      // Try to update the table anyway with an error note
      await supabase.from(normalizedTable).update({
        veeqo_id_notes: `Table name "${table}" not in allowed list for SKU ${sku} on ${new Date().toISOString()}`
      }).eq('id', id);
      console.log(`⚠️ Attempted to update notes in non-allowed table: ${normalizedTable}`);
    } catch (error) {
      console.error(`❌ Failed to update table: ${error.message}`);
    }

    return res.status(400).json({ error: `Invalid table name: "${table}" not in allowed list` });
  }

  if (productId) {
    // Set veeqo_id
    try {
      await supabase.from(normalizedTable).update({
        veeqo_id: productId,
        veeqo_id_notes: null
      }).eq('id', id);
      console.log(`✅ Updated ${normalizedTable} with Veeqo ID ${productId} for SKU ${sku}`);
      return res.json({ message: 'Veeqo ID updated', productId });
    } catch (error) {
      console.error(`❌ Error updating ${normalizedTable}: ${error.message}`);
      return res.status(500).json({ error: `Database update failed: ${error.message}` });
    }
  } else {
    // Mark it with a note
    try {
      await supabase.from(normalizedTable).update({
        veeqo_id_notes: `Veeqo ID not found for SKU ${sku} on ${new Date().toISOString()}`
      }).eq('id', id);
      console.log(`⚠️ No Veeqo ID found for SKU ${sku}, updated notes in ${normalizedTable}`);
      return res.status(404).json({ error: 'Veeqo ID not found' });
    } catch (error) {
      console.error(`❌ Error updating ${normalizedTable} notes: ${error.message}`);
      return res.status(500).json({ error: `Database update failed: ${error.message}` });
    }
  }
});





// The '/verify-image' endpoint called by Postgres (via http_post or a webhook):
app.post('/verify-image', (req, res) => {
  const { id, table_name, record_id } = req.body;
  if (!id || !table_name || !record_id) {
    return res.status(400).json({ error: 'Missing id, table_name or record_id in payload' });
  }
  console.log(`🔹 /verify-image triggered for t_images row id=${id}`);

  // Call our verifyImage.js script, passing id, table_name and record_id as CLI arguments.
  exec(`node verifyImage.js --id=${id} --table_name=${table_name} --record_id=${record_id}`, (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return res.status(500).send('Script execution failed');
    }
    if (stderr) {
      console.error(`⚠️ Stderr: ${stderr}`);
    }
    console.log(`✅ Output: ${stdout}`);
    res.send('Script executed successfully');
  });
});



//
app.post('/veeqo-update-old', async (req, res) => {
  // Expect the payload to have { sku, sdasin_id }
  const { sku, sdasin_id } = req.body;
  console.log(`🔹 /veeqo-update webhook triggered for sdasin_id: ${sdasin_id}, sku: ${sku}`);

  // Build the URL for Veeqo’s sellables endpoint.
  // The API expects the sku value to be quoted in JSON. We’ll do that here.
  const encodedSku = encodeURIComponent(`"${sku}"`);
  const url = `https://api.veeqo.com/sellables?filters%5Bsku_code%5D%5B%5D=${encodedSku}`;

  const options = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': 'Vqt/16c3acd642be598d3ca079590a8aae87'
    }
  };

  try {
    const response = await fetch(url, options);
    if (!response.ok) {
      const errMsg = `Veeqo API error: ${response.status}`;
      console.error(errMsg);
      return res.status(500).json({ error: errMsg });
    }
    const data = await response.json();

    if (Array.isArray(data) && data.length > 0 && data[0].product && data[0].product.id) {
      const productId = data[0].product.id;
      console.log(`✅ Found product id ${productId} for sku ${sku}`);
      // Return the product id in the response
      return res.json({ productId });
    } else {
      const errMsg = `No product found for sku ${sku}`;
      console.error(errMsg);
      return res.status(404).json({ error: errMsg });
    }
  } catch (err) {
    console.error('Error calling Veeqo API:', err);
    return res.status(500).json({ error: err.message });
  }
});



//
app.post('/upload_csv', (req, res) => {
    console.log('🔹 Webhook received. Running local script...');

    // Run the uploadCsv.js script
    exec('node uploadCsv.js', (error, stdout, stderr) => {
        if (error) {
            console.error(`❌ Error: ${error.message}`);
            return res.status(500).send('Script execution failed');
        }
        if (stderr) {
            console.error(`⚠️ Stderr: ${stderr}`);
        }
        console.log(`✅ Output: ${stdout}`);
        res.send('Script executed successfully');
    });
});


// New endpoint to trigger publishing a single collection.
app.post('/publishCollectionBrand', (req, res) => {
  // Expect the payload to include at least { id: <brand id> }
  const payload = req.body;
  if (!payload.id) {
    return res.status(400).json({ error: 'Missing brand id in payload' });
  }
  console.log(`🔹 /publishCollectionBrand webhook triggered for brand id: ${payload.id}`);

  // Execute the publishCollectionBrand.js script, passing the brand id as an argument.
  exec(`node publishCollectionBrand.js --id=${payload.id}`, (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return res.status(500).send('Script execution failed');
    }
    if (stderr) {
      console.error(`⚠️ Stderr: ${stderr}`);
    }
    console.log(`✅ Output: ${stdout}`);
    res.send('Script executed successfully');
  });
});


// New endpoint to trigger publishing a single plastic collection.
app.post('/publishCollectionPlastic', (req, res) => {
  // Expect the payload to include at least { id: <plastic id> }
  const payload = req.body;
  if (!payload.id) {
    return res.status(400).json({ error: 'Missing plastic id in payload' });
  }
  console.log(`🔹 /publishCollectionPlastic webhook triggered for plastic id: ${payload.id}`);

  // Execute the publishCollectionPlastic.js script, passing the plastic id as an argument.
  exec(`node publishCollectionPlastic.js --id=${payload.id}`, (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return res.status(500).send('Script execution failed');
    }
    if (stderr) {
      console.error(`⚠️ Stderr: ${stderr}`);
    }
    console.log(`✅ Output: ${stdout}`);
    res.send('Script executed successfully');
  });
});



// Endpoint to trigger publishing a single MPS collection.
app.post('/publishCollectionMPS', (req, res) => {
  // Expect the payload to include at least { id: <mps id> }
  const payload = req.body;
  if (!payload.id) {
    return res.status(400).json({ error: 'Missing mps id in payload' });
  }
  console.log(`🔹 /publishCollectionMPS webhook triggered for mps id: ${payload.id}`);

  // Execute the publishCollectionMPS.js script, passing the mps id as an argument.
  exec(`node publishCollectionMPS.js --id=${payload.id}`, (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return res.status(500).send('Script execution failed');
    }
    if (stderr) {
      console.error(`⚠️ Stderr: ${stderr}`);
    }
    console.log(`✅ Output: ${stdout}`);
    res.send('Script executed successfully');
  });
});


// Endpoint to trigger publishing a single Product OSL.
app.post('/publishProductOSL', (req, res) => {
  // Expect the payload to include at least { id: <t_order_sheet_lines id> }
  const payload = req.body;
  if (!payload.id) {
    return res.status(400).json({ error: 'Missing product OSL id in payload' });
  }
  console.log(`🔹 /publishProductOSL webhook triggered for product OSL id: ${payload.id}`);

  // Execute the publishProductOSL.js script, passing the product OSL id as an argument.
  exec(`node publishProductOSL.js --id=${payload.id}`, (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return res.status(500).send('Script execution failed');
    }
    if (stderr) {
      console.error(`⚠️ Stderr: ${stderr}`);
    }
    console.log(`✅ Output: ${stdout}`);
    res.send('Script executed successfully');
  });
});



// Endpoint to trigger publishing a single Product Disc.
app.post('/publishProductDisc', (req, res) => {
  // Expect the payload to include at least { id: <t_discs id> }
  const payload = req.body;
  if (!payload.id) {
    return res.status(400).json({ error: 'Missing disc id in payload' });
  }
  console.log(`🔹 /publishProductDisc webhook triggered for disc id: ${payload.id}`);

  // Execute the publishProductDisc.js script, passing the disc id as an argument.
  exec(`node publishProductDisc.js --id=${payload.id}`, (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      // Return a 500 error response so the worker knows the task failed.
      return res.status(500).json({ status: 'error', message: error.message });
    }
    if (stderr) {
      console.error(`⚠️ Stderr: ${stderr}`);
    }
    console.log(`✅ Output: ${stdout}`);
    // If we reach here, the disc publish was successful.
    res.status(200).json({ status: 'complete', message: 'Script executed successfully', discId: payload.id });
  });
});



// end point to trigger reconcile osl to veeqo
app.post('/reconcile_inv_osl_to_veeqo', (req, res) => {
  console.log(`🔹 /reconcile_inv_osl_to_veeqo webhook triggered.`);

  // Execute the reconcileInvOslToVeeqo.js script
  exec('node reconcileInvOslToVeeqo.js', (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return res.status(500).send('Script execution failed');
    }
    if (stderr) {
      console.error(`⚠️ Stderr: ${stderr}`);
    }
    console.log(`✅ Output: ${stdout}`);
    res.send('Script executed successfully');
  });
});


// Endpoint to trigger publishing a single Mold collection.
app.post('/publishCollectionMold', (req, res) => {
  // Expect the payload to include at least { id: <mold id> }
  const payload = req.body;
  if (!payload.id) {
    return res.status(400).json({ error: 'Missing mold id in payload' });
  }
  console.log(`🔹 /publishCollectionMold webhook triggered for mold id: ${payload.id}`);

  // Execute the publishCollectionMold.js script, passing the mold id as an argument.
  exec(`node publishCollectionMold.js --id=${payload.id}`, (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return res.status(500).send('Script execution failed');
    }
    if (stderr) {
      console.error(`⚠️ Stderr: ${stderr}`);
    }
    console.log(`✅ Output: ${stdout}`);
    res.send('Script executed successfully');
  });
});




//
app.post('/import_to_it_infor_no_buy_box', (req, res) => {
    console.log('🔹 Webhook received. Running local script...');

    // Run the import_to_it_infor_no_buy_box.js script
    exec('node import_to_it_infor_no_buy_box.js', (error, stdout, stderr) => {
        if (error) {
            console.error(`❌ Error: ${error.message}`);
            return res.status(500).send('Script execution failed');
        }
        if (stderr) {
            console.error(`⚠️ Stderr: ${stderr}`);
        }
        console.log(`✅ Output: ${stdout}`);
        res.send('Script executed successfully');
    });
});

//
app.post('/import_to_it_infor_all_fields', (req, res) => {
    console.log('🔹 Webhook received. Running local script...');

    // Run the import_to_it_infor_all_fields.js script
    exec('node import_to_it_infor_all_fields.js', (error, stdout, stderr) => {
        if (error) {
            console.error(`❌ Error: ${error.message}`);
            return res.status(500).send('Script execution failed');
        }
        if (stderr) {
            console.error(`⚠️ Stderr: ${stderr}`);
        }
        console.log(`✅ Output: ${stdout}`);
        res.send('Script executed successfully');
    });
});

//
app.post('/import_to_it_infor_competition_landscape', (req, res) => {
    console.log('🔹 Webhook received. Running local script...');

    // Run the import_to_it_infor_competition_landscape.js script
    exec('node import_to_it_infor_competition_landscape.js', (error, stdout, stderr) => {
        if (error) {
            console.error(`❌ Error: ${error.message}`);
            return res.status(500).send('Script execution failed');
        }
        if (stderr) {
            console.error(`⚠️ Stderr: ${stderr}`);
        }
        console.log(`✅ Output: ${stdout}`);
        res.send('Script executed successfully');
    });
});


// Ensure photos directory exists
const photosDir = path.join(__dirname, 'photos');
if (!fs.existsSync(photosDir)) {
    fs.mkdirSync(photosDir);
    console.log(`🟣 Created photos directory at ${photosDir}`);
} else {
    console.log(`🟣 Photos directory exists at ${photosDir}`);
}



// Start the server
const localIP = getLocalIPAddress();
const cameraURL = `http://${localIP}:${PORT}/camera/`;

const server = app.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 Webhook + Camera server listening on port ${PORT} (LAN accessible)`);
    console.log(`📸 Open this on your iPhone: ${cameraURL}`);
    console.log(`🌐 LAN access: http://${localIP}:${PORT}/`);
    qrcode.generate(cameraURL, { small: true }); // <-- Display QR Code
});



// ---- after your existing app.listen ----

const wss = new WebSocketServer({ server });

let activeWS = null; // <--- Global for latest active iPhone connection

wss.on('connection', (ws) => {
    console.log('📱 iPhone connected via WebSocket');
    activeWS = ws;
    let iphoneReady = false;

    ws.on('message', (data) => {
        const str = data.toString();

        if (str === 'ready') {
            iphoneReady = true;
            console.log('✅ iPhone reports camera is ready');
            return;
        }

        if (str.startsWith('data:image')) {
            const base64Data = str.replace(/^data:image\/(png|jpeg);base64,/, '');
            const filename = `photo_${Date.now()}.png`;
            const filepath = path.join(__dirname, 'photos', filename);
            fs.writeFileSync(filepath, base64Data, 'base64');
            console.log(`✅ Saved image: ${filename}`);
            ws.send('saved');
        }
    });
});

// ✅ ONE stdin trigger bound globally (not inside wss.on)
if (!process.stdin.listenerCount('data')) {
    process.stdin.on('data', () => {
        if (!activeWS) {
            console.log('⚠️ No iPhone connected.');
            return;
        }
        activeWS.send('capture');
        console.log('🟣 Sent capture command to iPhone');
    });
}

// end point to reset oldest sdasins records
app.post('/reset_oldest_sdasins', (req, res) => {
  console.log(`🔹 /reset_oldest_sdasins webhook triggered.`);

  // Execute the reset_oldest_sdasins.js script
  exec('node reset_oldest_sdasins.js', (error, stdout, stderr) => {
    if (error) {
      console.error(`❌ Error: ${error.message}`);
      return res.status(500).send('Script execution failed');
    }
    if (stderr) {
      console.error(`⚠️ Stderr: ${stderr}`);
    }
    console.log(`✅ Output: ${stdout}`);
    res.send('Script executed successfully');
  });
});

// Endpoint to update Shopify prices
app.post('/plastic_price_change', express.json(), async (req, res) => {
  console.log(`🔹 /update-shopify-prices webhook triggered.`);

  if (!req.body || !req.body.discs) {
    return res.status(400).json({
      status: 'error',
      message: 'Missing required data: discs array'
    });
  }

  try {
    // Process the price updates
    const results = await updatePrices(req.body.discs);

    // Return the results
    res.status(200).json({
      status: 'success',
      message: `Successfully processed ${results.success.length} variants, failed to process ${results.failed.length} variants`,
      results
    });
  } catch (error) {
    console.error(`❌ Error processing price updates: ${error.message}`);
    res.status(500).json({
      status: 'error',
      message: `Error processing price updates: ${error.message}`
    });
  }
});
