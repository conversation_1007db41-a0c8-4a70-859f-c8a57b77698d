require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkUnsoldDiscsNullVendorOsl() {
  try {
    console.log('Checking unsold discs with null vendor_osl_id...');
    
    // Get count of unsold discs with null vendor_osl_id
    const { count: unsoldNullCount, error: countError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('vendor_osl_id', null)
      .is('sold_date', null)  // Unsold discs
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (countError) {
      console.error('Error getting count:', countError);
      return;
    }
    
    console.log(`Unsold discs with null vendor_osl_id: ${unsoldNullCount}`);
    
    if (unsoldNullCount === 0) {
      console.log('✅ No unsold discs remaining with null vendor_osl_id!');
      return;
    }
    
    // Get a larger sample of these unsold discs to see what we're dealing with
    const { data: sampleDiscs, error: sampleError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .is('vendor_osl_id', null)
      .is('sold_date', null)  // Unsold discs
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null)
      .limit(20);  // Larger sample for unsold discs
    
    if (sampleError) {
      console.error('Error getting sample:', sampleError);
      return;
    }
    
    console.log(`\nChecking ${sampleDiscs.length} sample unsold discs for potential vendor OSL matches:`);
    
    let shouldHaveMatches = 0;
    let checkedCount = 0;
    
    for (const disc of sampleDiscs) {
      checkedCount++;
      console.log(`\n${checkedCount}. Disc ${disc.id}: MPS ${disc.mps_id}, Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id}`);
      console.log(`   Sold date: ${disc.sold_date || 'NULL (unsold)'}, Regular OSL: ${disc.order_sheet_line_id}`);
      
      // Test if this disc should have a vendor OSL match
      const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
        'find_matching_osl_by_mfg_weight',
        {
          mps_id_param: disc.mps_id,
          color_id_param: disc.color_id,
          weight_mfg_param: disc.weight_mfg
        }
      );
      
      if (vendorOslError) {
        console.log(`   ❌ Error testing function: ${vendorOslError.message}`);
        continue;
      }
      
      if (vendorOslData && vendorOslData.length > 0) {
        const vendorOslId = vendorOslData[0].osl_id;
        console.log(`   🎯 SHOULD MATCH OSL ${vendorOslId}!`);
        shouldHaveMatches++;
        
        if (vendorOslId !== disc.order_sheet_line_id) {
          console.log(`   📊 Different mappings: regular OSL ${disc.order_sheet_line_id}, vendor OSL ${vendorOslId}`);
        } else {
          console.log(`   📊 Same OSL for both mappings: ${vendorOslId}`);
        }
      } else {
        console.log(`   ❌ No vendor OSL match found`);
        
        // Show why there's no match for the first few
        if (checkedCount <= 5) {
          const { data: availableOsls, error: oslError } = await supabase
            .from('t_order_sheet_lines')
            .select('id, min_weight, max_weight, color_id')
            .eq('mps_id', disc.mps_id)
            .in('color_id', [disc.color_id, 23])
            .order('min_weight')
            .limit(5);
          
          if (!oslError && availableOsls && availableOsls.length > 0) {
            const roundedWeight = Math.round(disc.weight_mfg);
            console.log(`   Available OSLs for MPS ${disc.mps_id}:`);
            availableOsls.forEach(osl => {
              const inRange = roundedWeight >= osl.min_weight && roundedWeight <= osl.max_weight;
              console.log(`     OSL ${osl.id}: ${osl.min_weight}-${osl.max_weight}g, color ${osl.color_id}, fits ${roundedWeight}g? ${inRange ? '✅' : '❌'}`);
            });
          } else {
            console.log(`   No OSLs available for MPS ${disc.mps_id} with colors ${disc.color_id} or 23`);
          }
        }
      }
    }
    
    console.log(`\n📊 UNSOLD DISCS ANALYSIS:`);
    console.log(`Total unsold discs with null vendor_osl_id: ${unsoldNullCount}`);
    console.log(`Sample discs checked: ${checkedCount}`);
    console.log(`Sample discs that should have vendor matches: ${shouldHaveMatches}`);
    
    if (shouldHaveMatches > 0) {
      const estimatedMatches = Math.round((shouldHaveMatches / checkedCount) * unsoldNullCount);
      console.log(`Estimated unsold discs that could get vendor mappings: ~${estimatedMatches}`);
      console.log('\n🔍 There are unsold discs that should have vendor OSL mappings!');
      console.log('Would you like me to process all unsold discs to find and update the missing vendor mappings?');
    } else {
      console.log('\n✅ All sampled unsold discs correctly have no vendor OSL matches.');
      console.log('This suggests the remaining unsold discs have weights that fall outside OSL ranges.');
    }
    
    // Also show current vendor mapping stats for unsold discs
    const { count: unsoldWithVendorOsl, error: unsoldVendorError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .not('vendor_osl_id', 'is', null)
      .is('sold_date', null);  // Unsold discs
    
    if (!unsoldVendorError) {
      console.log(`\nCurrent unsold discs with vendor_osl_id: ${unsoldWithVendorOsl}`);
      const totalUnsoldEligible = unsoldWithVendorOsl + unsoldNullCount;
      const unsoldSuccessRate = ((unsoldWithVendorOsl / totalUnsoldEligible) * 100).toFixed(1);
      console.log(`Unsold discs vendor mapping success rate: ${unsoldSuccessRate}%`);
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

checkUnsoldDiscsNullVendorOsl();
