// checkVeeqoIds.js - Script to check if Veeqo product IDs in our database are correct

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Create a log file
const logFile = 'check_veeqo_ids.log';
fs.writeFileSync(logFile, `Starting Veeqo ID check at ${new Date().toISOString()}\n`);

// Load environment variables
dotenv.config();
fs.appendFileSync(logFile, `Environment variables loaded\n`);

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

// Veeqo API configuration
const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!supabaseUrl || !supabaseKey) {
  const errorMsg = 'Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file';
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

if (!veeqoApiKey) {
  const errorMsg = 'Error: VEEQO_API_KEY must be set in .env file';
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

fs.appendFileSync(logFile, `Supabase and Veeqo credentials found\n`);
const supabase = createClient(supabaseUrl, supabaseKey);
fs.appendFileSync(logFile, `Supabase client created\n`);

// Function to get Veeqo products
async function getVeeqoProducts(page = 1, pageSize = 25) {
  try {
    fs.appendFileSync(logFile, `Fetching Veeqo products (page ${page}, page_size ${pageSize})...\n`);
    console.log(`Fetching Veeqo products (page ${page}, page_size ${pageSize})...`);
    
    // Veeqo API endpoint for getting products
    const url = `https://api.veeqo.com/products?page=${page}&page_size=${pageSize}`;
    
    // Make the API request
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      fs.appendFileSync(logFile, `Error fetching Veeqo products: ${response.status} ${response.statusText} - ${errorText}\n`);
      console.error(`Error fetching Veeqo products: ${response.status} ${response.statusText} - ${errorText}`);
      return { products: [], meta: { pagination: { total_pages: 0 } } };
    }
    
    const data = await response.json();
    
    fs.appendFileSync(logFile, `Found ${data.length} products on page ${page}\n`);
    console.log(`Found ${data.length} products on page ${page}`);
    
    // Check if there's pagination information
    let totalPages = 1;
    if (data.meta && data.meta.pagination && data.meta.pagination.total_pages) {
      totalPages = data.meta.pagination.total_pages;
    }
    
    return { products: data, totalPages };
  } catch (error) {
    fs.appendFileSync(logFile, `Error in getVeeqoProducts: ${error.message}\n`);
    console.error(`Error in getVeeqoProducts: ${error.message}`);
    return { products: [], totalPages: 0 };
  }
}

// Function to get records with discrepancies
async function getDiscrepancyRecords() {
  try {
    fs.appendFileSync(logFile, `Fetching records with discrepancies...\n`);
    console.log('Fetching records with discrepancies...');
    
    const { data, error } = await supabase
      .from('v_reconcile_rpro_counts_to_veeqo')
      .select('*')
      .not('veeqo_id', 'is', null)
      .not('status', 'eq', 'Equal');
    
    if (error) {
      fs.appendFileSync(logFile, `Error fetching discrepancy records: ${error.message}\n`);
      console.error(`Error fetching discrepancy records: ${error.message}`);
      return [];
    }
    
    fs.appendFileSync(logFile, `Found ${data.length} records with discrepancies\n`);
    console.log(`Found ${data.length} records with discrepancies`);
    return data;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in getDiscrepancyRecords: ${error.message}\n`);
    console.error(`Error in getDiscrepancyRecords: ${error.message}`);
    return [];
  }
}

// Function to check if a product ID exists in Veeqo
async function checkProductId(productId) {
  try {
    fs.appendFileSync(logFile, `Checking if product ${productId} exists in Veeqo...\n`);
    console.log(`Checking if product ${productId} exists in Veeqo...`);
    
    // Veeqo API endpoint for getting product details
    const url = `https://api.veeqo.com/products/${productId}`;
    
    // Make the API request
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      fs.appendFileSync(logFile, `Product ${productId} does not exist in Veeqo: ${response.status} ${response.statusText} - ${errorText}\n`);
      console.error(`Product ${productId} does not exist in Veeqo: ${response.status} ${response.statusText} - ${errorText}`);
      return false;
    }
    
    fs.appendFileSync(logFile, `Product ${productId} exists in Veeqo\n`);
    console.log(`Product ${productId} exists in Veeqo`);
    return true;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in checkProductId for product ${productId}: ${error.message}\n`);
    console.error(`Error in checkProductId for product ${productId}: ${error.message}`);
    return false;
  }
}

// Function to get Veeqo product by SKU
async function getProductBySku(sku) {
  try {
    fs.appendFileSync(logFile, `Searching for product with SKU ${sku} in Veeqo...\n`);
    console.log(`Searching for product with SKU ${sku} in Veeqo...`);
    
    // Veeqo API endpoint for searching products
    const url = `https://api.veeqo.com/products?query=${sku}`;
    
    // Make the API request
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      fs.appendFileSync(logFile, `Error searching for product with SKU ${sku}: ${response.status} ${response.statusText} - ${errorText}\n`);
      console.error(`Error searching for product with SKU ${sku}: ${response.status} ${response.statusText} - ${errorText}`);
      return null;
    }
    
    const products = await response.json();
    
    if (!products || products.length === 0) {
      fs.appendFileSync(logFile, `No products found with SKU ${sku}\n`);
      console.error(`No products found with SKU ${sku}`);
      return null;
    }
    
    // Find the product with the exact SKU
    const exactMatch = products.find(product => {
      if (product.sellables && product.sellables.length > 0) {
        return product.sellables.some(sellable => sellable.sku_code === sku);
      }
      return false;
    });
    
    if (!exactMatch) {
      fs.appendFileSync(logFile, `No exact match found for SKU ${sku}\n`);
      console.error(`No exact match found for SKU ${sku}`);
      return null;
    }
    
    fs.appendFileSync(logFile, `Found product with ID ${exactMatch.id} for SKU ${sku}\n`);
    console.log(`Found product with ID ${exactMatch.id} for SKU ${sku}`);
    return exactMatch;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in getProductBySku for SKU ${sku}: ${error.message}\n`);
    console.error(`Error in getProductBySku for SKU ${sku}: ${error.message}`);
    return null;
  }
}

// Main function
async function main() {
  try {
    // Get records with discrepancies
    const discrepancyRecords = await getDiscrepancyRecords();
    
    if (discrepancyRecords.length === 0) {
      fs.appendFileSync(logFile, `No discrepancy records found, nothing to check\n`);
      console.log('No discrepancy records found, nothing to check');
      return;
    }
    
    // Check each product ID
    let existingCount = 0;
    let nonExistingCount = 0;
    let foundBySku = 0;
    
    for (const record of discrepancyRecords) {
      const productId = record.veeqo_id;
      const sku = record.sku_code;
      
      // Check if the product ID exists in Veeqo
      const exists = await checkProductId(productId);
      
      if (exists) {
        existingCount++;
      } else {
        nonExistingCount++;
        
        // Try to find the product by SKU
        if (sku) {
          const product = await getProductBySku(sku);
          
          if (product) {
            foundBySku++;
            fs.appendFileSync(logFile, `Product with ID ${productId} not found, but found by SKU ${sku} with ID ${product.id}\n`);
            console.log(`Product with ID ${productId} not found, but found by SKU ${sku} with ID ${product.id}`);
          }
        }
      }
      
      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Log summary
    fs.appendFileSync(logFile, `\nCheck summary:\n`);
    fs.appendFileSync(logFile, `Total records checked: ${discrepancyRecords.length}\n`);
    fs.appendFileSync(logFile, `Products found in Veeqo: ${existingCount}\n`);
    fs.appendFileSync(logFile, `Products not found in Veeqo: ${nonExistingCount}\n`);
    fs.appendFileSync(logFile, `Products found by SKU: ${foundBySku}\n`);
    
    console.log('\nCheck summary:');
    console.log(`Total records checked: ${discrepancyRecords.length}`);
    console.log(`Products found in Veeqo: ${existingCount}`);
    console.log(`Products not found in Veeqo: ${nonExistingCount}`);
    console.log(`Products found by SKU: ${foundBySku}`);
    
  } catch (error) {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`Unhandled error: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the main function
console.log('Starting Veeqo ID check...');
main()
  .then(() => {
    fs.appendFileSync(logFile, `Process completed at ${new Date().toISOString()}\n`);
    console.log('Process completed.');
  })
  .catch(error => {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    console.error(`Unhandled error: ${error.message}`);
  });
