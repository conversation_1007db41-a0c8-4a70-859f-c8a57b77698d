// importAmazonActiveListings.js - Import Amazon Active Listings Report into Supabase

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('[importAmazonActiveListings] Missing Supabase configuration');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Configuration
const DATA_DIR = 'C:\\Users\\<USER>\\supabase_project\\data\\external data\\Amazon Active Listings Report';
const TABLE_NAME = 'it_amaz_active_listings_report';

// Function to find the most recent file that hasn't been imported
async function findLatestUnimportedFile() {
  try {
    if (!fs.existsSync(DATA_DIR)) {
      throw new Error(`Directory does not exist: ${DATA_DIR}`);
    }

    const files = fs.readdirSync(DATA_DIR)
      .filter(file => file.match(/^Active\+Listings\+Report\+\d{2}-\d{2}-\d{4}\.txt$/))
      .map(file => {
        const match = file.match(/Active\+Listings\+Report\+(\d{2})-(\d{2})-(\d{4})\.txt/);
        if (match) {
          const [, month, day, year] = match;
          const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
          return {
            filename: file,
            fullPath: path.join(DATA_DIR, file),
            date: date,
            dateString: `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
          };
        }
        return null;
      })
      .filter(Boolean)
      .sort((a, b) => b.date - a.date); // Sort by date descending (newest first)

    if (files.length === 0) {
      throw new Error('No Active Listings Report files found in the directory');
    }

    // Check which files have already been imported
    for (const file of files) {
      const { data, error } = await supabase
        .from(TABLE_NAME)
        .select('id')
        .eq('report_date', file.dateString)
        .limit(1);

      if (error) {
        console.warn(`[importAmazonActiveListings] Error checking if file ${file.filename} was already imported: ${error.message}`);
        continue;
      }

      if (!data || data.length === 0) {
        console.log(`[importAmazonActiveListings] Found unimported file: ${file.filename} (${file.dateString})`);
        return file;
      } else {
        console.log(`[importAmazonActiveListings] File ${file.filename} already imported, skipping`);
      }
    }

    throw new Error('All available Active Listings Report files have already been imported');
  } catch (error) {
    console.error(`[importAmazonActiveListings] Error finding latest file: ${error.message}`);
    throw error;
  }
}

// Function to parse a tab-delimited line with proper handling of empty fields
function parseTabDelimitedLine(line, expectedFieldCount) {
  const fields = line.split('\t');
  
  // Pad with empty strings if we have fewer fields than expected
  while (fields.length < expectedFieldCount) {
    fields.push('');
  }
  
  // Truncate if we have more fields than expected
  if (fields.length > expectedFieldCount) {
    fields.splice(expectedFieldCount);
  }
  
  return fields;
}

// Function to convert field value to appropriate type
function convertFieldValue(value, fieldName) {
  if (!value || value.trim() === '') {
    return null;
  }

  const trimmedValue = value.trim();

  // Handle numeric fields
  if (['price', 'zshop_shipping_fee', 'business_price', 'quantity_price_1', 'quantity_price_2', 
       'quantity_price_3', 'quantity_price_4', 'quantity_price_5', 'progressive_price_1', 
       'progressive_price_2', 'progressive_price_3'].includes(fieldName)) {
    const numValue = parseFloat(trimmedValue);
    return isNaN(numValue) ? null : numValue;
  }

  // Handle integer fields
  if (['quantity', 'pending_quantity', 'quantity_lower_bound_1', 'quantity_lower_bound_2',
       'quantity_lower_bound_3', 'quantity_lower_bound_4', 'quantity_lower_bound_5',
       'progressive_lower_bound_1', 'progressive_lower_bound_2', 'progressive_lower_bound_3'].includes(fieldName)) {
    const intValue = parseInt(trimmedValue);
    return isNaN(intValue) ? null : intValue;
  }

  // Handle date fields
  if (fieldName === 'open_date') {
    try {
      // Expected format: "2018-02-20 01:03:43 PST"
      const dateMatch = trimmedValue.match(/^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/);
      if (dateMatch) {
        return new Date(dateMatch[1] + ' UTC').toISOString();
      }
    } catch (e) {
      console.warn(`[importAmazonActiveListings] Could not parse date: ${trimmedValue}`);
    }
    return null;
  }

  return trimmedValue;
}

// Main import function
async function importActiveListingsReport() {
  const startTime = Date.now();
  
  try {
    console.log('[importAmazonActiveListings] Starting Amazon Active Listings Report import...');

    // Find the latest unimported file
    const fileInfo = await findLatestUnimportedFile();
    console.log(`[importAmazonActiveListings] Processing file: ${fileInfo.filename}`);

    // Read and parse the file
    const fileContent = fs.readFileSync(fileInfo.fullPath, 'utf-8');
    const lines = fileContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);

    if (lines.length === 0) {
      throw new Error('File is empty or contains no valid data');
    }

    // Parse header line to get field names
    const headerLine = lines[0];
    const headers = parseTabDelimitedLine(headerLine, 50); // Expect around 50 fields based on the sample
    
    console.log(`[importAmazonActiveListings] Found ${headers.length} columns in header`);
    console.log(`[importAmazonActiveListings] Processing ${lines.length - 1} data rows`);

    // Map header names to database column names
    const fieldMapping = {
      'item-name': 'item_name',
      'item-description': 'item_description',
      'listing-id': 'listing_id',
      'seller-sku': 'seller_sku',
      'price': 'price',
      'quantity': 'quantity',
      'open-date': 'open_date',
      'image-url': 'image_url',
      'item-is-marketplace': 'item_is_marketplace',
      'product-id-type': 'product_id_type',
      'zshop-shipping-fee': 'zshop_shipping_fee',
      'item-note': 'item_note',
      'item-condition': 'item_condition',
      'zshop-category1': 'zshop_category1',
      'zshop-browse-path': 'zshop_browse_path',
      'zshop-storefront-feature': 'zshop_storefront_feature',
      'asin1': 'asin1',
      'asin2': 'asin2',
      'asin3': 'asin3',
      'will-ship-internationally': 'will_ship_internationally',
      'expedited-shipping': 'expedited_shipping',
      'zshop-boldface': 'zshop_boldface',
      'product-id': 'product_id',
      'bid-for-featured-placement': 'bid_for_featured_placement',
      'add-delete': 'add_delete',
      'pending-quantity': 'pending_quantity',
      'fulfillment-channel': 'fulfillment_channel',
      'Business Price': 'business_price',
      'Quantity Price Type': 'quantity_price_type',
      'Quantity Lower Bound 1': 'quantity_lower_bound_1',
      'Quantity Price 1': 'quantity_price_1',
      'Quantity Lower Bound 2': 'quantity_lower_bound_2',
      'Quantity Price 2': 'quantity_price_2',
      'Quantity Lower Bound 3': 'quantity_lower_bound_3',
      'Quantity Price 3': 'quantity_price_3',
      'Quantity Lower Bound 4': 'quantity_lower_bound_4',
      'Quantity Price 4': 'quantity_price_4',
      'Quantity Lower Bound 5': 'quantity_lower_bound_5',
      'Quantity Price 5': 'quantity_price_5',
      'merchant-shipping-group': 'merchant_shipping_group',
      'Progressive Price Type': 'progressive_price_type',
      'Progressive Lower Bound 1': 'progressive_lower_bound_1',
      'Progressive Price 1': 'progressive_price_1',
      'Progressive Lower Bound 2': 'progressive_lower_bound_2',
      'Progressive Price 2': 'progressive_price_2',
      'Progressive Lower Bound 3': 'progressive_lower_bound_3',
      'Progressive Price 3': 'progressive_price_3'
    };

    // Truncate existing data (this is a snapshot import)
    console.log('[importAmazonActiveListings] Truncating existing data...');
    const { error: truncateError } = await supabase
      .from(TABLE_NAME)
      .delete()
      .neq('id', 0); // Delete all records

    if (truncateError) {
      throw new Error(`Failed to truncate existing data: ${truncateError.message}`);
    }

    // Parse data lines
    const dataRows = [];
    let skippedRows = 0;

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i];
      if (!line.trim()) {
        skippedRows++;
        continue;
      }

      const values = parseTabDelimitedLine(line, headers.length);
      const row = {
        report_date: fileInfo.dateString
      };

      // Map each field
      for (let j = 0; j < headers.length && j < values.length; j++) {
        const headerName = headers[j];
        const dbFieldName = fieldMapping[headerName] || headerName.toLowerCase().replace(/[^a-z0-9]/g, '_');
        const value = convertFieldValue(values[j], dbFieldName);
        
        if (dbFieldName && value !== undefined) {
          row[dbFieldName] = value;
        }
      }

      dataRows.push(row);
    }

    console.log(`[importAmazonActiveListings] Parsed ${dataRows.length} valid rows, skipped ${skippedRows} rows`);

    if (dataRows.length === 0) {
      throw new Error('No valid data rows found to import');
    }

    // Import data in chunks
    const chunkSize = 1000;
    let totalImported = 0;

    for (let i = 0; i < dataRows.length; i += chunkSize) {
      const chunk = dataRows.slice(i, i + chunkSize);
      
      console.log(`[importAmazonActiveListings] Importing chunk ${Math.floor(i / chunkSize) + 1} (${chunk.length} records)`);

      const { data, error } = await supabase
        .from(TABLE_NAME)
        .insert(chunk);

      if (error) {
        throw new Error(`Failed to import chunk starting at row ${i + 1}: ${error.message}`);
      }

      totalImported += chunk.length;
    }

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    
    console.log(`[importAmazonActiveListings] Import completed successfully!`);
    console.log(`[importAmazonActiveListings] File: ${fileInfo.filename}`);
    console.log(`[importAmazonActiveListings] Report date: ${fileInfo.dateString}`);
    console.log(`[importAmazonActiveListings] Total records imported: ${totalImported}`);
    console.log(`[importAmazonActiveListings] Duration: ${duration} seconds`);

    // Return summary for API response
    return {
      success: true,
      fileName: fileInfo.filename,
      reportDate: fileInfo.dateString,
      totalRecords: totalImported,
      duration: `${duration} seconds`
    };

  } catch (error) {
    console.error(`[importAmazonActiveListings] Import failed: ${error.message}`);
    throw error;
  }
}

// Run the import if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  importActiveListingsReport()
    .then(result => {
      console.log('[importAmazonActiveListings] Import completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('[importAmazonActiveListings] Import failed:', error.message);
      process.exit(1);
    });
}

export { importActiveListingsReport };
