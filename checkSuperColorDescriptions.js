import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkSuperColorDescriptions() {
  try {
    console.log('🔍 Checking SuperColor vendor descriptions...\n');
    
    // Get all SuperColor products with their vendor descriptions
    const { data: superColorProducts, error } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .ilike('raw_line_type', '%SuperColor%')
      .limit(20);
    
    if (error) {
      console.error('Error querying SuperColor products:', error);
      return;
    }
    
    if (superColorProducts.length === 0) {
      console.log('⚪ No SuperColor products found');
      return;
    }
    
    console.log(`📋 Found ${superColorProducts.length} SuperColor products:\n`);
    
    superColorProducts.forEach((product, index) => {
      console.log(`${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
      console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
      console.log(`   Vendor Description: "${product.vendor_description || 'NULL/EMPTY'}"`);
      console.log('');
    });
    
    // Check if any vendor descriptions contain the patterns we're looking for
    console.log('🔍 Checking for specific patterns in vendor descriptions:');
    
    const patterns = [
      'supercolor paul mcbeth luna',
      'supercolor gallery buzzz',
      'full foil supercolor',
      'bali',
      'bunksy',
      'demise',
      'earth',
      'fire',
      'ancient alien',
      'astronaut',
      'owl',
      'lichten',
      'moon',
      'chains green',
      'chains pink',
      'chains blue'
    ];
    
    for (const pattern of patterns) {
      const matchingProducts = superColorProducts.filter(product => 
        product.vendor_description && 
        product.vendor_description.toLowerCase().includes(pattern)
      );
      
      if (matchingProducts.length > 0) {
        console.log(`   ✅ "${pattern}": ${matchingProducts.length} matches found`);
        matchingProducts.forEach(product => {
          console.log(`      "${product.vendor_description}"`);
        });
      } else {
        console.log(`   ⚪ "${pattern}": No matches found`);
      }
    }
    
    console.log('\n🎉 SuperColor description check completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkSuperColorDescriptions().catch(console.error);
