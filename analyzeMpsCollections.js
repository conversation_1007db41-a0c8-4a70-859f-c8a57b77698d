import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Shopify configuration
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

// Extract domain from the endpoint URL
let shopifyDomain = 'dzdiscs-new-releases.myshopify.com';
if (shopifyEndpoint) {
  const match = shopifyEndpoint.match(/https:\/\/([^\/]+)/);
  if (match) {
    shopifyDomain = match[1];
  }
}

/**
 * Check if a collection is an MPS collection based on its rules
 */
function isMpsCollection(collection) {
  if (!collection.rules || collection.rules.length < 3) {
    return false;
  }
  
  const hasDiscMoldTag = collection.rules.some(rule => 
    rule.column === 'tag' && 
    rule.relation === 'equals' && 
    rule.condition && 
    rule.condition.startsWith('disc_mold_')
  );
  
  const hasDiscPlasticTag = collection.rules.some(rule => 
    rule.column === 'tag' && 
    rule.relation === 'equals' && 
    rule.condition && 
    rule.condition.startsWith('disc_plastic_')
  );
  
  const hasDiscStampTag = collection.rules.some(rule => 
    rule.column === 'tag' && 
    rule.relation === 'equals' && 
    rule.condition && 
    rule.condition.startsWith('disc_stamp_')
  );
  
  return hasDiscMoldTag && hasDiscPlasticTag && hasDiscStampTag;
}

/**
 * Extract MPS tags from collection rules
 */
function extractMpsTags(collection) {
  const tags = {};
  
  collection.rules.forEach(rule => {
    if (rule.column === 'tag' && rule.relation === 'equals' && rule.condition) {
      if (rule.condition.startsWith('disc_mold_')) {
        tags.mold = rule.condition.replace('disc_mold_', '');
      } else if (rule.condition.startsWith('disc_plastic_')) {
        tags.plastic = rule.condition.replace('disc_plastic_', '');
      } else if (rule.condition.startsWith('disc_stamp_')) {
        tags.stamp = rule.condition.replace('disc_stamp_', '');
      }
    }
  });
  
  return tags;
}

/**
 * Fetch all smart collections from Shopify with pagination
 */
async function fetchAllShopifyCollections() {
  console.log('📊 Fetching all Shopify smart collections...');
  
  const allCollections = [];
  let nextPageInfo = null;
  let pageCount = 0;
  
  do {
    pageCount++;
    console.log(`   Fetching page ${pageCount}...`);
    
    let url = `https://${shopifyDomain}/admin/api/2024-01/smart_collections.json?limit=250`;
    if (nextPageInfo) {
      url += `&page_info=${nextPageInfo}`;
    }
    
    try {
      const response = await fetch(url, {
        headers: {
          'X-Shopify-Access-Token': shopifyAccessToken,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      const collections = data.smart_collections || [];
      
      allCollections.push(...collections);
      
      // Check for next page using Link header
      const linkHeader = response.headers.get('Link');
      nextPageInfo = null;
      
      if (linkHeader) {
        const nextMatch = linkHeader.match(/<[^>]*[?&]page_info=([^&>]+)[^>]*>;\s*rel="next"/);
        if (nextMatch) {
          nextPageInfo = nextMatch[1];
        }
      }
      
      // Rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error) {
      console.error(`❌ Error fetching collections page ${pageCount}:`, error.message);
      throw error;
    }
  } while (nextPageInfo);
  
  console.log(`✅ Total collections fetched: ${allCollections.length}`);
  return allCollections;
}

/**
 * Check if a collection has any products
 */
async function checkCollectionProducts(collectionId) {
  try {
    const response = await fetch(
      `https://${shopifyDomain}/admin/api/2024-01/products.json?collection_id=${collectionId}&limit=1`,
      {
        headers: {
          'X-Shopify-Access-Token': shopifyAccessToken,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (!response.ok) {
      console.warn(`⚠️  Could not check products for collection ${collectionId}: ${response.status}`);
      return null;
    }
    
    const data = await response.json();
    return (data.products && data.products.length > 0);
    
  } catch (error) {
    console.warn(`⚠️  Error checking products for collection ${collectionId}:`, error.message);
    return null;
  }
}

/**
 * Fetch MPS records from database
 */
async function fetchMpsRecords() {
  console.log('📊 Fetching MPS records from database...');
  
  const { data, error } = await supabase
    .from('t_mps')
    .select(`
      id,
      g_handle,
      shopify_collection_uploaded_at,
      active,
      t_molds!inner(mold),
      t_plastics!inner(plastic),
      t_stamps!inner(stamp)
    `)
    .not('shopify_collection_uploaded_at', 'is', null);
  
  if (error) {
    throw new Error(`Database error: ${error.message}`);
  }
  
  console.log(`✅ Found ${data.length} MPS records with shopify_collection_uploaded_at`);
  return data;
}

/**
 * Analyze MPS collections
 */
async function analyzeMpsCollections() {
  try {
    // Fetch all collections and MPS records
    const [allCollections, mpsRecords] = await Promise.all([
      fetchAllShopifyCollections(),
      fetchMpsRecords()
    ]);
    
    console.log('\n🔍 Filtering MPS collections...');
    
    // Filter to only MPS collections
    const mpsCollections = allCollections.filter(isMpsCollection);
    console.log(`✅ Found ${mpsCollections.length} MPS collections on Shopify`);
    
    // Sort by updated_at (oldest first)
    mpsCollections.sort((a, b) => new Date(a.updated_at) - new Date(b.updated_at));
    
    console.log('\n🔍 Checking for empty MPS collections...');
    
    const emptyMpsCollections = [];
    const collectionsWithProducts = [];
    
    // Check first 50 oldest MPS collections for products
    for (let i = 0; i < Math.min(50, mpsCollections.length); i++) {
      const collection = mpsCollections[i];
      console.log(`   Checking ${i + 1}/50: ${collection.title} (${collection.handle})`);
      
      const hasProducts = await checkCollectionProducts(collection.id);
      
      const collectionInfo = {
        id: collection.id,
        title: collection.title,
        handle: collection.handle,
        updated_at: collection.updated_at,
        created_at: collection.created_at,
        hasProducts: hasProducts,
        mpsTags: extractMpsTags(collection)
      };
      
      if (hasProducts === false) {
        emptyMpsCollections.push(collectionInfo);
      } else if (hasProducts === true) {
        collectionsWithProducts.push(collectionInfo);
      }
      
      // Rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('\n🔍 Matching with database records...');
    
    // Create lookup maps
    const mpsRecordsByHandle = new Map();
    mpsRecords.forEach(mps => {
      if (mps.g_handle) {
        mpsRecordsByHandle.set(mps.g_handle, mps);
      }
    });
    
    // Match empty collections with database records
    const matchedEmptyCollections = emptyMpsCollections.map(collection => {
      const mpsRecord = mpsRecordsByHandle.get(collection.handle);
      return {
        ...collection,
        mpsRecord: mpsRecord || null,
        hasDbRecord: !!mpsRecord
      };
    });
    
    // Sort by updated_at and take top 10
    const top10EmptyMps = matchedEmptyCollections
      .sort((a, b) => new Date(a.updated_at) - new Date(b.updated_at))
      .slice(0, 10);
    
    return {
      totalCollections: allCollections.length,
      totalMpsCollections: mpsCollections.length,
      emptyMpsCollections: matchedEmptyCollections,
      collectionsWithProducts,
      top10EmptyMps,
      mpsRecordsCount: mpsRecords.length
    };
    
  } catch (error) {
    console.error('❌ Error analyzing MPS collections:', error.message);
    throw error;
  }
}

/**
 * Display analysis results
 */
function displayResults(analysis) {
  console.log('\n' + '='.repeat(60));
  console.log('📊 MPS COLLECTIONS ANALYSIS RESULTS');
  console.log('='.repeat(60));
  
  console.log(`\n📈 SUMMARY:`);
  console.log(`   Total Shopify Collections: ${analysis.totalCollections}`);
  console.log(`   Total MPS Collections: ${analysis.totalMpsCollections}`);
  console.log(`   Empty MPS Collections Found: ${analysis.emptyMpsCollections.length}`);
  console.log(`   MPS Collections with Products: ${analysis.collectionsWithProducts.length}`);
  console.log(`   MPS Records in Database: ${analysis.mpsRecordsCount}`);
  
  console.log(`\n🗑️  TOP 10 OLDEST EMPTY MPS COLLECTIONS (DELETION CANDIDATES):`);
  analysis.top10EmptyMps.forEach((collection, index) => {
    const updatedDate = new Date(collection.updated_at).toLocaleDateString();
    const dbStatus = collection.hasDbRecord ? '✅ Has DB record' : '❌ No DB record';
    console.log(`   ${index + 1}. ${collection.title}`);
    console.log(`      Handle: ${collection.handle}`);
    console.log(`      Updated: ${updatedDate}`);
    console.log(`      Shopify ID: ${collection.id}`);
    console.log(`      Database: ${dbStatus}`);
    if (collection.mpsRecord) {
      console.log(`      MPS ID: ${collection.mpsRecord.id}`);
    }
    console.log('');
  });
  
  console.log('\n' + '='.repeat(60));
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Starting MPS Collections Analysis...\n');
  
  try {
    const analysis = await analyzeMpsCollections();
    
    displayResults(analysis);
    
    // Save results to file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `mps_collections_analysis_${timestamp}.json`;
    
    fs.writeFileSync(filename, JSON.stringify(analysis, null, 2));
    
    console.log(`\n💾 Detailed results saved to: ${filename}`);
    console.log('\n✅ Analysis complete!');
    
    return analysis;
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    process.exit(1);
  }
}

// Export for use in other modules
export { analyzeMpsCollections, fetchMpsRecords };

// Run if called directly
console.log('Script loaded, checking if should run main...');

// Check if this is the main module by comparing file paths
const currentFile = new URL(import.meta.url).pathname;
const mainFile = process.argv[1].replace(/\\/g, '/');
const isMainModule = currentFile.endsWith(mainFile.split('/').pop());

console.log('currentFile:', currentFile);
console.log('mainFile:', mainFile);
console.log('isMainModule:', isMainModule);

if (isMainModule) {
  console.log('Running main function...');
  main();
} else {
  console.log('Not main module, skipping main function');
}
