// processDeleteAmazonListingTask.js - Process task to delete Amazon listing
import AmazonSpApiClient from './amazonSpApiClient.js';

/**
 * Process a delete_amazon_listing task
 * @param {Object} task - The task object from the queue
 * @param {Object} context - Context object with supabase, updateTaskStatus, logError functions
 */
export default async function processDeleteAmazonListingTask(task, context) {
  const { supabase, updateTaskStatus, logError } = context;
  
  console.log(`[processDeleteAmazonListingTask] Processing task ${task.id} of type ${task.task_type}`);
  
  try {
    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');
    
    // Extract payload
    const payload = task.payload;
    if (!payload || !payload.sku) {
      throw new Error('Task payload must contain a SKU to delete');
    }
    
    const { sku, marketplaceIds, reason } = payload;
    
    console.log(`[processDeleteAmazonListingTask] Deleting Amazon listing for SKU: ${sku}`);
    if (reason) {
      console.log(`[processDeleteAmazonListingTask] Deletion reason: ${reason}`);
    }
    
    // Initialize Amazon SP-API client
    const amazonClient = new AmazonSpApiClient();
    
    // Test connection first
    const connectionTest = await amazonClient.testConnection();
    if (!connectionTest.success) {
      throw new Error(`Amazon SP-API connection failed: ${connectionTest.message}`);
    }
    
    // Check if listing exists before attempting deletion
    let listingExists = false;
    try {
      const existingListing = await amazonClient.getListing(sku, marketplaceIds);
      listingExists = true;
      console.log(`[processDeleteAmazonListingTask] Found existing listing for SKU: ${sku}`);
    } catch (error) {
      if (error.message.includes('404') || error.message.includes('not found')) {
        console.log(`[processDeleteAmazonListingTask] Listing for SKU ${sku} does not exist or is already deleted`);
        listingExists = false;
      } else {
        // Re-throw other errors
        throw error;
      }
    }
    
    let deletionResult = null;
    
    if (listingExists) {
      // Attempt to delete the listing
      try {
        deletionResult = await amazonClient.deleteListing(sku, marketplaceIds);
        console.log(`[processDeleteAmazonListingTask] Successfully initiated deletion for SKU: ${sku}`);
      } catch (error) {
        console.error(`[processDeleteAmazonListingTask] Failed to delete listing for SKU: ${sku}`, error.message);
        throw error;
      }
    }
    
    // Log the deletion in the database (optional - you might want to track deletions)
    try {
      const { error: logError } = await supabase
        .from('t_amazon_listing_deletions')
        .insert({
          sku: sku,
          marketplace_ids: marketplaceIds || ['ATVPDKIKX0DER'],
          reason: reason || 'Manual deletion via task queue',
          listing_existed: listingExists,
          deletion_result: deletionResult,
          deleted_at: new Date().toISOString(),
          task_id: task.id
        });
      
      if (logError) {
        console.warn(`[processDeleteAmazonListingTask] Could not log deletion to database: ${logError.message}`);
      }
    } catch (dbError) {
      console.warn(`[processDeleteAmazonListingTask] Could not log deletion to database: ${dbError.message}`);
    }
    
    // Mark task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: listingExists 
        ? `Successfully deleted Amazon listing for SKU: ${sku}`
        : `Listing for SKU ${sku} did not exist or was already deleted`,
      sku: sku,
      marketplace_ids: marketplaceIds || ['ATVPDKIKX0DER'],
      listing_existed: listingExists,
      deletion_result: deletionResult,
      completed_at: new Date().toISOString()
    });
    
    console.log(`[processDeleteAmazonListingTask] Task ${task.id} completed successfully`);
    
  } catch (error) {
    const errorMessage = `Failed to delete Amazon listing: ${error.message}`;
    console.error(`[processDeleteAmazonListingTask] Task ${task.id} failed:`, errorMessage);
    
    // Log error to database
    await logError(errorMessage, `Processing delete_amazon_listing task ${task.id}`);
    
    // Mark task as failed
    await updateTaskStatus(task.id, 'failed', {
      message: errorMessage,
      error: error.message,
      failed_at: new Date().toISOString()
    });
  }
}

/**
 * Helper function to enqueue a delete Amazon listing task
 * @param {Object} supabase - Supabase client
 * @param {string} sku - The SKU to delete
 * @param {Array} marketplaceIds - Optional array of marketplace IDs
 * @param {string} reason - Optional reason for deletion
 * @param {Date} scheduledAt - Optional scheduled time (defaults to 5 minutes from now)
 */
export async function enqueueDeleteAmazonListingTask(supabase, sku, marketplaceIds = null, reason = null, scheduledAt = null) {
  if (!sku) {
    throw new Error('SKU is required to enqueue delete Amazon listing task');
  }
  
  const now = new Date();
  const defaultScheduledAt = new Date(now.getTime() + 5 * 60 * 1000); // 5 minutes from now
  
  const task = {
    task_type: 'delete_amazon_listing',
    payload: {
      sku: sku,
      marketplaceIds: marketplaceIds || ['ATVPDKIKX0DER'],
      reason: reason || 'Scheduled deletion'
    },
    status: 'pending',
    scheduled_at: (scheduledAt || defaultScheduledAt).toISOString(),
    created_at: now.toISOString(),
    enqueued_by: 'enqueueDeleteAmazonListingTask'
  };
  
  console.log(`[enqueueDeleteAmazonListingTask] Enqueueing delete Amazon listing task for SKU: ${sku}`);
  
  const { data, error } = await supabase
    .from('t_task_queue')
    .insert(task)
    .select()
    .single();
  
  if (error) {
    console.error(`[enqueueDeleteAmazonListingTask] Failed to enqueue task:`, error.message);
    throw new Error(`Failed to enqueue delete Amazon listing task: ${error.message}`);
  }
  
  console.log(`[enqueueDeleteAmazonListingTask] Successfully enqueued task ${data.id} for SKU: ${sku}`);
  return data;
}
