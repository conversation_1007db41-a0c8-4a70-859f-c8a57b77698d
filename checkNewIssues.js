import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkNewIssues() {
  try {
    console.log('🔍 Checking new parsing issues...\n');
    
    // Check E<PERSON> (line 279)
    console.log('🔍 Testing ESP Roach (line 279):');
    const { data: espRoachProducts, error1 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .eq('raw_line_type', 'ESP')
      .eq('raw_model', 'Roach')
      .limit(5);
    
    if (error1) {
      console.error('Error querying ESP Roach products:', error1);
    } else if (espRoachProducts.length > 0) {
      espRoachProducts.forEach((product, index) => {
        console.log(`${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
        console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        console.log(`   Expected: ESP | Roach | Dye Line Blank Top Bottom`);
        
        const isCorrect = product.plastic_name === 'ESP' && 
                         product.mold_name === 'Roach' && 
                         product.stamp_name === 'Dye Line Blank Top Bottom';
        
        console.log(`   ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
        console.log(`   Vendor Description: "${product.vendor_description}"`);
        console.log('');
      });
    } else {
      console.log('   ⚪ No ESP Roach products found');
    }
    
    // Check Pierce Drive (line 322)
    console.log('🔍 Testing Pierce Drive (line 322):');
    const { data: pierceDriveProducts, error2 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .eq('raw_line_type', 'Pierce')
      .eq('raw_model', 'Drive')
      .limit(5);
    
    if (error2) {
      console.error('Error querying Pierce Drive products:', error2);
    } else if (pierceDriveProducts.length > 0) {
      pierceDriveProducts.forEach((product, index) => {
        console.log(`${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
        console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        console.log(`   Expected: ESP | Drive | Paige Pierce - PP Logo - ZigZag Pattern`);
        
        const isCorrect = product.plastic_name === 'ESP' && 
                         product.mold_name === 'Drive' && 
                         product.stamp_name === 'Paige Pierce - PP Logo - ZigZag Pattern';
        
        console.log(`   ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
        console.log(`   Vendor Description: "${product.vendor_description}"`);
        console.log('');
      });
    } else {
      console.log('   ⚪ No Pierce Drive products found');
    }
    
    // Also check all Pierce Drive products (in case there are variations)
    console.log('🔍 Checking all Pierce products with Drive in model:');
    const { data: allPierceDrive, error3 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .eq('raw_line_type', 'Pierce')
      .ilike('raw_model', '%Drive%')
      .limit(10);
    
    if (error3) {
      console.error('Error querying all Pierce Drive products:', error3);
    } else if (allPierceDrive.length > 0) {
      allPierceDrive.forEach((product, index) => {
        console.log(`${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
        console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        console.log(`   Vendor Description: "${product.vendor_description}"`);
        console.log('');
      });
    } else {
      console.log('   ⚪ No Pierce Drive products found');
    }
    
    console.log('🎉 Check completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkNewIssues().catch(console.error);
