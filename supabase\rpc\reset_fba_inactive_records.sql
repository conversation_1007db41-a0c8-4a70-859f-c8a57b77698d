-- Dedicated RPC to reset FBA inactive records in a single DB-side operation (idempotent)
-- Creates function: public.reset_fba_inactive_records()
-- Returns: integer = number of rows updated

-- Safety: run in Supabase SQL editor as an admin or with service role
-- Re-run is safe due to idempotent WHERE clause

create or replace function public.reset_fba_inactive_records()
returns integer
language plpgsql
security definer
set search_path = public
as $$
DECLARE
  v_now_utc text := to_char((now() at time zone 'UTC'), 'YYYY-MM-DD"T"HH24:MI:SS"Z"');
  v_prefix text := 'Discontinued and Out of Stock MPS - Listing removed from Amazon on ' || v_now_utc;
  v_count integer := 0;
BEGIN
  WITH candidates AS (
    SELECT s.id
    FROM t_sdasins s
    JOIN v_sdasins_fba_inv0_mps_inactive v ON v.sdasin_id = s.id
    WHERE (
      s.fba IS DISTINCT FROM 'N' OR
      s.fbafnsku IS NOT NULL OR
      s.fba_uploaded_at IS NOT NULL OR
      s.min_weight IS DISTINCT FROM 1 OR
      s.max_weight IS DISTINCT FROM 2
    )
  ), upd AS (
    UPDATE t_sdasins s
    SET
      fba = 'N',
      fbafnsku = NULL,
      fba_uploaded_at = NULL,
      min_weight = 1,
      max_weight = 2,
      notes = CONCAT(v_prefix, E'\n', COALESCE(s.notes, ''))
    WHERE s.id IN (SELECT id FROM candidates)
    RETURNING s.id
  )
  SELECT count(*) INTO v_count FROM upd;

  RETURN v_count;
END;
$$;

-- Optional: grant execute to common roles (adjust as needed)
-- grant execute on function public.reset_fba_inactive_records() to anon, authenticated, service_role;

