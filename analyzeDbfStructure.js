// analyzeDbfStructure.js - Script to analyze DBF file structure

import { DBFFile } from 'dbffile';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the DBF file path from command line or environment variable
const dbfFilePath = process.argv[2] || process.env.DBF_FILE_PATH || './data/daily_import.dbf';

/**
 * Maps DBF field types to PostgreSQL data types
 * @param {Object} field - DBF field definition
 * @returns {string} - PostgreSQL data type
 */
function mapDbfTypeToPostgres(field) {
  const typeMap = {
    'C': (field) => `VARCHAR(${field.length})`, // Character
    'N': (field) => field.decimalCount > 0 ? `NUMERIC(${field.length},${field.decimalCount})` : `INTEGER`, // Numeric
    'F': (field) => `NUMERIC(${field.length},${field.decimalCount})`, // Float
    'L': () => 'BOOLEAN', // Logical
    'D': () => 'DATE', // Date
    'T': () => 'TIMESTAMP', // DateTime
    'B': () => 'BYTEA', // Binary
    'M': () => 'TEXT', // Memo
    'I': () => 'INTEGER', // Integer
    'Y': () => 'NUMERIC(10,4)', // Currency
    '+': () => 'BIGINT', // Auto-increment
    'O': () => 'NUMERIC(8,2)', // Double
    '@': () => 'TIMESTAMP', // Timestamp
    'G': () => 'BYTEA', // General
    'P': () => 'BYTEA', // Picture
    'V': () => 'VARCHAR(255)' // Variable character
  };

  // Default to TEXT if type is not recognized
  return (typeMap[field.type] || (() => 'TEXT'))(field);
}

/**
 * Analyzes a DBF file and outputs its structure
 */
async function analyzeDbfStructure() {
  // Create a log file
  const logFile = path.join(process.cwd(), 'dbf_analysis.log');
  fs.writeFileSync(logFile, `Starting analysis of DBF file: ${dbfFilePath}\n`);
  fs.appendFileSync(logFile, `Current working directory: ${process.cwd()}\n`);
  fs.appendFileSync(logFile, `__dirname equivalent: ${path.dirname(new URL(import.meta.url).pathname)}\n`);
  fs.appendFileSync(logFile, `File exists check: ${fs.existsSync(dbfFilePath)}\n`);

  console.log(`Log file created at: ${logFile}`);

  try {
    console.log(`Analyzing DBF file structure: ${dbfFilePath}`);
    fs.appendFileSync(logFile, `Analyzing DBF file structure: ${dbfFilePath}\n`);

    // Check if file exists
    if (!fs.existsSync(dbfFilePath)) {
      const errorMsg = `DBF file not found at path: ${dbfFilePath}`;
      fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
      throw new Error(errorMsg);
    }

    fs.appendFileSync(logFile, `File exists, continuing with analysis\n`);

    // Open the DBF file
    const dbf = await DBFFile.open(dbfFilePath);
    console.log(`DBF file opened. Found ${dbf.recordCount} records.`);

    // Get field descriptions
    const fields = dbf.fields;
    console.log(`Found ${fields.length} fields in the DBF file.`);

    // Read a sample of records to analyze actual data
    const sampleSize = Math.min(100, dbf.recordCount);
    const sampleRecords = await dbf.readRecords(sampleSize);

    // Analyze each field
    const fieldAnalysis = fields.map(field => {
      // Get sample values for this field
      const sampleValues = sampleRecords.map(record => record[field.name]).filter(val => val !== null && val !== undefined);

      // Determine if field can be nullable
      const hasNulls = sampleRecords.some(record => record[field.name] === null || record[field.name] === undefined);

      // For character fields, find the maximum actual length used
      let maxLength = field.length;
      if (field.type === 'C' && sampleValues.length > 0) {
        const actualMaxLength = Math.max(...sampleValues.map(val => val.toString().length));
        maxLength = Math.max(actualMaxLength, Math.min(field.length, 255)); // Cap at 255 for VARCHAR
      }

      // Map DBF type to PostgreSQL type
      const postgresType = mapDbfTypeToPostgres({...field, length: maxLength});

      return {
        name: field.name.toLowerCase(), // Lowercase for PostgreSQL convention
        dbfType: field.type,
        dbfLength: field.length,
        dbfDecimalCount: field.decimalCount,
        postgresType,
        nullable: hasNulls,
        sampleValues: sampleValues.slice(0, 3) // Show up to 3 sample values
      };
    });

    // Output field analysis
    console.log('\nField Analysis:');
    console.table(fieldAnalysis.map(f => ({
      name: f.name,
      dbfType: f.dbfType,
      postgresType: f.postgresType,
      nullable: f.nullable,
      samples: f.sampleValues.map(s => s?.toString?.().substring(0, 20) || s).join(', ')
    })));

    // Generate CREATE TABLE SQL
    const createTableSql = generateCreateTableSql(fieldAnalysis);
    console.log('\nGenerated CREATE TABLE SQL:');
    console.log(createTableSql);

    // Save SQL to file
    const sqlFilePath = path.join(process.cwd(), 'create_table.sql');
    fs.writeFileSync(sqlFilePath, createTableSql);
    console.log(`\nSQL saved to: ${sqlFilePath}`);

    return {
      success: true,
      fields: fieldAnalysis,
      recordCount: dbf.recordCount,
      sqlFilePath
    };
  } catch (error) {
    console.error(`Error analyzing DBF file: ${error.message}`);
    console.error(error.stack);
    fs.appendFileSync(logFile, `ERROR analyzing DBF file: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Generates CREATE TABLE SQL statement based on field analysis
 * @param {Array} fieldAnalysis - Array of field analysis objects
 * @returns {string} - SQL statement
 */
function generateCreateTableSql(fieldAnalysis) {
  // Determine table name from file name
  const fileName = path.basename(dbfFilePath, path.extname(dbfFilePath)).toLowerCase();
  const tableName = `imported_${fileName.replace(/[^a-z0-9_]/g, '_')}`;

  // Generate field definitions
  const fieldDefinitions = fieldAnalysis.map(field => {
    return `  ${field.name} ${field.postgresType}${field.nullable ? '' : ' NOT NULL'}`;
  });

  // Add metadata fields
  fieldDefinitions.push('  imported_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()');
  fieldDefinitions.push('  import_batch_id UUID NOT NULL DEFAULT gen_random_uuid()');

  // Generate complete SQL
  const sql = `-- Create table for imported DBF data
CREATE TABLE public.${tableName} (
  id SERIAL PRIMARY KEY,
${fieldDefinitions.join(',\n')}
);

-- Add indexes for common query patterns
CREATE INDEX idx_${tableName}_imported_at ON public.${tableName}(imported_at);
CREATE INDEX idx_${tableName}_import_batch_id ON public.${tableName}(import_batch_id);

-- Grant access to authenticated users
ALTER TABLE public.${tableName} ENABLE ROW LEVEL SECURITY;
GRANT ALL ON public.${tableName} TO authenticated;

-- Create policy for authenticated users
CREATE POLICY "${tableName}_policy" ON public.${tableName}
  FOR ALL TO authenticated
  USING (true);

-- Comment on table
COMMENT ON TABLE public.${tableName} IS 'Imported data from ${path.basename(dbfFilePath)} - created on ${new Date().toISOString()}';
`;

  return sql;
}

// Run the analysis if this script is executed directly
// For ES modules, we check if the import.meta.url is the same as the process.argv[1]
if (import.meta.url === `file://${process.argv[1]}`) {
  analyzeDbfStructure()
    .then(result => {
      if (result.success) {
        console.log('DBF structure analysis completed successfully.');
        process.exit(0);
      } else {
        console.error('DBF structure analysis failed.');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Unhandled error during analysis:', error);
      process.exit(1);
    });
}

// Export the function for use in other scripts
export default analyzeDbfStructure;
