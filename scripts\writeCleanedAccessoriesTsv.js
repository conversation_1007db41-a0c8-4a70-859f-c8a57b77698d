import fs from 'fs';
import path from 'path';

const SRC = path.join(process.cwd(), 'data', 'external data', 'access', 'tAccessories.txt');
const DEST = path.join(process.cwd(), 'data', 'external data', 'access', 'tAccessories.cleaned.txt');

function cleanTsvContent(raw) {
  const lines = raw.split(/\r?\n/);
  if (lines.length === 0) return raw;
  const header = lines[0];
  const out = [header];
  let acc = '';
  let merges = 0;

  const isBalanced = (s) => {
    let inQuote = false;
    for (let i = 0; i < s.length; i++) {
      if (s[i] === '"') {
        if (s[i + 1] === '"') { i++; continue; }
        inQuote = !inQuote;
      }
    }
    return !inQuote;
  };

  for (let i = 1; i < lines.length; i++) {
    if (acc === '') {
      acc = lines[i];
    } else {
      acc += '\n' + lines[i];
      merges++;
    }
    if (isBalanced(acc)) {
      acc = acc.replace(/\n"\t/g, '\n\t');
      out.push(acc);
      acc = '';
    }
  }
  if (acc) out.push(acc);
  console.log(`[writeCleanedAccessoriesTsv] Merged multiline records: ~${merges}`);
  return out.join('\n');
}

function main() {
  if (!fs.existsSync(SRC)) {
    console.error('Source not found:', SRC);
    process.exit(1);
  }
  const raw = fs.readFileSync(SRC, 'utf-8');
  const cleaned = cleanTsvContent(raw);
  fs.writeFileSync(DEST, cleaned, 'utf-8');
  console.log('Wrote cleaned file:', DEST);
}

main();

