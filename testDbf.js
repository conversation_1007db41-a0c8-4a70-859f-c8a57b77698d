// testDbf.js - Simple script to test DBF file reading

import { DBFFile } from 'dbffile';
import fs from 'fs';
import path from 'path';

// Get the DBF file path from command line
const dbfFilePath = process.argv[2];

if (!dbfFilePath) {
  console.error('Please provide a DBF file path as an argument');
  process.exit(1);
}

// Create a log file
const logFile = path.join(process.cwd(), 'dbf_test.log');
fs.writeFileSync(logFile, `Starting test of DBF file: ${dbfFilePath}\n`);
fs.appendFileSync(logFile, `Current working directory: ${process.cwd()}\n`);
fs.appendFileSync(logFile, `File exists check: ${fs.existsSync(dbfFilePath)}\n`);

console.log(`Log file created at: ${logFile}`);

async function testDbfFile() {
  try {
    // Check if file exists
    if (!fs.existsSync(dbfFilePath)) {
      const errorMsg = `DBF file not found at path: ${dbfFilePath}`;
      fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
      console.error(errorMsg);
      return;
    }
    
    fs.appendFileSync(logFile, `File exists, continuing with test\n`);
    console.log(`File exists, continuing with test`);
    
    // Open the DBF file
    const dbf = await DBFFile.open(dbfFilePath);
    fs.appendFileSync(logFile, `DBF file opened. Found ${dbf.recordCount} records.\n`);
    console.log(`DBF file opened. Found ${dbf.recordCount} records.`);
    
    // Get field descriptions
    const fields = dbf.fields;
    fs.appendFileSync(logFile, `Found ${fields.length} fields in the DBF file.\n`);
    console.log(`Found ${fields.length} fields in the DBF file.`);
    
    // Log field information
    fs.appendFileSync(logFile, `\nField Information:\n`);
    console.log(`\nField Information:`);
    
    fields.forEach(field => {
      const fieldInfo = `${field.name} (${field.type}): Length=${field.length}, Decimal=${field.decimalCount}`;
      fs.appendFileSync(logFile, `${fieldInfo}\n`);
      console.log(fieldInfo);
    });
    
    // Read a sample of records
    const sampleSize = Math.min(5, dbf.recordCount);
    const sampleRecords = await dbf.readRecords(sampleSize);
    
    fs.appendFileSync(logFile, `\nSample Records (${sampleSize}):\n`);
    console.log(`\nSample Records (${sampleSize}):`);
    
    sampleRecords.forEach((record, index) => {
      fs.appendFileSync(logFile, `\nRecord ${index + 1}:\n`);
      console.log(`\nRecord ${index + 1}:`);
      
      for (const key in record) {
        const value = record[key] !== null ? record[key] : 'NULL';
        fs.appendFileSync(logFile, `  ${key}: ${value}\n`);
        console.log(`  ${key}: ${value}`);
      }
    });
    
    fs.appendFileSync(logFile, `\nTest completed successfully.\n`);
    console.log(`\nTest completed successfully.`);
  } catch (error) {
    fs.appendFileSync(logFile, `ERROR: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`Error testing DBF file: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the test
testDbfFile();
