// setupDbfImport.js - Combined script for setting up DBF import

import analyzeDbfStructure from './analyzeDbfStructure.js';
import createTableInSupabase from './createTableInSupabase.js';
import importDbfToSupabase from './importDbfToSupabase.js';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';

// Load environment variables
dotenv.config();

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('file', {
    alias: 'f',
    description: 'Path to the DBF file',
    type: 'string',
    default: process.env.DBF_FILE_PATH || './data/daily_import.dbf'
  })
  .option('table', {
    alias: 't',
    description: 'Target table name (optional, will be generated from file name if not provided)',
    type: 'string'
  })
  .option('analyze-only', {
    alias: 'a',
    description: 'Only analyze the DBF structure without creating the table or importing data',
    type: 'boolean',
    default: false
  })
  .option('create-only', {
    alias: 'c',
    description: 'Only create the table without importing data',
    type: 'boolean',
    default: false
  })
  .option('import-only', {
    alias: 'i',
    description: 'Only import data without creating the table',
    type: 'boolean',
    default: false
  })
  .option('truncate', {
    description: 'Truncate the table before importing data',
    type: 'boolean',
    default: true
  })
  .help()
  .alias('help', 'h')
  .argv;

/**
 * Main function to set up DBF import
 */
async function setupDbfImport() {
  try {
    console.log('Starting DBF import setup...');

    const dbfFilePath = argv.file;
    let targetTable = argv.table;

    // Check if file exists
    if (!fs.existsSync(dbfFilePath)) {
      throw new Error(`DBF file not found at path: ${dbfFilePath}`);
    }

    // Step 1: Analyze DBF structure
    if (!argv['import-only']) {
      console.log(`\n=== STEP 1: Analyzing DBF structure ===`);
      const analysis = await analyzeDbfStructure();

      if (!analysis.success) {
        throw new Error(`Failed to analyze DBF structure: ${analysis.error}`);
      }

      console.log(`DBF structure analysis completed successfully.`);

      // If we're only analyzing, stop here
      if (argv['analyze-only']) {
        console.log(`Analysis-only mode selected. Stopping after analysis.`);
        return {
          success: true,
          message: 'DBF structure analysis completed successfully',
          sqlFilePath: analysis.sqlFilePath
        };
      }
    }

    // Step 2: Create table in Supabase
    if (!argv['analyze-only'] && !argv['import-only']) {
      console.log(`\n=== STEP 2: Creating table in Supabase ===`);
      const tableCreation = await createTableInSupabase();

      if (!tableCreation.success) {
        throw new Error(`Failed to create table: ${tableCreation.error}`);
      }

      console.log(`Table creation completed successfully.`);

      // Extract the table name from the SQL
      if (!targetTable) {
        const sqlLines = tableCreation.sqlExecuted.split('\n');
        const createTableLine = sqlLines.find(line => line.trim().startsWith('CREATE TABLE'));
        if (createTableLine) {
          const match = createTableLine.match(/CREATE TABLE public\.([a-z0-9_]+)/i);
          if (match && match[1]) {
            targetTable = match[1];
            console.log(`Extracted table name from SQL: ${targetTable}`);
          }
        }
      }

      // If we're only creating the table, stop here
      if (argv['create-only']) {
        console.log(`Create-only mode selected. Stopping after table creation.`);
        return {
          success: true,
          message: 'Table creation completed successfully',
          tableName: targetTable
        };
      }
    }

    // Step 3: Import data
    if (!argv['analyze-only'] && !argv['create-only']) {
      console.log(`\n=== STEP 3: Importing data ===`);

      // If target table is still not defined, generate it from the file name
      if (!targetTable) {
        const fileName = path.basename(dbfFilePath, path.extname(dbfFilePath)).toLowerCase();
        targetTable = `imported_${fileName.replace(/[^a-z0-9_]/g, '_')}`;
        console.log(`Generated table name from file name: ${targetTable}`);
      }

      const importResult = await importDbfToSupabase(dbfFilePath, targetTable, argv.truncate);

      if (!importResult.success) {
        throw new Error(`Failed to import data: ${importResult.error}`);
      }

      console.log(`Data import completed successfully. Imported ${importResult.recordsImported} records.`);
    }

    console.log('\nDBF import setup completed successfully!');

    return {
      success: true,
      message: 'DBF import setup completed successfully',
      tableName: targetTable
    };
  } catch (error) {
    console.error(`Error setting up DBF import: ${error.message}`);
    console.error(error.stack);

    return {
      success: false,
      error: error.message
    };
  }
}

// Run the setup if this script is executed directly
// For ES modules, we check if the import.meta.url is the same as the process.argv[1]
if (import.meta.url === `file://${process.argv[1]}`) {
  setupDbfImport()
    .then(result => {
      if (result.success) {
        console.log('Setup completed successfully.');
        process.exit(0);
      } else {
        console.error('Setup failed.');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Unhandled error during setup:', error);
      process.exit(1);
    });
}

// Export the function for use in other scripts
export default setupDbfImport;
