-- Check if order sheet line 16890 exists and if it matches with disc 421349
SELECT 
    osl.id AS osl_id,
    osl.mps_id AS osl_mps_id,
    osl.min_weight,
    osl.max_weight,
    osl.color_id AS osl_color_id,
    d.id AS disc_id,
    d.mps_id AS disc_mps_id,
    d.weight AS disc_weight,
    d.color_id AS disc_color_id,
    d.sold_date,
    -- Check matching criteria
    (d.mps_id = osl.mps_id) AS mps_match,
    (d.weight >= osl.min_weight AND d.weight <= osl.max_weight) AS weight_match,
    (osl.color_id = 23 OR d.color_id = osl.color_id) AS color_match,
    (d.sold_date IS NULL) AS not_sold,
    -- Should match if all criteria are true
    (
        d.mps_id = osl.mps_id AND
        d.weight >= osl.min_weight AND
        d.weight <= osl.max_weight AND
        (osl.color_id = 23 OR d.color_id = osl.color_id) AND
        d.sold_date IS NULL
    ) AS should_match
FROM 
    t_order_sheet_lines osl,
    t_discs d
WHERE 
    osl.id = 16890 AND
    d.id = 421349;
