// checkRproTableStructure.js - Check the structure of imported_table_rpro
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkTable() {
  try {
    // First try to get sample data to see if table exists and what fields are available
    console.log('📊 Checking table by fetching sample data...');
    const { data: sampleData, error: sampleError } = await supabase
      .from('imported_table_rpro')
      .select('*')
      .limit(1);

    if (sampleError) {
      console.error('Error fetching sample data:', sampleError.message);
      return;
    }

    if (!sampleData || sampleData.length === 0) {
      console.log('⚠️  Table exists but has no data');
      return;
    }

    console.log('✅ Table exists and has data');
    console.log('\nAvailable columns:');
    console.log('==================');
    const columns = Object.keys(sampleData[0]);
    columns.forEach(col => {
      const value = sampleData[0][col];
      const type = typeof value;
      console.log(`${col.padEnd(25)} ${type.padEnd(15)} ${value === null ? 'null' : String(value).substring(0, 50)}`);
    });

    // Check if todo field exists
    const hasTodo = columns.includes('todo');
    if (hasTodo) {
      console.log('\n✅ todo field exists!');
    } else {
      console.log('\n❌ todo field does not exist');
    }

    // Check more sample data with relevant fields
    console.log('\n📊 Sample data with relevant fields:');
    const { data: moreData, error: moreError } = await supabase
      .from('imported_table_rpro')
      .select('id, ivno, ivqtylaw, ivaux3, todo')
      .limit(5);

    if (moreError) {
      console.error('Error fetching more sample data:', moreError.message);
    } else {
      console.log('Sample records:');
      moreData.forEach(record => {
        console.log(`  ID: ${record.id}, IVNO: ${record.ivno}, Qty: ${record.ivqtylaw}, Bin: ${record.ivaux3 || 'null'}, Todo: ${record.todo || 'null'}`);
      });
    }
    
  } catch (err) {
    console.error('Exception:', err.message);
  }
}

checkTable();
