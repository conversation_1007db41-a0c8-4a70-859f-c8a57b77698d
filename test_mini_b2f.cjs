const http = require('http');

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000 // 30 second timeout for debugging
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(body));
        } catch (e) {
          resolve(body);
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testMiniMoldB2F() {
  try {
    console.log('Testing mini mold B2F logic...');
    console.log('This test will run auto-select and look for mini molds specifically.\n');
    
    // Test with a small number first
    console.log('1. Testing with maxOsls: 2');
    const result1 = await makeRequest('/api/b2f/auto-select', 'POST', { maxOsls: 2 });
    console.log('Result 1:');
    console.log('- Success:', result1.success);
    console.log('- Message:', result1.message);
    console.log('- Total OSLs:', result1.summary?.totalOsls || 0);
    console.log('- Total discs:', result1.summary?.totalDiscs || 0);
    
    if (result1.selectedDiscs && result1.selectedDiscs.length > 0) {
      console.log('- Selected discs:');
      result1.selectedDiscs.forEach(osl => {
        const miniFlag = osl.isMiniMold ? ' 🏆 MINI MOLD' : '';
        console.log(`  • ${osl.osl} (${osl.moldName || 'Unknown'})${miniFlag}`);
      });
    } else {
      console.log('- No discs selected');
    }
    
    console.log('\n2. Testing with maxOsls: 5');
    const result2 = await makeRequest('/api/b2f/auto-select', 'POST', { maxOsls: 5 });
    console.log('Result 2:');
    console.log('- Success:', result2.success);
    console.log('- Message:', result2.message);
    console.log('- Total OSLs:', result2.summary?.totalOsls || 0);
    console.log('- Total discs:', result2.summary?.totalDiscs || 0);
    
    if (result2.selectedDiscs && result2.selectedDiscs.length > 0) {
      console.log('- Selected discs:');
      result2.selectedDiscs.forEach(osl => {
        const miniFlag = osl.isMiniMold ? ' 🏆 MINI MOLD' : '';
        console.log(`  • ${osl.osl} (${osl.moldName || 'Unknown'})${miniFlag}`);
      });
    } else {
      console.log('- No discs selected');
    }
    
    console.log('\n3. Testing with maxOsls: 50000 (like you normally use)');
    const result3 = await makeRequest('/api/b2f/auto-select', 'POST', { maxOsls: 50000 });
    console.log('Result 3:');
    console.log('- Success:', result3.success);
    console.log('- Message:', result3.message);
    console.log('- Total OSLs:', result3.summary?.totalOsls || 0);
    console.log('- Total discs:', result3.summary?.totalDiscs || 0);

    if (result3.selectedDiscs && result3.selectedDiscs.length > 0) {
      console.log('- Selected discs:');
      let miniMoldCount = 0;
      result3.selectedDiscs.forEach(osl => {
        const miniFlag = osl.isMiniMold ? ' 🏆 MINI MOLD' : '';
        console.log(`  • ${osl.osl} (${osl.moldName || 'Unknown'})${miniFlag}`);
        if (osl.isMiniMold) {
          miniMoldCount++;
          console.log(`    ✅ FOUND MINI MOLD: ${osl.moldName}`);
        }
      });

      if (miniMoldCount > 0) {
        console.log(`\n🎉 SUCCESS! Found ${miniMoldCount} mini mold(s) selected for B2F!`);
      } else {
        console.log(`\n❌ No mini molds found in the selected discs.`);
      }
    } else {
      console.log('- No discs selected');
    }

    if (result3.error) {
      console.log('- Error:', result3.error);
    }
    
  } catch (err) {
    console.error('Error:', err.message);
  }
}

testMiniMoldB2F();
