import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

// Import the getOrderSummary function from daily automation
async function getOrderSummary() {
    try {
        console.log('📊 Getting order summary (daily automation logic)...');
        
        // Get ALL orderable products from the base table (not just matched ones from the view)
        // This includes products that haven't been matched to MPS yet
        let allOrderableData = [];
        let from = 0;
        const pageSize = 1000;
        
        while (true) {
            const { data: batch, error: orderableError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
                .eq('is_orderable', true)
                .not('excel_mapping_key', 'is', null)  // Must have Excel mapping
                .range(from, from + pageSize - 1)
                .order('excel_mapping_key');

            if (orderableError) {
                throw new Error(`Failed to query orderable data: ${orderableError.message}`);
            }

            if (batch.length === 0) break;
            
            allOrderableData = allOrderableData.concat(batch);
            from += pageSize;
            
            if (batch.length < pageSize) break; // Last page
        }

        // Get order quantities from the view for matched products only using pagination
        let viewOrderData = [];
        from = 0;
        
        while (true) {
            const { data: batch, error: viewError } = await supabase
                .from('v_stats_by_osl_discraft')
                .select('excel_mapping_key, "order", mold_name, plastic_name, is_currently_available')
                .not('excel_mapping_key', 'is', null)
                .range(from, from + pageSize - 1);

            if (viewError) {
                throw new Error(`Failed to query view order data: ${viewError.message}`);
            }

            if (batch.length === 0) break;
            
            viewOrderData = viewOrderData.concat(batch);
            from += pageSize;
            
            if (batch.length < pageSize) break; // Last page
        }

        // Create a map of order data by excel_mapping_key
        const orderMap = {};
        viewOrderData.forEach(item => {
            orderMap[item.excel_mapping_key] = {
                order: item.order || 0,
                mold_name: item.mold_name,
                plastic_name: item.plastic_name,
                is_currently_available: item.is_currently_available
            };
        });

        // Combine all orderable products with their order quantities (defaulting to 0)
        const orderableData = allOrderableData.map(item => {
            const orderInfo = orderMap[item.excel_mapping_key] || {};
            return {
                excel_mapping_key: item.excel_mapping_key,
                excel_column: item.excel_column,
                excel_row_hint: item.excel_row_hint,
                order: orderInfo.order || 0,  // Default to 0 if no order or not matched
                calculated_mps_id: item.calculated_mps_id,
                mold_name: orderInfo.mold_name || 'Unknown',
                plastic_name: orderInfo.plastic_name || 'Unknown',
                is_currently_available: orderInfo.is_currently_available || false
            };
        });

        return {
            success: true,
            detailedData: orderableData
        };
        
    } catch (error) {
        console.error('❌ Order summary failed:', error);
        return { success: false, error: error.message };
    }
}

async function testDailyAutomationExport() {
  try {
    console.log('🧪 Testing daily automation export logic...\n');
    
    // Get order summary (same as daily automation)
    const orderSummary = await getOrderSummary();
    
    if (!orderSummary.success) {
      throw new Error(`Order summary failed: ${orderSummary.error}`);
    }
    
    console.log(`✅ Order summary completed: ${orderSummary.detailedData.length} records`);
    
    // Check records after line 332
    const afterLine332 = orderSummary.detailedData.filter(item => item.excel_row_hint > 332);
    console.log(`✅ Records after line 332: ${afterLine332.length}`);
    
    // Show sample of records with MPS IDs
    const withMpsIds = afterLine332.filter(item => item.calculated_mps_id);
    console.log(`✅ Records with MPS IDs after line 332: ${withMpsIds.length}`);
    
    console.log('\nSample records with MPS IDs:');
    withMpsIds.slice(0, 10).forEach((record, index) => {
      console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: MPS=${record.calculated_mps_id} | ${record.mold_name}`);
    });
    
    // Create MPS data for export (same as daily automation will do)
    const mpsData = orderSummary.detailedData.map(item => ({
        ...item,
        order: item.calculated_mps_id || 'NO_MPS' // Use calculated_mps_id or show NO_MPS
    }));
    
    // Check MPS data after line 332
    const mpsAfter332 = mpsData.filter(item => item.excel_row_hint > 332);
    const mpsWithIds = mpsAfter332.filter(item => item.order !== 'NO_MPS');
    
    console.log(`\n📊 MPS Export Preview:`);
    console.log(`   • Total records: ${mpsData.length}`);
    console.log(`   • Records after line 332: ${mpsAfter332.length}`);
    console.log(`   • Records with MPS IDs after line 332: ${mpsWithIds.length}`);
    console.log(`   • Records with NO_MPS after line 332: ${mpsAfter332.length - mpsWithIds.length}`);
    
    console.log('\n🎉 Daily automation export test completed!');
    console.log('\n📋 Summary:');
    console.log(`   • The daily automation WILL create MPS exports with valid MPS IDs`);
    console.log(`   • Records after line 332 ARE being processed`);
    console.log(`   • ${mpsWithIds.length} records after line 332 have valid MPS IDs`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testDailyAutomationExport().catch(console.error);
