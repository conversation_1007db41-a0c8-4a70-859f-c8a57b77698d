require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testMiniMoldSQL() {
  try {
    console.log('Testing mini mold SQL query...\n');

    // First, let's test using Supabase client directly
    console.log('1. Testing Supabase client query for known mini mold IDs...');
    const { data: directResult, error: directError } = await supabase
      .from('t_discs')
      .select('id, g_pull, location, sold_date, order_sheet_line_id')
      .in('id', [428958, 428956]);

    if (directError) {
      console.error('Error in direct query:', directError);
    } else {
      console.log(`Direct query found ${directResult?.length || 0} discs:`);
      directResult?.forEach(disc => {
        console.log(`- ID ${disc.id}: ${disc.g_pull}, location: ${disc.location}, OSL: ${disc.order_sheet_line_id}`);
      });
    }

    // Now test with exec_sql
    console.log('\n2. Testing exec_sql for known mini mold IDs...');
    const simpleQuery = `
      SELECT id, g_pull, location, sold_date, order_sheet_line_id
      FROM t_discs
      WHERE id IN (428958, 428956)
    `;

    const { data: simpleResult, error: simpleError } = await supabase.rpc('exec_sql', {
      sql_query: simpleQuery
    });

    if (simpleError) {
      console.error('Error in exec_sql query:', simpleError);
    } else {
      console.log(`Exec_sql query found ${simpleResult?.length || 0} discs:`);
      simpleResult?.forEach(disc => {
        console.log(`- ID ${disc.id}: ${disc.g_pull}, location: ${disc.location}, OSL: ${disc.order_sheet_line_id}`);
      });
    }

    // Now test the complex query step by step
    console.log('\n2. Testing complex query with joins...');
    const complexQuery = `
      SELECT
        d.id,
        d.g_pull as disc,
        d.location,
        d.sold_date,
        osl.id as osl_id,
        osl.g_code as osl,
        mps.id as mps_id,
        m.mold as mold_name
      FROM t_discs d
      JOIN t_order_sheet_lines osl ON d.order_sheet_line_id = osl.id
      JOIN t_mps mps ON osl.mps_id = mps.id
      JOIN t_molds m ON mps.mold_id = m.id
      WHERE d.id IN (428958, 428956)
    `;

    const { data: complexResult, error: complexError } = await supabase.rpc('exec_sql', {
      sql_query: complexQuery
    });

    if (complexError) {
      console.error('Error in complex query:', complexError);
    } else {
      console.log(`Complex query found ${complexResult?.length || 0} discs:`);
      complexResult?.forEach(disc => {
        console.log(`- ID ${disc.id}: ${disc.disc} (${disc.mold_name})`);
        console.log(`  Location: ${disc.location}, OSL: ${disc.osl}`);
      });
    }

    // Finally, test the full mini mold query
    console.log('\n3. Testing full mini mold query...');
    const miniMoldQuery = `
      SELECT
        d.id,
        d.g_pull as disc,
        osl.id as osl_id,
        osl.g_code as osl,
        d.grade as disc_grade,
        d.color_id,
        mps.release_date_online,
        mps.created_at as mps_created_at,
        m.mold as mold_name
      FROM t_discs d
      JOIN t_order_sheet_lines osl ON d.order_sheet_line_id = osl.id
      JOIN t_mps mps ON osl.mps_id = mps.id
      JOIN t_molds m ON mps.mold_id = m.id
      WHERE m.mold ILIKE '%mini%'
        AND d.location = 'BS'
        AND d.sold_date IS NULL
      ORDER BY d.order_sheet_line_id, d.g_pull
    `;

    const { data: allMiniMoldsInBS, error: miniMoldError } = await supabase.rpc('exec_sql', {
      sql_query: miniMoldQuery
    });

    if (miniMoldError) {
      console.error('Error executing full mini mold query:', miniMoldError);
    } else {
      console.log(`Full query found ${allMiniMoldsInBS?.length || 0} mini molds in back stock:`);

      if (allMiniMoldsInBS && allMiniMoldsInBS.length > 0) {
        allMiniMoldsInBS.forEach(disc => {
          console.log(`- ID ${disc.id}: ${disc.disc} (${disc.mold_name})`);
          console.log(`  OSL: ${disc.osl}`);
        });
      } else {
        console.log('No mini molds found in back stock');
      }
    }

  } catch (err) {
    console.error('Exception:', err);
  }
}

testMiniMoldSQL();
