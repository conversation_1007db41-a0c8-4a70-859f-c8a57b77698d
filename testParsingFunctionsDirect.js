// Test the parsing functions directly without database
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Import the parsing functions from fullDiscraftImport.js
// We'll need to extract the functions or create a test version

function extractMoldName(model) {
    if (!model) return 'Unknown';

    const modelStr = model.toString().trim();

    // Handle special cases first
    if (modelStr.includes('Cigarra')) return 'Cigarra';
    if (modelStr.includes('Buzzz SS')) return 'BuzzzSS';
    if (modelStr.includes('Surge SS')) return 'Surge SS';
    if (modelStr.includes('Avenger SS')) return 'Avenger SS';
    if (modelStr.includes('Crank SS')) return 'CrankSS';
    if (modelStr.includes('Buzzz OS')) return 'BuzzzOS';
    if (modelStr.includes('Nuke OS')) return 'NukeOS';
    if (modelStr.includes('Nuke SS')) return 'Nuke SS';
    if (modelStr.includes('Zone OS')) return 'Zone OS';
    if (modelStr.includes('Banger GT')) return 'Banger GT';
    if (modelStr.includes('Challenger OS')) return 'Challenger OS';
    if (modelStr.includes('Challenger SS')) return 'Challenger SS';
    if (modelStr.includes('Ringer GT')) return 'Ringer GT';

    // Remove common prefixes and suffixes
    let mold = modelStr
        .replace(/^(Fly Dye Z|Z Glo|Z Lite|Z|ESP|Jawbreaker|X)\s+/i, '')
        .replace(/\s+(Discontinued|Retired stamp|NEW).*$/i, '')
        .replace(/^(PS|RW|PP)\s+(Z|ESP)\s+/i, '')
        .replace(/^Soft\s+/i, '') // Remove "Soft" prefix
        .replace(/^Hard\s+/i, '') // Remove "Hard" prefix
        .replace(/^Big Z\s+/i, '') // Remove "Big Z" prefix
        .trim();

    // Handle "NEW -" prefix
    if (mold.startsWith('NEW -')) {
        mold = mold.replace(/^NEW\s*-\s*/, '').trim();
    }

    // Handle discontinued/retired patterns
    if (mold.includes('Discontinued -')) {
        mold = mold.replace(/.*Discontinued\s*-\s*/, '').trim();
    }

    if (mold.includes('Retired stamp -')) {
        mold = mold.replace(/.*Retired stamp\s*-\s*/, '').trim();
    }

    // Extract first word as mold name, but handle compound names
    const words = mold.split(/\s+/);
    if (words.length === 1) {
        return words[0] || 'Unknown';
    }

    // For compound names, check if it's a known multi-word mold
    const multiWordMolds = [
        'Zone OS', 'Buzzz SS', 'Buzzz OS', 'Nuke OS', 'Nuke SS', 'Surge SS', 
        'Avenger SS', 'Crank SS', 'Banger GT', 'Challenger OS', 'Challenger SS', 'Ringer GT'
    ];
    
    const fullName = words.join(' ');
    for (const multiWord of multiWordMolds) {
        if (fullName.includes(multiWord)) {
            return multiWord;
        }
    }

    // Default to first word
    return words[0] || 'Unknown';
}

function standardizePlasticName(rawPlastic, rawModel) {
    const plastic = rawPlastic?.toString().trim() || '';
    const model = rawModel?.toString().trim() || '';

    // Handle Hard/Soft putter line parsing
    if (plastic === 'Hard') {
        return 'Putter Line Hard';
    }

    if (plastic === 'Soft') {
        return 'Putter Line Soft';
    }

    return plastic || 'Unknown';
}

function parseMoldAndStamp(rawModel, rawPlastic, vendorDescription = '') {
    const model = rawModel?.toString().trim() || '';
    const plastic = rawPlastic?.toString().trim() || '';

    // Handle Hard putter line parsing
    if (plastic === 'Hard') {
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'Stock'
        };
    }

    // Handle Soft putter line parsing
    if (plastic === 'Soft') {
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'Stock'
        };
    }

    return {
        mold_name: extractMoldName(model),
        stamp_name: 'Stock'
    };
}

async function testParsingFunctionsDirect() {
    console.log('🧪 Testing parsing functions directly...\n');

    // Test cases from the user's requirements
    const testCases = [
        {
            name: 'Hard Challenger OS (line 369)',
            plastic: 'Hard',
            model: 'Challenger OS',
            expected: {
                plastic_name: 'Putter Line Hard',
                mold_name: 'Challenger OS',
                stamp_name: 'Stock'
            }
        },
        {
            name: 'Hard Challenger SS',
            plastic: 'Hard',
            model: 'Challenger SS',
            expected: {
                plastic_name: 'Putter Line Hard',
                mold_name: 'Challenger SS',
                stamp_name: 'Stock'
            }
        },
        {
            name: 'Soft Ringer GT (line 383)',
            plastic: 'Soft',
            model: 'Ringer GT',
            expected: {
                plastic_name: 'Putter Line Soft',
                mold_name: 'Ringer GT',
                stamp_name: 'Stock'
            }
        }
    ];

    testCases.forEach((testCase, index) => {
        console.log(`${index + 1}. Testing ${testCase.name}:`);
        console.log(`   Input: "${testCase.plastic}" | "${testCase.model}"`);
        
        const plasticName = standardizePlasticName(testCase.plastic, testCase.model);
        const { mold_name, stamp_name } = parseMoldAndStamp(testCase.model, testCase.plastic);
        
        console.log(`   Output: ${plasticName} | ${mold_name} | ${stamp_name}`);
        console.log(`   Expected: ${testCase.expected.plastic_name} | ${testCase.expected.mold_name} | ${testCase.expected.stamp_name}`);
        
        const isCorrect = plasticName === testCase.expected.plastic_name && 
                         mold_name === testCase.expected.mold_name && 
                         stamp_name === testCase.expected.stamp_name;
        
        console.log(`   ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}\n`);
    });

    // Test extractMoldName function specifically
    console.log('🔧 Testing extractMoldName function:');
    const moldTests = [
        { input: 'Challenger OS', expected: 'Challenger OS' },
        { input: 'Challenger SS', expected: 'Challenger SS' },
        { input: 'Ringer GT', expected: 'Ringer GT' },
        { input: 'Zone OS', expected: 'Zone OS' },
        { input: 'Banger GT', expected: 'Banger GT' },
        { input: 'Buzzz SS', expected: 'BuzzzSS' },
        { input: 'Regular Mold', expected: 'Regular' }
    ];

    moldTests.forEach((test, index) => {
        const result = extractMoldName(test.input);
        const isCorrect = result === test.expected;
        console.log(`   ${index + 1}. "${test.input}" → "${result}" (expected: "${test.expected}") ${isCorrect ? '✅' : '❌'}`);
    });

    console.log('\n🎉 Direct parsing test completed!');
}

testParsingFunctionsDirect().catch(console.error);
