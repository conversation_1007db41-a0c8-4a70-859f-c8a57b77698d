// importInnovaOrderTable.js
import dotenv from 'dotenv';
dotenv.config();
import XLSX from 'xlsx';
import path from 'path';
import fs from 'fs';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Missing Supabase URL or key in environment variables.');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

export async function importData() {
  // Define the path to your Excel file
  const filePath = path.join(process.cwd(), 'data', 'innovaorderform.xlsx');
  if (!fs.existsSync(filePath)) {
    throw new Error(`File not found: ${filePath}`);
  }

  // Read the workbook and select the "Order_Table" sheet
  const workbook = XLSX.readFile(filePath);
  const sheetName = 'Order_Table';
  const worksheet = workbook.Sheets[sheetName];
  if (!worksheet) {
    throw new Error(`Sheet "${sheetName}" not found in the workbook.`);
  }

  // Convert the worksheet to JSON using the first row as keys
  const jsonData = XLSX.utils.sheet_to_json(worksheet, { defval: null });
  console.log(`Found ${jsonData.length} records in sheet "${sheetName}".`);

  // Define the expected columns (based on your previous output)
  const expectedColumns = [
    "Category",
    "Sub-Category",
    "Description",
    "Availability",
    "SKU",
    "Internal ID",
    "Parent ID",
    "Ordered",
    "Matrix Type",
    "Matrix Option 1",
    "Matrix Option 2",
    "Base Price",
    "Dealer 25",
    "Dealer 50",
    "Dealer 100",
    "Dealer 200",
    "Dealer 500",
    "Current Price",
    "Total Price"
  ];

  // Create a new array with only the expected columns
  const dataToInsert = jsonData.map(record => {
    const newRecord = {};
    expectedColumns.forEach(col => {
      newRecord[col] = record[col] !== undefined ? record[col] : null;
    });
    return newRecord;
  });

  // First, delete existing rows (overwrite old data)
  console.log('Deleting old data from innova_osl...');
  const { error: deleteError } = await supabase
    .from('innova_osl')
    .delete()
    .neq('id', 0); // Assumes id > 0 for all rows
  if (deleteError) {
    throw new Error(`Error deleting old data: ${JSON.stringify(deleteError)}`);
  }
  console.log('Old data deleted.');

  // Insert new data into the Supabase table "innova_osl"
  const { data, error } = await supabase
    .from('innova_osl')
    .insert(dataToInsert);

  if (error) {
    throw new Error(`Error inserting new data: ${JSON.stringify(error)}`);
  }
  console.log('Data successfully inserted into Supabase:', data);
}
