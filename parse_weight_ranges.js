import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

function parseWeightRange(weightString) {
    if (!weightString || typeof weightString !== 'string') {
        return { min_weight: null, max_weight: null };
    }
    
    // Remove 'g' and trim whitespace
    const cleaned = weightString.replace(/g/gi, '').trim();
    
    // Pattern 1: Range like "120-130", "165-169", "170-172"
    const rangeMatch = cleaned.match(/^(\d+)-(\d+)$/);
    if (rangeMatch) {
        return {
            min_weight: parseInt(rangeMatch[1]),
            max_weight: parseInt(rangeMatch[2])
        };
    }
    
    // Pattern 2: Plus ranges like "173+", "177+"
    const plusMatch = cleaned.match(/^(\d+)\+$/);
    if (plusMatch) {
        const baseWeight = parseInt(plusMatch[1]);
        if (baseWeight === 173) {
            return { min_weight: 173, max_weight: 176 };
        } else if (baseWeight === 177) {
            return { min_weight: 177, max_weight: 180 };
        } else {
            // For other plus weights, assume +3g range
            return { min_weight: baseWeight, max_weight: baseWeight + 3 };
        }
    }
    
    // Pattern 3: Single weight like "175"
    const singleMatch = cleaned.match(/^(\d+)$/);
    if (singleMatch) {
        const weight = parseInt(singleMatch[1]);
        return { min_weight: weight, max_weight: weight };
    }
    
    // If no pattern matches, return null
    console.log(`Could not parse weight: "${weightString}"`);
    return { min_weight: null, max_weight: null };
}

async function updateWeightRanges() {
    console.log('Starting weight range parsing...');
    
    try {
        // Get all records with variant_option2 data
        console.log('Fetching records with weight data...');
        const { data: records, error: fetchError } = await supabase
            .from('it_dd_osl')
            .select('id, variant_option2')
            .not('variant_option2', 'is', null);
        
        if (fetchError) {
            console.error('Error fetching records:', fetchError);
            return;
        }
        
        console.log(`Found ${records.length} records with weight data`);
        
        // Parse weights and prepare updates
        const updates = [];
        const parseStats = {
            total: records.length,
            parsed: 0,
            failed: 0,
            patterns: {}
        };
        
        for (const record of records) {
            const { min_weight, max_weight } = parseWeightRange(record.variant_option2);
            
            if (min_weight !== null && max_weight !== null) {
                updates.push({
                    id: record.id,
                    parsed_min_weight: min_weight,
                    parsed_max_weight: max_weight
                });
                parseStats.parsed++;
                
                // Track patterns for statistics
                const pattern = record.variant_option2;
                parseStats.patterns[pattern] = (parseStats.patterns[pattern] || 0) + 1;
            } else {
                parseStats.failed++;
                console.log(`Failed to parse: "${record.variant_option2}" (ID: ${record.id})`);
            }
        }
        
        console.log(`\nParsing complete:`);
        console.log(`- Successfully parsed: ${parseStats.parsed}`);
        console.log(`- Failed to parse: ${parseStats.failed}`);
        
        // Show pattern breakdown
        console.log('\nWeight patterns found:');
        Object.entries(parseStats.patterns)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 10)
            .forEach(([pattern, count]) => {
                console.log(`  "${pattern}": ${count} records`);
            });
        
        if (updates.length === 0) {
            console.log('No updates to perform.');
            return;
        }
        
        // Update records in batches
        console.log(`\nUpdating ${updates.length} records...`);
        const batchSize = 100;
        let updatedCount = 0;
        
        for (let i = 0; i < updates.length; i += batchSize) {
            const batch = updates.slice(i, i + batchSize);
            
            // Update each record in the batch
            for (const update of batch) {
                const { error: updateError } = await supabase
                    .from('it_dd_osl')
                    .update({
                        parsed_min_weight: update.parsed_min_weight,
                        parsed_max_weight: update.parsed_max_weight
                    })
                    .eq('id', update.id);
                
                if (updateError) {
                    console.error(`Error updating record ${update.id}:`, updateError);
                } else {
                    updatedCount++;
                }
            }
            
            console.log(`Updated batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(updates.length/batchSize)} (${updatedCount}/${updates.length} records)`);
        }
        
        console.log(`\nUpdate complete! Updated ${updatedCount} records.`);
        
        // Verify results
        console.log('\nVerifying results...');
        const { data: verifyData, error: verifyError } = await supabase
            .from('it_dd_osl')
            .select('variant_option2, parsed_min_weight, parsed_max_weight')
            .not('parsed_min_weight', 'is', null)
            .limit(10);
        
        if (!verifyError && verifyData) {
            console.log('\nSample parsed results:');
            verifyData.forEach(row => {
                console.log(`"${row.variant_option2}" → ${row.parsed_min_weight}-${row.parsed_max_weight}g`);
            });
        }
        
        // Get final statistics
        const { data: finalStats, error: statsError } = await supabase
            .from('it_dd_osl')
            .select('parsed_min_weight, parsed_max_weight')
            .not('parsed_min_weight', 'is', null);
        
        if (!statsError && finalStats) {
            console.log(`\nFinal statistics:`);
            console.log(`- Records with parsed weights: ${finalStats.length}`);
            
            const weightRanges = {};
            finalStats.forEach(row => {
                const range = `${row.parsed_min_weight}-${row.parsed_max_weight}g`;
                weightRanges[range] = (weightRanges[range] || 0) + 1;
            });
            
            console.log('\nWeight range distribution:');
            Object.entries(weightRanges)
                .sort((a, b) => b[1] - a[1])
                .forEach(([range, count]) => {
                    console.log(`  ${range}: ${count} records`);
                });
        }
        
    } catch (error) {
        console.error('Script failed:', error);
    }
}

// Test the parsing function with sample data
function testParsing() {
    console.log('Testing weight parsing function...\n');
    
    const testCases = [
        '120-130g',
        '165-169g', 
        '170-172g',
        '173+g',
        '177+g',
        '175g',
        '180',
        null,
        '',
        'invalid',
        '150-160',
        '174+'
    ];
    
    testCases.forEach(testCase => {
        const result = parseWeightRange(testCase);
        console.log(`"${testCase}" → min: ${result.min_weight}, max: ${result.max_weight}`);
    });
    
    console.log('\n');
}

// Run the script
if (import.meta.url === `file://${process.argv[1].replace(/\\/g, '/')}`) {
    console.log('Weight Range Parser for Dynamic Discs Data\n');
    
    // Run tests first
    testParsing();
    
    // Then run the actual update
    updateWeightRanges()
        .then(() => {
            console.log('\nWeight parsing completed successfully');
            process.exit(0);
        })
        .catch(error => {
            console.error('Weight parsing failed:', error);
            process.exit(1);
        });
}

export { parseWeightRange, updateWeightRanges };
