// updateOSLDescriptions.js
// Bulk update body_html (descriptions) for OS (Order Sheet) products on Shopify
// Description content is built from: t_mps.g_blurb_with_link, v_molds.blurb_with_link, v_plastics.blurb_with_link, t_stamps.blurb
// Matching is performed by product handle generated from brand + plastic + mold + stamp, same as publishProductOSL.js

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import minimist from 'minimist';

const args = minimist(process.argv.slice(2));
const singleMpsId = args.mps_id ? Number(args.mps_id) : null;
const singleOslId = args.id ? Number(args.id) : null; // allow targeting by OSL id
const onlyEmpty = args.onlyEmpty || false; // if true, only update when current body_html is empty
const dryRun = args.dryRun || false;
const limit = args.limit ? Number(args.limit) : null; // optional limit for testing
// Hard-coded cutoff for idempotent backfill:
// 9/24/2025 4:36pm US Central Daylight Time (CDT, UTC-05:00) = 2025-09-24T21:36:00Z
const cutoffArg = args.cutoff ? String(args.cutoff) : null; // allow override via --cutoff=ISO8601
const defaultCutoffIso = '2025-09-24T21:36:00Z';
const cutoffIso = cutoffArg || defaultCutoffIso;
console.log(`INFO: Using cutoff timestamp: ${cutoffIso}`);


// Supabase setup
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Missing SUPABASE_URL or SUPABASE_KEY');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

// Shopify setup
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT; // GraphQL endpoint
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
if (!shopifyEndpoint || !shopifyAccessToken) {
  console.error('ERROR: Missing SHOPIFY_ENDPOINT or SHOPIFY_ACCESS_TOKEN');
  process.exit(1);
}
const productsEndpoint = shopifyEndpoint.replace('graphql.json', 'products.json');

function delay(ms) { return new Promise(r => setTimeout(r, ms)); }

// GraphQL request helper
async function shopifyGraphQLRequest(query, variables = {}) {
  const res = await fetch(shopifyEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken,
    },
    body: JSON.stringify({ query, variables }),
  });
  const body = await res.json();
  if (!res.ok || body.errors) {
    throw new Error(`GraphQL error: ${JSON.stringify(body.errors || body.data)}`);
  }
  return body.data;
}

async function getProductByHandle(handle) {
  const query = `
    query getProductByHandle($handle: String!) {
      productByHandle(handle: $handle) { id handle title }
    }
  `;
  const data = await shopifyGraphQLRequest(query, { handle });
  return data.productByHandle; // null if not found
}

async function getProductDetailsById(productId) {
  const productBase = productsEndpoint.split('/products.json')[0];
  const singleProductUrl = `${productBase}/products/${productId}.json`;
  const response = await fetch(singleProductUrl, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken,
    },
  });
  const result = await response.json();
  if (!response.ok) {
    throw new Error(`Error fetching product ${productId}: ${JSON.stringify(result)}`);
  }
  return result.product;
}

async function updateProductBodyHtml(productId, bodyHtml) {
  const productBase = productsEndpoint.split('/products.json')[0];
  const url = `${productBase}/products/${productId}.json`;
  const payload = { product: { id: Number(productId), body_html: bodyHtml } };
  if (dryRun) {
    console.log(`[DRY RUN] Would update product ${productId} body_html length=${bodyHtml.length}`);
    return { product: { id: productId } };
  }
  const response = await fetch(url, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken,
    },
    body: JSON.stringify(payload),
  });
  const result = await response.json();
  if (!response.ok) {
    throw new Error(`Error updating product ${productId}: ${JSON.stringify(result)}`);
  }
  return result.product;
}

// Handle generator copied from publishProductOSL.js to ensure identical handles
function generateMPSHandle(brand, plastic, mold, stamp) {
  let base = `${brand}-${plastic}-${mold}-${stamp}`.toLowerCase();
  base = base
    .replace(/ /g, '-')
    .replace(/'/g, '')
    .replace(/\//g, '')
    .replace(/\./g, '-')
    .replace(/&/g, '-')
    .replace(/\(/g, '')
    .replace(/\)/g, '')
    .replace(/"/g, '')
    .replace(/%/g, '')
    .replace(/#/g, '')
    .replace(/-\$/g, '');
  while (base.includes('--')) {
    base = base.replace(/--/g, '-');
  }
  return base;
}

async function buildOSLBodyHtmlFromMpsId(mpsId) {
  // Fetch t_mps
  const { data: mpsRecord, error: mpsError } = await supabase
    .from('t_mps')
    .select('*')
    .eq('id', mpsId)
    .single();
  if (mpsError) throw new Error(`Fetch t_mps ${mpsId} failed: ${mpsError.message}`);

  // Fetch t_plastics + v_plastics blurb
  const { data: plasticTableData, error: plasticTableError } = await supabase
    .from('t_plastics')
    .select('*')
    .eq('id', mpsRecord.plastic_id)
    .single();
  if (plasticTableError) throw new Error(`Fetch t_plastics failed: ${plasticTableError.message}`);
  const { data: plasticViewData, error: plasticViewError } = await supabase
    .from('v_plastics')
    .select('blurb_with_link')
    .eq('id', mpsRecord.plastic_id)
    .maybeSingle();
  if (plasticViewError) console.warn(`WARNING: v_plastics fetch: ${plasticViewError.message}`);
  const plasticData = { ...plasticTableData, blurb_with_link: plasticViewData ? plasticViewData.blurb_with_link : '' };

  // Fetch t_molds + v_molds blurb
  const { data: moldTableData, error: moldTableError } = await supabase
    .from('t_molds')
    .select('*')
    .eq('id', mpsRecord.mold_id)
    .single();
  if (moldTableError) throw new Error(`Fetch t_molds failed: ${moldTableError.message}`);
  const { data: moldViewData, error: moldViewError } = await supabase
    .from('v_molds')
    .select('blurb_with_link')
    .eq('id', mpsRecord.mold_id)
    .maybeSingle();
  if (moldViewError) console.warn(`WARNING: v_molds fetch: ${moldViewError.message}`);
  const moldData = { ...moldTableData, blurb_with_link: moldViewData ? moldViewData.blurb_with_link : '' };

  // Fetch stamp (and optionally player; not needed for description but harmless if present)
  const { data: stampData, error: stampError } = await supabase
    .from('t_stamps')
    .select('*, blurb')
    .eq('id', mpsRecord.stamp_id)
    .single();
  if (stampError) throw new Error(`Fetch t_stamps failed: ${stampError.message}`);

  // Fetch brand for handle title context
  const { data: brandData, error: brandError } = await supabase
    .from('t_brands')
    .select('*')
    .eq('id', moldData.brand_id)
    .single();
  if (brandError) throw new Error(`Fetch t_brands failed: ${brandError.message}`);

  // Assemble body_html
  const vMPSBlurbWithLink = mpsRecord.g_blurb_with_link || '';
  let body_html = '';
  body_html += vMPSBlurbWithLink || '';
  body_html += moldData.blurb_with_link || '';
  body_html += plasticData.blurb_with_link || '';
  body_html += stampData.blurb || '';

  // Build handle (same as creation)
  const handle = generateMPSHandle(
    brandData.brand,
    plasticData.plastic,
    moldData.mold,
    stampData.stamp
  );

  return { body_html, handle };
}

async function run() {
  console.log('INFO: Starting OSL description updater...');

  let targets = [];
  if (singleMpsId) {
    targets = [{ mps_id: singleMpsId }];
  } else if (singleOslId) {
    // Map OSL -> mps_id
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id')
      .eq('id', singleOslId)
      .single();
    if (oslError) throw new Error(`Fetch OSL ${singleOslId} failed: ${oslError.message}`);
    targets = [{ mps_id: osl.mps_id }];
  } else {
    // Distinct mps_id for OSLs that have been uploaded to Shopify
    let query = supabase
      .from('t_order_sheet_lines')
      .select('mps_id', { count: 'exact' })
      .not('shopify_uploaded_at', 'is', null)
      .lt('shopify_uploaded_at', cutoffIso);
    if (limit) query = query.limit(limit);

    const { data, error } = await query;
    if (error) throw new Error(`Fetch OSLs list failed: ${error.message}`);

    // Deduplicate mps_ids
    const seen = new Set();
    for (const row of data) {
      if (!seen.has(row.mps_id)) {
        seen.add(row.mps_id);
        targets.push({ mps_id: row.mps_id });
      }
    }
  }

  console.log(`INFO: Found ${targets.length} product groups to process.`);

  let updated = 0;
  let skipped = 0;
  for (const { mps_id } of targets) {
    try {
      const { body_html, handle } = await buildOSLBodyHtmlFromMpsId(mps_id);

      // Find product by handle
      const product = await getProductByHandle(handle);
      if (!product) {
        console.warn(`WARN: No product found by handle '${handle}' (mps_id ${mps_id}). Clearing shopify_uploaded_at so they can be re-enqueued.`);
        if (!dryRun) {
          const { error: clrErr } = await supabase
            .from('t_order_sheet_lines')
            .update({ shopify_uploaded_at: null })
            .eq('mps_id', mps_id)
            .not('shopify_uploaded_at', 'is', null)
            .lt('shopify_uploaded_at', cutoffIso);
          if (clrErr) {
            console.warn(`WARN: Failed to clear shopify_uploaded_at for mps_id ${mps_id}: ${clrErr.message}`);
          } else {
            console.log(`INFO: Cleared shopify_uploaded_at for OSLs with mps_id ${mps_id}.`);
          }
        }
        skipped++;
        continue;
      }

      // If onlyEmpty, fetch product details to check body_html
      if (onlyEmpty) {
        const details = await getProductDetailsById(product.id.match(/(\d+)$/)[1]);
        const current = (details && details.body_html) ? String(details.body_html).trim() : '';
        if (current.length > 0) {
          console.log(`INFO: Skipping product ${product.id} (handle ${handle}) because body_html is already populated (${current.length} chars).`);
          skipped++;
          continue;
        }
      }

      await updateProductBodyHtml(product.id.match(/(\d+)$/)[1], body_html);
      updated++;
      console.log(`INFO: Updated product ${product.id} (handle ${handle})`);

      // Update t_order_sheet_lines timestamps for this mps_id to track status
      if (!dryRun) {
        const nowIso = new Date().toISOString();
        const { error: updErr } = await supabase
          .from('t_order_sheet_lines')
          .update({ shopify_uploaded_at: nowIso })
          .eq('mps_id', mps_id)
          .not('shopify_uploaded_at', 'is', null)
          .lt('shopify_uploaded_at', cutoffIso);
        if (updErr) {
          console.warn(`WARN: Failed to update shopify_uploaded_at for mps_id ${mps_id}: ${updErr.message}`);
        } else {
          console.log(`INFO: Updated shopify_uploaded_at to now() for OSLs with mps_id ${mps_id}`);
        }
      }

      // Small delay to be gentle on API
      await delay(150);
    } catch (e) {
      console.error(`ERROR: Failed to update for mps_id ${mps_id}:`, e.message);
    }
  }

  console.log(`INFO: Completed. Updated=${updated}, Skipped=${skipped}, Total=${targets.length}${dryRun ? ' (dry run)' : ''}.`);
}

run().catch(err => {
  console.error('ERROR: Unexpected failure in updateOSLDescriptions:', err);
  process.exit(1);
});

