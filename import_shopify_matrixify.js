import { createClient } from '@supabase/supabase-js';
import <PERSON> from 'papaparse';
import fs from 'fs/promises';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: SUPABASE_URL or SUPABASE_KEY environment variables are not set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function importShopifyMatrixify() {
  console.log('⏳ Starting import of Shopify Matrixify export data...');

  // Define the path to your CSV file
  const filePath = path.resolve('C:\\Users\\<USER>\\supabase_project\\data\\external data\\shopify_matrixify_export_dg.csv');

  try {
    // Read the CSV file
    console.log(`📂 Reading CSV file from: ${filePath}`);
    const fileContent = await fs.readFile(filePath, 'utf-8');

    // Parse the CSV data
    console.log('🔄 Parsing CSV data...');
    const { data: parsedData, errors } = Papa.parse(fileContent, {
      header: true,
      skipEmptyLines: true,
      dynamicTyping: true,
      delimiter: ',',
    });

    if (errors.length > 0) {
      console.warn('⚠️ CSV Parsing Warnings:', errors.slice(0, 5)); // Show first 5 errors
    }

    console.log(`✅ CSV parsed successfully. Found ${parsedData.length} records.`);

    // Check if the table exists
    console.log('🔍 Checking if table exists...');
    let tableExists = true;
    try {
      const { count } = await supabase
        .from('imported_table_shopify_products_dz')
        .select('*', { count: 'exact', head: true });

      console.log(`✅ Table exists with ${count} records.`);
    } catch (error) {
      if (error.code === 'PGRST116') {
        tableExists = false;
        console.log('⚠️ Table does not exist. Will create it...');
      } else {
        console.error('❌ Error checking table:', error);
      }
    }

    // Create table if it doesn't exist
    if (!tableExists) {
      console.log('🔧 Creating table...');
      try {
        const { error: createTableError } = await supabase.rpc('exec_sql', {
          sql_statement: `
            CREATE TABLE IF NOT EXISTS public.imported_table_shopify_products_dz (
              "ID" bigint null,
              "Handle" text null,
              "Type" text null,
              "Status" text null,
              "URL" text null,
              "Total Inventory Qty" smallint null,
              "Variant Inventory Item ID" bigint null,
              "Variant ID" bigint null,
              "Option1 Name" text null,
              "Option1 Value" text null,
              "Option2 Name" text null,
              "Option2 Value" text null,
              "Option3 Name" text null,
              "Option3 Value" text null,
              "Variant SKU" text null,
              "Variant Weight" double precision null,
              "Variant Inventory Qty" smallint null,
              uuid uuid not null default gen_random_uuid (),
              constraint imported_table_shopify_products_dz_pkey primary key (uuid)
            );
          `
        });

        if (createTableError) {
          console.error('❌ Error creating table:', createTableError);
          throw new Error('Failed to create table');
        } else {
          console.log('✅ Table created successfully.');
        }
      } catch (error) {
        console.error('❌ Error creating table:', error);
        throw new Error('Failed to create table');
      }
    }

    // Create truncate function if needed
    console.log('🔧 Ensuring truncate function exists...');
    try {
      const { error: createFunctionError } = await supabase.rpc('exec_sql', {
        sql_statement: `
          -- Create a function to truncate the table
          CREATE OR REPLACE FUNCTION truncate_imported_table_shopify_products_dz()
          RETURNS void AS $$
          BEGIN
            TRUNCATE TABLE imported_table_shopify_products_dz;
          END;
          $$ LANGUAGE plpgsql;
        `
      });

      if (createFunctionError) {
        console.error('❌ Error creating truncate function:', createFunctionError);
        // Continue anyway, we'll try to delete records manually
      } else {
        console.log('✅ Truncate function created successfully.');
      }
    } catch (error) {
      console.error('❌ Error creating truncate function:', error);
      // Continue anyway, we'll try to delete records manually
    }

    // Clear the table before import using the delete strategy
    console.log('🗑️ Clearing existing data using delete strategy...');

    try {
      // First, check if there are any records
      const { count, error: countError } = await supabase
        .from('imported_table_shopify_products_dz')
        .select('*', { count: 'exact', head: true });

      if (countError) {
        console.error('❌ Error counting records:', countError);
        throw countError;
      }

      console.log(`📊 Found ${count} existing records in table`);

      if (count > 0) {
        if (count > 1) {
          // Step 1: Delete all but 1 record
          console.log('🗑️ Step 1: Deleting all but 1 record...');

          // Get the first record's uuid to keep
          const { data: firstRecord, error: firstRecordError } = await supabase
            .from('imported_table_shopify_products_dz')
            .select('uuid')
            .limit(1)
            .single();

          if (firstRecordError) {
            console.error('❌ Error getting first record:', firstRecordError);
            throw firstRecordError;
          }

          // Delete all records except the first one
          const { error: deleteAllButOneError } = await supabase
            .from('imported_table_shopify_products_dz')
            .delete()
            .neq('uuid', firstRecord.uuid);

          if (deleteAllButOneError) {
            console.error('❌ Error deleting all but one record:', deleteAllButOneError);
            throw deleteAllButOneError;
          }

          console.log('✅ Step 1 completed: Deleted all but 1 record');
        }

        // Step 2: Delete the last remaining record
        console.log('🗑️ Step 2: Deleting the last remaining record...');

        const { error: deleteLastError } = await supabase
          .from('imported_table_shopify_products_dz')
          .delete()
          .neq('uuid', '00000000-0000-0000-0000-000000000000'); // Delete all remaining records

        if (deleteLastError) {
          console.error('❌ Error deleting last record:', deleteLastError);
          throw deleteLastError;
        }

        console.log('✅ Step 2 completed: Deleted the last remaining record');
      }

      console.log('✅ Table cleared successfully using delete strategy');

    } catch (clearError) {
      console.error('❌ Error clearing table:', clearError);
      console.log('⚠️ Continuing with import anyway...');
    }

    // Transform the data to match the target table schema
    console.log('🔄 Transforming data to match target schema...');
    const transformedData = parsedData.map(record => {
      return {
        "ID": record.id || record.ID || record.product_id || null,
        "Handle": record.handle || record.Handle || null,
        "Type": record.product_type || record.Type || null,
        "Status": record.status || record.Status || null,
        "URL": record.url || record.URL || null,
        "Total Inventory Qty": record.total_inventory || record["Total Inventory Qty"] || null,
        "Variant Inventory Item ID": record.variant_inventory_item_id || record["Variant Inventory Item ID"] || null,
        "Variant ID": record.variant_id || record["Variant ID"] || null,
        "Option1 Name": record.option1_name || record["Option1 Name"] || null,
        "Option1 Value": record.option1_value || record["Option1 Value"] || null,
        "Option2 Name": record.option2_name || record["Option2 Name"] || null,
        "Option2 Value": record.option2_value || record["Option2 Value"] || null,
        "Option3 Name": record.option3_name || record["Option3 Name"] || null,
        "Option3 Value": record.option3_value || record["Option3 Value"] || null,
        "Variant SKU": record.variant_sku || record["Variant SKU"] || null,
        "Variant Weight": record.variant_weight || record["Variant Weight"] || null,
        "Variant Inventory Qty": record.variant_inventory_qty || record["Variant Inventory Qty"] || null
      };
    });

    // Insert data in chunks to avoid timeouts
    const chunkSize = 1000;
    console.log(`📤 Inserting data in chunks of ${chunkSize}...`);

    for (let i = 0; i < transformedData.length; i += chunkSize) {
      const chunk = transformedData.slice(i, i + chunkSize);
      console.log(`⏳ Inserting chunk ${Math.floor(i/chunkSize) + 1} of ${Math.ceil(transformedData.length/chunkSize)} (${chunk.length} records)...`);

      try {
        await supabase
          .from('imported_table_shopify_products_dz')
          .insert(chunk);

        console.log(`✅ Chunk ${Math.floor(i/chunkSize) + 1} inserted successfully.`);
      } catch (insertError) {
        console.error(`❌ Error inserting chunk ${Math.floor(i/chunkSize) + 1}:`, insertError);

        // If we get a constraint violation, try inserting records one by one
        if (insertError.code === '23505') {
          console.log(`⚠️ Constraint violation. Trying to insert records one by one...`);

          let successCount = 0;
          for (const record of chunk) {
            try {
              await supabase
                .from('imported_table_shopify_products_dz')
                .insert([record]);
              successCount++;
            } catch (singleInsertError) {
              console.error(`❌ Error inserting single record:`, singleInsertError);
            }
          }

          console.log(`✅ Inserted ${successCount} out of ${chunk.length} records individually.`);
        }
      }
    }

    console.log('✅ Import completed successfully!');

  } catch (error) {
    console.error('❌ Error during import process:', error);
    process.exit(1);
  }
}

// Run the import function
importShopifyMatrixify();
