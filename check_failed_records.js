import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkFailedRecords() {
    console.log('Checking failed parsing records...\n');
    
    try {
        // Check specific records 3317 and 3318
        const { data: specificRecords, error: specificError } = await supabase
            .from('it_dd_osl')
            .select('id, product_title, variant_title, product_vendor, parsed_mold, parsed_plastic, parsed_stamp')
            .in('id', [3317, 3318]);
        
        console.log('Records 3317 and 3318:');
        console.log('======================');
        specificRecords.forEach(record => {
            console.log(`ID ${record.id}: "${record.product_title}"`);
            console.log(`  Vendor: ${record.product_vendor}`);
            console.log(`  Variant: "${record.variant_title}"`);
            console.log(`  Parsed: Plastic="${record.parsed_plastic || 'NULL'}", Mold="${record.parsed_mold || 'NULL'}", Stamp="${record.parsed_stamp || 'NULL'}"`);
            console.log('---');
        });
        
        // Count records that failed parsing (no mold AND no plastic)
        const { count: failedCount, error: failedError } = await supabase
            .from('it_dd_osl')
            .select('*', { count: 'exact', head: true })
            .eq('product_product_type', 'Discs')
            .is('parsed_mold', null)
            .is('parsed_plastic', null);
        
        console.log(`\nRecords with no mold AND no plastic: ${failedCount}`);
        
        // Count records with DyeMax that have parsed data (should be cleared)
        const { count: dyemaxParsedCount, error: dyemaxError } = await supabase
            .from('it_dd_osl')
            .select('*', { count: 'exact', head: true })
            .eq('product_product_type', 'Discs')
            .or('product_title.ilike.%dyemax%,variant_title.ilike.%dyemax%')
            .not('parsed_mold', 'is', null);
        
        console.log(`Records with DyeMax that have parsed data (should be cleared): ${dyemaxParsedCount}`);
        
        // Get some examples of failed Active records
        const { data: failedActive, error: activeError } = await supabase
            .from('it_dd_osl')
            .select('id, product_title, variant_title, product_vendor, parsed_mold, parsed_plastic')
            .eq('product_product_type', 'Discs')
            .ilike('product_title', '%Active%')
            .is('parsed_mold', null)
            .limit(5);
        
        console.log('\nFailed Active records:');
        console.log('=====================');
        failedActive.forEach(record => {
            console.log(`ID ${record.id}: "${record.product_title}" [${record.product_vendor}]`);
        });
        
        // Get some examples of Classic records to understand the pattern
        const { data: classicRecords, error: classicError } = await supabase
            .from('it_dd_osl')
            .select('id, product_title, variant_title, product_vendor')
            .eq('product_product_type', 'Discs')
            .ilike('product_title', '%Classic%Deputy%')
            .limit(5);
        
        console.log('\nClassic Deputy records:');
        console.log('======================');
        classicRecords.forEach(record => {
            console.log(`ID ${record.id}: "${record.product_title}" [${record.product_vendor}]`);
            console.log(`  Variant: "${record.variant_title}"`);
            console.log('---');
        });
        
    } catch (error) {
        console.error('Query failed:', error);
    }
}

// Run the check
checkFailedRecords();
