// testCatchupSample.js - Test the updated catch-up script on a small sample
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

/**
 * Check a single RPRO record for readiness issues (same logic as catch-up script)
 */
function checkRproReadiness(record) {
  const issues = [];
  const quantity = parseFloat(record.ivqtylaw) || 0;
  const isInStock = quantity > 0;

  // Issue 1: In stock items (ivqtylaw > 0) must have a bin section (ivaux3 not null)
  const binSection = record.ivaux3;
  if (isInStock && (!binSection || binSection.trim() === '')) {
    issues.push(`In stock item (qty: ${quantity}) missing bin section (ivaux3)`);
  }

  // Issue 2: Image checks
  const imageField = record.ivaux2;
  const expectedImage = '9 White Square Big';
  
  if (isInStock) {
    // In stock products should have ivaux2 = '9 White Square Big'
    if (imageField !== expectedImage) {
      issues.push('Kenneth - In stock item needs image.');
    }
  } else {
    // Out of stock products should also have image but different message
    if (imageField !== expectedImage) {
      issues.push('No image but out of stock.');
    }
  }

  // Issue 3: Pricing validation rules
  const listPrice = parseFloat(record.ivprcbtlis) || 0;
  const regularPrice = parseFloat(record.ivprcbt_dollar) || 0;
  const salePrice = parseFloat(record.ivprcbtsal) || 0;
  const livePrice = parseFloat(record.ivprcbtliv) || 0;
  const wholesale1Price = parseFloat(record.ivprcws_1) || 0;
  const wholesale2Price = parseFloat(record.ivprcws_2) || 0;
  const carryingCost = parseFloat(record.ivavgcd) || 0;
  const msrp = parseFloat(record.ivprcmsrp) || 0;
  const map = parseFloat(record.ivprcmap) || 0;

  const priceIssues = [];

  // If List Price is not 0, then it must be higher than or equal to the Regular Price
  if (listPrice !== 0 && listPrice < regularPrice) {
    priceIssues.push('List Price must be >= Regular Price');
  }

  // Regular Price must not be 0
  if (regularPrice === 0) {
    priceIssues.push('Regular Price cannot be 0');
  }

  // If Sale Price is not 0, then it must be less than Regular Price
  if (salePrice !== 0 && salePrice >= regularPrice) {
    priceIssues.push('Sale Price must be < Regular Price');
  }

  // Live Price can not be 0
  if (livePrice === 0) {
    priceIssues.push('Live Price cannot be 0');
  }

  // Live Price must be greater than Wholesale 1 Price
  if (livePrice <= wholesale1Price) {
    priceIssues.push('Live Price must be > Wholesale 1 Price');
  }

  // If Sale Price is 0, then Live Price must = Regular Price
  if (salePrice === 0 && livePrice !== regularPrice) {
    priceIssues.push('When Sale Price is 0, Live Price must = Regular Price');
  }

  // Wholesale 2 Price must be greater than Carrying Cost
  if (wholesale2Price <= carryingCost) {
    priceIssues.push('Wholesale 2 Price must be > Carrying Cost');
  }

  // Wholesale 2 Price must be .9* Wholesale 1 Price
  const expectedWholesale2 = wholesale1Price * 0.9;
  if (Math.abs(wholesale2Price - expectedWholesale2) > 0.01) { // Allow small floating point differences
    priceIssues.push('Wholesale 2 Price must be 90% of Wholesale 1 Price');
  }

  // If MSRP is not 0, then MAP must be <= to MSRP
  if (msrp !== 0 && map > msrp) {
    priceIssues.push('MAP must be <= MSRP');
  }

  // If MAP is not 0, then Live Price must not be < MAP
  if (map !== 0 && livePrice < map) {
    priceIssues.push('Live Price must not be < MAP');
  }

  // Add pricing issues to main issues list with appropriate prefix
  priceIssues.forEach(priceIssue => {
    if (isInStock) {
      issues.push(`Kenneth - ${priceIssue}`);
    } else {
      issues.push(priceIssue);
    }
  });

  return {
    issues,
    ready: issues.length === 0
  };
}

async function testSample() {
  try {
    console.log('🧪 Testing updated validation rules on sample data...');
    console.log('===================================================');

    // Get a sample of records
    const { data: sampleRecords, error: sampleError } = await supabase
      .from('imported_table_rpro')
      .select(`
        id, ivno, ivqtylaw, ivaux3, ivaux2,
        ivprcbtlis, ivprcbt_dollar, ivprcbtsal, ivprcbtliv,
        ivprcws_1, ivprcws_2, ivavgcd, ivprcmsrp, ivprcmap
      `)
      .limit(50);

    if (sampleError) {
      console.error('❌ Error fetching sample records:', sampleError.message);
      return;
    }

    console.log(`📊 Analyzing ${sampleRecords.length} sample records...`);

    let totalReady = 0;
    let totalNotReady = 0;
    let issueTypes = {};

    sampleRecords.forEach(record => {
      const check = checkRproReadiness(record);
      
      if (check.ready) {
        totalReady++;
      } else {
        totalNotReady++;
        
        // Count issue types
        check.issues.forEach(issue => {
          if (!issueTypes[issue]) {
            issueTypes[issue] = 0;
          }
          issueTypes[issue]++;
        });
      }
    });

    console.log('\n📊 Sample Analysis Results:');
    console.log('===========================');
    console.log(`✅ Ready records: ${totalReady}`);
    console.log(`⚠️  Not ready records: ${totalNotReady}`);

    if (totalNotReady > 0) {
      console.log('\n🔍 Issue Types Found:');
      console.log('=====================');
      Object.entries(issueTypes)
        .sort((a, b) => b[1] - a[1]) // Sort by frequency
        .forEach(([issue, count]) => {
          console.log(`  ${count.toString().padStart(3)} - ${issue}`);
        });

      console.log('\n📋 Sample records with issues:');
      console.log('==============================');
      let shownCount = 0;
      sampleRecords.forEach(record => {
        const check = checkRproReadiness(record);
        if (!check.ready && shownCount < 10) {
          console.log(`  IVNO: ${record.ivno}, Qty: ${record.ivqtylaw}, Image: ${record.ivaux2 || 'null'}`);
          console.log(`    Issues: ${check.issues.join('; ')}`);
          console.log('');
          shownCount++;
        }
      });
    }

    console.log('\n🎉 Sample analysis completed!');

  } catch (err) {
    console.error('❌ Exception:', err.message);
    console.error(err.stack);
  }
}

testSample();
