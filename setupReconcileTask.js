// setupReconcileTask.js - <PERSON>ript to set up automatic reconciliation

import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the absolute path to the reconcile script
const scriptPath = path.resolve('./refreshReconcileData.js');

// Create a batch file for the scheduled task
const createBatchFile = () => {
  const batchContent = `@echo off
echo Starting RPRO to Veeqo reconciliation at %date% %time%
cd "${process.cwd()}"
node "${scriptPath}"
echo Reconciliation completed at %date% %time%
`;

  const batchPath = path.join(process.cwd(), 'runReconcile.bat');
  fs.writeFileSync(batchPath, batchContent);
  console.log(`Created batch file at: ${batchPath}`);
  return batchPath;
};

// Create a PM2 scheduled task
const createPm2ScheduledTask = () => {
  // Check if PM2 is installed
  exec('pm2 --version', (error) => {
    if (error) {
      console.error('PM2 is not installed. Please install it with: npm install -g pm2');
      return;
    }

    // Create the PM2 command
    // Schedule to run at 6:30 AM (after the RPRO import at 6:15 AM)
    const pm2Command = `pm2 start "${scriptPath}" --name "rpro-veeqo-reconcile" --cron "30 6 * * *" -- `;

    console.log(`Setting up PM2 scheduled task...`);
    console.log(`Command: ${pm2Command}`);

    // Execute the PM2 command
    exec(pm2Command, (err, stdout) => {
      if (err) {
        console.error(`Error starting PM2 process: ${err.message}`);
        return;
      }

      console.log('PM2 scheduled task created successfully!');
      console.log(stdout);

      // Save the PM2 configuration
      exec('pm2 save', (saveErr, saveStdout) => {
        if (saveErr) {
          console.error(`Error saving PM2 configuration: ${saveErr.message}`);
          return;
        }

        console.log('PM2 configuration saved:');
        console.log(saveStdout);

        // Display the PM2 list
        exec('pm2 list', (listErr, listStdout) => {
          if (!listErr) {
            console.log('PM2 process list:');
            console.log(listStdout);
          }
        });
      });
    });
  });
};

// Main function
const setupReconcileTask = () => {
  console.log('Setting up RPRO to Veeqo reconciliation task...');

  // Create the batch file
  const batchPath = createBatchFile();

  // Create the PM2 scheduled task
  createPm2ScheduledTask();

  console.log('\nYou can also run the reconciliation manually:');
  console.log(`node ${scriptPath}`);
};

// Run the setup
setupReconcileTask();
