// checkTriggers.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Create a Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Checking triggers on t_images table...');
    
    // First, create the execute_sql function if it doesn't exist
    const createFunctionSql = `
      CREATE OR REPLACE FUNCTION execute_sql(sql TEXT)
      RETURNS JSONB
      LANGUAGE plpgsql
      AS $$
      DECLARE
        v_result JSONB;
        v_record RECORD;
      BEGIN
        EXECUTE sql INTO v_record;
        v_result := to_jsonb(v_record);
        RETURN v_result;
      EXCEPTION WHEN OTHERS THEN
        RETURN jsonb_build_object(
          'error', SQLERRM,
          'detail', SQLSTATE,
          'sql', sql
        );
      END;
      $$;
    `;
    
    try {
      await supabase.rpc('execute_sql', { sql: createFunctionSql });
      console.log('execute_sql function created or updated');
    } catch (err) {
      console.error(`Error creating execute_sql function: ${err.message}`);
    }
    
    // Now, get the triggers on t_images
    const { data, error } = await supabase.rpc('execute_sql', {
      sql: `
        SELECT 
          trigger_name,
          event_manipulation,
          action_statement,
          action_timing
        FROM 
          information_schema.triggers
        WHERE 
          event_object_table = 't_images'
          AND event_object_schema = 'public'
      `
    });
    
    if (error) {
      console.error(`Error getting triggers: ${error.message}`);
      return;
    }
    
    if (!data || Object.keys(data).length === 0) {
      console.log('No triggers found on t_images table');
      return;
    }
    
    console.log('Triggers on t_images table:');
    console.log(JSON.stringify(data, null, 2));
    
    // Also check for functions related to t_images
    const { data: funcData, error: funcError } = await supabase.rpc('execute_sql', {
      sql: `
        SELECT 
          routine_name,
          routine_definition
        FROM 
          information_schema.routines
        WHERE 
          routine_schema = 'public'
          AND routine_definition LIKE '%t_images%'
          AND routine_type = 'FUNCTION'
      `
    });
    
    if (funcError) {
      console.error(`Error getting functions: ${funcError.message}`);
      return;
    }
    
    if (!funcData || Object.keys(funcData).length === 0) {
      console.log('No functions found related to t_images');
      return;
    }
    
    console.log('Functions related to t_images:');
    console.log(JSON.stringify(funcData, null, 2));
  } catch (err) {
    console.error(`Unexpected error: ${err.message}`);
    process.exit(1);
  }
}

main();
