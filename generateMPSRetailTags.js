import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import fs from 'fs';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

// Label dimensions in points (1 mm = 2.835 points)
const LABEL_WIDTH = 67 * 2.835;  // ~190.5 points
const LABEL_HEIGHT = 25 * 2.835; // ~70.87 points

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or key in environment variables.');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

// A simple text wrapper that splits a string into lines with a maximum number of characters.
function wrapText(text, maxChars) {
  const words = text.split(" ");
  const lines = [];
  let currentLine = "";
  for (const word of words) {
    const testLine = currentLine ? currentLine + " " + word : word;
    if (testLine.length <= maxChars) {
      currentLine = testLine;
    } else {
      if (currentLine) lines.push(currentLine);
      currentLine = word;
    }
  }
  if (currentLine) lines.push(currentLine);
  return lines;
}

async function generatePDF() {
  console.log('🔍 Querying Supabase for t_mps records...');
  
  // Query t_mps where new_release_retail_tag_printed_at is NULL
  const { data: mpsTags, error } = await supabase
    .from('t_mps')
    .select(`
      id, plastic_id, mold_id, stamp_id, val_override_retail_price,
      t_plastics!inner(brand_id, plastic, val_retail_price, t_brands!inner(brand)),
      t_molds!inner(mold, speed, glide, turn, fade),
      t_stamps!inner(stamp)
    `)
    .is('new_release_retail_tag_printed_at', null);

  if (error) {
    console.error('❌ Error querying t_mps:', error.message);
    process.exit(1);
  }
  if (!mpsTags || mpsTags.length === 0) {
    console.log('ℹ️ No records found in t_mps that need labels.');
    process.exit(0);
  }

  console.log(`✅ Retrieved ${mpsTags.length} MPS records.`);

  const pdfDoc = await PDFDocument.create();
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
  const italicFont = await pdfDoc.embedFont(StandardFonts.HelveticaOblique);

  // Font sizes and spacing
  const row1Size = 9;  // Title (Brand - Plastic - Mold - Stamp)
  const row2Size = 10; // Flight Numbers
  const row3Size = 12; // Larger Price (Bold + Italic)
  const spacing = 2;   // vertical spacing between rows

  mpsTags.forEach((tag) => {
    const page = pdfDoc.addPage([LABEL_WIDTH, LABEL_HEIGHT]);

    // --- ROW 1: Title (Brand - Plastic - Mold - Stamp) ---
    const title = `${tag.t_plastics.t_brands.brand} - ${tag.t_plastics.plastic} - ${tag.t_molds.mold} - ${tag.t_stamps.stamp}`;
    let row1Lines = wrapText(title, 40);

    // --- ROW 2: Flight Numbers (Speed, Glide, Turn, Fade) ---
    const flightNumbers = `Speed ${tag.t_molds.speed} | Glide ${tag.t_molds.glide} | Turn ${tag.t_molds.turn} | Fade ${tag.t_molds.fade}`;
    const row2Width = boldFont.widthOfTextAtSize(flightNumbers, row2Size);

    // --- ROW 3: Price (Bold + Italic) ---
    // Use t_mps.val_override_retail_price if available, otherwise fall back to t_plastics.val_retail_price
    const priceValue = tag.val_override_retail_price ?? tag.t_plastics.val_retail_price;
    const price = priceValue ? `$${priceValue.toFixed(2)}` : 'N/A';
    const row3Width = boldFont.widthOfTextAtSize(price, row3Size);

    // --- Calculate total block height ---
    const row1Height = row1Lines.length * row1Size + (row1Lines.length - 1) * spacing;
    const totalHeight = row1Height + spacing + row2Size + spacing + row3Size;

    // Start Y so the block is vertically centered, then shift down ~3 points
    const startY = (LABEL_HEIGHT + totalHeight) / 2 - 3;

    // --- Draw Row 1 (wrapped text, centered) ---
    let row1Y = startY;
    row1Lines.forEach((line) => {
      const lineWidth = font.widthOfTextAtSize(line, row1Size);
      const lineX = (LABEL_WIDTH - lineWidth) / 2;
      page.drawText(line, {
        x: lineX,
        y: row1Y,
        size: row1Size,
        font,
        color: rgb(0, 0, 0),
      });
      row1Y -= (row1Size + spacing);
    });

    // --- Draw Row 2 (Flight Numbers, centered) ---
    const row2Y = row1Y - spacing;
    const row2X = (LABEL_WIDTH - row2Width) / 2;
    page.drawText(flightNumbers, {
      x: row2X,
      y: row2Y,
      size: row2Size,
      font: boldFont,
      color: rgb(0, 0, 0),
    });

    // --- Draw Row 3 (Larger Bold Italic Price, centered at bottom) ---
    const row3Y = row2Y - row2Size - spacing;
    const row3X = (LABEL_WIDTH - row3Width) / 2;
    page.drawText(price, {
      x: row3X,
      y: row3Y,
      size: row3Size,
      font: boldFont,
      color: rgb(0, 0, 0),
    });
  });

  // Save PDF with a date-stamped filename
  const pdfBytes = await pdfDoc.save();
  const now = new Date();
  const dateStamp = now.toISOString().replace(/:/g, "-").slice(0, 19);
  const fileName = `mps_labels_${dateStamp}.pdf`;

  fs.writeFileSync(fileName, pdfBytes);
  console.log(`✅ MPS labels generated: ${fileName}`);

  // --- Step 2: Update new_release_retail_tag_printed_at in Supabase ---
  const mpsIds = mpsTags.map(d => d.id);
  if (mpsIds.length > 0) {
    console.log(`🔄 Updating new_release_retail_tag_printed_at for ${mpsIds.length} records...`);

    const { error: updateError } = await supabase
      .from('t_mps')
      .update({ new_release_retail_tag_printed_at: new Date().toISOString() })
      .in('id', mpsIds);

    if (updateError) {
      console.error(`❌ Failed to update new_release_retail_tag_printed_at: ${updateError.message}`);
    } else {
      console.log(`✅ Successfully updated new_release_retail_tag_printed_at for ${mpsIds.length} records.`);
    }
  }
}

generatePDF();
