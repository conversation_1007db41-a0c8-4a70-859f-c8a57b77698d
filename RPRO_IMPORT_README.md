# Importing RPRO DBF Data into Supabase

This guide explains how to import your RPRO DBF file (invdb.dbf) into Supabase with the table name `imported_table_rpro`.

## Step 1: Create the Table in Supabase

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy and paste the contents of `create_rpro_table.sql`
4. Run the SQL to create the table

## Step 2: Import the Data

You can use the `setupDbfImport.js` script to import the data:

```
node setupDbfImport.js --file=R:\Rpro\BRIDGE\invdb.dbf --table=imported_table_rpro --import-only
```

## Step 3: Set Up Daily Imports

To set up daily imports, use the `setupDailyImport.js` script:

```
node setupDailyImport.js
```

This will guide you through setting up a scheduled task to run the import daily at 6:15 AM (giving a buffer after the file is generated at 6:03 AM).

When prompted, specify the table name as `imported_table_rpro`.

## Manual Import

If you need to manually import data:

```
node testDbf.js R:\Rpro\BRIDGE\invdb.dbf
```

This will show you the structure of the DBF file and sample data.

Then, to import the data:

```
node importDbfToSupabase.js R:\Rpro\BRIDGE\invdb.dbf imported_table_rpro true
```

## Table Structure

The table I've created (`imported_table_rpro`) includes:

- All fields from your DBF file with appropriate data types
- A primary key `id` column
- `imported_at` timestamp column to track when records were imported
- `import_batch_id` UUID column to group records from the same import batch
- Appropriate indexes for common query patterns (ivno, ivupc, imported_at, import_batch_id)
- Row-level security policies for authenticated users

## Troubleshooting

If you encounter any issues:

1. Check the log files (`dbf_test.log` or `dbf_analysis.log`)
2. Make sure the DBF file exists at the specified path
3. Verify your Supabase credentials in the `.env` file
4. Check that the table was created successfully in Supabase
