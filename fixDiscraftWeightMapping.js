// This file demonstrates how to fix the Discraft weight mapping issue
// by dynamically reading weight headers instead of using hardcoded mappings

import XLSX from 'xlsx';

const inputFile = 'data/external data/discraftstock.xlsx';

// Function to parse weight range from header text
function parseWeightHeader(headerText) {
    if (!headerText) return null;
    
    const text = headerText.toString().trim();
    
    // Handle single weight like "150" or "150g"
    const singleMatch = text.match(/^(\d+)g?$/);
    if (singleMatch) {
        const weight = parseInt(singleMatch[1]);
        return { min: weight, max: weight, name: `${weight}g` };
    }
    
    // Handle range like "141-150", "151-159", "160-166"
    const rangeMatch = text.match(/^(\d+)-(\d+)g?$/);
    if (rangeMatch) {
        const min = parseInt(rangeMatch[1]);
        const max = parseInt(rangeMatch[2]);
        return { min: min, max: max, name: `${min}-${max}g` };
    }
    
    // Handle "177+" format
    const plusMatch = text.match(/^(\d+)\+$/);
    if (plusMatch) {
        const min = parseInt(plusMatch[1]);
        return { min: min, max: 180, name: `${min}+g` }; // Assume max 180g
    }
    
    return null;
}

// Function to find weight headers in a row
function findWeightHeaders(row, startCol = 11, endCol = 17) {
    const weightHeaders = {};
    
    for (let col = startCol; col <= endCol; col++) {
        const headerText = row[col];
        const weightInfo = parseWeightHeader(headerText);
        
        if (weightInfo) {
            const columnLetter = String.fromCharCode(65 + col); // Convert to A, B, C, etc.
            weightHeaders[col] = {
                ...weightInfo,
                letter: columnLetter,
                columnIndex: col
            };
        }
    }
    
    return weightHeaders;
}

try {
    console.log('Analyzing Discraft weight header patterns...\n');
    
    const workbook = XLSX.readFile(inputFile);
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    // Find all header rows (rows with "Line" and "Model")
    const headerRows = [];
    for (let i = 0; i < data.length; i++) {
        const row = data[i];
        if (row && row[1] === 'Line' && row[4] === 'Model') {
            headerRows.push({ rowIndex: i, row: row });
        }
    }
    
    console.log(`Found ${headerRows.length} header rows:`);
    
    headerRows.forEach((headerInfo, index) => {
        const { rowIndex, row } = headerInfo;
        console.log(`\n--- Header Row ${index + 1} (Excel row ${rowIndex + 1}) ---`);
        
        // Find weight headers in this row
        const weightHeaders = findWeightHeaders(row);
        
        console.log('Weight columns found:');
        Object.entries(weightHeaders).forEach(([colIndex, weightInfo]) => {
            console.log(`  Column ${weightInfo.letter} (${colIndex}): ${weightInfo.name} (${weightInfo.min}-${weightInfo.max}g)`);
        });
        
        // Show a few data rows following this header
        console.log('\nSample data rows:');
        for (let i = 1; i <= 3; i++) {
            const dataRowIndex = rowIndex + i;
            if (dataRowIndex < data.length && data[dataRowIndex]) {
                const dataRow = data[dataRowIndex];
                const line = dataRow[1] || '';
                const model = dataRow[4] || '';
                
                if (line && model) {
                    console.log(`  Row ${dataRowIndex + 1}: ${line} ${model}`);
                    
                    // Show weight column values
                    Object.entries(weightHeaders).forEach(([colIndex, weightInfo]) => {
                        const value = dataRow[colIndex];
                        if (value !== undefined && value !== null && value !== '') {
                            console.log(`    ${weightInfo.letter}: "${value}"`);
                        }
                    });
                }
            }
        }
    });
    
    console.log('\n--- Recommended Fix ---');
    console.log('The import logic should:');
    console.log('1. Scan for header rows (Line + Model columns)');
    console.log('2. For each header row, dynamically read the weight columns');
    console.log('3. Parse the weight ranges from the actual header text');
    console.log('4. Use those parsed ranges for the data rows that follow');
    console.log('5. Continue until the next header row or end of section');
    
} catch (err) {
    console.error('Error:', err.message);
}
