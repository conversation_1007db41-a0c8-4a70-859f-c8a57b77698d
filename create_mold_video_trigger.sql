-- Trigger to enqueue tasks when t_molds.video_url changes
-- - If video_url goes from NULL to NOT NULL, or changes to a new non-null value → enqueue mold_video_updated
-- - If video_url goes from NOT NULL to NULL → enqueue mold_video_removed

CREATE OR REPLACE FUNCTION fn_enqueue_mold_video_change()
RETURNS TRIGGER AS $$
BEGIN
  -- video_url removed
  IF (OLD.video_url IS NOT NULL AND NEW.video_url IS NULL) THEN
    INSERT INTO t_task_queue (
      task_type,
      payload,
      status,
      scheduled_at,
      created_at,
      enqueued_by
    ) VALUES (
      'mold_video_removed',
      jsonb_build_object('id', NEW.id),
      'pending',
      NOW(),
      NOW(),
      'trigger_mold_video_change'
    );

  -- video_url added or changed to a new non-null value
  ELSIF (
    (OLD.video_url IS NULL AND NEW.video_url IS NOT NULL) OR
    (OLD.video_url IS DISTINCT FROM NEW.video_url AND NEW.video_url IS NOT NULL)
  ) THEN
    INSERT INTO t_task_queue (
      task_type,
      payload,
      status,
      scheduled_at,
      created_at,
      enqueued_by
    ) VALUES (
      'mold_video_updated',
      jsonb_build_object('id', NEW.id, 'video_url', NEW.video_url),
      'pending',
      NOW(),
      NOW(),
      'trigger_mold_video_change'
    );
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS tr_enqueue_mold_video_change ON t_molds;
CREATE TRIGGER tr_enqueue_mold_video_change
AFTER UPDATE OF video_url ON t_molds
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_mold_video_change();

