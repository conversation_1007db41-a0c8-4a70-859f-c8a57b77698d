-- Create triggers on t_sales_orders to create movements when orders are Complete
-- Requirements:
-- 1) After UPDATE of status: when changed to 'Complete'
-- 2) After INSERT: when status is already 'Complete' AND created_by <> 'Migration'

-- Drop any previous trigger variants
DROP TRIGGER IF EXISTS trg_create_movements_for_completed_sale ON public.t_sales_orders;
DROP TRIGGER IF EXISTS trg_create_movements_for_completed_sale_upd ON public.t_sales_orders;
DROP TRIGGER IF EXISTS trg_create_movements_for_completed_sale_ins ON public.t_sales_orders;

-- After UPDATE case
CREATE TRIGGER trg_create_movements_for_completed_sale_upd
AFTER UPDATE OF status ON public.t_sales_orders
FOR EACH ROW
WHEN (NEW.status = 'Complete'::text AND NEW.status IS DISTINCT FROM OLD.status)
EXECUTE FUNCTION public.fn_create_movements_for_completed_sale();

-- After INSERT case (exclude Migration)
CREATE TRIGGER trg_create_movements_for_completed_sale_ins
AFTER INSERT ON public.t_sales_orders
FOR EACH ROW
WHEN (NEW.status = 'Complete'::text AND NEW.created_by IS DISTINCT FROM 'Migration'::text)
EXECUTE FUNCTION public.fn_create_movements_for_completed_sale();

DO $$ BEGIN RAISE NOTICE 'Created triggers trg_create_movements_for_completed_sale_upd and _ins on t_sales_orders.'; END $$;

