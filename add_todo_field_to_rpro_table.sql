-- Add todo field to imported_table_rpro table
-- This field will store readiness check results for RPRO records

-- Add the todo column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'imported_table_rpro' 
        AND column_name = 'todo'
    ) THEN
        ALTER TABLE public.imported_table_rpro 
        ADD COLUMN todo TEXT;
        
        -- Add a comment to the column
        COMMENT ON COLUMN public.imported_table_rpro.todo IS 'Readiness check results and todo items for RPRO records';
        
        RAISE NOTICE 'Added todo column to imported_table_rpro table';
    ELSE
        RAISE NOTICE 'todo column already exists in imported_table_rpro table';
    END IF;
END $$;

-- Create an index on the todo field for better query performance
CREATE INDEX IF NOT EXISTS idx_imported_table_rpro_todo 
ON public.imported_table_rpro(todo);

-- Grant necessary permissions
GRANT SELECT, UPDATE ON public.imported_table_rpro TO authenticated;
