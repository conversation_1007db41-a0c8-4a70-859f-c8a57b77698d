// create_db_functions.js
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function createDatabaseFunctions() {
  try {
    console.log('Creating database functions...');
    
    // Create get_pending_tasks_by_type function
    const pendingTasksQuery = `
      CREATE OR REPLACE FUNCTION get_pending_tasks_by_type()
      RETURNS TABLE(task_type text, count bigint) 
      LANGUAGE sql
      AS $$
        SELECT task_type, COUNT(*) as count
        FROM t_task_queue
        WHERE status = 'pending'
        AND scheduled_at <= NOW()
        GROUP BY task_type
        ORDER BY count DESC;
      $$;
    `;
    
    // Create get_future_tasks_by_type function
    const futureTasksQuery = `
      CREATE OR REPLACE FUNCTION get_future_tasks_by_type()
      RETURNS TABLE(task_type text, count bigint) 
      LANGUAGE sql
      AS $$
        SELECT task_type, COUNT(*) as count
        FROM t_task_queue
        WHERE status = 'pending'
        AND scheduled_at > NOW()
        GROUP BY task_type
        ORDER BY count DESC;
      $$;
    `;
    
    // Create get_completed_tasks_by_type function
    const completedTasksQuery = `
      CREATE OR REPLACE FUNCTION get_completed_tasks_by_type()
      RETURNS TABLE(task_type text, count bigint) 
      LANGUAGE sql
      AS $$
        SELECT task_type, COUNT(*) as count
        FROM t_task_queue
        WHERE status = 'complete'
        AND completed_at > NOW() - INTERVAL '30 minutes'
        GROUP BY task_type
        ORDER BY count DESC;
      $$;
    `;
    
    // Create get_error_tasks_by_type function
    const errorTasksQuery = `
      CREATE OR REPLACE FUNCTION get_error_tasks_by_type()
      RETURNS TABLE(task_type text, count bigint) 
      LANGUAGE sql
      AS $$
        SELECT task_type, COUNT(*) as count
        FROM t_task_queue
        WHERE status = 'error'
        AND completed_at > NOW() - INTERVAL '30 minutes'
        GROUP BY task_type
        ORDER BY count DESC;
      $$;
    `;
    
    // Execute the queries
    const { error: pendingError } = await supabase.rpc('exec_sql', { sql: pendingTasksQuery });
    if (pendingError) {
      console.error('Error creating get_pending_tasks_by_type function:', pendingError);
    } else {
      console.log('get_pending_tasks_by_type function created successfully');
    }
    
    const { error: futureError } = await supabase.rpc('exec_sql', { sql: futureTasksQuery });
    if (futureError) {
      console.error('Error creating get_future_tasks_by_type function:', futureError);
    } else {
      console.log('get_future_tasks_by_type function created successfully');
    }
    
    const { error: completedError } = await supabase.rpc('exec_sql', { sql: completedTasksQuery });
    if (completedError) {
      console.error('Error creating get_completed_tasks_by_type function:', completedError);
    } else {
      console.log('get_completed_tasks_by_type function created successfully');
    }
    
    const { error: errorError } = await supabase.rpc('exec_sql', { sql: errorTasksQuery });
    if (errorError) {
      console.error('Error creating get_error_tasks_by_type function:', errorError);
    } else {
      console.log('get_error_tasks_by_type function created successfully');
    }
    
    console.log('Database functions creation completed');
  } catch (err) {
    console.error('Exception creating database functions:', err);
  }
}

createDatabaseFunctions();
