const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!supabaseUrl || !supabaseKey || !veeqoApiKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

console.log('🗑️  VEEQO LISTING DELETION TOOL');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqo<PERSON><PERSON>Key
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    // For DELETE requests, there might not be a JSON response
    if (method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to get SDASIN record by ID
async function getSdasinRecord(sdasinId) {
  console.log(`🔍 Getting SDASIN record for ID: ${sdasinId}`);
  
  const { data, error } = await supabase
    .from('t_sdasins')
    .select('id, fbm_sku, fbm_uploaded_at, asin, parent_asin, veeqo_id')
    .eq('id', sdasinId)
    .single();
  
  if (error) {
    console.error(`❌ Error fetching SDASIN record: ${error.message}`);
    return null;
  }
  
  return data;
}

// Function to find all SDASINS with deleted Amazon listings
async function findDeletedAmazonListings() {
  console.log('🔍 Finding SDASINS with deleted Amazon listings (fbm_uploaded_at = null)...');
  
  const { data, error } = await supabase
    .from('t_sdasins')
    .select('id, fbm_sku, fbm_uploaded_at, asin, parent_asin, veeqo_id')
    .is('fbm_uploaded_at', null)
    .not('fbm_sku', 'is', null)
    .limit(50); // Limit for safety
  
  if (error) {
    console.error(`❌ Error fetching deleted Amazon listings: ${error.message}`);
    return [];
  }
  
  console.log(`📊 Found ${data.length} SDASINS with deleted Amazon listings`);
  return data;
}

// Function to check if Veeqo product exists and get its structure
async function analyzeVeeqoProduct(sku) {
  console.log(`\n🔍 Analyzing Veeqo product for SKU: ${sku}`);
  
  // First check the imported table
  const { data: importedData, error: importedError } = await supabase
    .from('imported_table_veeqo_sellables_export')
    .select('product_id, sku_code, product_title')
    .eq('sku_code', sku);
  
  if (importedError) {
    console.error(`❌ Error querying imported Veeqo table: ${importedError.message}`);
    return { exists: false, error: importedError.message };
  }
  
  if (!importedData || importedData.length === 0) {
    console.log(`ℹ️  SKU ${sku} not found in imported Veeqo table`);
    return { exists: false, reason: 'Not found in imported table' };
  }
  
  const productId = importedData[0].product_id;
  console.log(`✅ Found in imported table: Product ID ${productId}`);
  
  // Try to get the product from Veeqo API
  const productResult = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!productResult.success) {
    if (productResult.status === 404) {
      console.log(`⚠️  Product ${productId} exists in imported table but not in Veeqo API (404)`);
      return { 
        exists: false, 
        reason: 'Product deleted from Veeqo but still in imported table',
        productId,
        importedData: importedData[0]
      };
    } else {
      console.error(`❌ Error getting product from API: ${productResult.error}`);
      return { exists: false, error: productResult.error, productId };
    }
  }
  
  const product = productResult.data;
  console.log(`✅ Product exists in Veeqo API: "${product.title}"`);
  
  // Analyze channel products (listings)
  const channelProducts = product.channel_products || [];
  console.log(`📺 Found ${channelProducts.length} channel product(s)/listing(s):`);
  
  channelProducts.forEach((cp, index) => {
    console.log(`   ${index + 1}. Channel Product ID: ${cp.id}`);
    console.log(`      Channel: ${cp.channel?.name || 'Unknown'} (${cp.channel?.short_name || 'N/A'})`);
    console.log(`      Status: ${cp.status}`);
    console.log(`      Remote ID: ${cp.remote_id}`);
  });
  
  return {
    exists: true,
    product,
    productId,
    channelProducts,
    importedData: importedData[0]
  };
}

// Function to delete Veeqo listings (channel products)
async function deleteVeeqoListings(productAnalysis, sku) {
  console.log(`\n🗑️  Attempting to delete Veeqo listings for SKU: ${sku}`);
  
  if (!productAnalysis.exists) {
    if (productAnalysis.reason === 'Product deleted from Veeqo but still in imported table') {
      console.log(`ℹ️  Product already deleted from Veeqo, but still exists in imported table`);
      console.log(`💡 Consider cleaning up the imported table record for product ID: ${productAnalysis.productId}`);
      return { success: true, reason: 'Already deleted from Veeqo' };
    } else {
      console.log(`ℹ️  No Veeqo product found to delete`);
      return { success: true, reason: 'No product found' };
    }
  }
  
  const { channelProducts, productId } = productAnalysis;
  
  if (channelProducts.length === 0) {
    console.log(`ℹ️  Product ${productId} has no channel products/listings to delete`);
    return { success: true, reason: 'No channel products found' };
  }
  
  console.log(`🎯 Found ${channelProducts.length} channel product(s) to delete`);
  
  let deletedCount = 0;
  let errors = [];
  
  // Try to delete each channel product (listing)
  for (const channelProduct of channelProducts) {
    console.log(`🗑️  Deleting channel product ${channelProduct.id} from ${channelProduct.channel?.short_name || 'Unknown'}...`);
    
    // Try different possible deletion endpoints
    const deleteEndpoints = [
      `https://api.veeqo.com/channel_products/${channelProduct.id}`,
      `https://api.veeqo.com/products/${productId}/channel_products/${channelProduct.id}`
    ];
    
    let deleted = false;
    
    for (const endpoint of deleteEndpoints) {
      console.log(`   Trying DELETE ${endpoint}...`);
      const result = await makeVeeqoRequest(endpoint, 'DELETE');
      
      if (result.success) {
        console.log(`   ✅ Successfully deleted channel product ${channelProduct.id}`);
        deleted = true;
        deletedCount++;
        break;
      } else {
        console.log(`   ❌ Failed: ${result.error}`);
      }
    }
    
    if (!deleted) {
      console.log(`   ⚠️  Could not delete channel product ${channelProduct.id}`);
      errors.push(`Failed to delete channel product ${channelProduct.id}`);
    }
  }
  
  // If all channel products are deleted, try to delete the entire product
  if (deletedCount === channelProducts.length && channelProducts.length > 0) {
    console.log(`🗑️  All channel products deleted, attempting to delete entire product ${productId}...`);
    
    const productDeleteResult = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`, 'DELETE');
    
    if (productDeleteResult.success) {
      console.log(`✅ Successfully deleted entire product ${productId}`);
      return { 
        success: true, 
        reason: 'All listings and product deleted', 
        deletedChannelProducts: deletedCount,
        deletedProduct: true 
      };
    } else {
      console.log(`⚠️  Could not delete entire product: ${productDeleteResult.error}`);
      return { 
        success: true, 
        reason: 'Channel products deleted but product remains', 
        deletedChannelProducts: deletedCount,
        deletedProduct: false,
        productDeleteError: productDeleteResult.error
      };
    }
  }
  
  if (deletedCount > 0) {
    console.log(`✅ Successfully deleted ${deletedCount}/${channelProducts.length} channel products`);
    return { 
      success: true, 
      reason: 'Partial deletion', 
      deletedChannelProducts: deletedCount, 
      totalChannelProducts: channelProducts.length,
      errors 
    };
  } else {
    console.log(`❌ Failed to delete any channel products`);
    return { success: false, reason: 'No deletions successful', errors };
  }
}

// Function to process a single SDASIN
async function processSdasin(sdasin, dryRun = true) {
  console.log(`\n📋 Processing SDASIN ID: ${sdasin.id}`);
  console.log(`   FBM SKU: ${sdasin.fbm_sku}`);
  console.log(`   FBM Uploaded At: ${sdasin.fbm_uploaded_at}`);
  console.log(`   ASIN: ${sdasin.asin}`);
  
  if (sdasin.fbm_uploaded_at !== null) {
    console.log(`⚠️  SDASIN ${sdasin.id} still has fbm_uploaded_at set - skipping`);
    return { success: false, reason: 'Still has fbm_uploaded_at' };
  }
  
  // Analyze the Veeqo product
  const productAnalysis = await analyzeVeeqoProduct(sdasin.fbm_sku);
  
  if (dryRun) {
    console.log(`🔍 DRY RUN: Would attempt to delete Veeqo listings`);
    if (productAnalysis.exists) {
      console.log(`   Would delete ${productAnalysis.channelProducts.length} channel product(s)`);
    } else {
      console.log(`   ${productAnalysis.reason || 'No action needed'}`);
    }
    return { success: true, reason: 'Dry run - no actual deletion', analysis: productAnalysis };
  }
  
  // Actually delete the listings
  return await deleteVeeqoListings(productAnalysis, sdasin.fbm_sku);
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🛠️  USAGE:
  node deleteVeeqoListings.cjs <command> [options]

📋 COMMANDS:
  check <sdasin_id>           - Check a specific SDASIN for Veeqo listings to delete
  find-all                    - Find all SDASINS with deleted Amazon listings
  delete-single <sdasin_id>   - Delete Veeqo listings for a specific SDASIN
  delete-all [--confirm]      - Delete Veeqo listings for all SDASINS with deleted Amazon listings

📝 EXAMPLES:
  node deleteVeeqoListings.cjs check 46948
  node deleteVeeqoListings.cjs find-all
  node deleteVeeqoListings.cjs delete-single 46948
  node deleteVeeqoListings.cjs delete-all --confirm

⚠️  WARNING: Deletion operations are permanent! Use 'check' and 'find-all' first.
`);
    return;
  }
  
  const command = args[0];
  
  try {
    switch (command) {
      case 'check':
        if (args.length < 2) {
          console.error('❌ Please provide a SDASIN ID');
          return;
        }
        const sdasinId = parseInt(args[1]);
        const sdasin = await getSdasinRecord(sdasinId);
        if (sdasin) {
          await processSdasin(sdasin, true); // Dry run
        }
        break;
        
      case 'find-all':
        const deletedListings = await findDeletedAmazonListings();
        if (deletedListings.length > 0) {
          console.log(`\n📋 SDASINS with deleted Amazon listings:`);
          deletedListings.forEach(sdasin => {
            console.log(`   - ID: ${sdasin.id}, SKU: ${sdasin.fbm_sku}, ASIN: ${sdasin.asin}`);
          });
          console.log(`\n💡 Use 'delete-all --confirm' to delete corresponding Veeqo listings`);
        }
        break;
        
      case 'delete-single':
        if (args.length < 2) {
          console.error('❌ Please provide a SDASIN ID');
          return;
        }
        const targetSdasinId = parseInt(args[1]);
        const targetSdasin = await getSdasinRecord(targetSdasinId);
        if (targetSdasin) {
          await processSdasin(targetSdasin, false); // Actually delete
        }
        break;
        
      case 'delete-all':
        const confirmFlag = args.includes('--confirm');
        if (!confirmFlag) {
          console.error('❌ This will delete Veeqo listings! Use --confirm flag to proceed');
          console.log('💡 Run with --confirm to actually delete listings');
          return;
        }
        
        const allDeletedListings = await findDeletedAmazonListings();
        if (allDeletedListings.length === 0) {
          console.log('✅ No SDASINS with deleted Amazon listings found');
          return;
        }
        
        console.log(`🚨 About to process ${allDeletedListings.length} SDASINS for Veeqo listing deletion...`);
        
        let processedCount = 0;
        let successCount = 0;
        let errorCount = 0;
        
        for (const sdasin of allDeletedListings) {
          const result = await processSdasin(sdasin, false);
          processedCount++;
          
          if (result.success) {
            successCount++;
          } else {
            errorCount++;
          }
          
          // Add delay between deletions to be respectful to the API
          if (processedCount < allDeletedListings.length) {
            console.log('⏳ Waiting 2 seconds before next deletion...');
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
        
        console.log(`\n📊 DELETION SUMMARY:`);
        console.log(`✅ Successfully processed: ${successCount}`);
        console.log(`❌ Errors: ${errorCount}`);
        console.log(`📋 Total processed: ${processedCount}`);
        break;
        
      default:
        console.error(`❌ Unknown command: ${command}`);
        console.log('Use no arguments to see usage instructions.');
    }
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the main function
main();
