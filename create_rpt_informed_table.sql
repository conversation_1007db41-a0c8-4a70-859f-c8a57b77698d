-- Create rpt_informed table for Informed Repricer data snapshots
-- This table stores summary statistics from each import of it_infor_all_fields

CREATE TABLE IF NOT EXISTS public.rpt_informed (
    id SERIAL PRIMARY KEY,
    snapshot_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    active_fbm_listing_count INTEGER NOT NULL DEFAULT 0,
    active_fba_listing_count INTEGER NOT NULL DEFAULT 0,
    active_fbm_buybox_count_yes INTEGER NOT NULL DEFAULT 0,
    active_fbm_buybox_count_no INTEGER NOT NULL DEFAULT 0,
    active_fba_buybox_count_yes INTEGER NOT NULL DEFAULT 0,
    active_fba_buybox_count_no INTEGER NOT NULL DEFAULT 0,
    total_active_listings INTEGER GENERATED ALWAYS AS (active_fbm_listing_count + active_fba_listing_count) STORED,
    total_buybox_yes INTEGER GENERATED ALWAYS AS (active_fbm_buybox_count_yes + active_fba_buybox_count_yes) STORED,
    total_buybox_no INTEGER GENERATED ALWAYS AS (active_fbm_buybox_count_no + active_fba_buybox_count_no) STORED,
    fbm_buybox_rate DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN active_fbm_listing_count > 0 
            THEN ROUND((active_fbm_buybox_count_yes::DECIMAL / active_fbm_listing_count::DECIMAL) * 100, 2)
            ELSE 0 
        END
    ) STORED,
    fba_buybox_rate DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN active_fba_listing_count > 0 
            THEN ROUND((active_fba_buybox_count_yes::DECIMAL / active_fba_listing_count::DECIMAL) * 100, 2)
            ELSE 0 
        END
    ) STORED,
    overall_buybox_rate DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN (active_fbm_listing_count + active_fba_listing_count) > 0 
            THEN ROUND(((active_fbm_buybox_count_yes + active_fba_buybox_count_yes)::DECIMAL / (active_fbm_listing_count + active_fba_listing_count)::DECIMAL) * 100, 2)
            ELSE 0 
        END
    ) STORED,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    notes TEXT
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_rpt_informed_snapshot_date ON public.rpt_informed (snapshot_date DESC);
CREATE INDEX IF NOT EXISTS idx_rpt_informed_created_at ON public.rpt_informed (created_at DESC);

-- Add comments for documentation
COMMENT ON TABLE public.rpt_informed IS 'Summary statistics from Informed Repricer data imports';
COMMENT ON COLUMN public.rpt_informed.snapshot_date IS 'Timestamp when the snapshot was taken';
COMMENT ON COLUMN public.rpt_informed.active_fbm_listing_count IS 'Count of active FBM listings (stock > 0, LISTING_TYPE = Amazon MFN)';
COMMENT ON COLUMN public.rpt_informed.active_fba_listing_count IS 'Count of active FBA listings (stock > 0, LISTING_TYPE = Amazon FBA)';
COMMENT ON COLUMN public.rpt_informed.active_fbm_buybox_count_yes IS 'Count of FBM listings with buy box (BUYBOX_WINNER = YES)';
COMMENT ON COLUMN public.rpt_informed.active_fbm_buybox_count_no IS 'Count of FBM listings without buy box (BUYBOX_WINNER = NO)';
COMMENT ON COLUMN public.rpt_informed.active_fba_buybox_count_yes IS 'Count of FBA listings with buy box (BUYBOX_WINNER = YES)';
COMMENT ON COLUMN public.rpt_informed.active_fba_buybox_count_no IS 'Count of FBA listings without buy box (BUYBOX_WINNER = NO)';
COMMENT ON COLUMN public.rpt_informed.total_active_listings IS 'Total active listings (FBM + FBA)';
COMMENT ON COLUMN public.rpt_informed.total_buybox_yes IS 'Total listings with buy box';
COMMENT ON COLUMN public.rpt_informed.total_buybox_no IS 'Total listings without buy box';
COMMENT ON COLUMN public.rpt_informed.fbm_buybox_rate IS 'FBM buy box win rate as percentage';
COMMENT ON COLUMN public.rpt_informed.fba_buybox_rate IS 'FBA buy box win rate as percentage';
COMMENT ON COLUMN public.rpt_informed.overall_buybox_rate IS 'Overall buy box win rate as percentage';
COMMENT ON COLUMN public.rpt_informed.notes IS 'Optional notes about the snapshot';
