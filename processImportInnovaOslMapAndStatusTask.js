// processImportInnovaOslMapAndStatusTask.js - Process import_innova_osl_map_and_status tasks
import dotenv from 'dotenv';
import ExcelJS from 'exceljs';
import fs from 'fs';
import path from 'path';
import nodemailer from 'nodemailer';

dotenv.config();

async function getLookbackDays(supabase) {
  const { data, error } = await supabase
    .from('t_config')
    .select('value')
    .eq('key', 'innova_disc_order_look_back_days')
    .single();
  if (error || !data) return 60;
  const n = parseInt(data.value);
  return Number.isFinite(n) && n > 0 ? n : 60;
}

async function calculateInnovaDiscCountsViaRpc(supabase) {
  try {
    const { data, error } = await supabase.rpc('calculate_innova_internal_id_disc_counts');
    if (error) {
      console.error('[processImportInnovaOslMapAndStatusTask] RPC error:', error.message || error);
      throw error;
    }
    // data is a single summary row { rows_updated, in_stock_sum, sold_last_90_sum }
    const summary = Array.isArray(data) ? data[0] : data;
    return {
      success: true,
      updatedRecords: summary?.rows_updated ?? 0,
      details: summary
    };
  } catch (err) {
    return {
      success: false,
      error: err.message
    };
  }
}

async function calculateInnovaDiscCountsIndividually(supabase) {
  const lookbackDays = await getLookbackDays(supabase);
  const lookbackDate = new Date();
  lookbackDate.setDate(lookbackDate.getDate() - lookbackDays);

  // Fetch all internal_ids in chunks
  const pageSize = 1000;
  let offset = 0;
  let internalIds = [];
  while (true) {
    const { data, error } = await supabase
      .from('it_innova_order_sheet_lines')
      .select('internal_id')
      .order('internal_id', { ascending: true })
      .range(offset, offset + pageSize - 1);
    if (error) throw new Error(`Failed to fetch Innova internal IDs: ${error.message}`);
    if (!data || data.length === 0) break;
    internalIds.push(...data.map(r => r.internal_id).filter(v => v !== null));
    offset += pageSize;
    if (data.length < pageSize) break;
  }

  let updatedRecords = 0;
  let totalInStock = 0;
  let totalSoldLast90 = 0;
  const errors = [];

  for (const internalId of internalIds) {
    try {
      // 1) Find OSL IDs that map to this internal_id
      const { data: osls, error: oslsErr } = await supabase
        .from('t_order_sheet_lines')
        .select('id')
        .eq('vendor_internal_id', internalId);
      if (oslsErr) throw new Error(`Failed to fetch OSL IDs: ${oslsErr.message}`);
      const oslIds = (osls || []).map(r => r.id);

      let inStockCount = 0;
      let soldCount = 0;

      if (oslIds.length > 0) {
        // In stock
        const { count: inStock, error: inErr } = await supabase
          .from('t_discs')
          .select('*', { count: 'exact', head: true })
          .in('vendor_osl_id', oslIds)
          .is('sold_date', null);
        if (inErr) throw new Error(`Failed in-stock count: ${inErr.message}`);
        inStockCount = inStock || 0;

        // Sold in lookback
        const { count: sold, error: soldErr } = await supabase
          .from('t_discs')
          .select('*', { count: 'exact', head: true })
          .in('vendor_osl_id', oslIds)
          .gte('sold_date', lookbackDate.toISOString());
        if (soldErr) throw new Error(`Failed sold count: ${soldErr.message}`);
        soldCount = sold || 0;
      }

      const { error: updErr } = await supabase
        .from('it_innova_order_sheet_lines')
        .update({ in_stock: inStockCount, sold_last_90: soldCount })
        .eq('internal_id', internalId);
      if (updErr) throw new Error(`Failed updating counts: ${updErr.message}`);

      updatedRecords += 1;
      totalInStock += inStockCount;
      totalSoldLast90 += soldCount;
    } catch (e) {
      errors.push(`internal_id ${internalId}: ${e.message}`);
    }
  }

  return { updatedRecords, totalInStock, totalSoldLast90, errors: errors.length ? errors : null };
}

async function emailInnovaOrderSheet(filePath, totalDiscs, summary = {}) {
  try {
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: process.env.SMTP_PORT || 587,
      secure: false,
      auth: { user: process.env.EMAIL_USER, pass: process.env.EMAIL_PASS }
    });

    const today = new Date();
    const dateStr = today.toLocaleDateString('en-US', { year: 'numeric', month: '2-digit', day: '2-digit' });

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: '<EMAIL>',
      subject: `Innova Order Sheet — qty:${summary.qtySum ?? totalDiscs} / cells:${summary.qtyCount ?? 'N/A'} / sold(${summary.lookbackDays ?? 'N/A'}d):${summary.totalSoldLast90 ?? 'N/A'} — ${dateStr}`,
      html: `
        <h2>🎯 Innova Order Sheet Updated</h2>
        <p><strong>Date:</strong> ${dateStr}</p>
        <p><strong>Look back period:</strong> ${summary.lookbackDays ?? 'N/A'} days</p>
        <p><strong>Total Discs Added (sum of qty in Excel):</strong> ${totalDiscs}</p>
        <p><strong>Total discs sold during lookback (sum of it_innova_order_sheet_lines.sold_last_90):</strong> ${summary.totalSoldLast90 ?? 'N/A'}</p>
        <p><strong>Count of qty cells updated:</strong> ${summary.qtyCount ?? 'N/A'}</p>
        <p><strong>Sum of qty values:</strong> ${summary.qtySum ?? totalDiscs}</p>
        <p><strong>Sum of in_stock:</strong> ${summary.inStockSum ?? 'N/A'}</p>
        <p><strong>File:</strong> ${path.basename(filePath)}</p>

        <h3>Summary:</h3>
        <ul>
          <li>✅ Disc inventory counts calculated</li>
          <li>✅ Excel order form updated with current quantities</li>
        </ul>

        <p>The attached Excel file is ready for submission to Innova.</p>
      `,
      attachments: [{ filename: path.basename(filePath), path: filePath }]
    };

    const info = await transporter.sendMail(mailOptions);
    return { success: true, messageId: info.messageId, subject: mailOptions.subject };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

async function updateInnovaExcelFile(supabase) {
  // Use a real Windows path string; double backslashes are only needed in string literal, not quadruple
  const templatePath = 'C:\\Users\\<USER>\\supabase_project\\data\\external data\\innovaorderform.xlsx';

  try {
    if (!fs.existsSync(templatePath)) {
      throw new Error(`Excel file not found: ${templatePath}`);
    }

    // Copy to timestamped file next to the template
    const dir = path.dirname(templatePath);
    const base = path.basename(templatePath, path.extname(templatePath));
    const now = new Date();
    const timestamp = `${now.getFullYear()}-${String(now.getMonth()+1).padStart(2,'0')}-${String(now.getDate()).padStart(2,'0')}_${String(now.getHours()).padStart(2,'0')}-${String(now.getMinutes()).padStart(2,'0')}-${String(now.getSeconds()).padStart(2,'0')}`;
    const outPath = path.join(dir, `${base}_${timestamp}.xlsx`);
    fs.copyFileSync(templatePath, outPath);

    console.log(`[processImportInnovaOslMapAndStatusTask] Wrote timestamped Excel: ${outPath}`);

    // Load workbook from copy
    const wb = new ExcelJS.Workbook();
    await wb.xlsx.readFile(outPath);
    const sheet = wb.getWorksheet('Order_Table');
    if (!sheet) throw new Error(`Sheet 'Order_Table' not found in Excel file`);

    // Ensure the Order_Table sheet is visible and active when opened
    sheet.state = 'visible';
    const sheetIndex = wb.worksheets.findIndex(ws => ws.name === 'Order_Table');
    if (sheetIndex >= 0) {
      wb.views = [{ activeTab: sheetIndex }];
    }

    // Explicitly set a default font for the entire sheet to avoid inherited strikethrough
    const defaultFont = { name: 'Calibri', family: 2, size: 11, color: { argb: 'FF000000' }, bold: false, italic: false, underline: false, strike: false };
    for (let r = 1; r <= sheet.rowCount; r++) {
      const row = sheet.getRow(r);
      for (let c = 1; c <= sheet.columnCount; c++) {
        const cell = row.getCell(c);
        cell.font = defaultFont;
      }
    }
    // Also normalize the Order_Form sheet fonts to avoid inherited strikethrough
    const orderFormCandidateNames = ['Order_Form', 'Order Form'];
    for (const name of orderFormCandidateNames) {
      const formSheet = wb.getWorksheet(name);
      if (formSheet) {
        formSheet.state = 'visible';
        for (let r = 1; r <= formSheet.rowCount; r++) {
          const row = formSheet.getRow(r);
          for (let c = 1; c <= formSheet.columnCount; c++) {
            const cell = row.getCell(c);
            // Apply same default font and ensure strike is false
            cell.font = { ...defaultFont, strike: false };
          }
        }
        break; // stop at first matching name found
      }
    }


    // Discover column indices by header row
    const headerRow = sheet.getRow(1);
    const colIndexByName = {};
    headerRow.eachCell((cell, colNumber) => {
      const name = (typeof cell.value === 'string' ? cell.value : `${cell.value || ''}`).trim().toLowerCase();
      if (name) colIndexByName[name] = colNumber;
    });

    const internalIdCol = colIndexByName['internal id'];
    const orderedCol = colIndexByName['ordered'];
    if (!internalIdCol || !orderedCol) {
      throw new Error(`Could not find 'Internal ID' and/or 'Ordered' columns in header row`);
    }

    // Fetch qty data in chunks
    const pageSize = 1000;
    const records = [];
    for (let from = 0; ; from += pageSize) {
      const to = from + pageSize - 1;
      const { data, error } = await supabase
        .from('it_innova_order_sheet_lines')
        .select('internal_id, qty, ordered')
        .order('internal_id', { ascending: true })
        .range(from, to);
      if (error) throw new Error(`Failed to fetch Innova rows (range ${from}-${to}): ${error.message}`);
      if (!data || data.length === 0) break;
      records.push(...data);
      if (data.length < pageSize) break;
    }

    // Build map internal_id -> qty (fallback to ordered)
    const qtyByInternalId = new Map();
    for (const r of records) {
      const qty = (r.qty ?? r.ordered ?? 0) || 0;
      if (r.internal_id != null && qty > 0) qtyByInternalId.set(r.internal_id, qty);
    }

    let updatedCells = 0;
    let totalDiscsAdded = 0;

    // Iterate rows and write qtys
    sheet.eachRow((row, rowNumber) => {
      if (rowNumber === 1) return; // skip header
      const idCell = row.getCell(internalIdCol);
      const orderedCell = row.getCell(orderedCol);
      const internalId = Number(idCell.value);
      if (!Number.isFinite(internalId)) return;
    // Also explicitly clear strike-through on Order_Form if present
    for (const name of ['Order_Form', 'Order Form']) {
      const s = wb.getWorksheet(name);
      if (!s) continue;
      s.eachRow((row) => {
        row.eachCell((cell) => {
          const f = cell.font || {};
          if (f.strike) cell.font = { ...f, strike: false };
        });
      });
    }

      if (!qtyByInternalId.has(internalId)) return;

      const qty = qtyByInternalId.get(internalId);
      orderedCell.value = qty;
      updatedCells += 1;
      totalDiscsAdded += qty;
    });

    // Workaround: some templates end up with strike-through font after write; force-clear strike on entire sheet
    sheet.eachRow((row) => {
      row.eachCell((cell) => {
        const f = cell.font || {};
        if (f.strike) {
          cell.font = { ...f, strike: false };
        }
      });
    });

    await wb.xlsx.writeFile(outPath);

    // Build email summary
    const lookbackDays = await getLookbackDays(supabase);

    async function sumColumn(column) {
      let sum = 0;
      for (let from = 0; ; from += pageSize) {
        const to = from + pageSize - 1;
        const { data, error } = await supabase
          .from('it_innova_order_sheet_lines')
          .select(column)
          .order('internal_id', { ascending: true })
          .range(from, to);
        if (error) throw new Error(`Failed to fetch ${column} (range ${from}-${to}): ${error.message}`);
        if (!data || data.length === 0) break;
        if (column === 'sold_last_90') sum += data.reduce((s, r) => s + (r.sold_last_90 || 0), 0);
        if (column === 'in_stock') sum += data.reduce((s, r) => s + (r.in_stock || 0), 0);
        if (data.length < pageSize) break;
      }
      return sum;
    }

    const totalSoldLast90 = await sumColumn('sold_last_90');
    const inStockSum = await sumColumn('in_stock');

    return {
      success: true,
      message: `Updated ${updatedCells} rows in Excel file and <NAME_EMAIL>`,
      updatedCells,
      totalDiscsAdded,
      timestampedFile: outPath,
      summary: { lookbackDays, totalSoldLast90, qtyCount: updatedCells, qtySum: totalDiscsAdded, inStockSum }
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

export async function processImportInnovaOslMapAndStatusTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processImportInnovaOslMapAndStatusTask] Processing task ${task.id} of type ${task.task_type}`);
  const start = new Date();
  try {
    await updateTaskStatus(task.id, 'processing');

    // Step 1: Calculate disc counts (try RPC, fall back if missing)
    let countsResult = await calculateInnovaDiscCountsViaRpc(supabase);
    if (!countsResult.success) {
      console.log('[processImportInnovaOslMapAndStatusTask] RPC failed (above error printed), calculating counts individually...');
      countsResult = await calculateInnovaDiscCountsIndividually(supabase);
    }

    // Step 2: Update Excel and email
    const excelResult = await updateInnovaExcelFile(supabase);

    // Step 3: Email
    const emailResult = excelResult.success
      ? await emailInnovaOrderSheet(
          excelResult.timestampedFile,
          excelResult.totalDiscsAdded,
          excelResult.summary
        )
      : { success: false, error: excelResult.error || 'Excel update failed' };

    const durationMs = new Date() - start;
    const result = {
      success: true,
      message: 'Successfully processed Innova order sheet updates',
      counts: countsResult,
      excel: excelResult,
      email: emailResult,
      duration: `${durationMs}ms`
    };

    await updateTaskStatus(task.id, 'completed', result);
  } catch (error) {
    const durationMs = new Date() - start;
    console.error(`[processImportInnovaOslMapAndStatusTask] Task failed: ${error.message}`);
    await logError(`Import Innova order sheet task failed: ${error.message}`, `Task ${task.id}`);
    await updateTaskStatus(task.id, 'error', { success: false, error: error.message, duration: `${durationMs}ms` });
  }
}

export default processImportInnovaOslMapAndStatusTask;

