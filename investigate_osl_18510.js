// investigate_osl_18510.js
// Investigate OSL 18510 to understand why the error is being generated

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function investigateOSL18510() {
  try {
    console.log('Investigating OSL 18510...');
    
    // Get OSL details
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('*')
      .eq('id', 18510)
      .single();
    
    if (oslError) {
      console.error('Error fetching OSL:', oslError);
      return;
    }
    
    console.log('OSL 18510 Details:');
    console.log('MPS ID:', osl.mps_id);
    console.log('Color ID:', osl.color_id);
    console.log('Min Weight:', osl.min_weight);
    console.log('Max Weight:', osl.max_weight);
    
    // Get MPS details to construct the handle
    const { data: mps, error: mpsError } = await supabase
      .from('t_mps')
      .select('*')
      .eq('id', osl.mps_id)
      .single();
    
    if (mpsError) {
      console.error('Error fetching MPS:', mpsError);
      return;
    }
    
    // Get related records
    const { data: plastic } = await supabase.from('t_plastics').select('*').eq('id', mps.plastic_id).single();
    const { data: mold } = await supabase.from('t_molds').select('*').eq('id', mps.mold_id).single();
    const { data: stamp } = await supabase.from('t_stamps').select('*').eq('id', mps.stamp_id).single();
    const { data: brand } = await supabase.from('t_brands').select('*').eq('id', mold.brand_id).single();
    
    // Generate the handle that would be used
    function generateMPSHandle(brand, plastic, mold, stamp) {
      let base = `${brand}-${plastic}-${mold}-${stamp}`.toLowerCase();
      base = base
        .replace(/ /g, "-")
        .replace(/'/g, "")
        .replace(/\//g, "")
        .replace(/\./g, "-")
        .replace(/&/g, "-")
        .replace(/\(/g, "")
        .replace(/\)/g, "")
        .replace(/"/g, "")
        .replace(/%/g, "")
        .replace(/#/g, "")
        .replace(/-\$/g, "");
      while (base.includes("--")) {
        base = base.replace(/--/g, "-");
      }
      return base;
    }
    
    const expectedHandle = generateMPSHandle(
      brand.brand,
      plastic.plastic,
      mold.mold,
      stamp.stamp
    );
    
    console.log('\nExpected Shopify Handle:', expectedHandle);
    console.log('Product Details:');
    console.log(`- Brand: ${brand.brand}`);
    console.log(`- Plastic: ${plastic.plastic}`);
    console.log(`- Mold: ${mold.mold}`);
    console.log(`- Stamp: ${stamp.stamp}`);
    
    // Now let's check if this product exists on Shopify
    console.log('\n🔍 Checking if product exists on Shopify...');
    
    // Use the same GraphQL query that publishProductOSL.js uses
    const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
    const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
    
    const query = `
      query getProductByHandle($handle: String!) {
        productByHandle(handle: $handle) {
          id
          handle
          title
          options {
            id
            name
            position
            values
          }
        }
      }
    `;
    
    const response = await fetch(shopifyEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": shopifyAccessToken
      },
      body: JSON.stringify({ 
        query, 
        variables: { handle: expectedHandle } 
      })
    });
    
    const result = await response.json();
    
    if (result.data && result.data.productByHandle) {
      console.log('✅ FOUND: Product exists on Shopify!');
      console.log('Product ID:', result.data.productByHandle.id);
      console.log('Product Title:', result.data.productByHandle.title);
      console.log('Options:', JSON.stringify(result.data.productByHandle.options, null, 2));
      
      if (result.data.productByHandle.options.length === 1) {
        console.log('🎯 CONFIRMED: Product has 1-option structure - this explains the error!');
      } else {
        console.log('❓ UNEXPECTED: Product has', result.data.productByHandle.options.length, 'options');
      }
    } else {
      console.log('❌ NOT FOUND: Product does not exist on Shopify');
      console.log('This is strange - the error suggests it found a product with 1-option structure');
      console.log('GraphQL Response:', JSON.stringify(result, null, 2));
    }
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

investigateOSL18510();
