import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkMissingData() {
    console.log('Checking for missing brands, molds, and plastics...\n');
    
    try {
        // Check for Active brand
        const { data: activeBrand, error: activeBrandError } = await supabase
            .from('t_brands')
            .select('*')
            .ilike('brand', '%active%');
        
        console.log('Active brand search results:');
        console.log(activeBrand);
        
        // Check for EMac Truth mold
        const { data: emacMold, error: emacMoldError } = await supabase
            .from('t_molds')
            .select('*')
            .ilike('mold', '%emac%');
        
        console.log('\nEMac mold search results:');
        console.log(emacMold);
        
        // Check for Truth mold
        const { data: truthMold, error: truthMoldError } = await supabase
            .from('t_molds')
            .select('*')
            .ilike('mold', '%truth%');
        
        console.log('\nTruth mold search results:');
        console.log(truthMold);
        
        // Check for Gold Line Bio plastic
        const { data: goldBioPlastic, error: goldBioError } = await supabase
            .from('t_plastics')
            .select('*')
            .ilike('plastic', '%gold%bio%');
        
        console.log('\nGold Bio plastic search results:');
        console.log(goldBioPlastic);
        
        // Check for BT Medium Burst plastic
        const { data: btPlastic, error: btError } = await supabase
            .from('t_plastics')
            .select('*')
            .ilike('plastic', '%bt%medium%');
        
        console.log('\nBT Medium plastic search results:');
        console.log(btPlastic);
        
        // Check for Swan1 Reborn mold
        const { data: swanMold, error: swanError } = await supabase
            .from('t_molds')
            .select('*')
            .ilike('mold', '%swan%');
        
        console.log('\nSwan mold search results:');
        console.log(swanMold);
        
        // Check for Ballista Pro mold
        const { data: ballistaMold, error: ballistaError } = await supabase
            .from('t_molds')
            .select('*')
            .ilike('mold', '%ballista%');
        
        console.log('\nBallista mold search results:');
        console.log(ballistaMold);
        
        // Check what brands we have
        const { data: allBrands, error: brandsError } = await supabase
            .from('t_brands')
            .select('brand')
            .order('brand');
        
        console.log('\nAll brands:');
        allBrands.forEach(brand => console.log(`- ${brand.brand}`));
        
    } catch (error) {
        console.error('Query failed:', error);
    }
}

// Run the check
checkMissingData();
