# ---- Node / JS standard ignores ----
node_modules/
imageWatcher/node_modules/

# Builds and coverage
build/
dist/
.next/
.nuxt/
coverage/
.nyc_output/

# OS/editor artifacts
.DS_Store
Thumbs.db
*.swp
.idea/
.vscode/

# Environment and secrets
.env
.env.*
# Service account keys (keep out of git)
google-service-account.json

# Logs and runtime files
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Project data/output (large and not source)
data/
photos/
public/camera/

# Temp/cache
.tmp/
.temp/
.cache/

# Misc
*.pid
*.seed
*.orig

