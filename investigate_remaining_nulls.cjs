require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function investigateRemainingNulls() {
  try {
    console.log('Investigating remaining discs with null vendor_osl_id...');
    
    // Get current count of nulls
    const { count: nullCount, error: countError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (countError) {
      console.error('Error getting count:', countError);
      return;
    }
    
    console.log(`Current discs with null vendor_osl_id: ${nullCount}`);
    
    // Get a sample of these discs to investigate
    const { data: sampleDiscs, error: sampleError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null)
      .limit(10);
    
    if (sampleError) {
      console.error('Error getting sample discs:', sampleError);
      return;
    }
    
    console.log(`\nInvestigating ${sampleDiscs.length} sample discs:`);
    
    let shouldHaveMatches = 0;
    
    for (const disc of sampleDiscs) {
      console.log(`\n=== Disc ${disc.id} ===`);
      console.log(`MPS: ${disc.mps_id}, Weight: ${disc.weight}g, Weight MFG: ${disc.weight_mfg}g, Color: ${disc.color_id}`);
      console.log(`Current mappings: order_sheet_line_id=${disc.order_sheet_line_id}, vendor_osl_id=${disc.vendor_osl_id}`);
      
      // Test the vendor OSL function
      const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
        'find_matching_osl_by_mfg_weight',
        {
          mps_id_param: disc.mps_id,
          color_id_param: disc.color_id,
          weight_mfg_param: disc.weight_mfg
        }
      );
      
      if (vendorOslError) {
        console.error('Error calling function:', vendorOslError);
        continue;
      }
      
      console.log('Function result:', vendorOslData);
      
      const foundOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
      
      if (foundOslId) {
        console.log(`✅ SHOULD HAVE MATCH! Function found OSL: ${foundOslId}`);
        shouldHaveMatches++;
        
        // Try to update this disc right now
        const { error: updateError } = await supabase
          .from('t_discs')
          .update({ vendor_osl_id: foundOslId })
          .eq('id', disc.id);
        
        if (updateError) {
          console.error('Error updating disc:', updateError);
        } else {
          console.log(`✅ Successfully updated disc ${disc.id} with vendor_osl_id: ${foundOslId}`);
        }
      } else {
        console.log(`❌ No match found (this is expected for some discs)`);
        
        // Check what OSLs exist for this MPS and color
        const { data: availableOsls, error: oslError } = await supabase
          .from('t_order_sheet_lines')
          .select('id, min_weight, max_weight, color_id')
          .eq('mps_id', disc.mps_id)
          .in('color_id', [disc.color_id, 23])
          .limit(5);
        
        if (!oslError && availableOsls && availableOsls.length > 0) {
          console.log('Available OSLs for this MPS and color:');
          availableOsls.forEach(osl => {
            const roundedWeight = Math.round(disc.weight_mfg);
            const inRange = roundedWeight >= osl.min_weight && roundedWeight <= osl.max_weight;
            console.log(`  OSL ${osl.id}: ${osl.min_weight}-${osl.max_weight}g, color ${osl.color_id}, fits ${roundedWeight}g? ${inRange}`);
          });
        } else {
          console.log(`No OSLs found for MPS ${disc.mps_id} with colors ${disc.color_id} or 23`);
        }
      }
    }
    
    console.log(`\n=== INVESTIGATION SUMMARY ===`);
    console.log(`Sample discs investigated: ${sampleDiscs.length}`);
    console.log(`Discs that SHOULD have matches: ${shouldHaveMatches}`);
    
    if (shouldHaveMatches > 0) {
      console.log(`\n🔍 FOUND ISSUE: ${shouldHaveMatches} out of ${sampleDiscs.length} sample discs should have vendor OSL matches!`);
      console.log('This suggests there may be an issue with the bulk processing or the function.');
      console.log('\nLet me restart the process for ALL remaining null vendor_osl_id discs...');
      
      // Restart the process for all remaining nulls
      await processRemainingNulls(nullCount);
    } else {
      console.log('\n✅ All sample discs correctly have no matches - the process worked correctly.');
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

async function processRemainingNulls(totalCount) {
  try {
    console.log(`\n=== REPROCESSING ${totalCount} REMAINING DISCS ===`);
    
    let totalProcessed = 0;
    let totalUpdated = 0;
    let totalErrors = 0;
    const batchSize = 100;
    
    while (totalProcessed < totalCount) {
      console.log(`\nProcessing batch ${Math.floor(totalProcessed / batchSize) + 1}...`);
      
      // Get next batch of discs that still need vendor_osl_id
      const { data: discs, error: batchError } = await supabase
        .from('t_discs')
        .select('id, mps_id, weight_mfg, color_id, vendor_osl_id')
        .is('vendor_osl_id', null)
        .not('weight_mfg', 'is', null)
        .not('mps_id', 'is', null)
        .not('color_id', 'is', null)
        .range(0, batchSize - 1);
      
      if (batchError) {
        console.error('Error getting batch:', batchError);
        break;
      }
      
      if (!discs || discs.length === 0) {
        console.log('No more discs to process');
        break;
      }
      
      console.log(`Processing ${discs.length} discs in this batch...`);
      
      for (const disc of discs) {
        try {
          // Find matching vendor OSL
          const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
            'find_matching_osl_by_mfg_weight',
            {
              mps_id_param: disc.mps_id,
              color_id_param: disc.color_id,
              weight_mfg_param: disc.weight_mfg
            }
          );
          
          if (vendorOslError) {
            console.error(`Error finding vendor OSL for disc ${disc.id}:`, vendorOslError);
            totalErrors++;
            continue;
          }
          
          const vendorOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
          
          if (vendorOslId) {
            // Update the disc with vendor_osl_id
            const { error: updateError } = await supabase
              .from('t_discs')
              .update({ vendor_osl_id: vendorOslId })
              .eq('id', disc.id);
            
            if (updateError) {
              console.error(`Error updating disc ${disc.id}:`, updateError);
              totalErrors++;
            } else {
              totalUpdated++;
              if (totalUpdated % 50 === 0) {
                console.log(`  ✅ Updated ${totalUpdated} more discs...`);
              }
            }
          }
          
          totalProcessed++;
          
        } catch (err) {
          console.error(`Error processing disc ${disc.id}:`, err.message);
          totalErrors++;
          totalProcessed++;
        }
      }
      
      console.log(`Batch completed. Processed: ${totalProcessed}, Updated: ${totalUpdated}, Errors: ${totalErrors}`);
      
      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    console.log('\n=== REPROCESSING RESULTS ===');
    console.log(`Total discs reprocessed: ${totalProcessed}`);
    console.log(`Additional discs updated: ${totalUpdated}`);
    console.log(`Errors encountered: ${totalErrors}`);
    
    if (totalUpdated > 0) {
      console.log(`\n✅ SUCCESS! Found and updated ${totalUpdated} additional vendor OSL mappings!`);
    }
  } catch (err) {
    console.error('Error in reprocessing:', err.message);
  }
}

investigateRemainingNulls();
