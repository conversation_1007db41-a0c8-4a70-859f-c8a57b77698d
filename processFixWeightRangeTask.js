import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Shopify API configuration
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
const productsEndpoint = shopifyEndpoint.replace("graphql.json", "products.json");

// Rate limiting
const RATE_LIMIT_DELAY = 500; // 500ms between requests

/**
 * Sleep function for rate limiting
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Execute Shopify GraphQL query
 */
async function executeShopifyGraphQL(query, variables = {}) {
  const response = await fetch(shopifyEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken,
    },
    body: JSON.stringify({ query, variables }),
  });

  const result = await response.json();

  if (result.errors) {
    throw new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`);
  }

  return result.data;
}

/**
 * Find Shopify product by variant SKU using GraphQL
 * @param {string} sku - The variant SKU to search for
 * @returns {Object|null} - Product data or null if not found
 */
async function findProductBySku(sku) {
  try {
    console.log(`🔍 Finding Shopify product for variant SKU: ${sku}`);

    const query = `
      query getVariantBySku($query: String!) {
        productVariants(first: 1, query: $query) {
          edges {
            node {
              id
              sku
              product {
                id
                title
                tags
              }
            }
          }
        }
      }
    `;

    const variables = {
      query: `sku:${sku}`
    };

    const result = await executeShopifyGraphQL(query, variables);

    if (!result.productVariants.edges.length) {
      console.log(`❌ No product found for variant SKU: ${sku}`);
      return null;
    }

    const variant = result.productVariants.edges[0].node;
    const productId = variant.product.id.split('/').pop(); // Extract numeric ID

    console.log(`✅ Found product ${productId} (variant ${variant.id}) with SKU ${sku}`);

    return {
      productId,
      variantId: variant.id,
      title: variant.product.title,
      currentTags: variant.product.tags
    };
  } catch (error) {
    console.error(`❌ Error finding product for variant SKU ${sku}:`, error.message);
    return null;
  }
}

/**
 * Update Shopify product tags
 * @param {string} productId - The product ID
 * @param {Array} newTags - Array of new tags
 * @returns {Object} - Update result
 */
async function updateProductTags(productId, newTags) {
  try {
    console.log(`🔄 Updating product ${productId} with tags: ${newTags.join(', ')}`);
    
    const updateEndpoint = `${productsEndpoint.replace('.json', '')}/${productId}.json`;
    
    const payload = {
      product: {
        id: productId,
        tags: newTags.join(',')
      }
    };

    const response = await fetch(updateEndpoint, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Shopify API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const updatedProduct = await response.json();
    console.log(`✅ Successfully updated product ${productId} tags`);
    
    return {
      success: true,
      product: updatedProduct.product
    };
    
  } catch (error) {
    console.error(`❌ Error updating product ${productId}:`, error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Remove existing weight range tags from tag array
 * @param {Array} tags - Current tags array
 * @returns {Array} - Tags with weight range tags removed
 */
function removeExistingWeightRangeTags(tags) {
  const weightRangePattern = /^wt_rng_\d+-\d+$/;
  return tags.filter(tag => !weightRangePattern.test(tag));
}

/**
 * Calculate weight range tag based on disc weight
 * @param {number} weight - The disc weight
 * @returns {string} - The weight range tag
 */
function calculateWeightRangeTag(weight) {
  // Round to nearest 0.5 for proper range assignment
  const roundedWeight = Math.round(weight * 2) / 2;

  if (roundedWeight >= 10 && roundedWeight <= 49.5) {
    return 'wt_rng_10-49';
  } else if (roundedWeight >= 50 && roundedWeight <= 99.5) {
    return 'wt_rng_50-99';
  } else if (roundedWeight >= 100 && roundedWeight <= 119.5) {
    return 'wt_rng_100-119';
  } else if (roundedWeight >= 120 && roundedWeight <= 139.5) {
    return 'wt_rng_120-139';
  } else if (roundedWeight >= 140 && roundedWeight <= 149.5) {
    return 'wt_rng_140-149';
  } else if (roundedWeight >= 150 && roundedWeight <= 159.5) {
    return 'wt_rng_150-159';
  } else if (roundedWeight >= 160 && roundedWeight <= 169.5) {
    return 'wt_rng_160-169';
  } else if (roundedWeight >= 170 && roundedWeight <= 174.5) {
    return 'wt_rng_170-174';
  } else if (roundedWeight >= 175 && roundedWeight <= 180.5) {
    return 'wt_rng_175-180';
  } else if (roundedWeight >= 181 && roundedWeight <= 200) {
    return 'wt_rng_181-200';
  } else if (roundedWeight >= 201 && roundedWeight <= 249) {
    return 'wt_rng_201-249';
  } else {
    // For weights outside all ranges, return null to skip
    return null;
  }
}

/**
 * Process a fix_weight_range task
 * @param {Object} task - The task object
 * @param {Object} context - Context object with supabase, updateTaskStatus, logError
 */
async function processFixWeightRangeTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processFixWeightRangeTask] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      if (typeof task.payload === 'object' && task.payload !== null) {
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        payload = JSON.parse(task.payload);
      } else {
        throw new Error('Invalid payload format');
      }
    } catch (err) {
      const errMsg = `[processFixWeightRangeTask] Error parsing task payload: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, 'Parsing task payload');
      await updateTaskStatus(task.id, 'failed', { error: errMsg });
      return;
    }

    const { disc_id, weight, expected_weight_range_tag } = payload;

    if (!disc_id || !weight || !expected_weight_range_tag) {
      const errMsg = `[processFixWeightRangeTask] Missing required payload fields: disc_id=${disc_id}, weight=${weight}, expected_weight_range_tag=${expected_weight_range_tag}`;
      console.error(errMsg);
      await updateTaskStatus(task.id, 'failed', { error: errMsg });
      return;
    }

    // Validate that the expected weight range tag matches what we calculate
    const calculatedTag = calculateWeightRangeTag(weight);
    if (calculatedTag !== expected_weight_range_tag) {
      const errMsg = `[processFixWeightRangeTask] Weight range tag mismatch: expected ${expected_weight_range_tag}, calculated ${calculatedTag} for weight ${weight}g`;
      console.error(errMsg);
      await updateTaskStatus(task.id, 'failed', { error: errMsg });
      return;
    }

    console.log(`[processFixWeightRangeTask] Processing disc ${disc_id} with weight ${weight}g, expected tag: ${expected_weight_range_tag}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Generate SKU for the disc (format: D{disc_id})
    const sku = `D${disc_id}`;
    console.log(`[processFixWeightRangeTask] Looking for Shopify product with SKU: ${sku}`);

    // Find the product in Shopify
    const shopifyProduct = await findProductBySku(sku);
    await sleep(RATE_LIMIT_DELAY);

    if (!shopifyProduct) {
      const errMsg = `Product not found in Shopify for SKU: ${sku}`;
      console.log(`[processFixWeightRangeTask] ${errMsg}`);
      await updateTaskStatus(task.id, 'failed', {
        error: errMsg,
        disc_id: disc_id,
        sku: sku
      });
      return;
    }

    // Check if the expected weight range tag is already present
    const currentTags = shopifyProduct.currentTags;
    const hasExpectedTag = currentTags.includes(expected_weight_range_tag);
    
    if (hasExpectedTag) {
      console.log(`[processFixWeightRangeTask] Product ${shopifyProduct.productId} already has the correct weight range tag: ${expected_weight_range_tag}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `Product already has correct weight range tag: ${expected_weight_range_tag}`,
        disc_id: disc_id,
        sku: sku,
        product_id: shopifyProduct.productId,
        weight_range_tag: expected_weight_range_tag,
        action: 'no_change_needed'
      });
      return;
    }

    // Remove any existing weight range tags and add the new one
    const tagsWithoutWeightRange = removeExistingWeightRangeTags(currentTags);
    const newTags = [...tagsWithoutWeightRange, expected_weight_range_tag];
    
    console.log(`[processFixWeightRangeTask] Updating tags from [${currentTags.join(', ')}] to [${newTags.join(', ')}]`);

    // Update the product tags
    const updateResult = await updateProductTags(shopifyProduct.productId, newTags);
    await sleep(RATE_LIMIT_DELAY);

    if (updateResult.success) {
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully updated weight range tag to: ${expected_weight_range_tag}`,
        disc_id: disc_id,
        sku: sku,
        product_id: shopifyProduct.productId,
        weight_range_tag: expected_weight_range_tag,
        old_tags: currentTags,
        new_tags: newTags,
        action: 'updated'
      });
      console.log(`[processFixWeightRangeTask] Successfully completed weight range update for disc ${disc_id}`);
    } else {
      const errMsg = `Failed to update Shopify product tags: ${updateResult.error}`;
      console.error(`[processFixWeightRangeTask] ${errMsg}`);
      await updateTaskStatus(task.id, 'failed', {
        error: errMsg,
        disc_id: disc_id,
        sku: sku,
        product_id: shopifyProduct.productId
      });
    }

  } catch (err) {
    const errMsg = `[processFixWeightRangeTask] Exception processing task: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, 'Processing fix_weight_range task');
    await updateTaskStatus(task.id, 'failed', { error: errMsg });
  }
}

export { processFixWeightRangeTask };
