-- Sc<PERSON>t to find and disable the old trigger that creates duplicate publish_disc tasks
-- This trigger fires when ready_new changes to TRUE and creates publish_disc tasks without enqueued_by

-- First, let's see all triggers on t_discs table that might be related to publishing
DO $$
DECLARE
    trigger_record RECORD;
    trigger_def TEXT;
BEGIN
    RAISE NOTICE 'Checking all triggers on t_discs table...';
    
    FOR trigger_record IN 
        SELECT 
            t.tgname as trigger_name,
            t.tgenabled as enabled,
            p.proname as function_name,
            pg_get_triggerdef(t.oid) as trigger_definition
        FROM pg_trigger t
        JOIN pg_class c ON t.tgrelid = c.oid
        JOIN pg_proc p ON t.tgfoid = p.oid
        WHERE c.relname = 't_discs'
        AND t.tgisinternal = false
        ORDER BY t.tgname
    LOOP
        RAISE NOTICE 'Trigger: % (Function: %, Enabled: %)', 
            trigger_record.trigger_name, 
            trigger_record.function_name,
            CASE trigger_record.enabled 
                WHEN 'O' THEN 'YES' 
                ELSE 'NO' 
            END;
        
        -- Check if this trigger might be the culprit
        IF trigger_record.trigger_definition ILIKE '%ready_new%' OR 
           trigger_record.function_name ILIKE '%publish%' OR
           trigger_record.trigger_definition ILIKE '%publish_disc%' THEN
            RAISE NOTICE '  ⚠️  SUSPICIOUS: This trigger might create publish_disc tasks!';
            RAISE NOTICE '  Definition: %', trigger_record.trigger_definition;
        END IF;
    END LOOP;
END $$;

-- Common suspects for old triggers that might create publish_disc tasks:
-- Let's try to drop some likely candidates

-- 1. Check for triggers with "publish" in the name
DROP TRIGGER IF EXISTS tr_publish_disc ON t_discs;
DROP TRIGGER IF EXISTS trg_publish_disc ON t_discs;
DROP TRIGGER IF EXISTS trigger_publish_disc ON t_discs;
DROP TRIGGER IF EXISTS tr_try_publish_product_disc ON t_discs;
DROP TRIGGER IF EXISTS trg_try_publish_product_disc ON t_discs;

-- 2. Check for triggers that might fire on ready_new changes
DROP TRIGGER IF EXISTS tr_ready_new_publish ON t_discs;
DROP TRIGGER IF EXISTS trg_ready_new_publish ON t_discs;
DROP TRIGGER IF EXISTS trigger_ready_new_publish ON t_discs;

-- 3. Check for the specific function we found in fix_trigger_timeout.sql
DROP TRIGGER IF EXISTS tr_try_publish_product_disc_q ON t_discs;
DROP TRIGGER IF EXISTS trg_try_publish_product_disc_q ON t_discs;

-- 4. Check for any trigger using fn_try_publish_product_disc_q function
DO $$
DECLARE
    trigger_name TEXT;
BEGIN
    FOR trigger_name IN 
        SELECT t.tgname
        FROM pg_trigger t
        JOIN pg_class c ON t.tgrelid = c.oid
        JOIN pg_proc p ON t.tgfoid = p.oid
        WHERE c.relname = 't_discs'
        AND p.proname = 'fn_try_publish_product_disc_q'
        AND t.tgisinternal = false
    LOOP
        RAISE NOTICE 'Dropping trigger % that uses fn_try_publish_product_disc_q', trigger_name;
        EXECUTE 'DROP TRIGGER IF EXISTS ' || trigger_name || ' ON t_discs';
    END LOOP;
END $$;

-- 5. Also check for any triggers that might create publish_product_disc tasks
DROP TRIGGER IF EXISTS tr_publish_product_disc ON t_discs;
DROP TRIGGER IF EXISTS trg_publish_product_disc ON t_discs;

-- 6. Check for generic "queue" triggers that might create tasks
DROP TRIGGER IF EXISTS tr_queue_publish ON t_discs;
DROP TRIGGER IF EXISTS trg_queue_publish ON t_discs;
DROP TRIGGER IF EXISTS trigger_queue_publish ON t_discs;

-- Final check - show remaining triggers after cleanup
DO $$
DECLARE
    trigger_record RECORD;
    trigger_count INTEGER := 0;
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE 'Remaining triggers on t_discs table after cleanup:';
    
    FOR trigger_record IN 
        SELECT 
            t.tgname as trigger_name,
            t.tgenabled as enabled,
            p.proname as function_name
        FROM pg_trigger t
        JOIN pg_class c ON t.tgrelid = c.oid
        JOIN pg_proc p ON t.tgfoid = p.oid
        WHERE c.relname = 't_discs'
        AND t.tgisinternal = false
        ORDER BY t.tgname
    LOOP
        trigger_count := trigger_count + 1;
        RAISE NOTICE '  %: % (Function: %, Enabled: %)', 
            trigger_count,
            trigger_record.trigger_name, 
            trigger_record.function_name,
            CASE trigger_record.enabled 
                WHEN 'O' THEN 'YES' 
                ELSE 'NO' 
            END;
    END LOOP;
    
    IF trigger_count = 0 THEN
        RAISE NOTICE '  No triggers found on t_discs table';
    END IF;
END $$;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '✅ Old publish trigger cleanup completed!';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Test by setting a disc ready_new = TRUE and check if duplicate tasks are created';
    RAISE NOTICE '2. If duplicates still occur, check the remaining triggers above';
    RAISE NOTICE '3. The new workflow (check_if_disc_ready_to_publish) should be the only way publish_disc tasks are created';
END $$;
