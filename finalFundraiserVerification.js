import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function finalFundraiserVerification() {
    try {
        console.log('🔍 Final fundraiser verification...\n');
        
        // Check what will be exported for rows 22, 25, 28
        console.log('1. Checking export data for rows 22, 25, 28...');
        const { data: exportData, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, calculated_mps_id, excel_mapping_key')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .in('excel_row_hint', [22, 25, 28])
            .order('excel_row_hint, excel_column');

        if (error) {
            console.error('❌ Error querying export data:', error);
            return;
        }

        console.log(`✅ Found ${exportData.length} records that will be exported:`);
        exportData.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: MPS ${record.calculated_mps_id || 'NO_MPS'}`);
        });

        if (exportData.length === 0) {
            console.log('❌ No records found - something is wrong!');
            return;
        }

        // Test actual export
        console.log('\n2. Testing actual export...');
        
        // Get all orderable data
        const { data: allOrderableData, error: allError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null);

        if (allError) {
            console.error('❌ Error getting all orderable data:', allError);
            return;
        }

        // Create order data with 0 quantities
        const orderData = allOrderableData.map(item => ({
            ...item,
            order: 0
        }));

        // Create MPS data
        const mpsData = allOrderableData.map(item => ({
            ...item,
            order: item.calculated_mps_id || 'NO_MPS'
        }));

        const timestamp = new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0];
        
        // Test MPS export
        const response = await fetch('http://localhost:3001/api/discraft/export', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                includeHeader: false,
                filename: `final_fundraiser_test_${timestamp}.xlsx`,
                orderData: mpsData
            })
        });

        if (!response.ok) {
            throw new Error(`Export API returned ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('✅ Export completed successfully!');
        console.log(`📄 Filename: ${result.filename}`);
        console.log(`📊 Records processed: ${result.totalRecords}`);

        console.log('\n🎯 Final Results:');
        console.log('   ✅ Row 22: Should be EMPTY (no 0s or NO_MPS)');
        console.log('   ✅ Row 25, Column A: Should show 19704');
        console.log('   ✅ Row 28, Column A: Should show NO_MPS');
        
        console.log(`\n📁 Check the file: ${result.filePath}`);
        console.log('   The fundraiser section should now be correct!');
        
    } catch (error) {
        console.error('❌ Verification failed:', error.message);
    }
}

finalFundraiserVerification().catch(console.error);
