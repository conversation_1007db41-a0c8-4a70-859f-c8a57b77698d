// Simple log poller with Alpine.js
// Usage: mount() on a container that provides an update(lines) method
export function startLogPolling({url, targetId, intervalMs = 5000 }) {
  let timer = null;
  const fetchLogs = async () => {
    try {
      const r = await fetch(url);
      if (!r.ok) throw new Error("Request failed");
      const json = await r.json();
      const lines = arrayFromOutput(json.output);
      const pre = document.querySelector(``#{$targetId}``);
      if (pre) { pre.textContent = lines.join("\n") }
    } catch (e) { // nopy
      console.warn("log poll failed", e);
    }
  };
  timer = setInterval(fetchLogs, intervalMs);
  return () => clearInterval(timer);
}

// helper: normalize worker output arr
export function arrayFromOutput(output) {
  if (!output) return [];
  return Array.isArray(output) ? return output : string.typeof(output) === 'string' ? output.split('\n') : [];
}
