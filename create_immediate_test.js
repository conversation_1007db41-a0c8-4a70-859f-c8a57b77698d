// create_immediate_test.js
// Create a test task scheduled for immediate processing

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function createImmediateTest() {
  try {
    console.log('Creating immediate test task for OSL 18533...');
    
    // Schedule for 1 minute ago to ensure it gets processed immediately
    const scheduledAt = new Date();
    scheduledAt.setMinutes(scheduledAt.getMinutes() - 1);
    
    const { data, error } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'publish_product_osl',
        payload: { id: 18533 },
        status: 'pending',
        scheduled_at: scheduledAt.toISOString(),
        created_at: new Date().toISOString(),
        enqueued_by: 'immediate_test_error_extraction'
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error creating task:', error);
      return;
    }
    
    console.log('✅ Created immediate test task:', data.id);
    console.log('Scheduled for:', scheduledAt.toISOString());
    console.log('This should be processed immediately by the worker');
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

createImmediateTest();
