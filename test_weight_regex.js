import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testWeightRegex() {
  try {
    // Check the specific record first
    const { data: record, error } = await supabase
      .from('t_sdasins')
      .select('id, notes, parsed_brand, parsed_min_weight, parsed_max_weight')
      .eq('id', 61554)
      .single();
    
    if (error) {
      console.error('Error:', error);
      return;
    }
    
    console.log('Record 61554:');
    console.log('Notes:', record.notes);
    console.log('Parsed brand:', record.parsed_brand || 'NULL');
    console.log('Parsed weight:', (record.parsed_min_weight || 'NULL') + '-' + (record.parsed_max_weight || 'NULL'));
    console.log('');
    
    const testString = record.notes;
    
    console.log('Testing weight regex patterns on:', testString);
    console.log('');
    
    // Current regex from parser (3 digits only)
    const currentRegex = /(\d{3})-(\d{3})g?/;
    const currentMatch = testString.match(currentRegex);
    console.log('Current regex (3 digits only):', currentRegex);
    console.log('Match result:', currentMatch);
    
    if (currentMatch) {
      console.log('Min weight:', parseInt(currentMatch[1]));
      console.log('Max weight:', parseInt(currentMatch[2]));
    }
    console.log('');
    
    // Test broader patterns
    const patterns = [
      { name: '3 digits only', regex: /(\d{3})-(\d{3})/ },
      { name: '2-3 digits', regex: /(\d{2,3})-(\d{2,3})/ },
      { name: 'Any digits', regex: /(\d+)-(\d+)/ },
      { name: '3 digits with gram', regex: /(\d{3})-(\d{3})\s*gram/i },
      { name: '2-3 digits with gram', regex: /(\d{2,3})-(\d{2,3})\s*gram/i }
    ];
    
    patterns.forEach(pattern => {
      const match = testString.match(pattern.regex);
      console.log(`${pattern.name} (${pattern.regex}):`, match ? `${match[1]}-${match[2]}` : 'No match');
    });
    
    // Check why this record wasn't parsed - is it because parsed_brand is null?
    console.log('\nChecking why record wasn\'t parsed...');
    
    // Check if this record would be selected by our parsing query
    const { data: wouldBeParsed, error: parseError } = await supabase
      .from('t_sdasins')
      .select('id, notes')
      .eq('id', 61554)
      .not('notes', 'is', null)
      .not('notes', 'like', 'XXXX%')
      .is('parsed_brand', null);
    
    if (parseError) {
      console.error('Parse check error:', parseError);
    } else {
      console.log('Would this record be selected for parsing?', wouldBeParsed.length > 0 ? 'YES' : 'NO');
    }
    
    // Find other records with similar weight patterns that haven't been parsed
    console.log('\nLooking for other unparsed records with weight ranges...');
    
    const { data: unparsedWithWeights, error: unparsedError } = await supabase
      .from('t_sdasins')
      .select('id, notes')
      .not('notes', 'is', null)
      .not('notes', 'like', 'XXXX%')
      .is('parsed_brand', null)
      .like('notes', '%-%')
      .limit(10);
    
    if (!unparsedError && unparsedWithWeights) {
      console.log('Sample unparsed records with potential weight ranges:');
      unparsedWithWeights.forEach(record => {
        const weightMatch = record.notes.match(/(\d{2,3})-(\d{2,3})/);
        if (weightMatch) {
          console.log(`  ID ${record.id}: ${record.notes}`);
          console.log(`    Weight found: ${weightMatch[1]}-${weightMatch[2]}`);
        }
      });
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

testWeightRegex();
