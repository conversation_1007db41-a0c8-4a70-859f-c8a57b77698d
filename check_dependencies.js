// Check if required tables and views exist for B2F view
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkTable(tableName) {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);
    
    if (error) {
      console.log(`❌ ${tableName}: ${error.message}`);
      return false;
    } else {
      console.log(`✅ ${tableName}: exists`);
      return true;
    }
  } catch (err) {
    console.log(`❌ ${tableName}: ${err.message}`);
    return false;
  }
}

console.log('Checking required dependencies for v_b2f_pick_slim...\n');

await checkTable('t_discs');
await checkTable('t_order_sheet_lines');
await checkTable('v_stats_by_osl');

console.log('\nTesting sample query...');

try {
  const { data, error } = await supabase
    .from('t_discs')
    .select(`
      g_pull,
      location,
      sold_date,
      t_order_sheet_lines!inner(g_code)
    `)
    .eq('location', 'BS')
    .is('sold_date', null)
    .limit(5);
  
  if (error) {
    console.log('❌ Sample query failed:', error.message);
  } else {
    console.log('✅ Sample query successful, found', data.length, 'BS discs');
    console.log('Sample data:', data[0]);
  }
} catch (err) {
  console.log('❌ Sample query error:', err.message);
}
