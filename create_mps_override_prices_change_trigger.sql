-- Function to enqueue a task for MPS override price changes
CREATE OR REPLACE FUNCTION fn_enqueue_mps_override_prices_changed()
RETURNS TRIGGER AS $$
DECLARE
    retail_price_changed BOOLEAN := FALSE;
    msrp_price_changed BOOLEAN := FALSE;
    mps_description TEXT;
BEGIN
    -- Check if val_override_retail_price has changed
    IF OLD.val_override_retail_price IS DISTINCT FROM NEW.val_override_retail_price THEN
        retail_price_changed := TRUE;
    END IF;
    
    -- Check if val_override_msrp has changed
    IF OLD.val_override_msrp IS DISTINCT FROM NEW.val_override_msrp THEN
        msrp_price_changed := TRUE;
    END IF;
    
    -- Only enqueue a task if at least one override price has changed
    IF retail_price_changed OR msrp_price_changed THEN
        
        -- Get MPS description for logging
        SELECT CONCAT(m.mold, ' ', p.plastic, ' ', s.stamp)
        INTO mps_description
        FROM t_molds m, t_plastics p, t_stamps s
        WHERE m.id = NEW.mold_id 
          AND p.id = NEW.plastic_id 
          AND s.id = NEW.stamp_id;
        
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'mps_override_prices_changed_so_update_shopify',
            jsonb_build_object(
                'id', NEW.id,
                'retail_price_changed', retail_price_changed,
                'msrp_price_changed', msrp_price_changed,
                'old_override_retail_price', OLD.val_override_retail_price,
                'new_override_retail_price', NEW.val_override_retail_price,
                'old_override_msrp', OLD.val_override_msrp,
                'new_override_msrp', NEW.val_override_msrp,
                'mps_description', mps_description
            ),
            'pending',
            NOW(),
            NOW()
        );
        
        -- Log the change
        IF retail_price_changed AND msrp_price_changed THEN
            RAISE NOTICE 'Enqueued mps_override_prices_changed_so_update_shopify task for MPS ID % (%) - retail price changed from % to % and MSRP changed from % to %', 
                NEW.id, mps_description, OLD.val_override_retail_price, NEW.val_override_retail_price, OLD.val_override_msrp, NEW.val_override_msrp;
        ELSIF retail_price_changed THEN
            RAISE NOTICE 'Enqueued mps_override_prices_changed_so_update_shopify task for MPS ID % (%) - retail price changed from % to %', 
                NEW.id, mps_description, OLD.val_override_retail_price, NEW.val_override_retail_price;
        ELSIF msrp_price_changed THEN
            RAISE NOTICE 'Enqueued mps_override_prices_changed_so_update_shopify task for MPS ID % (%) - MSRP changed from % to %', 
                NEW.id, mps_description, OLD.val_override_msrp, NEW.val_override_msrp;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trg_enqueue_mps_override_prices_changed ON t_mps;

CREATE TRIGGER trg_enqueue_mps_override_prices_changed
AFTER UPDATE OF val_override_retail_price, val_override_msrp ON t_mps
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_mps_override_prices_changed();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger trg_enqueue_mps_override_prices_changed has been created on t_mps override price updates.';
END $$;
