import fetch from 'node-fetch';

// Shopify Admin API credentials
const shopifyEndpoint = 'https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/graphql.json';
const shopifyAccessToken = '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';

// Shopify GraphQL mutation to create a test product
const productMutation = `
  mutation {
    productCreate(input: {
      title: "Test Product from API",
      bodyHtml: "<strong>This is a test product created via API.</strong>",
      vendor: "Test Vendor",
      productType: "Disc Golf",
      tags: ["API Test", "Disc Golf"],
      variants: [
        {
          price: "29.99",
          sku: "TEST-API-001",
          weight: 175,
          weightUnit: GRAMS
        }
      ]
    }) {
      product {
        id
        title
        handle
      }
      userErrors {
        field
        message
      }
    }
  }
`;

// Function to create the product
async function createTestProduct() {
  try {
    const response = await fetch(shopifyEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken,
      },
      body: JSON.stringify({ query: productMutation }),
    });

    const json = await response.json();
    if (json.errors) {
      console.error('❌ Shopify API Errors:', json.errors);
    } else if (json.data.productCreate.userErrors.length > 0) {
      console.error('⚠️ User Errors:', json.data.productCreate.userErrors);
    } else {
      console.log('✅ Product Created Successfully:', json.data.productCreate.product);
    }
  } catch (error) {
    console.error('❌ Error creating product:', error);
  }
}

// Run the function
createTestProduct();
