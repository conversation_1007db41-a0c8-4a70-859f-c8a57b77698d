import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM3ODY1NDEsImV4cCI6MjA0OTM2MjU0MX0.MtBXMZt7rCqXd6c9clhrNoVtfOxEilX2C8oboMceOGg';
const supabase = createClient(supabaseUrl, supabaseKey);

const checkDiscOslMatch = async () => {
  try {
    // Check if the disc exists
    console.log('Checking if disc with ID 421349 exists...');
    const { count: discCount, error: discCountError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .eq('id', 421349);

    if (discCountError) {
      console.error('Error checking disc existence:', discCountError);
      return;
    }

    console.log(`Disc with ID 421349 exists: ${discCount > 0}`);

    if (discCount > 0) {
      // Get disc record with all fields
      const { data: discRecord, error: discError } = await supabase
        .from('t_discs')
        .select('*')
        .eq('id', 421349)
        .single();

      if (discError) {
        console.error('Error fetching disc record:', discError);
      } else {
        console.log('Disc Record:', discRecord);
      }
    }

    // Check if the order sheet line exists
    console.log('\nChecking if order sheet line with ID 16890 exists...');
    const { count: oslCount, error: oslCountError } = await supabase
      .from('t_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .eq('id', 16890);

    if (oslCountError) {
      console.error('Error checking order sheet line existence:', oslCountError);
      return;
    }

    console.log(`Order sheet line with ID 16890 exists: ${oslCount > 0}`);

    if (oslCount > 0) {
      // Get order sheet line record with all fields
      const { data: oslRecord, error: oslError } = await supabase
        .from('t_order_sheet_lines')
        .select('*')
        .eq('id', 16890)
        .single();

      if (oslError) {
        console.error('Error fetching order sheet line record:', oslError);
      } else {
        console.log('Order Sheet Line Record:', oslRecord);
      }
    }

    // Check recent tasks
    console.log('\nChecking recent match_disc_to_osl tasks...');
    const { data: taskData, error: taskError } = await supabase
      .from('t_task_queue')
      .select('id, task_type, payload, status, result, created_at')
      .eq('task_type', 'match_disc_to_osl')
      .eq('status', 'completed')
      .contains('payload', { id: 421349 })
      .order('created_at', { ascending: false })
      .limit(5);

    if (taskError) {
      console.error('Error fetching task queue data:', taskError);
      return;
    }

    if (!taskData || taskData.length === 0) {
      console.log('No completed match_disc_to_osl tasks found for disc ID 421349');
    } else {
      console.log('Recent match_disc_to_osl tasks:');
      taskData.forEach(task => {
        console.log(`Task ID: ${task.id}`);
        console.log(`Created: ${task.created_at}`);
        console.log(`Payload: ${JSON.stringify(task.payload)}`);
        console.log(`Result: ${JSON.stringify(task.result)}`);
        console.log('---');
      });
    }

    // If both records exist, manually test the matching criteria
    if (discCount > 0 && oslCount > 0) {
      console.log('\nManually testing matching criteria...');

      // Get the necessary fields for matching
      const { data: discFields, error: discFieldsError } = await supabase
        .from('t_discs')
        .select('mps_id, weight, color_id, sold_date')
        .eq('id', 421349)
        .single();

      const { data: oslFields, error: oslFieldsError } = await supabase
        .from('t_order_sheet_lines')
        .select('mps_id, min_weight, max_weight, color_id')
        .eq('id', 16890)
        .single();

      if (discFieldsError) {
        console.error('Error fetching disc fields:', discFieldsError);
        return;
      }

      if (oslFieldsError) {
        console.error('Error fetching OSL fields:', oslFieldsError);
        return;
      }

      console.log('Disc matching fields:', discFields);
      console.log('OSL matching fields:', oslFields);

      // Check matching criteria
      const mpsMatch = discFields.mps_id === oslFields.mps_id;
      const weightMatch = discFields.weight >= oslFields.min_weight && discFields.weight <= oslFields.max_weight;
      const colorMatch = oslFields.color_id === 23 || discFields.color_id === oslFields.color_id;
      const notSold = discFields.sold_date === null;

      console.log(`MPS Match: ${mpsMatch} (Disc MPS: ${discFields.mps_id}, OSL MPS: ${oslFields.mps_id})`);
      console.log(`Weight Match: ${weightMatch} (Disc Weight: ${discFields.weight}, OSL Min: ${oslFields.min_weight}, OSL Max: ${oslFields.max_weight})`);
      console.log(`Color Match: ${colorMatch} (Disc Color: ${discFields.color_id}, OSL Color: ${oslFields.color_id})`);
      console.log(`Not Sold: ${notSold} (Sold Date: ${discFields.sold_date})`);

      console.log(`Should Match: ${mpsMatch && weightMatch && colorMatch && notSold}`);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

checkDiscOslMatch();
