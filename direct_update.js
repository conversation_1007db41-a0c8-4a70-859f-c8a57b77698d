import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with your actual credentials
const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM3ODY1NDEsImV4cCI6MjA0OTM2MjU0MX0.MtBXMZt7rCqXd6c9clhrNoVtfOxEilX2C8oboMceOGg';
const supabase = createClient(supabaseUrl, supabaseKey);

const directUpdate = async () => {
  try {
    console.log('Attempting to directly update disc 421349...');
    
    // Execute a raw SQL query to update the disc
    const { data, error } = await supabase.rpc('update_disc_osl', {
      disc_id_param: 421349,
      osl_id_param: 16890
    });
    
    if (error) {
      console.error('Error executing RPC:', error);
      
      // Try a direct SQL query instead
      console.log('Trying direct SQL query...');
      try {
        const { data: sqlData, error: sqlError } = await supabase.rpc('execute_sql', {
          sql_query: "UPDATE t_discs SET order_sheet_line_id = 16890 WHERE id = 421349 RETURNING id, order_sheet_line_id"
        });
        
        if (sqlError) {
          console.error('Error executing SQL:', sqlError);
        } else {
          console.log('SQL query result:', sqlData);
        }
      } catch (e) {
        console.error('Error with SQL execution:', e.message);
        
        // Last resort: try a direct update
        console.log('Trying direct update...');
        const { data: updateData, error: updateError } = await supabase
          .from('t_discs')
          .update({ order_sheet_line_id: 16890 })
          .eq('id', 421349)
          .select();
          
        if (updateError) {
          console.error('Error with direct update:', updateError);
        } else {
          console.log('Direct update result:', updateData);
        }
      }
    } else {
      console.log('RPC result:', data);
    }
  } catch (error) {
    console.error('Unexpected error:', error);
  }
};

directUpdate();
