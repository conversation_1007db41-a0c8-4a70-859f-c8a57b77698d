import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function debugMpsExportData() {
    try {
        console.log('🔍 Debugging MPS export data mapping...\n');
        
        // Replicate the exact logic from our export test
        console.log('1. Getting orderable data (same as export)...');
        let allOrderableData = [];
        let from = 0;
        const pageSize = 1000;
        
        while (true) {
            const { data: batch, error: orderableError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
                .eq('is_orderable', true)
                .not('excel_mapping_key', 'is', null)
                .range(from, from + pageSize - 1)
                .order('excel_mapping_key');

            if (orderableError) {
                throw new Error(`Failed to query orderable data: ${orderableError.message}`);
            }

            if (batch.length === 0) break;
            
            allOrderableData = allOrderableData.concat(batch);
            from += pageSize;
            
            if (batch.length < pageSize) break;
        }

        console.log(`✅ Got ${allOrderableData.length} orderable records`);

        // Check records after line 332 specifically
        const afterLine332 = allOrderableData.filter(item => item.excel_row_hint > 332);
        console.log(`✅ Found ${afterLine332.length} records after line 332`);

        // Show sample of what we're getting
        console.log('\n2. Sample records after line 332 from base query:');
        afterLine332.slice(0, 10).forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: calculated_mps_id=${record.calculated_mps_id} (type: ${typeof record.calculated_mps_id})`);
        });

        // Now get view data
        console.log('\n3. Getting view data...');
        let viewOrderData = [];
        from = 0;
        
        while (true) {
            const { data: batch, error: viewError } = await supabase
                .from('v_stats_by_osl_discraft')
                .select('excel_mapping_key, "order", mold_name, plastic_name, is_currently_available')
                .not('excel_mapping_key', 'is', null)
                .range(from, from + pageSize - 1);

            if (viewError) {
                throw new Error(`Failed to query view order data: ${viewError.message}`);
            }

            if (batch.length === 0) break;
            
            viewOrderData = viewOrderData.concat(batch);
            from += pageSize;
            
            if (batch.length < pageSize) break;
        }

        console.log(`✅ Got ${viewOrderData.length} view records`);

        // Create the mapping (same as export logic)
        const orderMap = {};
        viewOrderData.forEach(item => {
            orderMap[item.excel_mapping_key] = {
                order: item.order || 0,
                mold_name: item.mold_name,
                plastic_name: item.plastic_name,
                is_currently_available: item.is_currently_available
            };
        });

        // Combine data (same as export logic)
        const orderableData = allOrderableData.map(item => {
            const orderInfo = orderMap[item.excel_mapping_key] || {};
            return {
                excel_mapping_key: item.excel_mapping_key,
                excel_column: item.excel_column,
                excel_row_hint: item.excel_row_hint,
                order: orderInfo.order || 0,
                calculated_mps_id: item.calculated_mps_id,  // This should preserve the MPS ID
                mold_name: orderInfo.mold_name || 'Unknown',
                plastic_name: orderInfo.plastic_name || 'Unknown',
                is_currently_available: orderInfo.is_currently_available || false
            };
        });

        // Check what we have after mapping
        const mappedAfter332 = orderableData.filter(item => item.excel_row_hint > 332);
        console.log(`\n4. After mapping - ${mappedAfter332.length} records after line 332:`);
        mappedAfter332.slice(0, 10).forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: calculated_mps_id=${record.calculated_mps_id} (type: ${typeof record.calculated_mps_id})`);
        });

        // Now create MPS data (same as export logic)
        console.log('\n5. Creating MPS data for export...');
        const mpsData = orderableData.map(item => ({
            ...item,
            order: item.calculated_mps_id || 'NO_MPS' // This is where the issue might be
        }));

        // Check MPS data after line 332
        const mpsAfter332 = mpsData.filter(item => item.excel_row_hint > 332);
        console.log(`✅ MPS data - ${mpsAfter332.length} records after line 332:`);
        mpsAfter332.slice(0, 10).forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: order=${record.order} (original calculated_mps_id=${record.calculated_mps_id})`);
        });

        console.log('\n🎉 Debug completed!');
        
    } catch (error) {
        console.error('❌ Debug failed:', error.message);
    }
}

debugMpsExportData().catch(console.error);
