import XLSX from 'xlsx';
import path from 'path';

async function debugMcBethNewImport() {
  try {
    console.log('🔍 Debugging McBeth NEW import process...\n');
    
    const filePath = path.join(process.cwd(), 'data', 'external data', 'discraftstock.xlsx');
    console.log(`📁 Reading file: ${filePath}`);
    
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    console.log(`📊 Sheet: ${sheetName}`);
    
    // Check rows 269-279 specifically for McBeth NEW products
    for (let row = 269; row <= 279; row++) {
      console.log(`\n📋 Row ${row}:`);
      
      // Read all columns for this row
      const rowData = {};
      for (let col = 0; col <= 30; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row - 1, c: col });
        const cell = worksheet[cellAddress];
        if (cell && cell.v !== null && cell.v !== undefined && cell.v !== '') {
          rowData[`col_${col}`] = cell.v;
        }
      }
      
      // Check if this is a McBeth NEW product
      const lineType = rowData.col_1 || ''; // Column B
      const model = rowData.col_4 || '';     // Column E
      
      if (lineType.includes('McBeth') && model.includes('NEW')) {
        console.log(`   🎯 FOUND McBeth NEW: "${lineType}" | "${model}"`);
        
        // Check vendor description columns (12-26)
        const vendorDesc = (rowData.col_12 || rowData.col_13 || rowData.col_14 || rowData.col_15 || 
                          rowData.col_16 || rowData.col_17 || rowData.col_18 || rowData.col_19 || 
                          rowData.col_20 || rowData.col_21 || rowData.col_22 || rowData.col_23 || 
                          rowData.col_24 || rowData.col_25 || rowData.col_26 || '').toString();
        
        console.log(`   📝 Vendor Description: "${vendorDesc}"`);
        
        // Check specifically column T (column 20)
        const colT = rowData.col_19 || ''; // Column T (0-indexed: 19)
        console.log(`   📍 Column T (19): "${colT}"`);
        
        // Check all columns to see where the vendor description is
        console.log(`   🔍 All columns with data:`);
        Object.entries(rowData).forEach(([colKey, value]) => {
          const colIndex = parseInt(colKey.replace('col_', ''));
          const colLetter = String.fromCharCode(65 + colIndex);
          console.log(`      ${colLetter} (${colIndex}): "${value}"`);
          
          if (value.toString().toLowerCase().includes('white') || 
              value.toString().toLowerCase().includes('blank') ||
              value.toString().toLowerCase().includes('stamp')) {
            console.log(`      ⭐ POTENTIAL VENDOR DESC: ${colLetter} (${colIndex}): "${value}"`);
          }
        });
        
        // Test the vendor description logic
        if (vendorDesc.includes('White/Blank Top/Bottom stamp')) {
          console.log(`   ✅ Vendor description logic would trigger: "Dye Line Blank Top Bottom"`);
        } else {
          console.log(`   ❌ Vendor description logic would NOT trigger`);
          console.log(`   📝 Looking for: "White/Blank Top/Bottom stamp"`);
          console.log(`   📝 Found: "${vendorDesc}"`);
        }
      }
    }
    
    console.log('\n🎉 Debug completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

debugMcBethNewImport().catch(console.error);
