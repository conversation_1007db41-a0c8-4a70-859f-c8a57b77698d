<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Galen - gt_todo</title>
  <style>
    body{font-family:Segoe UI,Arial,sans-serif;margin:0;padding:20px;background:#f5f5f5;color:#333}
    header{background:#2c3e50;color:#fff;padding:16px 20px;border-radius:6px;margin-bottom:16px}
    h1{margin:0;font-size:22px}
    .toolbar{display:flex;gap:8px;align-items:center;margin:10px 0}
    .card{background:#fff;border-radius:6px;box-shadow:0 2px 6px rgba(0,0,0,.08);padding:16px}
    table{width:100%;border-collapse:collapse;margin-top:10px;font-size:14px;table-layout:fixed}
    th,td{border:1px solid #e5e5e5;padding:8px;text-align:left}
    th{background:#2c3e50;color:#fff;cursor:pointer;user-select:none;position:sticky;top:0}
    thead .filters th{background:#f0f2f5;position:sticky;top:32px}
    tr:nth-child(even){background:#fafafa}
    input,select,textarea{border:1px solid #ccc;border-radius:4px;padding:6px;font-size:14px}
    button{background:#3498db;border:none;color:#fff;border-radius:4px;padding:8px 10px;cursor:pointer}
    button.secondary{background:#6c757d}
    button.danger{background:#e74c3c}
    /* wrap content so table never exceeds page width */
    th, td { white-space: normal; overflow-wrap: anywhere; word-break: break-word; }
    .row-actions button { white-space: nowrap; font-size:11px; padding:2px 2px; }

    .row-actions{display:flex;gap:4px;flex-wrap:wrap;align-items:flex-start}
    .row-actions .full-line{flex-basis:100%}
    .row-actions .first-line{flex-wrap:nowrap;display:flex;gap:4px;width:100%}
    .row-actions .delete-wrap{display:flex;justify-content:center;width:100%; margin-bottom:0}
    .link-open{background:transparent;border:none;color:#2c3e50;padding:0 4px;line-height:1}
    .link-open:hover{background:#eef2f7}
    .link-open svg{width:14px;height:14px;display:block}
    .status-indicator{display:inline-block;width:12px;height:12px;border-radius:50%;margin-right:5px}
    .status-running{background-color:#2ecc71}
    .status-stopped{background-color:#e74c3c}


    .inline-input{width:100%;box-sizing:border-box}
    .status{font-size:12px;color:#6c757d;margin-left:auto}
    .flash-save{background-color:#d4edda !important; transition: background-color 1.2s ease-out}
    tr.row-soon > td { background-color:#eaffea !important; }
    tr.row-past > td { background-color:#ffecec !important; }
    /* duplicate title styling */
    td.dup-title, td.dup-title * { background-color:#f3e6ff !important; }
    /* selection column */
    th.sel, td.sel { width:34px; min-width:34px; max-width:34px; text-align:center }
    input.row-select{ width:16px; height:16px }

  </style>
</head>
<body>
  <header>
    <h1>gt_todo - CRUD</h1>
  </header>


  <div class="card">
    <div class="toolbar">
      <button id="refreshBtn">Refresh</button>
      <div id="workerMini" style="display:flex;align-items:center;gap:8px;margin-left:auto">
        <span class="status-indicator status-stopped" id="workerMiniIndicator"></span>
        <span id="workerMiniStatus" style="font-weight:600;color:#2c3e50">Stopped</span>
        <button id="workerMiniRunDaemon" class="secondary" title="Run worker as daemon" disabled>Run Daemon</button>
      </div>

      <button id="addBtn">Add New</button>
      <label>Order by
        <select id="orderBy">
          <option value="id">id</option>
          <option value="scheduled_at" selected>scheduled_at</option>
        </select>
      </label>
          <button id="selectAllBtn" class="secondary">Select All</button>
          <button id="deselectAllBtn" class="secondary">Deselect All</button>

      <label>
        Asc <input type="checkbox" id="ascChk" checked />
      </label>
      <span class="status" id="status"></span>
    </div>

    <div style="overflow-x:auto">
      <table id="todoTable">
        <thead>
          <tr>
            <th class="sel"><input id="selectAll" type="checkbox" title="Select all" /></th>
            <th data-col="id" style="width:60px;min-width:60px;max-width:60px;">ID</th>
            <th data-col="title" style="width:26%;min-width:160px;">Title</th>
            <th data-col="status" style="width:8%;min-width:90px;">Status</th>
            <th data-col="notes" style="width:30%;min-width:200px;">Notes</th>
            <th data-col="link_url" style="width:14%;min-width:140px;">Link</th>
            <th data-col="scheduled_at" style="width:12%;min-width:150px;">Scheduled</th>
            <th style="width:10%;min-width:160px;">Actions</th>
          </tr>
          <tr class="filters">
            <th class="sel"></th>
            <th><input id="filter_id" class="inline-input" placeholder="filter" /></th>
            <th><input id="filter_title" class="inline-input" placeholder="filter" /></th>
            <th><input id="filter_status" class="inline-input" placeholder="filter" /></th>
            <th><input id="filter_notes" class="inline-input" placeholder="filter" /></th>
            <th><input id="filter_link_url" class="inline-input" placeholder="filter" /></th>
            <th><input id="filter_scheduled_at" class="inline-input" placeholder="filter" /></th>
            <th><button id="clearFilters" class="secondary" title="Clear filters">Clear</button></th>
          </tr>
        </thead>
        <tbody id="tbody">
          <tr><td colspan="8" style="text-align:center;color:#6c757d">Click Refresh to load records…</td></tr>
        </tbody>


      </table>
    </div>

  </div>

  <script>
    const statusEl = document.getElementById('status');
    const tbody = document.getElementById('tbody');
    const orderBySel = document.getElementById('orderBy');
    const ascChk = document.getElementById('ascChk');

    let rows = [];

    document.getElementById('refreshBtn').addEventListener('click', load);
    document.getElementById('addBtn').addEventListener('click', addNewRow);

    function setStatus(msg){ statusEl.textContent = msg || ''; }
    // Selection controls
    const selectAllCtrl = document.getElementById('selectAll');
    const selectAllBtn = document.getElementById('selectAllBtn');
    // Mini worker status (reuses admin dashboard endpoint semantics)
    function updateMiniIndicator(status){
      const ind = document.getElementById('workerMiniIndicator');
      const txt = document.getElementById('workerMiniStatus');
      if(!ind||!txt) return;
      const runBtn = document.getElementById('workerMiniRunDaemon');
      if(status==='running'){
        ind.classList.remove('status-stopped'); ind.classList.add('status-running');
        txt.textContent='Running';
        if(runBtn) runBtn.disabled = true;
      }else{
        ind.classList.remove('status-running'); ind.classList.add('status-stopped');
        txt.textContent='Stopped';
        if(runBtn) runBtn.disabled = false;
      }
    }

    async function refreshMiniWorker(){
      try{
        const res = await fetch('/api/worker/status');
        const data = await res.json();
        updateMiniIndicator(data.status||'stopped');
      }catch(e){ console.warn('mini worker status failed', e); }
    }
    // initial fetch
    refreshMiniWorker();

    // Run daemon from mini control
    document.getElementById('workerMiniRunDaemon')?.addEventListener('click', async ()=>{
      const btn = document.getElementById('workerMiniRunDaemon');
      btn.disabled = true;
      try{
        const res = await fetch('/api/worker/start-daemon', { method:'POST' });
        const data = await res.json();
        if(!data.success) throw new Error(data.error||'Failed to start daemon');
        updateMiniIndicator('running');
      }catch(e){ alert('Error starting daemon: '+e.message); }
      finally{ /* keep disabled when running */ }
    });

    const deselectAllBtn = document.getElementById('deselectAllBtn');
    function getRowCheckboxes(){ return Array.from(document.querySelectorAll('#todoTable tbody input.row-select')); }
    function setAllSelected(val){ getRowCheckboxes().forEach(cb=>{ cb.checked = !!val; cb.dispatchEvent(new Event('change')); }); }
    selectAllBtn?.addEventListener('click', ()=> setAllSelected(true));
    deselectAllBtn?.addEventListener('click', ()=> setAllSelected(false));
    // Clear filters button
    const clearBtn = document.getElementById('clearFilters');
    clearBtn?.addEventListener('click', ()=>{
      Object.values(filters).forEach(inp=>{ if(inp) inp.value=''; });
      applyClientFilterAndSort();
    });

    selectAllCtrl?.addEventListener('change', (e)=> setAllSelected(e.target.checked));







    async function load(){
      setStatus('Loading…');
      tbody.innerHTML = '<tr><td colspan="8" style="text-align:center;color:#6c757d">Loading…</td></tr>';
      const q = new URLSearchParams({ orderBy: orderBySel.value, asc: ascChk.checked ? 'true' : 'false' });
      const res = await fetch(`/api/gt_todo?${q.toString()}`);
      if(!res.ok){ tbody.innerHTML = `<tr><td colspan="8" style="color:#e74c3c">HTTP ${res.status}</td></tr>`; setStatus(''); return; }
      const data = await res.json();
      if(!data.success){ tbody.innerHTML = `<tr><td colspan="8" style="color:#e74c3c">${data.error||'Error'}</td></tr>`; setStatus(''); return; }
      rows = data.records || [];
      applyClientFilterAndSort();
      setStatus(`Loaded ${rows.length} record(s)`);
    }


    function render(){
      tbody.innerHTML = '';
      // Always keep a draft row at top
      if (rows.length===0 || rows[0]?.id) {
        rows.unshift({ title:'', status:'', notes:'', scheduled_at: new Date().toISOString() });
      }
      rows.forEach(r => tbody.appendChild(renderRow(r)));
      try{ markDuplicateTitles(); }catch{}
      // Duplicate title marker: add class to title cells that are duplicates
      function markDuplicateTitles(){
        const rowsEls = Array.from(document.querySelectorAll('#todoTable tbody tr'));
        const counts = new Map();
        rowsEls.forEach(tr => {
          const td = tr.children[1]; if(!td) return; td.classList.remove('dup-title');
          const inp = td.querySelector('input,textarea');
          const v = (inp ? inp.value : td.textContent || '').trim().toLowerCase();
          if(!v) return;
          counts.set(v, (counts.get(v)||0)+1);
        });
        rowsEls.forEach(tr => {
          const td = tr.children[1]; if(!td) return;
          const inp = td.querySelector('input,textarea');
          const v = (inp ? inp.value : td.textContent || '').trim().toLowerCase();
          if(!v) return;
          if ((counts.get(v)||0) > 1) td.classList.add('dup-title');
        });
      }

    }

    function renderRow(r){
      const tr = document.createElement('tr'); tr.dataset.rowId = r.id || 'draft';

      // highlight by time
      try{
        if (r.scheduled_at){
          const t = new Date(r.scheduled_at).getTime();
          const now = Date.now();
          if (t < now) tr.classList.add('row-past');
          else if (t <= now + 2*60*60*1000) tr.classList.add('row-soon');
        }
      }catch{}
      // selection checkbox cell
      const tdSel = document.createElement('td'); tdSel.className='sel';
      const cb = document.createElement('input'); cb.type='checkbox'; cb.className='row-select'; cb.checked = !!r._selected;
      cb.addEventListener('change', ()=>{ r._selected = cb.checked; });
      tdSel.appendChild(cb); tr.appendChild(tdSel);

      tr.appendChild(tdText(r.id));
      tr.appendChild(tdInput('title', r));
      tr.appendChild(tdInput('status', r));
      tr.appendChild(tdInput('notes', r));
      tr.appendChild(tdLink(r));
      // helper to render truncated link with open button
      function tdLink(r){
        const td = document.createElement('td');
        const wrap = document.createElement('div'); wrap.style.display='flex'; wrap.style.alignItems='center'; wrap.style.gap='6px';
        const inp = document.createElement('input'); inp.className='inline-input'; inp.value = r.link_url || ''; inp.placeholder='https://...';
        inp.addEventListener('blur', async()=>{ r.link_url = inp.value || null; await save(r); });
        inp.addEventListener('keydown', async (e)=>{
          if(e.key==='Enter'){ e.preventDefault(); inp.dataset.suppressNextBlur='1'; r.link_url = inp.value || null; await save(r); }
          if(e.key==='Tab'){ inp.dataset.suppressNextBlur='1'; r.link_url = inp.value || null; await save(r); }
        });
        const open = document.createElement('button'); open.className='link-open'; open.title='Open in new tab';
        open.innerHTML = '<svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 3h7v7"/><path d="M10 14L21 3"/><path d="M21 10v11H3V3h11"/></svg>';
        open.addEventListener('click', (e)=>{ e.preventDefault(); e.stopPropagation(); const url=(inp.value||'').trim(); if(url) window.open(url, '_blank'); });
        wrap.appendChild(inp);
        wrap.appendChild(open);
        td.appendChild(wrap);
        return td;
      }



      const schedTd = tdScheduled(r);
      tr.appendChild(schedTd);

      // row highlight updater
      function updateHighlight(){
        try{
          tr.classList.remove('row-soon');
          tr.classList.remove('row-past');
          if (r.scheduled_at){
            const t = new Date(r.scheduled_at).getTime();
            const now = Date.now();
            if (t < now) tr.classList.add('row-past');
            else if (t <= now + 2*60*60*1000) tr.classList.add('row-soon');
          }
        }catch{}
      }

      // scheduled_at cell with datetime-local input
      function tdScheduled(r){
        const td = document.createElement('td');
        const input = document.createElement('input');
        input.type = 'datetime-local';
        input.className = 'inline-input';
        function pad(n){ return String(n).padStart(2,'0'); }
        function toLocalValue(iso){
          if(!iso) return '';
          const d = new Date(iso);
          return `${d.getFullYear()}-${pad(d.getMonth()+1)}-${pad(d.getDate())}T${pad(d.getHours())}:${pad(d.getMinutes())}`;
        }
        input.value = toLocalValue(r.scheduled_at);

        const commit = async () => {
          lastDesiredFocus = { rowId: r.id || 'draft', field: 'scheduled_at' };
          const val = input.value;
          if(!val) { r.scheduled_at = null; }
          else {
            const newLocal = new Date(val);
            r.scheduled_at = newLocal.toISOString();
          }
          // if not yet saved, create first to get id
          if(!r.id){ await save(r); }
          // update scheduled_at via API
          if(r.id){
            const res = await fetch(`/api/gt_todo/${r.id}`, { method:'PUT', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ scheduled_at: r.scheduled_at }) });
            const data = await res.json();
            if(!data.success) throw new Error(data.error||'Failed to update scheduled_at');
          }
          // flash and refresh
          td.classList.add('flash-save'); setTimeout(()=> td.classList.remove('flash-save'), 900);
          updateHighlight();
          try{ markDuplicateTitles(); }catch{}
        };

        input.addEventListener('change', commit);
        input.addEventListener('blur', ()=>{ if (input.dataset.suppressNextBlur==='1'){ delete input.dataset.suppressNextBlur; return;} commit(); });
        input.addEventListener('keydown', async (e)=>{
          if(e.key==='Enter'){ e.preventDefault(); input.dataset.suppressNextBlur='1'; await commit(); }
          if(e.key==='Tab'){ input.dataset.suppressNextBlur='1'; setTimeout(commit,0); }
        });

        input.dataset.rowId = r.id || 'draft';
        input.dataset.field = 'scheduled_at';
        td.appendChild(input);
        return td;
      }

      // simple spinner while bumping
      const spinner = document.createElement('span'); spinner.textContent='⏳'; spinner.style.marginLeft='6px';

      const actions = document.createElement('td');
      actions.className = 'row-actions';

      const mkBtn = (label, ms) => {
        const b = document.createElement('button'); b.textContent = label; b.className='secondary';
        if (label === 'Tm') b.title = 'Set to 8:00 AM tomorrow (local time)';
        if (label === '2d') b.title = 'Set to 8:00 AM in 2 days (local time)';
        b.addEventListener('click', async ()=> {
          actions.appendChild(spinner);

          // Determine which rows to apply to
          const targets = rows.filter(x=>x._selected) ;
          const list = (targets.length>0 ? targets : [r]);
          if (list.length>1 && !confirm(`Apply "${label}" to ${list.length} rows?`)) { if (spinner.parentNode===actions) actions.removeChild(spinner); return; }

          const runFor = async (row) => {
            // compute effective offset; special-case 'Tm' → tomorrow 8am local; '2d' → 8am in 2 days
            let effectiveMs = ms;
            if (label === 'Tm' || label === '2d') {
              const now = new Date();
              const target = new Date(now);
              target.setDate(now.getDate() + (label === 'Tm' ? 1 : 2));
              target.setHours(8, 0, 0, 0);
              effectiveMs = target.getTime() - now.getTime();
            }
            const before = row.scheduled_at;
            await bumpSchedule(row, effectiveMs, schedTd);
            if (before !== row.scheduled_at) {
              setStatus(`Scheduled updated: ${before ? new Date(before).toLocaleString() : 'none'} → ${row.scheduled_at ? new Date(row.scheduled_at).toLocaleString() : 'none'}`);
            }
          };

          b.disabled = true;
          for (const row of list) { await runFor(row); }
          b.disabled = false;
          if (spinner.parentNode === actions) actions.removeChild(spinner);

          // update row highlight if current row changed
          try{
            tr.classList.remove('row-soon');
            tr.classList.remove('row-past');
            if (r.scheduled_at){
              const t = new Date(r.scheduled_at).getTime();
              const now = Date.now();
              if (t < now) tr.classList.add('row-past');
              else if (t <= now + 2*60*60*1000) tr.classList.add('row-soon');
            }
          }catch{}
        });
        return b;
      };



      // First line of buttons, no wrap
      const firstLine = document.createElement('div'); firstLine.className='first-line';
      firstLine.appendChild(mkBtn('1h', 60*60*1000));
      firstLine.appendChild(mkBtn('3h', 3*60*60*1000));
      firstLine.appendChild(mkBtn('Tm', 24*60*60*1000));
      firstLine.appendChild(mkBtn('2d', 2*24*60*60*1000));
      firstLine.appendChild(mkBtn('1w', 7*24*60*60*1000));
      firstLine.appendChild(mkBtn('1m', 30*24*60*60*1000));
      actions.appendChild(firstLine);

      // Second line: centered delete
      const delWrap = document.createElement('div'); delWrap.className='delete-wrap';
      const delBtn = document.createElement('button'); delBtn.textContent = 'Delete'; delBtn.className='danger'; delBtn.addEventListener('click',()=>del(r.id));
      delWrap.appendChild(delBtn);
      actions.appendChild(delWrap);

      tr.appendChild(actions);

      return tr;
    }

    // Style helper to make textarea look like a single-line input while allowing wrapping
    function styleTextareaAsSingleLine(el){
      el.style.resize = 'vertical';
      el.style.overflowY = 'hidden';
      el.style.lineHeight = '1.4';
      el.style.minHeight = '0';
      el.addEventListener('input', ()=>{
        el.style.height = 'auto';
        el.style.height = Math.min(el.scrollHeight, 40) + 'px';
      });
      // initialize height
      setTimeout(()=>{ el.dispatchEvent(new Event('input')); }, 0);
    }

    function tdText(text){ const td=document.createElement('td'); td.textContent = text ?? ''; return td; }


    function tdInput(field, r, type='text'){
      const td = document.createElement('td');
      const input = document.createElement(type==='number'?'input':(field==='notes'?'textarea':'input'));
      if(type==='number') input.type='number';
      input.value = r[field] ?? '';
      input.className = 'inline-input';
      if (field==='notes' && input.tagName.toLowerCase()==='textarea') { input.rows = 1; styleTextareaAsSingleLine(input); }

      const commit = async () => {
        // Update local model before save
        lastDesiredFocus = { rowId: r.id || 'draft', field: field };
        const val = (type==='number' && input.value!=='') ? Number(input.value) : input.value;
        r[field] = val;
        await save(r);
      };

      input.addEventListener('blur', async () => {
        if (input.dataset.suppressNextBlur === '1') { delete input.dataset.suppressNextBlur; return; }
        await commit();
      });
      input.addEventListener('keydown', async (e) => {
        if (e.key === 'Enter') {
          e.preventDefault();
          input.dataset.suppressNextBlur = '1';
          await commit();
        }
        if (e.key === 'Tab') {
          // prevent blur handler from firing immediately after keydown
          input.dataset.suppressNextBlur = '1';
          // debounce Tab-triggered save to avoid double submit with blur
          if (!input.dataset.tabPending) {
            input.dataset.tabPending = '1';
            setTimeout(async () => {
              delete input.dataset.tabPending;
              await commit();
            }, 0);
          }
        }
      });

      input.dataset.rowId = r.id || 'draft';
      input.dataset.field = field;
      td.appendChild(input);
      return td;
    }

    function addNewRow(){
      const draft = { title:'', status:'', priority:0, notes:'' };
      rows.unshift(draft);
      render();
    }

    async function save(r){
      if (r._saving) return; // prevent double submits (e.g., Tab + blur)
      r._saving = true;
      try{
        setStatus('Saving…');
        const payload = { title: r.title||null, status: r.status||null, priority: r.priority??null, notes: r.notes||null, link_url: r.link_url||null };
        let res, data;
        if (r.id) {
          res = await fetch(`/api/gt_todo/${r.id}`, { method:'PUT', headers:{'Content-Type':'application/json'}, body: JSON.stringify(payload) });
          data = await res.json();
        } else {
          res = await fetch('/api/gt_todo', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify(payload) });
          if (res.status === 409) {
            // surface unique violation in a friendly way
            const err = await res.json().catch(()=>({}));
            throw new Error(err?.error || 'Duplicate title.');
          }
          data = await res.json();
        }
        if(!data.success) throw new Error(data.error||'Save failed');
        const saved = data.record || data.records || data;
        if(saved && saved.id){
          const idx = rows.findIndex(x=>x.id===saved.id);
          if(idx>=0) rows[idx]=saved; else { rows = rows.filter(x=>x.id); rows.unshift(saved); }
        }

        applyClientFilterAndSort();
        setStatus('Saved');
      } catch(e) {
        console.error(e);
        setStatus('Error: '+e.message);
        if ((e.message||'').toLowerCase().includes('duplicate')) {
          alert('A task with this title already exists.');
        }
      } finally {
        delete r._saving;
      }
    }


    async function bumpSchedule(r, ms, schedTd){
      try{
        const now = new Date();
        const newDt = new Date(now.getTime() + ms);
        // If computed time is in the past (edge case), bump to next day 8am
        if (ms > 0 && newDt.getTime() <= now.getTime()) {
          newDt.setDate(now.getDate() + 1);
          newDt.setHours(8,0,0,0);
        }
        r.scheduled_at = newDt.toISOString();
        setStatus(`Scheduling to ${newDt.toLocaleString()}…`);
        // If record not yet saved, create it first to get an id
        if(!r.id){
          await save(r);
        }
        const res = await fetch(`/api/gt_todo/${r.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ scheduled_at: r.scheduled_at })
        });
        const data = await res.json();
        if(!data.success) throw new Error(data.error||'Failed to update scheduled_at');
        const saved = data.record;
        if(saved){
          const idx = rows.findIndex(x=>x.id===saved.id);
          if(idx>=0) rows[idx]=saved;
          // Update the UI cell and flash feedback
          if (schedTd){
            schedTd.textContent = saved.scheduled_at ? new Date(saved.scheduled_at).toLocaleString() : '';
            schedTd.classList.add('flash-save');
            setTimeout(()=> schedTd.classList.remove('flash-save'), 900);
          }
        }
        applyClientFilterAndSort();
        setStatus('Scheduled updated');
      }catch(e){ console.error(e); setStatus('Error: '+e.message); }
    }


    async function del(id){
      if(!id) { setStatus('Cannot delete unsaved row'); return; }
      if(!confirm('Delete this record?')) return;
      try{
        setStatus('Deleting…');
        const res = await fetch(`/api/gt_todo/${id}`, { method:'DELETE' });
        const data = await res.json();
        if(!data.success) throw new Error(data.error||'Delete failed');
        rows = rows.filter(r=>r.id!==id);
        applyClientFilterAndSort();
        setStatus('Deleted');
      }catch(e){ console.error(e); setStatus('Error: '+e.message); }
    }

    // Sorting and filtering (top-level)
    const headerCells = Array.from(document.querySelectorAll('#todoTable thead tr:first-child th'));
    let sortCol = 'scheduled_at';
    let sortAsc = true;

    // Ensure a blank draft row exists at the top for quick entry
    function ensureDraftRow(){
      if (rows.length===0 || rows[0]?.id) {
        rows.unshift({ title:'', status:'', notes:'', scheduled_at: new Date().toISOString() });
      }
    }

    headerCells.forEach((th) => {
      const col = th.dataset.col;
      if (!col) return;
      th.style.position = 'relative';
      th.addEventListener('click', ()=>{
        if (sortCol === col) sortAsc = !sortAsc; else { sortCol = col; sortAsc = true; }
        applyClientFilterAndSort();
      });
    });

    const filters = {
      id: document.getElementById('filter_id'),
      title: document.getElementById('filter_title'),
      status: document.getElementById('filter_status'),
      notes: document.getElementById('filter_notes'),
      link_url: document.getElementById('filter_link_url'),
      scheduled_at: document.getElementById('filter_scheduled_at')
    };

    Object.values(filters).forEach(inp => inp && inp.addEventListener('input', applyClientFilterAndSort));

    function applyClientFilterAndSort(){
      let data = [...rows];
      // Filtering
      data = data.filter(r => {
        const matches = (k, v) => {
          if (!v) return true;
          const val = r[k];
          return (val!==null && val!==undefined) && String(val).toLowerCase().includes(String(v).toLowerCase());
        };
        return matches('id', filters.id?.value) &&
               matches('title', filters.title?.value) &&
               matches('status', filters.status?.value) &&
               matches('notes', filters.notes?.value) &&
               matches('link_url', filters.link_url?.value) &&
               matches('scheduled_at', filters.scheduled_at?.value);
      });

      // Sorting
      data.sort((a,b)=>{
        const av = a[sortCol];
        const bv = b[sortCol];
        const na = av===null||av===undefined; const nb = bv===null||bv===undefined;
        if (na && nb) return 0; if (na) return sortAsc?1:-1; if (nb) return sortAsc?-1:1;
        if (sortCol==='priority' || sortCol==='id'){
          const x = Number(av)||0, y = Number(bv)||0; return sortAsc ? x-y : y-x;
        }
        // For scheduled_at, compare dates
        if (sortCol==='scheduled_at'){
          const x = new Date(av).getTime() || 0, y = new Date(bv).getTime() || 0;
          return sortAsc ? x-y : y-x;
        }
        const x = String(av).toLowerCase(), y = String(bv).toLowerCase();
        if (x<y) return sortAsc?-1:1; if (x>y) return sortAsc?1:-1; return 0;
      });

      renderFiltered(data);
    }

      // Highlight duplicate titles in purple
      function markDuplicateTitles(){
        const nodes = Array.from(document.querySelectorAll('#todoTable tbody tr'));
        const map = new Map();
        nodes.forEach(tr => {
          const tcell = tr.children[1];
          if(!tcell) return;
          const inp = tcell.querySelector('input,textarea');
          const val = inp ? inp.value : tcell.textContent;
          const key = (val||'').trim().toLowerCase();
          if(!key) return;
          map.set(key, (map.get(key)||0)+1);
        });
        nodes.forEach(tr => {
          const tcell = tr.children[1];
          if(!tcell) return;
          const inp = tcell.querySelector('input,textarea');
          const val = inp ? inp.value : tcell.textContent;
          const key = (val||'').trim().toLowerCase();
          if(!key){ tcell.style.background=''; return; }
          tcell.style.background = (map.get(key)||0) > 1 ? '#f3e6ff' : '';
        });
      }


    function renderFiltered(data){
      // Always render a draft row on top for quick entry
      tbody.innerHTML = '';
      const draft = { title:'', status:'', notes:'', scheduled_at: new Date().toISOString() };
      tbody.appendChild(renderRow(draft));

      // Render actual data rows (if any)
      if (data && data.length) {
        data.forEach(r => tbody.appendChild(renderRow(r)));
      }

      try{ markDuplicateTitles(); }catch{}

    }


    // Auto-load on open
    load();
      try{ markDuplicateTitles(); }catch{}

  </script>
</body>

</html>

