FUNCTION: CREATE OR R<PERSON>LACE FUNCTION public.f_truncate_data_from_it_infor_tables()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Truncate data from the specified tables
    TRUNCATE TABLE it_infor_all_fields;
    TRUNCATE TABLE it_infor_competition_landscape;
    TRUNCATE TABLE it_infor_no_buy_box;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.tr_match_on_sdasin_up_func()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  PERFORM fn_sdasin_matching_logic(NEW);
  RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.log_missing_players()
 <PERSON><PERSON><PERSON><PERSON> trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  RAISE NOTICE 'Attempted to access missing table players from %', current_query();
  RETURN NULL;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.update_updated_at_column()
 <PERSON><PERSON><PERSON><PERSON> trigger
 LANGUAGE plpgsql
AS $function$
begin
    new.updated_at = now();
    return new;
end;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.update_last_change_date_stock_ledger()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    NEW.last_change_date = now();  -- Set last_change_date to current timestamp
    RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.dynamic_execute_function()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    IF NEW.run THEN
        BEGIN
            -- Dynamically execute the function specified in function
            EXECUTE 'SELECT ' || quote_ident(NEW.function) || '()';

            -- Update last_ran_at and results
            NEW.last_ran_at := NOW();
            NEW.results := 'Function executed successfully';
        EXCEPTION WHEN OTHERS THEN
            -- Handle errors gracefully and log them in results
            NEW.results := 'Error: ' || SQLERRM;
        END;

        -- Set run back to false
        NEW.run := FALSE;
    END IF;

    RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.check_file_existence_and_age()
 RETURNS void
 LANGUAGE plpgsql
AS $function$DECLARE
    -- Variables for file checking
    file_record RECORD;
    file_name TEXT;
    bucket_name TEXT;
    section_name TEXT;
    file_path TEXT;
    file_age INTERVAL;
    status_result TEXT;
    status_notes_result TEXT;

    -- Define file list (file name, bucket, section)
    files TEXT[][] := ARRAY[
        ARRAY['from_informed_all_fields.csv', 'uploads', 'Informed Repricer'],
        ARRAY['from_informed_competition_landscape.csv', 'uploads', 'Informed Repricer'],
        ARRAY['from_informed_no_buy_box.csv', 'uploads', 'Informed Repricer']
    ];
    
    -- Variables for table checking
    table_record RECORD;
    table_name TEXT;
    min_record_count INT;
    max_age_hours INT;
    table_section TEXT;
    record_count INT;
    table_age INTERVAL;
    
    -- Define table list (table name, min records, max age, section)
    tables TEXT[][] := ARRAY[
        ARRAY['it_infor_all_fields', '15000', '24', 'Informed Repricer'],
        ARRAY['it_infor_competition_landscape', '5000', '24', 'Informed Repricer'],
        ARRAY['tu_informed', '15000', '24', 'Informed Repricer'],
        ARRAY['it_infor_no_buy_box', '2000', '24', 'Informed Repricer']
    ];
    
    i INT;
BEGIN
    -- 1️⃣ LOOP THROUGH FILES TO CHECK EXISTENCE & AGE
    FOR i IN 1 .. array_length(files, 1) LOOP
        file_name := files[i][1];
        bucket_name := files[i][2];
        section_name := files[i][3];

        -- Construct file path
        file_path := bucket_name || '/' || file_name;
        
        -- Check if the file exists and get its creation time
        SELECT created_at INTO file_record 
        FROM storage.objects
        WHERE bucket_id = bucket_name AND name = file_name;

        -- Determine status based on existence and age
        IF file_record.created_at IS NULL THEN
            status_result := 'ERROR';
            status_notes_result := 'File does not exist';
        ELSE
            -- Calculate file age
            file_age := now() - file_record.created_at;
            
            IF file_age > INTERVAL '24 hours' THEN
                status_result := 'ERROR';
                status_notes_result := 'File exists but is too old (' || EXTRACT(EPOCH FROM file_age)/3600 || ' hours old)';
            ELSE
                status_result := 'GOOD';
                status_notes_result := 'File exists and is current (' || EXTRACT(EPOCH FROM file_age)/3600 || ' hours old)';
            END IF;
        END IF;

        -- Log file status into t_status table
        INSERT INTO t_status (created_by, section, checking, status, status_notes)
        VALUES ('system', section_name, 'Checking existence of file: ' || file_name, status_result, status_notes_result);
    END LOOP;

    -- 2️⃣ LOOP THROUGH TABLES TO CHECK RECORD COUNT & DATA AGE
    FOR i IN 1 .. array_length(tables, 1) LOOP
        table_name := tables[i][1];
        min_record_count := tables[i][2]::INT;
        max_age_hours := tables[i][3]::INT;
        table_section := tables[i][4];

        -- Get record count
        EXECUTE format('SELECT COUNT(*) FROM %I', table_name) INTO record_count;

        -- Get the created_at timestamp of the latest record
        EXECUTE format('SELECT MIN(created_at) FROM %I', table_name) INTO table_record;

        -- Determine table status
        IF record_count < min_record_count THEN
            status_result := 'ERROR';
            status_notes_result := format('Table %s has only %s records (minimum required: %s)', table_name, record_count, min_record_count);
        ELSE
            table_age := now() - table_record.min;
            
            IF table_age > (max_age_hours * INTERVAL '1 hour') THEN
                status_result := 'ERROR';
                status_notes_result := format('Table %s data is too old (%s hours old, max allowed: %s hours)', table_name, EXTRACT(EPOCH FROM table_age)/3600, max_age_hours);
            ELSE
                status_result := 'GOOD';
                status_notes_result := format('Table %s has %s records and is up-to-date (%s hours old)', table_name, record_count, EXTRACT(EPOCH FROM table_age)/3600);
            END IF;
        END IF;

        -- Log table status into t_status table
        INSERT INTO t_status (created_by, section, checking, status, status_notes)
        VALUES ('system', table_section, 'Checking status of table: ' || table_name, status_result, status_notes_result);
    END LOOP;
END;$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.f_manage_sdasin_updates()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
  rec RECORD;
  computed_reason text;
BEGIN
  -- Step 1: Delete existing join records for this SDASIN.
  DELETE FROM tjoin_discs_sdasins
   WHERE sdasin_id = NEW.id;

  -- Step 2: Loop over candidate discs that are unsold or sold within the last 14 days.
  FOR rec IN
    SELECT
      d.id AS disc_id,
      -- Compute the reason using a nested CASE expression.
      CASE 
        WHEN (d.mps_id = NEW.mps_id OR d.mps_id = NEW.mps_id2) THEN
          CASE 
            WHEN NEW.color_id = 23 THEN 'mps exact, good weight, color varies'
            WHEN NEW.color_id IS NOT NULL THEN 'mps exact, good weight, color exact'
            ELSE 'mps exact, good weight, color unknown'
          END
        ELSE
          CASE 
            WHEN NEW.color_id = 23 THEN 'mps stock equivalent, good weight, color varies'
            WHEN NEW.color_id IS NOT NULL THEN 'mps stock equivalent, good weight, color exact'
            ELSE 'mps stock equivalent, good weight, color unknown'
          END
      END AS reason
    FROM t_discs d
      -- Join for the disc's mps and its stamp.
      LEFT JOIN t_mps dm ON d.mps_id = dm.id
      LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
      -- For stock equivalent matching, join the two possible SDASIN mps records and stamps.
      LEFT JOIN t_mps sm1 ON NEW.mps_id = sm1.id
      LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
      LEFT JOIN t_mps sm2 ON NEW.mps_id2 = sm2.id
      LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
    WHERE
      -- Only consider discs that are unsold or sold within the last 14 days.
      (d.sold_date IS NULL OR d.sold_date > now() - interval '14 days')
      -- Weight criteria (inclusive).
      AND d.weight >= NEW.min_weight
      AND d.weight <= NEW.max_weight
      -- Color criteria: if SDASIN color is 23, any disc color qualifies; otherwise, colors must match.
      AND (NEW.color_id = 23 OR d.color_id = NEW.color_id)
      -- MPS matching:
      AND (
           -- Exact match on mps.
           (d.mps_id = NEW.mps_id OR d.mps_id = NEW.mps_id2)
           OR
           -- Stock equivalent match:
           (
             (
               ((sst1.is_sdasin_stock = TRUE OR sst1.id = 38) AND d.mps_id = NEW.mps_id)
               OR
               ((sst2.is_sdasin_stock = TRUE OR sst2.id = 38) AND d.mps_id = NEW.mps_id2)
             )
             AND dst.is_vendor_stock = TRUE
             AND (
                  (d.mps_id = NEW.mps_id AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id)
                  OR
                  (d.mps_id = NEW.mps_id2 AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id)
             )
           )
      )
  LOOP
    -- Explicitly assign the computed reason.
    computed_reason := rec.reason;
    INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
    VALUES (rec.disc_id, NEW.id, computed_reason, 'system')
    ON CONFLICT (disc_id, sdasin_id) DO NOTHING;
  END LOOP;

  -- Step 3: Update the timestamp to indicate matching was performed.
  UPDATE t_sdasins
    SET looked_for_matching_discs_at = NOW()
  WHERE id = NEW.id;

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    INSERT INTO t_error_logs (error_message, context)
    VALUES (SQLERRM, 'f_manage_sdasin_updates: Trigger execution error');
    RAISE;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.f_manage_joins_on_disc_updates()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Step 1: Delete existing join records for this disc.
  DELETE FROM tjoin_discs_sdasins
   WHERE disc_id = NEW.id;

  -- Step 2: Skip matching if the disc is sold and older than 14 days.
  IF NEW.sold_date IS NOT NULL AND NEW.sold_date <= (now() - interval '14 days') THEN
    RETURN NEW;
  END IF;

  -- Step 3: Insert join records for each SDASIN that meets the criteria.
  INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
  SELECT
    NEW.id AS disc_id,
    s.id AS sdasin_id,
    COALESCE(
      CASE 
        WHEN (NEW.mps_id = s.mps_id OR NEW.mps_id = s.mps_id2) THEN
          CASE 
            WHEN s.color_id = 23 THEN 'mps exact, good weight, color varies'
            WHEN s.color_id IS NOT NULL THEN 'mps exact, good weight, color exact'
            ELSE 'mps exact, good weight, color unknown'
          END
        ELSE
          CASE 
            WHEN s.color_id = 23 THEN 'mps stock equivalent, good weight, color varies'
            WHEN s.color_id IS NOT NULL THEN 'mps stock equivalent, good weight, color exact'
            ELSE 'mps stock equivalent, good weight, color unknown'
          END
      END,
      'unknown match reason'
    ) AS reason,
    'system' AS created_by
  FROM t_sdasins s
    -- For stock-equivalent matching, join the two possible SDASIN mps records and their stamps.
    LEFT JOIN t_mps sm1 ON s.mps_id = sm1.id
    LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
    LEFT JOIN t_mps sm2 ON s.mps_id2 = sm2.id
    LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
    -- Join the disc’s mps record and stamp.
    LEFT JOIN t_mps dm ON NEW.mps_id = dm.id
    LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
  WHERE
      -- Weight criteria (inclusive)
      NEW.weight >= s.min_weight
      AND NEW.weight <= s.max_weight

      -- Color criteria: if the SDASIN color is 23, any disc color qualifies; otherwise, they must match.
      AND ( s.color_id = 23 OR NEW.color_id = s.color_id )

      -- MPS Matching:
      AND (
           -- (A) Exact match: disc.mps_id equals sdasin.mps_id or sdasin.mps_id2.
           ( NEW.mps_id = s.mps_id OR NEW.mps_id = s.mps_id2 )
           OR
           -- (B) Stock equivalent match: check either candidate mps.
           (
             (
               ((sst1.is_sdasin_stock = TRUE OR sst1.id = 38) AND NEW.mps_id = s.mps_id)
               OR
               ((sst2.is_sdasin_stock = TRUE OR sst2.id = 38) AND NEW.mps_id = s.mps_id2)
             )
             AND dst.is_vendor_stock = TRUE
             AND (
                  ( NEW.mps_id = s.mps_id AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id )
                  OR
                  ( NEW.mps_id = s.mps_id2 AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id )
             )
           )
      )
  ON CONFLICT (disc_id, sdasin_id) DO NOTHING;

  RETURN NEW;

EXCEPTION
  WHEN OTHERS THEN
    INSERT INTO t_error_logs (error_message, context)
    VALUES (SQLERRM, 'f_manage_joins_on_disc_updates: Trigger execution error');
    RAISE;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.fn_try_publish_collection_plastic()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
  -- Get the current webhook prefix from t_config.
  webhook_prefix TEXT := (SELECT value FROM t_config WHERE key = 'current_webhook_4_character_prefix');
  -- Build the webhook URL using the prefix. This URL calls your publishCollectionPlastic endpoint.
  webhook_url TEXT := 'https://' || webhook_prefix || '-24-124-112-70.ngrok-free.app/publishCollectionPlastic';
  payload TEXT;
  v_response http_response;
BEGIN
  -- Build a JSON payload containing only the plastic id.
  payload := json_build_object(
    'id', NEW.id
  )::text;

  -- Send the HTTP POST request to the webhook endpoint.
  v_response := http_post(webhook_url, payload, 'application/json');

  IF v_response.status_code <> 200 THEN
    RAISE NOTICE 'Webhook call failed for plastic % with status code %', NEW.id, v_response.status_code;
  ELSE
    RAISE NOTICE 'Webhook successfully sent for plastic %', NEW.id;
  END IF;

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in fn_try_publish_collection_plastic: %', SQLERRM;
    RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.tr_fn_match_on_sdasin_in()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Call the core matching function, passing in the newly inserted SDASIN row
  PERFORM fn_sdasin_matching_logic(NEW);

  -- Return the inserted row so it can be committed to the table
  RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.notify_error_status()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    email_subject TEXT;
    email_message TEXT;
BEGIN
    IF NEW.status = 'ERROR' THEN
        email_subject := '🚨 ERROR: ' || NEW.section || ' - ' || NEW.checking;
        email_message := 'An issue was detected in section: ' || NEW.section ||
                         '.\nCheck: ' || NEW.checking ||
                         '\nStatus: ' || NEW.status ||
                         '\nNotes: ' || NEW.status_notes ||
                         '\nTimestamp: ' || NEW.created_at;

        -- Call function to send email
        PERFORM send_email_alert(email_subject, email_message);
    END IF;

    RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.f_update_veeqo_qty()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    veeqo_sellable_id TEXT;
    veeqo_api_url TEXT;
    headers TEXT := 'Content-Type:application/json
x-api-key:Vqt/16c3acd642be598d3ca079590a8aae87
User-Agent:PostgreSQL-Http-Client';
    payload TEXT;
    http_response JSONB;
    status_code INT;
    veeqo_updated_at TIMESTAMP;
BEGIN
    -- Fetch veeqo_sellable_id from products table
    SELECT p.veeqo_sellable_id INTO veeqo_sellable_id
    FROM products p
    WHERE p.id = NEW.product_id;

    -- If veeqo_sellable_id is NULL or empty, log it and update stock_ledger
    IF veeqo_sellable_id IS NULL OR veeqo_sellable_id = '' THEN
        INSERT INTO veeqo_api_log (product_id, stock_on_hand, request_url, request_payload, response_status_code, response_body, from_function_name)
        VALUES (
            NEW.product_id,
            NEW.stock_on_hand,
            'could not generate - product has null or empty veeqo_sellable_id',
            'never received without a URL',
            NULL,
            http_response::TEXT,
            'f_update_veeqo_qty'
        );
        -- On error, update veeqo_qty_updated_from_ledger_at to NULL
        UPDATE stock_ledger
        SET veeqo_qty_updated_from_ledger_at = NULL
        WHERE product_id = NEW.product_id;
        RETURN NEW;
    END IF;

    -- Construct API URL using veeqo_sellable_id
    veeqo_api_url := 'https://api.veeqo.com/sellables/' || veeqo_sellable_id || '/warehouses/99881/stock_entry';

    -- Construct JSON payload
    payload := '{"stock_entry": {"physical_stock_level": ' || NEW.stock_on_hand || ', "infinite": false}}';

    -- Make the PUT request
    SELECT status, content::JSONB INTO status_code, http_response
    FROM http_put(
        veeqo_api_url, -- API endpoint
        payload,       -- JSON payload
        headers        -- Headers
    );

    -- Log the response in the veeqo_api_log table at the beginning
    INSERT INTO veeqo_api_log (product_id, stock_on_hand, request_url, request_payload, response_status_code, response_body, from_function_name)
    VALUES (
        NEW.product_id,
        NEW.stock_on_hand,
        veeqo_api_url,
        payload,
        status_code,
        http_response::TEXT,
        'f_update_veeqo_qty'
    );

    -- Perform appropriate actions based on status code
    IF status_code = 200 THEN
        veeqo_updated_at := (http_response->>'updated_at')::TIMESTAMP;

        -- Update stock_ledger with the updated_at field only
        UPDATE stock_ledger
        SET veeqo_qty_updated_from_ledger_at = veeqo_updated_at
        WHERE product_id = NEW.product_id;
    ELSE
        -- On error, update veeqo_qty_updated_from_ledger_at to NULL
        UPDATE stock_ledger
        SET veeqo_qty_updated_from_ledger_at = NULL
        WHERE product_id = NEW.product_id;
    END IF;

    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        -- On exception, update stock_ledger.veeqo_qty_updated_from_ledger_at to NULL
        UPDATE stock_ledger
        SET veeqo_qty_updated_from_ledger_at = NULL
        WHERE product_id = NEW.product_id;
        RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.http_set_curlopt(curlopt character varying, value character varying)
 RETURNS boolean
 LANGUAGE c
AS '$libdir/http', $function$http_set_curlopt$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.http_reset_curlopt()
 RETURNS boolean
 LANGUAGE c
AS '$libdir/http', $function$http_reset_curlopt$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.http_list_curlopt()
 RETURNS TABLE(curlopt text, value text)
 LANGUAGE c
AS '$libdir/http', $function$http_list_curlopt$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.http_header(field character varying, value character varying)
 RETURNS http_header
 LANGUAGE sql
AS $function$ SELECT $1, $2 $function$


FUNCTION: CREATE OR REPLACE FUNCTION public.http(request http_request)
 RETURNS http_response
 LANGUAGE c
AS '$libdir/http', $function$http_request$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.http_get(uri character varying)
 RETURNS http_response
 LANGUAGE sql
AS $function$ SELECT public.http(('GET', $1, NULL, NULL, NULL)::public.http_request) $function$


FUNCTION: CREATE OR REPLACE FUNCTION public.http_post(uri character varying, content character varying, content_type character varying)
 RETURNS http_response
 LANGUAGE sql
AS $function$ SELECT public.http(('POST', $1, NULL, $3, $2)::public.http_request) $function$


FUNCTION: CREATE OR REPLACE FUNCTION public.http_put(uri character varying, content character varying, content_type character varying)
 RETURNS http_response
 LANGUAGE sql
AS $function$ SELECT public.http(('PUT', $1, NULL, $3, $2)::public.http_request) $function$


FUNCTION: CREATE OR REPLACE FUNCTION public.http_patch(uri character varying, content character varying, content_type character varying)
 RETURNS http_response
 LANGUAGE sql
AS $function$ SELECT public.http(('PATCH', $1, NULL, $3, $2)::public.http_request) $function$


FUNCTION: CREATE OR REPLACE FUNCTION public.http_delete(uri character varying)
 RETURNS http_response
 LANGUAGE sql
AS $function$ SELECT public.http(('DELETE', $1, NULL, NULL, NULL)::public.http_request) $function$


FUNCTION: CREATE OR REPLACE FUNCTION public.http_delete(uri character varying, content character varying, content_type character varying)
 RETURNS http_response
 LANGUAGE sql
AS $function$ SELECT public.http(('DELETE', $1, NULL, $3, $2)::public.http_request) $function$


FUNCTION: CREATE OR REPLACE FUNCTION public.http_head(uri character varying)
 RETURNS http_response
 LANGUAGE sql
AS $function$ SELECT public.http(('HEAD', $1, NULL, NULL, NULL)::public.http_request) $function$


FUNCTION: CREATE OR REPLACE FUNCTION public.urlencode(string character varying)
 RETURNS text
 LANGUAGE c
 IMMUTABLE STRICT
AS '$libdir/http', $function$urlencode$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.urlencode(string bytea)
 RETURNS text
 LANGUAGE c
 IMMUTABLE STRICT
AS '$libdir/http', $function$urlencode$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.urlencode(data jsonb)
 RETURNS text
 LANGUAGE c
 IMMUTABLE STRICT
AS '$libdir/http', $function$urlencode_jsonb$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.http_get(uri character varying, data jsonb)
 RETURNS http_response
 LANGUAGE sql
AS $function$
        SELECT public.http(('GET', $1 || '?' || public.urlencode($2), NULL, NULL, NULL)::public.http_request)
    $function$


FUNCTION: CREATE OR REPLACE FUNCTION public.http_post(uri character varying, data jsonb)
 RETURNS http_response
 LANGUAGE sql
AS $function$
        SELECT public.http(('POST', $1, NULL, 'application/x-www-form-urlencoded', public.urlencode($2))::public.http_request)
    $function$


FUNCTION: CREATE OR REPLACE FUNCTION public.text_to_bytea(data text)
 RETURNS bytea
 LANGUAGE c
 IMMUTABLE STRICT
AS '$libdir/http', $function$text_to_bytea$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.bytea_to_text(data bytea)
 RETURNS text
 LANGUAGE c
 IMMUTABLE STRICT
AS '$libdir/http', $function$bytea_to_text$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.create_stock_ledger_entry()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Insert a new entry into the stock_ledger for the new product, defaulting stock_on_hand to 0
    INSERT INTO public.stock_ledger (product_id, stock_on_hand, last_change_date)
    VALUES (NEW.id, 0, now());  -- Default stock_on_hand to 0

    RETURN NEW;  -- Return the new product record
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.fn_download_and_import_innova_osl()
 RETURNS void
 LANGUAGE plpgsql
AS $function$DECLARE
  -- Retrieve the webhook prefix from your configuration table.
  webhook_prefix TEXT := (SELECT value FROM t_config WHERE key = 'current_webhook_4_character_prefix');
  -- Build the webhook URL using the prefix.
  webhook_url TEXT := 'https://' || webhook_prefix || '-24-124-112-70.ngrok-free.app/download-and-import-innova-osl';
  -- Build a simple JSON payload.
  payload TEXT := json_build_object('action', 'download_and_import')::text;
  v_response http_response;
BEGIN
  RAISE NOTICE 'Calling webhook URL: % with payload: %', webhook_url, payload;
  
  -- Send the HTTP POST request to the webhook endpoint.
  v_response := http_post(webhook_url, payload, 'application/json');

  IF v_response.status_code <> 200 THEN
    RAISE NOTICE 'Webhook call failed with status code %', v_response.status_code;
  ELSE
    RAISE NOTICE 'Webhook successfully sent.';
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in fn_download_and_import_innova_osl: %', SQLERRM;
END;$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.fn_check_disc_sdasin_match(p_disc_id integer, p_sdasin_id integer)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
  v_reason       TEXT;
  disc_rec       t_discs;
  sdasin_rec     t_sdasins;

  dm   t_mps;       -- The disc's MPS record
  sm1  t_mps;       -- sdasin's mps_id
  sm2  t_mps;       -- sdasin's mps_id2
  dst  t_stamps;    -- The disc's stamp
  sst1 t_stamps;    -- stamp for sdasin's mps_id
  sst2 t_stamps;    -- stamp for sdasin's mps_id2
BEGIN
  ----------------------------------------------------------------------
  -- 1) Load the disc and SDASIN
  ----------------------------------------------------------------------
  SELECT * INTO disc_rec
    FROM t_discs
   WHERE id = p_disc_id;
  IF NOT FOUND THEN
    RETURN NULL;
  END IF;

  SELECT * INTO sdasin_rec
    FROM t_sdasins
   WHERE id = p_sdasin_id;
  IF NOT FOUND THEN
    RETURN NULL;
  END IF;

  ----------------------------------------------------------------------
  -- 2) Possibly skip disc if sold more than 14 days ago
  ----------------------------------------------------------------------
  IF disc_rec.sold_date IS NOT NULL
     AND disc_rec.sold_date <= (NOW() - INTERVAL '14 days')
  THEN
    RETURN NULL;
  END IF;

  ----------------------------------------------------------------------
  -- 3) Weight check (inclusive)
  ----------------------------------------------------------------------
  IF disc_rec.weight < sdasin_rec.min_weight
     OR disc_rec.weight > sdasin_rec.max_weight
  THEN
    RETURN NULL;
  END IF;

  ----------------------------------------------------------------------
  -- 4) Color check
  --    If sdasin color_id=23 => "any disc color"
  --    Otherwise disc.color_id must match sdasin.color_id
  ----------------------------------------------------------------------
  IF sdasin_rec.color_id <> 23
     AND disc_rec.color_id <> sdasin_rec.color_id
  THEN
    RETURN NULL;
  END IF;

  ----------------------------------------------------------------------
  -- 5) Load MPS + Stamp info for deeper checks
  ----------------------------------------------------------------------
  IF disc_rec.mps_id IS NOT NULL THEN
    SELECT * INTO dm FROM t_mps    WHERE id = disc_rec.mps_id;
    IF FOUND THEN
      SELECT * INTO dst FROM t_stamps WHERE id = dm.stamp_id;
    END IF;
  END IF;

  IF sdasin_rec.mps_id IS NOT NULL THEN
    SELECT * INTO sm1 FROM t_mps    WHERE id = sdasin_rec.mps_id;
    IF FOUND THEN
      SELECT * INTO sst1 FROM t_stamps WHERE id = sm1.stamp_id;
    END IF;
  END IF;

  IF sdasin_rec.mps_id2 IS NOT NULL THEN
    SELECT * INTO sm2 FROM t_mps    WHERE id = sdasin_rec.mps_id2;
    IF FOUND THEN
      SELECT * INTO sst2 FROM t_stamps WHERE id = sm2.stamp_id;
    END IF;
  END IF;

  ----------------------------------------------------------------------
  -- 6) MPS matching logic: EXACT vs. STOCK EQUIVALENT
  ----------------------------------------------------------------------
  IF disc_rec.mps_id = sdasin_rec.mps_id
     OR disc_rec.mps_id = sdasin_rec.mps_id2
  THEN
    -- EXACT MPS MATCH
    IF sdasin_rec.color_id = 23 THEN
      v_reason := 'mps exact, good weight, color varies';
    ELSE
      v_reason := 'mps exact, good weight, color exact';
    END IF;
  ELSE
    ------------------------------------------------------------------
    -- STOCK EQUIVALENT match
    -- According to your logic:
    --   ( (sst1.is_sdasin_stock=TRUE OR sst1.id=38) AND disc.mps_id= sdasin.mps_id )
    --   OR ...
    --   AND dst.is_vendor_stock= TRUE
    --   AND matching plastic/mold combos
    ------------------------------------------------------------------
    IF dm.id IS NOT NULL
       AND dst.id IS NOT NULL
       AND (
            (
              (sst1.is_sdasin_stock = TRUE OR sst1.id=38)
              AND disc_rec.mps_id = sdasin_rec.mps_id
            )
            OR
            (
              (sst2.is_sdasin_stock = TRUE OR sst2.id=38)
              AND disc_rec.mps_id = sdasin_rec.mps_id2
            )
          )
       AND dst.is_vendor_stock = TRUE
       AND (
            (
              disc_rec.mps_id = sdasin_rec.mps_id
              AND dm.plastic_id = sm1.plastic_id
              AND dm.mold_id    = sm1.mold_id
            )
            OR
            (
              disc_rec.mps_id = sdasin_rec.mps_id2
              AND dm.plastic_id = sm2.plastic_id
              AND dm.mold_id    = sm2.mold_id
            )
          )
    THEN
      IF sdasin_rec.color_id = 23 THEN
        v_reason := 'mps stock equivalent, good weight, color varies';
      ELSE
        v_reason := 'mps stock equivalent, good weight, color exact';
      END IF;
    ELSE
      -- Not a match
      RETURN NULL;
    END IF;
  END IF;

  ----------------------------------------------------------------------
  -- 7) If we reach here, we have a match => return the reason
  ----------------------------------------------------------------------
  RETURN v_reason;

EXCEPTION
  WHEN OTHERS THEN
    INSERT INTO t_error_logs (error_message, context)
    VALUES (SQLERRM, 'fn_check_disc_sdasin_match: execution error');
    RAISE;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.fn_match_on_sdasin_up_in()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
  disc_rec   RECORD;
  v_reason   TEXT;
BEGIN
  ----------------------------------------------------------------------
  -- 1) Delete old joins for this SDASIN
  ----------------------------------------------------------------------
  DELETE FROM tjoin_discs_sdasins
   WHERE sdasin_id = NEW.id;

  ----------------------------------------------------------------------
  -- 2) Loop over candidate discs
  ----------------------------------------------------------------------
  FOR disc_rec IN
    SELECT d.id
    FROM t_discs d
    -- Only consider unsold or sold in last 14 days
    WHERE (d.sold_date IS NULL OR d.sold_date > NOW() - INTERVAL '14 days')
  LOOP
    --------------------------------------------------------------------
    -- 2a) Check if they match
    --------------------------------------------------------------------
    v_reason := fn_check_disc_sdasin_match(disc_rec.id, NEW.id);

    IF v_reason IS NOT NULL THEN
      ------------------------------------------------------------------
      -- 2b) Insert into tjoin_discs_sdasins
      ------------------------------------------------------------------
      INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
      VALUES (disc_rec.id, NEW.id, v_reason, 'system')
      ON CONFLICT (disc_id, sdasin_id) DO NOTHING;
    END IF;
  END LOOP;

  ----------------------------------------------------------------------
  -- 3) Optionally update a timestamp
  ----------------------------------------------------------------------
  UPDATE t_sdasins
     SET looked_for_matching_discs_at = NOW()
   WHERE id = NEW.id;

  RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.handle_order_sheet_line_events()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Handle INSERT
    IF TG_OP = 'INSERT' THEN
        -- Step 1: Insert a new record into t_inv_osl
        INSERT INTO t_inv_osl (id, available_quantity)
        VALUES (NEW.id, 0)
        ON CONFLICT (id) DO NOTHING;

        -- Step 2: Find matches in t_discs
        UPDATE t_discs
        SET order_sheet_line_id = NEW.id
        WHERE 
            mps_id = NEW.mps_id AND
            sold_date IS NULL AND
            weight >= NEW.min_weight AND
            weight <= NEW.max_weight AND
            (
                NEW.color_id = 23 OR -- Allow any color when color_id = 23
                color_id = NEW.color_id -- Otherwise, match color_id
            );

        -- Step 3: Update available_quantity in t_inv_osl
        UPDATE t_inv_osl
        SET available_quantity = (
            SELECT COUNT(*) 
            FROM t_discs
            WHERE 
                order_sheet_line_id = NEW.id AND
                sold_date IS NULL
        )
        WHERE id = NEW.id;
    END IF;

    -- Handle DELETE
    IF TG_OP = 'DELETE' THEN
        -- Set available_quantity to 0 in t_inv_osl
        UPDATE t_inv_osl
        SET available_quantity = 0
        WHERE id = OLD.id;
    END IF;

    -- Handle UPDATE
    IF TG_OP = 'UPDATE' THEN
        -- Step 1: Unlink all discs from the old record
        UPDATE t_discs
        SET order_sheet_line_id = NULL
        WHERE order_sheet_line_id = OLD.id;

        -- Step 2: Set available_quantity to 0 in t_inv_osl
        UPDATE t_inv_osl
        SET available_quantity = 0
        WHERE id = OLD.id;

        -- Step 3: Find matches in t_discs based on updated criteria
        UPDATE t_discs
        SET order_sheet_line_id = NEW.id
        WHERE 
            mps_id = NEW.mps_id AND
            sold_date IS NULL AND
            weight >= NEW.min_weight AND
            weight <= NEW.max_weight AND
            (
                NEW.color_id = 23 OR -- Allow any color when color_id = 23
                color_id = NEW.color_id -- Otherwise, match color_id
            );

        -- Step 4: Update available_quantity in t_inv_osl
        UPDATE t_inv_osl
        SET available_quantity = (
            SELECT COUNT(*) 
            FROM t_discs
            WHERE 
                order_sheet_line_id = NEW.id AND
                sold_date IS NULL
        )
        WHERE id = NEW.id;
    END IF;

    RETURN NULL; -- Triggers of type AFTER return NULL
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.fn_match_on_disc_up_in()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
  sdasin_rec  RECORD;
  v_reason    TEXT;
BEGIN
  ----------------------------------------------------------------------
  -- 1) Delete old joins for this disc
  ----------------------------------------------------------------------
  DELETE FROM tjoin_discs_sdasins
   WHERE disc_id = NEW.id;

  ----------------------------------------------------------------------
  -- 2) Possibly skip if disc sold >14 days
  ----------------------------------------------------------------------
  IF NEW.sold_date IS NOT NULL
     AND NEW.sold_date <= NOW() - INTERVAL '14 days'
  THEN
    RETURN NEW;
  END IF;

  ----------------------------------------------------------------------
  -- 3) Loop over potential SDASINs
  --    For performance, you might filter by MPS match if you wish.
  ----------------------------------------------------------------------
  FOR sdasin_rec IN
    SELECT s.*
    FROM t_sdasins s
    WHERE 
      (s.min_weight IS NOT NULL AND s.max_weight IS NOT NULL)
      AND (s.mps_id IS NOT NULL OR s.mps_id2 IS NOT NULL)
      AND s.color_id IS NOT NULL
      -- optional: check if disc.mps_id in (s.mps_id, s.mps_id2), etc.
  LOOP
    --------------------------------------------------------------------
    -- 3a) Check if matched
    --------------------------------------------------------------------
    v_reason := fn_check_disc_sdasin_match(NEW.id, sdasin_rec.id);

    IF v_reason IS NOT NULL THEN
      ------------------------------------------------------------------
      -- 3b) Insert if matched
      ------------------------------------------------------------------
      INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
      VALUES (NEW.id, sdasin_rec.id, v_reason, 'system')
      ON CONFLICT (disc_id, sdasin_id) DO NOTHING;
    END IF;
  END LOOP;

  ----------------------------------------------------------------------
  -- 4) Optionally update a timestamp on t_discs
  ----------------------------------------------------------------------
  UPDATE t_discs
     SET looked_for_matching_sdasin_at = NOW()
   WHERE id = NEW.id;

  RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.process_disc_sdasin_matching_25_chunk100()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE 
    batch_size INTEGER := 100;
    offset_val INTEGER := 0;
    total_sdasins INTEGER;
    iteration_count INTEGER := 0;
BEGIN
    -- Delete existing records in small batches to avoid long locks
    DELETE FROM tjoin_discs_sdasins WHERE sdasin_id IN (SELECT id FROM t_sdasins LIMIT 5000);

    -- Get the total number of SDASIN records to process
    SELECT COUNT(*) INTO total_sdasins FROM t_sdasins;

    -- Process up to 25 chunks of 100 SDASINs at a time
    WHILE offset_val < total_sdasins AND iteration_count < 25 LOOP
        RAISE NOTICE 'Processing batch % (offset %)', iteration_count + 1, offset_val;

        -- Insert new join records for the current batch of 100 SDASINs
        INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
        SELECT
            d.id AS disc_id,
            s.id AS sdasin_id,
            CASE 
                WHEN (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2) THEN
                    CASE 
                        WHEN s.color_id = 23 THEN 'mps exact, good weight, color varies'
                        ELSE 'mps exact, good weight, color exact'
                    END
                ELSE
                    CASE 
                        WHEN s.color_id = 23 THEN 'mps stock equivalent, good weight, color varies'
                        ELSE 'mps stock equivalent, good weight, color exact'
                    END
            END AS reason,
            'system' AS created_by
        FROM (
            -- Fetch the next batch of 100 SDASINs
            SELECT * FROM t_sdasins
            ORDER BY id
            LIMIT batch_size OFFSET offset_val
        ) s
        CROSS JOIN t_discs d
        -- Join to get the disc’s MPS and stamp info.
        LEFT JOIN t_mps dm ON d.mps_id = dm.id
        LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
        -- Join to get SDASIN’s primary MPS info and stamp.
        LEFT JOIN t_mps sm1 ON s.mps_id = sm1.id
        LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
        -- Join to get SDASIN’s secondary MPS info and stamp.
        LEFT JOIN t_mps sm2 ON s.mps_id2 = sm2.id
        LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
        WHERE
            -- Only consider discs that are unsold or sold within the last 14 days.
            (d.sold_date IS NULL OR d.sold_date > now() - interval '14 days')
            -- Weight criteria: disc's weight must be within the SDASIN's min and max range.
            AND d.weight BETWEEN s.min_weight AND s.max_weight
            -- Color criteria: if SDASIN's color is 23, any disc color qualifies; otherwise, they must match.
            AND (s.color_id = 23 OR d.color_id = s.color_id)
            -- MPS Matching: either an exact match or a stock equivalent match.
            AND (
                -- Exact match: disc.mps_id equals s.mps_id or s.mps_id2.
                (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2)
                OR
                -- Stock equivalent match:
                (
                    (
                        ((sst1.is_sdasin_stock = TRUE OR sst1.id = 38) AND d.mps_id = s.mps_id)
                        OR ((sst2.is_sdasin_stock = TRUE OR sst2.id = 38) AND d.mps_id = s.mps_id2)
                    )
                    AND dst.is_vendor_stock = TRUE
                    AND (
                        (d.mps_id = s.mps_id AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id)
                        OR (d.mps_id = s.mps_id2 AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id)
                    )
                )
            );

        -- Move to the next batch of SDASINs
        offset_val := offset_val + batch_size;
        iteration_count := iteration_count + 1;
    END LOOP;

    -- Update SDASIN timestamps in bulk after processing all batches
    UPDATE t_sdasins
    SET looked_for_matching_discs_at = NOW()
    WHERE id IN (SELECT id FROM t_sdasins ORDER BY id LIMIT batch_size * iteration_count);

    RETURN;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error: %', SQLERRM;
        RETURN;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.f_update_stock_ledger_from_stock_movement()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Determine the stock movement based on the related receipt type
    DECLARE
        var_receipt_type text;
    BEGIN
        -- Get the receipt type associated with the stock movement
        SELECT receipt_type INTO var_receipt_type
        FROM public.receipts
        WHERE id = NEW.receipt_id;  -- Assuming receipt_id is available in stock_movement

        -- Update the stock_on_hand in stock_ledger based on the receipt type
        IF var_receipt_type = 'sale' THEN
            -- Subtract the quantity for sales
            UPDATE public.stock_ledger
            SET stock_on_hand = stock_on_hand - NEW.qty
            WHERE product_id = NEW.product_id;
        ELSIF var_receipt_type IN ('refund', 'voucher', 'adjustment') THEN
            -- Add the quantity for refunds, vouchers, or adjustments
            UPDATE public.stock_ledger
            SET stock_on_hand = stock_on_hand + NEW.qty
            WHERE product_id = NEW.product_id;
        END IF;

        RETURN NEW;  -- Return the new stock movement record
    END;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.get_sellable_id(p_product_id text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Construct the URL for querying a product by its product ID.
    v_url TEXT := 'https://api.veeqo.com/products/' || p_product_id;
    
    -- Variable to hold the HTTP response.
    v_response http_response;
    
    -- Variable to hold the parsed JSON response.
    v_json JSON;
    
    -- Variable to store the sellable id.
    v_sellable_id TEXT;
    
    -- Variable for custom headers.
    v_headers TEXT;
    
    -- Variable for storing the API key from t_config.
    my_api_key TEXT;
BEGIN
    -- Retrieve the API key from t_config (make sure your table and column names are correct).
    SELECT value
      INTO my_api_key
      FROM t_config
     WHERE key = 'veeqo_api_key';
     
    IF my_api_key IS NULL THEN
        RAISE EXCEPTION 'Veeqo API key not found in t_config';
    END IF;
    
    -- Build the headers string using the retrieved API key.
    v_headers := 'Accept: application/json' || E'\n' ||
                 'x-api-key: ' || my_api_key;
    
    -- Set the custom headers for the session using set_config.
    PERFORM set_config('http.custom_headers', v_headers, false);
    
    -- Make the HTTP GET request to the Veeqo API.
    v_response := http_get(v_url);
    
    IF v_response.status = 200 THEN
        -- Optional: output the raw response content for debugging.
        RAISE NOTICE 'HTTP response content: %', v_response.content;
        
        -- Attempt to parse the response content as JSON.
        BEGIN
            v_json := v_response.content::json;
        EXCEPTION WHEN others THEN
            RAISE EXCEPTION 'Failed to parse JSON response: %', v_response.content;
        END;
        
        -- Extract the sellable id from the JSON response.
        -- Assumes the sellable id is in a top-level field called "sellable_id".
        v_sellable_id := v_json->>'sellable_id';
        
        IF v_sellable_id IS NULL THEN
            RAISE NOTICE 'No sellable id found for product id: %', p_product_id;
            RETURN NULL;
        END IF;
        
        RETURN v_sellable_id;
    ELSE
        RAISE EXCEPTION 'Error from Veeqo API (status %): %', v_response.status, v_response.content;
    END IF;
    
EXCEPTION
    WHEN others THEN
        RAISE EXCEPTION 'An error occurred in get_sellable_id: %', SQLERRM;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.regenerate_all_joins()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Delete all existing join records.
  DELETE FROM tjoin_discs_sdasins;

  -- Insert new join records for every disc/SDASIN pair that qualifies.
  INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
  SELECT
    d.id AS disc_id,
    s.id AS sdasin_id,
    CASE 
      WHEN (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2) THEN
        CASE 
          WHEN s.color_id = 23 THEN 'mps exact, good weight, color varies'
          ELSE 'mps exact, good weight, color exact'
        END
      ELSE
        CASE 
          WHEN s.color_id = 23 THEN 'mps stock equivalent, good weight, color varies'
          ELSE 'mps stock equivalent, good weight, color exact'
        END
    END AS reason,
    'system' AS created_by
  FROM t_discs d
    CROSS JOIN t_sdasins s
    -- Join to get the disc’s MPS and stamp info.
    LEFT JOIN t_mps dm ON d.mps_id = dm.id
    LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
    -- Join to get SDASIN’s primary MPS info and stamp.
    LEFT JOIN t_mps sm1 ON s.mps_id = sm1.id
    LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
    -- Join to get SDASIN’s secondary MPS info and stamp.
    LEFT JOIN t_mps sm2 ON s.mps_id2 = sm2.id
    LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
  WHERE
    -- Only consider discs that are unsold or sold within the last 14 days.
    (d.sold_date IS NULL OR d.sold_date > now() - interval '14 days')
    -- Weight criteria: disc's weight is between the SDASIN's min and max (inclusive).
    AND d.weight >= s.min_weight
    AND d.weight <= s.max_weight
    -- Color criteria: if SDASIN's color is 23, any disc color qualifies; otherwise, they must match.
    AND (s.color_id = 23 OR d.color_id = s.color_id)
    -- MPS Matching: either an exact match or a stock equivalent match.
    AND (
         -- Exact match: disc.mps_id equals s.mps_id or s.mps_id2.
         (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2)
         OR
         -- Stock equivalent match:
         (
           (
             ((sst1.is_sdasin_stock = TRUE OR sst1.id = 38) AND d.mps_id = s.mps_id)
             OR
             ((sst2.is_sdasin_stock = TRUE OR sst2.id = 38) AND d.mps_id = s.mps_id2)
           )
           AND dst.is_vendor_stock = TRUE
           AND (
                (d.mps_id = s.mps_id AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id)
                OR
                (d.mps_id = s.mps_id2 AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id)
           )
         )
    );

  -- Update every SDASIN's "looked_for_matching_discs_at" timestamp to indicate the matching time.
  UPDATE t_sdasins
    SET looked_for_matching_discs_at = NOW();

  RETURN;
EXCEPTION
  WHEN OTHERS THEN
    RAISE;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.fn_osl_on_disc_in_up_del()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Handle INSERT
    IF TG_OP = 'INSERT' THEN
        -- Find a matching order sheet line
        UPDATE t_discs
        SET order_sheet_line_id = (
            SELECT id
            FROM t_order_sheet_lines
            WHERE 
                mps_id = NEW.mps_id AND
                (color_id = NEW.color_id OR color_id = 23) AND
                weight >= min_weight AND
                weight <= max_weight
            LIMIT 1
        )
        WHERE id = NEW.id;

        -- If the disc is not sold, increase the relevant t_inv_osl
        IF NEW.sold_date IS NULL THEN
            UPDATE t_inv_osl
            SET available_quantity = available_quantity + 1
            WHERE id = (
                SELECT order_sheet_line_id
                FROM t_discs
                WHERE id = NEW.id
            );
        END IF;
    END IF;

    -- Handle UPDATE
    IF TG_OP = 'UPDATE' THEN
        -- Handle sold_date changes
        IF OLD.sold_date IS NULL AND NEW.sold_date IS NOT NULL THEN
            -- Disc is now sold, decrease quantity
            UPDATE t_inv_osl
            SET available_quantity = available_quantity - 1
            WHERE id = OLD.order_sheet_line_id;
        ELSIF OLD.sold_date IS NOT NULL AND NEW.sold_date IS NULL THEN
            -- Disc is now unsold, increase quantity
            UPDATE t_inv_osl
            SET available_quantity = available_quantity + 1
            WHERE id = OLD.order_sheet_line_id;
        END IF;

        -- Handle changes to weight, color, or mps_id
        IF (OLD.weight IS DISTINCT FROM NEW.weight OR
            OLD.color_id IS DISTINCT FROM NEW.color_id OR
            OLD.mps_id IS DISTINCT FROM NEW.mps_id) THEN

            -- Decrease quantity in old t_inv_osl
            IF OLD.order_sheet_line_id IS NOT NULL THEN
                UPDATE t_inv_osl
                SET available_quantity = available_quantity - 1
                WHERE id = OLD.order_sheet_line_id;
            END IF;

            -- Find a new match in t_order_sheet_lines
            UPDATE t_discs
            SET order_sheet_line_id = (
                SELECT id
                FROM t_order_sheet_lines
                WHERE 
                    mps_id = NEW.mps_id AND
                    (color_id = NEW.color_id OR color_id = 23) AND
                    weight >= min_weight AND
                    weight <= max_weight
                LIMIT 1
            )
            WHERE id = NEW.id;

            -- Increase quantity in new t_inv_osl
            IF NEW.order_sheet_line_id IS NOT NULL AND NEW.sold_date IS NULL THEN
                UPDATE t_inv_osl
                SET available_quantity = available_quantity + 1
                WHERE id = NEW.order_sheet_line_id;
            END IF;
        END IF;
    END IF;

    -- Handle DELETE
    IF TG_OP = 'DELETE' THEN
        -- Decrease quantity in relevant t_inv_osl
        IF OLD.order_sheet_line_id IS NOT NULL AND OLD.sold_date IS NULL THEN
            UPDATE t_inv_osl
            SET available_quantity = available_quantity - 1
            WHERE id = OLD.order_sheet_line_id;
        END IF;
    END IF;

    RETURN NULL; -- Triggers of type AFTER return NULL
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.list_all_products()
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_url TEXT := 'https://api.veeqo.com/products';
    v_response http_response;
    v_json JSON;
    v_headers TEXT;
    my_api_key TEXT;
BEGIN
    -- Retrieve and trim the API key from t_config.
    SELECT trim(value)
      INTO my_api_key
      FROM t_config
     WHERE key = 'veeqo_api_key';
     
    IF my_api_key IS NULL OR my_api_key = '' THEN
        RAISE EXCEPTION 'Veeqo API key not found or empty in t_config';
    END IF;
    
    -- Build the header string exactly as required.
    -- According to your working input bundle, only x-api-key is sent,
    -- but we also add an Accept header to request JSON.
    v_headers := 'x-api-key: ' || my_api_key || E'\n' ||
                 'Accept: application/json';
                 
    -- Optionally, you can add a Content-Type header if desired:
    -- v_headers := v_headers || E'\nContent-Type: application/json';
    
    -- Set the custom headers for the session.
    PERFORM set_config('http.custom_headers', v_headers, false);
    
    -- Make the HTTP GET request.
    v_response := http_get(v_url);
    
    IF v_response.status = 200 THEN
        BEGIN
            v_json := v_response.content::json;
        EXCEPTION WHEN others THEN
            RAISE EXCEPTION 'Failed to parse JSON response: %', v_response.content;
        END;
        RETURN v_json;
    ELSE
        RAISE EXCEPTION 'Error from Veeqo API (status %): %', v_response.status, v_response.content;
    END IF;
    
EXCEPTION
    WHEN others THEN
        RAISE EXCEPTION 'An error occurred in list_all_products: %', SQLERRM;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.regenerate_all_joins_1st100()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Delete all existing join records (optional, but included to ensure clean joins)
    DELETE FROM tjoin_discs_sdasins;

    -- Insert new join records for only the first 100 SDASIN records
    INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
    SELECT
        d.id AS disc_id,
        s.id AS sdasin_id,
        CASE 
            WHEN (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2) THEN
                CASE 
                    WHEN s.color_id = 23 THEN 'mps exact, good weight, color varies'
                    ELSE 'mps exact, good weight, color exact'
                END
            ELSE
                CASE 
                    WHEN s.color_id = 23 THEN 'mps stock equivalent, good weight, color varies'
                    ELSE 'mps stock equivalent, good weight, color exact'
                END
        END AS reason,
        'system' AS created_by
    FROM t_discs d
    CROSS JOIN (
        -- Select only the first 100 SDASIN records
        SELECT * FROM t_sdasins ORDER BY id LIMIT 100
    ) s
    -- Join to get the disc’s MPS and stamp info.
    LEFT JOIN t_mps dm ON d.mps_id = dm.id
    LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
    -- Join to get SDASIN’s primary MPS info and stamp.
    LEFT JOIN t_mps sm1 ON s.mps_id = sm1.id
    LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
    -- Join to get SDASIN’s secondary MPS info and stamp.
    LEFT JOIN t_mps sm2 ON s.mps_id2 = sm2.id
    LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
    WHERE
        -- Only consider discs that are unsold or sold within the last 14 days.
        (d.sold_date IS NULL OR d.sold_date > NOW() - INTERVAL '14 days')
        -- Weight criteria: disc's weight must be within the SDASIN's min and max range.
        AND d.weight BETWEEN s.min_weight AND s.max_weight
        -- Color criteria: if SDASIN's color is 23, any disc color qualifies; otherwise, they must match.
        AND (s.color_id = 23 OR d.color_id = s.color_id)
        -- MPS Matching: either an exact match or a stock equivalent match.
        AND (
            -- Exact match: disc.mps_id equals s.mps_id or s.mps_id2.
            (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2)
            OR
            -- Stock equivalent match:
            (
                (
                    ((sst1.is_sdasin_stock = TRUE OR sst1.id = 38) AND d.mps_id = s.mps_id)
                    OR ((sst2.is_sdasin_stock = TRUE OR sst2.id = 38) AND d.mps_id = s.mps_id2)
                )
                AND dst.is_vendor_stock = TRUE
                AND (
                    (d.mps_id = s.mps_id AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id)
                    OR (d.mps_id = s.mps_id2 AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id)
                )
            )
        );

    -- Update only the first 100 SDASINs to mark them as processed
    UPDATE t_sdasins
    SET looked_for_matching_discs_at = NOW()
    WHERE id IN (SELECT id FROM t_sdasins ORDER BY id LIMIT 100);

    RETURN;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error: %', SQLERRM;
        RETURN;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sql(query text)
 RETURNS TABLE(ddl text)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    RETURN QUERY EXECUTE query;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.manage_inv_sdasin_on_disc_sold_unsold()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
begin
    -- If the disc is marked as sold (sold_date changed from NULL to a non-NULL value)
    if old.sold_date is null and new.sold_date is not null then
        update public.t_inv_sdasin
        set available_quantity = available_quantity - 1
        where id in (
            select sdasin_id
            from public.tjoin_discs_sdasins
            where disc_id = new.id
        );

    -- If the disc is unmarked as sold (sold_date changed from non-NULL to NULL)
    elsif old.sold_date is not null and new.sold_date is null then
        update public.t_inv_sdasin
        set available_quantity = available_quantity + 1
        where id in (
            select sdasin_id
            from public.tjoin_discs_sdasins
            where disc_id = old.id
        );
    end if;

    return new;
end;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.import_csv_to_it_infor_no_buy_box()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
    csv_content TEXT;
BEGIN
    -- Fetch CSV from storage
    SELECT string_agg(line, E'\n')
    INTO csv_content
    FROM storage.objects
    WHERE name = 'from_informed_no_buy_box.csv' AND bucket_id = 'uploads';

    -- Insert data into the table
    INSERT INTO it_infor_no_buy_box
    SELECT * FROM csv_content;

    -- Log success
    RAISE NOTICE 'CSV imported successfully!';
END;
$function$


FUNCTION: CREATE OR REPLACE PROCEDURE public.regenerate_all_joins_chunk100()
 LANGUAGE plpgsql
AS $procedure$
DECLARE 
    batch_size INTEGER := 100;
    offset_val INTEGER := 0;
    total_sdasins INTEGER;
    inserted_count INTEGER;
BEGIN
    -- Delete all existing join records and commit immediately
    DELETE FROM tjoin_discs_sdasins;
    COMMIT;

    -- Get the total number of SDASIN records to process
    SELECT COUNT(*) INTO total_sdasins FROM t_sdasins;
    
    IF total_sdasins = 0 THEN
        RAISE NOTICE 'No SDASIN records found. Exiting procedure.';
        RETURN;
    END IF;

    RAISE NOTICE 'Starting processing of % SDASIN records in batches of %', total_sdasins, batch_size;

    -- Process SDASIN records in chunks of 100
    WHILE offset_val < total_sdasins LOOP
        RAISE NOTICE 'Processing batch starting at offset %', offset_val;

        -- Insert new join records for the current batch of 100 SDASINs
        INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
        SELECT
            d.id AS disc_id,
            s.id AS sdasin_id,
            CASE 
                WHEN (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2) THEN
                    CASE 
                        WHEN s.color_id = 23 THEN 'mps exact, good weight, color varies'
                        ELSE 'mps exact, good weight, color exact'
                    END
                ELSE
                    CASE 
                        WHEN s.color_id = 23 THEN 'mps stock equivalent, good weight, color varies'
                        ELSE 'mps stock equivalent, good weight, color exact'
                    END
            END AS reason,
            'system' AS created_by
        FROM t_discs d
        CROSS JOIN (
            -- Fetch the next batch of 100 SDASINs
            SELECT * FROM t_sdasins
            ORDER BY id
            LIMIT batch_size OFFSET offset_val
        ) s
        -- Join to get the disc’s MPS and stamp info.
        LEFT JOIN t_mps dm ON d.mps_id = dm.id
        LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
        -- Join to get SDASIN’s primary MPS info and stamp.
        LEFT JOIN t_mps sm1 ON s.mps_id = sm1.id
        LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
        -- Join to get SDASIN’s secondary MPS info and stamp.
        LEFT JOIN t_mps sm2 ON s.mps_id2 = sm2.id
        LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
        WHERE
            -- Only consider discs that are unsold or sold within the last 14 days.
            (d.sold_date IS NULL OR d.sold_date > NOW() - INTERVAL '14 days')
            -- Weight criteria: disc's weight must be within the SDASIN's min and max range.
            AND d.weight BETWEEN s.min_weight AND s.max_weight
            -- Color criteria: if SDASIN's color is 23, any disc color qualifies; otherwise, they must match.
            AND (s.color_id = 23 OR d.color_id = s.color_id)
            -- MPS Matching: either an exact match or a stock equivalent match.
            AND (
                (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2)
                OR (
                    (
                        ((sst1.is_sdasin_stock = TRUE OR sst1.id = 38) AND d.mps_id = s.mps_id)
                        OR ((sst2.is_sdasin_stock = TRUE OR sst2.id = 38) AND d.mps_id = s.mps_id2)
                    )
                    AND dst.is_vendor_stock = TRUE
                    AND (
                        (d.mps_id = s.mps_id AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id)
                        OR (d.mps_id = s.mps_id2 AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id)
                    )
                )
            );

        GET DIAGNOSTICS inserted_count = ROW_COUNT;
        RAISE NOTICE 'Batch starting at offset % inserted % rows.', offset_val, inserted_count;

        -- Commit after every batch to free memory and release locks
        COMMIT;
        
        -- Update the processed SDASIN records with the current timestamp
        UPDATE t_sdasins
        SET looked_for_matching_discs_at = NOW()
        WHERE id IN (
            SELECT id FROM t_sdasins
            ORDER BY id
            LIMIT batch_size OFFSET offset_val
        );
        COMMIT;

        -- Move to the next batch
        offset_val := offset_val + batch_size;
    END LOOP;

    -- Final bulk update: update any remaining SDASIN timestamps if necessary
    UPDATE t_sdasins
    SET looked_for_matching_discs_at = NOW();
    COMMIT;

    RAISE NOTICE 'Finished processing all % SDASIN records.', total_sdasins;
END;
$procedure$


FUNCTION: CREATE OR REPLACE PROCEDURE public.regenerate_all_joins_chunk50()
 LANGUAGE plpgsql
AS $procedure$
DECLARE 
    batch_size INTEGER := 50;
    offset_val INTEGER := 0;
    total_sdasins INTEGER;
    inserted_count INTEGER;
BEGIN
    -- Delete all existing join records and commit immediately
    DELETE FROM tjoin_discs_sdasins;
    COMMIT;

    -- Get the total number of SDASIN records to process
    SELECT COUNT(*) INTO total_sdasins FROM t_sdasins;
    
    IF total_sdasins = 0 THEN
        RAISE NOTICE 'No SDASIN records found. Exiting procedure.';
        RETURN;
    END IF;

    RAISE NOTICE 'Starting processing of % SDASIN records in batches of %', total_sdasins, batch_size;

    -- Process SDASIN records in chunks of 50 at a time
    WHILE offset_val < total_sdasins LOOP
        RAISE NOTICE 'Processing batch starting at offset %', offset_val;

        INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
        SELECT
            d.id AS disc_id,
            s.id AS sdasin_id,
            CASE 
                WHEN (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2) THEN
                    CASE 
                        WHEN s.color_id = 23 THEN 'mps exact, good weight, color varies'
                        ELSE 'mps exact, good weight, color exact'
                    END
                ELSE
                    CASE 
                        WHEN s.color_id = 23 THEN 'mps stock equivalent, good weight, color varies'
                        ELSE 'mps stock equivalent, good weight, color exact'
                    END
            END AS reason,
            'system' AS created_by
        FROM t_discs d
        CROSS JOIN (
            SELECT * FROM t_sdasins
            ORDER BY id
            LIMIT batch_size OFFSET offset_val
        ) s
        LEFT JOIN t_mps dm ON d.mps_id = dm.id
        LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
        LEFT JOIN t_mps sm1 ON s.mps_id = sm1.id
        LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
        LEFT JOIN t_mps sm2 ON s.mps_id2 = sm2.id
        LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
        WHERE
            (d.sold_date IS NULL OR d.sold_date > NOW() - INTERVAL '14 days')
            AND d.weight BETWEEN s.min_weight AND s.max_weight
            AND (s.color_id = 23 OR d.color_id = s.color_id)
            AND (
                (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2)
                OR (
                    (
                        ((sst1.is_sdasin_stock = TRUE OR sst1.id = 38) AND d.mps_id = s.mps_id)
                        OR ((sst2.is_sdasin_stock = TRUE OR sst2.id = 38) AND d.mps_id = s.mps_id2)
                    )
                    AND dst.is_vendor_stock = TRUE
                    AND (
                        (d.mps_id = s.mps_id AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id)
                        OR (d.mps_id = s.mps_id2 AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id)
                    )
                )
            );

        GET DIAGNOSTICS inserted_count = ROW_COUNT;
        RAISE NOTICE 'Batch starting at offset % inserted % rows.', offset_val, inserted_count;

        COMMIT;
        
        UPDATE t_sdasins
        SET looked_for_matching_discs_at = NOW()
        WHERE id IN (
            SELECT id FROM t_sdasins ORDER BY id LIMIT batch_size OFFSET offset_val
        );
        COMMIT;

        offset_val := offset_val + batch_size;
    END LOOP;

    UPDATE t_sdasins
    SET looked_for_matching_discs_at = NOW();
    COMMIT;

    RAISE NOTICE 'Finished processing all % SDASIN records.', total_sdasins;
END;
$procedure$


FUNCTION: CREATE OR REPLACE FUNCTION public.regenerate_all_joins_chunk100_fn()
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE 
    batch_size INTEGER := 100;
    offset_val INTEGER := 0;
    total_sdasins INTEGER;
BEGIN
    -- Delete all existing join records (using a dummy WHERE clause)
    DELETE FROM tjoin_discs_sdasins WHERE true;
    
    -- Get the total number of SDASIN records to process
    SELECT COUNT(*) INTO total_sdasins FROM t_sdasins;
    
    IF total_sdasins = 0 THEN
        RETURN 'No SDASIN records found.';
    END IF;
    
    WHILE offset_val < total_sdasins LOOP
        INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
        SELECT
            d.id AS disc_id,
            s.id AS sdasin_id,
            CASE 
                WHEN (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2) THEN
                    CASE 
                        WHEN s.color_id = 23 THEN 'mps exact, good weight, color varies'
                        ELSE 'mps exact, good weight, color exact'
                    END
                ELSE
                    CASE 
                        WHEN s.color_id = 23 THEN 'mps stock equivalent, good weight, color varies'
                        ELSE 'mps stock equivalent, good weight, color exact'
                    END
            END AS reason,
            'system' AS created_by
        FROM t_discs d
        CROSS JOIN (
            -- Fetch the next batch of 100 SDASINs
            SELECT * FROM t_sdasins
            ORDER BY id
            LIMIT batch_size OFFSET offset_val
        ) s
        LEFT JOIN t_mps dm ON d.mps_id = dm.id
        LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
        LEFT JOIN t_mps sm1 ON s.mps_id = sm1.id
        LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
        LEFT JOIN t_mps sm2 ON s.mps_id2 = sm2.id
        LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
        WHERE
            (d.sold_date IS NULL OR d.sold_date > NOW() - INTERVAL '14 days')
            AND d.weight BETWEEN s.min_weight AND s.max_weight
            AND (s.color_id = 23 OR d.color_id = s.color_id)
            AND (
                (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2)
                OR (
                    (
                        ((sst1.is_sdasin_stock = TRUE OR sst1.id = 38) AND d.mps_id = s.mps_id)
                        OR ((sst2.is_sdasin_stock = TRUE OR sst2.id = 38) AND d.mps_id = s.mps_id2)
                    )
                    AND dst.is_vendor_stock = TRUE
                    AND (
                        (d.mps_id = s.mps_id AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id)
                        OR (d.mps_id = s.mps_id2 AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id)
                    )
                )
            );
            
        offset_val := offset_val + batch_size;
    END LOOP;
    
    RETURN 'Processing complete for ' || total_sdasins || ' SDASIN records.';
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.regenerate_all_joins_chunk25_fn()
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE 
    batch_size INTEGER := 25;
    offset_val INTEGER := 0;
    total_sdasins INTEGER;
BEGIN
    -- Delete all existing join records (using a dummy WHERE clause)
    DELETE FROM tjoin_discs_sdasins WHERE true;
    
    -- Get the total number of SDASIN records to process
    SELECT COUNT(*) INTO total_sdasins FROM t_sdasins;
    
    IF total_sdasins = 0 THEN
        RETURN 'No SDASIN records found.';
    END IF;
    
    WHILE offset_val < total_sdasins LOOP
        INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
        SELECT
            d.id AS disc_id,
            s.id AS sdasin_id,
            CASE 
                WHEN (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2) THEN
                    CASE 
                        WHEN s.color_id = 23 THEN 'mps exact, good weight, color varies'
                        ELSE 'mps exact, good weight, color exact'
                    END
                ELSE
                    CASE 
                        WHEN s.color_id = 23 THEN 'mps stock equivalent, good weight, color varies'
                        ELSE 'mps stock equivalent, good weight, color exact'
                    END
            END AS reason,
            'system' AS created_by
        FROM t_discs d
        CROSS JOIN (
            -- Fetch the next batch of 25 SDASINs
            SELECT * FROM t_sdasins
            ORDER BY id
            LIMIT batch_size OFFSET offset_val
        ) s
        LEFT JOIN t_mps dm ON d.mps_id = dm.id
        LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
        LEFT JOIN t_mps sm1 ON s.mps_id = sm1.id
        LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
        LEFT JOIN t_mps sm2 ON s.mps_id2 = sm2.id
        LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
        WHERE
            (d.sold_date IS NULL OR d.sold_date > NOW() - INTERVAL '14 days')
            AND d.weight BETWEEN s.min_weight AND s.max_weight
            AND (s.color_id = 23 OR d.color_id = s.color_id)
            AND (
                (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2)
                OR (
                    (
                        ((sst1.is_sdasin_stock = TRUE OR sst1.id = 38) AND d.mps_id = s.mps_id)
                        OR ((sst2.is_sdasin_stock = TRUE OR sst2.id = 38) AND d.mps_id = s.mps_id2)
                    )
                    AND dst.is_vendor_stock = TRUE
                    AND (
                        (d.mps_id = s.mps_id AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id)
                        OR (d.mps_id = s.mps_id2 AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id)
                    )
                )
            );
            
        offset_val := offset_val + batch_size;
    END LOOP;
    
    RETURN 'Processing complete for ' || total_sdasins || ' SDASIN records.';
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.regenerate_all_joins_chunk5_fn()
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE 
    batch_size INTEGER := 5;
    offset_val INTEGER := 0;
    total_sdasins INTEGER;
BEGIN
    -- Delete all existing join records (using a dummy WHERE clause)
    DELETE FROM tjoin_discs_sdasins WHERE true;
    
    -- Get the total number of SDASIN records to process
    SELECT COUNT(*) INTO total_sdasins FROM t_sdasins;
    
    IF total_sdasins = 0 THEN
        RETURN 'No SDASIN records found.';
    END IF;
    
    -- Loop through the SDASIN records in batches of 5
    WHILE offset_val < total_sdasins LOOP
        INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
        SELECT
            d.id AS disc_id,
            s.id AS sdasin_id,
            CASE 
                WHEN (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2) THEN
                    CASE 
                        WHEN s.color_id = 23 THEN 'mps exact, good weight, color varies'
                        ELSE 'mps exact, good weight, color exact'
                    END
                ELSE
                    CASE 
                        WHEN s.color_id = 23 THEN 'mps stock equivalent, good weight, color varies'
                        ELSE 'mps stock equivalent, good weight, color exact'
                    END
            END AS reason,
            'system' AS created_by
        FROM t_discs d
        CROSS JOIN (
            -- Fetch the next batch of 5 SDASIN records
            SELECT * FROM t_sdasins
            ORDER BY id
            LIMIT batch_size OFFSET offset_val
        ) s
        -- Join to get the disc’s MPS and stamp info.
        LEFT JOIN t_mps dm ON d.mps_id = dm.id
        LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
        LEFT JOIN t_mps sm1 ON s.mps_id = sm1.id
        LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
        LEFT JOIN t_mps sm2 ON s.mps_id2 = sm2.id
        LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
        WHERE
            -- Only consider discs that are unsold or sold within the last 14 days.
            (d.sold_date IS NULL OR d.sold_date > NOW() - INTERVAL '14 days')
            -- Weight criteria: disc's weight must be between the SDASIN's min and max range.
            AND d.weight BETWEEN s.min_weight AND s.max_weight
            -- Color criteria: if SDASIN's color is 23, any disc color qualifies; otherwise, they must match.
            AND (s.color_id = 23 OR d.color_id = s.color_id)
            -- MPS Matching: either an exact match or a stock equivalent match.
            AND (
                (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2)
                OR (
                    (
                        ((sst1.is_sdasin_stock = TRUE OR sst1.id = 38) AND d.mps_id = s.mps_id)
                        OR ((sst2.is_sdasin_stock = TRUE OR sst2.id = 38) AND d.mps_id = s.mps_id2)
                    )
                    AND dst.is_vendor_stock = TRUE
                    AND (
                        (d.mps_id = s.mps_id AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id)
                        OR (d.mps_id = s.mps_id2 AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id)
                    )
                )
            );
        
        -- Increase the offset to process the next batch of 5 records
        offset_val := offset_val + batch_size;
    END LOOP;
    
    RETURN 'Processing complete for ' || total_sdasins || ' SDASIN records.';
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.regenerate_joins_10th100_fn()
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE 
    batch_size INTEGER := 100;
    offset_val INTEGER := 900;  -- Process records 901 to 1000 (10th batch)
BEGIN
    -- Insert new join records for just the 10th batch (records 901-1000)
    INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
    SELECT
        d.id AS disc_id,
        s.id AS sdasin_id,
        CASE 
            WHEN (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2) THEN
                CASE 
                    WHEN s.color_id = 23 THEN 'mps exact, good weight, color varies'
                    ELSE 'mps exact, good weight, color exact'
                END
            ELSE
                CASE 
                    WHEN s.color_id = 23 THEN 'mps stock equivalent, good weight, color varies'
                    ELSE 'mps stock equivalent, good weight, color exact'
                END
        END AS reason,
        'system' AS created_by
    FROM t_discs d
    CROSS JOIN (
        -- Fetch exactly 100 SDASIN records starting at offset 900
        SELECT * FROM t_sdasins
        ORDER BY id
        LIMIT batch_size OFFSET offset_val
    ) s
    LEFT JOIN t_mps dm ON d.mps_id = dm.id
    LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
    LEFT JOIN t_mps sm1 ON s.mps_id = sm1.id
    LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
    LEFT JOIN t_mps sm2 ON s.mps_id2 = sm2.id
    LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
    WHERE
        -- Only consider discs that are unsold or sold within the last 14 days.
        (d.sold_date IS NULL OR d.sold_date > NOW() - INTERVAL '14 days')
        -- Weight criteria: disc's weight must be within the SDASIN's min and max range.
        AND d.weight BETWEEN s.min_weight AND s.max_weight
        -- Color criteria: if SDASIN's color is 23, any disc color qualifies; otherwise, they must match.
        AND (s.color_id = 23 OR d.color_id = s.color_id)
        -- MPS Matching: either an exact match or a stock equivalent match.
        AND (
            (d.mps_id = s.mps_id OR d.mps_id = s.mps_id2)
            OR (
                (
                    ((sst1.is_sdasin_stock = TRUE OR sst1.id = 38) AND d.mps_id = s.mps_id)
                    OR ((sst2.is_sdasin_stock = TRUE OR sst2.id = 38) AND d.mps_id = s.mps_id2)
                )
                AND dst.is_vendor_stock = TRUE
                AND (
                    (d.mps_id = s.mps_id AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id)
                    OR (d.mps_id = s.mps_id2 AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id)
                )
            )
        )
    ON CONFLICT (disc_id, sdasin_id) DO NOTHING;
        
    -- Optionally update the timestamp for just these 100 SDASINs
    UPDATE t_sdasins
    SET looked_for_matching_discs_at = NOW()
    WHERE id IN (
        SELECT id FROM t_sdasins ORDER BY id LIMIT batch_size OFFSET offset_val
    );
    
    RETURN 'Processed 10th batch (records 901-1000) of SDASINs.';
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'Error: ' || SQLERRM;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.f_receipt_completed()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    stock_line RECORD;
BEGIN
    -- Update the completed_at column for the receipt
    UPDATE receipts
    SET completed_at = NOW()
    WHERE id = NEW.id;

    -- Iterate over related stock_movement_lines
    FOR stock_line IN
        SELECT * FROM stock_movement_line
        WHERE receipt_id = NEW.id AND complete = FALSE
    LOOP
        -- Update the stock_ledger table
        UPDATE stock_ledger
        SET stock_on_hand = stock_on_hand +
            CASE
                WHEN (SELECT receipt_type FROM receipts WHERE id = NEW.id) IN ('adjustment', 'refund', 'voucher') THEN stock_line.qty
                WHEN (SELECT receipt_type FROM receipts WHERE id = NEW.id) = 'sale' THEN -stock_line.qty
            END,
            last_change_date = NOW()
        WHERE product_id = stock_line.product_id;

        -- Mark the stock_movement_line as complete
        UPDATE stock_movement_line
        SET complete = TRUE,
            completed_at = NOW()
        WHERE id = stock_line.id;
    END LOOP;

    RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.export_csv_from_v_informed_upload()
 RETURNS bytea
 LANGUAGE plpgsql
AS $function$
DECLARE
    csv_data bytea;
BEGIN
    -- Generate CSV with headers
    csv_data := (
        SELECT convert_to(
            (
                SELECT string_agg(
                    line,
                    E'\n'
                )
                FROM (
                    SELECT 'sku,marketplace_id,cost,currency,min_price,max_price,map_price,listing_type,strategy_id,managed,dateadded' AS line
                    UNION ALL
                    SELECT FORMAT(
                        '%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s',
                        sku,
                        marketplace_id,
                        cost,
                        currency,
                        min_price,
                        max_price,
                        map_price,
                        listing_type,
                        strategy_id,
                        managed,
                        dateadded
                    ) AS line
                    FROM v_informed_upload
                ) AS csv_lines
            ),
            'utf8'
        )
    );
    RETURN csv_data;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.auto_verify_image()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    base_url     TEXT;
    full_url     TEXT;
    config_key   TEXT;
    prefix       TEXT;
    domain       TEXT;
    webhook_url  TEXT;
    json_payload TEXT;
    new_file_name TEXT;
BEGIN
    -- Capture the updated image_file_name
    new_file_name := NEW.image_file_name;

    -- Build config key from the table name
    config_key := 'public_folder_url_for_' || NEW.table_name;
    SELECT value INTO base_url FROM t_config WHERE key = config_key;
    IF base_url IS NULL THEN
        RAISE EXCEPTION 'Configuration key % not found in t_config', config_key;
    END IF;

    -- Build the full image URL using the new file name
    full_url := base_url || '/' || new_file_name;

    -- Get the current webhook prefix
    SELECT value INTO prefix FROM t_config WHERE key = 'current_webhook_4_character_prefix';
    IF prefix IS NULL THEN
      RAISE EXCEPTION 'No value found for current_webhook_4_character_prefix in t_config';
    END IF;

    -- Construct the webhook domain and URL
    domain := prefix || '-24-124-112-70.ngrok-free.app';
    webhook_url := 'https://' || domain || '/verify-image';

    -- Build the JSON payload for your verification server
    json_payload := json_build_object('id', NEW.id, 'url', full_url)::TEXT;

    -- Attempt the HTTP POST call.
    -- If it times out or fails, catch the exception and log it so the update still proceeds.
    BEGIN
        PERFORM content
          FROM http_post(
            webhook_url,
            json_payload,
            'application/json'
          );
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'HTTP POST call failed: %', SQLERRM;
    END;

    -- Mark the verification as “in progress”
    NEW.image_verified_at := now();
    NEW.image_verified_notes := 'Verification in progress';

    -- Explicitly restore the new image_file_name so it isn’t overwritten
    NEW.image_file_name := new_file_name;

    RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.update_veeqo_sellable_quantity()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    veeqo_sellable_id TEXT;
    veeqo_api_url      TEXT;
    payload            TEXT;
    headers            TEXT;
    status_code        INT;
    http_response      JSONB;
    api_key            TEXT;
    response_text      TEXT;
BEGIN
    RAISE NOTICE 'Trigger update_veeqo_sellable_quantity fired for id=%', NEW.id;

    -- Only proceed if available_quantity has changed.
    IF NEW.available_quantity = OLD.available_quantity THEN
        RAISE NOTICE 'No change in available_quantity. Old: %, New: %', OLD.available_quantity, NEW.available_quantity;
        RETURN NEW;
    END IF;

    -- Retrieve the API key from t_config
    SELECT value INTO api_key
    FROM t_config
    WHERE key = 'veeqo_api_key'
    LIMIT 1;

    IF api_key IS NULL THEN
        RAISE NOTICE 'No API key found in t_config for key: veeqo_api_key';
        IF TG_TABLE_NAME = 't_inv_sdasins' THEN
            UPDATE t_inv_sdasins
            SET veeqo_qty_update_notes = 'No API key found in t_config for key: veeqo_api_key'
            WHERE id = NEW.id;
        ELSE
            UPDATE t_inv_osl
            SET veeqo_qty_update_notes = 'No API key found in t_config for key: veeqo_api_key'
            WHERE id = NEW.id;
        END IF;
        RETURN NEW;
    END IF;

    -- Determine the source table and get veeqo_id
    IF TG_TABLE_NAME = 't_inv_sdasins' THEN
        SELECT s.veeqo_id INTO veeqo_sellable_id
        FROM t_sdasins s
        WHERE s.id = NEW.id;

        IF veeqo_sellable_id IS NULL THEN
            UPDATE t_sdasins SET id = id WHERE id = NEW.id;
            SELECT s.veeqo_id INTO veeqo_sellable_id
            FROM t_sdasins s
            WHERE s.id = NEW.id;
        END IF;

    ELSIF TG_TABLE_NAME = 't_inv_osl' THEN
        SELECT osl.veeqo_id INTO veeqo_sellable_id
        FROM t_order_sheet_lines osl
        WHERE osl.id = NEW.id;

        IF veeqo_sellable_id IS NULL THEN
            UPDATE t_order_sheet_lines SET id = id WHERE id = NEW.id;
            SELECT osl.veeqo_id INTO veeqo_sellable_id
            FROM t_order_sheet_lines osl
            WHERE osl.id = NEW.id;
        END IF;
    END IF;

    IF veeqo_sellable_id IS NULL THEN
        RAISE NOTICE 'Veeqo ID is still null after update.';
        IF TG_TABLE_NAME = 't_inv_sdasins' THEN
            UPDATE t_inv_sdasins
            SET veeqo_qty_update_notes = 'Veeqo ID is null after attempted update.'
            WHERE id = NEW.id;
        ELSE
            UPDATE t_inv_osl
            SET veeqo_qty_update_notes = 'Veeqo ID is null after attempted update.'
            WHERE id = NEW.id;
        END IF;
        RETURN NEW;
    END IF;

    -- Construct the API URL
    veeqo_api_url := 'https://api.veeqo.com/sellables/' || veeqo_sellable_id || '/warehouses/99881/stock_entry';
    RAISE NOTICE 'Constructed API URL: %', veeqo_api_url;

    -- Construct JSON payload
    payload := '{"stock_entry": {"physical_stock_level": ' || NEW.available_quantity || ', "infinite": false}}';
    RAISE NOTICE 'Constructed payload: %', payload;

    -- Build headers
    headers := 'Content-Type:application/json' || E'\n' ||
               'x-api-key:' || api_key || E'\n' ||
               'User-Agent:PostgreSQL-Http-Client';

    -- Make the PUT request
    BEGIN
        SELECT status, content::JSONB
        INTO status_code, http_response
        FROM public.http_put(veeqo_api_url, payload, headers);

        RAISE NOTICE 'Veeqo API call returned status: %', status_code;
        RAISE NOTICE 'Veeqo API response: %', http_response;

        response_text := COALESCE(http_response::TEXT, 'No response body returned. Status code: ' || status_code);

        IF status_code = 200 THEN
            IF TG_TABLE_NAME = 't_inv_sdasins' THEN
                UPDATE t_inv_sdasins
                SET veeqo_qty_updated_at = now(),
                    veeqo_qty_update_notes = 'Veeqo update successful, status code ' || status_code,
                    veeqo_qty_update_full_http_response = response_text
                WHERE id = NEW.id;
            ELSE
                UPDATE t_inv_osl
                SET veeqo_qty_updated_at = now(),
                    veeqo_qty_update_notes = 'Veeqo update successful, status code ' || status_code,
                    veeqo_qty_update_full_http_response = response_text
                WHERE id = NEW.id;
            END IF;
        ELSE
            IF TG_TABLE_NAME = 't_inv_sdasins' THEN
                UPDATE t_inv_sdasins
                SET veeqo_qty_update_notes = 'Veeqo update failed, status code ' || status_code || '. ' || response_text,
                    veeqo_qty_update_full_http_response = response_text
                WHERE id = NEW.id;
            ELSE
                UPDATE t_inv_osl
                SET veeqo_qty_update_notes = 'Veeqo update failed, status code ' || status_code || '. ' || response_text,
                    veeqo_qty_update_full_http_response = response_text
                WHERE id = NEW.id;
            END IF;
        END IF;

    EXCEPTION WHEN OTHERS THEN
        response_text := 'Error: ' || SQLERRM;
        IF TG_TABLE_NAME = 't_inv_sdasins' THEN
            UPDATE t_inv_sdasins
            SET veeqo_qty_update_notes = response_text,
                veeqo_qty_update_full_http_response = response_text
            WHERE id = NEW.id;
        ELSE
            UPDATE t_inv_osl
            SET veeqo_qty_update_notes = response_text,
                veeqo_qty_update_full_http_response = response_text
            WHERE id = NEW.id;
        END IF;
    END;

    RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.manage_sdasin_updates()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    is_match boolean;
    d record;  -- Iterate over discs
    r record;  -- Iterate over matching rules
    c record;  -- Iterate over criteria for each rule
BEGIN
    -- Step 1: Delete existing joins for this SDASIN
    DELETE FROM tjoin_discs_sdasins
     WHERE sdasin_id = NEW.id;

    -- Step 2: Loop over relevant discs (unsold or sold within last 14 days)
    FOR d IN
        SELECT *
        FROM t_discs
        WHERE
          (
            sold_date IS NULL
            OR sold_date > now() - interval '14 days'
          )
          AND (
            mps_id = NEW.mps_id
            OR (NEW.mps_id2 IS NOT NULL AND mps_id = NEW.mps_id2)
          )
    LOOP
        -- Step 3: Loop over each active matching rule
        FOR r IN
            SELECT *
            FROM t_matching_rules
            WHERE active = TRUE
        LOOP
            is_match := TRUE;  -- Assume match until a criterion fails

            -- Step 4: Check each criterion for the current rule
            FOR c IN
                SELECT *
                FROM t_matching_rule_criteria
                WHERE matching_rule_id = r.id
                ORDER BY id
            LOOP
                -- Interpret criteria based on field_name, operator, and value

                IF c.field_name = 'color_id' THEN
                    -- Criterion: disc.color_id must match sdasin.color_id
                    IF c.operator = '=' AND c.value = 'color_id' THEN
                        IF d.color_id <> NEW.color_id THEN
                            is_match := FALSE;
                            EXIT;  -- Skip remaining criteria for this rule
                        END IF;
                    -- Criterion: sdasin.color_id must be 23 (ignore disc color)
                    ELSIF c.operator = '=' AND c.value = '23' THEN
                        IF NEW.color_id <> 23 THEN
                            is_match := FALSE;
                            EXIT;
                        END IF;
                    END IF;

                ELSIF c.field_name = 'weight' THEN
                    -- Criterion: disc.weight >= sdasin.min_weight
                    IF c.operator = '>=' AND c.value = 'min_weight' THEN
                        IF d.weight < NEW.min_weight THEN
                            is_match := FALSE;
                            EXIT;
                        END IF;
                    -- Criterion: disc.weight <= sdasin.max_weight
                    ELSIF c.operator = '<=' AND c.value = 'max_weight' THEN
                        IF d.weight > NEW.max_weight THEN
                            is_match := FALSE;
                            EXIT;
                        END IF;
                    END IF;

                ELSIF c.field_name = 'stamp' THEN
                    -- Add stamp-related criteria here if applicable
                    -- Example:
                    -- IF c.operator = '=' AND c.value = 'desired_stamp' THEN
                    --     IF d.stamp <> desired_stamp THEN
                    --         is_match := FALSE;
                    --         EXIT;
                    --     END IF;
                    -- END IF;
                    -- (Customize based on your stamp logic)

                ELSE
                    RAISE NOTICE 'Unrecognized field/operator/value: %, %, %', 
                                 c.field_name, c.operator, c.value;
                END IF;
            END LOOP; -- End criteria loop

            -- Step 5: If all criteria passed, insert the join
            IF is_match THEN
                INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, matching_rule_id, created_by)
                VALUES (d.id, NEW.id, r.id, 'system')
                ON CONFLICT (disc_id, sdasin_id) DO NOTHING;
            END IF;
        END LOOP; -- End rules loop
    END LOOP; -- End discs loop

    -- Step 6: Update the timestamp
    UPDATE t_sdasins
       SET looked_for_matching_discs_at = NOW()
     WHERE id = NEW.id;

    RETURN NEW;

EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO t_error_logs (error_message, context)
        VALUES (SQLERRM, 'manage_sdasin_updates: Trigger execution error');
        RAISE;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.manage_inv_on_join_inserts_and_deletes()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Handle INSERT: Track affected sdasin_id
    IF TG_OP = 'INSERT' THEN
        UPDATE t_inv_sdasin
        SET available_quantity = (
            SELECT COUNT(*)
            FROM tjoin_discs_sdasins tjoin
            INNER JOIN t_discs d ON tjoin.disc_id = d.id
            WHERE tjoin.sdasin_id = NEW.sdasin_id
              AND d.sold_date IS NULL
        )
        WHERE id = NEW.sdasin_id;
    END IF;

    -- Handle DELETE: Track affected sdasin_id
    IF TG_OP = 'DELETE' THEN
        UPDATE t_inv_sdasin
        SET available_quantity = (
            SELECT COUNT(*)
            FROM tjoin_discs_sdasins tjoin
            INNER JOIN t_discs d ON tjoin.disc_id = d.id
            WHERE tjoin.sdasin_id = OLD.sdasin_id
              AND d.sold_date IS NULL
        )
        WHERE id = OLD.sdasin_id;
    END IF;

    -- Handle TRUNCATE: Set all quantities to zero
    IF TG_OP = 'TRUNCATE' THEN
        UPDATE t_inv_sdasin
        SET available_quantity = 0;
    END IF;

    RETURN NULL; -- Triggers do not return a modified row
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.f_manage_sdasin_insert_updates()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
  rec RECORD;
  computed_reason text;
BEGIN
  -- Step 1: Delete any existing join records for this SDASIN (if any).
  DELETE FROM tjoin_discs_sdasins
   WHERE sdasin_id = NEW.id;

  -- Step 2: Loop over candidate discs that are unsold or sold within the last 14 days.
  FOR rec IN
    SELECT
      d.id AS disc_id,
      -- Compute the reason using a nested CASE expression.
      CASE 
        WHEN (d.mps_id = NEW.mps_id OR d.mps_id = NEW.mps_id2) THEN
          CASE 
            WHEN NEW.color_id = 23 THEN 'mps exact, good weight, color varies'
            WHEN NEW.color_id IS NOT NULL THEN 'mps exact, good weight, color exact'
            ELSE 'mps exact, good weight, color unknown'
          END
        ELSE
          CASE 
            WHEN NEW.color_id = 23 THEN 'mps stock equivalent, good weight, color varies'
            WHEN NEW.color_id IS NOT NULL THEN 'mps stock equivalent, good weight, color exact'
            ELSE 'mps stock equivalent, good weight, color unknown'
          END
      END AS reason
    FROM t_discs d
      -- Join for the disc's mps and its stamp.
      LEFT JOIN t_mps dm ON d.mps_id = dm.id
      LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
      -- For stock equivalent matching, join the two possible SDASIN mps records and stamps.
      LEFT JOIN t_mps sm1 ON NEW.mps_id = sm1.id
      LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
      LEFT JOIN t_mps sm2 ON NEW.mps_id2 = sm2.id
      LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
    WHERE
      -- Only consider discs that are unsold or sold within the last 14 days.
      (d.sold_date IS NULL OR d.sold_date > now() - interval '14 days')
      -- Weight criteria (inclusive).
      AND d.weight >= NEW.min_weight
      AND d.weight <= NEW.max_weight
      -- Color criteria: if SDASIN color is 23, any disc color qualifies; otherwise, colors must match.
      AND (NEW.color_id = 23 OR d.color_id = NEW.color_id)
      -- MPS matching:
      AND (
           -- Exact match on mps.
           (d.mps_id = NEW.mps_id OR d.mps_id = NEW.mps_id2)
           OR
           -- Stock equivalent match:
           (
             (
               ((sst1.is_sdasin_stock = TRUE OR sst1.id = 38) AND d.mps_id = NEW.mps_id)
               OR
               ((sst2.is_sdasin_stock = TRUE OR sst2.id = 38) AND d.mps_id = NEW.mps_id2)
             )
             AND dst.is_vendor_stock = TRUE
             AND (
                  (d.mps_id = NEW.mps_id AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id)
                  OR
                  (d.mps_id = NEW.mps_id2 AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id)
             )
           )
      )
  LOOP
    -- Explicitly assign the computed reason.
    computed_reason := rec.reason;
    INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
    VALUES (rec.disc_id, NEW.id, computed_reason, 'system')
    ON CONFLICT (disc_id, sdasin_id) DO NOTHING;
  END LOOP;

  -- Step 3: Update the timestamp to indicate matching was performed.
  UPDATE t_sdasins
    SET looked_for_matching_discs_at = NOW()
  WHERE id = NEW.id;

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    INSERT INTO t_error_logs (error_message, context)
    VALUES (SQLERRM, 'f_manage_sdasin_insert_updates: Trigger execution error');
    RAISE;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.get_veeqo_id_on_update()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
  webhook_prefix TEXT := (SELECT value FROM t_config WHERE key = 'current_webhook_4_character_prefix');
  webhook_url TEXT := 'https://' || webhook_prefix || '-24-124-112-70.ngrok-free.app/veeqo-update';
  payload TEXT;
  v_response http_response;
  sku_prefix TEXT;
BEGIN
  -- Only proceed if veeqo_id is currently null.
  IF NEW.veeqo_id IS NOT NULL THEN
    RETURN NEW;
  END IF;
  
  -- Determine SKU prefix based on the triggering table
  IF TG_TABLE_NAME = 't_sdasins' THEN
    sku_prefix := 'Disc_';
  ELSIF TG_TABLE_NAME = 't_discs' THEN
    sku_prefix := 'D';
  ELSIF TG_TABLE_NAME = 't_order_sheet_lines' THEN
    sku_prefix := 'OS';
  ELSE
    RAISE EXCEPTION 'Unsupported table: %', TG_TABLE_NAME;
  END IF;
  
  -- Build the JSON payload with appropriate SKU format
  payload := json_build_object(
    'sku', sku_prefix || NEW.id,
    'sdasin_id', CASE WHEN TG_TABLE_NAME = 't_sdasins' THEN NEW.id ELSE NULL END
  )::text;
  
  -- Call the webhook endpoint via HTTP POST.
  v_response := http_post(webhook_url, payload, 'application/json');
  
  -- If the webhook returns status 200, parse the response and update veeqo_id.
  IF v_response.status = 200 THEN
    -- Expecting a JSON response like: { "productId": 114797571 }
    NEW.veeqo_id := (v_response.content::json->>'productId')::bigint;
    NEW.veeqo_id_notes := NULL;
  ELSE
    NEW.veeqo_id_notes := 'Webhook error (status ' || v_response.status || '): ' || v_response.content;
  END IF;
  
  RETURN NEW;
EXCEPTION WHEN OTHERS THEN
  NEW.veeqo_id_notes := 'Exception: ' || SQLERRM;
  RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.reconcile_inv_sdasin_quantities()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
declare
    log_record record;
begin
    -- Step 1: Ensure every `t_sdasin` has a corresponding `t_inv_sdasin`
    insert into t_inv_sdasin (id, available_quantity, created_by)
    select s.id, 0, 'reconciliation'
    from t_sdasins s
    where not exists (
        select 1 from t_inv_sdasin i where i.id = s.id
    );

    -- Step 2: Zero out `t_inv_sdasin` records without matching `t_sdasin`
    for log_record in
        update t_inv_sdasin
        set available_quantity = 0
        where not exists (
            select 1 from t_sdasins s where s.id = t_inv_sdasin.id
        )
        and available_quantity != 0 -- Only log if there’s a change
        returning id as sku, available_quantity as count_old, 0 as count_new
    loop
        insert into t_inv_reconcile_log (class, sku, count_old, count_new, created_by)
        values ('sdasin', log_record.sku, log_record.count_old, log_record.count_new, 'reconciliation');
    end loop;

    -- Step 3: Reconcile quantities for matching `t_sdasin` records
    for log_record in
        select
            t_inv_sdasin.id as sku,
            t_inv_sdasin.available_quantity as count_old,
            subquery.correct_quantity as count_new
        from t_inv_sdasin
        join (
            select
                s.id as sdasin_id,
                count(d.id) as correct_quantity
            from t_sdasins s
            left join tjoin_discs_sdasins j on s.id = j.sdasin_id
            left join t_discs d on d.id = j.disc_id and d.sold_date is null
            group by s.id
        ) as subquery
        on t_inv_sdasin.id = subquery.sdasin_id
        where t_inv_sdasin.available_quantity != subquery.correct_quantity -- Only log if there’s a discrepancy
    loop
        -- Log the change before performing the update
        insert into t_inv_reconcile_log (class, sku, count_old, count_new, created_by)
        values ('sdasin', log_record.sku, log_record.count_old, log_record.count_new, 'reconciliation');

        -- Update the quantity
        update t_inv_sdasin
        set available_quantity = log_record.count_new
        where id = log_record.sku;
    end loop;

end;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.fn_verify_image()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    prefix TEXT;
    webhook_domain TEXT;
    webhook_url TEXT;
    json_payload TEXT;  -- We'll store the JSON string for the POST body
    config_key TEXT := 'current_webhook_4_character_prefix';

    -- We'll also build the public image URL for verification
    config_folder_key TEXT;
    base_url TEXT;
    full_url TEXT;
BEGIN
    -- 1. Grab the dynamic 4-char prefix (e.g. '92bd')
    SELECT value INTO prefix
      FROM t_config
     WHERE key = config_key;

    IF prefix IS NULL THEN
       RAISE EXCEPTION 'No value found in t_config for key=%', config_key;
    END IF;

    -- 2. Build the final webhook URL, e.g. https://92bd-24-124-112-70.ngrok-free.app/verify-image
    webhook_domain := prefix || '-24-124-112-70.ngrok-free.app';
    webhook_url := 'https://' || webhook_domain || '/verify-image';

    -- 3. Build the public folder key: 'public_folder_url_for_' || table_name
    config_folder_key := 'public_folder_url_for_' || NEW.table_name;

    -- 4. Retrieve the base public URL (e.g. https://your-supabase-bucket/public/molds)
    SELECT value INTO base_url
      FROM t_config
     WHERE key = config_folder_key;

    IF base_url IS NULL THEN
        RAISE EXCEPTION 'No value found in t_config for key=%', config_folder_key;
    END IF;

    -- 5. Construct the actual image URL
    full_url := base_url || '/' || NEW.image_file_name;

    -- 6. Prepare JSON payload
    json_payload := json_build_object('id', NEW.id, 'url', full_url)::TEXT;

    -- 7. Make the POST request
    PERFORM content
      FROM http_post(
        webhook_url,
        json_payload,
        'application/json'
      );

    -- 8. Provide placeholders
    NEW.image_verified_at := NOW();
    NEW.image_verified_notes := 'Verification in progress via fn_verify_image';

    RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.calculate_a_disc_carrying_cost(disc_id integer)
 RETURNS numeric
 LANGUAGE plpgsql
AS $function$
DECLARE
    order_cost NUMERIC;
    shipping_multiplier NUMERIC;
    carrying_cost NUMERIC;
BEGIN
    -- Fetch the order cost from t_mps or fallback to t_plastics
    SELECT COALESCE(m.val_override_order_cost, p.val_order_cost)
    INTO order_cost
    FROM public.t_discs d
    JOIN public.t_mps m ON d.mps_id = m.id
    JOIN public.t_plastics p ON m.plastic_id = p.id
    WHERE d.id = disc_id;

    -- If order_cost is NULL, log the error and return NULL
    IF order_cost IS NULL THEN
        INSERT INTO public.t_error_logs (error_message, context)
        VALUES (
            'Order cost is missing for disc.',
            jsonb_build_object('disc_id', disc_id, 'mps_id', (SELECT mps_id FROM public.t_discs WHERE id = disc_id))
        );
        RETURN NULL;
    END IF;

    -- Fetch the shipping multiplier from the invoice
    SELECT i.total_amount / NULLIF(i.subtotal, 0)
    INTO shipping_multiplier
    FROM public.t_discs d
    JOIN public.t_shipments s ON d.shipment_id = s.id
    JOIN public.t_invoices i ON s.invoice_id = i.id
    WHERE d.id = disc_id;

    -- If shipping_multiplier is NULL, log the error and return NULL
    IF shipping_multiplier IS NULL THEN
        INSERT INTO public.t_error_logs (error_message, context)
        VALUES (
            'Shipping multiplier is missing for disc.',
            jsonb_build_object('disc_id', disc_id, 'shipment_id', (SELECT shipment_id FROM public.t_discs WHERE id = disc_id))
        );
        RETURN NULL;
    END IF;

    -- Calculate the carrying cost
    carrying_cost := order_cost * shipping_multiplier;

    RETURN carrying_cost;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.handle_disc_carrying_cost()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- On INSERT, set carrying cost using the calculate_a_disc_carrying_cost function
    IF TG_OP = 'INSERT' THEN
        NEW.carrying_cost := calculate_a_disc_carrying_cost(NEW.id);
    END IF;

    -- On UPDATE of mps_id or shipment_id, recalculate the carrying cost
    IF TG_OP = 'UPDATE' AND 
       (OLD.mps_id IS DISTINCT FROM NEW.mps_id OR OLD.shipment_id IS DISTINCT FROM NEW.shipment_id) THEN
        NEW.carrying_cost := calculate_a_disc_carrying_cost(NEW.id);
    END IF;

    RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_ts_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_ts_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbtreekey4_in(cstring)
 RETURNS gbtreekey4
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbtreekey_in$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbtreekey4_out(gbtreekey4)
 RETURNS cstring
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbtreekey_out$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbtreekey8_in(cstring)
 RETURNS gbtreekey8
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbtreekey_in$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbtreekey8_out(gbtreekey8)
 RETURNS cstring
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbtreekey_out$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbtreekey16_in(cstring)
 RETURNS gbtreekey16
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbtreekey_in$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbtreekey16_out(gbtreekey16)
 RETURNS cstring
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbtreekey_out$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbtreekey32_in(cstring)
 RETURNS gbtreekey32
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbtreekey_in$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbtreekey32_out(gbtreekey32)
 RETURNS cstring
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbtreekey_out$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbtreekey_var_in(cstring)
 RETURNS gbtreekey_var
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbtreekey_in$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbtreekey_var_out(gbtreekey_var)
 RETURNS cstring
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbtreekey_out$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.cash_dist(money, money)
 RETURNS money
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$cash_dist$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.date_dist(date, date)
 RETURNS integer
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$date_dist$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.float4_dist(real, real)
 RETURNS real
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$float4_dist$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.float8_dist(double precision, double precision)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$float8_dist$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.int2_dist(smallint, smallint)
 RETURNS smallint
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$int2_dist$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.int4_dist(integer, integer)
 RETURNS integer
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$int4_dist$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.int8_dist(bigint, bigint)
 RETURNS bigint
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$int8_dist$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.interval_dist(interval, interval)
 RETURNS interval
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$interval_dist$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.oid_dist(oid, oid)
 RETURNS oid
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$oid_dist$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.time_dist(time without time zone, time without time zone)
 RETURNS interval
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$time_dist$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.ts_dist(timestamp without time zone, timestamp without time zone)
 RETURNS interval
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$ts_dist$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.tstz_dist(timestamp with time zone, timestamp with time zone)
 RETURNS interval
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$tstz_dist$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_oid_consistent(internal, oid, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_oid_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_oid_distance(internal, oid, smallint, oid, internal)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_oid_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_oid_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_oid_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_oid_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_oid_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_decompress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_decompress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_var_decompress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_var_decompress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_var_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_var_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_oid_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_oid_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_oid_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_oid_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_oid_union(internal, internal)
 RETURNS gbtreekey8
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_oid_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_oid_same(gbtreekey8, gbtreekey8, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_oid_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int2_consistent(internal, smallint, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int2_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int2_distance(internal, smallint, smallint, oid, internal)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int2_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int2_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int2_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int2_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int2_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int2_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int2_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int2_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int2_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int2_union(internal, internal)
 RETURNS gbtreekey4
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int2_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int2_same(gbtreekey4, gbtreekey4, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int2_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int4_consistent(internal, integer, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int4_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int4_distance(internal, integer, smallint, oid, internal)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int4_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int4_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int4_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int4_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int4_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int4_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int4_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int4_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int4_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int4_union(internal, internal)
 RETURNS gbtreekey8
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int4_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int4_same(gbtreekey8, gbtreekey8, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int4_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int8_consistent(internal, bigint, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int8_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int8_distance(internal, bigint, smallint, oid, internal)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int8_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int8_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int8_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int8_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int8_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int8_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int8_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int8_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int8_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int8_union(internal, internal)
 RETURNS gbtreekey16
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int8_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_int8_same(gbtreekey16, gbtreekey16, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_int8_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float4_consistent(internal, real, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float4_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float4_distance(internal, real, smallint, oid, internal)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float4_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float4_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float4_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float4_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float4_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float4_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float4_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float4_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float4_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float4_union(internal, internal)
 RETURNS gbtreekey8
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float4_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float4_same(gbtreekey8, gbtreekey8, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float4_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float8_consistent(internal, double precision, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float8_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float8_distance(internal, double precision, smallint, oid, internal)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float8_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float8_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float8_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float8_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float8_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float8_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float8_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float8_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float8_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float8_union(internal, internal)
 RETURNS gbtreekey16
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float8_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_float8_same(gbtreekey16, gbtreekey16, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_float8_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_ts_consistent(internal, timestamp without time zone, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_ts_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_ts_distance(internal, timestamp without time zone, smallint, oid, internal)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_ts_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_tstz_consistent(internal, timestamp with time zone, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_tstz_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_tstz_distance(internal, timestamp with time zone, smallint, oid, internal)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_tstz_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_tstz_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_tstz_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_ts_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_ts_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_ts_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_ts_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_ts_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_ts_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_ts_union(internal, internal)
 RETURNS gbtreekey16
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_ts_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_ts_same(gbtreekey16, gbtreekey16, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_ts_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_time_consistent(internal, time without time zone, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_time_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_time_distance(internal, time without time zone, smallint, oid, internal)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_time_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_timetz_consistent(internal, time with time zone, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_timetz_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_time_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_time_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_timetz_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_timetz_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_time_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_time_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_time_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_time_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_time_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_time_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_time_union(internal, internal)
 RETURNS gbtreekey16
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_time_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_time_same(gbtreekey16, gbtreekey16, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_time_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_date_consistent(internal, date, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_date_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_date_distance(internal, date, smallint, oid, internal)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_date_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_date_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_date_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_date_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_date_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_date_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_date_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_date_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_date_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_date_union(internal, internal)
 RETURNS gbtreekey8
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_date_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_date_same(gbtreekey8, gbtreekey8, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_date_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_intv_consistent(internal, interval, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_intv_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_intv_distance(internal, interval, smallint, oid, internal)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_intv_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_intv_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_intv_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_intv_decompress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_intv_decompress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_intv_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_intv_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_intv_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_intv_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_intv_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_intv_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_intv_union(internal, internal)
 RETURNS gbtreekey32
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_intv_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_intv_same(gbtreekey32, gbtreekey32, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_intv_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_cash_consistent(internal, money, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_cash_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_cash_distance(internal, money, smallint, oid, internal)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_cash_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_cash_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_cash_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_cash_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_cash_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_cash_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_cash_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_cash_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_cash_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_cash_union(internal, internal)
 RETURNS gbtreekey16
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_cash_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_cash_same(gbtreekey16, gbtreekey16, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_cash_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_macad_consistent(internal, macaddr, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_macad_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_macad_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_macad_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_macad_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_macad_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_macad_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_macad_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_macad_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_macad_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_macad_union(internal, internal)
 RETURNS gbtreekey16
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_macad_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_macad_same(gbtreekey16, gbtreekey16, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_macad_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_text_consistent(internal, text, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_text_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bpchar_consistent(internal, character, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_bpchar_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_text_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_text_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bpchar_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_bpchar_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_text_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_text_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_text_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_text_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_text_union(internal, internal)
 RETURNS gbtreekey_var
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_text_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_text_same(gbtreekey_var, gbtreekey_var, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_text_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bytea_consistent(internal, bytea, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_bytea_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bytea_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_bytea_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bytea_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_bytea_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bytea_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_bytea_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bytea_union(internal, internal)
 RETURNS gbtreekey_var
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_bytea_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bytea_same(gbtreekey_var, gbtreekey_var, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_bytea_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_numeric_consistent(internal, numeric, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_numeric_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_numeric_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_numeric_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_numeric_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_numeric_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_numeric_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_numeric_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_numeric_union(internal, internal)
 RETURNS gbtreekey_var
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_numeric_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_numeric_same(gbtreekey_var, gbtreekey_var, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_numeric_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bit_consistent(internal, bit, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_bit_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bit_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_bit_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bit_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_bit_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bit_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_bit_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bit_union(internal, internal)
 RETURNS gbtreekey_var
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_bit_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bit_same(gbtreekey_var, gbtreekey_var, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_bit_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_inet_consistent(internal, inet, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_inet_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_inet_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_inet_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_inet_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_inet_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_inet_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_inet_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_inet_union(internal, internal)
 RETURNS gbtreekey16
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_inet_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_inet_same(gbtreekey16, gbtreekey16, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_inet_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_uuid_consistent(internal, uuid, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_uuid_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_uuid_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_uuid_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_uuid_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_uuid_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_uuid_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_uuid_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_uuid_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_uuid_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_uuid_union(internal, internal)
 RETURNS gbtreekey32
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_uuid_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_uuid_same(gbtreekey32, gbtreekey32, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_uuid_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_macad8_consistent(internal, macaddr8, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_macad8_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_macad8_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_macad8_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_macad8_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_macad8_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_macad8_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_macad8_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_macad8_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_macad8_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_macad8_union(internal, internal)
 RETURNS gbtreekey16
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_macad8_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_macad8_same(gbtreekey16, gbtreekey16, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_macad8_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_enum_consistent(internal, anyenum, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_enum_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_enum_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_enum_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_enum_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_enum_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_enum_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_enum_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_enum_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_enum_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_enum_union(internal, internal)
 RETURNS gbtreekey8
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_enum_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_enum_same(gbtreekey8, gbtreekey8, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbt_enum_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbtreekey2_in(cstring)
 RETURNS gbtreekey2
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbtreekey_in$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbtreekey2_out(gbtreekey2)
 RETURNS cstring
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/btree_gist', $function$gbtreekey_out$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bool_consistent(internal, boolean, smallint, oid, internal)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE STRICT
AS '$libdir/btree_gist', $function$gbt_bool_consistent$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bool_compress(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE STRICT
AS '$libdir/btree_gist', $function$gbt_bool_compress$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bool_fetch(internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE STRICT
AS '$libdir/btree_gist', $function$gbt_bool_fetch$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bool_penalty(internal, internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE STRICT
AS '$libdir/btree_gist', $function$gbt_bool_penalty$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bool_picksplit(internal, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE STRICT
AS '$libdir/btree_gist', $function$gbt_bool_picksplit$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bool_union(internal, internal)
 RETURNS gbtreekey2
 LANGUAGE c
 IMMUTABLE STRICT
AS '$libdir/btree_gist', $function$gbt_bool_union$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.gbt_bool_same(gbtreekey2, gbtreekey2, internal)
 RETURNS internal
 LANGUAGE c
 IMMUTABLE STRICT
AS '$libdir/btree_gist', $function$gbt_bool_same$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.f_apply_matching_to_all_sdasins()
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
DECLARE
  processed_count INTEGER := 0;
  sdasin_rec RECORD;
  disc_rec RECORD;
  computed_reason TEXT;
BEGIN
  -- Loop over up to 5 SDASIN records that:
  -- 1. Have the necessary matching fields.
  -- 2. Have not been checked in the last 24 hours.
  FOR sdasin_rec IN
    SELECT *
    FROM t_sdasins
    WHERE min_weight IS NOT NULL
      AND max_weight IS NOT NULL
      AND (mps_id IS NOT NULL OR mps_id2 IS NOT NULL)
      AND color_id IS NOT NULL
      AND (looked_for_matching_discs_at IS NULL OR looked_for_matching_discs_at < NOW() - INTERVAL '24 hours')
    ORDER BY looked_for_matching_discs_at ASC NULLS FIRST
    LIMIT 5
  LOOP
    processed_count := processed_count + 1;

    -- Delete existing join records for this SDASIN.
    DELETE FROM tjoin_discs_sdasins
     WHERE sdasin_id = sdasin_rec.id;

    -- Find matching discs using the same logic as in your trigger.
    FOR disc_rec IN
      SELECT
        d.id AS disc_id,
        CASE 
          WHEN (d.mps_id = sdasin_rec.mps_id OR d.mps_id = sdasin_rec.mps_id2) THEN
            CASE 
              WHEN sdasin_rec.color_id = 23 THEN 'mps exact, good weight, color varies'
              WHEN sdasin_rec.color_id IS NOT NULL THEN 'mps exact, good weight, color exact'
              ELSE 'mps exact, good weight, color unknown'
            END
          ELSE
            CASE 
              WHEN sdasin_rec.color_id = 23 THEN 'mps stock equivalent, good weight, color varies'
              WHEN sdasin_rec.color_id IS NOT NULL THEN 'mps stock equivalent, good weight, color exact'
              ELSE 'mps stock equivalent, good weight, color unknown'
            END
        END AS reason
      FROM t_discs d
        -- Join for the disc's mps and its stamp.
        LEFT JOIN t_mps dm ON d.mps_id = dm.id
        LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id
        -- For stock equivalent matching, join the two possible SDASIN mps records and stamps.
        LEFT JOIN t_mps sm1 ON sdasin_rec.mps_id = sm1.id
        LEFT JOIN t_stamps sst1 ON sm1.stamp_id = sst1.id
        LEFT JOIN t_mps sm2 ON sdasin_rec.mps_id2 = sm2.id
        LEFT JOIN t_stamps sst2 ON sm2.stamp_id = sst2.id
      WHERE
        -- Consider discs that are unsold or sold within the last 14 days.
        (d.sold_date IS NULL OR d.sold_date > NOW() - INTERVAL '14 days')
        -- Weight criteria.
        AND d.weight >= sdasin_rec.min_weight
        AND d.weight <= sdasin_rec.max_weight
        -- Color criteria: if SDASIN color is 23, any disc color qualifies; otherwise, the disc’s color must match.
        AND (sdasin_rec.color_id = 23 OR d.color_id = sdasin_rec.color_id)
        -- MPS matching rules:
        AND (
             -- Exact match on mps.
             (d.mps_id = sdasin_rec.mps_id OR d.mps_id = sdasin_rec.mps_id2)
             OR
             -- Stock equivalent match.
             (
               (
                 ((sst1.is_sdasin_stock = TRUE OR sst1.id = 38) AND d.mps_id = sdasin_rec.mps_id)
                 OR
                 ((sst2.is_sdasin_stock = TRUE OR sst2.id = 38) AND d.mps_id = sdasin_rec.mps_id2)
               )
               AND dst.is_vendor_stock = TRUE
               AND (
                    (d.mps_id = sdasin_rec.mps_id AND dm.plastic_id = sm1.plastic_id AND dm.mold_id = sm1.mold_id)
                    OR
                    (d.mps_id = sdasin_rec.mps_id2 AND dm.plastic_id = sm2.plastic_id AND dm.mold_id = sm2.mold_id)
               )
             )
        )
    LOOP
      computed_reason := disc_rec.reason;
      INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
      VALUES (disc_rec.disc_id, sdasin_rec.id, computed_reason, 'system')
      ON CONFLICT (disc_id, sdasin_id) DO NOTHING;
    END LOOP;

    -- Update the timestamp to indicate matching was performed.
    UPDATE t_sdasins
      SET looked_for_matching_discs_at = NOW()
    WHERE id = sdasin_rec.id;
    
  END LOOP;
  
  -- If no records were processed, return NULL.
  IF processed_count = 0 THEN
    RETURN NULL;
  ELSE
    RETURN processed_count;
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    INSERT INTO t_error_logs (error_message, context)
    VALUES (SQLERRM, 'f_apply_matching_to_all_sdasins execution error');
    RAISE;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.import_discs_from_informed(file_url text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    file_content TEXT;
    temp_table TEXT := 'temp_informed_discs_import';
BEGIN
    -- Download file content from the Google Drive link
    SELECT content
    INTO file_content
    FROM http_get(file_url);

    -- Check if the file was downloaded successfully
    IF file_content IS NULL THEN
        RAISE EXCEPTION 'Failed to fetch file from Google Drive.';
    END IF;

    -- Create a temporary table for importing the CSV data
    EXECUTE format('
        CREATE TEMP TABLE %I (
            shipment_id TEXT,
            pms_id INTEGER,
            weight NUMERIC,
            color_modifier TEXT,
            color_id INTEGER,
            description TEXT,
            location TEXT,
            notes TEXT
        )
    ', temp_table);

    -- Copy the CSV data into the temporary table
    EXECUTE format('
        COPY %I (shipment_id, pms_id, weight, color_modifier, color_id, description, location, notes)
        FROM STDIN WITH (FORMAT csv, HEADER true)
    ', temp_table) USING file_content;

    -- Insert data into the main t_discs table
    INSERT INTO t_discs (shipment_id, pms_id, weight, color_modifier, color_id, description, location, notes)
    SELECT shipment_id, pms_id, weight, color_modifier, color_id, description, location, notes
    FROM temp_informed_discs_import;

    -- Clean up: Drop the temporary table
    EXECUTE format('DROP TABLE %I', temp_table);

    RETURN 'Discs imported successfully.';
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.import_discs_to_t_infor_all_fields(file_url text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    file_content TEXT;
BEGIN
    -- Download file content from the Google Drive link
    SELECT content
    INTO file_content
    FROM http_get(file_url);

    -- Check if the file was downloaded successfully
    IF file_content IS NULL THEN
        RAISE EXCEPTION 'Failed to fetch file from Google Drive.';
    END IF;

    -- Clear the table before importing new data
    TRUNCATE TABLE t_infor_all_fields;

    -- Use the file_content to insert data into the t_infor_all_fields table
    -- Use pg_temp as a way to handle CSV content processing
    EXECUTE format('
        COPY t_infor_all_fields (shipment_id, pms_id, weight, color_modifier, color_id, description, location, notes)
        FROM PROGRAM ''echo %L'' WITH (FORMAT csv, HEADER true)
    ', file_content);

    RETURN 'Data imported successfully into t_infor_all_fields.';
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.fn_try_publish_collection_brand()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
  -- Get the current webhook prefix from t_config.
  webhook_prefix TEXT := (SELECT value FROM t_config WHERE key = 'current_webhook_4_character_prefix');
  -- Build the webhook URL using the prefix. This URL calls your publishCollectionBrand endpoint.
  webhook_url TEXT := 'https://' || webhook_prefix || '-24-124-112-70.ngrok-free.app/publishCollectionBrand';
  payload TEXT;
  v_response http_response;
  sku_prefix TEXT;  -- (Optional: declare if you need to use it for additional logic)
BEGIN
  -- (The trigger's WHEN clause already ensures that NEW.shopify is true and NEW.shopify_collection_created_at is null.)
  --
  -- Build a JSON payload with the key fields from the updated row.
  payload := json_build_object(
    'id', NEW.id,
    'brand', NEW.brand,
    'shopify_tag', NEW.shopify_tag
  )::text;

  -- Send the HTTP POST request to the webhook endpoint.
  v_response := http_post(webhook_url, payload, 'application/json');

  IF v_response.status_code <> 200 THEN
    RAISE NOTICE 'Webhook call failed for brand % with status code %', NEW.brand, v_response.status_code;
  ELSE
    RAISE NOTICE 'Webhook successfully sent for brand %', NEW.brand;
  END IF;

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in fn_try_publish_collection_brand: %', SQLERRM;
    RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.fbm_autopicker(input_sdasin_id integer)
 RETURNS TABLE(id integer, mps_id integer, color_id integer, weight real, grade integer, location text, weight_points integer, color_points integer, age_points integer, rank_points integer, pull text)
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN QUERY
    WITH base_data AS (
        SELECT
            d.id AS id, -- Matches the "id" column in v_discs
            d.mps_id::integer AS mps_id, -- Cast to integer
            d.color_id::integer AS color_id, -- Cast to integer
            d.weight,
            d.grade::integer AS grade, -- Cast to integer
            d.location,
            COUNT(*) OVER (PARTITION BY d.weight) AS weight_match_count_bigint, -- Raw COUNT result
            COUNT(*) OVER (PARTITION BY d.color_id) AS color_match_count_bigint, -- Raw COUNT result
            EXTRACT(MONTH FROM AGE(NOW(), d.created_at))::integer AS age_points, -- Cast to integer
            (
                CASE 
                    WHEN d.location = 'Back Stock' THEN 3
                    WHEN d.location LIKE 'B2F%' THEN 1
                    ELSE 0
                END
            ) AS location_points,
            v.pull AS pull -- Include the pull field from v_discs
        FROM
            t_discs d
        INNER JOIN
            v_discs v ON d.id = v.id -- Join with v_discs view using the new "id" column
        INNER JOIN
            tjoin_discs_sdasins tjoin
            ON d.id = tjoin.disc_id
        INNER JOIN
            t_sdasins s
            ON tjoin.sdasin_id = s.id
        WHERE
            d.sold_date IS NULL -- Only consider available discs
            AND s.id = input_sdasin_id -- Explicit reference to the function parameter
    ),
    ranked_matches AS (
        SELECT
            base_data.id,
            base_data.mps_id,
            base_data.color_id,
            base_data.weight,
            base_data.grade,
            base_data.location,
            base_data.weight_match_count_bigint::integer AS weight_match_count, -- Cast COUNT to integer
            base_data.color_match_count_bigint::integer AS color_match_count, -- Cast COUNT to integer
            base_data.age_points,
            (
                base_data.location_points + 
                base_data.weight_match_count_bigint::integer + 
                base_data.color_match_count_bigint::integer + 
                base_data.age_points
            )::integer AS rank_points, -- Cast rank_points to integer
            base_data.pull
        FROM
            base_data
    )
    SELECT
        ranked_matches.id,
        ranked_matches.mps_id,
        ranked_matches.color_id,
        ranked_matches.weight,
        ranked_matches.grade,
        ranked_matches.location,
        ranked_matches.weight_match_count AS weight_points,
        ranked_matches.color_match_count AS color_points,
        ranked_matches.age_points,
        ranked_matches.rank_points,
        ranked_matches.pull
    FROM
        ranked_matches
    ORDER BY
        ranked_matches.rank_points DESC;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.safe_update_image_verification(p_id integer, p_image_verified boolean, p_image_verified_at timestamp with time zone, p_image_verified_notes text)
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$
DECLARE
    updated_count INTEGER;
BEGIN
    RAISE NOTICE 'Starting update for ID: %', p_id;

    UPDATE t_images
    SET 
        image_verified = p_image_verified,
        image_verified_at = p_image_verified_at,
        image_verified_notes = p_image_verified_notes
    WHERE id = p_id
    AND id IN (
        SELECT id FROM t_images WHERE id = p_id FOR UPDATE SKIP LOCKED
    )
    RETURNING id INTO updated_count;

    -- Debug output for PostgreSQL logs
    IF updated_count IS NOT NULL THEN
        RAISE NOTICE 'Update successful for ID: %', p_id;
        RETURN TRUE;
    ELSE
        RAISE NOTICE 'No update performed for ID: % (possibly locked or missing)', p_id;
        RETURN FALSE;
    END IF;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.f_truncate_and_fill_tu_informed()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$BEGIN
    -- Step 1: Truncate the table to remove old data
    TRUNCATE tu_informed RESTART IDENTITY;

   
    -- Step 2: Insert fresh data from the view, handling NULL values in map_price
    INSERT INTO tu_informed
    SELECT 
        sku,
        marketplace_id,
        cost,
        currency,
        min_price,
        max_price,
        COALESCE(map_price, 0) AS map_price,  -- Replace NULL with 0
        listing_type,
        strategy_id,
        managed,
        dateadded
    FROM v_informed_upload;
END;$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.f_export_tu_informed_to_csv()
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    csv_content TEXT;
    header TEXT := 'sku,marketplace_id,cost,currency,min_price,max_price,map_price,listing_type,strategy_id,managed,dateadded';
BEGIN
    -- Generate CSV content with column headers
    csv_content := (
        SELECT header || E'\n' || string_agg(
            format('%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s',
                sku, marketplace_id, cost, currency, min_price, max_price, map_price,
                listing_type, strategy_id, managed, dateadded
            ),
            E'\n'
        )
        FROM tu_informed
    );

    -- Check if CSV content is empty
    IF csv_content IS NULL OR csv_content = '' THEN
        RAISE EXCEPTION 'No data found in tu_informed';
    END IF;

    -- Return the CSV content
    RETURN csv_content;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.execute_sql(query text)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    EXECUTE query;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.disable_user_triggers(table_name text)
 RETURNS text
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  EXECUTE format('ALTER TABLE %I DISABLE TRIGGER USER', table_name);
  RETURN 'ok';
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.enable_user_triggers(table_name text)
 RETURNS text
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  EXECUTE format('ALTER TABLE %I ENABLE TRIGGER USER', table_name);
  RETURN 'ok';
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.f_match_discs_to_asins()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Truncate all rows from tjoin_discs_sdasins before starting matching
    TRUNCATE TABLE tjoin_discs_sdasins;

    -- Matching Scheme 1: Exact Stamps
    INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, matching_rule_id, created_at)
    SELECT
        d.id AS disc_id,
        s.id AS sdasin_id,
        1 AS matching_rule_id, -- Rule ID for exact stamps
        NOW() AS created_at
    FROM
        t_discs d
    INNER JOIN
        t_sdasins s
    ON
        d.mps_id = s.mps_id
        AND (
            s.color_id = 23 OR d.color_id = s.color_id
        )
    WHERE
        (d.sold_date IS NULL OR d.sold_date > (NOW() - INTERVAL '14 days'))
        AND (d.location IS NULL OR d.location NOT IN ('Local Love', 'Auction'))
        AND d.weight >= s.min_weight
        AND d.weight <= s.max_weight
        AND NOT EXISTS (
            SELECT 1
            FROM tjoin_discs_sdasins tjoin
            WHERE tjoin.disc_id = d.id
              AND tjoin.sdasin_id = s.id
        );

    -- Matching Scheme 2: Stock Stamp Equivalents
    INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, matching_rule_id, created_at)
    SELECT
        d.id AS disc_id,
        s.id AS sdasin_id,
        2 AS matching_rule_id, -- Rule ID for stock stamp equivalents
        NOW() AS created_at
    FROM
        t_discs d
    INNER JOIN
        t_sdasins s
    ON
        (SELECT m.mold_id
         FROM t_mps m
         WHERE m.id = d.mps_id
        ) = (SELECT m.mold_id
             FROM t_mps m
             WHERE m.id = s.mps_id
        )
        AND (SELECT m.plastic_id
             FROM t_mps m
             WHERE m.id = d.mps_id
        ) = (SELECT m.plastic_id
             FROM t_mps m
             WHERE m.id = s.mps_id
        )
        AND (
            s.color_id = 23 OR d.color_id = s.color_id
        )
        AND (
            (SELECT m.stamp_id
             FROM t_mps m
             WHERE m.id = d.mps_id
            ) = 38 OR (SELECT st.is_vendor_stock
                       FROM t_stamps st
                       WHERE st.id = (SELECT m.stamp_id FROM t_mps m WHERE m.id = d.mps_id)
            ) = TRUE
        )
        AND (
            (SELECT m.stamp_id
             FROM t_mps m
             WHERE m.id = s.mps_id
            ) = 38 OR (SELECT st.is_sdasin_stock
                       FROM t_stamps st
                       WHERE st.id = (SELECT m.stamp_id FROM t_mps m WHERE m.id = s.mps_id)
            ) = TRUE
        )
    WHERE
        (d.sold_date IS NULL OR d.sold_date > (NOW() - INTERVAL '14 days'))
        AND (d.location IS NULL OR d.location NOT IN ('Local Love', 'Auction'))
        AND d.weight >= s.min_weight
        AND d.weight <= s.max_weight
        AND NOT EXISTS (
            SELECT 1
            FROM tjoin_discs_sdasins tjoin
            WHERE tjoin.disc_id = d.id
              AND tjoin.sdasin_id = s.id
        );
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.fn_sdasin_matching_logic(p_sdasin t_sdasins)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
  rec RECORD;
  computed_reason text;
BEGIN
  -- Step 1: Delete existing join records for this SDASIN.
  DELETE FROM tjoin_discs_sdasins
   WHERE sdasin_id = p_sdasin.id;

  -- Step 2: Loop over candidate discs that are unsold or sold within the last 14 days.
  FOR rec IN
    SELECT
      d.id AS disc_id,
      -- Compute the reason using a nested CASE expression.
      CASE 
        WHEN (d.mps_id = p_sdasin.mps_id OR d.mps_id = p_sdasin.mps_id2) THEN
          CASE 
            WHEN p_sdasin.color_id = 23 THEN 'mps exact, good weight, color varies'
            WHEN p_sdasin.color_id IS NOT NULL THEN 'mps exact, good weight, color exact'
            ELSE 'mps exact, good weight, color unknown'
          END
        ELSE
          CASE 
            WHEN p_sdasin.color_id = 23 THEN 'mps stock equivalent, good weight, color varies'
            WHEN p_sdasin.color_id IS NOT NULL THEN 'mps stock equivalent, good weight, color exact'
            ELSE 'mps stock equivalent, good weight, color unknown'
          END
      END AS reason
    FROM t_discs d
      -- Join for the disc's mps and its stamp.
      LEFT JOIN t_mps    dm  ON d.mps_id = dm.id
      LEFT JOIN t_stamps dst ON dm.stamp_id = dst.id

      -- For stock-equivalent matching, join the two possible SDASIN mps records and stamps.
      LEFT JOIN t_mps    sm1  ON p_sdasin.mps_id  = sm1.id
      LEFT JOIN t_stamps sst1 ON sm1.stamp_id     = sst1.id
      LEFT JOIN t_mps    sm2  ON p_sdasin.mps_id2 = sm2.id
      LEFT JOIN t_stamps sst2 ON sm2.stamp_id     = sst2.id

    WHERE
      -- Only consider discs that are unsold or sold within the last 14 days.
      (d.sold_date IS NULL OR d.sold_date > now() - interval '14 days')

      -- Weight criteria (inclusive).
      AND d.weight >= p_sdasin.min_weight
      AND d.weight <= p_sdasin.max_weight

      -- Color criteria: if SDASIN color is 23, any disc color qualifies; otherwise, colors must match.
      AND (p_sdasin.color_id = 23 OR d.color_id = p_sdasin.color_id)

      -- MPS matching:
      AND (
           -- Exact match on mps.
           (d.mps_id = p_sdasin.mps_id OR d.mps_id = p_sdasin.mps_id2)
           OR
           -- Stock equivalent match:
           (
             (
               ((sst1.is_sdasin_stock = TRUE OR sst1.id = 38) AND d.mps_id = p_sdasin.mps_id)
               OR
               ((sst2.is_sdasin_stock = TRUE OR sst2.id = 38) AND d.mps_id = p_sdasin.mps_id2)
             )
             AND dst.is_vendor_stock = TRUE
             AND (
                  (d.mps_id = p_sdasin.mps_id
                   AND dm.plastic_id = sm1.plastic_id
                   AND dm.mold_id   = sm1.mold_id)
                  OR
                  (d.mps_id = p_sdasin.mps_id2
                   AND dm.plastic_id = sm2.plastic_id
                   AND dm.mold_id   = sm2.mold_id)
             )
           )
      )
  LOOP
    -- Explicitly assign the computed reason.
    computed_reason := rec.reason;

    INSERT INTO tjoin_discs_sdasins (disc_id, sdasin_id, reason, created_by)
    VALUES (rec.disc_id, p_sdasin.id, computed_reason, 'system')
    ON CONFLICT (disc_id, sdasin_id) DO NOTHING;
  END LOOP;

  -- Step 3: Update the timestamp to indicate matching was performed.
  UPDATE t_sdasins
    SET looked_for_matching_discs_at = now()
    WHERE id = p_sdasin.id;

EXCEPTION
  WHEN OTHERS THEN
    INSERT INTO t_error_logs (error_message, context)
    VALUES (SQLERRM, 'fn_sdasin_matching_logic: execution error');
    RAISE;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.fn_try_publish_collection_mold()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
  -- Get the current webhook prefix from t_config.
  webhook_prefix TEXT := (SELECT value FROM t_config WHERE key = 'current_webhook_4_character_prefix');
  -- Build the webhook URL using the prefix. This URL calls your publishCollectionMold endpoint.
  webhook_url TEXT := 'https://' || webhook_prefix || '-24-124-112-70.ngrok-free.app/publishCollectionMold';
  payload TEXT;
  v_response http_response;
BEGIN
  -- Build a JSON payload containing only the mold id.
  payload := json_build_object(
    'id', NEW.id
  )::text;

  -- Send the HTTP POST request to the webhook endpoint.
  v_response := http_post(webhook_url, payload, 'application/json');

  IF v_response.status_code <> 200 THEN
    RAISE NOTICE 'Webhook call failed for mold % with status code %', NEW.id, v_response.status_code;
  ELSE
    RAISE NOTICE 'Webhook successfully sent for mold %', NEW.id;
  END IF;

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in fn_try_publish_collection_mold: %', SQLERRM;
    RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.fn_try_publish_collection_mps()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
  -- Get the current webhook prefix from t_config.
  webhook_prefix TEXT := (SELECT value FROM t_config WHERE key = 'current_webhook_4_character_prefix');
  -- Build the webhook URL using the prefix. This URL calls your publishCollectionMPS endpoint.
  webhook_url TEXT := 'https://' || webhook_prefix || '-24-124-112-70.ngrok-free.app/publishCollectionMPS';
  payload TEXT;
  v_response http_response;
BEGIN
  -- Build a JSON payload containing only the mps id.
  payload := json_build_object(
    'id', NEW.id
  )::text;

  -- Send the HTTP POST request to the webhook endpoint.
  v_response := http_post(webhook_url, payload, 'application/json');

  IF v_response.status_code <> 200 THEN
    RAISE NOTICE 'Webhook call failed for mps % with status code %', NEW.id, v_response.status_code;
  ELSE
    RAISE NOTICE 'Webhook successfully sent for mps %', NEW.id;
  END IF;

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE NOTICE 'Error in fn_try_publish_collection_mps: %', SQLERRM;
    RETURN NEW;
END;
$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_norm(vector)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_norm$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_dims(vector)
 RETURNS integer
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_dims$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.l2_normalize(vector)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$l2_normalize$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.binary_quantize(vector)
 RETURNS bit
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$binary_quantize$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.subvector(vector, integer, integer)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$subvector$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_add(vector, vector)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_add$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_sub(vector, vector)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_sub$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_mul(vector, vector)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_mul$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_concat(vector, vector)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_concat$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_lt(vector, vector)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_lt$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_le(vector, vector)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_le$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_eq(vector, vector)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_eq$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_ne(vector, vector)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_ne$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_ge(vector, vector)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_ge$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_gt(vector, vector)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_gt$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_cmp(vector, vector)
 RETURNS integer
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_cmp$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_l2_squared_distance(vector, vector)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_l2_squared_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_negative_inner_product(vector, vector)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_negative_inner_product$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_spherical_distance(vector, vector)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_spherical_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_accum(double precision[], vector)
 RETURNS double precision[]
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_accum$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_avg(double precision[])
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_avg$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_in(cstring, oid, integer)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_in$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_out(vector)
 RETURNS cstring
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_out$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_typmod_in(cstring[])
 RETURNS integer
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_typmod_in$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_recv(internal, oid, integer)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_recv$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_send(vector)
 RETURNS bytea
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_send$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.l2_distance(vector, vector)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$l2_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.inner_product(vector, vector)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$inner_product$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.cosine_distance(vector, vector)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$cosine_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.l1_distance(vector, vector)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$l1_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_combine(double precision[], double precision[])
 RETURNS double precision[]
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_combine$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector(vector, integer, boolean)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.array_to_vector(integer[], integer, boolean)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$array_to_vector$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.array_to_vector(real[], integer, boolean)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$array_to_vector$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.array_to_vector(double precision[], integer, boolean)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$array_to_vector$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.array_to_vector(numeric[], integer, boolean)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$array_to_vector$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_to_float4(vector, integer, boolean)
 RETURNS real[]
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_to_float4$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.ivfflathandler(internal)
 RETURNS index_am_handler
 LANGUAGE c
AS '$libdir/vector', $function$ivfflathandler$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.hnswhandler(internal)
 RETURNS index_am_handler
 LANGUAGE c
AS '$libdir/vector', $function$hnswhandler$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.ivfflat_halfvec_support(internal)
 RETURNS internal
 LANGUAGE c
AS '$libdir/vector', $function$ivfflat_halfvec_support$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.ivfflat_bit_support(internal)
 RETURNS internal
 LANGUAGE c
AS '$libdir/vector', $function$ivfflat_bit_support$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.hnsw_halfvec_support(internal)
 RETURNS internal
 LANGUAGE c
AS '$libdir/vector', $function$hnsw_halfvec_support$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.hnsw_bit_support(internal)
 RETURNS internal
 LANGUAGE c
AS '$libdir/vector', $function$hnsw_bit_support$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.hnsw_sparsevec_support(internal)
 RETURNS internal
 LANGUAGE c
AS '$libdir/vector', $function$hnsw_sparsevec_support$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_in(cstring, oid, integer)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_in$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_out(halfvec)
 RETURNS cstring
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_out$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_typmod_in(cstring[])
 RETURNS integer
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_typmod_in$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_recv(internal, oid, integer)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_recv$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_send(halfvec)
 RETURNS bytea
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_send$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.l2_distance(halfvec, halfvec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_l2_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.inner_product(halfvec, halfvec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_inner_product$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.cosine_distance(halfvec, halfvec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_cosine_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.l1_distance(halfvec, halfvec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_l1_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_dims(halfvec)
 RETURNS integer
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_vector_dims$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.l2_norm(halfvec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_l2_norm$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.l2_normalize(halfvec)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_l2_normalize$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.binary_quantize(halfvec)
 RETURNS bit
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_binary_quantize$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.subvector(halfvec, integer, integer)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_subvector$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_add(halfvec, halfvec)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_add$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_sub(halfvec, halfvec)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_sub$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_mul(halfvec, halfvec)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_mul$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_concat(halfvec, halfvec)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_concat$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_lt(halfvec, halfvec)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_lt$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_le(halfvec, halfvec)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_le$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_eq(halfvec, halfvec)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_eq$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_ne(halfvec, halfvec)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_ne$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_ge(halfvec, halfvec)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_ge$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_gt(halfvec, halfvec)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_gt$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_cmp(halfvec, halfvec)
 RETURNS integer
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_cmp$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_l2_squared_distance(halfvec, halfvec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_l2_squared_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_negative_inner_product(halfvec, halfvec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_negative_inner_product$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_spherical_distance(halfvec, halfvec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_spherical_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_accum(double precision[], halfvec)
 RETURNS double precision[]
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_accum$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_avg(double precision[])
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_avg$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_combine(double precision[], double precision[])
 RETURNS double precision[]
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_combine$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec(halfvec, integer, boolean)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_to_vector(halfvec, integer, boolean)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_to_vector$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_to_halfvec(vector, integer, boolean)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_to_halfvec$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.array_to_halfvec(integer[], integer, boolean)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$array_to_halfvec$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.array_to_halfvec(real[], integer, boolean)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$array_to_halfvec$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.array_to_halfvec(double precision[], integer, boolean)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$array_to_halfvec$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.array_to_halfvec(numeric[], integer, boolean)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$array_to_halfvec$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_to_float4(halfvec, integer, boolean)
 RETURNS real[]
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_to_float4$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.hamming_distance(bit, bit)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$hamming_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.jaccard_distance(bit, bit)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$jaccard_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_in(cstring, oid, integer)
 RETURNS sparsevec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_in$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_out(sparsevec)
 RETURNS cstring
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_out$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_typmod_in(cstring[])
 RETURNS integer
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_typmod_in$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_recv(internal, oid, integer)
 RETURNS sparsevec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_recv$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_send(sparsevec)
 RETURNS bytea
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_send$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.l2_distance(sparsevec, sparsevec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_l2_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.inner_product(sparsevec, sparsevec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_inner_product$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.cosine_distance(sparsevec, sparsevec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_cosine_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.l1_distance(sparsevec, sparsevec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_l1_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.l2_norm(sparsevec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_l2_norm$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.l2_normalize(sparsevec)
 RETURNS sparsevec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_l2_normalize$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_lt(sparsevec, sparsevec)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_lt$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_le(sparsevec, sparsevec)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_le$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_eq(sparsevec, sparsevec)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_eq$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_ne(sparsevec, sparsevec)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_ne$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_ge(sparsevec, sparsevec)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_ge$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_gt(sparsevec, sparsevec)
 RETURNS boolean
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_gt$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_cmp(sparsevec, sparsevec)
 RETURNS integer
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_cmp$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_l2_squared_distance(sparsevec, sparsevec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_l2_squared_distance$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_negative_inner_product(sparsevec, sparsevec)
 RETURNS double precision
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_negative_inner_product$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec(sparsevec, integer, boolean)
 RETURNS sparsevec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.vector_to_sparsevec(vector, integer, boolean)
 RETURNS sparsevec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$vector_to_sparsevec$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_to_vector(sparsevec, integer, boolean)
 RETURNS vector
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_to_vector$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.halfvec_to_sparsevec(halfvec, integer, boolean)
 RETURNS sparsevec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$halfvec_to_sparsevec$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.sparsevec_to_halfvec(sparsevec, integer, boolean)
 RETURNS halfvec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$sparsevec_to_halfvec$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.array_to_sparsevec(integer[], integer, boolean)
 RETURNS sparsevec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$array_to_sparsevec$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.array_to_sparsevec(real[], integer, boolean)
 RETURNS sparsevec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$array_to_sparsevec$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.array_to_sparsevec(double precision[], integer, boolean)
 RETURNS sparsevec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$array_to_sparsevec$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.array_to_sparsevec(numeric[], integer, boolean)
 RETURNS sparsevec
 LANGUAGE c
 IMMUTABLE PARALLEL SAFE STRICT
AS '$libdir/vector', $function$array_to_sparsevec$function$


FUNCTION: CREATE OR REPLACE FUNCTION public.update_inventory()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    IF NEW.transaction_type = 'RECEIVE' THEN
        UPDATE Inventory
        SET quantity_on_hand = quantity_on_hand + NEW.quantity
        WHERE product_id = NEW.product_id;
    ELSIF NEW.transaction_type = 'SALE' THEN
        UPDATE Inventory
        SET quantity_on_hand = quantity_on_hand - NEW.quantity
        WHERE product_id = NEW.product_id;
    END IF;
    RETURN NEW;
END;
$function$


VIEW:  SELECT s.id,
    s.delivered_date,
    i.name AS invoice,
    i.invoice_date,
    i.invoice_number,
    v.vendor
   FROM t_shipments s
     LEFT JOIN t_invoices i ON s.invoice_id = i.id
     LEFT JOIN t_vendors v ON i.vendor_id = v.id;

VIEW:  SELECT 't_discs'::text AS "table",
    t_discs.id,
    'disc id '::text || t_discs.id AS record_name,
    'discs need to be on order sheets'::text AS issue,
    'reorders can''t be done'::text AS effect,
    'important'::text AS severity
   FROM t_discs
  WHERE t_discs.order_sheet_line_id IS NULL;

VIEW:  SELECT sl.product_id,
    sl.stock_on_hand AS current_qty,
    sml.id AS stock_movement_id,
    sml.date_created,
    sml.qty AS movement_qty,
    sml.notes,
    sml.price_cost,
    sml.complete,
    sml.completed_at,
    r.receipt_type,
    r.receipt_date
   FROM stock_ledger sl
     JOIN stock_movement_line sml ON sl.product_id = sml.product_id
     LEFT JOIN receipts r ON sml.receipt_id = r.id
  WHERE sl.product_id = (( SELECT stock_ledger.product_id
           FROM stock_ledger
         LIMIT 1));

VIEW:  SELECT 't_plastics'::text AS "table",
    tp.id,
    (COALESCE(tb.brand, ''::text) || ' '::text) || tp.plastic AS record_name,
        CASE
            WHEN tb.shopify_collection_created_at IS NULL THEN 'Brand collection is not yet uploaded to Shopify.'::text
            ELSE 'Description is null or too short. Write a description.'::text
        END AS issue,
        CASE
            WHEN tb.shopify_collection_created_at IS NULL THEN 'A plastic can''t upload w/o its brand collection already being uploaded.'::text
            ELSE 'Stops the plastic collection from uploading and then also the MPS Collection, OSL Products, and Disc Products.'::text
        END AS effect,
        CASE
            WHEN count(td.id) = 0 THEN 'Important, but we have no discs that currently match this plastic.'::text
            ELSE ('Critical: We have '::text || count(td.id)::text) || ' discs in stock that are not uploading because of this.'::text
        END AS severity
   FROM t_plastics tp
     LEFT JOIN t_mps tm ON tp.id = tm.plastic_id
     LEFT JOIN t_discs td ON tm.id = td.mps_id
     LEFT JOIN t_brands tb ON tp.brand_id = tb.id
     CROSS JOIN ( SELECT t_config.value::integer AS min_length
           FROM t_config
          WHERE t_config.key = 'minimum_length_of_plastic_description'::text) cfg
  WHERE (tp.description IS NULL OR length(tp.description) < cfg.min_length OR tb.shopify_collection_created_at IS NULL) AND tp.shopify_collection_uploaded_at IS NULL
  GROUP BY tp.id, tb.brand, tp.plastic, tb.shopify_collection_created_at;

VIEW:  SELECT vp."table",
    vp.id,
    vp.record_name,
    vp.issue,
    vp.effect,
    vp.severity
   FROM v_todo_plastics vp
  WHERE vp.severity ~~* 'Critical%'::text
UNION ALL
 SELECT vb."table",
    vb.id,
    vb.record_name,
    vb.issue,
    vb.effect,
    vb.severity
   FROM v_todo_brands vb
  WHERE vb.severity ~~* 'Critical%'::text
UNION ALL
 SELECT vm."table",
    vm.id,
    vm.record_name,
    vm.issue,
    vm.effect,
    vm.severity
   FROM v_todo_molds vm
  WHERE vm.severity ~~* 'Critical%'::text;

VIEW:  WITH fba_strategy AS (
         SELECT tm.id AS mps_id,
            tv.amazon_informed_strategy_override AS fba_override,
            tv.amazon_informed_strategy_fba_default AS fba_default,
            COALESCE(tv.amazon_informed_strategy_override::integer, tv.amazon_informed_strategy_fba_default::integer, 37123) AS fba_informed_strategy_id
           FROM t_mps tm
             JOIN t_plastics tp ON tm.plastic_id = tp.id
             JOIN t_brands tb ON tp.brand_id = tb.id
             JOIN t_vendors tv ON tb.vendor_id = tv.id
        ), fbm_vendor_data AS (
         SELECT tm.id AS mps_id,
            tv.amazon_informed_strategy_override,
            tv.amazon_informed_strategy_fbm_default
           FROM t_mps tm
             JOIN t_plastics tp ON tm.plastic_id = tp.id
             JOIN t_brands tb ON tp.brand_id = tb.id
             JOIN t_vendors tv ON tb.vendor_id = tv.id
        ), nibb AS (
         SELECT nib."SKU",
            nib."BUY_BOX_WINNER",
            tis.informed_id AS competitor_informed_id
           FROM it_infor_no_buy_box nib
             LEFT JOIN t_informed_strategies tis ON nib."BUY_BOX_WINNER" = tis.competitor_id
        ), deduplicated_sdasins AS (
         SELECT DISTINCT ON (ts.id) ts.id,
            ts.fba_sku,
            ts.fbm_sku,
            ts.mps_id,
            iif."STOCK" AS fbaqty,
            COALESCE(fba_strategy.fba_informed_strategy_id::bigint, 37123::bigint) AS fba_informed_strategy_id,
            fba_strategy.fba_override,
            fba_strategy.fba_default,
            fbm_vendor_data.amazon_informed_strategy_override AS fbm_override,
            fbm_vendor_data.amazon_informed_strategy_fbm_default AS fbm_default,
            nibb.competitor_informed_id
           FROM t_sdasins ts
             LEFT JOIN it_infor_all_fields iif ON ts.fba_sku = iif."SKU"
             LEFT JOIN fba_strategy ON ts.mps_id = fba_strategy.mps_id
             LEFT JOIN fbm_vendor_data ON ts.mps_id = fbm_vendor_data.mps_id
             LEFT JOIN nibb ON ts.fbm_sku = nibb."SKU"
        )
 SELECT d.id,
    d.fba_sku,
    d.fbm_sku,
    d.fba_informed_strategy_id,
        CASE
            WHEN d.fbaqty > 0 THEN 39861::bigint
            WHEN d.fbm_override IS NOT NULL THEN d.fbm_override::bigint
            WHEN d.competitor_informed_id IS NOT NULL THEN d.competitor_informed_id::bigint
            WHEN d.fbm_default IS NOT NULL THEN d.fbm_default::bigint
            ELSE 45364::bigint
        END AS fbm_informed_strategy_id,
    d.mps_id,
    d.fbaqty AS "STOCK",
        CASE
            WHEN d.fbaqty > 0 THEN 'FBA Stock Available (39861)'::text
            WHEN d.fbm_override IS NOT NULL THEN 'Vendor Override'::text
            WHEN d.competitor_informed_id IS NOT NULL THEN 'No-Buy-Box Competitor-Based'::text
            WHEN d.fbm_default IS NOT NULL THEN 'Vendor Default'::text
            ELSE 'Global Default (45364)'::text
        END AS fbm_strategy_basis,
        CASE
            WHEN d.fba_override IS NOT NULL THEN 'Vendor Override (FBA)'::text
            WHEN d.fba_default IS NOT NULL THEN 'Vendor Default (FBA)'::text
            ELSE 'Global Default (37123)'::text
        END AS fba_strategy_basis
   FROM deduplicated_sdasins d;

VIEW:  SELECT ts.fbm_sku AS sku,
    ( SELECT count(*) AS count
           FROM tjoin_discs_sdasins tj
             JOIN t_discs td ON tj.disc_id = td.id
          WHERE tj.sdasin_id = ts.id AND td.sold_date IS NULL) AS disc_qty_in_stock,
    ( SELECT count(*) AS count
           FROM tjoin_discs_sdasins tj
             JOIN t_discs td ON tj.disc_id = td.id
          WHERE tj.sdasin_id = ts.id AND td.sold_date IS NOT NULL AND td.sold_channel = 'FBM'::text) AS disc_qty_sold_fbm,
    COALESCE(vsa.avg_carrying_cost_fbm, 0.0::double precision) + ((( SELECT tc.value
           FROM t_config tc
          WHERE tc.key = 'fbm_fees'::text
         LIMIT 1))::numeric)::double precision AS total_cost,
    (COALESCE(vsa.avg_carrying_cost_fbm, 0.0::double precision) + ((( SELECT tc.value
           FROM t_config tc
          WHERE tc.key = 'fbm_fees'::text
         LIMIT 1))::numeric)::double precision) / 0.85::double precision AS total_cost_with_fees,
    GREATEST(COALESCE(tm.val_override_map_price, tp.val_map_price, 0::numeric)::double precision, (COALESCE(vsa.avg_carrying_cost_fbm, 0.0::double precision) + ((( SELECT tc.value
           FROM t_config tc
          WHERE tc.key = 'fbm_fees'::text
         LIMIT 1))::numeric)::double precision) / 0.85::double precision) AS our_min,
    COALESCE(ts.override_amazon_max_price, tm.val_override_max_amazon_price, tp.val_max_amazon_price, tp.val_msrp) AS our_max,
    COALESCE(tm.val_override_map_price, tp.val_map_price) AS our_map,
    vs.fbm_informed_strategy_id AS informed_strategy_id,
    'Amazon FBM'::text AS listing_type
   FROM t_sdasins ts
     JOIN v_sdasins_avg_carrying_costs vsa ON ts.id = vsa.sdasin_id
     JOIN t_mps tm ON ts.mps_id = tm.id
     JOIN t_plastics tp ON tm.plastic_id = tp.id
     LEFT JOIN v_sdasins vs ON ts.id = vs.id
  WHERE ts.fbm_uploaded_at IS NOT NULL;

VIEW:  SELECT vpfba.sku,
    '9432'::text AS marketplace_id,
    (vpfba.our_min - 0.01::double precision)::numeric AS cost,
    'USD'::text AS currency,
    vpfba.our_min AS min_price,
    vpfba.our_max AS max_price,
    vpfba.our_map AS map_price,
    vpfba.listing_type,
    vs.fba_informed_strategy_id AS strategy_id,
    '1'::text AS managed,
    now() AS dateadded
   FROM v_informed_prep_discs_fba vpfba
     JOIN v_informed_prep_disctint_fba_sdasins vs ON vpfba.sku = vs.fba_sku
UNION ALL
 SELECT vpfbm.sku,
    '9432'::text AS marketplace_id,
    (vpfbm.our_min - 0.01::double precision)::numeric AS cost,
    'USD'::text AS currency,
    vpfbm.our_min AS min_price,
    vpfbm.our_max AS max_price,
    vpfbm.our_map AS map_price,
    vpfbm.listing_type,
    vs.fbm_informed_strategy_id AS strategy_id,
    '1'::text AS managed,
    now() AS dateadded
   FROM v_informed_prep_discs_fbm vpfbm
     JOIN v_informed_prep_disctint_fbm_sdasins vs ON vpfbm.sku = vs.fbm_sku;

VIEW:  SELECT ts.fba_sku AS sku,
    ( SELECT count(*) AS count
           FROM tjoin_discs_sdasins tj
             JOIN t_discs td ON tj.disc_id = td.id
          WHERE tj.sdasin_id = ts.id AND td.sold_date IS NULL) AS disc_qty_in_stock,
    ( SELECT count(*) AS count
           FROM tjoin_discs_sdasins tj
             JOIN t_discs td ON tj.disc_id = td.id
          WHERE tj.sdasin_id = ts.id AND td.sold_date IS NOT NULL AND td.sold_channel = 'FBA'::text) AS disc_qty_sold_fba,
    COALESCE(vsa.avg_carrying_cost_fba, 0.0::double precision) + ((( SELECT tc.value
           FROM t_config tc
          WHERE tc.key = 'fba_min_profit'::text
         LIMIT 1))::numeric)::double precision + ((( SELECT tc.value
           FROM t_config tc
          WHERE tc.key = 'fba_s_and_h_fees'::text
         LIMIT 1))::numeric)::double precision AS total_cost,
    (COALESCE(vsa.avg_carrying_cost_fba, 0.0::double precision) + ((( SELECT tc.value
           FROM t_config tc
          WHERE tc.key = 'fba_min_profit'::text
         LIMIT 1))::numeric)::double precision + ((( SELECT tc.value
           FROM t_config tc
          WHERE tc.key = 'fba_s_and_h_fees'::text
         LIMIT 1))::numeric)::double precision) / 0.85::double precision AS total_cost_with_fees,
    GREATEST(COALESCE(tm.val_override_map_price, tp.val_map_price, 0::numeric)::double precision, (COALESCE(vsa.avg_carrying_cost_fba, 0.0::double precision) + ((( SELECT tc.value
           FROM t_config tc
          WHERE tc.key = 'fba_min_profit'::text
         LIMIT 1))::numeric)::double precision + ((( SELECT tc.value
           FROM t_config tc
          WHERE tc.key = 'fba_s_and_h_fees'::text
         LIMIT 1))::numeric)::double precision) / 0.85::double precision) AS our_min,
    COALESCE(ts.override_amazon_max_price, tm.val_override_max_amazon_price, tp.val_max_amazon_price, tp.val_msrp) AS our_max,
    COALESCE(tm.val_override_map_price, tp.val_map_price) AS our_map,
    vs.fba_informed_strategy_id AS informed_strategy_id,
    'Amazon FBA'::text AS listing_type
   FROM t_sdasins ts
     JOIN v_sdasins_avg_carrying_costs vsa ON ts.id = vsa.sdasin_id
     JOIN t_mps tm ON ts.mps_id = tm.id
     JOIN t_plastics tp ON tm.plastic_id = tp.id
     LEFT JOIN v_sdasins vs ON ts.id = vs.id
  WHERE ts.fba_uploaded_at IS NOT NULL;

VIEW:  SELECT mps.id,
    mps.plastic_id,
    p.plastic AS plastic_name,
    p.description AS plastic_description,
    p.val_order_cost AS plastic_order_cost,
    p.val_map_price AS plastic_map_price,
    p.val_retail_price AS plastic_retail_price,
    p.val_sale_price AS plastic_sale_price,
    p.val_msrp AS plastic_msrp,
    p.val_max_amazon_price AS plastic_max_amazon_price,
    mps.mold_id,
    m.mold AS mold_name,
    m.description AS mold_description,
    m.type AS mold_type,
    m.speed AS flight_speed,
    m.glide AS flight_glide,
    m.turn AS flight_turn,
    m.fade AS flight_fade,
    mps.stamp_id,
    s.stamp AS stamp_name,
    s.description AS stamp_description,
    s.tags AS stamp_tags,
    pl.name AS player_name,
    pl.pdga_number AS player_pdga_number,
    pl.image_file_name AS player_image,
    mps.vendor_status,
    mps.notes,
    mps.val_override_max_amazon_price,
    mps.val_override_order_cost,
    mps.val_override_map_price,
    mps.val_override_retail_price,
    mps.val_override_sale_price,
    mps.val_override_msrp,
    mps.upc,
    mps.release_date_online,
    mps.release_date_instore,
    mps.created_by,
    mps.description AS mps_description,
        CASE
            WHEN mps.image_file_name IS NULL OR mps.image_file_name = ''::text THEN NULL::text
            ELSE concat(( SELECT t_config.value
               FROM t_config
              WHERE t_config.key = 'mps_image_base_url'::text), mps.image_file_name)
        END AS mps_image_url,
    concat(mps.id, ' - ', m.mold, ' - ', p.plastic, ' - ', s.stamp) AS code
   FROM t_mps mps
     LEFT JOIN t_molds m ON mps.mold_id = m.id
     LEFT JOIN t_plastics p ON mps.plastic_id = p.id
     LEFT JOIN t_stamps s ON mps.stamp_id = s.id
     LEFT JOIN t_players pl ON s.player_id = pl.id;

VIEW:  SELECT osl.id AS osl_id,
    osl.available_quantity,
    COALESCE(sum(
        CASE
            WHEN d.sold_date >= (now() - '30 days'::interval) THEN 1
            ELSE 0
        END), 0::bigint) AS sold_last_30_days,
    COALESCE(sum(
        CASE
            WHEN d.sold_date >= (now() - '90 days'::interval) THEN 1
            ELSE 0
        END), 0::bigint) AS sold_last_90_days,
    osl_lines.min_weight,
    osl_lines.max_weight,
    v_mps.plastic_name,
    v_mps.mold_name,
    v_mps.stamp_name,
    t_mps.id AS mps_id,
    t_vendors.vendor AS vendor_name,
    osl_lines.sort1,
    osl_lines.sort2
   FROM t_inv_osl osl
     LEFT JOIN t_discs d ON d.order_sheet_line_id = osl.id
     LEFT JOIN t_order_sheet_lines osl_lines ON osl.id = osl_lines.id
     LEFT JOIN v_mps v_mps ON v_mps.id = osl_lines.mps_id
     LEFT JOIN t_mps ON osl_lines.mps_id = t_mps.id
     LEFT JOIN t_vendors ON osl_lines.vendor_id = t_vendors.id
  GROUP BY osl.id, osl_lines.min_weight, osl_lines.max_weight, v_mps.plastic_name, v_mps.mold_name, v_mps.stamp_name, t_mps.id, t_vendors.vendor, osl_lines.sort1, osl_lines.sort2;

VIEW:  SELECT d.id,
    ''::text AS title,
    concat(d.id, ' ', d.location, ' ', b.brand, ' ', m.mold, ' ', COALESCE(cm.modifier, ''::text), ' ', COALESCE(c.color, ''::text), ' ', d.weight) AS pull,
    d.mps_id,
    d.carrying_cost,
    d.sold_date,
    v.vendor,
    b.brand,
    d.order_sheet_line_id,
    d.location,
    d.weight,
    d.color_id,
    c.color,
    vm.code AS mps_code,
    d.created_at,
    d.notes,
    d.looked_for_matching_sdasin_at,
    d.sdasin_searched_for_at,
    d.description,
    d.image_file_name
   FROM t_discs d
     LEFT JOIN t_mps mps ON d.mps_id = mps.id
     LEFT JOIN v_mps vm ON d.mps_id = vm.id
     LEFT JOIN t_molds m ON mps.mold_id = m.id
     LEFT JOIN t_brands b ON m.brand_id = b.id
     LEFT JOIN t_vendors v ON b.vendor_id = v.id
     LEFT JOIN t_color_modifiers cm ON d.color_modifier_id = cm.id
     LEFT JOIN t_colors c ON d.color_id = c.id;

VIEW:  SELECT DISTINCT ON (v_sdasins.fba_sku) v_sdasins.id,
    v_sdasins.fba_sku,
    v_sdasins.fba_informed_strategy_id
   FROM v_sdasins
  WHERE v_sdasins.fba_sku IS NOT NULL;

VIEW:  SELECT DISTINCT ON (v_sdasins.fbm_sku) v_sdasins.id,
    v_sdasins.fbm_sku,
    v_sdasins.fbm_informed_strategy_id
   FROM v_sdasins
  WHERE v_sdasins.fbm_sku IS NOT NULL;

VIEW:  WITH cfg AS (
         SELECT t_config.value::integer AS min_length
           FROM t_config
          WHERE t_config.key = 'minimum_length_of_mold_description'::text
        )
 SELECT 't_molds'::text AS "table",
    m.id,
    (COALESCE(b.brand, ''::text) || ' '::text) || m.mold AS record_name,
        CASE
            WHEN i.image_file_name IS NULL THEN 'Mold has no image file name.'::text
            WHEN i.image_file_name IS NOT NULL AND (i.image_verified IS FALSE OR i.image_verified IS NULL) THEN 'There is an image file name but its existence has not yet been confirmed.'::text
            WHEN m.speed IS NULL OR m.glide IS NULL OR m.turn IS NULL OR m.fade IS NULL THEN 'Flight numbers are not complete.'::text
            WHEN m.description IS NULL OR length(m.description) < cfg.min_length THEN 'Mold doesn''t have a description.'::text
            ELSE NULL::text
        END AS issue,
        CASE
            WHEN i.image_file_name IS NULL THEN 'If we don''t have a mold image, then the mold collection can''t upload.'::text
            WHEN i.image_file_name IS NOT NULL AND (i.image_verified IS FALSE OR i.image_verified IS NULL) THEN 'Mold collections won''t upload without a verified image file.'::text
            WHEN m.speed IS NULL OR m.glide IS NULL OR m.turn IS NULL OR m.fade IS NULL THEN 'Molds must have flight numbers for proper upload and filtering.'::text
            WHEN m.description IS NULL OR length(m.description) < cfg.min_length THEN 'Mold collections don''t upload without a description.'::text
            ELSE NULL::text
        END AS effect,
        CASE
            WHEN i.image_file_name IS NULL THEN
            CASE
                WHEN count(d.id) = 0 THEN 'Important. We need a mold image so the mold collection can upload.'::text
                ELSE ('Critical! We have '::text || count(d.id)::text) || ' discs in stock right now that can''t upload because we don''t have this mold image.'::text
            END
            WHEN i.image_file_name IS NOT NULL AND (i.image_verified IS FALSE OR i.image_verified IS NULL) THEN
            CASE
                WHEN count(d.id) = 0 THEN 'Important. We need mold image verified so the mold collection can upload.'::text
                ELSE ('Critical! We have '::text || count(d.id)::text) || ' discs in stock right now that can''t upload because we don''t have this mold image verified.'::text
            END
            WHEN m.speed IS NULL OR m.glide IS NULL OR m.turn IS NULL OR m.fade IS NULL THEN
            CASE
                WHEN count(d.id) = 0 THEN 'Important. We need mold flight numbers so the mold collection can upload and filters work correctly.'::text
                ELSE ('Critical! We have '::text || count(d.id)::text) || ' discs in stock right now that can''t upload because we don''t have mold flight numbers.'::text
            END
            WHEN m.description IS NULL OR length(m.description) < cfg.min_length THEN
            CASE
                WHEN count(d.id) = 0 THEN 'Important. We need a mold description so the mold collection can upload.'::text
                ELSE ('Critical! We have '::text || count(d.id)::text) || ' discs in stock right now that can''t upload because we don''t have a mold description.'::text
            END
            ELSE NULL::text
        END AS severity
   FROM t_molds m
     LEFT JOIN t_brands b ON m.brand_id = b.id
     LEFT JOIN t_images i ON i.table_name = 't_molds'::text AND i.record_id = m.id
     LEFT JOIN t_mps mp ON mp.mold_id = m.id
     LEFT JOIN t_discs d ON d.mps_id = mp.id AND d.sold_date IS NULL
     CROSS JOIN cfg
  WHERE (i.image_file_name IS NULL OR i.image_file_name IS NOT NULL AND (i.image_verified IS FALSE OR i.image_verified IS NULL) OR m.speed IS NULL OR m.glide IS NULL OR m.turn IS NULL OR m.fade IS NULL OR m.description IS NULL OR length(m.description) < cfg.min_length) AND m.shopify_collection_created_at IS NULL
  GROUP BY m.id, b.brand, m.mold, i.image_file_name, i.image_verified, m.speed, m.glide, m.turn, m.fade, m.description, cfg.min_length;

VIEW:  SELECT vd.created_at,
    vd.id,
    vd.brand,
    vd.mps_code,
    vd.weight,
    vd.color,
    vd.description,
    vd.sdasin_searched_for_at,
    vd.notes,
    vd.looked_for_matching_sdasin_at,
    vd.sold_date
   FROM v_discs vd
  WHERE NOT (vd.id IN ( SELECT tjoin_discs_sdasins.disc_id
           FROM tjoin_discs_sdasins)) AND vd.sold_date IS NULL AND lower(vd.mps_code) !~~ '%dzdiscs%'::text AND lower(vd.mps_code) !~~ '%maverick%'::text;

VIEW:  WITH filtered_brands AS (
         SELECT t_brands.id,
            t_brands.brand,
            t_brands.vendor_id,
            t_brands.code,
            t_brands.created_at,
            t_brands.shopify_tag,
            t_brands.shopify_collection_created_at,
            t_brands.shopify_collection_customized_at,
            t_brands.shopify_in_nav_at,
            t_brands.shopify_rel_canon,
            t_brands.seo_meta_description,
            t_brands.shopify,
            t_brands.country_of_mfg,
            t_brands.created_by,
            t_brands.updated_at,
            t_brands.updated_by
           FROM t_brands
          WHERE t_brands.shopify = true AND t_brands.shopify_collection_created_at IS NULL
        ), brand_disc_counts AS (
         SELECT tmold.brand_id,
            count(td.id) AS disc_count
           FROM t_mps tm
             LEFT JOIN t_discs td ON tm.id = td.mps_id AND td.sold_date IS NULL
             LEFT JOIN t_molds tmold ON tm.mold_id = tmold.id
          GROUP BY tmold.brand_id
        ), config AS (
         SELECT t_config.value::integer AS min_desc_length
           FROM t_config
          WHERE t_config.key = 'minimum_length_of_brand_description'::text
        )
 SELECT 't_brands'::text AS "table",
    fb.id,
    fb.brand AS record_name,
    'Code is null'::text AS issue,
    'No code keeps mps lookups from functioning correctly.'::text AS effect,
    'Critical - Every brand needs a code to help entry forms be more efficient.'::text AS severity
   FROM filtered_brands fb
  WHERE fb.code IS NULL OR fb.code = ''::text
UNION ALL
 SELECT 't_brands'::text AS "table",
    fb.id,
    fb.brand AS record_name,
    'Shopify tag is null'::text AS issue,
    'No tag keeps on-site brand filters from working.'::text AS effect,
    'Critical - Every brand needs a tag for filters to work.'::text AS severity
   FROM filtered_brands fb
  WHERE fb.shopify_tag IS NULL OR fb.shopify_tag = ''::text
UNION ALL
 SELECT 't_brands'::text AS "table",
    fb.id,
    fb.brand AS record_name,
    'Shopify rel-canon is null'::text AS issue,
    'No rel-canon keeps on-site SEO from working correctly.'::text AS effect,
    'Critical - Every brand needs a shopify_rel_canon.'::text AS severity
   FROM filtered_brands fb
  WHERE fb.shopify_rel_canon IS NULL OR fb.shopify_rel_canon = ''::text
UNION ALL
 SELECT 't_brands'::text AS "table",
    fb.id,
    fb.brand AS record_name,
    'Vendor ID is null'::text AS issue,
    'No vendor id means we do not know where to get this brand from.'::text AS effect,
    'Critical - Every brand needs a vendor_id.'::text AS severity
   FROM filtered_brands fb
  WHERE fb.vendor_id IS NULL
UNION ALL
 SELECT 't_brands'::text AS "table",
    fb.id,
    fb.brand AS record_name,
    'SEO meta description is too short'::text AS issue,
    'Brand collection will not upload with too short of a description.'::text AS effect,
        CASE
            WHEN COALESCE(bdc.disc_count, 0::bigint) = 0 THEN 'Important, but we have no discs that currently match this brand.'::text
            ELSE ('Critical: We have '::text || bdc.disc_count) || ' discs in stock that are not uploading because this brand will not upload without a better description.'::text
        END AS severity
   FROM filtered_brands fb
     LEFT JOIN brand_disc_counts bdc ON fb.id = bdc.brand_id
     CROSS JOIN config cfg
  WHERE fb.seo_meta_description IS NULL OR length(fb.seo_meta_description) < cfg.min_desc_length;

VIEW:  SELECT ts.id AS sdasin_id,
    ts.fbm_sku AS sku,
    vsa.avg_carrying_cost_fbm AS avg_carrying_cost,
    COALESCE(m.val_override_order_cost, p.val_order_cost) AS order_cost,
    ( SELECT i.total_amount / NULLIF(i.subtotal, 0::numeric)
           FROM t_discs d
             JOIN tjoin_discs_sdasins tj ON d.id = tj.disc_id
             JOIN t_shipments s ON d.shipment_id = s.id
             JOIN t_invoices i ON s.invoice_id = i.id
          WHERE tj.sdasin_id = ts.id
         LIMIT 1) AS shipping_multiplier,
    ( SELECT tc.value::double precision AS value
           FROM t_config tc
          WHERE tc.key = 'fbm_fees'::text
         LIMIT 1) AS fbm_fees,
    COALESCE(vsa.avg_carrying_cost_fbm, 0.0::double precision) + (( SELECT tc.value::double precision AS value
           FROM t_config tc
          WHERE tc.key = 'fbm_fees'::text
         LIMIT 1)) AS total_cost,
    (COALESCE(vsa.avg_carrying_cost_fbm, 0.0::double precision) + (( SELECT tc.value::double precision AS value
           FROM t_config tc
          WHERE tc.key = 'fbm_fees'::text
         LIMIT 1))) / 0.85::double precision AS total_cost_with_fees,
    GREATEST(COALESCE(m.val_override_map_price, p.val_map_price, 0.0)::double precision, (COALESCE(vsa.avg_carrying_cost_fbm, 0.0::double precision) + (( SELECT tc.value::double precision AS value
           FROM t_config tc
          WHERE tc.key = 'fbm_fees'::text
         LIMIT 1))) / 0.85::double precision) AS our_min,
    ts.override_amazon_max_price,
    m.val_override_max_amazon_price,
    p.val_max_amazon_price,
    p.val_msrp,
    COALESCE(ts.override_amazon_max_price, m.val_override_max_amazon_price, p.val_max_amazon_price, p.val_msrp) AS our_max
   FROM t_sdasins ts
     JOIN v_sdasins_avg_carrying_costs vsa ON ts.id = vsa.sdasin_id
     JOIN t_mps m ON ts.mps_id = m.id
     JOIN t_plastics p ON m.plastic_id = p.id;

VIEW:  WITH brand_totals AS (
         SELECT 'Brand'::text AS category,
            b.brand AS name,
            count(d.id) AS disc_count,
            sum(d.carrying_cost) AS total_cost
           FROM t_discs d
             JOIN t_mps m ON d.mps_id = m.id
             JOIN t_plastics p ON m.plastic_id = p.id
             JOIN t_brands b ON p.brand_id = b.id
          WHERE d.sold_date IS NULL
          GROUP BY b.brand
        ), vendor_totals AS (
         SELECT 'Vendor'::text AS category,
            v.vendor AS name,
            count(d.id) AS disc_count,
            sum(d.carrying_cost) AS total_cost
           FROM t_discs d
             JOIN t_mps m ON d.mps_id = m.id
             JOIN t_plastics p ON m.plastic_id = p.id
             JOIN t_brands b ON p.brand_id = b.id
             JOIN t_vendors v ON b.vendor_id = v.id
          WHERE d.sold_date IS NULL
          GROUP BY v.vendor
        ), total_cost AS (
         SELECT 'Total'::text AS category,
            NULL::text AS name,
            count(d.id) AS disc_count,
            sum(d.carrying_cost) AS total_cost
           FROM t_discs d
          WHERE d.sold_date IS NULL
        )
 SELECT brand_totals.category,
    brand_totals.name,
    brand_totals.disc_count,
    brand_totals.total_cost
   FROM brand_totals
UNION ALL
 SELECT vendor_totals.category,
    vendor_totals.name,
    vendor_totals.disc_count,
    vendor_totals.total_cost
   FROM vendor_totals
UNION ALL
 SELECT total_cost.category,
    total_cost.name,
    total_cost.disc_count,
    total_cost.total_cost
   FROM total_cost;

VIEW:  SELECT 'innova_osl'::text AS "table",
    concat(i."Description", ' ', i."Matrix Option 1", ' ', i."Internal ID") AS record_name,
    'Order sheet line from Innova is not in our dbase.'::text AS issue,
    'We can''t order what we don''t track.'::text AS effect,
    'Order Sheet Critical'::text AS severity,
    i."Category" AS category
   FROM innova_osl i
     LEFT JOIN t_order_sheet_lines osl ON i."Internal ID" = osl.vendor_internal_id
  WHERE osl.id IS NULL AND COALESCE(NULLIF(i."Availability", ''::text), '0'::text)::integer > 0 AND (i."Category" <> ALL (ARRAY['Ultimate/Sport Discs'::text, 'Targets'::text, 'Short Sleeve Tee'::text, 'Performance Apparel'::text, 'Mini Markers'::text, 'Long Sleeve Tee'::text, 'Iron On Patches'::text, 'Hats'::text, 'Display / POP'::text, 'Bags'::text, 'Apparel'::text, 'Accessories'::text, '3-Pack Sets'::text]));

VIEW:  SELECT ts.id AS sdasin_id,
    COALESCE(( SELECT avg(vd.carrying_cost) AS avg
           FROM tjoin_discs_sdasins tj
             JOIN t_discs td ON tj.disc_id = td.id
             JOIN v_discs vd ON td.id = vd.id
          WHERE tj.sdasin_id = ts.id AND td.sold_date IS NULL), ( SELECT avg(vd.carrying_cost) AS avg
           FROM tjoin_discs_sdasins tj
             JOIN t_discs td ON tj.disc_id = td.id
             JOIN v_discs vd ON td.id = vd.id
          WHERE tj.sdasin_id = ts.id AND td.sold_date IS NOT NULL AND td.sold_channel = 'FBM'::text), ( SELECT avg(vd.carrying_cost) AS avg
           FROM tjoin_discs_sdasins tj
             JOIN t_discs td ON tj.disc_id = td.id
             JOIN v_discs vd ON td.id = vd.id
          WHERE tj.sdasin_id = ts.id AND td.sold_date IS NOT NULL), 0.0::double precision) AS avg_carrying_cost_fbm,
    ( SELECT count(*) AS count
           FROM tjoin_discs_sdasins tj
             JOIN t_discs td ON tj.disc_id = td.id
          WHERE tj.sdasin_id = ts.id AND td.sold_date IS NULL) AS qty_in_stock,
    ( SELECT count(*) AS count
           FROM tjoin_discs_sdasins tj
             JOIN t_discs td ON tj.disc_id = td.id
          WHERE tj.sdasin_id = ts.id AND td.sold_date IS NOT NULL AND td.sold_channel = 'FBM'::text) AS fbm_qty_sold,
    ( SELECT count(*) AS count
           FROM tjoin_discs_sdasins tj
             JOIN t_discs td ON tj.disc_id = td.id
          WHERE tj.sdasin_id = ts.id AND td.sold_date IS NOT NULL) AS misc_qty_sold,
    COALESCE(( SELECT avg(vd.carrying_cost) AS avg
           FROM tjoin_discs_sdasins tj
             JOIN t_discs td ON tj.disc_id = td.id
             JOIN v_discs vd ON td.id = vd.id
          WHERE tj.sdasin_id = ts.id AND td.sold_date IS NOT NULL AND td.sold_channel = 'FBA'::text), ( SELECT avg(vd.carrying_cost) AS avg
           FROM tjoin_discs_sdasins tj
             JOIN t_discs td ON tj.disc_id = td.id
             JOIN v_discs vd ON td.id = vd.id
          WHERE tj.sdasin_id = ts.id AND td.sold_date IS NOT NULL), ( SELECT avg(vd.carrying_cost) AS avg
           FROM tjoin_discs_sdasins tj
             JOIN t_discs td ON tj.disc_id = td.id
             JOIN v_discs vd ON td.id = vd.id
          WHERE tj.sdasin_id = ts.id), 5.00::double precision) AS avg_carrying_cost_fba,
    ( SELECT count(*) AS count
           FROM tjoin_discs_sdasins tj
             JOIN t_discs td ON tj.disc_id = td.id
          WHERE tj.sdasin_id = ts.id AND td.sold_date IS NOT NULL AND td.sold_channel = 'FBA'::text) AS fba_qty_sold
   FROM t_sdasins ts;

VIEW:  WITH disc_data AS (
         SELECT osl.vendor_internal_id,
            count(*) FILTER (WHERE d.sold_date > (now() - '90 days'::interval)) AS sold90,
            count(*) FILTER (WHERE d.sold_date IS NULL) AS onhand
           FROM t_discs d
             JOIN t_order_sheet_lines osl ON d.order_sheet_line_id = osl.id
          GROUP BY osl.vendor_internal_id
        ), order_sheet_exists AS (
         SELECT DISTINCT t_order_sheet_lines.vendor_internal_id
           FROM t_order_sheet_lines
        )
 SELECT i.id,
    i."Description" AS description,
    i."Availability" AS availability,
    i."Internal ID" AS internal_id,
    COALESCE(dd.sold90, 0::bigint) AS sold90,
    COALESCE(dd.onhand, 0::bigint) AS onhand,
        CASE
            WHEN ose.vendor_internal_id IS NOT NULL AND COALESCE(dd.onhand, 0::bigint) = 0 THEN 1
            ELSE 0
        END AS oos_bump,
    GREATEST(ceil(COALESCE(dd.sold90, 0::bigint)::numeric / 2.0) +
        CASE
            WHEN ose.vendor_internal_id IS NOT NULL AND COALESCE(dd.onhand, 0::bigint) = 0 THEN 1
            ELSE 0
        END::numeric, 0::numeric) AS max,
        CASE
            WHEN LEAST(GREATEST(ceil(COALESCE(dd.sold90, 0::bigint)::numeric / 2.0) +
            CASE
                WHEN ose.vendor_internal_id IS NOT NULL AND COALESCE(dd.onhand, 0::bigint) = 0 THEN 1
                ELSE 0
            END::numeric - COALESCE(dd.onhand, 0::bigint)::numeric, 0::numeric), COALESCE(NULLIF(i."Availability", ''::text), '0'::text)::integer::numeric) = 1::numeric AND COALESCE(NULLIF(i."Availability", ''::text), '0'::text)::integer > 1 THEN 2::numeric
            ELSE LEAST(GREATEST(ceil(COALESCE(dd.sold90, 0::bigint)::numeric / 2.0) +
            CASE
                WHEN ose.vendor_internal_id IS NOT NULL AND COALESCE(dd.onhand, 0::bigint) = 0 THEN 1
                ELSE 0
            END::numeric - COALESCE(dd.onhand, 0::bigint)::numeric, 0::numeric), COALESCE(NULLIF(i."Availability", ''::text), '0'::text)::integer::numeric)
        END AS order_now
   FROM innova_osl i
     LEFT JOIN disc_data dd ON dd.vendor_internal_id = i."Internal ID"
     LEFT JOIN order_sheet_exists ose ON ose.vendor_internal_id = i."Internal ID"
  ORDER BY i.id;

TRIGGER: tr_check_filters CREATE TRIGGER tr_check_filters BEFORE INSERT OR UPDATE ON realtime.subscription FOR EACH ROW EXECUTE FUNCTION realtime.subscription_check_filters()

TRIGGER: key_encrypt_secret_trigger_raw_key CREATE TRIGGER key_encrypt_secret_trigger_raw_key BEFORE INSERT OR UPDATE OF raw_key ON pgsodium.key FOR EACH ROW EXECUTE FUNCTION pgsodium.key_encrypt_secret_raw_key()

TRIGGER: secrets_encrypt_secret_trigger_secret CREATE TRIGGER secrets_encrypt_secret_trigger_secret BEFORE INSERT OR UPDATE OF secret ON vault.secrets FOR EACH ROW EXECUTE FUNCTION vault.secrets_encrypt_secret_secret()

TRIGGER: set_last_change_date CREATE TRIGGER set_last_change_date BEFORE UPDATE ON public.stock_ledger FOR EACH ROW EXECUTE FUNCTION update_last_change_date_stock_ledger()

TRIGGER: t_receipt_completed CREATE TRIGGER t_receipt_completed AFTER UPDATE ON public.receipts FOR EACH ROW WHEN (((new.complete = true) AND (old.complete = false))) EXECUTE FUNCTION f_receipt_completed()

TRIGGER: trg_manage_inv_on_join_inserts_and_deletes CREATE TRIGGER trg_manage_inv_on_join_inserts_and_deletes AFTER INSERT OR DELETE ON public.tjoin_discs_sdasins FOR EACH ROW EXECUTE FUNCTION manage_inv_on_join_inserts_and_deletes()

TRIGGER: trig_get_veeqo_id_on_updates CREATE TRIGGER trig_get_veeqo_id_on_updates BEFORE UPDATE ON public.t_sdasins FOR EACH ROW WHEN ((new.veeqo_id IS NULL)) EXECUTE FUNCTION get_veeqo_id_on_update()

TRIGGER: t_update_veeqo_qty CREATE TRIGGER t_update_veeqo_qty AFTER UPDATE OF stock_on_hand ON public.stock_ledger FOR EACH ROW EXECUTE FUNCTION f_update_veeqo_qty()

TRIGGER: cron_job_cache_invalidate CREATE TRIGGER cron_job_cache_invalidate AFTER INSERT OR DELETE OR UPDATE OR TRUNCATE ON cron.job FOR EACH STATEMENT EXECUTE FUNCTION cron.job_cache_invalidate()

TRIGGER: trg_manage_inv_on_join_inserts_and_deletes_statement CREATE TRIGGER trg_manage_inv_on_join_inserts_and_deletes_statement AFTER INSERT OR DELETE OR TRUNCATE ON public.tjoin_discs_sdasins FOR EACH STATEMENT EXECUTE FUNCTION manage_inv_on_join_inserts_and_deletes()

TRIGGER: trig_handle_disc_carrying_cost CREATE TRIGGER trig_handle_disc_carrying_cost BEFORE INSERT OR UPDATE OF mps_id, shipment_id ON public.t_discs FOR EACH ROW EXECUTE FUNCTION handle_disc_carrying_cost()

TRIGGER: update_veeqo_sellable_quantity_trigger CREATE TRIGGER update_veeqo_sellable_quantity_trigger AFTER UPDATE OF available_quantity ON public.t_inv_sdasin FOR EACH ROW EXECUTE FUNCTION update_veeqo_sellable_quantity()

TRIGGER: tr_try_publish_collection_plastic CREATE TRIGGER tr_try_publish_collection_plastic AFTER UPDATE OF plastic, description, code, shopify_collection_uploaded_at, brand_id ON public.t_plastics FOR EACH ROW WHEN ((new.shopify_collection_uploaded_at IS NULL)) EXECUTE FUNCTION fn_try_publish_collection_plastic()

TRIGGER: update_objects_updated_at CREATE TRIGGER update_objects_updated_at BEFORE UPDATE ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.update_updated_at_column()

TRIGGER: trigger_dynamic_execute_function CREATE TRIGGER trigger_dynamic_execute_function BEFORE UPDATE ON public.t_run_functions FOR EACH ROW WHEN (((new.run IS TRUE) AND (old.run IS DISTINCT FROM new.run))) EXECUTE FUNCTION dynamic_execute_function()

TRIGGER: trig_handle_disc_matches_and_inv_on_osl_insert_or_delete_update CREATE TRIGGER trig_handle_disc_matches_and_inv_on_osl_insert_or_delete_update AFTER INSERT OR DELETE OR UPDATE OF mps_id, color_id, min_weight, max_weight ON public.t_order_sheet_lines FOR EACH ROW EXECUTE FUNCTION handle_order_sheet_line_events()

TRIGGER: update_updated_at_t_sdasins CREATE TRIGGER update_updated_at_t_sdasins BEFORE UPDATE ON public.t_sdasins FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()

TRIGGER: update_updated_at_t_invoices CREATE TRIGGER update_updated_at_t_invoices BEFORE UPDATE ON public.t_invoices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()

TRIGGER: update_updated_at_t_shipments CREATE TRIGGER update_updated_at_t_shipments BEFORE UPDATE ON public.t_shipments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()

TRIGGER: add_stock_ledger_entry CREATE TRIGGER add_stock_ledger_entry AFTER INSERT ON public.products FOR EACH ROW EXECUTE FUNCTION create_stock_ledger_entry()

TRIGGER: update_updated_at_receipts CREATE TRIGGER update_updated_at_receipts BEFORE UPDATE ON public.receipts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()

TRIGGER: update_updated_at_tjoin_discs_sdasins CREATE TRIGGER update_updated_at_tjoin_discs_sdasins BEFORE UPDATE ON public.tjoin_discs_sdasins FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()

TRIGGER: update_updated_at_t_inv_osl CREATE TRIGGER update_updated_at_t_inv_osl BEFORE UPDATE ON public.t_inv_osl FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()

TRIGGER: update_updated_at_t_inv_sdasin CREATE TRIGGER update_updated_at_t_inv_sdasin BEFORE UPDATE ON public.t_inv_sdasin FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()

TRIGGER: tr_osl_on_disc_in_up_del CREATE TRIGGER tr_osl_on_disc_in_up_del AFTER INSERT OR DELETE OR UPDATE ON public.t_discs FOR EACH ROW EXECUTE FUNCTION fn_osl_on_disc_in_up_del()

TRIGGER: trig_manage_inv_sdasin_on_disc_sold_unsold CREATE TRIGGER trig_manage_inv_sdasin_on_disc_sold_unsold AFTER UPDATE OF sold_date ON public.t_discs FOR EACH ROW EXECUTE FUNCTION manage_inv_sdasin_on_disc_sold_unsold()

TRIGGER: trigger_get_veeqo_id_on_update_osl CREATE TRIGGER trigger_get_veeqo_id_on_update_osl BEFORE UPDATE ON public.t_order_sheet_lines FOR EACH ROW WHEN (((new.mps_id IS NOT NULL) AND (new.min_weight IS NOT NULL) AND (new.max_weight IS NOT NULL) AND (new.vendor_id IS NOT NULL))) EXECUTE FUNCTION get_veeqo_id_on_update()

TRIGGER: update_updated_at_t_order_sheet_lines CREATE TRIGGER update_updated_at_t_order_sheet_lines BEFORE UPDATE ON public.t_order_sheet_lines FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()

TRIGGER: trg_update_veeqo_qty_osl CREATE TRIGGER trg_update_veeqo_qty_osl AFTER UPDATE OF available_quantity ON public.t_inv_osl FOR EACH ROW WHEN ((old.available_quantity IS DISTINCT FROM new.available_quantity)) EXECUTE FUNCTION update_veeqo_sellable_quantity()

TRIGGER: tr_try_publish_collection_brand CREATE TRIGGER tr_try_publish_collection_brand AFTER UPDATE OF brand, code, shopify_tag, shopify_collection_created_at, shopify_rel_canon, seo_meta_description, shopify ON public.t_brands FOR EACH ROW WHEN (((new.shopify = true) AND (new.shopify_collection_created_at IS NULL))) EXECUTE FUNCTION fn_try_publish_collection_brand()

TRIGGER: tr_match_on_sdasin_in CREATE TRIGGER tr_match_on_sdasin_in AFTER INSERT ON public.t_sdasins FOR EACH ROW WHEN (((new.min_weight IS NOT NULL) AND (new.max_weight IS NOT NULL) AND ((new.mps_id IS NOT NULL) OR (new.mps_id2 IS NOT NULL)) AND (new.color_id IS NOT NULL))) EXECUTE FUNCTION fn_match_on_sdasin_up_in()

TRIGGER: tr_match_on_sdasin_up CREATE TRIGGER tr_match_on_sdasin_up AFTER UPDATE OF min_weight, max_weight, mps_id, mps_id2, color_id ON public.t_sdasins FOR EACH ROW WHEN (((new.min_weight IS NOT NULL) AND (new.max_weight IS NOT NULL) AND ((new.mps_id IS NOT NULL) OR (new.mps_id2 IS NOT NULL)) AND (new.color_id IS NOT NULL))) EXECUTE FUNCTION fn_match_on_sdasin_up_in()

TRIGGER: tr_match_on_disc_in CREATE TRIGGER tr_match_on_disc_in AFTER INSERT ON public.t_discs FOR EACH ROW WHEN (((new.mps_id IS NOT NULL) AND (new.weight IS NOT NULL) AND (new.color_id IS NOT NULL))) EXECUTE FUNCTION fn_match_on_disc_up_in()

TRIGGER: trigger_get_veeqo_id_on_update_discs CREATE TRIGGER trigger_get_veeqo_id_on_update_discs BEFORE UPDATE ON public.t_discs FOR EACH ROW EXECUTE FUNCTION get_veeqo_id_on_update()

TRIGGER: update_updated_at_t_discs CREATE TRIGGER update_updated_at_t_discs BEFORE UPDATE ON public.t_discs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()

TRIGGER: tr_try_publish_collection_mold CREATE TRIGGER tr_try_publish_collection_mold AFTER UPDATE OF mold, description, shopify_collection_created_at, brand_id ON public.t_molds FOR EACH ROW WHEN ((new.shopify_collection_created_at IS NULL)) EXECUTE FUNCTION fn_try_publish_collection_mold()

TRIGGER: tr_match_on_disc_up CREATE TRIGGER tr_match_on_disc_up AFTER UPDATE OF mps_id, weight, color_id ON public.t_discs FOR EACH ROW WHEN (((new.mps_id IS NOT NULL) AND (new.weight IS NOT NULL) AND (new.color_id IS NOT NULL) AND (NOT (old.sold_date IS DISTINCT FROM new.sold_date)))) EXECUTE FUNCTION fn_match_on_disc_up_in()