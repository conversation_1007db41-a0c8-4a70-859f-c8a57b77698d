# Reset FBA Inactive Records

## Overview

This script resets FBA (Fulfilled by Amazon) records for inactive MPS products with zero inventory in both local and Amazon systems. It's designed to remove discontinued products from Amazon FBA and mark them appropriately in the database.

## Files

- **`resetFbaInactiveRecords.js`** - Main script that performs the reset operation
- **Admin Interface** - New card added to the Amazon FBA tab in `admin.html`
- **API Endpoint** - `/api/reset-fba-inactive-records` in `adminServer.js`

## What It Does

The script queries the `v_sdasins_fba_inv0_mps_inactive` view to find FBA records that meet the following criteria:
- `fba_uploaded_at` is not null (has been uploaded to FBA)
- `far.quantity_available` = 0 (zero inventory on Amazon)
- `inv.available_quantity` = 0 (zero local inventory)
- `mps.active` = false (inactive MPS)

For each matching record, it updates the corresponding `t_sdasins` record with:
- `fba` = "N"
- `fbafnsku` = NULL
- `fba_uploaded_at` = NULL
- `min_weight` = 1
- `max_weight` = 2
- `notes` = Prepended with "Discontinued and Out of Stock MPS - Listing removed from Amazon on [timestamp]"

## Database View

The script uses the existing view `v_sdasins_fba_inv0_mps_inactive`:

```sql
create view public.v_sdasins_fba_inv0_mps_inactive as
select
  s.id as sdasin_id,
  s.asin,
  s.parent_asin,
  -- ... other fields
from
  t_sdasins s
  join it_amaz_fulfilled_inventory_report far on far.seller_sku = s.fba_sku
  join t_inv_sdasin inv on inv.id = s.id
  join t_mps mps on s.mps_id = mps.id
where
  s.fba_uploaded_at is not null
  and far.quantity_available = 0
  and inv.available_quantity = 0
  and mps.active = false;
```

## Usage

### Command Line
```bash
node resetFbaInactiveRecords.js
```

### Admin Interface
1. Navigate to the **Amazon FBA** tab in the admin interface
2. Find the **"Reset FBA Inactive Records"** card
3. Click the **"🚫 Reset FBA Inactive Records"** button
4. View the results in the output area

### API Endpoint
```bash
POST /api/reset-fba-inactive-records
Content-Type: application/json
```

## Expected Results

- Disables FBA fulfillment for discontinued products
- Clears FBA-specific identifiers and timestamps
- Standardizes weight ranges to default values (1-2)
- Documents the removal with timestamped notes
- Prevents products from being re-uploaded to FBA automatically

## Safety

- The operation is **safe to run multiple times**
- Only records matching the view criteria will be updated
- Notes are prepended (not overwritten) to preserve existing information
- The script provides detailed feedback on what was changed

## Example Output

```
🚀 Running FBA inactive records reset script...
🔄 Starting FBA inactive records reset...
📊 Querying v_sdasins_fba_inv0_mps_inactive view...
📋 Found 125 records in view
🎯 Updating 125 t_sdasins records...
✅ Successfully updated 125 t_sdasins records
📝 Changes made:
   - fba set to "N"
   - fbafnsku set to NULL
   - fba_uploaded_at set to NULL
   - min_weight set to 1
   - max_weight set to 2
   - notes prepended with discontinuation message

✅ Script completed successfully!
📊 Records found: 125
🔄 Records updated: 125
```

## Notes Field Update

The script prepends the following message to the existing notes:
```
Discontinued and Out of Stock MPS - Listing removed from Amazon on [ISO timestamp]
```

If there were existing notes, they are preserved below the new message, separated by a newline.

## Environment Variables

The script requires the following environment variables (from `.env`):
- `SUPABASE_URL` - Supabase project URL
- `SUPABASE_KEY` - Supabase service role key

## Error Handling

The script includes comprehensive error handling:
- Validates environment variables
- Checks for view existence
- Handles database connection issues
- Processes records individually to handle partial failures
- Provides detailed error messages for failed updates
- Returns structured results for API integration

## Integration

The script is fully integrated into the existing admin system:
- **Admin Interface**: New card in Amazon FBA tab with danger styling
- **API Endpoint**: RESTful endpoint for programmatic access
- **Error Handling**: Consistent with other admin functions
- **Logging**: Detailed console output and structured results

## Important Considerations

⚠️ **This operation removes products from Amazon FBA.** Only run this for products that are truly discontinued and should no longer be sold on Amazon. The notes field will be updated with a permanent record of this action.

The script is designed to be conservative and only processes records that meet all the criteria in the view, ensuring that only truly inactive and discontinued products are affected.
