// Test the view directly to see what fields are available
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

console.log('🔍 Testing v_b2f_pick_slim view directly...\n');

try {
  // Test the view structure
  const { data, error } = await supabase
    .from('v_b2f_pick_slim')
    .select('*')
    .limit(3);

  if (error) {
    console.error('❌ Error querying view:', error);
  } else {
    console.log('✅ View query successful');
    console.log('📊 Record count:', data.length);
    
    if (data.length > 0) {
      console.log('\n📋 First record structure:');
      console.log(JSON.stringify(data[0], null, 2));
      
      console.log('\n🔑 Available fields:');
      Object.keys(data[0]).forEach(key => {
        console.log(`   - ${key}: ${typeof data[0][key]} = ${data[0][key]}`);
      });
    }
  }
} catch (err) {
  console.error('❌ Exception:', err.message);
}
