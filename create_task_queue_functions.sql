-- Create get_pending_tasks_by_type function
CREATE OR REPLACE FUNCTION get_pending_tasks_by_type()
RETURNS TABLE(task_type text, count bigint) 
LANGUAGE sql
AS $$
  SELECT task_type, COUNT(*) as count
  FROM t_task_queue
  WHERE status = 'pending'
  AND scheduled_at <= NOW()
  GROUP BY task_type
  ORDER BY count DESC;
$$;

-- Create get_future_tasks_by_type function
CREATE OR REPLACE FUNCTION get_future_tasks_by_type()
RETURNS TABLE(task_type text, count bigint) 
LANGUAGE sql
AS $$
  SELECT task_type, COUNT(*) as count
  FROM t_task_queue
  WHERE status = 'pending'
  AND scheduled_at > NOW()
  GROUP BY task_type
  ORDER BY count DESC;
$$;

-- Create get_completed_tasks_by_type function
CREATE OR REPLACE FUNCTION get_completed_tasks_by_type()
RETURNS TABLE(task_type text, count bigint) 
LANGUAGE sql
AS $$
  SELECT task_type, COUNT(*) as count
  FROM t_task_queue
  WHERE status = 'complete'
  AND completed_at > NOW() - INTERVAL '30 minutes'
  GROUP BY task_type
  ORDER BY count DESC;
$$;

-- Create get_error_tasks_by_type function
CREATE OR REPLACE FUNCTION get_error_tasks_by_type()
RETURNS TABLE(task_type text, count bigint) 
LANGUAGE sql
AS $$
  SELECT task_type, COUNT(*) as count
  FROM t_task_queue
  WHERE status = 'error'
  AND completed_at > NOW() - INTERVAL '30 minutes'
  GROUP BY task_type
  ORDER BY count DESC;
$$;
