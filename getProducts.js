import fetch from 'node-fetch'; // Make sure node-fetch is installed via npm install node-fetch

const url = 'https://api.veeqo.com/sellables?filters%5Bsku_code%5D%5B%5D=%22Disc_52441%22';

const options = {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
    'x-api-key': 'Vqt/16c3acd642be598d3ca079590a8aae87'
  }
};

fetch(url, options)
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('Product List:', data);
  })
  .catch(err => {
    console.error('Error fetching product list:', err);
  });
