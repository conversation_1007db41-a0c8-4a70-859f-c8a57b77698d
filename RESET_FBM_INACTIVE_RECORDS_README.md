# Reset FBM Inactive Records

## Overview

This script resets FBM (Fulfilled by Merchant) records for inactive MPS products with zero inventory. It's designed to clean up FBM records that are no longer relevant and allow them to be re-processed through the normal workflow when they become available again.

## Files

- **`resetFbmInactiveRecords.js`** - Main script that performs the reset operation
- **Admin Interface** - New card added to the Amazon FBA tab in `admin.html`
- **API Endpoint** - `/api/reset-fbm-inactive-records` in `adminServer.js`

## What It Does

The script queries the `v_sdasins_fbm_inv0_mps_inactive` view to find FBM records that meet the following criteria:
- `fbm_uploaded_at` is not null (has been uploaded to FBM)
- `available_quantity` = 0 (zero inventory)
- `mps.active` = false (inactive MPS)

For each matching record, it updates the corresponding `t_sdasins` record with:
- `fbm_uploaded_at` = NULL
- `min_weight` = 1
- `max_weight` = 2

## Database View

The script uses the existing view `v_sdasins_fbm_inv0_mps_inactive`:

```sql
create view public.v_sdasins_fbm_inv0_mps_inactive as
select
  s.id as sdasin_id,
  s.asin,
  s.parent_asin,
  s.min_weight,
  s.max_weight,
  -- ... other fields
from
  t_sdasins s
  join t_inv_sdasin inv on s.id = inv.id
  join t_mps mps on s.mps_id = mps.id
where
  s.fbm_uploaded_at is not null
  and inv.available_quantity = 0
  and mps.active = false;
```

## Usage

### Command Line
```bash
node resetFbmInactiveRecords.js
```

### Admin Interface
1. Navigate to the **Amazon FBA** tab in the admin interface
2. Find the **"Reset FBM Inactive Records"** card
3. Click the **"🔄 Reset FBM Inactive Records"** button
4. View the results in the output area

### API Endpoint
```bash
POST /api/reset-fbm-inactive-records
Content-Type: application/json
```

## Expected Results

- Clears FBM upload timestamps for inactive products
- Standardizes weight ranges to default values (1-2)
- Allows products to be re-evaluated for FBM eligibility
- Provides count of records processed

## Safety

- The operation is **safe to run multiple times**
- Only records matching the view criteria will be updated
- No data is deleted, only reset to allow re-processing
- The script provides detailed feedback on what was changed

## Example Output

```
🚀 Running FBM inactive records reset script...
🔄 Starting FBM inactive records reset...
📊 Querying v_sdasins_fbm_inv0_mps_inactive view...
📋 Found 827 records in view
🎯 Updating 827 t_sdasins records...
✅ Successfully updated 827 t_sdasins records
📝 Changes made:
   - fbm_uploaded_at set to NULL
   - min_weight set to 1
   - max_weight set to 2

✅ Script completed successfully!
📊 Records found: 827
🔄 Records updated: 827
```

## Environment Variables

The script requires the following environment variables (from `.env`):
- `SUPABASE_URL` - Supabase project URL
- `SUPABASE_KEY` - Supabase service role key

## Error Handling

The script includes comprehensive error handling:
- Validates environment variables
- Checks for view existence
- Handles database connection issues
- Provides detailed error messages
- Returns structured results for API integration

## Integration

The script is fully integrated into the existing admin system:
- **Admin Interface**: New card in Amazon FBA tab
- **API Endpoint**: RESTful endpoint for programmatic access
- **Error Handling**: Consistent with other admin functions
- **Logging**: Detailed console output and structured results
