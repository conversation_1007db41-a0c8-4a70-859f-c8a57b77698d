import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import fs from 'fs';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

// Label dimensions in points (1 mm = 2.835 points)
const LABEL_WIDTH = 67 * 2.835;  // ~190.5 points
const LABEL_HEIGHT = 25 * 2.835; // ~70.87 points

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or key in environment variables.');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

// A simple text wrapper that splits a string into lines with a maximum number of characters.
function wrapText(text, maxChars) {
  const words = text.split(" ");
  const lines = [];
  let currentLine = "";
  for (const word of words) {
    const testLine = currentLine ? currentLine + " " + word : word;
    if (testLine.length <= maxChars) {
      currentLine = testLine;
    } else {
      if (currentLine) lines.push(currentLine);
      currentLine = word;
    }
  }
  if (currentLine) lines.push(currentLine);
  return lines;
}

async function generatePDF() {
  // 1. Query data from v_ready_disc_tags
  const { data: discTags, error } = await supabase
    .from('v_ready_disc_tags')
    .select('*');
  if (error) {
    console.error('Error querying v_ready_disc_tags:', error.message);
    process.exit(1);
  }
  if (!discTags || discTags.length === 0) {
    console.log('No records found in v_ready_disc_tags.');
    process.exit(0);
  }

  const pdfDoc = await PDFDocument.create();
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  // Font sizes and spacing
  const row1Size = 10; // Price & Speed
  const row2Size = 9;  // Title
  const row3Size = 8;  // ID & date
  const spacing = 2;   // vertical spacing between rows

  discTags.forEach((tag) => {
    const page = pdfDoc.addPage([LABEL_WIDTH, LABEL_HEIGHT]);

    // --- ROW 1: Price and Speed ---
    const row1Text = `${tag.price}  Speed: ${tag.speed}`;
    const row1Width = font.widthOfTextAtSize(row1Text, row1Size);

    // --- ROW 2: Title ---
    // Attempt a segmented approach if " - " is present.
    // Otherwise, just draw the entire title in one line or wrap.
    let segmented = false;
    let segmentedParts = [];
    let segmentedTotalWidth = 0;
    const separator = " - ";

    if (tag.title.includes(separator)) {
      const parts = tag.title.split(separator);
      // Expecting brand - plastic - mold - stamp - color - weight
      if (parts.length >= 6) {
        segmented = true;
        segmentedParts = parts;
        // Calculate total width (bold font for the 3rd part).
        segmentedParts.forEach((part, idx) => {
          const currentFont = (idx === 2) ? boldFont : font; // bold mold
          segmentedTotalWidth += currentFont.widthOfTextAtSize(part, row2Size);
          if (idx < segmentedParts.length - 1) {
            segmentedTotalWidth += font.widthOfTextAtSize(separator, row2Size);
          }
        });
      }
    }

    // If not segmented or too few segments, fallback to wrapping the title.
    let row2Lines = [];
    if (!segmented) {
      row2Lines = wrapText(tag.title, 40);
    }

    // --- ROW 3: ID & created_at in bold ---
    const createdDate = new Date(tag.created_at).toISOString().split('T')[0];
    const row3Text = `D${tag.id}    ${createdDate}`;
    const row3Width = boldFont.widthOfTextAtSize(row3Text, row3Size);

    // --- Calculate total block height ---
    let row2Height;
    if (segmented) {
      row2Height = row2Size; // single line
    } else {
      row2Height = row2Lines.length * row2Size + (row2Lines.length - 1) * spacing;
    }
    const totalHeight = row1Size + spacing + row2Height + spacing + row3Size;

    // Start Y so the block is vertically centered, then shift down ~3 points
    const startY = (LABEL_HEIGHT + totalHeight) / 2 - 3;

    // --- Draw Row 1 (centered) ---
    const row1X = (LABEL_WIDTH - row1Width) / 2;
    const row1Y = startY;
    page.drawText(row1Text, {
      x: row1X,
      y: row1Y,
      size: row1Size,
      font,
      color: rgb(0, 0, 0),
    });

    // --- Draw Row 2 ---
    let row2Y = row1Y - row1Size - spacing;
    if (segmented) {
      // Draw in one line, using bold for the 3rd part (mold).
      let currentX = (LABEL_WIDTH - segmentedTotalWidth) / 2;
      segmentedParts.forEach((part, idx) => {
        const currentFont = (idx === 2) ? boldFont : font;
        page.drawText(part, {
          x: currentX,
          y: row2Y,
          size: row2Size,
          font: currentFont,
          color: rgb(0, 0, 0),
        });
        currentX += currentFont.widthOfTextAtSize(part, row2Size);
        if (idx < segmentedParts.length - 1) {
          page.drawText(separator, {
            x: currentX,
            y: row2Y,
            size: row2Size,
            font,
            color: rgb(0, 0, 0),
          });
          currentX += font.widthOfTextAtSize(separator, row2Size);
        }
      });
    } else {
      // Wrap mode
      row2Lines.forEach((line) => {
        const lineWidth = font.widthOfTextAtSize(line, row2Size);
        const lineX = (LABEL_WIDTH - lineWidth) / 2;
        page.drawText(line, {
          x: lineX,
          y: row2Y,
          size: row2Size,
          font,
          color: rgb(0, 0, 0),
        });
        row2Y -= (row2Size + spacing);
      });
    }

    // --- Draw Row 3 in bold, centered ---
    const row3Y = row2Y - spacing;
    const row3X = (LABEL_WIDTH - row3Width) / 2;
    page.drawText(row3Text, {
      x: row3X,
      y: row3Y,
      size: row3Size,
      font: boldFont,
      color: rgb(0, 0, 0),
    });
  });

  // 2. Save PDF with date-stamped filename
  const pdfBytes = await pdfDoc.save();
  const now = new Date();
  const dateStamp = now.toISOString().replace(/:/g, "-").slice(0, 19);
  const fileName = `roll_labels_ready_disc_tags_${dateStamp}.pdf`;

  fs.writeFileSync(fileName, pdfBytes);
  console.log(`✅ Roll labels generated: ${fileName}`);

  // 3. Update t_discs.tag_printed_at for all IDs in discTags
  const discIds = discTags.map(d => d.id);
  if (discIds.length > 0) {
    const { error: updateError } = await supabase
      .from('t_discs')
      .update({ tag_printed_at: new Date().toISOString() })
      .in('id', discIds);

    if (updateError) {
      console.error(`❌ Failed to update tag_printed_at: ${updateError.message}`);
    } else {
      console.log(`✅ tag_printed_at updated for ${discIds.length} discs.`);
    }
  }
}

generatePDF();
