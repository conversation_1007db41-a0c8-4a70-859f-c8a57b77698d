const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize clients
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!supabaseUrl || !supabaseKey || !veeqoApiKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

console.log('🎯 TARGETED VEEQO LISTING DELETION TOOL');
console.log('='.repeat(50));

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqo<PERSON>piKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }
    
    // For DELETE requests, there might not be a JSON response
    if (method === 'DELETE') {
      return { success: true, data: null };
    }
    
    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Function to get product info from imported table
async function getProductFromImportedTable(sku) {
  console.log(`🔍 Looking up SKU ${sku} in imported Veeqo table...`);
  
  const { data, error } = await supabase
    .from('imported_table_veeqo_sellables_export')
    .select('product_id, sku_code, product_title')
    .eq('sku_code', sku)
    .limit(1);
  
  if (error) {
    console.error(`❌ Error querying imported table: ${error.message}`);
    return null;
  }
  
  if (!data || data.length === 0) {
    console.log(`❌ SKU ${sku} not found in imported table`);
    return null;
  }
  
  console.log(`✅ Found in imported table: Product ID ${data[0].product_id}`);
  return data[0];
}

// Function to get product details from Veeqo API
async function getProductFromVeeqoAPI(productId) {
  console.log(`📡 Getting product ${productId} from Veeqo API...`);
  
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!result.success) {
    if (result.status === 404) {
      console.log(`⚠️  Product ${productId} not found in Veeqo API (404) - might be deleted`);
      return { exists: false, reason: 'Product not found (404)' };
    } else {
      console.error(`❌ Error getting product: ${result.error}`);
      return { exists: false, error: result.error };
    }
  }
  
  const product = result.data;
  console.log(`✅ Product found: "${product.title}"`);
  
  return { exists: true, product };
}

// Function to find and delete channel products for a product
async function findAndDeleteChannelProducts(product, sku, dryRun = true) {
  console.log(`\n🔍 Analyzing channel products for product ${product.id}...`);
  
  const channelProducts = product.channel_products || [];
  
  if (channelProducts.length === 0) {
    console.log(`ℹ️  No channel products found for product ${product.id}`);
    return { success: true, reason: 'No channel products found' };
  }
  
  console.log(`📺 Found ${channelProducts.length} channel product(s):`);
  
  channelProducts.forEach((cp, index) => {
    console.log(`   ${index + 1}. Channel Product ID: ${cp.id}`);
    console.log(`      Channel: ${cp.channel?.name || 'Unknown'} (${cp.channel?.short_name || 'N/A'})`);
    console.log(`      Status: ${cp.status}`);
    console.log(`      Remote ID: ${cp.remote_id}`);
    console.log(`      Remote Title: ${cp.remote_title}`);
    
    if (cp.channel_sellables && cp.channel_sellables.length > 0) {
      console.log(`      Channel Sellables:`);
      cp.channel_sellables.forEach(cs => {
        console.log(`         - Remote SKU: ${cs.remote_sku}, Price: ${cs.remote_price}`);
      });
    }
  });
  
  if (dryRun) {
    console.log(`\n🔍 DRY RUN: Would delete ${channelProducts.length} channel product(s)`);
    return { success: true, reason: 'Dry run - no actual deletion', channelProductCount: channelProducts.length };
  }
  
  console.log(`\n🗑️  Deleting ${channelProducts.length} channel product(s)...`);
  
  let deletedCount = 0;
  let errors = [];
  
  for (const cp of channelProducts) {
    console.log(`🗑️  Deleting channel product ${cp.id} from ${cp.channel?.short_name || 'Unknown'}...`);
    
    const deleteResult = await makeVeeqoRequest(`https://api.veeqo.com/channel_products/${cp.id}`, 'DELETE');
    
    if (deleteResult.success) {
      console.log(`   ✅ Successfully deleted channel product ${cp.id}`);
      deletedCount++;
    } else {
      console.error(`   ❌ Failed to delete channel product ${cp.id}: ${deleteResult.error}`);
      errors.push(`Channel product ${cp.id}: ${deleteResult.error}`);
    }
    
    // Add delay between deletions
    if (channelProducts.length > 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  if (deletedCount === channelProducts.length) {
    console.log(`✅ Successfully deleted all ${deletedCount} channel products`);
    
    // Try to delete the entire product if all channel products are deleted
    console.log(`🗑️  Attempting to delete entire product ${product.id}...`);
    const productDeleteResult = await makeVeeqoRequest(`https://api.veeqo.com/products/${product.id}`, 'DELETE');
    
    if (productDeleteResult.success) {
      console.log(`✅ Successfully deleted entire product ${product.id}`);
      return { 
        success: true, 
        reason: 'All channel products and product deleted', 
        deletedChannelProducts: deletedCount,
        deletedProduct: true 
      };
    } else {
      console.log(`⚠️  Could not delete entire product: ${productDeleteResult.error}`);
      return { 
        success: true, 
        reason: 'Channel products deleted but product remains', 
        deletedChannelProducts: deletedCount,
        deletedProduct: false 
      };
    }
  } else {
    console.log(`⚠️  Partially successful: deleted ${deletedCount}/${channelProducts.length} channel products`);
    return { 
      success: false, 
      reason: 'Partial deletion', 
      deletedChannelProducts: deletedCount, 
      totalChannelProducts: channelProducts.length,
      errors 
    };
  }
}

// Function to process a single SKU
async function processSku(sku, dryRun = true) {
  console.log(`\n📋 Processing SKU: ${sku}`);
  console.log(`${'='.repeat(30)}`);
  
  // Step 1: Get product info from imported table
  const importedProduct = await getProductFromImportedTable(sku);
  if (!importedProduct) {
    return { success: false, reason: 'SKU not found in imported table' };
  }
  
  // Step 2: Get product details from Veeqo API
  const veeqoProduct = await getProductFromVeeqoAPI(importedProduct.product_id);
  if (!veeqoProduct.exists) {
    if (veeqoProduct.reason === 'Product not found (404)') {
      console.log(`ℹ️  Product already deleted from Veeqo, but still exists in imported table`);
      console.log(`💡 Consider cleaning up imported table record for product ID: ${importedProduct.product_id}`);
      return { success: true, reason: 'Product already deleted from Veeqo' };
    } else {
      return { success: false, error: veeqoProduct.error };
    }
  }
  
  // Step 3: Find and delete channel products
  return await findAndDeleteChannelProducts(veeqoProduct.product, sku, dryRun);
}

// Function to get SDASIN record
async function getSdasinRecord(sdasinId) {
  const { data, error } = await supabase
    .from('t_sdasins')
    .select('id, fbm_sku, fbm_uploaded_at, asin, parent_asin')
    .eq('id', sdasinId)
    .single();
  
  if (error) {
    console.error(`❌ Error fetching SDASIN record: ${error.message}`);
    return null;
  }
  
  return data;
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🛠️  USAGE:
  node findAndDeleteVeeqoListing.cjs <command> [options]

📋 COMMANDS:
  check-sku <sku>                - Check a specific SKU for Veeqo listings
  check-sdasin <sdasin_id>       - Check a specific SDASIN for Veeqo listings  
  delete-sku <sku>               - Delete Veeqo listings for a specific SKU
  delete-sdasin <sdasin_id>      - Delete Veeqo listings for a specific SDASIN

📝 EXAMPLES:
  node findAndDeleteVeeqoListing.cjs check-sku DISC_46948
  node findAndDeleteVeeqoListing.cjs check-sdasin 46948
  node findAndDeleteVeeqoListing.cjs delete-sku DISC_46948
  node findAndDeleteVeeqoListing.cjs delete-sdasin 46948

⚠️  WARNING: Deletion operations are permanent!
`);
    return;
  }
  
  const command = args[0];
  
  try {
    switch (command) {
      case 'check-sku':
        if (args.length < 2) {
          console.error('❌ Please provide a SKU');
          return;
        }
        const sku = args[1];
        await processSku(sku, true); // Dry run
        break;
        
      case 'check-sdasin':
        if (args.length < 2) {
          console.error('❌ Please provide a SDASIN ID');
          return;
        }
        const sdasinId = parseInt(args[1]);
        const sdasin = await getSdasinRecord(sdasinId);
        
        if (!sdasin) {
          console.error('❌ SDASIN not found');
          return;
        }
        
        console.log(`📋 SDASIN ${sdasin.id}: ${sdasin.fbm_sku} (fbm_uploaded_at: ${sdasin.fbm_uploaded_at})`);
        
        if (sdasin.fbm_sku) {
          await processSku(sdasin.fbm_sku, true); // Dry run
        }
        break;
        
      case 'delete-sku':
        if (args.length < 2) {
          console.error('❌ Please provide a SKU');
          return;
        }
        const skuToDelete = args[1];
        await processSku(skuToDelete, false); // Actually delete
        break;
        
      case 'delete-sdasin':
        if (args.length < 2) {
          console.error('❌ Please provide a SDASIN ID');
          return;
        }
        const sdasinIdToDelete = parseInt(args[1]);
        const sdasinToDelete = await getSdasinRecord(sdasinIdToDelete);
        
        if (!sdasinToDelete) {
          console.error('❌ SDASIN not found');
          return;
        }
        
        console.log(`📋 SDASIN ${sdasinToDelete.id}: ${sdasinToDelete.fbm_sku} (fbm_uploaded_at: ${sdasinToDelete.fbm_uploaded_at})`);
        
        if (sdasinToDelete.fbm_uploaded_at !== null) {
          console.log(`⚠️  WARNING: SDASIN ${sdasinToDelete.id} still has fbm_uploaded_at set!`);
          console.log(`   This means the Amazon listing might still be active.`);
          console.log(`   Are you sure you want to delete the Veeqo listing?`);
        }
        
        if (sdasinToDelete.fbm_sku) {
          await processSku(sdasinToDelete.fbm_sku, false); // Actually delete
        }
        break;
        
      default:
        console.error(`❌ Unknown command: ${command}`);
        console.log('Use no arguments to see usage instructions.');
    }
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the main function
main();
