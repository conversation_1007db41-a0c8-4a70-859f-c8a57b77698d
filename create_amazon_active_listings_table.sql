-- Create table for Amazon Active Listings Report
-- This is a snapshot table that gets truncated and replaced on each import

CREATE TABLE IF NOT EXISTS public.it_amaz_active_listings_report (
  id SERIAL PRIMARY KEY,
  item_name TEXT,
  item_description TEXT,
  listing_id TEXT,
  seller_sku TEXT,
  price NUMERIC(10,2),
  quantity INTEGER,
  open_date TIMESTAMPTZ,
  image_url TEXT,
  item_is_marketplace TEXT,
  product_id_type TEXT,
  zshop_shipping_fee NUMERIC(10,2),
  item_note TEXT,
  item_condition TEXT,
  zshop_category1 TEXT,
  zshop_browse_path TEXT,
  zshop_storefront_feature TEXT,
  asin1 TEXT,
  asin2 TEXT,
  asin3 TEXT,
  will_ship_internationally TEXT,
  expedited_shipping TEXT,
  zshop_boldface TEXT,
  product_id TEXT,
  bid_for_featured_placement TEXT,
  add_delete TEXT,
  pending_quantity INTEGER,
  fulfillment_channel TEXT,
  business_price NUMERIC(10,2),
  quantity_price_type TEXT,
  quantity_lower_bound_1 INTEGER,
  quantity_price_1 NUMERIC(10,2),
  quantity_lower_bound_2 INTEGER,
  quantity_price_2 NUMERIC(10,2),
  quantity_lower_bound_3 INTEGER,
  quantity_price_3 NUMERIC(10,2),
  quantity_lower_bound_4 INTEGER,
  quantity_price_4 NUMERIC(10,2),
  quantity_lower_bound_5 INTEGER,
  quantity_price_5 NUMERIC(10,2),
  merchant_shipping_group TEXT,
  progressive_price_type TEXT,
  progressive_lower_bound_1 INTEGER,
  progressive_price_1 NUMERIC(10,2),
  progressive_lower_bound_2 INTEGER,
  progressive_price_2 NUMERIC(10,2),
  progressive_lower_bound_3 INTEGER,
  progressive_price_3 NUMERIC(10,2),
  
  -- Additional tracking fields
  report_date DATE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  import_batch_id UUID DEFAULT gen_random_uuid()
);

-- Create indexes for common search fields
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_seller_sku ON public.it_amaz_active_listings_report(seller_sku);
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_asin1 ON public.it_amaz_active_listings_report(asin1);
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_product_id ON public.it_amaz_active_listings_report(product_id);
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_listing_id ON public.it_amaz_active_listings_report(listing_id);
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_report_date ON public.it_amaz_active_listings_report(report_date);
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_import_batch ON public.it_amaz_active_listings_report(import_batch_id);
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_fulfillment_channel ON public.it_amaz_active_listings_report(fulfillment_channel);

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_timestamp_amaz_active_listings
BEFORE UPDATE ON public.it_amaz_active_listings_report
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Add comments for documentation
COMMENT ON TABLE public.it_amaz_active_listings_report IS 'Amazon Active Listings Report - snapshot data that gets truncated and replaced on each import';
COMMENT ON COLUMN public.it_amaz_active_listings_report.seller_sku IS 'Your SKU for the item';
COMMENT ON COLUMN public.it_amaz_active_listings_report.asin1 IS 'Primary Amazon Standard Identification Number';
COMMENT ON COLUMN public.it_amaz_active_listings_report.product_id IS 'Product identifier';
COMMENT ON COLUMN public.it_amaz_active_listings_report.listing_id IS 'Amazon listing identifier';
COMMENT ON COLUMN public.it_amaz_active_listings_report.fulfillment_channel IS 'Fulfillment method (DEFAULT, AMAZON_NA, etc.)';
COMMENT ON COLUMN public.it_amaz_active_listings_report.report_date IS 'Date of the report file that was imported';
COMMENT ON COLUMN public.it_amaz_active_listings_report.import_batch_id IS 'UUID to track which import batch this record belongs to';

-- Create function to truncate the table (for use by the import process)
CREATE OR REPLACE FUNCTION truncate_it_amaz_active_listings_report()
RETURNS void AS $$
BEGIN
  TRUNCATE TABLE public.it_amaz_active_listings_report RESTART IDENTITY;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT ALL ON public.it_amaz_active_listings_report TO postgres;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.it_amaz_active_listings_report TO anon;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.it_amaz_active_listings_report TO authenticated;
GRANT USAGE ON SEQUENCE public.it_amaz_active_listings_report_id_seq TO anon;
GRANT USAGE ON SEQUENCE public.it_amaz_active_listings_report_id_seq TO authenticated;
