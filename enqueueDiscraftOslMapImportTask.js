// enqueueDiscraftOslMapImportTask.js - Enqueue a Discraft OSL map import task

import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase environment variables');
    console.error('Please ensure SUPABASE_URL and SUPABASE_KEY are set in your .env file');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function enqueueDiscraftOslMapImportTask() {
    console.log('📋 Enqueueing Discraft OSL Map Import Task...');
    console.log('==============================================');

    try {
        // Create the task
        const taskData = {
            task_type: 'import_discraft_osl_map_and_status',
            payload: {}, // Empty payload since we're using hardcoded URLs
            status: 'pending',
            scheduled_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
            enqueued_by: 'manual_enqueue_script'
        };

        console.log('📝 Task data:', JSON.stringify(taskData, null, 2));

        const { data: task, error } = await supabase
            .from('t_task_queue')
            .insert([taskData])
            .select()
            .single();

        if (error) {
            throw new Error(`Failed to enqueue task: ${error.message}`);
        }

        console.log('✅ Task enqueued successfully!');
        console.log('📋 Task details:');
        console.table([task]);

        console.log('\n🔍 You can monitor the task progress by:');
        console.log('1. Checking the task queue worker logs');
        console.log('2. Querying the t_task_queue table for task ID:', task.id);
        console.log('3. Checking the it_discraft_osl_map table for imported data');

        return task;

    } catch (error) {
        console.error('❌ Failed to enqueue task:', error);
        throw error;
    }
}

// Run the enqueue function
enqueueDiscraftOslMapImportTask()
    .then((task) => {
        console.log('\n🎉 Enqueue script completed successfully');
        console.log(`📋 Task ID: ${task.id}`);
        process.exit(0);
    })
    .catch(error => {
        console.error('💥 Enqueue script failed:', error);
        process.exit(1);
    });
