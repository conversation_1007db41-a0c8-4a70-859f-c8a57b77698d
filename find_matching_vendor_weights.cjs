require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function findMatchingVendorWeights() {
  try {
    console.log('Looking for discs where manufacturer weight would match OSL ranges...');
    
    // Get all discs that need vendor_osl_id
    const { data: discs, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null)
      .limit(50);
    
    if (discError) {
      console.error('Error getting discs:', discError);
      return;
    }
    
    console.log(`Checking ${discs.length} discs...`);
    
    let foundMatches = 0;
    let checkedCount = 0;
    
    for (const disc of discs) {
      checkedCount++;
      
      // Test the vendor OSL function
      const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
        'find_matching_osl_by_mfg_weight',
        {
          mps_id_param: disc.mps_id,
          color_id_param: disc.color_id,
          weight_mfg_param: disc.weight_mfg
        }
      );
      
      if (!vendorOslError && vendorOslData && vendorOslData.length > 0) {
        foundMatches++;
        const vendorOslId = vendorOslData[0].osl_id;
        
        console.log(`✅ MATCH FOUND - Disc ${disc.id}:`);
        console.log(`  Weight: ${disc.weight}g, Weight MFG: ${disc.weight_mfg}g`);
        console.log(`  Regular OSL: ${disc.order_sheet_line_id}, Vendor OSL: ${vendorOslId}`);
        
        if (vendorOslId !== disc.order_sheet_line_id) {
          console.log(`  🎯 DIFFERENT MAPPINGS! This is exactly what we want.`);
        }
        
        // Update this disc
        const { error: updateError } = await supabase
          .from('t_discs')
          .update({ vendor_osl_id: vendorOslId })
          .eq('id', disc.id);
        
        if (updateError) {
          console.error(`  Error updating disc ${disc.id}:`, updateError);
        } else {
          console.log(`  ✅ Updated disc ${disc.id} with vendor_osl_id: ${vendorOslId}`);
        }
      }
      
      if (checkedCount % 10 === 0) {
        console.log(`Checked ${checkedCount}/${discs.length} discs, found ${foundMatches} matches so far...`);
      }
    }
    
    console.log('\n=== SUMMARY ===');
    console.log(`Total discs checked: ${checkedCount}`);
    console.log(`Matches found and updated: ${foundMatches}`);
    console.log(`Success rate: ${((foundMatches / checkedCount) * 100).toFixed(1)}%`);
    
    if (foundMatches === 0) {
      console.log('\n🤔 No matches found. This could mean:');
      console.log('1. Manufacturer weights are consistently outside OSL weight ranges');
      console.log('2. The weight ranges in OSLs are too narrow');
      console.log('3. There might be a systematic difference between mfg and actual weights');
      
      // Let's analyze the weight differences
      console.log('\nAnalyzing weight differences...');
      const weightDiffs = discs.map(d => ({
        id: d.id,
        actual: d.weight,
        mfg: d.weight_mfg,
        diff: d.weight - d.weight_mfg
      }));
      
      const avgDiff = weightDiffs.reduce((sum, d) => sum + d.diff, 0) / weightDiffs.length;
      const minDiff = Math.min(...weightDiffs.map(d => d.diff));
      const maxDiff = Math.max(...weightDiffs.map(d => d.diff));
      
      console.log(`Average weight difference (actual - mfg): ${avgDiff.toFixed(2)}g`);
      console.log(`Min difference: ${minDiff.toFixed(2)}g`);
      console.log(`Max difference: ${maxDiff.toFixed(2)}g`);
      
      console.log('\nSample weight differences:');
      weightDiffs.slice(0, 10).forEach(d => {
        console.log(`  Disc ${d.id}: actual ${d.actual}g, mfg ${d.mfg}g, diff ${d.diff.toFixed(2)}g`);
      });
    } else {
      console.log(`\n✅ Successfully updated ${foundMatches} discs with vendor_osl_id!`);
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

findMatchingVendorWeights();
