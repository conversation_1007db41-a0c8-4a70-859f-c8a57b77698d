// imageApi.js - REST API for accessing images and folders
import express from 'express';
import cors from 'cors';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import {
  listDirectory,
  getImageMetadata,
  validatePath,
  isImage,
  createDirectory
} from './imageUtils.js';

// Load environment variables
dotenv.config();

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Base directory for images - this is the directory we want to expose
const BASE_IMAGE_DIR = 'N:\\Images\\DG Discs\\Uploaded to AWS';

// Create Express app
const app = express();

// Enable CORS for all routes
app.use(cors());

// Parse JSON request bodies
app.use(express.json());

// Middleware to log all requests
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  next();
});

// Serve static files from the image directory with proper MIME types
app.use('/static', express.static(BASE_IMAGE_DIR, {
  setHeaders: (res, filePath) => {
    if (isImage(filePath)) {
      const ext = path.extname(filePath).toLowerCase();
      switch (ext) {
        case '.jpg':
        case '.jpeg':
          res.setHeader('Content-Type', 'image/jpeg');
          break;
        case '.png':
          res.setHeader('Content-Type', 'image/png');
          break;
        case '.gif':
          res.setHeader('Content-Type', 'image/gif');
          break;
        case '.webp':
          res.setHeader('Content-Type', 'image/webp');
          break;
        default:
          res.setHeader('Content-Type', 'application/octet-stream');
      }
    }
  }
}));

// API endpoint to list directory contents
app.get('/api/list', async (req, res) => {
  try {
    const relativePath = req.query.path || '';

    // Validate the path to prevent directory traversal attacks
    if (!validatePath(relativePath)) {
      return res.status(400).json({
        error: 'Invalid path. Path cannot contain ".." or other potentially unsafe characters.'
      });
    }

    // Parse pagination, sorting, and filtering options
    const options = {
      page: parseInt(req.query.page) || 1,
      pageSize: parseInt(req.query.pageSize) || 50,
      sort: ['name', 'date', 'size'].includes(req.query.sort) ? req.query.sort : 'name',
      order: ['asc', 'desc'].includes(req.query.order) ? req.query.order : 'asc',
      latestOnly: req.query.latestOnly === 'true',
      latestCount: parseInt(req.query.latestCount) || 20
    };

    const fullPath = path.join(BASE_IMAGE_DIR, relativePath);
    const result = await listDirectory(fullPath, relativePath, options);

    res.json({
      path: relativePath,
      ...result
    });
  } catch (error) {
    console.error(`Error listing directory: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// API endpoint to get image metadata
app.get('/api/metadata', async (req, res) => {
  try {
    const relativePath = req.query.path || '';

    // Validate the path to prevent directory traversal attacks
    if (!validatePath(relativePath)) {
      return res.status(400).json({
        error: 'Invalid path. Path cannot contain ".." or other potentially unsafe characters.'
      });
    }

    const fullPath = path.join(BASE_IMAGE_DIR, relativePath);
    const metadata = await getImageMetadata(fullPath);

    res.json({
      path: relativePath,
      metadata
    });
  } catch (error) {
    console.error(`Error getting metadata: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// API endpoint to create a new directory
app.post('/api/directory', async (req, res) => {
  try {
    const { path: relativePath = '', name } = req.body;

    // Validate the path to prevent directory traversal attacks
    if (!validatePath(relativePath)) {
      return res.status(400).json({
        error: 'Invalid path. Path cannot contain ".." or other potentially unsafe characters.'
      });
    }

    // Validate folder name
    if (!name || typeof name !== 'string' || name.trim() === '') {
      return res.status(400).json({ error: 'Folder name is required' });
    }

    const result = await createDirectory(BASE_IMAGE_DIR, relativePath, name);

    if (result.success) {
      res.status(201).json(result);
    } else {
      res.status(409).json(result); // 409 Conflict for already existing folder
    }
  } catch (error) {
    console.error(`Error creating directory: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Root endpoint with API documentation
app.get('/', (req, res) => {
  res.json({
    name: 'Image API',
    description: 'REST API for accessing images and folders',
    version: '1.0.0',
    endpoints: [
      {
        path: '/api/list',
        method: 'GET',
        description: 'List directory contents with pagination, sorting, and filtering',
        params: [
          { name: 'path', type: 'string', description: 'Relative path to list (optional)' },
          { name: 'page', type: 'number', description: 'Page number (1-based, default: 1)' },
          { name: 'pageSize', type: 'number', description: 'Number of items per page (default: 50)' },
          { name: 'sort', type: 'string', description: 'Sort field (name, date, size, default: name)' },
          { name: 'order', type: 'string', description: 'Sort order (asc, desc, default: asc)' },
          { name: 'latestOnly', type: 'boolean', description: 'Show only the latest files (default: false)' },
          { name: 'latestCount', type: 'number', description: 'Number of latest files to show (default: 20)' }
        ]
      },
      {
        path: '/api/metadata',
        method: 'GET',
        description: 'Get metadata for a file or directory',
        params: [
          { name: 'path', type: 'string', description: 'Relative path to the file or directory (optional)' }
        ]
      },
      {
        path: '/api/directory',
        method: 'POST',
        description: 'Create a new directory',
        body: {
          path: { type: 'string', description: 'Relative path where to create the directory (optional)' },
          name: { type: 'string', description: 'Name of the directory to create (required)' }
        }
      },
      {
        path: '/static/:path',
        method: 'GET',
        description: 'Get a static file (image)',
        params: [
          { name: 'path', type: 'string', description: 'Relative path to the file' }
        ]
      }
    ]
  });
});

// Start server
const PORT = process.env.IMAGE_API_PORT || 3005;
app.listen(PORT, '0.0.0.0', () => {
  console.log(`[imageApi] Server running on port ${PORT} (LAN accessible)`);
  console.log(`[imageApi] Base image directory: ${BASE_IMAGE_DIR}`);
  console.log(`[imageApi] API documentation: http://localhost:${PORT}/`);
  console.log(`[imageApi] Static files: http://localhost:${PORT}/static/`);
});
