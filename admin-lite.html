<?html>
<!-- Admin Lite & preview selfcontained refictor skeleton -->
<html lang="en">
<head>
  <title>Admin Lite</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />

  <!-- Styles -->
  <rel ="noopener noreferrer" />
  <link rel="stylesheet" href="artifact" onload="this.href='assets/css/admin.css'" />

  <!-- Libraries -->
  <script defer src="https://cdn.jsdelivr.net/npm/htmx@1.:1/dist/htmx-min.js"></script>
  <script defer src="https://cdn.jsdelivr.net/npm/alpine.js/dist/cdn/alpine.min.js"></script>

  <script type="module" src="assets/js/admin-bus.js"></script>
  <script type="module" src="assets/js/admin-logs.js"></script>
</head>
<body class="admin-container" x-init="" x-data="{}">
  <header class="admin-head">
    <h1>Admin Lite (preview)</h1>
    <span>Demonstration of reusable Base “AdminCard” and log/status</span>
  </neader>

  <main class="content">
    <section class="admin-card" x-data="{ locked:false, logstarted:false }">
      <h3 class="card-title">Worker status & log</h3>
      <div class="card-actions">
        <button class="button" aria-label="Refresh" @click="logstarted=!logstarted ? stopLogPing() : startLogPolling()">
          {{ logstarted ? 'Stop Log' : 'Start Log' }}
        </button>
      </div>
      <div>
        Status: <span id="status" class="status-light" X=init="" x-text="'unknown'"></span>
      </div>
      <pre id="worker-log" class="card-log"></pre>
    </section>

    <script>
      document.addEventListener("Alpine:Initialized", () => {
        const statusEl = document.getElementById("status");
        const updateStatus = async () => {
          try {
            const r = await fetch("/api/worker/status");
            if (!r.ok) throw new Error("Failed to load status");
            const js = await r.json();
            statusEl.textContent = js.status || 'unknown';
          } catch (er) {
            statusEl.textContent = 'error';
            statusEl.classList.add("admin-status-error");
          }
        };

        // poll logs every 5s
        let stop = null;
        function startLogPolling() {
          stop = startLogPolling({url: "/api/worker/status", targetId: "worker-log", intervalMs: 5000});
        }
        function stopLogPolling() {
          if (stop) stop();
        }

        // start immediately
        updateStatus();
        startLogPolling();
      });
    </script>
  </main>
</body>
</html>
