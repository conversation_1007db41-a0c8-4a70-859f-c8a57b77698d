import { createClient } from '@supabase/supabase-js';
import XLSX from 'xlsx';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testDiscraftExport() {
  try {
    console.log('🧪 Testing Discraft export logic...\n');

    // 1. Test if we can query the view
    console.log('1. Testing view query...');
    const { data: orderData, error: orderError } = await supabase
      .from('v_stats_by_osl_discraft')
      .select('excel_mapping_key, excel_column, "order"')
      .not('"order"', 'is', null)
      .gt('"order"', 0)
      .limit(5);

    if (orderError) {
      console.error('❌ Error querying view:', orderError);
      return;
    }

    console.log(`✅ Found ${orderData.length} records with order quantities`);
    if (orderData.length > 0) {
      console.log('Sample records:');
      orderData.forEach((record, index) => {
        console.log(`  ${index + 1}. Key: "${record.excel_mapping_key}", Column: ${record.excel_column}, Order: ${record.order}`);
      });
    }

    // 2. Test Excel file reading
    console.log('\n2. Testing Excel file reading...');
    const inputFile = path.join(__dirname, 'data', 'external data', 'discraftstock.xlsx');
    
    if (!fs.existsSync(inputFile)) {
      console.error('❌ Input file not found:', inputFile);
      return;
    }

    const workbook = XLSX.readFile(inputFile);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    console.log(`✅ Excel file loaded: ${data.length} rows`);

    // 3. Test the column mapping logic
    console.log('\n3. Testing column mapping logic...');
    const columnLetterToIndex = {
      'L': 11,     // 150g
      'M': 12,     // 160-166g  
      'N': 13,     // 167-169g
      'O': 14,     // 170-172g
      'P': 15,     // 173-174g
      'Q': 16,     // 175-176g
      'R': 17,     // 177+g
      'ASSORTED': 11  // Default to first weight column for assorted
    };

    // Create order map with columns
    const orderMapWithColumns = {};
    orderData.forEach(row => {
      if (row.excel_mapping_key && row.excel_column) {
        orderMapWithColumns[row.excel_mapping_key] = {
          order: row.order,
          excel_column: row.excel_column
        };
      }
    });

    console.log(`✅ Created order map with ${Object.keys(orderMapWithColumns).length} entries`);

    // 4. Test matching logic on a few rows
    console.log('\n4. Testing matching logic...');
    let matchesFound = 0;
    let rowsProcessed = 0;

    for (let rowIndex = 0; rowIndex < Math.min(100, data.length); rowIndex++) {
      const row = data[rowIndex];
      if (!row || row.length < 3) continue;

      rowsProcessed++;

      const line = row[0]?.toString().trim() || '';
      const model = row[3]?.toString().trim() || '';

      // Find weight range
      let weightRange = '';
      const weightMappings = {
        11: '150g',
        12: '160-166g',
        13: '167-169g',
        14: '170-172g',
        15: '173-174g',
        16: '175-176g',
        17: '177+g'
      };

      for (let col = 11; col <= 17; col++) {
        const cellValue = row[col]?.toString().trim() || '';
        if (cellValue && cellValue !== '' && cellValue !== 'N/A') {
          weightRange = weightMappings[col];
          break;
        }
      }

      if (line && model && weightRange) {
        const excelMappingKey = `${line}|${model}|${weightRange}`;
        
        if (orderMapWithColumns[excelMappingKey]) {
          const orderInfo = orderMapWithColumns[excelMappingKey];
          const targetColumnIndex = columnLetterToIndex[orderInfo.excel_column] || 11;
          
          console.log(`  ✅ Match found: "${excelMappingKey}" -> Column ${orderInfo.excel_column} (index ${targetColumnIndex}), Order: ${orderInfo.order}`);
          matchesFound++;
        }
      }
    }

    console.log(`\n📊 Results:`);
    console.log(`   • Rows processed: ${rowsProcessed}`);
    console.log(`   • Matches found: ${matchesFound}`);
    console.log(`   • Match rate: ${rowsProcessed > 0 ? ((matchesFound / rowsProcessed) * 100).toFixed(1) : 0}%`);

    if (matchesFound > 0) {
      console.log('\n✅ Export logic appears to be working correctly!');
      console.log('The updated export function should properly place order quantities in the correct weight columns.');
    } else {
      console.log('\n⚠️ No matches found. This could mean:');
      console.log('   • No order quantities > 0 in the view');
      console.log('   • Excel mapping keys don\'t match between import and export');
      console.log('   • Excel file structure has changed');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testDiscraftExport();
