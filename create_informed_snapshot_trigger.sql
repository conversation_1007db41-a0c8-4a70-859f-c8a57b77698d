-- Create trigger to automatically generate Informed snapshots after data imports
-- This trigger fires after data is imported into it_infor_all_fields

-- First, create a function to handle the trigger
CREATE OR REPLACE FUNCTION public.fn_trigger_informed_snapshot()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
    v_record_count INTEGER;
    v_recent_snapshots INTEGER;
    v_snapshot_result RECORD;
BEGIN
    -- Only create snapshot if this is a significant data change (bulk import)
    SELECT COUNT(*) INTO v_record_count FROM public.it_infor_all_fields;

    -- Avoid creating multiple snapshots within a short window (batch inserts)
    SELECT COUNT(*) INTO v_recent_snapshots
    FROM public.rpt_informed
    WHERE snapshot_date > NOW() - INTERVAL '5 minutes';

    IF v_record_count >= 100 AND v_recent_snapshots = 0 THEN
        RAISE NOTICE 'Informed import trigger fired - creating snapshot for % records', v_record_count;

        -- Create the snapshot with automatic note
        SELECT * INTO v_snapshot_result
        FROM public.fn_create_informed_snapshot(
            'Auto-generated after import of ' || v_record_count || ' records'
        ) LIMIT 1;

        RAISE NOTICE 'Auto-created Informed snapshot ID: %', v_snapshot_result.snapshot_id;
    ELSE
        RAISE NOTICE 'Skipping snapshot creation - records: %, recent snapshots in last 5 min: %', v_record_count, v_recent_snapshots;
    END IF;

    RETURN NULL; -- For AFTER trigger
END;
$$;

-- Create the trigger on it_infor_all_fields
-- This trigger fires after INSERT operations (bulk imports)
DROP TRIGGER IF EXISTS tr_informed_snapshot_after_import ON public.it_infor_all_fields;

CREATE TRIGGER tr_informed_snapshot_after_import
    AFTER INSERT ON public.it_infor_all_fields
    FOR EACH STATEMENT -- Statement-level trigger (fires once per import, not per row)
    EXECUTE FUNCTION public.fn_trigger_informed_snapshot();

-- Add comments for documentation
COMMENT ON FUNCTION public.fn_trigger_informed_snapshot() IS 'Trigger function to automatically create Informed snapshots after data imports';
COMMENT ON TRIGGER tr_informed_snapshot_after_import ON public.it_infor_all_fields IS 'Automatically creates Informed snapshot after bulk data imports';
