import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Shopify GraphQL Admin API credentials
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

if (!shopifyEndpoint || !shopifyAccessToken) {
  console.error('ERROR: Missing Shopify endpoint or access token in environment variables.');
  process.exit(1);
}

/**
 * Execute a GraphQL request to Shopify
 * @param {string} query - The GraphQL query
 * @param {Object} variables - Variables for the GraphQL query
 * @returns {Promise<Object>} - The response data
 */
async function executeShopifyGraphQL(query, variables = {}) {
  try {
    const response = await fetch(shopifyEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      },
      body: JSON.stringify({ query, variables })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    if (result.errors) {
      throw new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`);
    }

    return result.data;
  } catch (error) {
    console.error('Error executing Shopify GraphQL request:', error);
    throw error;
  }
}

/**
 * Find products with handles ending in d######-1 or d######-2 pattern
 * @returns {Promise<Array>} - Array of duplicate products
 */
async function findDuplicateProducts() {
  console.log('Searching for duplicate Shopify products...');
  
  const duplicates = [];
  let hasNextPage = true;
  let cursor = null;
  let totalProducts = 0;
  let duplicateCount = 0;

  // Regular expression to match handles ending in d######-1 or d######-2
  const duplicatePattern = /d\d{6}-[12]$/;

  while (hasNextPage) {
    const query = `
      query getProducts($first: Int!, $after: String) {
        products(first: $first, after: $after) {
          edges {
            node {
              id
              title
              handle
              status
              createdAt
              updatedAt
            }
            cursor
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }
    `;

    const variables = {
      first: 250, // Maximum allowed by Shopify
      after: cursor
    };

    try {
      const data = await executeShopifyGraphQL(query, variables);
      const products = data.products.edges;
      
      totalProducts += products.length;
      console.log(`Processed ${totalProducts} products so far...`);

      // Check each product for duplicate pattern
      for (const edge of products) {
        const product = edge.node;
        
        if (duplicatePattern.test(product.handle)) {
          duplicates.push({
            id: product.id.replace('gid://shopify/Product/', ''), // Remove GraphQL prefix
            title: product.title,
            handle: product.handle,
            status: product.status,
            created_at: product.createdAt,
            updated_at: product.updatedAt
          });
          duplicateCount++;
          console.log(`Found duplicate: ${product.handle} (${product.title})`);
        }
      }

      // Update pagination
      hasNextPage = data.products.pageInfo.hasNextPage;
      cursor = data.products.pageInfo.endCursor;

      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
      console.error('Error fetching products:', error);
      throw error;
    }
  }

  console.log(`\nSearch completed!`);
  console.log(`Total products scanned: ${totalProducts}`);
  console.log(`Duplicate products found: ${duplicateCount}`);

  return duplicates;
}

/**
 * Main function to find duplicate products
 */
async function main() {
  try {
    const duplicates = await findDuplicateProducts();
    
    if (duplicates.length > 0) {
      console.log('\nDuplicate products found:');
      duplicates.forEach((product, index) => {
        console.log(`${index + 1}. ${product.handle} - ${product.title}`);
      });
    } else {
      console.log('\nNo duplicate products found.');
    }

    return {
      success: true,
      duplicates: duplicates,
      totalFound: duplicates.length
    };
  } catch (error) {
    console.error('Error in main function:', error);
    return {
      success: false,
      error: error.message,
      duplicates: []
    };
  }
}

// Export the main function for use in other modules
export default main;

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().then(result => {
    console.log('\nResult:', JSON.stringify(result, null, 2));
    process.exit(result.success ? 0 : 1);
  });
}
