/**
 * Test script for MPS override price change task processors
 * 
 * This script demonstrates how to enqueue and test the new MPS override task type:
 * - mps_override_prices_changed_so_update_shopify
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Test enqueueing an mps_override_prices_changed_so_update_shopify task
 * @param {number} mpsId - The MPS ID to test with
 * @param {boolean} testRetailChange - Whether to test retail price change
 * @param {boolean} testMsrpChange - Whether to test MSRP change
 */
async function testMpsOverridePriceChangeTask(mpsId, testRetailChange = true, testMsrpChange = true) {
  console.log(`\n🧪 Testing mps_override_prices_changed_so_update_shopify task for MPS ID: ${mpsId}`);
  console.log(`📋 Testing retail change: ${testRetailChange}, MSRP change: ${testMsrpChange}`);
  
  try {
    // First, let's check what MPS we're working with
    const { data: mps, error: mpsError } = await supabase
      .from('t_mps')
      .select(`
        id,
        val_override_retail_price,
        val_override_msrp,
        t_molds!inner (mold),
        t_plastics!inner (plastic, val_retail_price, val_msrp),
        t_stamps!inner (stamp)
      `)
      .eq('id', mpsId)
      .single();

    if (mpsError) {
      console.error('❌ Error fetching MPS:', mpsError.message);
      return;
    }

    if (!mps) {
      console.error(`❌ MPS with ID ${mpsId} not found`);
      return;
    }

    const mpsDescription = `${mps.t_molds.mold} ${mps.t_plastics.plastic} ${mps.t_stamps.stamp}`;
    console.log(`📋 MPS: ${mpsDescription}`);
    console.log(`📋 Current override retail price: ${mps.val_override_retail_price ? '$' + mps.val_override_retail_price : 'None (uses plastic: $' + mps.t_plastics.val_retail_price + ')'}`);
    console.log(`📋 Current override MSRP: ${mps.val_override_msrp ? '$' + mps.val_override_msrp : 'None (uses plastic: $' + mps.t_plastics.val_msrp + ')'}`);

    // Check how many discs would be affected
    const { data: affectedDiscs, error: discsError } = await supabase
      .from('t_discs')
      .select('id, mps_id')
      .is('sold_date', null)
      .not('shopify_uploaded_at', 'is', null)
      .eq('mps_id', mpsId);

    if (discsError) {
      console.error('❌ Error fetching affected discs:', discsError.message);
      return;
    }

    console.log(`📊 Found ${affectedDiscs.length} discs that would be affected by this MPS override change`);

    if (affectedDiscs.length > 0) {
      console.log(`📝 Sample affected disc IDs: ${affectedDiscs.slice(0, 5).map(d => d.id).join(', ')}${affectedDiscs.length > 5 ? '...' : ''}`);
    }

    // Also check how many OSLs would be affected
    const { data: affectedOsls, error: oslsError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id')
      .not('shopify_uploaded_at', 'is', null)
      .eq('mps_id', mpsId);

    if (oslsError) {
      console.error('❌ Error fetching affected OSLs:', oslsError.message);
      return;
    }

    console.log(`📊 Found ${affectedOsls.length} OSLs that would be affected by this MPS override change`);

    if (affectedOsls.length > 0) {
      console.log(`📝 Sample affected OSL IDs: ${affectedOsls.slice(0, 5).map(o => o.id).join(', ')}${affectedOsls.length > 5 ? '...' : ''}`);
    }

    // Enqueue the task
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'mps_override_prices_changed_so_update_shopify',
        payload: { 
          id: mpsId,
          retail_price_changed: testRetailChange,
          msrp_price_changed: testMsrpChange,
          mps_description: mpsDescription
        },
        status: 'pending',
        scheduled_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        enqueued_by: 'testMpsOverridePriceChangeTasks.js'
      })
      .select()
      .single();

    if (taskError) {
      console.error('❌ Error enqueueing task:', taskError.message);
      return;
    }

    console.log(`✅ Successfully enqueued mps_override_prices_changed_so_update_shopify task with ID: ${task.id}`);
    console.log(`⏰ Task scheduled at: ${task.scheduled_at}`);
    
    return task.id;

  } catch (error) {
    console.error('❌ Error in testMpsOverridePriceChangeTask:', error.message);
  }
}

/**
 * Test the database trigger by actually updating MPS override prices
 * @param {number} mpsId - The MPS ID to test with
 * @param {number} newRetailPrice - New retail override price (or null to clear)
 * @param {number} newMsrpPrice - New MSRP override price (or null to clear)
 */
async function testMpsOverridePriceTrigger(mpsId, newRetailPrice = null, newMsrpPrice = null) {
  console.log(`\n🧪 Testing MPS override price trigger for MPS ID: ${mpsId}`);
  console.log(`📋 Setting retail override to: ${newRetailPrice ? '$' + newRetailPrice : 'NULL'}`);
  console.log(`📋 Setting MSRP override to: ${newMsrpPrice ? '$' + newMsrpPrice : 'NULL'}`);
  
  try {
    // Get current values first
    const { data: currentMps, error: currentError } = await supabase
      .from('t_mps')
      .select('id, val_override_retail_price, val_override_msrp')
      .eq('id', mpsId)
      .single();

    if (currentError) {
      console.error('❌ Error fetching current MPS:', currentError.message);
      return;
    }

    console.log(`📋 Current retail override: ${currentMps.val_override_retail_price ? '$' + currentMps.val_override_retail_price : 'NULL'}`);
    console.log(`📋 Current MSRP override: ${currentMps.val_override_msrp ? '$' + currentMps.val_override_msrp : 'NULL'}`);

    // Update the MPS override prices (this should trigger the database trigger)
    const { error: updateError } = await supabase
      .from('t_mps')
      .update({
        val_override_retail_price: newRetailPrice,
        val_override_msrp: newMsrpPrice
      })
      .eq('id', mpsId);

    if (updateError) {
      console.error('❌ Error updating MPS override prices:', updateError.message);
      return;
    }

    console.log(`✅ Successfully updated MPS override prices for MPS ID: ${mpsId}`);
    console.log(`🔔 Database trigger should have automatically enqueued a task`);

    // Wait a moment and check for the automatically created task
    setTimeout(async () => {
      try {
        const { data: recentTasks, error: tasksError } = await supabase
          .from('t_task_queue')
          .select('*')
          .eq('task_type', 'mps_override_prices_changed_so_update_shopify')
          .order('created_at', { ascending: false })
          .limit(5);

        if (tasksError) {
          console.error('❌ Error fetching recent tasks:', tasksError.message);
          return;
        }

        console.log(`\n📊 Recent mps_override_prices_changed_so_update_shopify tasks:`);
        recentTasks.forEach(task => {
          console.log(`   Task ID: ${task.id}, Status: ${task.status}, Created: ${task.created_at}`);
          if (task.payload && task.payload.id === mpsId) {
            console.log(`   ✅ Found task for our MPS ID ${mpsId}!`);
          }
        });
      } catch (error) {
        console.error('❌ Error checking for recent tasks:', error.message);
      }
    }, 2000);

  } catch (error) {
    console.error('❌ Error in testMpsOverridePriceTrigger:', error.message);
  }
}

/**
 * Monitor task progress
 * @param {number} taskId - The task ID to monitor
 */
async function monitorTask(taskId) {
  console.log(`\n👀 Monitoring task ID: ${taskId}`);
  
  try {
    const { data: task, error } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', taskId)
      .single();

    if (error) {
      console.error('❌ Error fetching task:', error.message);
      return;
    }

    console.log(`📊 Task Status: ${task.status}`);
    console.log(`📊 Task Type: ${task.task_type}`);
    console.log(`📊 Created: ${task.created_at}`);
    console.log(`📊 Updated: ${task.updated_at}`);
    
    if (task.completed_at) {
      console.log(`📊 Completed: ${task.completed_at}`);
    }
    
    if (task.result) {
      console.log(`📊 Result:`, JSON.stringify(task.result, null, 2));
    }

  } catch (error) {
    console.error('❌ Error in monitorTask:', error.message);
  }
}

/**
 * Main test function
 */
async function main() {
  console.log('🚀 Starting MPS override price change task tests...');
  
  // Example usage - replace these with actual IDs from your database
  const testMpsId = 18810; // Replace with a real MPS ID that has discs/OSLs uploaded to Shopify
  const testNewRetailPrice = 21.99;
  const testNewMsrpPrice = 24.99;

  console.log('\n📝 Instructions:');
  console.log('1. Replace testMpsId with a real MPS ID from your t_mps table');
  console.log('2. Make sure the MPS has discs or OSLs that are uploaded to Shopify');
  console.log('3. Make sure your task queue worker is running to process these tasks');
  console.log('4. Monitor the tasks using the provided task IDs');

  // Test 1: Manual task enqueueing (both retail and MSRP)
  const manualTaskId = await testMpsOverridePriceChangeTask(testMpsId, true, true);
  
  // Test 2: Manual task enqueueing (retail only)
  const retailOnlyTaskId = await testMpsOverridePriceChangeTask(testMpsId, true, false);
  
  // Test 3: Manual task enqueueing (MSRP only)
  const msrpOnlyTaskId = await testMpsOverridePriceChangeTask(testMpsId, false, true);

  // Test 4: Database trigger test (uncomment to test - this will actually change data!)
  // console.log('\n⚠️  Uncomment the line below to test the database trigger (this will modify data!)');
  // await testMpsOverridePriceTrigger(testMpsId, testNewRetailPrice, testNewMsrpPrice);

  // Monitor the tasks (you can run this separately later)
  if (manualTaskId) {
    setTimeout(() => monitorTask(manualTaskId), 5000);
  }
  
  if (retailOnlyTaskId) {
    setTimeout(() => monitorTask(retailOnlyTaskId), 7000);
  }

  if (msrpOnlyTaskId) {
    setTimeout(() => monitorTask(msrpOnlyTaskId), 9000);
  }

  console.log('\n✅ Test tasks enqueued. Check your task queue worker logs for processing details.');
  console.log('\n💡 To test the database trigger, uncomment the trigger test line in the code and run again.');
}

// Run the tests
main().catch(console.error);
