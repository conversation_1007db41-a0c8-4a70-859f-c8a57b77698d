-- Create table for imported DBF data from invdb.dbf
CREATE TABLE public.imported_table_rpro (
  id SERIAL PRIMARY KEY,
  ivno INTEGER,
  ivalu VARCHAR(255),
  ivvc VARCHAR(50),
  ivdcs VARCHAR(50),
  ivdesc2 VARCHAR(255),
  ivdesc1 VARCHAR(255),
  ivattr VARCHAR(255),
  ivsize VARCHAR(255),
  ivupc VARCHAR(50),
  ivkityp INTEGER,
  ivdesc3 VARCHAR(255),
  ivqohcm NUMERIC(10,2),
  ivqsacm NUMERIC(10,2),
  ivqsalaw NUMERIC(10,2),
  ivqsatop NUMERIC(10,2),
  ivdesc4 VARCHAR(255),
  ivapd NUMERIC(10,2),
  ivavgcd NUMERIC(10,3),
  ivudnam VARCHAR(255),
  ivaux3 VARCHAR(255),
  ivaux4 VARCHAR(255),
  ivqtylaw NUMERIC(10,2),
  ivqtytop NUMERIC(10,2),
  ivisid VARCHAR(50),
  ivssid VARCHAR(50),
  ivprcdzlis NUMERIC(10,2),
  ivprcdz_dollar NUMERIC(10,2),
  ivprcdzsal NUMERIC(10,2),
  ivprcdzliv NUMERIC(10,2),
  ivprcbtlis NUMERIC(10,2),
  ivprcbt_dollar NUMERIC(10,2),
  ivprcbtsal NUMERIC(10,2),
  ivprcbtliv NUMERIC(10,2),
  ivprcpplis NUMERIC(10,2),
  ivprcpp_dollar NUMERIC(10,2),
  ivprcppsal NUMERIC(10,2),
  ivprcppliv NUMERIC(10,2),
  ivprcmsrp NUMERIC(10,2),
  ivprcmap NUMERIC(10,2),
  ivprccase NUMERIC(10,2),
  ivprcws_1 NUMERIC(10,2),
  ivprcws_2 NUMERIC(10,2),
  ivldr DATE,
  ivaux2 VARCHAR(255),
  ivaux5 VARCHAR(50),
  ivaux6 VARCHAR(255),
  ivlstcd NUMERIC(10,2),
  ivrpnlaw NUMERIC(10,2),
  ivrpxlaw NUMERIC(10,2),
  ivaux1 VARCHAR(255),
  ivaux8 VARCHAR(255),
  ivmsc4 INTEGER,
  ivdisdt DATE,
  ivmsc1 INTEGER,
  ivilmdt DATE,
  ivilmtm VARCHAR(50),
  ivmsc3 INTEGER,
  ivuddt DATE,
  ivvnld INTEGER,
  ivvnlcd INTEGER,
  ivmsc2 INTEGER,
  imported_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  import_batch_id UUID NOT NULL DEFAULT gen_random_uuid()
);

-- Add indexes for common query patterns
CREATE INDEX idx_imported_table_rpro_ivno ON public.imported_table_rpro(ivno);
CREATE INDEX idx_imported_table_rpro_ivupc ON public.imported_table_rpro(ivupc);
CREATE INDEX idx_imported_table_rpro_imported_at ON public.imported_table_rpro(imported_at);
CREATE INDEX idx_imported_table_rpro_import_batch_id ON public.imported_table_rpro(import_batch_id);

-- Grant access to authenticated users
ALTER TABLE public.imported_table_rpro ENABLE ROW LEVEL SECURITY;
GRANT ALL ON public.imported_table_rpro TO authenticated;

-- Create policy for authenticated users
CREATE POLICY "imported_table_rpro_policy" ON public.imported_table_rpro
  FOR ALL TO authenticated
  USING (true);

-- Comment on table
COMMENT ON TABLE public.imported_table_rpro IS 'Imported data from invdb.dbf - created on 2024-05-15';
