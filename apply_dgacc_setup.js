// apply_dgacc_setup.js - Apply DGACC view and trigger SQL to Supabase
import 'dotenv/config';
import fs from 'fs';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in environment');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

async function execSql(sql) {
  // Try common arg names used in this repo
  let res = await supabase.rpc('exec_sql', { sql_query: sql });
  if (res.error) {
    res = await supabase.rpc('exec_sql', { sql_statement: sql });
  }
  return res;
}

async function main() {
  try {
    const viewSql = fs.readFileSync('create_v_dgacc_live_qty.sql', 'utf8');
    const triggerSql = fs.readFileSync('create_dgacc_veeqo_qty_on_movement_trigger.sql', 'utf8');

    console.log('Applying create_v_dgacc_live_qty.sql ...');
    let { error: vErr } = await execSql(viewSql);
    if (vErr) {
      console.error('Error applying view SQL:', vErr.message);
      process.exit(1);
    }
    console.log('Applied view successfully.');

    console.log('Applying create_dgacc_veeqo_qty_on_movement_trigger.sql ...');
    let { error: tErr } = await execSql(triggerSql);
    if (tErr) {
      console.error('Error applying trigger SQL:', tErr.message);
      process.exit(1);
    }
    console.log('Applied trigger successfully.');

    console.log('Done.');
  } catch (e) {
    console.error('Fatal error:', e?.message || e);
    process.exit(1);
  }
}

main();

