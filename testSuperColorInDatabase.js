import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testSuperColorInDatabase() {
  try {
    console.log('🧪 Testing SuperColor parsing in database...\n');
    
    // Test SuperColor products
    const { data: superColorProducts, error: error1 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .ilike('raw_line_type', '%SuperColor%')
      .limit(20);
    
    if (error1) {
      console.error('Error querying SuperColor products:', error1);
    } else if (superColorProducts.length > 0) {
      console.log(`✅ Found ${superColorProducts.length} SuperColor products:`);
      superColorProducts.forEach((product, index) => {
        console.log(`\n${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
        console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        if (product.vendor_description) {
          console.log(`   Description: ${product.vendor_description.substring(0, 80)}...`);
        }
      });
    } else {
      console.log('⚪ No SuperColor products found');
    }
    
    // Test ESP Full Foil SuperColor products
    const { data: fullFoilProducts, error: error2 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .eq('plastic_name', 'ESP Full Foil SuperColor')
      .limit(10);
    
    if (error2) {
      console.error('Error querying Full Foil SuperColor products:', error2);
    } else if (fullFoilProducts.length > 0) {
      console.log(`\n✅ Found ${fullFoilProducts.length} ESP Full Foil SuperColor products:`);
      fullFoilProducts.forEach((product, index) => {
        console.log(`\n${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
        console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        if (product.vendor_description) {
          console.log(`   Description: ${product.vendor_description.substring(0, 80)}...`);
        }
      });
    } else {
      console.log('\n⚪ No ESP Full Foil SuperColor products found');
    }
    
    // Test ESP SuperColor products
    const { data: espSuperColorProducts, error: error3 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .eq('plastic_name', 'ESP SuperColor')
      .limit(10);
    
    if (error3) {
      console.error('Error querying ESP SuperColor products:', error3);
    } else if (espSuperColorProducts.length > 0) {
      console.log(`\n✅ Found ${espSuperColorProducts.length} ESP SuperColor products:`);
      espSuperColorProducts.forEach((product, index) => {
        console.log(`\n${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
        console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        if (product.vendor_description) {
          console.log(`   Description: ${product.vendor_description.substring(0, 80)}...`);
        }
      });
    } else {
      console.log('\n⚪ No ESP SuperColor products found');
    }
    
    // Test specific stamp names
    console.log('\n🎯 Testing specific SuperColor stamp names:');
    
    const stampTests = [
      'SuperColor Bali',
      'SuperColor Bunsky',
      'SuperColor Nebula Fire',
      'Brian Allen Ancient Alien',
      'Brian Allen Owl',
      'Chains Green',
      'Chains Pink',
      'Chains Blue'
    ];
    
    for (const stampName of stampTests) {
      const { data: stampProducts, error } = await supabase
        .from('it_discraft_order_sheet_lines')
        .select('plastic_name, mold_name, stamp_name, vendor_description')
        .eq('stamp_name', stampName)
        .limit(3);
      
      if (error) {
        console.error(`Error querying ${stampName}:`, error);
      } else if (stampProducts.length > 0) {
        console.log(`   ✅ ${stampName}: ${stampProducts.length} products found`);
        stampProducts.forEach(product => {
          console.log(`      ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        });
      } else {
        console.log(`   ⚪ ${stampName}: No products found`);
      }
    }
    
    console.log('\n🎉 SuperColor database test completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testSuperColorInDatabase().catch(console.error);
