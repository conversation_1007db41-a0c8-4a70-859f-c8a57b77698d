// Function to process a disc_updated_unsell task
async function processDiscUpdatedUnsellTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to unsell disc. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to unsell disc. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;
    const originalSoldDate = payload.original_sold_date;
    console.log(`[taskQueueWorker.js] Unselling disc with id=${discId}, original_sold_date=${originalSoldDate}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Get current disc status to check if we should unsell it
    const { data: currentDisc, error: discError } = await supabase
      .from('t_discs')
      .select('sold_date, sold_channel')
      .eq('id', discId)
      .single();

    if (discError) {
      const errMsg = `[taskQueueWorker.js] Error fetching current disc status: ${discError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching disc status for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to unsell disc. Could not fetch current disc status.",
        error: discError.message
      });
      return;
    }

    // Check if we should unsell this disc
    const shouldUnsell = originalSoldDate === null &&
                        currentDisc.sold_channel === 'Fixed' &&
                        currentDisc.sold_date !== null;

    if (!shouldUnsell) {
      // Don't unsell - disc was either already sold before workflow or not sold by our workflow
      const reason = originalSoldDate !== null
        ? `Disc was already sold before workflow started (original_sold_date: ${originalSoldDate})`
        : currentDisc.sold_channel !== 'Fixed'
          ? `Disc sold_channel is '${currentDisc.sold_channel}', not 'Fixed' from our workflow`
          : `Disc is not currently sold (sold_date: ${currentDisc.sold_date})`;

      console.log(`[taskQueueWorker.js] Skipping unsell for disc ${discId}: ${reason}`);

      // Still enqueue the next task but don't modify the disc
      await processNextTask(discId, false, reason, currentDisc);
      return;
    }

    // Proceed with unselling since this disc was sold by our workflow
    console.log(`[taskQueueWorker.js] Disc ${discId} was sold by our workflow (sold_channel='Fixed'), proceeding with unsell`);

    // Update the disc record to clear sold_channel and sold_date
    const updateData = {
      sold_channel: null,
      sold_date: null
    };

    console.log(`[taskQueueWorker.js] Updating disc ${discId} with: ${JSON.stringify(updateData)}`);

    const { error: updateError } = await supabase
      .from('t_discs')
      .update(updateData)
      .eq('id', discId);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating disc record: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to unsell disc. Database error when updating disc record.",
        error: updateError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully unsold disc ${discId}`);

    // Enqueue the next task
    await processNextTask(discId, true, "Successfully unsold disc", null);

  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to unsell disc due to an unexpected error.",
      error: err.message,
      disc_id: discId,
      next_task_enqueued: false,
      workflow_stopped: true,
      reason: "Unexpected exception during task processing"
    });
    return; // Don't call processNextTask on error
  }

  // Helper function to enqueue the next task and complete this task
  async function processNextTask(discId, wasUnsold, reason, currentDiscData = null) {
    try {
      console.log(`[taskQueueWorker.js] Enqueueing new_t_discs_record task for disc ${discId}`);

      const { data: newDiscTask, error: newDiscError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'new_t_discs_record',
          payload: {
            id: discId,
            operation: 'INSERT'
          },
          status: 'pending',
          scheduled_at: new Date().toISOString(), // No delay
          created_at: new Date().toISOString(),
          enqueued_by: `disc_updated_unsell_${discId}`
        })
        .select();

      if (newDiscError) {
        console.error(`[taskQueueWorker.js] Error enqueueing new_t_discs_record task: ${newDiscError.message}`);
        await logError(`Error enqueueing new_t_discs_record task: ${newDiscError.message}`, `Enqueueing task for disc id=${discId}`);
      } else {
        console.log(`[taskQueueWorker.js] Successfully enqueued new_t_discs_record task (ID: ${newDiscTask[0].id}) for disc ${discId}`);
      }

      const message = wasUnsold
        ? `Successfully unsold disc ${discId}. Cleared sold_channel and sold_date.`
        : `Skipped unselling disc ${discId}. ${reason}`;

      await updateTaskStatus(task.id, 'completed', {
        message: message,
        disc_id: discId,
        was_unsold: wasUnsold,
        unsell_reason: reason,
        sold_channel: wasUnsold ? null : (currentDiscData?.sold_channel || null),
        sold_date: wasUnsold ? null : (currentDiscData?.sold_date || null),
        next_task_enqueued: newDiscError ? false : true,
        next_task_error: newDiscError?.message || null
      });
    } catch (err) {
      console.error(`[taskQueueWorker.js] Exception enqueueing new_t_discs_record task: ${err.message}`);
      await logError(`Exception enqueueing new_t_discs_record task: ${err.message}`, `Enqueueing task for disc id=${discId}`);

      const message = wasUnsold
        ? `Successfully unsold disc ${discId}. Cleared sold_channel and sold_date. Failed to enqueue next task.`
        : `Skipped unselling disc ${discId}. ${reason}. Failed to enqueue next task.`;

      await updateTaskStatus(task.id, 'completed', {
        message: message,
        disc_id: discId,
        was_unsold: wasUnsold,
        unsell_reason: reason,
        sold_channel: wasUnsold ? null : (currentDiscData?.sold_channel || null),
        sold_date: wasUnsold ? null : (currentDiscData?.sold_date || null),
        next_task_enqueued: false,
        next_task_error: err.message
      });
    }
  }
}

export default processDiscUpdatedUnsellTask;
