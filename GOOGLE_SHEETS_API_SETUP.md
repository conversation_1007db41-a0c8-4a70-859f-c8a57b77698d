# 📊 Google Sheets API Setup for Discraft OSL Import

This guide will help you set up Google Sheets API access so you can import Discraft OSL data from private Google Sheets without making them public.

## 🎯 Overview

The Discraft OSL import task can access private Google Sheets using:
1. **Service Account** (Recommended for server applications)
2. **API Key** (Only works with public sheets)

## 🚀 Method 1: Service Account Setup (Recommended)

### Step 1: Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note your project ID

### Step 2: Enable Google Sheets API

1. In the Google Cloud Console, go to **APIs & Services** > **Library**
2. Search for "Google Sheets API"
3. Click on it and press **Enable**

### Step 3: Create a Service Account

1. Go to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **Service Account**
3. Fill in the details:
   - **Service account name**: `discraft-sheets-reader`
   - **Description**: `Service account for reading Discraft OSL data from Google Sheets`
4. Click **Create and Continue**
5. Skip the optional steps and click **Done**

### Step 4: Create and Download Service Account Key

1. In the **Credentials** page, find your service account
2. Click on the service account email
3. Go to the **Keys** tab
4. Click **Add Key** > **Create New Key**
5. Choose **JSON** format
6. Click **Create** - this will download a JSON file

### Step 5: Configure Environment Variables

You have two options:

#### Option A: JSON String in Environment Variable (Recommended)
1. Open the downloaded JSON file
2. Copy the entire JSON content
3. Add to your `.env` file:
```env
GOOGLE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"your-project",...}
```

#### Option B: File Path in Environment Variable
1. Save the JSON file securely (e.g., `./config/google-service-account.json`)
2. Add to your `.env` file:
```env
GOOGLE_APPLICATION_CREDENTIALS=./config/google-service-account.json
```

### Step 6: Share Google Sheets with Service Account

1. Open your Google Sheet
2. Click the **Share** button
3. Add the service account email (found in the JSON file, looks like `<EMAIL>`)
4. Set permission to **Viewer**
5. Click **Send**

## 🔑 Method 2: API Key Setup (Public Sheets Only)

### Step 1: Create an API Key

1. In Google Cloud Console, go to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **API Key**
3. Copy the generated API key
4. (Optional) Click **Restrict Key** to limit it to Google Sheets API only

### Step 2: Configure Environment Variable

Add to your `.env` file:
```env
GOOGLE_API_KEY=your-api-key-here
```

### Step 3: Make Sheet Public

1. Open your Google Sheet
2. Click **Share** > **Change to anyone with the link**
3. Set to **Viewer**
4. Click **Done**

## 🧪 Testing the Setup

### Test with Direct Script
```bash
node testDiscraftOslMapImport.js
```

### Test with Admin Interface
1. Start your admin server: `npm run start-admin`
2. Open `test-discraft-osl-import.html` in your browser
3. Click "Import OSL Map & Status"

### Test with API Call
```bash
curl -X POST http://localhost:3001/api/discraft/import-osl-map \
     -H "Content-Type: application/json" \
     -d "{}"
```

## 🔧 Troubleshooting

### Error: "Access denied to Google Sheet"
- Make sure you shared the sheet with the service account email
- Check that the service account has Viewer permissions
- Verify the Google Sheets API is enabled

### Error: "Sheet not found"
- Check the sheet name is correct (case-sensitive)
- Verify the spreadsheet ID in the URL
- Make sure the sheet exists

### Error: "Authentication not configured"
- Check your `.env` file has the correct environment variables
- Verify the JSON format if using `GOOGLE_SERVICE_ACCOUNT_KEY`
- Make sure the file path is correct if using `GOOGLE_APPLICATION_CREDENTIALS`

### Error: "Invalid credentials"
- Re-download the service account key
- Check the JSON format is valid
- Verify the service account still exists in Google Cloud Console

## 📋 Environment Variables Summary

Choose one of these authentication methods:

```env
# Method 1A: Service Account JSON String (Recommended)
GOOGLE_SERVICE_ACCOUNT_KEY={"type":"service_account",...}

# Method 1B: Service Account File Path
GOOGLE_APPLICATION_CREDENTIALS=./path/to/service-account.json

# Method 2: API Key (Public sheets only)
GOOGLE_API_KEY=your-api-key-here
```

## 🔒 Security Best Practices

1. **Never commit credentials to version control**
2. **Use environment variables or secure file storage**
3. **Limit service account permissions to minimum required**
4. **Regularly rotate API keys and service account keys**
5. **Monitor API usage in Google Cloud Console**

## 📚 Additional Resources

- [Google Sheets API Documentation](https://developers.google.com/sheets/api)
- [Google Cloud Service Accounts](https://cloud.google.com/iam/docs/service-accounts)
- [Google API Authentication](https://cloud.google.com/docs/authentication)

---

Once you've completed the setup, the Discraft OSL import will be able to access your private Google Sheets securely! 🎉
