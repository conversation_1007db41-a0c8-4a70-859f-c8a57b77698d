import puppeteer from 'puppeteer';

async function debugLogin() {
    console.log('Starting debug login test...');
    
    const browser = await puppeteer.launch({ 
        headless: false,
        defaultViewport: null,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
        devtools: true
    });
    
    try {
        const page = await browser.newPage();
        
        // Enable console logging from the page
        page.on('console', msg => console.log('PAGE LOG:', msg.text()));
        page.on('pageerror', error => console.log('PAGE ERROR:', error.message));
        
        console.log('Navigating to main site...');
        await page.goto('https://discgolfdistribution.com', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });
        
        console.log('Main page loaded, taking screenshot...');
        await page.screenshot({ path: 'main_page.png', fullPage: true });
        
        console.log('Checking if we can access login page...');
        await page.goto('https://discgolfdistribution.com/account/login', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });
        
        console.log('Login page loaded, taking screenshot...');
        await page.screenshot({ path: 'login_page.png', fullPage: true });
        
        // Check page content
        const title = await page.title();
        console.log('Page title:', title);
        
        const url = page.url();
        console.log('Current URL:', url);
        
        // Look for login form elements
        const emailField = await page.$('input[name="customer[email]"]');
        const passwordField = await page.$('input[name="customer[password]"]');
        const submitButton = await page.$('input[type="submit"], button[type="submit"]');
        
        console.log('Email field found:', !!emailField);
        console.log('Password field found:', !!passwordField);
        console.log('Submit button found:', !!submitButton);
        
        if (!emailField || !passwordField) {
            console.log('Login form not found. Checking for alternative selectors...');
            
            // Try alternative selectors
            const altEmailField = await page.$('input[type="email"]');
            const altPasswordField = await page.$('input[type="password"]');
            
            console.log('Alternative email field found:', !!altEmailField);
            console.log('Alternative password field found:', !!altPasswordField);
            
            // Get all input fields
            const allInputs = await page.$$eval('input', inputs => 
                inputs.map(input => ({
                    type: input.type,
                    name: input.name,
                    id: input.id,
                    placeholder: input.placeholder
                }))
            );
            
            console.log('All input fields found:', JSON.stringify(allInputs, null, 2));
        }
        
        // Wait for user to manually inspect the page
        console.log('Browser will stay open for 30 seconds for manual inspection...');
        await page.waitForTimeout(30000);
        
    } catch (error) {
        console.error('Error:', error.message);
        console.error('Stack:', error.stack);
    } finally {
        console.log('Closing browser...');
        await browser.close();
    }
}

debugLogin();
