import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

// Function to unarchive a product by updating its channel status
async function unarchiveProduct(productId, channelId = 244185) {
  try {
    console.log(`🔄 Attempting to unarchive product ${productId}...`);
    
    // First, get the product details
    const productResponse = await fetch(`https://api.veeqo.com/products/${productId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    });
    
    if (!productResponse.ok) {
      console.error(`❌ Failed to get product ${productId}: ${productResponse.status}`);
      return false;
    }
    
    const product = await productResponse.json();
    console.log(`📋 Product: "${product.title}"`);
    
    // Check if product has channel products that are pulled
    if (product.channel_products && product.channel_products.length > 0) {
      const pulledChannels = product.channel_products.filter(cp => cp.status === 'pulled');
      
      if (pulledChannels.length > 0) {
        console.log(`📤 Found ${pulledChannels.length} pulled channel(s)`);
        
        // Note: The exact API endpoint for updating channel product status 
        // may vary. You might need to check Veeqo's API documentation for the correct endpoint.
        // This is a placeholder for the actual unarchive operation.
        
        console.log(`⚠️  Manual action required: Update channel product status in Veeqo interface`);
        console.log(`   Product ID: ${productId}`);
        console.log(`   Product Title: ${product.title}`);
        
        return true;
      }
    }
    
    console.log(`✅ Product ${productId} doesn't appear to need unarchiving`);
    return true;
    
  } catch (error) {
    console.error(`❌ Error unarchiving product ${productId}: ${error.message}`);
    return false;
  }
}

// Example usage:
// unarchiveProduct(211445804); // Replace with actual product ID

export { unarchiveProduct };
