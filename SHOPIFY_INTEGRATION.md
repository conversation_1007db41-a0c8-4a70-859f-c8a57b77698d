# Shopify Integration

This document provides information about the Shopify integration for the task queue system.

## Environment Variables

The following environment variables are required for the Shopify integration:

```
# Shopify GraphQL Admin API credentials
SHOPIFY_ENDPOINT=https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/graphql.json
SHOPIFY_ACCESS_TOKEN=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
```

Optionally, you can specify a Shopify location ID:

```
# Shopify location ID (optional)
SHOPIFY_LOCATION_ID=gid://shopify/Location/63618220220
```

If the `SHOPIFY_LOCATION_ID` is not specified, the system will use the Drop Zone Disc Golf LFK Retail Shop location (ID: `gid://shopify/Location/63618220220`) as the default.

## Shopify Locations

The following Shopify locations are available:

| Name | ID | Address |
|------|----|----|
| Drop Zone Disc Golf LFK Retail Shop | gid://shopify/Location/63618220220 | 811 East 23rd Street, Suite E, Lawrence KS 66046, United States |
| 2023 Dynamic Discs Open | gid://shopify/Location/68169269436 | 1801 Rural Street, Hole 18 Emporia Country Club, Emporia KS 66801, United States |
| 2023GBO | gid://shopify/Location/68153082044 | 502 Commercial, Emporia KS 66801, United States |
| 2024 DDO & Worlds Emporia | gid://shopify/Location/71011139772 | 1801 Rural Street, Emporia KS 66801, United States |
| Ozawkie, KS | gid://shopify/Location/68887511228 | Ozawkie KS 66070, United States |
| PAINTBALL PARK | gid://shopify/Location/68543545532 | 1224 1 Rd, Overbrook KS 66524, United States |

## Reconciling Sold Discs on Shopify

The system includes a feature to find sold discs that are still showing on Shopify and set their inventory to zero. This is done through the following process:

1. Browse the `v_reconcile_d_to_shopify` view to review discs with the issue "Sold disc still showing up on Shopify."
2. Click the "Enqueue Sold Discs Shopify Tasks" button on the tasks tab.
3. This will enqueue a task of type `reconcile_clear_count_from_shopify_for_sold_disc` for each sold disc still showing on Shopify.
4. Process these tasks one at a time by running the worker in "Run Once" mode.

## Shopify GraphQL API

The system uses the Shopify GraphQL API to update inventory quantities. The following GraphQL operations are used:

- `getProductVariants` - Gets the variants for a product
- `getInventoryLevel` - Gets the current inventory level for an inventory item
- `inventorySetOnHandQuantities` - Sets the inventory quantity for an inventory item (Shopify API 2023-07 and later)
- `inventoryBulkAdjustQuantityAtLocation` - Updates the inventory quantity for an inventory item (Shopify API before 2023-07)

### API Version Compatibility

The system is designed to work with both older and newer versions of the Shopify GraphQL API. It will first try the newer API format (2023-07 and later) and fall back to the older format if needed. This ensures compatibility with different Shopify API versions.

## Product ID Formatting

Shopify product IDs can be in two formats:

1. **Numeric format**: e.g., `8672605307068`
2. **GID format**: e.g., `gid://shopify/Product/8672605307068`

The Shopify GraphQL API requires product IDs to be in the GID format. The system automatically converts numeric product IDs to the GID format, so you can use either format when working with the system.

## Troubleshooting

If you encounter errors related to the Shopify integration, check the following:

1. Make sure the environment variables are correctly set in the `.env` file.
2. Check that the Shopify access token is valid and has the necessary permissions.
3. Verify that the Shopify location ID is correct.
4. Check the worker logs for detailed error messages.
5. If you see a "Variable $productId of type ID! was provided invalid value" error, make sure the product ID is in the correct format (the system should handle this automatically, but it's good to check).

If you need to get the Shopify location ID, you can run the `getShopifyLocationId.js` script:

```
node getShopifyLocationId.js
```

This will display all available Shopify locations and their IDs.
