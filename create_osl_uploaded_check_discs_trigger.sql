-- Function to enqueue a task for checking related discs when an OSL is uploaded
CREATE OR REPLACE FUNCTION fn_enqueue_osl_uploaded_check_related_discs()
RETURNS TRIGGER AS $$
BEGIN
    -- Only enqueue a task if shopify_uploaded_at has changed from NULL to NOT NULL
    IF OLD.shopify_uploaded_at IS NULL AND NEW.shopify_uploaded_at IS NOT NULL THEN
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'osl_uploaded_check_if_related_discs_are_ready',
            jsonb_build_object('id', NEW.id),
            'pending',
            NOW(),
            NOW()
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the old trigger if it exists
DROP TRIGGER IF EXISTS tr_enqueue_osl_uploaded_check_related_discs ON t_order_sheet_lines;

-- Create the new trigger
CREATE TRIGGER tr_enqueue_osl_uploaded_check_related_discs
AFTER UPDATE OF shopify_uploaded_at
ON t_order_sheet_lines
FOR EACH ROW
WHEN (OLD.shopify_uploaded_at IS NULL AND NEW.shopify_uploaded_at IS NOT NULL)
EXECUTE FUNCTION fn_enqueue_osl_uploaded_check_related_discs();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger tr_enqueue_osl_uploaded_check_related_discs has been created to enqueue a task.';
END $$;
