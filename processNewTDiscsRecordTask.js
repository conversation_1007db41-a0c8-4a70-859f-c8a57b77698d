// Function to process a new_t_discs_record task
async function processNewTDiscsRecordTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process new disc record. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process new disc record. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;
    console.log(`[taskQueueWorker.js] Processing new disc record with id=${discId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Fetch the disc record to check if image_file_name is not null or empty
    const { data: discRecord, error: discError } = await supabase
      .from('t_discs')
      .select('image_file_name')
      .eq('id', discId)
      .maybeSingle();

    if (discError) {
      const errMsg = `[taskQueueWorker.js] Error fetching disc record: ${discError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process new disc record. Database error when retrieving disc record.",
        error: discError.message
      });
      return;
    }

    // Get the current time for scheduling
    const now = new Date();

    // Create tasks with different scheduled times
    const tasks = [
      {
        task_type: 'generate_disc_title_pull_and_handle',
        scheduled_at: new Date(now), // Immediately
        minutes_delay: 0
      },
      {
        task_type: 'match_disc_to_osl',
        scheduled_at: new Date(now.getTime() + 60000), // 1 minute later
        minutes_delay: 1
      },
      {
        task_type: 'check_if_disc_is_ready',
        scheduled_at: new Date(now.getTime() + 360000), // 6 minutes later
        minutes_delay: 6
      },
      {
        task_type: 'match_disc_to_asins',
        scheduled_at: new Date(now.getTime() + 180000), // 3 minutes later
        minutes_delay: 3
      },
      {
        task_type: 'set_disc_carry_cost',
        scheduled_at: new Date(now.getTime() + 240000), // 4 minutes later
        minutes_delay: 4
      }
    ];

    // Add verify_disc_image task if image_file_name is not null or empty
    if (discRecord && discRecord.image_file_name && discRecord.image_file_name.trim() !== '') {
      console.log(`[taskQueueWorker.js] Disc ${discId} has image_file_name=${discRecord.image_file_name}, adding verify_disc_image task`);
      tasks.push({
        task_type: 'verify_disc_image',
        scheduled_at: new Date(now.getTime() + 300000), // 5 minutes later
        minutes_delay: 5
      });
    }

    // Enqueue all tasks
    const results = [];
    for (const taskInfo of tasks) {
      try {
        // Prepare the payload based on task type
        let payload = {
          id: discId,
          operation: 'INSERT'
        };

        // For verify_disc_image task, we only need to pass the disc ID
        if (taskInfo.task_type === 'verify_disc_image') {
          // Just pass the disc ID directly
          payload = {
            id: discId
          };
        }

        const { data, error } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: taskInfo.task_type,
            payload: payload,
            status: 'pending',
            scheduled_at: taskInfo.scheduled_at.toISOString(),
            created_at: now.toISOString(),
            enqueued_by: `new_t_discs_record_${discId}`
          })
          .select();

        if (error) {
          const errMsg = `[taskQueueWorker.js] Error enqueueing ${taskInfo.task_type} task: ${error.message}`;
          console.error(errMsg);
          await logError(errMsg, `Enqueueing ${taskInfo.task_type} task`);
          results.push({
            task_type: taskInfo.task_type,
            success: false,
            error: error.message
          });
        } else {
          console.log(`[taskQueueWorker.js] Successfully enqueued ${taskInfo.task_type} task for disc ${discId}, scheduled ${taskInfo.minutes_delay} minutes later`);
          results.push({
            task_type: taskInfo.task_type,
            success: true,
            task_id: data[0].id
          });
        }
      } catch (err) {
        const errMsg = `[taskQueueWorker.js] Exception enqueueing ${taskInfo.task_type} task: ${err.message}`;
        console.error(errMsg);
        await logError(errMsg, `Enqueueing ${taskInfo.task_type} task`);
        results.push({
          task_type: taskInfo.task_type,
          success: false,
          error: err.message
        });
      }
    }

    // Prepare completion message
    let message = `Successfully processed new disc record and enqueued follow-up tasks.`;

    // Add information about image verification if applicable
    if (discRecord && discRecord.image_file_name && discRecord.image_file_name.trim() !== '') {
      message += ` Image verification task scheduled for ${discRecord.image_file_name}.`;
    }

    // Mark the task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: message,
      results: results,
      has_image: discRecord && discRecord.image_file_name && discRecord.image_file_name.trim() !== ''
    });

  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process new disc record due to an unexpected error.",
      error: err.message
    });
  }
}

export default processNewTDiscsRecordTask;
