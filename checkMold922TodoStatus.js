// checkMold922TodoStatus.js
// Check why mold 922 appears in v_todo_molds

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkMold922TodoStatus() {
  try {
    console.log('🔍 Checking why mold 922 appears in v_todo_molds...\n');
    
    // Check if mold 922 is currently in v_todo_molds
    const { data: todoRecords, error: todoError } = await supabase
      .from('v_todo_molds')
      .select('*')
      .eq('id', 922);
    
    if (todoError) {
      console.error('❌ Error checking v_todo_molds:', todoError);
      return;
    }
    
    console.log(`📋 Current v_todo_molds status for mold 922:`);
    if (todoRecords && todoRecords.length > 0) {
      console.log('❌ Mold 922 IS present in v_todo_molds');
      todoRecords.forEach(record => {
        console.log(`   Issue: ${record.issue}`);
        console.log(`   Effect: ${record.effect}`);
        console.log(`   Severity: ${record.severity}`);
      });
    } else {
      console.log('✅ Mold 922 is NOT present in v_todo_molds (ready for publishing)');
    }
    
    // Get detailed mold information to understand what might be wrong
    console.log(`\n🔍 Detailed analysis of mold 922 requirements:`);
    
    const { data: moldDetails, error: moldError } = await supabase
      .from('t_molds')
      .select(`
        *,
        t_brands!inner(
          id,
          brand,
          shopify_collection_created_at
        )
      `)
      .eq('id', 922)
      .single();
    
    if (moldError) {
      console.error('❌ Error fetching mold details:', moldError);
      return;
    }
    
    // Check images
    const { data: images, error: imageError } = await supabase
      .from('t_images')
      .select('id, image_verified')
      .eq('table_name', 't_molds')
      .eq('record_id', 922);
    
    if (imageError) {
      console.error('❌ Error fetching images:', imageError);
      return;
    }
    
    // Get config for min_length
    const { data: config, error: configError } = await supabase
      .from('t_config')
      .select('value')
      .eq('key', 'min_description_length')
      .single();
    
    const minLength = config ? parseInt(config.value) : 50; // Default to 50 if not found
    
    console.log(`📊 Requirement Analysis:`);
    
    // 1. Image requirements
    const verifiedImages = images.filter(img => img.image_verified);
    console.log(`   Images: ${images.length} total, ${verifiedImages.length} verified`);
    if (verifiedImages.length === 0) {
      console.log('   ❌ ISSUE: No verified images found');
    } else {
      console.log('   ✅ Has verified images');
    }
    
    // 2. Flight numbers
    const hasCompleteFlightNumbers = moldDetails.speed !== null && 
                                   moldDetails.glide !== null && 
                                   moldDetails.turn !== null && 
                                   moldDetails.fade !== null;
    console.log(`   Flight Numbers: ${moldDetails.speed}/${moldDetails.glide}/${moldDetails.turn}/${moldDetails.fade}`);
    if (!hasCompleteFlightNumbers) {
      console.log('   ❌ ISSUE: Missing flight numbers');
    } else {
      console.log('   ✅ Complete flight numbers');
    }
    
    // 3. Description
    const descriptionLength = moldDetails.description ? moldDetails.description.length : 0;
    console.log(`   Description: ${descriptionLength} characters (min required: ${minLength})`);
    if (!moldDetails.description || descriptionLength < minLength) {
      console.log('   ❌ ISSUE: Description missing or too short');
    } else {
      console.log('   ✅ Description meets requirements');
    }
    
    // 4. Brand collection
    console.log(`   Brand Collection Created: ${moldDetails.t_brands.shopify_collection_created_at ? 'Yes' : 'No'}`);
    if (!moldDetails.t_brands.shopify_collection_created_at) {
      console.log('   ❌ ISSUE: Brand collection not created');
    } else {
      console.log('   ✅ Brand collection exists');
    }
    
    // 5. Already published check
    console.log(`   Already Published: ${moldDetails.shopify_collection_created_at ? 'Yes' : 'No'}`);
    if (moldDetails.shopify_collection_created_at) {
      console.log('   ⚠️  Mold already published - should not be in todo');
    } else {
      console.log('   ✅ Not yet published');
    }
    
    // Check if there are any discs in stock for this mold (affects severity)
    const { data: discCount, error: discError } = await supabase
      .from('t_discs')
      .select('id', { count: 'exact' })
      .eq('mps_id', moldDetails.id)
      .is('sold_date', null);
    
    if (!discError) {
      console.log(`   Discs in Stock: ${discCount.length || 0}`);
    }
    
    console.log(`\n💡 Summary:`);
    if (todoRecords && todoRecords.length > 0) {
      console.log('The mold appears in v_todo_molds, which means the publishCollectionMold.js');
      console.log('process correctly detected issues and exited early without creating the collection.');
      console.log('This is the expected behavior - the task system is working correctly.');
    } else {
      console.log('The mold does NOT appear in v_todo_molds, which means it should be ready');
      console.log('for publishing. If the task failed, there might be a different issue.');
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

checkMold922TodoStatus();
