-- Function to enqueue a task for mold updates
CREATE OR R<PERSON>LACE FUNCTION fn_queue_mold_update_downstream_generate_mps_fields()
RETURNS TRIGGER AS $$
BEGIN
    -- Only proceed if relevant fields have changed
    IF (OLD.mold <> NEW.mold OR 
        OLD.speed <> NEW.speed OR 
        OLD.glide <> NEW.glide OR 
        OLD.turn <> NEW.turn OR 
        OLD.fade <> NEW.fade OR 
        OLD.type <> NEW.type OR 
        OLD.description <> NEW.description OR 
        OLD.brand_id <> NEW.brand_id OR
        (OLD.mold IS NULL AND NEW.mold IS NOT NULL) OR
        (OLD.speed IS NULL AND NEW.speed IS NOT NULL) OR
        (OLD.glide IS NULL AND NEW.glide IS NOT NULL) OR
        (OLD.turn IS NULL AND NEW.turn IS NOT NULL) OR
        (OLD.fade IS NULL AND NEW.fade IS NOT NULL) OR
        (OLD.type IS NULL AND NEW.type IS NOT NULL) OR
        (OLD.description IS NULL AND NEW.description IS NOT NULL) OR
        (OLD.brand_id IS NULL AND NEW.brand_id IS NOT NULL)) THEN
        
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'mold_update_downstream_generate_mps_fields',
            jsonb_build_object('id', NEW.id),
            'pending',
            NOW(),
            NOW()
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trg_queue_mold_update_downstream_generate_mps_fields ON t_molds;

CREATE TRIGGER trg_queue_mold_update_downstream_generate_mps_fields
AFTER UPDATE OF mold, speed, glide, turn, fade, type, description, brand_id ON t_molds
FOR EACH ROW
EXECUTE FUNCTION fn_queue_mold_update_downstream_generate_mps_fields();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger trg_queue_mold_update_downstream_generate_mps_fields has been created.';
END $$;
