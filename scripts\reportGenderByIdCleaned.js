import fs from 'fs';
import path from 'path';
import Papa from 'papaparse';

const FILE_PATH = path.join(process.cwd(), 'data', 'external data', 'access', 'tAccessories.txt');

function cleanTsvContent(raw) {
  const lines = raw.split(/\r?\n/);
  if (lines.length === 0) return raw;
  const header = lines[0];
  const out = [header];
  let acc = '';
  
  const isBalanced = (s) => {
    let inQuote = false;
    for (let i = 0; i < s.length; i++) {
      if (s[i] === '"') {
        if (s[i + 1] === '"') { i++; continue; }
        inQuote = !inQuote;
      }
    }
    return !inQuote;
  };

  for (let i = 1; i < lines.length; i++) {
    if (acc === '') {
      acc = lines[i];
    } else {
      acc += '\n' + lines[i];
    }
    if (isBalanced(acc)) {
      acc = acc.replace(/\n"\t/g, '\n\t');
      out.push(acc);
      acc = '';
    }
  }
  if (acc) out.push(acc);
  return out.join('\n');
}

function parseCleaned(filePath) {
  const raw = fs.readFileSync(filePath, 'utf-8');
  const cleaned = cleanTsvContent(raw);
  const { data } = Papa.parse(cleaned, {
    header: true,
    skipEmptyLines: true,
    delimiter: '\t',
  });
  return data;
}

function main() {
  const ids = process.argv.slice(2);
  if (!ids.length) {
    console.log('Usage: node scripts/reportGenderByIdCleaned.js <AccessoryID> [more IDs...]');
    process.exit(1);
  }
  const rows = parseCleaned(FILE_PATH);
  const set = new Set(ids);
  const matches = rows.filter(r => set.has(String(r['AccessoryID']).trim()));
  for (const r of matches) {
    console.log(`AccessoryID=${r['AccessoryID']}  CategoryID=${r['CategoryID']}  Title="${(r['Title']||'').toString().trim()}"  GenderRaw=${JSON.stringify(r['Gender'])}  GenderTrimmed=${JSON.stringify((r['Gender']||'').toString().trim())}`);
  }
}

main();

