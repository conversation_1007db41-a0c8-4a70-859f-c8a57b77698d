import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Calculate weight range tag based on disc weight
 * @param {number} weight - The disc weight
 * @returns {string} - The weight range tag
 */
function calculateWeightRangeTag(weight) {
  // Round to nearest 0.5 for proper range assignment
  const roundedWeight = Math.round(weight * 2) / 2;

  if (roundedWeight >= 10 && roundedWeight <= 49.5) {
    return 'wt_rng_10-49';
  } else if (roundedWeight >= 50 && roundedWeight <= 99.5) {
    return 'wt_rng_50-99';
  } else if (roundedWeight >= 100 && roundedWeight <= 119.5) {
    return 'wt_rng_100-119';
  } else if (roundedWeight >= 120 && roundedWeight <= 139.5) {
    return 'wt_rng_120-139';
  } else if (roundedWeight >= 140 && roundedWeight <= 149.5) {
    return 'wt_rng_140-149';
  } else if (roundedWeight >= 150 && roundedWeight <= 159.5) {
    return 'wt_rng_150-159';
  } else if (roundedWeight >= 160 && roundedWeight <= 169.5) {
    return 'wt_rng_160-169';
  } else if (roundedWeight >= 170 && roundedWeight <= 174.5) {
    return 'wt_rng_170-174';
  } else if (roundedWeight >= 175 && roundedWeight <= 180.5) {
    return 'wt_rng_175-180';
  } else if (roundedWeight >= 181 && roundedWeight <= 200) {
    return 'wt_rng_181-200';
  } else if (roundedWeight >= 201 && roundedWeight <= 249) {
    return 'wt_rng_201-249';
  } else {
    // For weights outside all ranges, return null to skip
    return null;
  }
}

/**
 * Get a small sample of discs for testing
 */
async function getTestDiscs(limit = 5) {
  try {
    console.log(`📊 Querying ${limit} test discs for weight range tag updates...`);
    
    const { data: discs, error } = await supabase
      .from('t_discs')
      .select('id, weight')
      .not('shopify_uploaded_at', 'is', null)
      .is('sold_date', null)
      .not('weight', 'is', null)
      .order('id')
      .limit(limit);

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    console.log(`📊 Found ${discs.length} test discs`);
    return discs;
  } catch (error) {
    console.error(`❌ Error querying test discs:`, error.message);
    throw error;
  }
}

/**
 * Enqueue fix_weight_range tasks for test discs
 */
async function enqueueTestTasks(limit = 5, dryRun = false) {
  try {
    const discs = await getTestDiscs(limit);
    
    if (discs.length === 0) {
      console.log('ℹ️ No test discs found');
      return;
    }

    console.log(`🚀 ${dryRun ? 'DRY RUN: Would enqueue' : 'Enqueueing'} ${discs.length} test fix_weight_range tasks...`);
    
    const now = new Date();
    const scheduledAt = new Date(now.getTime() + 1 * 60 * 1000); // Schedule 1 minute in future for testing
    
    // Prepare tasks
    const tasks = [];
    let validDiscs = 0;
    let skippedDiscs = 0;
    
    for (const disc of discs) {
      const weightRangeTag = calculateWeightRangeTag(disc.weight);
      
      if (weightRangeTag === null) {
        console.log(`⚠️ Skipping disc ${disc.id} with weight ${disc.weight}g (outside standard ranges)`);
        skippedDiscs++;
        continue;
      }
      
      console.log(`✅ Disc ${disc.id}: weight ${disc.weight}g → tag ${weightRangeTag}`);
      
      tasks.push({
        task_type: 'fix_weight_range',
        payload: {
          disc_id: disc.id,
          weight: disc.weight,
          expected_weight_range_tag: weightRangeTag
        },
        status: 'pending',
        scheduled_at: scheduledAt.toISOString(),
        created_at: now.toISOString(),
        enqueued_by: `weight_range_test_batch_${now.getTime()}`
      });
      
      validDiscs++;
    }
    
    console.log(`✅ Prepared ${validDiscs} test tasks`);
    console.log(`⚠️ Skipped ${skippedDiscs} discs with weights outside standard ranges`);
    
    if (dryRun) {
      console.log('🧪 DRY RUN: Tasks prepared but not enqueued');
      tasks.forEach((task, index) => {
        console.log(`Task ${index + 1}:`, JSON.stringify(task, null, 2));
      });
      return;
    }

    // Insert tasks
    const { error: insertError } = await supabase
      .from('t_task_queue')
      .insert(tasks);

    if (insertError) {
      console.error(`❌ Error enqueueing test tasks:`, insertError);
      throw insertError;
    }
    
    console.log(`🎉 Successfully enqueued ${validDiscs} test fix_weight_range tasks`);
    console.log(`📅 Tasks scheduled to start at: ${scheduledAt.toISOString()}`);
    
  } catch (error) {
    console.error(`❌ Error enqueueing test tasks:`, error.message);
    throw error;
  }
}

// Main execution
async function main() {
  try {
    // Parse command line arguments
    const args = process.argv.slice(2);
    const dryRun = args.includes('--dry-run');
    const limitArg = args.find(arg => arg.startsWith('--limit='));
    const limit = limitArg ? parseInt(limitArg.split('=')[1]) : 5;
    
    if (dryRun) {
      console.log('🧪 Running in DRY RUN mode - no tasks will be enqueued');
    }
    
    console.log(`🧪 Testing weight range update system with ${limit} discs`);
    
    await enqueueTestTasks(limit, dryRun);
    
  } catch (error) {
    console.error('❌ Test script failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
