# Amazon SP-API Setup Guide

This guide will help you set up Amazon Selling Partner API (SP-API) integration for deleting listings through your task queue system.

## Prerequisites

1. **Amazon Seller Central Account**: You need an active Amazon Seller Central account
2. **Developer Registration**: You must be registered as an Amazon SP-API developer
3. **Application Registration**: You need to register your application with Amazon

## Step 1: Register as an Amazon SP-API Developer

1. Go to [Amazon Developer Console](https://developer.amazonservices.com/)
2. Sign in with your Amazon Seller Central credentials
3. Navigate to "Selling Partner API" section
4. Complete the developer registration process

## Step 2: Create an SP-API Application

1. In the Developer Console, go to "Apps & Services" > "Selling Partner API"
2. Click "Create a new app"
3. Choose "Private Application" (for your own use)
4. Fill in the application details:
   - **Application Name**: Your application name (e.g., "Disc Golf Inventory Manager")
   - **Description**: Brief description of your application
   - **Privacy Policy URL**: Your privacy policy URL (if applicable)

## Step 3: Get Your Credentials

After creating your application, you'll receive:

1. **Client ID**: Your application's client identifier
2. **Client Secret**: Your application's secret key
3. **Refresh Token**: Long-lived token for authentication

## Step 4: Configure Environment Variables

Add the following variables to your `.env` file:

```bash
# Amazon SP-API credentials
AMAZON_CLIENT_ID=your_amazon_client_id
AMAZON_CLIENT_SECRET=your_amazon_client_secret
AMAZON_REFRESH_TOKEN=your_amazon_refresh_token
AMAZON_SELLER_ID=your_amazon_seller_id
AMAZON_MARKETPLACE_ID=ATVPDKIKX0DER
AMAZON_REGION=us-east-1
AMAZON_SP_API_ENDPOINT=https://sellingpartnerapi-na.amazon.com
```

### Finding Your Seller ID

1. Go to Amazon Seller Central
2. Navigate to "Settings" > "Account Info"
3. Your Seller ID is listed under "Business Information"

### Marketplace IDs

Common marketplace IDs:
- **US**: `ATVPDKIKX0DER`
- **Canada**: `A2EUQ1WTGCTBG2`
- **Mexico**: `A1AM78C64UM0Y8`
- **UK**: `A1F83G8C2ARO7P`
- **Germany**: `A1PA6795UKMFR9`
- **France**: `A13V1IB3VIYZZH`
- **Italy**: `APJ6JRA9NG5V4`
- **Spain**: `A1RKKUPIHCS9HS`

## Step 5: Self-Authorization (Private Application)

For private applications, you need to self-authorize:

1. Go to Amazon Seller Central
2. Navigate to "Apps & Services" > "Manage Your Apps"
3. Find your application and click "Authorize"
4. Grant the necessary permissions:
   - **Listings Management**: Required for deleting listings
   - **Orders**: If you plan to manage orders
   - **Inventory**: If you plan to manage inventory

## Step 6: Test Your Connection

1. Start your admin server: `npm run start-admin`
2. Open the admin interface in your browser
3. Go to the "Amazon FBA" tab
4. Click "Test Amazon Connection"
5. Verify that the connection is successful

## Important Notes

### Security Considerations

1. **Keep Credentials Secure**: Never commit your `.env` file to version control
2. **Rotate Tokens**: Regularly rotate your refresh tokens for security
3. **Limit Permissions**: Only grant the minimum required permissions

### Rate Limits

Amazon SP-API has rate limits:
- **Listings API**: 5 requests per second
- **Burst Capacity**: 10 requests
- **Daily Quota**: Varies by operation

### Error Handling

Common errors and solutions:

1. **401 Unauthorized**: Check your credentials and token expiry
2. **403 Forbidden**: Verify you have the required permissions
3. **429 Too Many Requests**: Implement retry logic with exponential backoff
4. **404 Not Found**: The listing may not exist or SKU is incorrect

## Usage

### Deleting a Listing

1. Go to the "Amazon FBA" tab in the admin interface
2. Enter the SKU you want to delete
3. Select the marketplace (defaults to US)
4. Optionally provide a reason for deletion
5. Click "Enqueue Listing Deletion"
6. Monitor the task progress in the "Worker" tab

### Task Queue Integration

The Amazon listing deletion is integrated with your existing task queue:

- **Task Type**: `delete_amazon_listing`
- **Payload**: Contains SKU, marketplace IDs, and reason
- **Processing**: Handled by the task queue worker
- **Logging**: Results are logged to the database

## Troubleshooting

### Connection Issues

1. **Invalid Credentials**: Double-check your client ID, secret, and refresh token
2. **Wrong Endpoint**: Ensure you're using the correct regional endpoint
3. **Expired Tokens**: Refresh tokens can expire; you may need to re-authorize

### Listing Deletion Issues

1. **SKU Not Found**: Verify the SKU exists in your Amazon inventory
2. **Permission Denied**: Ensure your application has Listings Management permissions
3. **Marketplace Mismatch**: Verify you're targeting the correct marketplace

### Getting Help

1. **Amazon Documentation**: [SP-API Developer Guide](https://developer-docs.amazon.com/sp-api/)
2. **Support**: Contact Amazon Developer Support for API-specific issues
3. **Community**: Amazon Developer Forums for community support

## Next Steps

Once your Amazon SP-API integration is working:

1. **Automate Workflows**: Create automated tasks for listing management
2. **Monitor Performance**: Track API usage and success rates
3. **Expand Functionality**: Add more SP-API operations as needed
4. **Error Handling**: Implement robust error handling and retry logic

## Additional Resources

- [Amazon SP-API Documentation](https://developer-docs.amazon.com/sp-api/)
- [Listings Items API Guide](https://developer-docs.amazon.com/sp-api/docs/listings-items-api-v2020-09-01-use-case-guide)
- [SP-API Authorization Guide](https://developer-docs.amazon.com/sp-api/docs/authorizing-selling-partner-api-applications)
