import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function fixMissedSpecialCases() {
  try {
    console.log('Fixing missed special case records...\n');

    const specialCases = [
      { contains: 'Starter Set', replacement: 'XXXX Accessory' },
      { contains: 'Disc Storage Rack', replacement: 'XXXX Accessory' },
      { contains: 'Golf Basket', replacement: 'XXXX Basket' }
    ];

    let totalFixed = 0;

    for (const specialCase of specialCases) {
      console.log(`Processing "${specialCase.contains}"...`);

      // Get ALL records that match and don't start with XXXX (regardless of raw_notes status)
      const { data: matchingRecords, error: fetchError } = await supabase
        .from('t_sdasins')
        .select('id, notes, raw_notes')
        .like('notes', `%${specialCase.contains}%`)
        .not('notes', 'like', 'XXXX%');

      if (fetchError) {
        console.error(`Error fetching records for ${specialCase.contains}:`, fetchError);
        continue;
      }

      console.log(`Found ${matchingRecords?.length || 0} records to update`);

      // Update each record
      for (const record of matchingRecords || []) {
        const updateData = {
          notes: specialCase.replacement
        };

        // Only set raw_notes if it's not already set
        if (!record.raw_notes) {
          updateData.raw_notes = record.notes;
        }

        const { error } = await supabase
          .from('t_sdasins')
          .update(updateData)
          .eq('id', record.id);

        if (error) {
          console.error(`Error updating record ${record.id}:`, error);
        } else {
          totalFixed++;
          if (totalFixed <= 10) { // Show first 10 updates
            console.log(`  Updated ID ${record.id}: ${record.notes} → ${specialCase.replacement}`);
          }
        }
      }

      console.log(`Completed "${specialCase.contains}": ${matchingRecords?.length || 0} records updated\n`);
    }

    console.log(`Total records fixed: ${totalFixed}`);

    // Verify the fixes
    console.log('\nVerifying fixes...');
    
    for (const specialCase of specialCases) {
      const { count, error } = await supabase
        .from('t_sdasins')
        .select('*', { count: 'exact', head: true })
        .like('notes', `%${specialCase.contains}%`)
        .not('notes', 'like', 'XXXX%');

      if (!error) {
        console.log(`Remaining "${specialCase.contains}" records not marked as XXXX: ${count}`);
      }
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

fixMissedSpecialCases();
