-- View to compute current on-hand quantity for DGACC accessories from movements
-- Sign rules:
--   voucher  -> add quantity
--   sale     -> sum quantity as-is (sales are already stored as negative qty)
-- Negative quantities in either type are treated naturally as reversals

CREATE OR REPLACE VIEW public.v_dgacc_live_qty AS
SELECT
  m.product_variant_id,
  COALESCE(SUM(
    CASE
      WHEN m.movement_type = 'voucher' THEN COALESCE(m.quantity, 0)
      WHEN m.movement_type = 'sale' THEN COALESCE(m.quantity, 0)
      ELSE 0
    END
  ), 0::bigint) AS on_hand_qty
FROM public.t_inventory_movements m
GROUP BY m.product_variant_id;

-- Optional: enable simple selection
COMMENT ON VIEW public.v_dgacc_live_qty IS 'Aggregates t_inventory_movements into current on-hand quantity per product_variant_id using voucher +, sale summed as-is (sales stored negative).';

