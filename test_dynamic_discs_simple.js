import puppeteer from 'puppeteer';

async function testBasicAccess() {
    console.log('Starting basic test...');
    
    let browser;
    try {
        console.log('Launching browser...');
        browser = await puppeteer.launch({ 
            headless: false,
            defaultViewport: null,
            args: ['--no-sandbox', '--disable-setuid-sandbox'],
            timeout: 60000
        });
        
        console.log('Creating new page...');
        const page = await browser.newPage();
        
        console.log('Setting user agent...');
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        console.log('Navigating to main site...');
        await page.goto('https://discgolfdistribution.com', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });
        
        console.log('Page loaded successfully');
        const title = await page.title();
        console.log('Page title:', title);
        
        console.log('Taking screenshot...');
        await page.screenshot({ path: 'test_screenshot.png', fullPage: true });
        
        console.log('Navigating to login page...');
        await page.goto('https://discgolfdistribution.com/account/login', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });
        
        console.log('Login page loaded');
        const loginTitle = await page.title();
        console.log('Login page title:', loginTitle);
        
        console.log('Taking login page screenshot...');
        await page.screenshot({ path: 'login_screenshot.png', fullPage: true });
        
        // Check if login form exists
        const emailField = await page.$('input[name="customer[email]"]');
        const passwordField = await page.$('input[name="customer[password]"]');
        
        console.log('Email field found:', !!emailField);
        console.log('Password field found:', !!passwordField);
        
        if (emailField && passwordField) {
            console.log('Login form found, attempting login...');
            
            await page.type('input[name="customer[email]"]', '<EMAIL>');
            await page.type('input[name="customer[password]"]', 'Sdisplatgun9!');
            
            console.log('Credentials entered, submitting form...');
            await page.click('input[type="submit"], button[type="submit"]');
            
            console.log('Waiting for navigation...');
            await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 30000 });
            
            console.log('Login completed');
            const afterLoginTitle = await page.title();
            console.log('After login title:', afterLoginTitle);
            
            await page.screenshot({ path: 'after_login_screenshot.png', fullPage: true });
            
            // Try to access the Dynamic Discs collection
            console.log('Navigating to Dynamic Discs collection...');
            await page.goto('https://discgolfdistribution.com/collections/dynamic-discs', { 
                waitUntil: 'networkidle2',
                timeout: 30000 
            });
            
            console.log('Collection page loaded');
            const collectionTitle = await page.title();
            console.log('Collection page title:', collectionTitle);
            
            await page.screenshot({ path: 'collection_screenshot.png', fullPage: true });
            
            // Try the main products JSON endpoint
            console.log('Testing main products JSON endpoint...');
            const jsonResponse = await page.goto('https://discgolfdistribution.com/products.json?page=1&limit=250', {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            console.log('JSON response status:', jsonResponse.status());
            const content = await page.content();
            console.log('JSON content preview:', content.substring(0, 1000));

            if (jsonResponse.status() === 200) {
                console.log('Success! JSON endpoint is accessible');

                // Try to parse the JSON
                try {
                    const jsonMatch = content.match(/<pre[^>]*>(.*?)<\/pre>/s);
                    if (jsonMatch) {
                        const jsonData = JSON.parse(jsonMatch[1]);
                        console.log('Found', jsonData.products?.length || 0, 'products on page 1');

                        // Look for Dynamic Discs products
                        const dynamicDiscsProducts = jsonData.products?.filter(product =>
                            product.vendor?.toLowerCase().includes('dynamic') ||
                            product.title?.toLowerCase().includes('dynamic') ||
                            product.tags?.some(tag => tag.toLowerCase().includes('dynamic'))
                        ) || [];

                        console.log('Found', dynamicDiscsProducts.length, 'Dynamic Discs products on page 1');

                        if (dynamicDiscsProducts.length > 0) {
                            console.log('Sample Dynamic Discs product:', JSON.stringify(dynamicDiscsProducts[0], null, 2));
                        }
                    }
                } catch (parseError) {
                    console.error('Error parsing JSON:', parseError.message);
                }
            }
            
        } else {
            console.log('Login form not found - checking page content');
            const content = await page.content();
            console.log('Page content preview:', content.substring(0, 1000));
        }
        
    } catch (error) {
        console.error('Error:', error.message);
        console.error('Stack:', error.stack);
    } finally {
        if (browser) {
            console.log('Closing browser...');
            await browser.close();
        }
    }
}

testBasicAccess();
