// apply_color23_any.js
// Applies updated SQL functions to treat SDASIN color_id=23 as wildcard, then verifies disc 426716 matches SDASIN 64839

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs/promises';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in .env');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

async function readFunctionSqlOnly(path) {
  const content = await fs.readFile(path, 'utf8');
  // Remove any confirmation DO $$ blocks by splitting on a marker or on DO $$
  const splitOnConfirm = content.split(/\n-- Confirmation message[\s\S]*/i)[0];
  const splitOnDO = splitOnConfirm.split(/\nDO \$\$[\s\S]*?\$\$;?/i)[0];
  return splitOnDO.trim();
}

async function applyFunction(sql, name) {
  console.log(`\nApplying function: ${name} ...`);
  const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
  if (error) {
    console.error(`❌ Failed applying ${name}:`, error);
    throw new Error(`exec_sql failed for ${name}: ${error.message}`);
  }
  console.log(`✅ Applied ${name}`);
}

async function verify() {
  const discId = 426716;
  const sdasinId = 64839;
  console.log(`\nRunning match_disc_to_all_sdasins(${discId}) ...`);
  const { data: matchCount, error: matchErr } = await supabase.rpc('match_disc_to_all_sdasins', { disc_id_param: discId });
  if (matchErr) {
    console.error('❌ Error running match_disc_to_all_sdasins:', matchErr);
  } else {
    console.log(`✅ match_disc_to_all_sdasins returned: ${matchCount}`);
  }

  console.log(`\nChecking tjoin_discs_sdasins for disc ${discId} ↔ SDASIN ${sdasinId} ...`);
  const { data: joinRows, error: joinErr } = await supabase
    .from('tjoin_discs_sdasins')
    .select('disc_id, sdasin_id, reason, created_at')
    .eq('disc_id', discId)
    .eq('sdasin_id', sdasinId)
    .order('created_at', { ascending: false })
    .limit(5);

  if (joinErr) {
    console.error('❌ Error querying tjoin_discs_sdasins:', joinErr);
  } else if (!joinRows || joinRows.length === 0) {
    console.log('❌ No match rows found. It may still be blocked by MPS or weight range checks, or disc is outside the 14-day window.');
  } else {
    console.log('✅ Found join rows:');
    console.log(joinRows);
  }
}

async function main() {
  try {
    console.log('Starting apply_color23_any...');

    const f1 = await readFunctionSqlOnly('create_match_disc_to_all_sdasins_function.sql');
    const f2 = await readFunctionSqlOnly('create_match_sdasin_to_all_discs_function.sql');
    const f3 = await readFunctionSqlOnly('create_disc_sdasin_match_function.sql');

    await applyFunction(f1, 'match_disc_to_all_sdasins');
    await applyFunction(f2, 'match_sdasin_to_all_discs');
    await applyFunction(f3, 'check_disc_sdasin_match');

    await verify();
  } catch (e) {
    console.error('apply_color23_any failed:', e.message);
    process.exit(1);
  }
}

main();

