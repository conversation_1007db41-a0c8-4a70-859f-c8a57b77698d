// create_and_test_trigger.js - Create trigger and test it directly
import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in environment');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Creating the trigger function and trigger...');
    
    // Create the function first
    const functionSql = `
CREATE OR REPLACE FUNCTION public.enqueue_variant_ready_on_image_verified()
RETURNS TRIGGER AS $$
BEGIN
  -- Only act when image_verified changes from FALSE to TRUE for t_product_variants records
  IF (NEW.table_name = 't_product_variants' 
      AND OLD.image_verified = FALSE 
      AND NEW.image_verified = TRUE) THEN
    
    -- Insert task, avoiding duplicates already pending/processing for this variant
    INSERT INTO public.t_task_queue (task_type, payload, status, scheduled_at, created_at, enqueued_by)
    SELECT
      'check_if_product_variant_is_ready' AS task_type,
      jsonb_build_object('id', NEW.record_id) AS payload,
      'pending' AS status,
      NOW() AS scheduled_at,
      NOW() AS created_at,
      'trigger:t_images.image_verified_for_variant' AS enqueued_by
    WHERE NOT EXISTS (
      SELECT 1 FROM public.t_task_queue tq
      WHERE tq.task_type = 'check_if_product_variant_is_ready'
        AND (tq.status = 'pending' OR tq.status = 'processing')
        AND (tq.payload->>'id')::INT = NEW.record_id
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
    `;
    
    const { error: funcError } = await supabase.rpc('exec_sql', { sql_query: functionSql });
    
    if (funcError) {
      console.error('❌ Error creating function:', funcError);
      return;
    }
    
    console.log('✅ Function created successfully');
    
    // Create the trigger
    const triggerSql = `
DROP TRIGGER IF EXISTS trg_t_images_enqueue_variant_ready_on_image_verified ON public.t_images;

CREATE TRIGGER trg_t_images_enqueue_variant_ready_on_image_verified
AFTER UPDATE OF image_verified ON public.t_images
FOR EACH ROW
WHEN (NEW.table_name = 't_product_variants' AND OLD.image_verified = FALSE AND NEW.image_verified = TRUE)
EXECUTE FUNCTION public.enqueue_variant_ready_on_image_verified();
    `;
    
    const { error: triggerError } = await supabase.rpc('exec_sql', { sql_query: triggerSql });
    
    if (triggerError) {
      console.error('❌ Error creating trigger:', triggerError);
      return;
    }
    
    console.log('✅ Trigger created successfully');
    
    // Now let's test the trigger by finding a t_product_variants image record and updating it
    console.log('\nLooking for a test record...');
    
    const { data: testRecord, error: findError } = await supabase
      .from('t_images')
      .select('*')
      .eq('table_name', 't_product_variants')
      .eq('image_verified', false)
      .limit(1)
      .maybeSingle();
    
    if (findError) {
      console.error('Error finding test record:', findError);
      return;
    }
    
    if (!testRecord) {
      console.log('No t_product_variants records with image_verified=false found for testing');
      
      // Let's see what t_product_variants records exist
      const { data: allVariantImages, error: allError } = await supabase
        .from('t_images')
        .select('id, record_id, image_verified')
        .eq('table_name', 't_product_variants')
        .limit(5);
      
      if (allError) {
        console.error('Error checking variant images:', allError);
      } else {
        console.log(`Found ${allVariantImages.length} t_product_variants image records:`);
        allVariantImages.forEach(img => {
          console.log(`  ID: ${img.id}, Record ID: ${img.record_id}, Verified: ${img.image_verified}`);
        });
      }
      return;
    }
    
    console.log(`Found test record: ID ${testRecord.id}, Record ID ${testRecord.record_id}`);
    
    // Check task queue before update
    const { data: tasksBefore, error: tasksBeforeError } = await supabase
      .from('t_task_queue')
      .select('id, task_type, payload')
      .eq('task_type', 'check_if_product_variant_is_ready')
      .contains('payload', { id: testRecord.record_id });
    
    if (tasksBeforeError) {
      console.error('Error checking tasks before:', tasksBeforeError);
    } else {
      console.log(`Tasks before update: ${tasksBefore.length}`);
    }
    
    // Update the record to trigger the function
    console.log('Updating image_verified from false to true...');
    
    const { error: updateError } = await supabase
      .from('t_images')
      .update({ 
        image_verified: true,
        image_verified_at: new Date().toISOString(),
        image_verified_notes: 'Test trigger update'
      })
      .eq('id', testRecord.id);
    
    if (updateError) {
      console.error('Error updating record:', updateError);
      return;
    }
    
    console.log('✅ Record updated successfully');
    
    // Wait a moment for the trigger to fire
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check task queue after update
    const { data: tasksAfter, error: tasksAfterError } = await supabase
      .from('t_task_queue')
      .select('id, task_type, payload, enqueued_by, created_at')
      .eq('task_type', 'check_if_product_variant_is_ready')
      .contains('payload', { id: testRecord.record_id })
      .order('created_at', { ascending: false });
    
    if (tasksAfterError) {
      console.error('Error checking tasks after:', tasksAfterError);
    } else {
      console.log(`Tasks after update: ${tasksAfter.length}`);
      if (tasksAfter.length > tasksBefore.length) {
        console.log('🎉 SUCCESS! New task was created by the trigger:');
        const newTask = tasksAfter[0];
        console.log(`   Task ID: ${newTask.id}`);
        console.log(`   Payload: ${JSON.stringify(newTask.payload)}`);
        console.log(`   Enqueued by: ${newTask.enqueued_by}`);
        console.log(`   Created at: ${newTask.created_at}`);
      } else {
        console.log('❌ No new task was created - trigger may not have fired');
      }
    }

  } catch (e) {
    console.error('Fatal error:', e?.message || e);
    process.exit(1);
  }
}

main();
