require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function enqueueTasksRemainingAgain() {
  try {
    console.log('Getting ALL remaining unsold discs with null vendor_osl_id and enqueueing match_disc_to_osl tasks again...');
    
    // Get ALL remaining unsold discs with null vendor_osl_id (no limit this time)
    const { data: remainingDiscs, error: fetchError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .is('vendor_osl_id', null)
      .is('sold_date', null)  // Unsold discs only
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null)
      .order('id');  // Get ALL remaining discs
    
    if (fetchError) {
      console.error('Error getting remaining discs:', fetchError);
      return;
    }
    
    console.log(`Found ${remainingDiscs.length} remaining unsold discs with null vendor_osl_id`);
    
    if (remainingDiscs.length === 0) {
      console.log('✅ No remaining unsold discs with null vendor_osl_id!');
      return;
    }
    
    console.log('\nAll remaining discs:');
    remainingDiscs.forEach((disc, index) => {
      console.log(`${index + 1}. Disc ${disc.id}: MPS ${disc.mps_id}, Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id}`);
    });
    
    // Enqueue match_disc_to_osl tasks for each disc
    console.log('\nEnqueueing match_disc_to_osl tasks for all remaining discs...');
    
    const tasksToEnqueue = remainingDiscs.map(disc => ({
      task_type: 'match_disc_to_osl',
      payload: {
        id: disc.id,
        operation: 'UPDATE',
        old_data: disc
      },
      status: 'pending',
      scheduled_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      enqueued_by: 'final_vendor_osl_check_round2'
    }));
    
    const { data: enqueuedTasks, error: enqueueError } = await supabase
      .from('t_task_queue')
      .insert(tasksToEnqueue)
      .select('id, task_type, payload');
    
    if (enqueueError) {
      console.error('Error enqueueing tasks:', enqueueError);
      return;
    }
    
    console.log(`\n✅ Successfully enqueued ${enqueuedTasks.length} match_disc_to_osl tasks!`);
    
    console.log('\nTask details:');
    enqueuedTasks.forEach((task, index) => {
      const discId = task.payload.id;
      console.log(`${index + 1}. Task ${task.id}: match_disc_to_osl for disc ${discId}`);
    });
    
    console.log('\n🔄 Tasks have been enqueued and will be processed by the task queue worker.');
    console.log('The worker will:');
    console.log('1. Test both regular OSL mapping (using weight)');
    console.log('2. Test vendor OSL mapping (using weight_mfg)');
    console.log('3. Update both order_sheet_line_id and vendor_osl_id if matches are found');
    console.log('4. Provide detailed debug information in the task results');
    
    console.log('\nTo check the results after processing:');
    console.log('1. Monitor the task queue for completion');
    console.log('2. Check the task results for any matches found');
    console.log('3. Verify if any discs now have vendor_osl_id set');
    
    // Show a query to check task status later
    console.log('\n📋 To check task status for this round, run:');
    console.log(`
SELECT 
  id, 
  status, 
  result->>'message' as message,
  result->>'vendor_osl_id' as vendor_osl_id,
  payload->>'id' as disc_id,
  processed_at
FROM t_task_queue 
WHERE enqueued_by = 'final_vendor_osl_check_round2' 
ORDER BY created_at;
    `);
    
    // Also show current count
    const { count: currentNullCount, error: countError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('vendor_osl_id', null)
      .is('sold_date', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (!countError) {
      console.log(`\n📊 Current count of unsold discs with null vendor_osl_id: ${currentNullCount}`);
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

enqueueTasksRemainingAgain();
