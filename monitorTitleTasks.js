// monitorTitleTasks.js
// Script to monitor the progress of title/pull/handle generation tasks
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or key in environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function monitorProgress() {
  console.log('Monitoring progress of title/pull/handle generation tasks...');

  try {
    // Get counts of tasks by status
    const { data: statusCounts, error: statusError } = await supabase
      .from('t_task_queue')
      .select('status, count(*)', { count: 'exact' })
      .eq('task_type', 'generate_disc_title_pull_and_handle')
      .group('status');

    if (statusError) {
      console.error('Error fetching status counts:', statusError);
      return;
    }

    // Format the status counts
    console.log('\nTask Status Summary:');
    console.log('-------------------');

    const statusMap = {};
    let total = 0;

    statusCounts.forEach(item => {
      statusMap[item.status] = parseInt(item.count);
      total += parseInt(item.count);
    });

    console.log(`Total Tasks: ${total}`);
    console.log(`Pending: ${statusMap.pending || 0}`);
    console.log(`Processing: ${statusMap.processing || 0}`);
    console.log(`Completed: ${statusMap.completed || 0}`);
    console.log(`Error: ${statusMap.error || 0}`);

    // Calculate completion percentage
    const completedPercentage = total > 0
      ? ((statusMap.completed || 0) / total * 100).toFixed(2)
      : 0;

    console.log(`Completion: ${completedPercentage}%`);

    // Get the most recent errors
    if (statusMap.error && statusMap.error > 0) {
      console.log('\nRecent Errors:');
      console.log('-------------');

      const { data: recentErrors, error: errorsError } = await supabase
        .from('t_task_queue')
        .select('id, payload, result, updated_at')
        .eq('task_type', 'generate_disc_title_pull_and_handle')
        .eq('status', 'error')
        .order('updated_at', { ascending: false })
        .limit(5);

      if (errorsError) {
        console.error('Error fetching recent errors:', errorsError);
      } else if (recentErrors && recentErrors.length > 0) {
        recentErrors.forEach(task => {
          console.log(`Task ID: ${task.id}`);
          console.log(`Disc ID: ${task.payload?.id || 'Unknown'}`);
          console.log(`Error: ${task.result?.error || 'Unknown error'}`);
          console.log(`Time: ${new Date(task.updated_at).toLocaleString()}`);
          console.log('---');
        });
      } else {
        console.log('No recent errors found.');
      }
    }

    // Get count of discs still needing updates
    const { count: remainingCount, error: remainingError } = await supabase
      .from('t_discs')
      .select('id', { count: 'exact', head: true })
      .or('g_title.is.null,g_pull.is.null,g_handle.is.null');

    if (remainingError) {
      console.error('Error fetching remaining count:', remainingError);
    } else {
      console.log(`\nDiscs still needing updates: ${remainingCount}`);
    }

  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the monitoring function
monitorProgress().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});
