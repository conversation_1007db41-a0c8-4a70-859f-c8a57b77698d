import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function debugFirstRun() {
    console.log('Debugging "First Run C-Line FD1" parsing...\n');
    
    try {
        // Get Discmania brand ID
        const { data: brandData, error: brandError } = await supabase
            .from('t_brands')
            .select('id, brand')
            .eq('brand', 'Discmania');
        
        const discmaniaBrandId = brandData[0].id;
        console.log(`Discmania brand ID: ${discmaniaBrandId}`);
        
        // Get Discmania plastics
        const { data: plastics, error: plasticsError } = await supabase
            .from('t_plastics')
            .select('plastic')
            .eq('brand_id', discmaniaBrandId)
            .ilike('plastic', '%c-line%');
        
        console.log('\nC-Line related plastics:');
        plastics.forEach(plastic => {
            console.log(`- "${plastic.plastic}"`);
        });
        
        // Test the parsing logic
        const title = "First Run C-Line FD1";
        console.log(`\nTitle: "${title}"`);
        
        // Check if "C-Line" is found
        const cLineIndex = title.indexOf('C-Line');
        console.log(`"C-Line" found at index: ${cLineIndex}`);
        
        if (cLineIndex > 0) {
            const prefix = title.substring(0, cLineIndex).trim();
            console.log(`Prefix before C-Line: "${prefix}"`);
            
            const afterCLine = title.substring(cLineIndex + 'C-Line'.length).trim();
            console.log(`After C-Line: "${afterCLine}"`);
        }
        
        // Check if FD1 mold exists
        const { data: fd1Mold, error: moldError } = await supabase
            .from('t_molds')
            .select('mold')
            .eq('brand_id', discmaniaBrandId)
            .eq('mold', 'FD1');
        
        console.log(`\nFD1 mold exists: ${fd1Mold.length > 0}`);
        
    } catch (error) {
        console.error('Debug failed:', error);
    }
}

// Run the debug
debugFirstRun();
