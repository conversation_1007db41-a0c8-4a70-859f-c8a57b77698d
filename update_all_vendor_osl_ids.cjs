require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function updateAllVendorOslIds() {
  try {
    console.log('Starting bulk update of vendor_osl_id for all discs with null values...');
    
    // First, get count of discs that need updating
    const { count: totalCount, error: countError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (countError) {
      console.error('Error getting count:', countError);
      return;
    }
    
    console.log(`Found ${totalCount} discs that need vendor_osl_id updates`);
    
    if (totalCount === 0) {
      console.log('No discs need updating. Exiting.');
      return;
    }
    
    // Process in batches to avoid overwhelming the database
    const batchSize = 100;
    let processed = 0;
    let updated = 0;
    let errors = 0;
    
    console.log(`Processing in batches of ${batchSize}...`);
    
    while (processed < totalCount) {
      console.log(`\nProcessing batch ${Math.floor(processed / batchSize) + 1}...`);
      
      // Get next batch of discs
      const { data: discs, error: fetchError } = await supabase
        .from('t_discs')
        .select('id, mps_id, weight_mfg, color_id, vendor_osl_id')
        .is('vendor_osl_id', null)
        .not('weight_mfg', 'is', null)
        .not('mps_id', 'is', null)
        .not('color_id', 'is', null)
        .range(0, batchSize - 1);
      
      if (fetchError) {
        console.error('Error fetching batch:', fetchError);
        break;
      }
      
      if (!discs || discs.length === 0) {
        console.log('No more discs to process');
        break;
      }
      
      console.log(`Processing ${discs.length} discs in this batch...`);
      
      // Process each disc in the batch
      for (const disc of discs) {
        try {
          // Find matching vendor OSL
          const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
            'find_matching_osl_by_mfg_weight',
            {
              mps_id_param: disc.mps_id,
              color_id_param: disc.color_id,
              weight_mfg_param: disc.weight_mfg
            }
          );
          
          if (vendorOslError) {
            console.error(`Error finding vendor OSL for disc ${disc.id}:`, vendorOslError);
            errors++;
            continue;
          }
          
          const vendorOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
          
          if (vendorOslId) {
            // Update the disc with vendor_osl_id
            const { error: updateError } = await supabase
              .from('t_discs')
              .update({ vendor_osl_id: vendorOslId })
              .eq('id', disc.id);
            
            if (updateError) {
              console.error(`Error updating disc ${disc.id}:`, updateError);
              errors++;
            } else {
              updated++;
              if (updated % 50 === 0) {
                console.log(`  Updated ${updated} discs so far...`);
              }
            }
          }
          
          processed++;
          
        } catch (err) {
          console.error(`Error processing disc ${disc.id}:`, err.message);
          errors++;
          processed++;
        }
      }
      
      console.log(`Batch completed. Total processed: ${processed}, Updated: ${updated}, Errors: ${errors}`);
      
      // Small delay between batches to avoid overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log('\n=== FINAL RESULTS ===');
    console.log(`Total discs processed: ${processed}`);
    console.log(`Successfully updated: ${updated}`);
    console.log(`Errors encountered: ${errors}`);
    console.log(`Discs with no matching vendor OSL: ${processed - updated - errors}`);
    
    if (updated > 0) {
      console.log('\n✅ Bulk update completed successfully!');
    } else {
      console.log('\n⚠️ No discs were updated. Check if vendor OSL mappings exist.');
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

// Add confirmation prompt
console.log('This will update ALL discs with null vendor_osl_id values.');
console.log('Press Ctrl+C to cancel, or any key to continue...');

process.stdin.setRawMode(true);
process.stdin.resume();
process.stdin.on('data', () => {
  process.stdin.setRawMode(false);
  updateAllVendorOslIds();
});
