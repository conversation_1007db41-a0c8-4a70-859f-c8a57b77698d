-- Function to enqueue a task for MPS g_code updates
CREATE OR REPLACE FUNCTION fn_queue_mps_g_code_update_downstream_generate_osl_fields()
RETURNS TRIGGER AS $$
BEGIN
    -- Only proceed if g_code has changed
    IF (OLD.g_code <> NEW.g_code OR 
        (OLD.g_code IS NULL AND NEW.g_code IS NOT NULL)) THEN
        
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'mps_g_code_update_downstream_generate_osl_fields',
            jsonb_build_object('id', NEW.id),
            'pending',
            NOW(),
            NOW()
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trg_queue_mps_g_code_update_downstream_generate_osl_fields ON t_mps;

CREATE TRIGGER trg_queue_mps_g_code_update_downstream_generate_osl_fields
AFTER UPDATE OF g_code ON t_mps
FOR EACH ROW
EXECUTE FUNCTION fn_queue_mps_g_code_update_downstream_generate_osl_fields();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger trg_queue_mps_g_code_update_downstream_generate_osl_fields has been created.';
END $$;
