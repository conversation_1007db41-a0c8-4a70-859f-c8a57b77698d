// generateDiscLabels.js
// Script to generate disc labels based on specified criteria
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import fs from 'fs';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';

dotenv.config();

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('ids', {
    describe: 'Comma-separated list of disc IDs to generate labels for',
    type: 'string'
  })
  .option('limit', {
    describe: 'Maximum number of labels to generate',
    type: 'number',
    default: 500
  })
  .option('queryType', {
    describe: 'Type of query to run (ready, pending, all)',
    type: 'string',
    default: 'ready'
  })
  .help()
  .alias('help', 'h')
  .argv;

// Label dimensions in points (1 mm = 2.835 points)
const LABEL_WIDTH = 67 * 2.835;  // ~190.5 points
const LABEL_HEIGHT = 25 * 2.835; // ~70.87 points

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or key in environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Helper function to wrap text
function wrapText(text, maxChars) {
  if (!text) return [''];

  const words = text.split(' ');
  const lines = [];
  let currentLine = '';

  words.forEach(word => {
    if ((currentLine + word).length <= maxChars) {
      currentLine += (currentLine ? ' ' : '') + word;
    } else {
      lines.push(currentLine);
      currentLine = word;
    }
  });

  if (currentLine) {
    lines.push(currentLine);
  }

  return lines;
}

async function generatePDF() {
  console.log('🔍 Querying Supabase for disc records...');

  let query = supabase.from('v_ready_disc_tags').select('*');

  // If specific IDs were provided, use them
  if (argv.ids) {
    const discIds = argv.ids.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
    if (discIds.length > 0) {
      console.log(`Using specific disc IDs: ${discIds.join(', ')}`);
      query = query.in('id', discIds);
    }
  } else {
    // Otherwise, use the queryType to determine which discs to include
    if (argv.queryType === 'ready') {
      console.log('Querying discs that are ready for labels');
      query = query.is('tag_printed_at', null);
    } else if (argv.queryType === 'pending') {
      console.log('Querying discs with pending labels');
      query = query.not('tag_printed_at', 'is', null);
    }
    // If queryType is 'all', we don't add any filters

    // Apply the limit
    query = query.limit(argv.limit);
  }

  // Execute the query
  const { data: discTags, error } = await query;

  if (error) {
    console.error(`❌ Error querying discs: ${error.message}`);
    process.exit(1);
  }

  if (!discTags || discTags.length === 0) {
    console.log('ℹ️ No disc records found that match the criteria.');
    process.exit(0);
  }

  console.log(`✅ Retrieved ${discTags.length} disc records.`);

  // Create the PDF document
  const pdfDoc = await PDFDocument.create();
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  // Font sizes and spacing
  const row1Size = 10; // Price & Speed
  const row2Size = 9;  // Title
  const row3Size = 8;  // ID & date
  const spacing = 2;   // vertical spacing between rows

  // Process each disc and create a label
  discTags.forEach((tag) => {
    const page = pdfDoc.addPage([LABEL_WIDTH, LABEL_HEIGHT]);

    // --- ROW 1: Price and Speed ---
    const row1Text = `${tag.price}  Speed: ${tag.speed}`;
    const row1Width = font.widthOfTextAtSize(row1Text, row1Size);

    // --- ROW 2: Title ---
    // Attempt a segmented approach if " - " is present.
    // Otherwise, just draw the entire title in one line or wrap.
    let segmented = false;
    let segmentedParts = [];
    let segmentedTotalWidth = 0;
    const separator = " - ";

    if (tag.title && tag.title.includes(separator)) {
      const parts = tag.title.split(separator);
      // Expecting brand - plastic - mold - stamp - color - weight
      if (parts.length >= 6) {
        segmented = true;
        segmentedParts = parts;
        // Calculate total width (bold font for the 3rd part).
        segmentedParts.forEach((part, idx) => {
          const currentFont = (idx === 2) ? boldFont : font; // bold mold
          segmentedTotalWidth += currentFont.widthOfTextAtSize(part, row2Size);
          if (idx < segmentedParts.length - 1) {
            segmentedTotalWidth += font.widthOfTextAtSize(separator, row2Size);
          }
        });
      }
    }

    // If not segmented or too few segments, fallback to wrapping the title.
    let row2Lines = [];
    if (!segmented && tag.title) {
      row2Lines = wrapText(tag.title, 40);
    }

    // Calculate row2 height based on whether it's segmented or wrapped
    const row2Height = segmented ? row2Size : (row2Lines.length * row2Size + (row2Lines.length - 1) * spacing);

    // --- ROW 3: ID, Date, and Manufacturing Weight ---
    const formattedDate = new Date().toLocaleDateString();
    const mfgWeightText = tag.weight_mfg ? ` - mfgwt: ${tag.weight_mfg}` : '';
    const row3Text = `ID: ${tag.id} - ${formattedDate}${mfgWeightText}`;
    const row3Width = boldFont.widthOfTextAtSize(row3Text, row3Size);

    // --- Calculate total block height ---
    const totalHeight = row1Size + spacing + row2Height + spacing + row3Size;

    // Start Y so the block is vertically centered, then shift down ~3 points
    const startY = (LABEL_HEIGHT + totalHeight) / 2 - 3;

    // --- Draw Row 1 (centered) ---
    const row1X = (LABEL_WIDTH - row1Width) / 2;
    const row1Y = startY;
    page.drawText(row1Text, {
      x: row1X,
      y: row1Y,
      size: row1Size,
      font,
      color: rgb(0, 0, 0),
    });

    // --- Draw Row 2 ---
    let row2Y = row1Y - row1Size - spacing;
    if (segmented) {
      // Draw in one line, using bold for the 3rd part (mold).
      let currentX = (LABEL_WIDTH - segmentedTotalWidth) / 2;
      segmentedParts.forEach((part, idx) => {
        const currentFont = (idx === 2) ? boldFont : font;
        page.drawText(part, {
          x: currentX,
          y: row2Y,
          size: row2Size,
          font: currentFont,
          color: rgb(0, 0, 0),
        });
        currentX += currentFont.widthOfTextAtSize(part, row2Size);
        if (idx < segmentedParts.length - 1) {
          page.drawText(separator, {
            x: currentX,
            y: row2Y,
            size: row2Size,
            font,
            color: rgb(0, 0, 0),
          });
          currentX += font.widthOfTextAtSize(separator, row2Size);
        }
      });
    } else {
      // Wrap mode
      row2Lines.forEach((line) => {
        const lineWidth = font.widthOfTextAtSize(line, row2Size);
        const lineX = (LABEL_WIDTH - lineWidth) / 2;
        page.drawText(line, {
          x: lineX,
          y: row2Y,
          size: row2Size,
          font,
          color: rgb(0, 0, 0),
        });
        row2Y -= (row2Size + spacing);
      });
    }

    // --- Draw Row 3 in bold, centered ---
    const row3Y = segmented ? (row2Y - row2Size - spacing) : (row2Y - spacing);
    const row3X = (LABEL_WIDTH - row3Width) / 2;
    page.drawText(row3Text, {
      x: row3X,
      y: row3Y,
      size: row3Size,
      font: boldFont,
      color: rgb(0, 0, 0),
    });
  });

  // Save PDF with date-stamped filename
  const pdfBytes = await pdfDoc.save();
  const now = new Date();
  const dateStamp = now.toISOString().replace(/:/g, "-").slice(0, 19);
  const fileName = `disc_labels_${dateStamp}.pdf`;

  fs.writeFileSync(fileName, pdfBytes);
  console.log(`✅ Disc labels generated: ${fileName}`);

  // Update t_discs.tag_printed_at for all IDs in discTags
  if (argv.queryType !== 'pending') {  // Only update if not in 'pending' mode
    const discIds = discTags.map(d => d.id);
    if (discIds.length > 0) {
      console.log(`🔄 Updating tag_printed_at for ${discIds.length} discs...`);

      const { error: updateError } = await supabase
        .from('t_discs')
        .update({ tag_printed_at: new Date().toISOString() })
        .in('id', discIds);

      if (updateError) {
        console.error(`❌ Failed to update tag_printed_at: ${updateError.message}`);
      } else {
        console.log(`✅ tag_printed_at updated for ${discIds.length} discs.`);
      }
    }
  }
}

// Run the script
generatePDF().catch(err => {
  console.error(`❌ Error generating disc labels: ${err.message}`);
  console.error(err.stack);
  process.exit(1);
});
