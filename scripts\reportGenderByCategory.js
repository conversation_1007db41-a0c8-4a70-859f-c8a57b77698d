import fs from 'fs';
import path from 'path';
import Papa from 'papaparse';

const FILE_PATH = path.join(process.cwd(), 'data', 'external data', 'access', 'tAccessories.txt');

function parseTsv(filePath) {
  const raw = fs.readFileSync(filePath, 'utf-8');
  const { data } = Papa.parse(raw, {
    header: true,
    skipEmptyLines: true,
    delimiter: '\t',
    dynamicTyping: false,
  });
  return data;
}

function toInt(v){ const n = Number(String(v||'').trim()); return Number.isFinite(n) ? Math.trunc(n) : null; }

function main(){
  if (!fs.existsSync(FILE_PATH)) { console.error('Not found:', FILE_PATH); process.exit(1); }
  const rows = parseTsv(FILE_PATH);

  // Look for CategoryID == 6 (legacy for Bags) with non-empty Gender
  const legacyCat = 6;
  const matches = rows.filter(r => toInt(r['CategoryID']) === legacyCat && String(r['Gender']||'').trim() !== '');

  console.log(`Rows with CategoryID=${legacyCat} (Bags legacy) AND non-empty Gender: ${matches.length}`);
  for (const r of matches.slice(0, 50)) {
    console.log(`AccessoryID=${r['AccessoryID']}  Title="${(r['Title']||'').toString().trim()}"  Gender="${(r['Gender']||'').toString().trim()}"`);
  }
  if (matches.length > 50) console.log(`... and ${matches.length - 50} more`);

  // Also show the two variant IDs we saw in logs: 38 and 40 (if present)
  const idsToInspect = new Set(['38','40']);
  const inspected = rows.filter(r => idsToInspect.has(String(r['AccessoryID']).trim()));
  if (inspected.length) {
    console.log('\nSpecific check for AccessoryID 38 and 40:');
    for (const r of inspected) {
      console.log(`AccessoryID=${r['AccessoryID']}  CategoryID=${r['CategoryID']}  Title="${(r['Title']||'').toString().trim()}"  Gender="${(r['Gender']||'').toString().trim()}"  AgeGroup="${(r['Age Group']||'').toString().trim()}"`);
    }
  }
}

main();

