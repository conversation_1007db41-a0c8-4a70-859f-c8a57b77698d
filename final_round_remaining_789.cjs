require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function finalRoundRemaining789() {
  try {
    console.log('Final round: Processing remaining 789 discs with null vendor_osl_id...');
    
    // Get current count to verify
    const { count: currentNullCount, error: countError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (countError) {
      console.error('Error getting count:', countError);
      return;
    }
    
    console.log(`Current discs with null vendor_osl_id: ${currentNullCount}`);
    
    if (currentNullCount === 0) {
      console.log('✅ No discs remaining to process!');
      return;
    }
    
    // Process ALL remaining discs in smaller batches for better tracking
    let totalProcessed = 0;
    let totalUpdated = 0;
    let totalErrors = 0;
    let totalNoMatch = 0;
    const batchSize = 50; // Smaller batches for final round
    
    console.log(`\nProcessing ${currentNullCount} remaining discs in batches of ${batchSize}...`);
    console.log('This final round will be extra thorough to catch any remaining matches.\n');
    
    while (totalProcessed < currentNullCount) {
      const batchNumber = Math.floor(totalProcessed / batchSize) + 1;
      console.log(`Final Round - Batch ${batchNumber}:`);
      
      // Get next batch of discs with null vendor_osl_id
      const { data: discs, error: batchError } = await supabase
        .from('t_discs')
        .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
        .is('vendor_osl_id', null)
        .not('weight_mfg', 'is', null)
        .not('mps_id', 'is', null)
        .not('color_id', 'is', null)
        .range(0, batchSize - 1);  // Always get first batch since we're updating them
      
      if (batchError) {
        console.error('Error getting batch:', batchError);
        break;
      }
      
      if (!discs || discs.length === 0) {
        console.log('  No more discs to process');
        break;
      }
      
      console.log(`  Processing ${discs.length} discs...`);
      
      let batchUpdated = 0;
      let batchErrors = 0;
      let batchNoMatch = 0;
      
      for (const disc of discs) {
        try {
          // Test the vendor OSL function for this disc
          const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
            'find_matching_osl_by_mfg_weight',
            {
              mps_id_param: disc.mps_id,
              color_id_param: disc.color_id,
              weight_mfg_param: disc.weight_mfg
            }
          );
          
          if (vendorOslError) {
            console.error(`    Error finding vendor OSL for disc ${disc.id}:`, vendorOslError);
            batchErrors++;
            totalErrors++;
            continue;
          }
          
          const vendorOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
          
          if (vendorOslId) {
            // This disc SHOULD have a vendor OSL mapping!
            console.log(`    🎯 FINAL MATCH FOUND: Disc ${disc.id} → OSL ${vendorOslId}`);
            
            // Show disc details for this final round
            console.log(`      MPS: ${disc.mps_id}, Weight: ${disc.weight}g, Weight MFG: ${disc.weight_mfg}g, Color: ${disc.color_id}`);
            
            // Update the disc with vendor_osl_id
            const { error: updateError } = await supabase
              .from('t_discs')
              .update({ vendor_osl_id: vendorOslId })
              .eq('id', disc.id);
            
            if (updateError) {
              console.error(`      Error updating disc ${disc.id}:`, updateError);
              batchErrors++;
              totalErrors++;
            } else {
              batchUpdated++;
              totalUpdated++;
              
              // Show mapping comparison
              if (vendorOslId !== disc.order_sheet_line_id) {
                console.log(`      Different mappings: regular OSL ${disc.order_sheet_line_id}, vendor OSL ${vendorOslId}`);
              } else {
                console.log(`      Same OSL for both mappings: ${vendorOslId}`);
              }
            }
          } else {
            // No match found - this is expected for some discs
            batchNoMatch++;
            totalNoMatch++;
            
            // For final round, let's show why there's no match for a few examples
            if (batchNoMatch <= 3) {
              console.log(`    ❌ No match: Disc ${disc.id} (MPS: ${disc.mps_id}, Weight MFG: ${disc.weight_mfg}g, Color: ${disc.color_id})`);
              
              // Show available OSLs for this disc
              const { data: availableOsls, error: oslError } = await supabase
                .from('t_order_sheet_lines')
                .select('id, min_weight, max_weight, color_id')
                .eq('mps_id', disc.mps_id)
                .in('color_id', [disc.color_id, 23])
                .limit(3);
              
              if (!oslError && availableOsls && availableOsls.length > 0) {
                const roundedWeight = Math.round(disc.weight_mfg);
                console.log(`      Available OSLs: ${availableOsls.map(osl => 
                  `${osl.id}(${osl.min_weight}-${osl.max_weight}g,color${osl.color_id})`
                ).join(', ')}`);
                console.log(`      Rounded weight ${roundedWeight}g doesn't fit any range`);
              } else {
                console.log(`      No OSLs available for MPS ${disc.mps_id} with colors ${disc.color_id} or 23`);
              }
            }
          }
          
          totalProcessed++;
          
        } catch (err) {
          console.error(`    Error processing disc ${disc.id}:`, err.message);
          batchErrors++;
          totalErrors++;
          totalProcessed++;
        }
      }
      
      console.log(`  Batch ${batchNumber} complete: ${batchUpdated} updated, ${batchNoMatch} no match, ${batchErrors} errors`);
      console.log(`  Running totals: ${totalUpdated} updated, ${totalNoMatch} no match, ${totalErrors} errors, ${totalProcessed} processed\n`);
      
      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log('\n=== FINAL ROUND COMPLETE ===');
    console.log(`Total discs processed: ${totalProcessed}`);
    console.log(`Additional matches found and updated: ${totalUpdated}`);
    console.log(`Discs with no vendor OSL match: ${totalNoMatch}`);
    console.log(`Errors encountered: ${totalErrors}`);
    
    if (totalUpdated > 0) {
      console.log(`\n🎉 FINAL SUCCESS! Found and updated ${totalUpdated} additional discs in this final round!`);
      
      // Get final statistics
      const { count: finalVendorOslCount, error: finalError } = await supabase
        .from('t_discs')
        .select('*', { count: 'exact', head: true })
        .not('vendor_osl_id', 'is', null);
      
      const { count: finalNullCount, error: finalNullError } = await supabase
        .from('t_discs')
        .select('*', { count: 'exact', head: true })
        .is('vendor_osl_id', null)
        .not('weight_mfg', 'is', null)
        .not('mps_id', 'is', null)
        .not('color_id', 'is', null);
      
      if (!finalError && !finalNullError) {
        console.log(`\n📊 FINAL STATISTICS AFTER ALL ROUNDS:`);
        console.log(`Total discs with vendor_osl_id: ${finalVendorOslCount}`);
        console.log(`Remaining discs with null vendor_osl_id: ${finalNullCount}`);
        console.log(`This final round improvement: +${totalUpdated} new vendor mappings`);
      }
      
      console.log('\n✅ The dual mapping system is now as complete as possible!');
    } else {
      console.log('\n✅ No additional matches found - all possible vendor mappings have been created.');
      console.log(`The remaining ${totalNoMatch} discs correctly have no vendor OSL matches.`);
    }
    
    // Summary of all rounds
    console.log('\n🎯 COMPLETE DUAL MAPPING SYSTEM SUMMARY:');
    console.log('Round 1: Populated weight_mfg for 60,013 discs');
    console.log('Round 2: Found 46,206 initial vendor mappings');
    console.log('Round 3: Found 9,585 additional vendor mappings in comprehensive recheck');
    console.log(`Round 4: Found ${totalUpdated} final vendor mappings in this final round`);
    console.log(`\nTotal vendor mappings created: ${46206 + 9585 + totalUpdated}`);
    console.log('🚀 Dual mapping system implementation complete!');
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

finalRoundRemaining789();
