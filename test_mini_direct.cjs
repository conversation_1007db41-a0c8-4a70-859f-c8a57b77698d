require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testMiniMoldLogicDirect() {
  try {
    console.log('Testing mini mold logic directly (simulating adminServer logic)...\n');
    
    // Step 1: Get all mini molds
    console.log('Step 1: Getting all mini molds...');
    const { data: miniMolds, error: moldsError } = await supabase
      .from('t_molds')
      .select('id')
      .ilike('mold', '%mini%');
    
    if (moldsError) {
      console.error('Error fetching mini molds:', moldsError);
      return;
    }
    
    console.log(`Found ${miniMolds.length} mini mold types`);
    
    if (miniMolds.length === 0) {
      console.log('No mini molds found, stopping test');
      return;
    }
    
    // Step 2: Get MPS records for these molds
    console.log('\nStep 2: Getting MPS records for mini molds...');
    const moldIds = miniMolds.map(m => m.id);
    const { data: mpsRecords, error: mpsError } = await supabase
      .from('t_mps')
      .select('id, mold_id')
      .in('mold_id', moldIds);
    
    if (mpsError) {
      console.error('Error fetching MPS records:', mpsError);
      return;
    }
    
    console.log(`Found ${mpsRecords.length} MPS records for mini molds`);
    
    if (mpsRecords.length === 0) {
      console.log('No MPS records found, stopping test');
      return;
    }
    
    // Step 3: Get OSL records for these MPS
    console.log('\nStep 3: Getting OSL records for mini molds...');
    const mpsIds = mpsRecords.map(m => m.id);
    const { data: oslRecords, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, g_code')
      .in('mps_id', mpsIds);
    
    if (oslError) {
      console.error('Error fetching OSL records:', oslError);
      return;
    }
    
    console.log(`Found ${oslRecords.length} OSL records for mini molds`);
    
    if (oslRecords.length === 0) {
      console.log('No OSL records found, stopping test');
      return;
    }
    
    // Step 4: Get disc records for these OSLs in back stock
    console.log('\nStep 4: Getting disc records in back stock...');
    const oslIds = oslRecords.map(o => o.id);
    const { data: discRecords, error: discError } = await supabase
      .from('t_discs')
      .select(`
        id,
        g_pull,
        grade,
        color_id,
        order_sheet_line_id,
        location,
        sold_date,
        t_order_sheet_lines!t_discs_order_sheet_line_id_fkey(
          id,
          g_code,
          mps_id,
          t_mps!inner(
            id,
            mold_id,
            release_date_online,
            created_at,
            t_molds!inner(
              id,
              mold
            )
          )
        )
      `)
      .in('order_sheet_line_id', oslIds)
      .eq('location', 'BS')
      .is('sold_date', null);
    
    if (discError) {
      console.error('Error fetching disc records:', discError);
      return;
    }
    
    console.log(`Found ${discRecords.length} mini mold discs in back stock:`);
    
    if (discRecords.length > 0) {
      discRecords.forEach(disc => {
        const moldName = disc.t_order_sheet_lines.t_mps.t_molds.mold;
        console.log(`- ID ${disc.id}: ${disc.g_pull} (${moldName})`);
        console.log(`  OSL: ${disc.t_order_sheet_lines.g_code}, Location: ${disc.location}`);
      });
      
      console.log(`\n🎉 SUCCESS! Found ${discRecords.length} mini mold discs in back stock that should be selected for B2F!`);
      
      // Show the specific discs we know about
      const knownMiniDiscs = discRecords.filter(d => [428958, 428956].includes(d.id));
      if (knownMiniDiscs.length > 0) {
        console.log('\n✅ Found the known mini mold discs:');
        knownMiniDiscs.forEach(disc => {
          const moldName = disc.t_order_sheet_lines.t_mps.t_molds.mold;
          console.log(`- ID ${disc.id}: ${moldName} (${disc.g_pull})`);
        });
      }
    } else {
      console.log('❌ No mini mold discs found in back stock');
    }
    
  } catch (err) {
    console.error('Exception:', err);
  }
}

testMiniMoldLogicDirect();
