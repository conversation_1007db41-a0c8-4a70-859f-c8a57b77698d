const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkColumns() {
  console.log('🔍 Checking Veeqo table columns...');
  
  try {
    // Get a sample record to see available columns
    const { data, error } = await supabase
      .from('imported_table_veeqo_sellables_export')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ Error:', error.message);
      return;
    }
    
    if (data && data.length > 0) {
      console.log('✅ Available columns:');
      Object.keys(data[0]).forEach(key => {
        console.log(`   - ${key}: ${typeof data[0][key]} = ${data[0][key]}`);
      });
    } else {
      console.log('❌ No data found in table');
    }
    
  } catch (err) {
    console.error('❌ Exception:', err.message);
  }
}

checkColumns();
