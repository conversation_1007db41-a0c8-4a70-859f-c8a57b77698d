// fix_task_173676.js
// Update task 173676 result with the correct error message from OSL 18533

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function fixTask173676() {
  try {
    console.log('Fixing task 173676 result with correct error message from OSL 18533...');
    
    // Get the current task result
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 173676)
      .single();
    
    if (taskError) {
      console.error('Error fetching task:', taskError);
      return;
    }
    
    console.log('Current task result:', JSON.stringify(task.result, null, 2));
    
    // Get the error message from OSL 18533
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('shopify_product_uploaded_notes')
      .eq('id', 18533)
      .single();
    
    if (oslError) {
      console.error('Error fetching OSL:', oslError);
      return;
    }
    
    if (!osl || !osl.shopify_product_uploaded_notes) {
      console.log('No error message found in OSL notes');
      return;
    }
    
    console.log('OSL error message:', osl.shopify_product_uploaded_notes);
    
    // Update the task result with the correct error message
    const currentResult = typeof task.result === 'string' ? JSON.parse(task.result) : task.result;
    const updatedResult = {
      ...currentResult,
      error: osl.shopify_product_uploaded_notes,
      error_source: 'extracted_from_database',
      fixed_at: new Date().toISOString()
    };
    
    const { error: updateError } = await supabase
      .from('t_task_queue')
      .update({ result: updatedResult })
      .eq('id', 173676);
    
    if (updateError) {
      console.error('Error updating task:', updateError);
      return;
    }
    
    console.log('✅ Successfully updated task 173676 with correct error message');
    console.log('New result error field:', updatedResult.error);
    
    console.log('\n🎯 Summary:');
    console.log('- Task 173676 now shows the detailed MANUAL FIX REQUIRED error message');
    console.log('- The error explains exactly what needs to be fixed');
    console.log('- You can now take action based on the clear error message');
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

fixTask173676();
