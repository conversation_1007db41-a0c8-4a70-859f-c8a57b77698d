import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkFundraiserFields() {
    try {
        console.log('🔍 Checking fundraiser fields in detail...\n');
        
        // Check all fields for rows 22, 25, 28
        const { data: fundraiserData, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*')
            .in('excel_row_hint', [22, 25, 28])
            .order('excel_row_hint, excel_column');

        if (error) {
            console.error('❌ Error querying fundraiser data:', error);
            return;
        }

        console.log(`✅ Found ${fundraiserData.length} records in rows 22, 25, 28`);
        
        fundraiserData.forEach((record, index) => {
            console.log(`\n--- Record ${index + 1} ---`);
            console.log(`Row: ${record.excel_row_hint}, Column: ${record.excel_column}`);
            console.log(`Mold: "${record.mold_name}"`);
            console.log(`Plastic: "${record.plastic_name}"`);
            console.log(`Stamp: "${record.stamp_name}"`);
            console.log(`Vendor Description: "${record.vendor_description}"`);
            console.log(`Raw Line Type: "${record.raw_line_type}"`);
            console.log(`Raw Model: "${record.raw_model}"`);
            console.log(`Excel Mapping Key: "${record.excel_mapping_key}"`);
            console.log(`Is Orderable: ${record.is_orderable}`);
        });

        // Check specifically for any records containing "Ben Askren"
        console.log('\n🔍 Searching for any records containing "Ben Askren"...');
        const { data: benAskrenData, error: benError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, vendor_description, raw_line_type, raw_model')
            .or('vendor_description.ilike.%Ben Askren%,raw_line_type.ilike.%Ben Askren%,raw_model.ilike.%Ben Askren%');

        if (benError) {
            console.error('❌ Error searching for Ben Askren:', benError);
        } else {
            console.log(`✅ Found ${benAskrenData.length} records containing "Ben Askren"`);
            benAskrenData.forEach((record, index) => {
                console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}`);
                console.log(`      Vendor Desc: "${record.vendor_description}"`);
                console.log(`      Raw Line: "${record.raw_line_type}"`);
                console.log(`      Raw Model: "${record.raw_model}"`);
            });
        }

        console.log('\n🎉 Field check completed!');
        
    } catch (error) {
        console.error('❌ Check failed:', error.message);
    }
}

checkFundraiserFields().catch(console.error);
