require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function debugTask302684() {
  try {
    console.log('Debugging task ID 302684...');
    
    // Get the task details
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 302684)
      .single();
    
    if (taskError) {
      console.error('Error getting task:', taskError);
      return;
    }
    
    console.log('\n=== TASK 302684 DETAILS ===');
    console.log(`Task Type: ${task.task_type}`);
    console.log(`Status: ${task.status}`);
    console.log(`Payload:`, task.payload);
    console.log(`Enqueued By: ${task.enqueued_by}`);
    console.log(`Created At: ${task.created_at}`);
    console.log(`Scheduled At: ${task.scheduled_at}`);
    console.log(`Error:`, task.result?.error || 'None');
    console.log(`Message:`, task.result?.message || 'None');
    
    // If it's a check_if_disc_is_ready task, check the disc
    if (task.task_type === 'check_if_disc_is_ready') {
      const discId = task.payload.id;
      console.log(`\n=== DISC ${discId} DETAILS ===`);
      
      // Get disc details
      const { data: disc, error: discError } = await supabase
        .from('t_discs')
        .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date, ready_new, image_verified, ready_button, shopify_uploaded_at')
        .eq('id', discId)
        .single();
      
      if (discError) {
        console.error('Error getting disc:', discError);
        return;
      }
      
      console.log(`MPS ID: ${disc.mps_id}`);
      console.log(`Weight: ${disc.weight}g`);
      console.log(`Weight MFG: ${disc.weight_mfg}g`);
      console.log(`Color ID: ${disc.color_id}`);
      console.log(`Order Sheet Line ID: ${disc.order_sheet_line_id}`);
      console.log(`Vendor OSL ID: ${disc.vendor_osl_id}`);
      console.log(`Sold Date: ${disc.sold_date || 'NULL (unsold)'}`);
      console.log(`Ready New: ${disc.ready_new}`);
      console.log(`Image Verified: ${disc.image_verified}`);
      console.log(`Ready Button: ${disc.ready_button}`);
      console.log(`Shopify Uploaded At: ${disc.shopify_uploaded_at || 'NULL'}`);
      
      // Test the problematic query that was causing issues
      console.log('\n=== TESTING PROBLEMATIC QUERY ===');
      console.log('Testing the old query that caused relationship ambiguity...');
      
      const { data: testOldQuery, error: oldQueryError } = await supabase
        .from('t_discs')
        .select('*, t_order_sheet_lines!left(shopify_uploaded_at, todo)')
        .eq('id', discId);
      
      if (oldQueryError) {
        console.log('❌ Old query still fails:', oldQueryError.message);
      } else {
        console.log('✅ Old query now works (unexpected!)');
      }
      
      // Test the fixed query
      console.log('\nTesting the fixed query...');
      const { data: testFixedQuery, error: fixedQueryError } = await supabase
        .from('t_discs')
        .select('*, t_order_sheet_lines!order_sheet_line_id(shopify_uploaded_at, todo)')
        .eq('id', discId);
      
      if (fixedQueryError) {
        console.log('❌ Fixed query fails:', fixedQueryError.message);
      } else {
        console.log('✅ Fixed query works');
        console.log('OSL data:', testFixedQuery[0]?.t_order_sheet_lines);
      }
      
      // Check if this disc has both relationships
      if (disc.order_sheet_line_id && disc.vendor_osl_id) {
        console.log('\n=== DUAL RELATIONSHIP CHECK ===');
        console.log(`This disc has BOTH relationships:`);
        console.log(`- Regular OSL: ${disc.order_sheet_line_id}`);
        console.log(`- Vendor OSL: ${disc.vendor_osl_id}`);
        
        if (disc.order_sheet_line_id === disc.vendor_osl_id) {
          console.log('✅ Both relationships point to the SAME OSL');
        } else {
          console.log('⚠️ Relationships point to DIFFERENT OSLs');
          
          // Get details of both OSLs
          const { data: regularOsl, error: regOslError } = await supabase
            .from('t_order_sheet_lines')
            .select('id, mps_id, min_weight, max_weight, color_id, shopify_uploaded_at, todo')
            .eq('id', disc.order_sheet_line_id)
            .single();
          
          const { data: vendorOsl, error: vendorOslError } = await supabase
            .from('t_order_sheet_lines')
            .select('id, mps_id, min_weight, max_weight, color_id, shopify_uploaded_at, todo')
            .eq('id', disc.vendor_osl_id)
            .single();
          
          if (!regOslError && !vendorOslError) {
            console.log(`Regular OSL ${disc.order_sheet_line_id}: MPS ${regularOsl.mps_id}, ${regularOsl.min_weight}-${regularOsl.max_weight}g, color ${regularOsl.color_id}, uploaded: ${regularOsl.shopify_uploaded_at ? 'Yes' : 'No'}`);
            console.log(`Vendor OSL ${disc.vendor_osl_id}: MPS ${vendorOsl.mps_id}, ${vendorOsl.min_weight}-${vendorOsl.max_weight}g, color ${vendorOsl.color_id}, uploaded: ${vendorOsl.shopify_uploaded_at ? 'Yes' : 'No'}`);
          }
        }
      }
      
      // Check if there are any other similar tasks that might be causing batch processing issues
      console.log('\n=== RELATED TASKS CHECK ===');
      const { data: relatedTasks, error: relatedError } = await supabase
        .from('t_task_queue')
        .select('id, status, created_at, payload')
        .eq('task_type', 'check_if_disc_is_ready')
        .eq('status', 'error')
        .contains('result', { error: 'Could not embed because more than one relationship was found' })
        .order('created_at', { ascending: false })
        .limit(10);
      
      if (!relatedError && relatedTasks) {
        console.log(`Found ${relatedTasks.length} other tasks with the same relationship error:`);
        relatedTasks.forEach(relatedTask => {
          console.log(`- Task ${relatedTask.id}: Disc ${relatedTask.payload.id}, Created: ${relatedTask.created_at}`);
        });
      }
    }
    
    // Check if this is a different task type
    if (task.task_type !== 'check_if_disc_is_ready') {
      console.log('\n=== NON-CHECK_IF_DISC_IS_READY TASK ===');
      console.log('This task is not a check_if_disc_is_ready task.');
      console.log('The relationship ambiguity error might be in a different part of the code.');
      
      // Look for other places in the codebase that might have similar queries
      console.log('This suggests there might be other queries in the codebase that need the same fix.');
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

debugTask302684();
