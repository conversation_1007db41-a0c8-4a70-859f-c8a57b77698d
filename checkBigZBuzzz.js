import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkBigZBuzzz() {
    try {
        console.log('🔍 Checking Big Z Collection Buzzz MPS records...\n');
        
        // Check if there's a Big Z Collection plastic
        const { data: bigZPlastics, error: plasticError } = await supabase
            .from('t_plastics')
            .select('id, plastic')
            .eq('brand_id', 6)
            .ilike('plastic', '%Big Z%');

        if (plasticError) {
            console.error('❌ Error checking plastics:', plasticError);
            return;
        }

        console.log(`✅ Found ${bigZPlastics.length} Big Z plastics:`);
        bigZPlastics.forEach(plastic => {
            console.log(`   ID ${plastic.id}: "${plastic.plastic}"`);
        });

        // Check if there's a Buzzz mold
        const { data: buzzzMolds, error: moldError } = await supabase
            .from('t_molds')
            .select('id, mold')
            .eq('brand_id', 6)
            .ilike('mold', '%Buzzz%');

        if (moldError) {
            console.error('❌ Error checking molds:', moldError);
            return;
        }

        console.log(`\n✅ Found ${buzzzMolds.length} Buzzz molds:`);
        buzzzMolds.forEach(mold => {
            console.log(`   ID ${mold.id}: "${mold.mold}"`);
        });

        // Check if there's the fundraiser stamp
        const { data: fundraiserStamps, error: stampError } = await supabase
            .from('t_stamps')
            .select('id, stamp')
            .eq('brand_id', 6)
            .ilike('stamp', '%Ben Askren%');

        if (stampError) {
            console.error('❌ Error checking stamps:', stampError);
            return;
        }

        console.log(`\n✅ Found ${fundraiserStamps.length} Ben Askren stamps:`);
        fundraiserStamps.forEach(stamp => {
            console.log(`   ID ${stamp.id}: "${stamp.stamp}"`);
        });

        // Check existing MPS records for Big Z Collection + Buzzz
        if (bigZPlastics.length > 0 && buzzzMolds.length > 0) {
            const { data: existingMps, error: mpsError } = await supabase
                .from('t_mps')
                .select('id, t_plastics(plastic), t_molds(mold), t_stamps(stamp)')
                .eq('plastic_id', bigZPlastics[0].id)
                .eq('mold_id', buzzzMolds[0].id);

            if (mpsError) {
                console.error('❌ Error checking existing MPS:', mpsError);
                return;
            }

            console.log(`\n✅ Found ${existingMps.length} existing MPS records for Big Z Collection + Buzzz:`);
            existingMps.forEach(mps => {
                console.log(`   ID ${mps.id}: ${mps.t_plastics.plastic} | ${mps.t_molds.mold} | ${mps.t_stamps.stamp}`);
            });

            // If we have the plastic, mold, and stamp, we can create the MPS record
            if (fundraiserStamps.length > 0) {
                console.log('\n🔧 All components available - checking if MPS record exists...');
                
                const { data: specificMps, error: specificError } = await supabase
                    .from('t_mps')
                    .select('id')
                    .eq('plastic_id', bigZPlastics[0].id)
                    .eq('mold_id', buzzzMolds[0].id)
                    .eq('stamp_id', fundraiserStamps[0].id);

                if (specificError) {
                    console.error('❌ Error checking specific MPS:', specificError);
                    return;
                }

                if (specificMps.length === 0) {
                    console.log('❌ MPS record does not exist - would need to be created');
                    console.log(`   Plastic ID: ${bigZPlastics[0].id} (${bigZPlastics[0].plastic})`);
                    console.log(`   Mold ID: ${buzzzMolds[0].id} (${buzzzMolds[0].mold})`);
                    console.log(`   Stamp ID: ${fundraiserStamps[0].id} (${fundraiserStamps[0].stamp})`);
                } else {
                    console.log(`✅ MPS record already exists: ID ${specificMps[0].id}`);
                }
            }
        }

        console.log('\n🎉 Big Z Buzzz check completed!');
        
    } catch (error) {
        console.error('❌ Check failed:', error.message);
    }
}

checkBigZBuzzz().catch(console.error);
