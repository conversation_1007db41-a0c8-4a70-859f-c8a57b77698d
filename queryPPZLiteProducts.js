import http from 'http';

function queryPPZLiteProducts() {
  // Make a request to get Discraft status which includes product counts
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/discraft/status',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const result = JSON.parse(data);
        console.log('Discraft status result:', JSON.stringify(result, null, 2));
        
        if (result.success) {
          console.log('\n📊 Import Statistics:');
          console.log(`Total imported products: ${result.totalImportedProducts || 'N/A'}`);
          console.log(`Products with MPS IDs: ${result.productsWithMpsIds || 'N/A'}`);
          console.log(`Products without MPS IDs: ${result.productsWithoutMpsIds || 'N/A'}`);
          
          if (result.plasticBreakdown) {
            console.log('\n🎯 By Plastic Type:');
            Object.entries(result.plasticBreakdown).forEach(([plastic, count]) => {
              console.log(`  ${plastic}: ${count} products`);
            });
          }
          
          if (result.moldBreakdown) {
            console.log('\n🥏 By Mold (Top 10):');
            Object.entries(result.moldBreakdown)
              .slice(0, 10)
              .forEach(([mold, count]) => {
                console.log(`  ${mold}: ${count} products`);
              });
          }
        }
        
      } catch (err) {
        console.log('Raw response:', data);
      }
    });
  });

  req.on('error', (err) => {
    console.error('Error:', err.message);
  });

  req.end();
}

console.log('Querying PP Z Lite products through Discraft status...');
queryPPZLiteProducts();
