// testSupabaseConnection.js - Script to test Supabase connection

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';

// Load environment variables
dotenv.config();

// Create a log file
const logFile = 'supabase_connection.log';
fs.writeFileSync(logFile, `Starting Supabase connection test at ${new Date().toISOString()}\n`);

// Log environment variables (without sensitive values)
fs.appendFileSync(logFile, `SUPABASE_URL is ${process.env.SUPABASE_URL ? 'set' : 'not set'}\n`);
fs.appendFileSync(logFile, `SUPABASE_KEY is ${process.env.SUPABASE_KEY ? 'set' : 'not set'}\n`);

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  const errorMsg = 'Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file';
  fs.appendFileSync(logFile, `${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  try {
    console.log('Testing connection to Supabase...');
    fs.appendFileSync(logFile, 'Testing connection to Supabase...\n');
    
    // Try to get the server version
    const { data, error } = await supabase
      .rpc('version');
    
    if (error) {
      const errorMsg = `Error connecting to Supabase: ${error.message}`;
      fs.appendFileSync(logFile, `${errorMsg}\n`);
      console.error(errorMsg);
      
      // Try a different query
      fs.appendFileSync(logFile, 'Trying a different query...\n');
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError) {
        const userErrorMsg = `Error getting user: ${userError.message}`;
        fs.appendFileSync(logFile, `${userErrorMsg}\n`);
        console.error(userErrorMsg);
      } else {
        fs.appendFileSync(logFile, `Successfully got user data\n`);
        console.log('Successfully got user data');
      }
      
      return;
    }
    
    const successMsg = `Successfully connected to Supabase! Server version: ${data}`;
    fs.appendFileSync(logFile, `${successMsg}\n`);
    console.log(successMsg);
    
    // Try to list tables
    fs.appendFileSync(logFile, 'Trying to list tables...\n');
    const { data: tablesData, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .limit(10);
    
    if (tablesError) {
      const tablesErrorMsg = `Error listing tables: ${tablesError.message}`;
      fs.appendFileSync(logFile, `${tablesErrorMsg}\n`);
      console.error(tablesErrorMsg);
    } else {
      fs.appendFileSync(logFile, `Successfully listed tables: ${JSON.stringify(tablesData)}\n`);
      console.log(`Successfully listed tables: ${JSON.stringify(tablesData)}`);
    }
  } catch (error) {
    const errorMsg = `Unexpected error: ${error.message}`;
    fs.appendFileSync(logFile, `${errorMsg}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(errorMsg);
    console.error(error.stack);
  }
}

// Run the test
testConnection()
  .then(() => {
    const msg = 'Test completed.';
    fs.appendFileSync(logFile, `${msg}\n`);
    console.log(msg);
  })
  .catch(error => {
    const errorMsg = `Unhandled error: ${error.message}`;
    fs.appendFileSync(logFile, `${errorMsg}\n`);
    console.error(errorMsg);
  });
