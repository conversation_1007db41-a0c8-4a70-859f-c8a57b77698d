// check_osl_18500_current.js
// Check the current status of OSL 18500

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkOSL18500Current() {
  try {
    console.log('Checking current status of OSL 18500...');
    
    const { data: osl, error } = await supabase
      .from('t_order_sheet_lines')
      .select('id, max_weight, ready_button, ready, shopify_uploaded_at, shopify_product_uploaded_notes')
      .eq('id', 18500)
      .single();
    
    if (error) {
      console.error('Error fetching OSL:', error);
      return;
    }
    
    console.log('OSL 18500 Current Status:');
    console.log('Max Weight:', osl.max_weight + 'g');
    console.log('Ready Button:', osl.ready_button);
    console.log('Ready:', osl.ready);
    console.log('Shopify Uploaded At:', osl.shopify_uploaded_at);
    console.log('Upload Notes:', osl.shopify_product_uploaded_notes);
    
    if (osl.shopify_uploaded_at) {
      console.log('\n✅ OSL 18500 is successfully uploaded to Shopify!');
      console.log('Task 173717 was actually successful, not an error.');
    } else {
      console.log('\n❌ OSL 18500 is not uploaded yet.');
    }
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

checkOSL18500Current();
