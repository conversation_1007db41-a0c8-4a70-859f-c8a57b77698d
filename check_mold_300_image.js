import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkMold300Image() {
  try {
    console.log('🔍 Checking Mold 300 (CD2) Image Configuration...\n');
    
    // Get current image configuration
    const { data: publicServerData, error: publicServerError } = await supabase
      .from('t_config')
      .select('value')
      .eq('key', 'public_image_server')
      .single();

    const { data: folderData, error: folderError } = await supabase
      .from('t_config')
      .select('value')
      .eq('key', 'folder_molds')
      .single();

    console.log('=== IMAGE CONFIGURATION ===');
    if (publicServerError) {
      console.log('❌ Error getting public_image_server:', publicServerError);
    } else {
      console.log('✅ Public Image Server:', publicServerData?.value);
    }

    if (folderError) {
      console.log('❌ Error getting folder_molds:', folderError);
    } else {
      console.log('✅ Molds Folder:', folderData?.value);
    }

    // Construct the expected image URL
    if (publicServerData && folderData) {
      const expectedImageUrl = `${publicServerData.value}/${folderData.value}/300.jpg`;
      console.log('\n=== EXPECTED IMAGE URL ===');
      console.log('Expected URL:', expectedImageUrl);
      
      // Test if the image exists
      console.log('\n=== TESTING IMAGE ACCESSIBILITY ===');
      try {
        const response = await fetch(expectedImageUrl, { method: 'HEAD' });
        if (response.ok) {
          console.log('✅ Image is accessible at expected URL');
          console.log('Status:', response.status);
          console.log('Content-Type:', response.headers.get('content-type'));
          console.log('Content-Length:', response.headers.get('content-length'));
        } else {
          console.log('❌ Image not accessible at expected URL');
          console.log('Status:', response.status);
          console.log('Status Text:', response.statusText);
        }
      } catch (fetchError) {
        console.log('❌ Error testing image URL:', fetchError.message);
      }
    }

    // Check what URL was actually used in the failed task
    console.log('\n=== FAILED TASK URL ===');
    console.log('URL used in failed task: http://s3.amazonaws.com/paintball/shopify/molds/300.jpg');
    
    // Test the failed URL
    try {
      const response = await fetch('http://s3.amazonaws.com/paintball/shopify/molds/300.jpg', { method: 'HEAD' });
      if (response.ok) {
        console.log('✅ Failed URL is actually accessible (unexpected!)');
      } else {
        console.log('❌ Failed URL is not accessible (as expected)');
        console.log('Status:', response.status);
      }
    } catch (fetchError) {
      console.log('❌ Failed URL is not accessible:', fetchError.message);
    }

    // Check if there's an image record for this mold
    console.log('\n=== IMAGE RECORD CHECK ===');
    const { data: imageRecord, error: imageError } = await supabase
      .from('t_images')
      .select('*')
      .eq('table_name', 't_molds')
      .eq('record_id', 300)
      .single();

    if (imageError) {
      console.log('❌ No image record found for mold 300:', imageError.message);
    } else {
      console.log('✅ Image record found:');
      console.log('Image ID:', imageRecord.id);
      console.log('File Name:', imageRecord.image_file_name);
      console.log('Verified:', imageRecord.image_verified);
      console.log('Created At:', imageRecord.created_at);
    }

    // Check mold readiness
    console.log('\n=== MOLD READINESS CHECK ===');
    const { data: todoMold, error: todoError } = await supabase
      .from('v_todo_molds')
      .select('*')
      .eq('id', 300)
      .single();

    if (todoError) {
      console.log('✅ Mold 300 is not in v_todo_molds (might be ready)');
    } else {
      console.log('❌ Mold 300 is in v_todo_molds (not ready):');
      console.log('Effect:', todoMold.effect);
      console.log('Severity:', todoMold.severity);
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

checkMold300Image();
