// Function to process a sdasin_updated_find_discs_to_match task
async function processSdasinUpdatedFindDiscsToMatchTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Start timing the task
    const startTime = new Date();

    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process SDASIN update. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process SDASIN update. Missing SDASIN id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const sdasinId = payload.id;

    console.log(`[taskQueueWorker.js] Finding discs to match with SDASIN id=${sdasinId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Log that the task is being processed
    await logError(
      `Processing sdasin_updated_find_discs_to_match task for sdasin_id=${sdasinId}`,
      `Task ${task.id}`,
      'taskQueueWorker'
    );

    // Use the optimized database function to match the SDASIN to all discs in a single operation
    console.log(`[taskQueueWorker.js] Matching SDASIN ${sdasinId} to all discs using optimized function...`);

    const { data: matchResult, error: matchError } = await supabase.rpc(
      'match_sdasin_to_all_discs',
      {
        sdasin_id_param: sdasinId
      }
    );

    if (matchError) {
      const errMsg = `[taskQueueWorker.js] Error matching SDASIN to discs: ${matchError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Matching SDASIN to discs`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to match SDASIN to discs. Database error.",
        error: matchError.message
      });
      return;
    }

    const matchCount = matchResult;
    console.log(`[taskQueueWorker.js] Successfully matched SDASIN ${sdasinId} to ${matchCount} discs using optimized function`);

    // Calculate processing time
    const endTime = new Date();
    const processingTimeMs = endTime - startTime;
    const processingTimeSec = (processingTimeMs / 1000).toFixed(2);

    // The function already updates the SDASIN record to mark that matching has been attempted
    await updateTaskStatus(task.id, 'completed', {
      message: `Success! SDASIN matched to ${matchCount} discs in ${processingTimeSec} seconds.`,
      match_count: matchCount,
      processing_time_ms: processingTimeMs,
      processing_time_sec: parseFloat(processingTimeSec)
    });
  } catch (err) {
    // Calculate processing time even for errors
    const endTime = new Date();
    const processingTimeMs = endTime - startTime;
    const processingTimeSec = (processingTimeMs / 1000).toFixed(2);

    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to match SDASIN to discs due to an unexpected error.",
      error: err.message,
      processing_time_ms: processingTimeMs,
      processing_time_sec: parseFloat(processingTimeSec)
    });
  }
}

export default processSdasinUpdatedFindDiscsToMatchTask;
