// publishCollectionPlastic.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import minimist from 'minimist';

// For Node 18+ the global fetch is available.
// For earlier versions, install and import node-fetch if necessary.
// import fetch from 'node-fetch';

// Parse command-line arguments (expecting --id=<plasticId>)
const args = minimist(process.argv.slice(2));
const plasticId = args.id;
if (!plasticId) {
  console.error('No plastic id provided. Use --id=<plasticId>');
  process.exit(1);
}

console.log(`INFO: Received plastic id: ${plasticId}`);

// Initialize Supabase client using your service key.
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Missing Supabase URL or key in environment variables.');
  process.exit(1);
}
console.log(`INFO: Initializing Supabase client...`);
const supabase = createClient(supabaseUrl, supabaseKey);

// Derive Shopify REST endpoint for smart collections.
// Your SHOPIFY_ENDPOINT is assumed to be the GraphQL endpoint;
// here we replace "graphql.json" with "smart_collections.json".
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
if (!shopifyEndpoint || !shopifyAccessToken) {
  console.error('ERROR: Missing Shopify endpoint or access token in environment variables.');
  process.exit(1);
}
const smartCollectionsEndpoint = shopifyEndpoint.replace('graphql.json', 'smart_collections.json');
console.log(`INFO: Shopify smart collections endpoint: ${smartCollectionsEndpoint}`);

/**
 * Generates a handle for a plastic collection.
 * This function concatenates the brand and plastic values and then applies a series of replacements:
 * - Remove periods.
 * - Replace spaces with dashes.
 * - Remove apostrophes.
 * - Remove forward slashes.
 * - Replace ampersands with dashes.
 * - Remove parentheses.
 * - Remove double quotes.
 * - Remove occurrences of "-#" and "-$".
 * - Replace any occurrence of double dashes with a single dash.
 * Finally, append "-plastic" at the end.
 *
 * @param {string} brand - The brand name.
 * @param {string} plastic - The plastic name.
 * @returns {string} - The generated handle.
 */
function generatePlasticHandle(brand, plastic) {
  let base = (brand + " " + plastic).toLowerCase();
  base = base.replace(/\./g, "");      // Remove periods.
  base = base.replace(/ /g, "-");      // Replace spaces with dashes.
  base = base.replace(/'/g, "");       // Remove apostrophes.
  base = base.replace(/\//g, "");      // Remove forward slashes.
  base = base.replace(/&/g, "-");      // Replace ampersands with dashes.
  base = base.replace(/\(/g, "");      // Remove "(".
  base = base.replace(/\)/g, "");      // Remove ")".
  base = base.replace(/"/g, "");       // Remove double quotes.
  base = base.replace(/-#/g, "");      // Remove "-#".
  base = base.replace(/-\$/g, "");     // Remove "-$".
  while (base.includes("--")) {
    base = base.replace(/--/g, "-");   // Replace double dashes with a single dash.
  }
  return base + "-plastic";
}

/**
 * Creates a smart collection on Shopify using the REST Admin API.
 *
 * For plastics, the payload is constructed as follows:
 * - Title: t_brands.brand + " " + t_plastics.plastic + " Plastic"
 * - Body HTML: t_plastics.description
 * - Handle: generated by concatenating t_brands.brand and t_plastics.plastic (with replacements) + "-plastic"
 * - Sort Order: "best-selling"
 * - Template Suffix: "plastic-collection"
 * - disjunctive: false => All conditions must match (logical AND)
 * - Rules: 
 *    1) Tag equals "disc_plastic_<plastic>"
 *    2) variant_inventory > 0
 *
 * @param {object} plasticRecord - The plastic record from t_plastics (augmented with the joined brand name)
 * @returns {Promise<object>} - The created smart collection details.
 */
async function createShopifySmartCollection(plasticRecord) {
  const payload = {
    smart_collection: {
      title: plasticRecord.brand + " " + plasticRecord.plastic + " Plastic",
      body_html: plasticRecord.description,
      handle: generatePlasticHandle(plasticRecord.brand, plasticRecord.plastic),
      sort_order: "best-selling",      // Using allowed value "best-selling"
      template_suffix: "plastic-collection",
      published: true,
      published_scope: "global",         // 🔑 NEW: Ensures visibility across all sales channels
      // All rules must match => AND logic
      disjunctive: false,
      rules: [
        {
          column: "tag",
          relation: "equals",
          condition: "disc_plastic_" + plasticRecord.plastic
        },
        {
          column: "variant_inventory",
          relation: "greater_than",
          condition: "0"
        }
      ]
    }
  };

  const response = await fetch(smartCollectionsEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken
    },
    body: JSON.stringify(payload)
  });
  
  const result = await response.json();
  if (!response.ok) {
    throw new Error(
      `Error creating smart collection for plastic "${plasticRecord.plastic}": ${JSON.stringify(result)}`
    );
  }
  return result.smart_collection;
}

/**
 * Returns a promise that resolves after the given number of milliseconds.
 * @param {number} ms - The delay in milliseconds.
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function main() {
  // Always wait 5 seconds to allow the view (v_todo_plastics) to update.
  console.log("INFO: Waiting 5 seconds for v_todo_plastics to update...");
  await delay(5000);
  
  // Check if the plastic id is present in v_todo_plastics.
  console.log(`INFO: Checking if plastic id ${plasticId} is present in v_todo_plastics...`);
  const { data: todoRecords, error: todoError } = await supabase
    .from('v_todo_plastics')
    .select('id')
    .eq('id', plasticId);
  
  if (todoError) {
    console.error('ERROR: Checking v_todo_plastics:', todoError);
    process.exit(1);
  }
  
  if (todoRecords && todoRecords.length > 0) {
    const noteMessage = `After delay: Plastic id ${plasticId} is present in v_todo_plastics and is NOT ready to be processed.`;
    console.log(`INFO: ${noteMessage}`);
    const { error: noteError } = await supabase
      .from('t_plastics')
      .update({ shopify_collection_uploaded_notes: noteMessage })
      .eq('id', plasticId);
    if (noteError) {
      console.error(`ERROR: Failed to update shopify_collection_uploaded_notes for plastic id ${plasticId}: ${noteError.message}`);
    } else {
      console.log(`INFO: Successfully updated shopify_collection_uploaded_notes for plastic id ${plasticId}.`);
    }
    process.exit(0);
  }

  // Retrieve the plastic record from t_plastics.
  console.log(`INFO: Fetching plastic record with id ${plasticId} from t_plastics...`);
  let { data: plasticRecord, error: plasticError } = await supabase
    .from('t_plastics')
    .select('*')
    .eq('id', plasticId)
    .single();
  
  if (plasticError) {
    console.error('ERROR: Fetching plastic from t_plastics:', plasticError);
    process.exit(1);
  }
  
  // Retrieve the associated brand from t_brands.
  console.log(`INFO: Fetching brand name from t_brands for brand_id ${plasticRecord.brand_id}...`);
  const { data: brandData, error: brandError } = await supabase
    .from('t_brands')
    .select('brand')
    .eq('id', plasticRecord.brand_id)
    .single();
  
  if (brandError) {
    console.error('ERROR: Fetching brand from t_brands:', brandError);
    process.exit(1);
  }
  
  // Add the brand name to our plasticRecord for constructing the payload.
  plasticRecord.brand = brandData.brand;
  
  // Verify that the plastic is eligible for publishing:
  // It must have shopify_collection_uploaded_at still null.
  console.log("INFO: Verifying plastic eligibility for publishing...");
  if (plasticRecord.shopify_collection_uploaded_at !== null) {
    const noteMessage = 'Plastic is not eligible for publishing.';
    console.error(`ERROR: ${noteMessage}`);
    const { error: noteError } = await supabase
      .from('t_plastics')
      .update({ shopify_collection_uploaded_notes: noteMessage })
      .eq('id', plasticId);
    if (noteError) {
      console.error(`ERROR: Failed to update shopify_collection_uploaded_notes for plastic ${plasticRecord.plastic}: ${noteError.message}`);
    }
    process.exit(1);
  }
  console.log("INFO: Plastic is eligible for publishing.");

  // Attempt to create the smart collection on Shopify.
  try {
    console.log("INFO: Creating Shopify smart collection for plastic...");
    const collection = await createShopifySmartCollection(plasticRecord);
    console.log(`INFO: Successfully created collection for plastic ${plasticRecord.plastic}:`);
    console.log(collection);
    
    // Update t_plastics: set shopify_collection_uploaded_at to now and update the success note.
    const successNote = "Success! Plastic Collection created on Shopify through webhook > .js > Shopify API.";
    console.log("INFO: Updating t_plastics record with success note and current timestamp...");
    const { error: updateError } = await supabase
      .from('t_plastics')
      .update({
        shopify_collection_uploaded_at: new Date().toISOString(),
        shopify_collection_uploaded_notes: successNote
      })
      .eq('id', plasticId);
    if (updateError) {
      console.error(`ERROR: Failed to update shopify_collection_uploaded_at for plastic ${plasticRecord.plastic}:`, updateError);
      process.exit(1);
    }
    console.log(`INFO: Updated shopify_collection_uploaded_at for plastic ${plasticRecord.plastic}.`);
  } catch (err) {
    console.error(`ERROR: Failed to create collection for plastic ${plasticRecord.plastic}: ${err.message}`);
    // On error, update t_plastics.shopify_collection_uploaded_notes with the error message.
    const { error: noteError } = await supabase
      .from('t_plastics')
      .update({ shopify_collection_uploaded_notes: err.message })
      .eq('id', plasticId);
    if (noteError) {
      console.error(`ERROR: Failed to update shopify_collection_uploaded_notes for plastic ${plasticRecord.plastic}: ${noteError.message}`);
    }
    process.exit(1);
  }
  
  console.log("INFO: publishCollectionPlastic process completed successfully.");
}

main().catch(err => {
  console.error('ERROR: Unexpected error:', err);
  process.exit(1);
});
