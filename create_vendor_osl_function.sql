-- Function to find a matching order sheet line using manufacturer weight
CREATE OR REPLACE FUNCTION find_matching_osl_by_mfg_weight(
    mps_id_param INTEGER,
    color_id_param INTEGER,
    weight_mfg_param NUMERIC
)
RETURNS TABLE (
    osl_id INTEGER
) AS $$
DECLARE
    rounded_weight NUMERIC;
BEGIN
    -- Round the weight to the nearest integer using standard rounding rules
    -- (0.5 and up rounds up, below 0.5 rounds down)
    rounded_weight := ROUND(weight_mfg_param);
    
    RETURN QUERY
    SELECT id AS osl_id
    FROM t_order_sheet_lines
    WHERE mps_id = mps_id_param
      AND (color_id = color_id_param OR color_id = 23)
      AND rounded_weight >= min_weight
      AND rounded_weight <= max_weight
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;
