// setupGoogleSheetsAPI.js - Helper script to test Google Sheets API setup

import dotenv from 'dotenv';
import { google } from 'googleapis';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

async function testGoogleSheetsAPISetup() {
    console.log('🧪 Testing Google Sheets API Setup...');
    console.log('=====================================');

    // Check environment variables
    console.log('\n📋 Checking Environment Variables:');
    
    const hasServiceAccountKey = !!process.env.GOOGLE_SERVICE_ACCOUNT_KEY;
    const hasCredentialsFile = !!process.env.GOOGLE_APPLICATION_CREDENTIALS;
    const hasApiKey = !!process.env.GOOGLE_API_KEY;
    
    console.log(`   GOOGLE_SERVICE_ACCOUNT_KEY: ${hasServiceAccountKey ? '✅ Set' : '❌ Not set'}`);
    console.log(`   GOOGLE_APPLICATION_CREDENTIALS: ${hasCredentialsFile ? '✅ Set' : '❌ Not set'}`);
    console.log(`   GOOGLE_API_KEY: ${hasApiKey ? '✅ Set' : '❌ Not set'}`);

    if (!hasServiceAccountKey && !hasCredentialsFile && !hasApiKey) {
        console.log('\n❌ No Google Sheets authentication configured!');
        console.log('\nPlease set up authentication by following the guide in GOOGLE_SHEETS_API_SETUP.md');
        console.log('\nQuick setup options:');
        console.log('1. Service Account (Recommended): Set GOOGLE_SERVICE_ACCOUNT_KEY in .env');
        console.log('2. Service Account File: Set GOOGLE_APPLICATION_CREDENTIALS in .env');
        console.log('3. API Key (Public sheets only): Set GOOGLE_API_KEY in .env');
        return;
    }

    // Test authentication
    console.log('\n🔐 Testing Authentication:');
    
    try {
        let auth;
        let authMethod = '';

        if (hasServiceAccountKey) {
            authMethod = 'Service Account Key (Environment Variable)';
            console.log(`   Using: ${authMethod}`);
            try {
                const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_KEY);
                console.log(`   Service Account Email: ${credentials.client_email}`);
                console.log(`   Project ID: ${credentials.project_id}`);
                
                auth = new google.auth.GoogleAuth({
                    credentials: credentials,
                    scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly']
                });
            } catch (error) {
                throw new Error(`Invalid GOOGLE_SERVICE_ACCOUNT_KEY format: ${error.message}`);
            }
        } else if (hasCredentialsFile) {
            authMethod = 'Service Account Key (File)';
            console.log(`   Using: ${authMethod}`);
            console.log(`   File Path: ${process.env.GOOGLE_APPLICATION_CREDENTIALS}`);
            
            // Check if file exists
            if (!fs.existsSync(process.env.GOOGLE_APPLICATION_CREDENTIALS)) {
                throw new Error(`Service account key file not found: ${process.env.GOOGLE_APPLICATION_CREDENTIALS}`);
            }
            
            // Try to read and parse the file
            const keyFile = JSON.parse(fs.readFileSync(process.env.GOOGLE_APPLICATION_CREDENTIALS, 'utf8'));
            console.log(`   Service Account Email: ${keyFile.client_email}`);
            console.log(`   Project ID: ${keyFile.project_id}`);
            
            auth = new google.auth.GoogleAuth({
                keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS,
                scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly']
            });
        } else if (hasApiKey) {
            authMethod = 'API Key';
            console.log(`   Using: ${authMethod}`);
            console.log(`   API Key: ${process.env.GOOGLE_API_KEY.substring(0, 10)}...`);
            auth = process.env.GOOGLE_API_KEY;
        }

        const sheets = google.sheets({ version: 'v4', auth });
        console.log('   ✅ Authentication initialized successfully');

        // Test with the actual Discraft spreadsheet
        console.log('\n📊 Testing Access to Discraft Spreadsheet:');
        const spreadsheetId = '1x0C8UmI-ukFP0r2rCM9wNipQ53XakFv30wQiYwJYgC8';
        console.log(`   Spreadsheet ID: ${spreadsheetId}`);

        try {
            // Get spreadsheet metadata
            const spreadsheetResponse = await sheets.spreadsheets.get({
                spreadsheetId: spreadsheetId
            });

            const spreadsheet = spreadsheetResponse.data;
            console.log(`   ✅ Spreadsheet Title: "${spreadsheet.properties.title}"`);
            console.log(`   📋 Available Sheets:`);
            
            spreadsheet.sheets.forEach(sheet => {
                console.log(`      - ${sheet.properties.title} (${sheet.properties.gridProperties.rowCount} rows, ${sheet.properties.gridProperties.columnCount} cols)`);
            });

            // Test reading from Map sheet
            console.log('\n🗺️ Testing Map Sheet Access:');
            try {
                const mapResponse = await sheets.spreadsheets.values.get({
                    spreadsheetId: spreadsheetId,
                    range: 'Map!A1:E5', // Just get a small sample
                    valueRenderOption: 'UNFORMATTED_VALUE'
                });

                const mapValues = mapResponse.data.values || [];
                console.log(`   ✅ Successfully read ${mapValues.length} rows from Map sheet`);
                console.log(`   📋 Sample data (first 5 rows, 5 columns):`);
                mapValues.forEach((row, index) => {
                    console.log(`      Row ${index + 1}: [${row.slice(0, 5).map(cell => `"${cell || ''}"`).join(', ')}]`);
                });
            } catch (mapError) {
                console.log(`   ❌ Error reading Map sheet: ${mapError.message}`);
            }

            // Test reading from New sheet
            console.log('\n🆕 Testing New Sheet Access:');
            try {
                const newResponse = await sheets.spreadsheets.values.get({
                    spreadsheetId: spreadsheetId,
                    range: 'New!A1:E5', // Just get a small sample
                    valueRenderOption: 'UNFORMATTED_VALUE'
                });

                const newValues = newResponse.data.values || [];
                console.log(`   ✅ Successfully read ${newValues.length} rows from New sheet`);
                console.log(`   📋 Sample data (first 5 rows, 5 columns):`);
                newValues.forEach((row, index) => {
                    console.log(`      Row ${index + 1}: [${row.slice(0, 5).map(cell => `"${cell || ''}"`).join(', ')}]`);
                });
            } catch (newError) {
                console.log(`   ❌ Error reading New sheet: ${newError.message}`);
            }

        } catch (spreadsheetError) {
            console.log(`   ❌ Error accessing spreadsheet: ${spreadsheetError.message}`);
            
            if (spreadsheetError.code === 403) {
                console.log('\n💡 This usually means:');
                console.log('   1. The service account doesn\'t have access to the sheet');
                console.log('   2. Share the sheet with your service account email');
                console.log('   3. Give it Viewer permissions');
                if (hasServiceAccountKey) {
                    try {
                        const credentials = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_KEY);
                        console.log(`   4. Service account email: ${credentials.client_email}`);
                    } catch (e) {
                        console.log('   4. Check your service account email in the credentials');
                    }
                }
            } else if (spreadsheetError.code === 404) {
                console.log('\n💡 Spreadsheet not found - check the spreadsheet ID');
            }
        }

    } catch (error) {
        console.log(`   ❌ Authentication failed: ${error.message}`);
        console.log('\n💡 Common issues:');
        console.log('   1. Invalid JSON format in GOOGLE_SERVICE_ACCOUNT_KEY');
        console.log('   2. Missing or incorrect file path in GOOGLE_APPLICATION_CREDENTIALS');
        console.log('   3. Invalid API key in GOOGLE_API_KEY');
        console.log('   4. Google Sheets API not enabled in Google Cloud Console');
    }

    console.log('\n🎉 Setup test completed!');
    console.log('\nIf everything looks good, you can now run the Discraft OSL import task.');
    console.log('If there are issues, check the GOOGLE_SHEETS_API_SETUP.md guide for detailed instructions.');
}

// Run the test
testGoogleSheetsAPISetup()
    .then(() => {
        process.exit(0);
    })
    .catch(error => {
        console.error('💥 Setup test failed:', error);
        process.exit(1);
    });
