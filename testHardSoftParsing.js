import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testHardSoftParsing() {
  try {
    console.log('🧪 Testing Hard/Soft putter line parsing fixes...\n');
    
    // Test Hard Challenger OS parsing (line 369)
    const { data: hardChallengerOS, error: error1 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model')
      .eq('raw_line_type', 'Hard')
      .ilike('raw_model', '%Challenger OS%')
      .limit(5);
    
    if (error1) {
      console.error('Error querying Hard Challenger OS:', error1);
    } else if (hardChallengerOS.length > 0) {
      console.log('✅ Hard Challenger OS parsing (line 369):');
      hardChallengerOS.forEach(item => {
        console.log(`   Raw: "${item.raw_line_type}" | "${item.raw_model}"`);
        console.log(`   Parsed: ${item.plastic_name} | ${item.mold_name} | ${item.stamp_name}`);
        console.log(`   Expected: Putter Line Hard | Challenger OS | Stock`);
        const isCorrect = item.plastic_name === 'Putter Line Hard' && 
                         item.mold_name === 'Challenger OS' && 
                         item.stamp_name === 'Stock';
        console.log(`   ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}\n`);
      });
    } else {
      console.log('⚪ No Hard Challenger OS records found\n');
    }
    
    // Test Hard Challenger SS parsing
    const { data: hardChallengerSS, error: error2 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model')
      .eq('raw_line_type', 'Hard')
      .ilike('raw_model', '%Challenger SS%')
      .limit(5);
    
    if (error2) {
      console.error('Error querying Hard Challenger SS:', error2);
    } else if (hardChallengerSS.length > 0) {
      console.log('✅ Hard Challenger SS parsing:');
      hardChallengerSS.forEach(item => {
        console.log(`   Raw: "${item.raw_line_type}" | "${item.raw_model}"`);
        console.log(`   Parsed: ${item.plastic_name} | ${item.mold_name} | ${item.stamp_name}`);
        console.log(`   Expected: Putter Line Hard | Challenger SS | Stock`);
        const isCorrect = item.plastic_name === 'Putter Line Hard' && 
                         item.mold_name === 'Challenger SS' && 
                         item.stamp_name === 'Stock';
        console.log(`   ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}\n`);
      });
    } else {
      console.log('⚪ No Hard Challenger SS records found\n');
    }
    
    // Test Soft Ringer GT parsing (line 383)
    const { data: softRingerGT, error: error3 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model')
      .eq('raw_line_type', 'Soft')
      .ilike('raw_model', '%Ringer GT%')
      .limit(5);
    
    if (error3) {
      console.error('Error querying Soft Ringer GT:', error3);
    } else if (softRingerGT.length > 0) {
      console.log('✅ Soft Ringer GT parsing (line 383):');
      softRingerGT.forEach(item => {
        console.log(`   Raw: "${item.raw_line_type}" | "${item.raw_model}"`);
        console.log(`   Parsed: ${item.plastic_name} | ${item.mold_name} | ${item.stamp_name}`);
        console.log(`   Expected: Putter Line Soft | Ringer GT | Stock`);
        const isCorrect = item.plastic_name === 'Putter Line Soft' && 
                         item.mold_name === 'Ringer GT' && 
                         item.stamp_name === 'Stock';
        console.log(`   ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}\n`);
      });
    } else {
      console.log('⚪ No Soft Ringer GT records found\n');
    }
    
    // Test all Hard products to see the variety
    const { data: allHardProducts, error: error4 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model')
      .eq('raw_line_type', 'Hard')
      .limit(10);
    
    if (error4) {
      console.error('Error querying all Hard products:', error4);
    } else if (allHardProducts.length > 0) {
      console.log('📋 All Hard products in database:');
      allHardProducts.forEach((item, index) => {
        console.log(`   ${index + 1}. Raw: "${item.raw_line_type}" | "${item.raw_model}"`);
        console.log(`      Parsed: ${item.plastic_name} | ${item.mold_name} | ${item.stamp_name}`);
      });
      console.log('');
    }
    
    // Test all Soft products to see the variety
    const { data: allSoftProducts, error: error5 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model')
      .eq('raw_line_type', 'Soft')
      .limit(10);
    
    if (error5) {
      console.error('Error querying all Soft products:', error5);
    } else if (allSoftProducts.length > 0) {
      console.log('📋 All Soft products in database:');
      allSoftProducts.forEach((item, index) => {
        console.log(`   ${index + 1}. Raw: "${item.raw_line_type}" | "${item.raw_model}"`);
        console.log(`      Parsed: ${item.plastic_name} | ${item.mold_name} | ${item.stamp_name}`);
      });
      console.log('');
    }
    
    console.log('🎉 Hard/Soft parsing test completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testHardSoftParsing().catch(console.error);
