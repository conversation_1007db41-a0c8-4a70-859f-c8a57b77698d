import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Error: SUPABASE_URL or SUPABASE_KEY environment variables are not set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function reconcileAccessoriesInventory() {
  console.log('🔄 Starting accessories inventory reconciliation...');

  try {
    // Read and parse the qAccQtys.txt file
    console.log('📖 Reading qAccQtys.txt file...');
    const filePath = path.resolve('C:\\Users\\<USER>\\supabase_project\\data\\external data\\qAccQtys.txt');
    
    if (!fs.existsSync(filePath)) {
      console.error('❌ File not found:', filePath);
      return;
    }

    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const lines = fileContent.split('\n').filter(line => line.trim());
    
    if (lines.length === 0) {
      console.error('❌ File is empty or has no valid lines');
      return;
    }

    // Parse the header to get column positions
    const header = lines[0].split('\t');
    console.log('📋 File headers:', header);

    const accessoryIdIndex = header.indexOf('"AccessoryID"');
    const onHandQtyIndex = header.indexOf('"OnHandQty"');
    
    if (accessoryIdIndex === -1 || onHandQtyIndex === -1) {
      console.error('❌ Required columns not found. AccessoryID index:', accessoryIdIndex, 'OnHandQty index:', onHandQtyIndex);
      return;
    }

    // Parse the data lines
    const accessoryData = [];
    for (let i = 1; i < lines.length; i++) {
      const columns = lines[i].split('\t');
      if (columns.length > Math.max(accessoryIdIndex, onHandQtyIndex)) {
        const accessoryId = columns[accessoryIdIndex]?.trim();
        const onHandQty = parseFloat(columns[onHandQtyIndex]?.trim() || '0');
        
        if (accessoryId && !isNaN(onHandQty)) {
          const dgaccSku = `DGACC${accessoryId}`;
          accessoryData.push({
            accessory_id: accessoryId,
            dgacc_sku: dgaccSku,
            qaccqtys_qty: onHandQty
          });
        }
      }
    }

    console.log(`✅ Parsed ${accessoryData.length} accessory records from file`);

    // Get Veeqo data for DGACC SKUs - chunk through all data
    console.log('📊 Fetching Veeqo data for DGACC SKUs...');
    let veeqoData = [];
    let offset = 0;
    const chunkSize = 1000;
    let hasMore = true;

    while (hasMore) {
      console.log(`  📥 Fetching chunk starting at offset ${offset}...`);
      const { data: chunk, error: veeqoError } = await supabase
        .from('imported_table_veeqo_sellables_export')
        .select('sku_code, product_title, qty_on_hand, total_qty')
        .like('sku_code', 'DGACC%')
        .range(offset, offset + chunkSize - 1);

      if (veeqoError) {
        console.error('❌ Error fetching Veeqo data:', veeqoError);
        return;
      }

      if (chunk && chunk.length > 0) {
        veeqoData = veeqoData.concat(chunk);
        console.log(`    ✅ Got ${chunk.length} records (total so far: ${veeqoData.length})`);

        if (chunk.length < chunkSize) {
          hasMore = false;
        } else {
          offset += chunkSize;
        }
      } else {
        hasMore = false;
      }
    }

    console.log(`✅ Found ${veeqoData.length} DGACC records in Veeqo (complete dataset)`);

    // Create lookup maps for easier comparison
    const accessoryMap = new Map();
    accessoryData.forEach(item => {
      accessoryMap.set(item.dgacc_sku, item);
    });

    const veeqoMap = new Map();
    veeqoData.forEach(item => {
      veeqoMap.set(item.sku_code, item);
    });

    // Find discrepancies
    const discrepancies = [];

    // Check accessories that exist in qAccQtys but not in Veeqo
    for (const [dgaccSku, accessoryItem] of accessoryMap) {
      if (!veeqoMap.has(dgaccSku)) {
        discrepancies.push({
          sku_code: dgaccSku,
          accessory_id: accessoryItem.accessory_id,
          status: 'In qAccQtys only',
          qaccqtys_qty: accessoryItem.qaccqtys_qty,
          veeqo_qty_on_hand: null,
          veeqo_total_qty: null,
          veeqo_product_title: null,
          qty_difference: null
        });
      }
    }

    // Check Veeqo items and compare quantities
    for (const [skuCode, veeqoItem] of veeqoMap) {
      const accessoryItem = accessoryMap.get(skuCode);
      
      if (!accessoryItem) {
        // Exists in Veeqo but not in qAccQtys
        discrepancies.push({
          sku_code: skuCode,
          accessory_id: null,
          status: 'In Veeqo only',
          qaccqtys_qty: null,
          veeqo_qty_on_hand: parseFloat(veeqoItem.qty_on_hand || '0'),
          veeqo_total_qty: parseFloat(veeqoItem.total_qty || '0'),
          veeqo_product_title: veeqoItem.product_title,
          qty_difference: null
        });
      } else {
        // Exists in both - compare quantities
        const veeqoQtyOnHand = parseFloat(veeqoItem.qty_on_hand || '0');
        const qaccQty = accessoryItem.qaccqtys_qty;
        const difference = qaccQty - veeqoQtyOnHand;
        
        if (Math.abs(difference) > 0.01) { // Allow for small floating point differences
          discrepancies.push({
            sku_code: skuCode,
            accessory_id: accessoryItem.accessory_id,
            status: 'Quantity difference',
            qaccqtys_qty: qaccQty,
            veeqo_qty_on_hand: veeqoQtyOnHand,
            veeqo_total_qty: parseFloat(veeqoItem.total_qty || '0'),
            veeqo_product_title: veeqoItem.product_title,
            qty_difference: difference
          });
        }
      }
    }

    // Display results
    console.log('\n📊 RECONCILIATION RESULTS:');
    console.log('=' .repeat(80));
    console.log(`Total qAccQtys records: ${accessoryData.length}`);
    console.log(`Total Veeqo DGACC records: ${veeqoData.length}`);
    console.log(`Total discrepancies found: ${discrepancies.length}`);
    console.log('=' .repeat(80));

    if (discrepancies.length === 0) {
      console.log('✅ No discrepancies found! All quantities match.');
      return;
    }

    // Group discrepancies by status
    const byStatus = discrepancies.reduce((acc, item) => {
      if (!acc[item.status]) acc[item.status] = [];
      acc[item.status].push(item);
      return acc;
    }, {});

    // Display each category
    Object.keys(byStatus).forEach(status => {
      console.log(`\n📋 ${status.toUpperCase()} (${byStatus[status].length} items):`);
      console.log('-'.repeat(80));
      
      byStatus[status].forEach(item => {
        console.log(`SKU: ${item.sku_code}`);
        if (item.accessory_id) console.log(`  Accessory ID: ${item.accessory_id}`);
        if (item.veeqo_product_title) console.log(`  Product: ${item.veeqo_product_title}`);
        if (item.qaccqtys_qty !== null) console.log(`  qAccQtys Qty: ${item.qaccqtys_qty}`);
        if (item.veeqo_qty_on_hand !== null) console.log(`  Veeqo Qty On Hand: ${item.veeqo_qty_on_hand}`);
        if (item.veeqo_total_qty !== null) console.log(`  Veeqo Total Qty: ${item.veeqo_total_qty}`);
        if (item.qty_difference !== null) console.log(`  Difference: ${item.qty_difference > 0 ? '+' : ''}${item.qty_difference}`);
        console.log('');
      });
    });

    // Save results to a file for further analysis
    const resultsFile = path.resolve('accessories_inventory_reconciliation_results.json');
    fs.writeFileSync(resultsFile, JSON.stringify({
      summary: {
        total_qaccqtys_records: accessoryData.length,
        total_veeqo_records: veeqoData.length,
        total_discrepancies: discrepancies.length,
        discrepancies_by_status: Object.keys(byStatus).map(status => ({
          status,
          count: byStatus[status].length
        }))
      },
      discrepancies: discrepancies
    }, null, 2));

    console.log(`\n💾 Detailed results saved to: ${resultsFile}`);

  } catch (error) {
    console.error('❌ Error during reconciliation:', error);
  }
}

// Run the reconciliation
reconcileAccessoriesInventory();
