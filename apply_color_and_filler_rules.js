import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function applyColorAndFillerRules() {
  try {
    console.log('Applying color_id and additional filler text rules...\n');

    // Handle color_id updates first
    await handleColorIdUpdates();

    // Remove additional filler text from existing records
    await removeMoreFillerText();

    console.log('\nColor and filler text rules applied successfully!');

  } catch (error) {
    console.error('Error:', error);
  }
}

async function handleColorIdUpdates() {
  console.log('Handling color_id updates for "Colors Will Vary" and "Colors May Vary"...');

  const colorPhrases = [
    'Colors Will Vary',
    'Colors May Vary'
  ];

  let totalColorUpdates = 0;

  for (const phrase of colorPhrases) {
    console.log(`Processing "${phrase}"...`);

    // Get records that contain this phrase and have null color_id
    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes, color_id')
      .like('notes', `%${phrase}%`)
      .is('color_id', null)
      .not('notes', 'like', 'XXXX%'); // Skip already processed records

    if (fetchError) {
      console.error(`Error fetching records for "${phrase}":`, fetchError);
      continue;
    }

    let phraseUpdated = 0;

    for (const record of matchingRecords || []) {
      // Remove the phrase from notes and set color_id to 23
      const updatedNotes = record.notes.replace(new RegExp(escapeRegex(phrase), 'gi'), '').trim();
      
      // Clean up extra spaces and punctuation
      const cleanedNotes = updatedNotes
        .replace(/\s+/g, ' ')
        .replace(/^\s*[-|,\[\]]\s*/, '')
        .replace(/\s*[-|,\[\]]\s*$/, '')
        .trim();

      const updateData = {
        notes: cleanedNotes,
        color_id: 23
      };

      // Store original notes in raw_notes if not already stored
      if (!record.raw_notes) {
        updateData.raw_notes = record.notes;
      }

      const { error } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', record.id);

      if (error) {
        console.error(`Error updating record ${record.id}:`, error);
      } else {
        phraseUpdated++;
        totalColorUpdates++;
        if (phraseUpdated <= 5) { // Show first 5 updates per phrase
          console.log(`  Updated ID ${record.id}: set color_id=23, removed "${phrase}"`);
        }
      }
    }

    console.log(`  Updated ${phraseUpdated} records with "${phrase}"`);
  }

  console.log(`Total color_id updates: ${totalColorUpdates}\n`);
}

async function removeMoreFillerText() {
  console.log('Removing additional filler text from existing records...');

  // More filler phrases to remove (longest to shortest)
  const moreFillerPhrases = [
    'Putt and Approach Golf Disc',
    'Straight Disc Golf Putter',
    'High Speed Long Range',
    'Firm Grippy Plastic'
  ];

  let totalUpdated = 0;

  for (const phrase of moreFillerPhrases) {
    console.log(`Removing "${phrase}"...`);

    // Get records that contain this phrase
    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .like('notes', `%${phrase}%`)
      .not('notes', 'like', 'XXXX%'); // Skip already processed records

    if (fetchError) {
      console.error(`Error fetching records for "${phrase}":`, fetchError);
      continue;
    }

    let phraseUpdated = 0;

    for (const record of matchingRecords || []) {
      // Remove the phrase from notes
      const updatedNotes = record.notes.replace(new RegExp(escapeRegex(phrase), 'gi'), '').trim();
      
      // Clean up extra spaces and punctuation
      const cleanedNotes = updatedNotes
        .replace(/\s+/g, ' ')
        .replace(/^\s*[-|,]\s*/, '')
        .replace(/\s*[-|,]\s*$/, '')
        .trim();

      const updateData = {
        notes: cleanedNotes
      };

      // Store original notes in raw_notes if not already stored
      if (!record.raw_notes) {
        updateData.raw_notes = record.notes;
      }

      const { error } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', record.id);

      if (error) {
        console.error(`Error updating record ${record.id}:`, error);
      } else {
        phraseUpdated++;
        totalUpdated++;
        if (phraseUpdated <= 3) { // Show first 3 updates per phrase
          console.log(`  Updated ID ${record.id}: removed "${phrase}"`);
        }
      }
    }

    console.log(`  Removed from ${phraseUpdated} records`);
  }

  console.log(`\nTotal records updated with additional filler text removal: ${totalUpdated}`);
}

function escapeRegex(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Run the script
applyColorAndFillerRules();
