// reconcileScheduler.js - <PERSON>ript to schedule reconciliation without continuous restarts

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';
import { exec } from 'child_process';

// Create a log file
const logFile = 'reconcile_scheduler.log';
fs.writeFileSync(logFile, `Starting reconciliation scheduler at ${new Date().toISOString()}\n`);

// Load environment variables
dotenv.config();
fs.appendFileSync(logFile, `Environment variables loaded\n`);

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  const errorMsg = 'Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file';
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

fs.appendFileSync(logFile, `Supabase credentials found\n`);
const supabase = createClient(supabaseUrl, supabaseKey);
fs.appendFileSync(logFile, `Supabase client created\n`);

// Function to check if reconciliation is needed
async function isReconciliationNeeded() {
  try {
    // Check when the last reconciliation was done
    const { data, error } = await supabase
      .from('reconcile_rpro_counts_to_veeqo')
      .select('last_updated')
      .order('last_updated', { ascending: false })
      .limit(1);
    
    if (error) {
      fs.appendFileSync(logFile, `Error checking last reconciliation: ${error.message}\n`);
      console.error(`Error checking last reconciliation: ${error.message}`);
      return true; // If there's an error, assume reconciliation is needed
    }
    
    if (!data || data.length === 0) {
      fs.appendFileSync(logFile, `No previous reconciliation found\n`);
      console.log('No previous reconciliation found');
      return true; // If there's no previous reconciliation, it's needed
    }
    
    const lastUpdated = new Date(data[0].last_updated);
    const now = new Date();
    const hoursSinceLastUpdate = (now - lastUpdated) / (1000 * 60 * 60);
    
    fs.appendFileSync(logFile, `Last reconciliation was ${hoursSinceLastUpdate.toFixed(2)} hours ago\n`);
    console.log(`Last reconciliation was ${hoursSinceLastUpdate.toFixed(2)} hours ago`);
    
    // Only reconcile if it's been more than 12 hours since the last update
    return hoursSinceLastUpdate > 12;
  } catch (error) {
    fs.appendFileSync(logFile, `Error checking reconciliation need: ${error.message}\n`);
    console.error(`Error checking reconciliation need: ${error.message}`);
    return true; // If there's an error, assume reconciliation is needed
  }
}

// Function to run the reconciliation
function runReconciliation() {
  return new Promise((resolve, reject) => {
    fs.appendFileSync(logFile, `Running reconciliation...\n`);
    console.log('Running reconciliation...');
    
    // Run the reconciliation script as a separate process
    exec('node refreshReconcileData.js', (error, stdout, stderr) => {
      if (error) {
        fs.appendFileSync(logFile, `Error running reconciliation: ${error.message}\n`);
        fs.appendFileSync(logFile, `stderr: ${stderr}\n`);
        console.error(`Error running reconciliation: ${error.message}`);
        reject(error);
        return;
      }
      
      fs.appendFileSync(logFile, `Reconciliation completed successfully\n`);
      fs.appendFileSync(logFile, `stdout: ${stdout}\n`);
      console.log('Reconciliation completed successfully');
      resolve();
    });
  });
}

// Main function
async function main() {
  try {
    // Check if reconciliation is needed
    const reconciliationNeeded = await isReconciliationNeeded();
    
    if (reconciliationNeeded) {
      fs.appendFileSync(logFile, `Reconciliation is needed\n`);
      console.log('Reconciliation is needed');
      
      // Run the reconciliation
      await runReconciliation();
    } else {
      fs.appendFileSync(logFile, `Reconciliation is not needed at this time\n`);
      console.log('Reconciliation is not needed at this time');
    }
    
    fs.appendFileSync(logFile, `Scheduler completed at ${new Date().toISOString()}\n`);
    console.log('Scheduler completed');
    
    // Exit the process
    process.exit(0);
  } catch (error) {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`Unhandled error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the main function
console.log('Starting reconciliation scheduler...');
main();
