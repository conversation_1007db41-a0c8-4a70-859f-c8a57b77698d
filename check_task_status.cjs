require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkTaskStatus() {
  try {
    console.log('Checking task status...');
    
    // Check the task we created
    const { data: tasks, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('enqueued_by', 'test_vendor_osl_mapping')
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (taskError) {
      console.error('Error fetching task:', taskError);
      return;
    }
    
    if (!tasks || tasks.length === 0) {
      console.log('No test tasks found');
      return;
    }
    
    const task = tasks[0];
    console.log('Task status:', task.status);
    console.log('Task result:', task.result);
    
    // Check if the disc was updated
    const discId = task.payload.id;
    const { data: disc, error: discError } = await supabase
      .from('t_discs')
      .select('id, order_sheet_line_id, vendor_osl_id, weight, weight_mfg')
      .eq('id', discId)
      .single();
    
    if (discError) {
      console.error('Error fetching disc:', discError);
      return;
    }
    
    console.log('Updated disc:', disc);
    
    if (disc.vendor_osl_id) {
      console.log('✅ SUCCESS: vendor_osl_id has been set!');
    } else {
      console.log('⏳ vendor_osl_id is still null - task may not have been processed yet');
    }
    
  } catch (err) {
    console.error('Error:', err.message);
  }
}

checkTaskStatus();
