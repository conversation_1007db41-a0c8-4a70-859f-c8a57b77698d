require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testRedirectLogicDisc408210() {
  try {
    console.log('Testing redirect logic with disc 408210 example...');
    
    // Get disc 408210 details
    const { data: disc, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
      .eq('id', 408210)
      .single();
    
    if (discError) {
      console.error('Error getting disc:', discError);
      return;
    }
    
    console.log('\n=== DISC 408210 DETAILS ===');
    console.log(`MPS ID: ${disc.mps_id}`);
    console.log(`Weight: ${disc.weight}g`);
    console.log(`Weight MFG: ${disc.weight_mfg}g`);
    console.log(`Color ID: ${disc.color_id}`);
    console.log(`Current order_sheet_line_id: ${disc.order_sheet_line_id}`);
    console.log(`Current vendor_osl_id: ${disc.vendor_osl_id}`);
    
    // Test the old function (should find OSL 11602)
    console.log('\n=== TESTING OLD FUNCTION (without redirect) ===');
    const { data: oldResult, error: oldError } = await supabase.rpc(
      'find_matching_osl_by_mfg_weight',
      {
        mps_id_param: disc.mps_id,
        color_id_param: disc.color_id,
        weight_mfg_param: disc.weight_mfg
      }
    );
    
    if (oldError) {
      console.error('Old function error:', oldError);
    } else {
      const oldOslId = oldResult && oldResult.length > 0 ? oldResult[0].osl_id : null;
      console.log(`Old function result: OSL ${oldOslId}`);
      
      if (oldOslId) {
        // Check if this OSL's MPS has order_through_mps_id set
        const { data: oslDetails, error: oslError } = await supabase
          .from('t_order_sheet_lines')
          .select('id, mps_id')
          .eq('id', oldOslId)
          .single();
        
        if (!oslError) {
          const { data: mpsDetails, error: mpsError } = await supabase
            .from('t_mps')
            .select('id, order_through_mps_id')
            .eq('id', oslDetails.mps_id)
            .single();
          
          if (!mpsError) {
            console.log(`OSL ${oldOslId} belongs to MPS ${mpsDetails.id}`);
            console.log(`MPS ${mpsDetails.id} order_through_mps_id: ${mpsDetails.order_through_mps_id || 'NULL'}`);
            
            if (mpsDetails.order_through_mps_id) {
              console.log(`🔄 This MPS requires redirect to MPS ${mpsDetails.order_through_mps_id}`);
            } else {
              console.log(`✅ This MPS does not require redirect`);
            }
          }
        }
      }
    }
    
    // Test the new function with redirect logic (should find OSL 18756)
    console.log('\n=== TESTING NEW FUNCTION (with redirect) ===');
    const { data: newResult, error: newError } = await supabase.rpc(
      'find_matching_osl_by_mfg_weight_with_redirect',
      {
        mps_id_param: disc.mps_id,
        color_id_param: disc.color_id,
        weight_mfg_param: disc.weight_mfg
      }
    );
    
    if (newError) {
      console.error('New function error:', newError);
    } else {
      console.log('New function result:', newResult);
      
      if (newResult && newResult.length > 0) {
        const newOslId = newResult[0].osl_id;
        const redirectInfo = newResult[0].redirect_info;
        
        console.log(`New function result: OSL ${newOslId}`);
        console.log(`Redirect info: ${redirectInfo}`);
        
        if (newOslId === 18756) {
          console.log('🎯 SUCCESS! Function correctly redirected to OSL 18756!');
        } else if (newOslId) {
          console.log(`⚠️ Function returned OSL ${newOslId}, expected 18756`);
          
          // Check details of returned OSL
          const { data: returnedOslDetails, error: returnedOslError } = await supabase
            .from('t_order_sheet_lines')
            .select('id, mps_id, min_weight, max_weight, color_id')
            .eq('id', newOslId)
            .single();
          
          if (!returnedOslError) {
            console.log(`Returned OSL ${newOslId}: MPS ${returnedOslDetails.mps_id}, Weight ${returnedOslDetails.min_weight}-${returnedOslDetails.max_weight}g, Color ${returnedOslDetails.color_id}`);
          }
        } else {
          console.log('❌ Function returned no OSL');
        }
      }
    }
    
    // Check OSL 18756 details to verify it should match
    console.log('\n=== VERIFYING OSL 18756 DETAILS ===');
    const { data: targetOsl, error: targetOslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, min_weight, max_weight, color_id')
      .eq('id', 18756)
      .single();
    
    if (targetOslError) {
      console.error('Error getting target OSL:', targetOslError);
    } else {
      console.log(`OSL 18756: MPS ${targetOsl.mps_id}, Weight ${targetOsl.min_weight}-${targetOsl.max_weight}g, Color ${targetOsl.color_id}`);
      
      const roundedWeightMfg = Math.round(disc.weight_mfg);
      const shouldMatch = targetOsl.mps_id === 19687 && 
                         (disc.color_id === targetOsl.color_id || targetOsl.color_id === 23) &&
                         roundedWeightMfg >= targetOsl.min_weight && 
                         roundedWeightMfg <= targetOsl.max_weight;
      
      console.log(`Should disc 408210 match OSL 18756? ${shouldMatch ? '✅' : '❌'}`);
      console.log(`  MPS match (19687): ${targetOsl.mps_id === 19687 ? '✅' : '❌'}`);
      console.log(`  Color match (${disc.color_id} === ${targetOsl.color_id} OR ${targetOsl.color_id} === 23): ${(disc.color_id === targetOsl.color_id || targetOsl.color_id === 23) ? '✅' : '❌'}`);
      console.log(`  Weight match (${roundedWeightMfg} >= ${targetOsl.min_weight} AND <= ${targetOsl.max_weight}): ${(roundedWeightMfg >= targetOsl.min_weight && roundedWeightMfg <= targetOsl.max_weight) ? '✅' : '❌'}`);
    }
    
    // Show the redirect path
    console.log('\n=== EXPECTED REDIRECT PATH ===');
    console.log('1. Disc 408210 (MPS 16912, Weight MFG 174g, Color 7)');
    console.log('2. Initial search finds OSL 11602 (MPS 16912)');
    console.log('3. MPS 16912 has order_through_mps_id = 19687');
    console.log('4. Redirect to MPS 19687, find OSL with weight 173-174g');
    console.log('5. Should find OSL 18756 (MPS 19687, Weight 173-174g)');
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

testRedirectLogicDisc408210();
