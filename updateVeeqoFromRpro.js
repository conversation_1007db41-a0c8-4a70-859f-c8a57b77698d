// updateVeeqoFromRpro.js - Script to update Veeqo quantities based on RPRO quantities

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Create a log file
const logFile = 'update_veeqo_from_rpro.log';
fs.writeFileSync(logFile, `Starting Veeqo update from RPRO at ${new Date().toISOString()}\n`);

// Load environment variables
dotenv.config();
fs.appendFileSync(logFile, `Environment variables loaded\n`);

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

// Veeqo API configuration
const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!supabaseUrl || !supabaseKey) {
  const errorMsg = 'Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file';
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

if (!veeqoApiKey) {
  const errorMsg = 'Error: VEEQO_API_KEY must be set in .env file';
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

fs.appendFileSync(logFile, `Supabase and Veeqo credentials found\n`);
const supabase = createClient(supabaseUrl, supabaseKey);
fs.appendFileSync(logFile, `Supabase client created\n`);

// Function to get records with discrepancies using pagination
async function getDiscrepancyRecords() {
  try {
    fs.appendFileSync(logFile, `Fetching records with discrepancies...\n`);
    console.log('Fetching records with discrepancies...');

    let allRecords = [];
    let hasMore = true;
    let page = 0;
    const pageSize = 1000;

    while (hasMore) {
      fs.appendFileSync(logFile, `Fetching page ${page + 1} of discrepancy records...\n`);
      console.log(`Fetching page ${page + 1} of discrepancy records...`);

      const { data, error } = await supabase
        .from('v_reconcile_rpro_counts_to_veeqo')
        .select('*')
        .not('veeqo_id', 'is', null)
        .not('status', 'eq', 'Equal')
        .range(page * pageSize, (page + 1) * pageSize - 1);

      if (error) {
        fs.appendFileSync(logFile, `Error fetching discrepancy records on page ${page + 1}: ${error.message}\n`);
        console.error(`Error fetching discrepancy records on page ${page + 1}: ${error.message}`);
        return allRecords; // Return what we have so far
      }

      if (data.length === 0) {
        hasMore = false;
      } else {
        allRecords = [...allRecords, ...data];
        fs.appendFileSync(logFile, `Found ${data.length} records on page ${page + 1}, total so far: ${allRecords.length}\n`);
        console.log(`Found ${data.length} records on page ${page + 1}, total so far: ${allRecords.length}`);
        page++;
      }
    }

    fs.appendFileSync(logFile, `Found a total of ${allRecords.length} records with discrepancies\n`);
    console.log(`Found a total of ${allRecords.length} records with discrepancies`);
    return allRecords;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in getDiscrepancyRecords: ${error.message}\n`);
    console.error(`Error in getDiscrepancyRecords: ${error.message}`);
    return [];
  }
}

// Function to get sellable IDs for a product
async function getSellableIds(productId) {
  try {
    fs.appendFileSync(logFile, `Getting sellable IDs for product ${productId}...\n`);
    console.log(`Getting sellable IDs for product ${productId}...`);

    // Veeqo API endpoint for getting product details
    const url = `https://api.veeqo.com/products/${productId}`;

    // Make the API request
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      fs.appendFileSync(logFile, `Error getting product ${productId} details: ${response.status} ${response.statusText} - ${errorText}\n`);
      console.error(`Error getting product ${productId} details: ${response.status} ${response.statusText} - ${errorText}`);
      return [];
    }

    const productData = await response.json();

    if (!productData.sellables || productData.sellables.length === 0) {
      fs.appendFileSync(logFile, `No sellables found for product ${productId}\n`);
      console.error(`No sellables found for product ${productId}`);
      return [];
    }

    const sellableIds = productData.sellables.map(sellable => sellable.id);
    fs.appendFileSync(logFile, `Found ${sellableIds.length} sellables for product ${productId}: ${sellableIds.join(', ')}\n`);
    console.log(`Found ${sellableIds.length} sellables for product ${productId}: ${sellableIds.join(', ')}`);
    return sellableIds;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in getSellableIds for product ${productId}: ${error.message}\n`);
    console.error(`Error in getSellableIds for product ${productId}: ${error.message}`);
    return [];
  }
}

// Function to get warehouse IDs
async function getWarehouseIds() {
  try {
    fs.appendFileSync(logFile, `Getting warehouse IDs...\n`);
    console.log(`Getting warehouse IDs...`);

    // Veeqo API endpoint for getting warehouses
    const url = `https://api.veeqo.com/warehouses`;

    // Make the API request
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      fs.appendFileSync(logFile, `Error getting warehouses: ${response.status} ${response.statusText} - ${errorText}\n`);
      console.error(`Error getting warehouses: ${response.status} ${response.statusText} - ${errorText}`);
      return [];
    }

    const warehouses = await response.json();

    if (!warehouses || warehouses.length === 0) {
      fs.appendFileSync(logFile, `No warehouses found\n`);
      console.error(`No warehouses found`);
      return [];
    }

    const warehouseIds = warehouses.map(warehouse => warehouse.id);
    fs.appendFileSync(logFile, `Found ${warehouseIds.length} warehouses: ${warehouseIds.join(', ')}\n`);
    console.log(`Found ${warehouseIds.length} warehouses: ${warehouseIds.join(', ')}`);
    return warehouseIds;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in getWarehouseIds: ${error.message}\n`);
    console.error(`Error in getWarehouseIds: ${error.message}`);
    return [];
  }
}

// Function to update Veeqo stock entry
async function updateStockEntry(sellableId, warehouseId, newQuantity) {
  try {
    fs.appendFileSync(logFile, `Updating stock entry for sellable ${sellableId} in warehouse ${warehouseId} to quantity ${newQuantity}...\n`);
    console.log(`Updating stock entry for sellable ${sellableId} in warehouse ${warehouseId} to quantity ${newQuantity}...`);

    // Veeqo API endpoint for updating stock entry
    const url = `https://api.veeqo.com/sellables/${sellableId}/warehouses/${warehouseId}/stock_entry`;

    // Prepare the request body
    const body = {
      stock_entry: {
        physical_stock_level: newQuantity,
        infinite: false
      }
    };

    // Make the API request
    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const errorText = await response.text();
      fs.appendFileSync(logFile, `Error updating stock entry for sellable ${sellableId} in warehouse ${warehouseId}: ${response.status} ${response.statusText} - ${errorText}\n`);
      console.error(`Error updating stock entry for sellable ${sellableId} in warehouse ${warehouseId}: ${response.status} ${response.statusText} - ${errorText}`);
      return false;
    }

    fs.appendFileSync(logFile, `Successfully updated stock entry for sellable ${sellableId} in warehouse ${warehouseId} to quantity ${newQuantity}\n`);
    console.log(`Successfully updated stock entry for sellable ${sellableId} in warehouse ${warehouseId} to quantity ${newQuantity}`);
    return true;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in updateStockEntry for sellable ${sellableId} in warehouse ${warehouseId}: ${error.message}\n`);
    console.error(`Error in updateStockEntry for sellable ${sellableId} in warehouse ${warehouseId}: ${error.message}`);
    return false;
  }
}

// Function to get product by SKU
async function getProductBySku(sku) {
  try {
    fs.appendFileSync(logFile, `Searching for product with SKU ${sku} in Veeqo...\n`);
    console.log(`Searching for product with SKU ${sku} in Veeqo...`);

    // Veeqo API endpoint for searching products
    const url = `https://api.veeqo.com/products?query=${sku}`;

    // Make the API request
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      fs.appendFileSync(logFile, `Error searching for product with SKU ${sku}: ${response.status} ${response.statusText} - ${errorText}\n`);
      console.error(`Error searching for product with SKU ${sku}: ${response.status} ${response.statusText} - ${errorText}`);
      return null;
    }

    const products = await response.json();

    if (!products || products.length === 0) {
      fs.appendFileSync(logFile, `No products found with SKU ${sku}\n`);
      console.error(`No products found with SKU ${sku}`);
      return null;
    }

    // Find the product with the exact SKU
    const exactMatch = products.find(product => {
      if (product.sellables && product.sellables.length > 0) {
        return product.sellables.some(sellable => sellable.sku_code === sku);
      }
      return false;
    });

    if (!exactMatch) {
      fs.appendFileSync(logFile, `No exact match found for SKU ${sku}\n`);
      console.error(`No exact match found for SKU ${sku}`);
      return null;
    }

    fs.appendFileSync(logFile, `Found product with ID ${exactMatch.id} for SKU ${sku}\n`);
    console.log(`Found product with ID ${exactMatch.id} for SKU ${sku}`);
    return exactMatch;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in getProductBySku for SKU ${sku}: ${error.message}\n`);
    console.error(`Error in getProductBySku for SKU ${sku}: ${error.message}`);
    return null;
  }
}

// Function to update the local database with the new quantity
async function updateLocalDatabase(sku, newQuantity) {
  try {
    fs.appendFileSync(logFile, `Updating local database for SKU ${sku} to quantity ${newQuantity}...\n`);
    console.log(`Updating local database for SKU ${sku} to quantity ${newQuantity}...`);

    // Update the total_qty field in imported_table_veeqo_sellables_export
    const { data, error } = await supabase
      .from('imported_table_veeqo_sellables_export')
      .update({ total_qty: newQuantity })
      .eq('sku_code', sku);

    if (error) {
      fs.appendFileSync(logFile, `Error updating local database for SKU ${sku}: ${error.message}\n`);
      console.error(`Error updating local database for SKU ${sku}: ${error.message}`);
      return false;
    }

    fs.appendFileSync(logFile, `Successfully updated local database for SKU ${sku} to quantity ${newQuantity}\n`);
    console.log(`Successfully updated local database for SKU ${sku} to quantity ${newQuantity}`);
    return true;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in updateLocalDatabase for SKU ${sku}: ${error.message}\n`);
    console.error(`Error in updateLocalDatabase for SKU ${sku}: ${error.message}`);
    return false;
  }
}

// Function to update Veeqo product quantity
async function updateVeeqoQuantity(productId, sku, newQuantity) {
  try {
    fs.appendFileSync(logFile, `Updating Veeqo product for SKU ${sku} to quantity ${newQuantity}...\n`);
    console.log(`Updating Veeqo product for SKU ${sku} to quantity ${newQuantity}...`);

    // Find the product by SKU
    const product = await getProductBySku(sku);

    if (!product) {
      fs.appendFileSync(logFile, `No product found for SKU ${sku}, skipping update\n`);
      console.error(`No product found for SKU ${sku}, skipping update`);
      return false;
    }

    // Get sellable IDs from the product
    const sellableIds = product.sellables.map(sellable => sellable.id);

    if (sellableIds.length === 0) {
      fs.appendFileSync(logFile, `No sellables found for product with SKU ${sku}, skipping update\n`);
      console.error(`No sellables found for product with SKU ${sku}, skipping update`);
      return false;
    }

    // Use only warehouse 99881 (exclude 198479 which is managed by Amazon)
    const warehouseIds = [99881];

    fs.appendFileSync(logFile, `Using warehouse 99881 only (excluding Amazon-managed warehouse 198479)\n`);
    console.log(`Using warehouse 99881 only (excluding Amazon-managed warehouse 198479)`);

    // Update stock entry for each sellable in each warehouse
    let success = true;

    for (const sellableId of sellableIds) {
      for (const warehouseId of warehouseIds) {
        const updateSuccess = await updateStockEntry(sellableId, warehouseId, newQuantity);
        if (!updateSuccess) {
          success = false;
        }

        // Add a small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    // If the Veeqo update was successful, update the local database
    if (success) {
      const localUpdateSuccess = await updateLocalDatabase(sku, newQuantity);
      if (!localUpdateSuccess) {
        fs.appendFileSync(logFile, `Warning: Veeqo was updated successfully, but local database update failed for SKU ${sku}\n`);
        console.warn(`Warning: Veeqo was updated successfully, but local database update failed for SKU ${sku}`);
      }
    }

    return success;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in updateVeeqoQuantity for SKU ${sku}: ${error.message}\n`);
    console.error(`Error in updateVeeqoQuantity for SKU ${sku}: ${error.message}`);
    return false;
  }
}

// Main function
async function main() {
  try {
    // Get records with discrepancies
    const discrepancyRecords = await getDiscrepancyRecords();

    if (discrepancyRecords.length === 0) {
      fs.appendFileSync(logFile, `No discrepancy records found, nothing to update\n`);
      console.log('No discrepancy records found, nothing to update');
      return;
    }

    // Process each record
    let successCount = 0;
    let failureCount = 0;

    for (const record of discrepancyRecords) {
      // Calculate the new quantity for Veeqo
      let newQuantity;

      if (record.rpro_qty < 0) {
        // If RPRO quantity is negative, set Veeqo quantity to 0
        newQuantity = 0;
      } else {
        // Otherwise, set Veeqo quantity to RPRO quantity (rounded down if needed)
        newQuantity = Math.floor(record.rpro_qty);
      }

      // Update Veeqo quantity using SKU code
      const success = await updateVeeqoQuantity(record.veeqo_id, record.sku_code, newQuantity);

      if (success) {
        successCount++;
      } else {
        failureCount++;
      }

      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Log summary
    fs.appendFileSync(logFile, `\nUpdate summary:\n`);
    fs.appendFileSync(logFile, `Total records processed: ${discrepancyRecords.length}\n`);
    fs.appendFileSync(logFile, `Successful Veeqo updates: ${successCount}\n`);
    fs.appendFileSync(logFile, `Failed Veeqo updates: ${failureCount}\n`);

    console.log('\nUpdate summary:');
    console.log(`Total records processed: ${discrepancyRecords.length}`);
    console.log(`Successful Veeqo updates: ${successCount}`);
    console.log(`Failed Veeqo updates: ${failureCount}`);

  } catch (error) {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`Unhandled error: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the main function
console.log('Starting Veeqo update from RPRO...');
main()
  .then(() => {
    fs.appendFileSync(logFile, `Process completed at ${new Date().toISOString()}\n`);
    console.log('Process completed.');
  })
  .catch(error => {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    console.error(`Unhandled error: ${error.message}`);
  });
