-- Function to get the order cost for a disc
CREATE OR REPLACE FUNCTION get_disc_order_cost(
    mps_id_param INTEGER
)
RETURNS TABLE (
    order_cost NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT COALESCE(m.val_override_order_cost, p.val_order_cost) AS order_cost
    FROM public.t_mps m
    JOIN public.t_plastics p ON m.plastic_id = p.id
    WHERE m.id = mps_id_param;
END;
$$ LANGUAGE plpgsql;

-- Function to get the shipping multiplier for a shipment
CREATE OR REPLACE FUNCTION get_shipment_multiplier(
    shipment_id_param INTEGER
)
RETURNS TABLE (
    shipping_multiplier NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT (i.total_amount / NULLIF(i.subtotal, 0)) AS shipping_multiplier
    FROM public.t_shipments s
    JOIN public.t_invoices i ON s.invoice_id = i.id
    WHERE s.id = shipment_id_param;
END;
$$ LANGUAGE plpgsql;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Carry cost helper functions created.';
END $$;
