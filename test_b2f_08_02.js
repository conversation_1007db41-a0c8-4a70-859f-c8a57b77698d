// Test script for update_b2f_location task with the correct location
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Create a Supabase client using environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testB2f0802Task() {
  console.log('Testing update_b2f_location task with B2F 08-02...');

  try {
    // First, let's check the current discs with B2F 08-02 location
    const { data: beforeDiscs, error: beforeError } = await supabase
      .from('t_discs')
      .select('id, location')
      .eq('location', 'B2F 08-02')
      .limit(10);

    if (beforeError) {
      console.error('Error checking for B2F 08-02 discs:', beforeError);
      return;
    }

    console.log(`Found ${beforeDiscs?.length || 0} discs with location 'B2F 08-02' before update:`);
    if (beforeDiscs && beforeDiscs.length > 0) {
      beforeDiscs.forEach(disc => {
        console.log(`  Disc ID ${disc.id}: location = '${disc.location}'`);
      });
    }

    // Enqueue the task with the correct location
    const testPayload = {
      location: "B2F 08-02"
    };

    console.log('\nEnqueueing update_b2f_location task with payload:', JSON.stringify(testPayload));

    const { data: taskData, error: taskError } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'update_b2f_location',
        payload: testPayload,
        status: 'pending',
        scheduled_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        enqueued_by: 'test_b2f_08_02'
      })
      .select();

    if (taskError) {
      console.error('Error creating task:', taskError);
      return;
    }

    if (!taskData || taskData.length === 0) {
      console.log('No task created');
      return;
    }

    const task = taskData[0];
    console.log(`Task created successfully with ID: ${task.id}`);
    console.log('Task details:', JSON.stringify(task, null, 2));

    console.log('\nTask has been enqueued. Run the worker to process it.');
    console.log(`You can check the task status with: SELECT * FROM t_task_queue WHERE id = ${task.id};`);

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the test
testB2f0802Task();
