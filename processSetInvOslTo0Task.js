// processSetInvOslTo0Task.js - Process set_inv_osl_to_0 tasks
import { createClient } from '@supabase/supabase-js';

// Function to process a set_inv_osl_to_0 task
export default async function processSetInvOslTo0Task(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processSetInvOslTo0Task.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[processSetInvOslTo0Task.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[processSetInvOslTo0Task.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[processSetInvOslTo0Task.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[processSetInvOslTo0Task.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[processSetInvOslTo0Task.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process set inv_osl to 0 task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[processSetInvOslTo0Task.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process set inv_osl to 0 task. Missing OSL id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const oslId = payload.id;
    console.log(`[processSetInvOslTo0Task.js] Setting available_quantity to 0 for OSL id=${oslId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Check if the record exists
    const { data: existingRecord, error: checkError } = await supabase
      .from('t_inv_osl')
      .select('id, available_quantity')
      .eq('id', oslId)
      .maybeSingle();

    if (checkError) {
      const errMsg = `[processSetInvOslTo0Task.js] Error checking for existing record: ${checkError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Checking for existing record for OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to check for existing inv_osl record. Database error.",
        error: checkError.message
      });
      return;
    }

    if (!existingRecord) {
      // Create a new record if it doesn't exist
      console.log(`[processSetInvOslTo0Task.js] No t_inv_osl record found for id=${oslId}, creating new record with available_quantity=0`);

      const { error: insertError } = await supabase
        .from('t_inv_osl')
        .insert({
          id: oslId,
          available_quantity: 0
        });

      if (insertError) {
        const errMsg = `[processSetInvOslTo0Task.js] Error creating t_inv_osl record for id=${oslId}: ${insertError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Creating t_inv_osl record for id=${oslId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to create t_inv_osl record. Database error.",
          error: insertError.message
        });
        return;
      }

      console.log(`[processSetInvOslTo0Task.js] Successfully created t_inv_osl record for id=${oslId} with available_quantity=0`);
      await updateTaskStatus(task.id, 'completed', {
        message: "Created new t_inv_osl record with available_quantity=0.",
        osl_id: oslId
      });
      return;
    }

    // Update the existing record
    const oldQuantity = existingRecord.available_quantity || 0;
    console.log(`[processSetInvOslTo0Task.js] Updating t_inv_osl record for id=${oslId} from available_quantity=${oldQuantity} to 0`);

    const { error: updateError } = await supabase
      .from('t_inv_osl')
      .update({
        available_quantity: 0
      })
      .eq('id', oslId);

    if (updateError) {
      const errMsg = `[processSetInvOslTo0Task.js] Error updating t_inv_osl record: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating t_inv_osl record for OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update t_inv_osl record. Database error.",
        error: updateError.message
      });
      return;
    }

    console.log(`[processSetInvOslTo0Task.js] Successfully updated t_inv_osl record for OSL id=${oslId}`);
    await updateTaskStatus(task.id, 'completed', {
      message: `Success! Updated t_inv_osl record for OSL id=${oslId} from available_quantity=${oldQuantity} to 0.`,
      osl_id: oslId,
      old_quantity: oldQuantity,
      new_quantity: 0
    });
  } catch (err) {
    const errMsg = `[processSetInvOslTo0Task.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process set inv_osl to 0 task due to an unexpected error.",
      error: err.message
    });
  }
}
