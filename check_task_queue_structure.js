// check_task_queue_structure.js
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkTaskQueueStructure() {
  try {
    // First, try to get the table structure using Postgres information_schema
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type')
      .eq('table_name', 't_task_queue');

    if (columnsError) {
      console.error('Error fetching table structure:', columnsError);
    } else if (columns && columns.length > 0) {
      console.log('t_task_queue columns:');
      columns.forEach(col => {
        console.log(`${col.column_name}: ${col.data_type}`);
      });
    }

    // Get a single row from t_task_queue to see its structure
    const { data, error } = await supabase
      .from('t_task_queue')
      .select('*')
      .limit(1);

    if (error) {
      console.error('Error fetching task queue structure:', error);
      return;
    }

    if (data && data.length > 0) {
      console.log('\nt_task_queue sample row:');
      console.log(JSON.stringify(data[0], null, 2));
    } else {
      console.log('\nNo rows found in t_task_queue');

      // Create a sample task to see the structure
      console.log('Creating a sample task...');
      const { data: insertData, error: insertError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'sample_task',
          status: 'pending',
          payload: { sample: 'data' },
          scheduled_at: new Date().toISOString()
        })
        .select();

      if (insertError) {
        console.error('Error creating sample task:', insertError);
      } else if (insertData && insertData.length > 0) {
        console.log('Sample task created:');
        console.log(JSON.stringify(insertData[0], null, 2));
      }
    }
  } catch (err) {
    console.error('Exception checking task queue structure:', err);
  }
}

checkTaskQueueStructure();
