import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function debugEmacTruth() {
    console.log('Debugging EMac Truth parsing...\n');
    
    try {
        // Get Dynamic Discs brand ID
        const { data: brandData, error: brandError } = await supabase
            .from('t_brands')
            .select('id, brand')
            .eq('brand', 'Dynamic Discs');
        
        const ddBrandId = brandData[0].id;
        console.log(`Dynamic Discs brand ID: ${ddBrandId}`);
        
        // Get all Dynamic Discs molds
        const { data: molds, error: moldsError } = await supabase
            .from('t_molds')
            .select('mold')
            .eq('brand_id', ddBrandId)
            .order('mold');
        
        console.log('\nDynamic Discs molds containing "Truth":');
        const truthMolds = molds.filter(m => m.mold.toLowerCase().includes('truth'));
        truthMolds.forEach(mold => {
            console.log(`- "${mold.mold}" (length: ${mold.mold.length})`);
        });
        
        // Test the parsing logic
        const title = "BioFuzion EMac Truth Halloween Triple Stamp";
        const plastic = "BioFuzion";
        const remainingAfterPlastic = title.substring(plastic.length).trim(); // "EMac Truth Halloween Triple Stamp"
        
        console.log(`\nTitle: "${title}"`);
        console.log(`After removing plastic "${plastic}": "${remainingAfterPlastic}"`);
        
        // No dash, so moldPart = remainingAfterPlastic
        const moldPart = remainingAfterPlastic; // "EMac Truth Halloween Triple Stamp"
        console.log(`Mold part to search: "${moldPart}"`);
        
        // Sort molds by length (longest first)
        const sortedMolds = truthMolds.map(m => m.mold).sort((a, b) => b.length - a.length);
        console.log('\nMolds sorted by length (longest first):');
        sortedMolds.forEach(mold => {
            console.log(`- "${mold}" (length: ${mold.length})`);
        });
        
        // Test exact match
        console.log('\nTesting exact match:');
        for (const mold of sortedMolds) {
            const isExact = moldPart === mold;
            console.log(`"${moldPart}" === "${mold}": ${isExact}`);
        }
        
        // Test starts with match
        console.log('\nTesting starts with match:');
        for (const mold of sortedMolds) {
            const startsWith = moldPart.startsWith(mold + ' ');
            console.log(`"${moldPart}".startsWith("${mold} "): ${startsWith}`);
        }
        
        // The issue is that "EMac Truth Halloween Triple Stamp" should match "EMAC Truth"
        // Let's see what happens if we check for the mold at the beginning
        console.log('\nTesting if mold appears at start of moldPart:');
        for (const mold of sortedMolds) {
            const startsWithMold = moldPart.startsWith(mold);
            console.log(`"${moldPart}".startsWith("${mold}"): ${startsWithMold}`);
            if (startsWithMold) {
                const afterMold = moldPart.substring(mold.length).trim();
                console.log(`  After removing mold: "${afterMold}"`);
            }
        }
        
    } catch (error) {
        console.error('Debug failed:', error);
    }
}

// Run the debug
debugEmacTruth();
