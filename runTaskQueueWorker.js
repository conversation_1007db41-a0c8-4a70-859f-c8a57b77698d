// runTaskQueueWorker.js

// Suppress Node.js deprecation warnings
process.noDeprecation = true;

import dotenv from 'dotenv';
dotenv.config();

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const WORKER_SCRIPT = path.join(__dirname, 'taskQueueWorker.js');
const INTERVAL_MS = 15000; // Run every 15 seconds
const DAEMON_MODE = true; // Always run in daemon mode

console.log(`[runTaskQueueWorker.js] Starting task queue worker runner`);
console.log(`[runTaskQueueWorker.js] Worker script: ${WORKER_SCRIPT}`);
console.log(`[runTaskQueueWorker.js] Run interval: ${INTERVAL_MS}ms`);

// Flag to track if we're currently running a worker process
let isWorkerRunning = false;

// Import the worker module directly instead of spawning a new process
import { processTaskQueue } from './taskQueueWorker.js';
import { enqueueWorkerStatusTask } from './enqueueWorkerStatusTask.js';

// Function to run the worker script
async function runWorker() {
  // Don't start a new worker if one is already running
  if (isWorkerRunning) {
    console.log(`[runTaskQueueWorker.js] Worker is already running, skipping this run`);
    return;
  }

  isWorkerRunning = true;
  console.log(`[runTaskQueueWorker.js] Running worker at ${new Date().toISOString()}`);

  try {
    // Call the processTaskQueue function directly
    await processTaskQueue();
    console.log(`[runTaskQueueWorker.js] Worker completed successfully`);
  } catch (err) {
    console.error(`[runTaskQueueWorker.js] Error running worker: ${err.message}`);
  } finally {
    isWorkerRunning = false; // Reset the flag when the worker completes
  }
}

// Function to schedule the next hourly status update
function scheduleHourlyStatusUpdate() {
  const now = new Date();
  const nextHour = new Date(now);

  // Set to the next hour
  nextHour.setHours(now.getHours() + 1);
  nextHour.setMinutes(0);
  nextHour.setSeconds(0);
  nextHour.setMilliseconds(0);

  // Calculate milliseconds until next hour
  const msUntilNextHour = nextHour - now;

  console.log(`[runTaskQueueWorker.js] Scheduling next status update at ${nextHour.toISOString()} (in ${Math.round(msUntilNextHour / 1000)} seconds)`);

  // Schedule the status update
  setTimeout(async () => {
    try {
      // Enqueue the status update task
      await enqueueWorkerStatusTask();

      // Schedule the next update
      scheduleHourlyStatusUpdate();
    } catch (err) {
      console.error(`[runTaskQueueWorker.js] Error in hourly status update: ${err.message}`);
      // Still try to schedule the next update even if this one failed
      scheduleHourlyStatusUpdate();
    }
  }, msUntilNextHour);
}

// Run the worker immediately
runWorker();

// Then run it at the specified interval
const intervalId = setInterval(runWorker, INTERVAL_MS);

// Schedule the first hourly status update
scheduleHourlyStatusUpdate();

// Also enqueue a status update immediately on startup
enqueueWorkerStatusTask().catch(err => {
  console.error(`[runTaskQueueWorker.js] Error enqueueing initial status update: ${err.message}`);
});

console.log(`[runTaskQueueWorker.js] Worker runner started in daemon mode. Press Ctrl+C to stop.`);

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('[runTaskQueueWorker.js] Received SIGINT signal. Shutting down gracefully...');
  clearInterval(intervalId);

  // Give any running worker a chance to finish
  if (isWorkerRunning) {
    console.log('[runTaskQueueWorker.js] Waiting for current worker to finish...');
    setTimeout(() => {
      console.log('[runTaskQueueWorker.js] Forcing exit after timeout');
      process.exit(0);
    }, 5000); // Wait up to 5 seconds for worker to finish
  } else {
    process.exit(0);
  }
});

// Keep the process running
process.stdin.resume();
