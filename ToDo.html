<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToDo Management</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            width: 100%;
            box-sizing: border-box;
        }
        header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        header h1 {
            color: white;
            margin: 0;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
            width: 100%;
            box-sizing: border-box;
        }
        .card-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-header h2 {
            margin: 0;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #2980b9;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: #ddd;
            border-radius: 5px 5px 0 0;
            margin-right: 5px;
            cursor: pointer;
        }
        .tab.active {
            background-color: white;
            border-bottom: 2px solid #3498db;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }

        .placeholder-content {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
            font-style: italic;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .data-table th {
            background-color: #2c3e50;
            color: white;
            text-align: left;
            padding: 12px;
            border: 1px solid #ddd;
            cursor: pointer;
            user-select: none;
            position: relative;
        }
        .data-table th:hover {
            background-color: #34495e;
        }
        .data-table th.sortable::after {
            content: ' ↕';
            opacity: 0.5;
            margin-left: 5px;
        }
        .data-table th.sort-asc::after {
            content: ' ↑';
            opacity: 1;
        }
        .data-table th.sort-desc::after {
            content: ' ↓';
            opacity: 1;
        }
        .data-table td {
            padding: 10px 12px;
            border: 1px solid #ddd;
            text-align: right;
        }
        .data-table td:first-child,
        .data-table td:nth-child(2) {
            text-align: left;
        }
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .data-table tr:hover {
            background-color: #e9ecef;
        }
        .action-btn {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 0;
        }
        .action-btn:hover {
            background-color: #c0392b;
        }
        .loading-message {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
            font-style: italic;
        }
        .refresh-btn {
            background-color: #27ae60;
            margin-bottom: 15px;
        }
        .refresh-btn:hover {
            background-color: #229954;
        }
        .editable-field {
            background: none;
            border: 1px solid transparent;
            padding: 4px 8px;
            width: 100%;
            text-align: right;
            font-family: inherit;
            font-size: inherit;
        }
        .editable-field:hover {
            border-color: #3498db;
            background-color: #f8f9fa;
        }
        .editable-field:focus {
            outline: none;
            border-color: #3498db;
            background-color: white;
            box-shadow: 0 0 3px rgba(52, 152, 219, 0.3);
        }
        .verify-btn {
            background-color: #27ae60;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin: 0;
        }
        .verify-btn:hover {
            background-color: #229954;
        }
        .verify-btn:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }




    </style>
</head>
<body>
    <header>
        <h1>ToDo Management Dashboard</h1>
        <p>Manage tasks and items across different data types</p>
    </header>



    <div class="tabs">
        <div class="tab active" data-tab="plastic">Plastic</div>
        <div class="tab" data-tab="mold">Mold</div>
        <div class="tab" data-tab="mps">MPS</div>
        <div class="tab" data-tab="osl">OSL</div>
        <div class="tab" data-tab="disc">Disc</div>
        <div class="tab" data-tab="mps-review">MPS Review</div>
    </div>

    <div id="plastic" class="tab-content active">
        <div class="card">
            <div class="card-header">
                <h2>Plastic Pricing Review</h2>
                <button id="refreshPlasticPricingBtn" class="refresh-btn">Refresh Data</button>
            </div>
            <div>
                <p>Review and update plastic pricing information. Edit price fields directly in the table and verify cost/price data.</p>

                <!-- Search bar -->
                <div style="margin-bottom: 15px;">
                    <input type="text" id="plasticSearchInput" placeholder="Search plastics..." style="padding: 8px; width: 300px; border: 1px solid #ddd; border-radius: 4px;">
                </div>

                <div id="plasticPricingTableContainer">
                    <table class="data-table" id="plasticPricingTable">
                        <thead>
                            <tr>
                                <th class="sortable" data-column="id" data-type="number">ID</th>
                                <th class="sortable" data-column="plastic" data-type="text">Plastic</th>
                                <th class="sortable" data-column="discs_in_stock_and_uploaded" data-type="number">Discs in Stock & Uploaded</th>
                                <th class="sortable" data-column="val_order_cost" data-type="number">Order Cost</th>
                                <th class="sortable" data-column="val_map_price" data-type="number">MAP Price</th>
                                <th class="sortable" data-column="val_retail_price" data-type="number">Retail Price</th>
                                <th class="sortable" data-column="val_msrp" data-type="number">MSRP</th>
                                <th class="sortable" data-column="val_max_amazon_price" data-type="number">Max Amazon Price</th>
                                <th class="sortable" data-column="price_cost_verified_at" data-type="date">Price Verified At</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="plasticPricingTableBody">
                            <tr>
                                <td colspan="10" class="loading-message">Click "Refresh Data" to load plastic pricing data...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div id="mold" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Mold ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>Mold Management</h3>
                <p>This section will contain mold-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="mps" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>MPS ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>MPS Management</h3>
                <p>This section will contain MPS-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="osl" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>OSL ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>OSL Management</h3>
                <p>This section will contain OSL-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="disc" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>Disc ToDo Items</h2>
            </div>
            <div class="placeholder-content">
                <h3>Disc Management</h3>
                <p>This section will contain disc-related todo items and management tools.</p>
                <p>Content coming soon...</p>
            </div>
        </div>
    </div>

    <div id="mps-review" class="tab-content">
        <div class="card">
            <div class="card-header">
                <h2>MPS Review - Active MPS with No Stock</h2>
            </div>
            <div>
                <p>Review active MPS records that currently have no discs in stock. These may be candidates for marking as inactive.</p>
                <button id="refreshMpsReviewBtn" class="refresh-btn">Refresh Data</button>

                <div id="mpsReviewTableContainer">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th class="sortable" data-column="id" data-type="number">MPS ID</th>
                                <th class="sortable" data-column="g_code" data-type="text">G Code</th>
                                <th class="sortable" data-column="sold_date_last" data-type="date">Last Sold Date</th>
                                <th class="sortable" data-column="received_date_last" data-type="date">Last Received Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="mpsReviewTableBody">
                            <tr>
                                <td colspan="5" class="loading-message">Click "Refresh Data" to load MPS records...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab functionality
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs and content
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab and corresponding content
                tab.classList.add('active');
                const tabId = tab.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // MPS Review functionality
        document.getElementById('refreshMpsReviewBtn').addEventListener('click', function() {
            loadMpsReviewData();
        });

        // Plastic Pricing functionality
        document.getElementById('refreshPlasticPricingBtn').addEventListener('click', function() {
            loadPlasticPricingData();
        });



        // Global variable to store current data for sorting
        let currentMpsData = [];
        let currentSortColumn = 'id';
        let currentSortDirection = 'asc';

        // Global variables for plastic pricing
        let currentPlasticData = [];
        let currentPlasticSortColumn = 'id';
        let currentPlasticSortDirection = 'asc';



        function loadMpsReviewData() {
            const tableBody = document.getElementById('mpsReviewTableBody');
            tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Loading MPS review data...</td></tr>';

            // Call API to get MPS review data
            fetch('/api/mps-review')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.records) {
                        currentMpsData = data.records;
                        sortAndDisplayData();
                        setupSortingEventListeners();
                    } else {
                        tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Error loading data: ' + (data.error || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">Error loading data: ' + error.message + '. Make sure the adminServer.js is running.</td></tr>';
                });
        }

        function displayMpsReviewData(records) {
            const tableBody = document.getElementById('mpsReviewTableBody');

            if (!records || records.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="loading-message">No active MPS records with zero stock found.</td></tr>';
                return;
            }

            tableBody.innerHTML = '';

            records.forEach(record => {
                const row = document.createElement('tr');

                // MPS ID
                const idCell = document.createElement('td');
                idCell.textContent = record.id;
                row.appendChild(idCell);

                // G Code
                const codeCell = document.createElement('td');
                codeCell.textContent = record.g_code || '';
                row.appendChild(codeCell);

                // Last Sold Date
                const soldDateCell = document.createElement('td');
                soldDateCell.textContent = record.sold_date_last ? new Date(record.sold_date_last).toLocaleDateString() : '';
                row.appendChild(soldDateCell);

                // Last Received Date
                const receivedDateCell = document.createElement('td');
                receivedDateCell.textContent = record.received_date_last ? new Date(record.received_date_last).toLocaleDateString() : '';
                row.appendChild(receivedDateCell);

                // Action button
                const actionCell = document.createElement('td');
                const actionBtn = document.createElement('button');
                actionBtn.textContent = 'Mark Inactive';
                actionBtn.className = 'action-btn';
                actionBtn.onclick = () => markMpsInactive(record.id);
                actionCell.appendChild(actionBtn);
                row.appendChild(actionCell);

                tableBody.appendChild(row);
            });
        }

        function markMpsInactive(mpsId) {
            // Call API to mark MPS as inactive
            fetch('/api/mps-mark-inactive', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mpsId: mpsId }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Remove the record from current data and refresh display
                    currentMpsData = currentMpsData.filter(record => record.id !== mpsId);
                    sortAndDisplayData();
                } else {
                    console.error('Error marking MPS as inactive:', data.error || 'Unknown error');
                }
            })
            .catch(error => {
                console.error('Error marking MPS as inactive:', error.message);
            });
        }

        function setupSortingEventListeners() {
            document.querySelectorAll('.sortable').forEach(header => {
                header.addEventListener('click', function() {
                    const column = this.getAttribute('data-column');
                    const type = this.getAttribute('data-type');

                    // Toggle sort direction if clicking the same column
                    if (currentSortColumn === column) {
                        currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        currentSortColumn = column;
                        currentSortDirection = 'asc';
                    }

                    sortAndDisplayData();
                    updateSortIndicators();
                });
            });
        }

        function sortAndDisplayData() {
            const sortedData = [...currentMpsData].sort((a, b) => {
                let aVal = a[currentSortColumn];
                let bVal = b[currentSortColumn];

                // Handle null/undefined values
                if (aVal === null || aVal === undefined) aVal = '';
                if (bVal === null || bVal === undefined) bVal = '';

                // Convert dates for comparison
                if (currentSortColumn.includes('date') && aVal && bVal) {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                }

                // Convert numbers for comparison
                if (currentSortColumn === 'id') {
                    aVal = parseInt(aVal) || 0;
                    bVal = parseInt(bVal) || 0;
                }

                // Compare values
                if (aVal < bVal) return currentSortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return currentSortDirection === 'asc' ? 1 : -1;
                return 0;
            });

            displayMpsReviewData(sortedData);
        }

        function updateSortIndicators() {
            // Remove all sort classes
            document.querySelectorAll('.sortable').forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });

            // Add sort class to current column
            const currentHeader = document.querySelector(`[data-column="${currentSortColumn}"]`);
            if (currentHeader) {
                currentHeader.classList.add(currentSortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
            }
        }

        // Plastic Pricing Functions
        function loadPlasticPricingData() {
            const tableBody = document.getElementById('plasticPricingTableBody');
            tableBody.innerHTML = '<tr><td colspan="10" class="loading-message">Loading plastic pricing data...</td></tr>';

            // Call API to get plastic pricing data
            fetch('/api/plastic-pricing')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.records) {
                        currentPlasticData = data.records;
                        sortAndDisplayPlasticData();
                        setupPlasticSortingEventListeners();
                        setupPlasticSearchFilter();
                    } else {
                        tableBody.innerHTML = '<tr><td colspan="10" class="loading-message">Error loading data: ' + (data.error || 'Unknown error') + '</td></tr>';
                    }
                })
                .catch(error => {
                    tableBody.innerHTML = '<tr><td colspan="10" class="loading-message">Error loading data: ' + error.message + '. Make sure the adminServer.js is running.</td></tr>';
                });
        }

        function displayPlasticPricingData(records) {
            const tableBody = document.getElementById('plasticPricingTableBody');

            if (!records || records.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="10" class="loading-message">No plastic records found.</td></tr>';
                return;
            }

            tableBody.innerHTML = '';

            records.forEach(record => {
                const row = document.createElement('tr');

                // ID
                const idCell = document.createElement('td');
                idCell.textContent = record.id;
                row.appendChild(idCell);

                // Plastic name
                const plasticCell = document.createElement('td');
                plasticCell.textContent = record.plastic || '';
                row.appendChild(plasticCell);

                // Discs in stock and uploaded
                const stockCell = document.createElement('td');
                stockCell.textContent = record.discs_in_stock_and_uploaded || 0;
                stockCell.style.textAlign = 'right';
                row.appendChild(stockCell);

                // Editable price fields
                const priceFields = ['val_order_cost', 'val_map_price', 'val_retail_price', 'val_msrp', 'val_max_amazon_price'];
                priceFields.forEach(field => {
                    const cell = document.createElement('td');
                    const input = document.createElement('input');
                    input.type = 'number';
                    input.step = '0.01';
                    input.className = 'editable-field';
                    input.value = record[field] || '';
                    input.dataset.field = field;
                    input.dataset.id = record.id;
                    input.addEventListener('blur', updatePlasticField);
                    input.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            this.blur();
                        }
                    });
                    cell.appendChild(input);
                    row.appendChild(cell);
                });

                // Price verified at
                const verifiedCell = document.createElement('td');
                verifiedCell.textContent = record.price_cost_verified_at ? new Date(record.price_cost_verified_at).toLocaleDateString() : '';
                row.appendChild(verifiedCell);

                // Action button
                const actionCell = document.createElement('td');
                const verifyBtn = document.createElement('button');
                verifyBtn.textContent = 'Verify Cost/Price';
                verifyBtn.className = 'verify-btn';
                verifyBtn.onclick = () => verifyPlasticCostPrice(record.id);
                actionCell.appendChild(verifyBtn);
                row.appendChild(actionCell);

                tableBody.appendChild(row);
            });
        }

        function updatePlasticField(event) {
            const input = event.target;
            const id = input.dataset.id;
            const field = input.dataset.field;
            const value = input.value;

            // Call API to update the field
            fetch('/api/plastic-pricing/update', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    id: parseInt(id),
                    field: field,
                    value: value ? parseFloat(value) : null
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the record in current data
                    const record = currentPlasticData.find(r => r.id == id);
                    if (record) {
                        record[field] = value ? parseFloat(value) : null;
                    }
                    // Visual feedback
                    input.style.backgroundColor = '#d4edda';
                    setTimeout(() => {
                        input.style.backgroundColor = '';
                    }, 1000);
                } else {
                    console.error('Error updating plastic field:', data.error || 'Unknown error');
                    // Reset to original value
                    const record = currentPlasticData.find(r => r.id == id);
                    if (record) {
                        input.value = record[field] || '';
                    }
                    // Visual feedback for error
                    input.style.backgroundColor = '#f8d7da';
                    setTimeout(() => {
                        input.style.backgroundColor = '';
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('Error updating plastic field:', error.message);
                // Reset to original value
                const record = currentPlasticData.find(r => r.id == id);
                if (record) {
                    input.value = record[field] || '';
                }
            });
        }

        function verifyPlasticCostPrice(plasticId) {
            // Call API to verify cost/price
            fetch('/api/plastic-pricing/verify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: plasticId }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the record in current data
                    const record = currentPlasticData.find(r => r.id == plasticId);
                    if (record) {
                        record.price_cost_verified_at = new Date().toISOString();
                    }
                    // Reapply the current search filter instead of showing all data
                    applyCurrentPlasticSearch();
                } else {
                    console.error('Error verifying plastic cost/price:', data.error || 'Unknown error');
                }
            })
            .catch(error => {
                console.error('Error verifying plastic cost/price:', error.message);
            });
        }

        function setupPlasticSortingEventListeners() {
            document.querySelectorAll('#plasticPricingTable .sortable').forEach(header => {
                header.addEventListener('click', function() {
                    const column = this.getAttribute('data-column');
                    const type = this.getAttribute('data-type');

                    // Toggle sort direction if clicking the same column
                    if (currentPlasticSortColumn === column) {
                        currentPlasticSortDirection = currentPlasticSortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        currentPlasticSortColumn = column;
                        currentPlasticSortDirection = 'asc';
                    }

                    sortAndDisplayPlasticData();
                    updatePlasticSortIndicators();
                });
            });
        }

        function sortAndDisplayPlasticData() {
            const sortedData = [...currentPlasticData].sort((a, b) => {
                let aVal = a[currentPlasticSortColumn];
                let bVal = b[currentPlasticSortColumn];

                // Handle null/undefined values
                if (aVal === null || aVal === undefined) aVal = '';
                if (bVal === null || bVal === undefined) bVal = '';

                // Convert dates for comparison
                if (currentPlasticSortColumn.includes('date') && aVal && bVal) {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                }

                // Convert numbers for comparison
                if (currentPlasticSortColumn === 'id' || currentPlasticSortColumn.startsWith('val_') || currentPlasticSortColumn === 'discs_in_stock_and_uploaded') {
                    aVal = parseFloat(aVal) || 0;
                    bVal = parseFloat(bVal) || 0;
                }

                // Compare values
                if (aVal < bVal) return currentPlasticSortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return currentPlasticSortDirection === 'asc' ? 1 : -1;
                return 0;
            });

            displayPlasticPricingData(sortedData);
        }

        function updatePlasticSortIndicators() {
            // Remove all sort classes from plastic table
            document.querySelectorAll('#plasticPricingTable .sortable').forEach(header => {
                header.classList.remove('sort-asc', 'sort-desc');
            });

            // Add sort class to current column
            const currentHeader = document.querySelector(`#plasticPricingTable [data-column="${currentPlasticSortColumn}"]`);
            if (currentHeader) {
                currentHeader.classList.add(currentPlasticSortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
            }
        }

        function applyCurrentPlasticSearch() {
            const searchInput = document.getElementById('plasticSearchInput');
            const searchTerm = searchInput.value.toLowerCase().trim();

            if (!searchTerm) {
                // If search is empty, show all data sorted
                sortAndDisplayPlasticData();
                return;
            }

            // Split search term into individual words
            const searchWords = searchTerm.split(/\s+/);

            const filteredData = currentPlasticData.filter(record => {
                const plasticName = (record.plastic || '').toLowerCase();
                const plasticId = (record.id || '').toString();

                // Check if ALL search words are found in either plastic name or ID
                return searchWords.every(word =>
                    plasticName.includes(word) || plasticId.includes(word)
                );
            });

            // Sort the filtered data before displaying
            const sortedFilteredData = [...filteredData].sort((a, b) => {
                let aVal = a[currentPlasticSortColumn];
                let bVal = b[currentPlasticSortColumn];

                // Handle null/undefined values
                if (aVal === null || aVal === undefined) aVal = '';
                if (bVal === null || bVal === undefined) bVal = '';

                // Convert dates for comparison
                if (currentPlasticSortColumn.includes('date') && aVal && bVal) {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                }

                // Convert numbers for comparison
                if (currentPlasticSortColumn === 'id' || currentPlasticSortColumn.startsWith('val_') || currentPlasticSortColumn === 'discs_in_stock_and_uploaded') {
                    aVal = parseFloat(aVal) || 0;
                    bVal = parseFloat(bVal) || 0;
                }

                // Compare values
                if (aVal < bVal) return currentPlasticSortDirection === 'asc' ? -1 : 1;
                if (aVal > bVal) return currentPlasticSortDirection === 'asc' ? 1 : -1;
                return 0;
            });

            displayPlasticPricingData(sortedFilteredData);
        }

        function setupPlasticSearchFilter() {
            const searchInput = document.getElementById('plasticSearchInput');
            searchInput.addEventListener('input', function() {
                applyCurrentPlasticSearch();
            });
        }


    </script>
</body>
</html>
