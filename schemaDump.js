import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Supabase credentials using your service role key
const supabase = createClient(
  'https://aepabhlwpjfjulrjeitn.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzc4NjU0MSwiZXhwIjoyMDQ5MzYyNTQxfQ.FQyPTgdBP83VhuykdlZkagHADc5nBmx7-yd0JYt5aP8'
);

async function dumpSchema() {
  try {
    // SQL query to extract functions (excluding aggregates), views, and triggers.
    const schemaQuery = `
      SELECT 'FUNCTION: ' || pg_catalog.pg_get_functiondef(p.oid) AS ddl
      FROM pg_proc p
      JOIN pg_namespace n ON p.pronamespace = n.oid
      WHERE n.nspname = 'public'
        AND p.prokind != 'a'  -- Exclude aggregate functions

      UNION ALL

      SELECT 'VIEW: ' || pg_get_viewdef(c.oid, true) AS ddl
      FROM pg_class c
      JOIN pg_namespace n ON c.relnamespace = n.oid
      WHERE n.nspname = 'public' AND c.relkind = 'v'

      UNION ALL

      SELECT 'TRIGGER: ' || tgname || ' ' || pg_get_triggerdef(oid) AS ddl
      FROM pg_trigger
      WHERE NOT tgisinternal;
    `;

    // Execute the SQL query via the Supabase RPC function
    const { data, error } = await supabase.rpc('sql', { query: schemaQuery });

    if (error) {
      throw new Error(error.message);
    }

    // Combine the DDL statements
    const ddlStatements = data.map(row => row.ddl).join('\n\n');

    // Write to file
    fs.writeFileSync('disc_public_schema.sql', ddlStatements);
    console.log('✅ Schema exported successfully to disc_public_schema.sql');

  } catch (err) {
    console.error('❌ Error exporting schema:', err);
  }
}

dumpSchema();
