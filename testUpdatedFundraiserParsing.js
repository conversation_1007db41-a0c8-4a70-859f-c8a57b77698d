// Test the updated fundraiser parsing functions

// Copy the updated functions from fullDiscraftImport.js
function standardizePlasticName(rawPlastic, rawModel, vendorDescription = '') {
    if (!rawPlastic && !rawModel) return 'Unknown';

    const plastic = rawPlastic?.toString().trim() || '';
    const model = rawModel?.toString().trim() || '';

    // Handle fundraiser items first
    if (model.includes('Fundraiser - <PERSON>')) {
        const match = model.match(/Fundraiser - <PERSON> (.+?) (Thrasher|Buzzz|[A-Z][a-z]+)\s*\(/);
        if (match) {
            const plasticPart = match[1].trim(); // "Z Jawbreaker" or "Big Z"
            if (plasticPart.includes('Z Jawbreaker')) {
                return 'Elite Z Jawbreaker';
            } else if (plasticPart.includes('Big Z')) {
                return 'Big Z Collection';
            }
        }
        return 'Unknown'; // Fallback for unrecognized fundraiser items
    }

    // Handle special signature series first
    if (plastic.includes('<PERSON><PERSON><PERSON> Z FLX')) {
        return 'Elite Z FLX Confetti';
    }

    if (plastic.includes('Anthony Barela Signature Series Z')) {
        return 'Elite Z';
    }

    // Standard mappings
    const plasticMappings = {
        'Z': 'Elite Z',
        'ESP': 'ESP',
        'Jawbreaker': 'Jawbreaker'
    };

    return plasticMappings[plastic] || plastic || 'Unknown';
}

function parseMoldAndStamp(rawModel, rawPlastic, vendorDescription = '') {
    const model = rawModel?.toString().trim() || '';
    const plastic = rawPlastic?.toString().trim() || '';
    const vendorDesc = vendorDescription?.toString().trim() || '';

    // Handle fundraiser items first
    if (model.includes('Fundraiser - Ben Askren')) {
        const match = model.match(/Fundraiser - Ben Askren (.+?) (Thrasher|Buzzz|[A-Z][a-z]+)\s*\(/);
        if (match) {
            const moldName = match[2]; // "Thrasher" or "Buzzz"
            return {
                mold_name: moldName,
                stamp_name: 'Live Free - Be Happy - Funky Ben Askren Fundraiser'
            };
        }
    }

    // Default parsing
    return {
        mold_name: model || 'Unknown',
        stamp_name: 'Stock'
    };
}

// Test cases
console.log('🧪 Testing updated fundraiser parsing functions...\n');

const testCases = [
    {
        name: 'Ben Askren Z Jawbreaker Thrasher',
        rawPlastic: 'SPECIAL',
        rawModel: 'Fundraiser - Ben Askren Z Jawbreaker Thrasher (msrp $19.99, cost $10.00)',
        expected: {
            plastic: 'Elite Z Jawbreaker',
            mold: 'Thrasher',
            stamp: 'Live Free - Be Happy - Funky Ben Askren Fundraiser'
        }
    },
    {
        name: 'Ben Askren Big Z Buzzz',
        rawPlastic: 'SPECIAL',
        rawModel: 'Fundraiser - Ben Askren Big Z Buzzz  (msrp $19.99, cost $10.00)',
        expected: {
            plastic: 'Big Z Collection',
            mold: 'Buzzz',
            stamp: 'Live Free - Be Happy - Funky Ben Askren Fundraiser'
        }
    },
    {
        name: 'Header row (should not be parsed as fundraiser)',
        rawPlastic: 'LIMITED RELEASE - Fundraiser for Ben Askren',
        rawModel: '',
        expected: {
            plastic: 'Unknown',
            mold: 'Unknown',
            stamp: 'Stock'
        }
    },
    {
        name: 'Regular product (should not be affected)',
        rawPlastic: 'Z',
        rawModel: 'Buzzz',
        expected: {
            plastic: 'Elite Z',
            mold: 'Buzzz',
            stamp: 'Stock'
        }
    }
];

testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. Testing: ${testCase.name}`);
    console.log(`   Input: rawPlastic="${testCase.rawPlastic}", rawModel="${testCase.rawModel}"`);
    
    const plastic = standardizePlasticName(testCase.rawPlastic, testCase.rawModel);
    const { mold_name, stamp_name } = parseMoldAndStamp(testCase.rawModel, testCase.rawPlastic);
    
    console.log(`   Output: plastic="${plastic}", mold="${mold_name}", stamp="${stamp_name}"`);
    console.log(`   Expected: plastic="${testCase.expected.plastic}", mold="${testCase.expected.mold}", stamp="${testCase.expected.stamp}"`);
    
    const plasticMatch = plastic === testCase.expected.plastic;
    const moldMatch = mold_name === testCase.expected.mold;
    const stampMatch = stamp_name === testCase.expected.stamp;
    
    if (plasticMatch && moldMatch && stampMatch) {
        console.log('   ✅ PASS\n');
    } else {
        console.log('   ❌ FAIL');
        if (!plasticMatch) console.log(`      Plastic mismatch: got "${plastic}", expected "${testCase.expected.plastic}"`);
        if (!moldMatch) console.log(`      Mold mismatch: got "${mold_name}", expected "${testCase.expected.mold}"`);
        if (!stampMatch) console.log(`      Stamp mismatch: got "${stamp_name}", expected "${testCase.expected.stamp}"`);
        console.log('');
    }
});

console.log('🎉 Fundraiser parsing test completed!');
