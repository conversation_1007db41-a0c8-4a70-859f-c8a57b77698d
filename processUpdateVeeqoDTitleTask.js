/**
 * Process update_veeqo_d_title task
 *
 * This task updates a disc's Veeqo product title to match the disc's g_pull value.
 * It uses the disc's SKU (format: 'D' + disc.id) to find the Veeqo product
 * and updates the product title to the g_pull value.
 */

import fetch from 'node-fetch';

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('❌ VEEQO_API_KEY environment variable is not set');
}

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(endpoint, options);

    if (!response.ok) {
      const errorText = await response.text();
      return { success: false, error: `${response.status}: ${errorText}`, status: response.status };
    }

    // Many Veeqo endpoints (e.g., PUT) may return 204 No Content or an empty body.
    const rawText = await response.text();
    if (!rawText || rawText.trim().length === 0) {
      return { success: true, data: null, status: response.status };
    }

    // Try to parse JSON; if it fails, return raw text as data
    try {
      const json = JSON.parse(rawText);
      return { success: true, data: json, status: response.status };
    } catch (parseErr) {
      return { success: true, data: rawText, status: response.status };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
}

// Helpers for title handling
function normalizeTitle(t) {
  return (t ?? '').toString().replace(/\s+/g, ' ').trim();
}
function titlesEqual(a, b) {
  return normalizeTitle(a).toLowerCase() === normalizeTitle(b).toLowerCase();
}
function getSafeTitle(t) {
  // Veeqo likely enforces a max length; use 255 conservatively
  return normalizeTitle(t).slice(0, 255);
}
function extractTitle(data) {
  if (!data) return null;
  if (typeof data === 'string') return data;
  if (data.product && typeof data.product.title === 'string') return data.product.title;
  if (typeof data.title === 'string') return data.title;
  if (typeof data.name === 'string') return data.name;
  return null;
}

// Function to search for Veeqo products by SKU
async function searchVeeqoProductBySku(sku) {
  console.log(`[processUpdateVeeqoDTitleTask] Searching for Veeqo product with SKU: ${sku}`);

  // Try the products search endpoint
  const result = await makeVeeqoRequest(`https://api.veeqo.com/products?query=${encodeURIComponent(sku)}`);

  if (!result.success) {
    console.error(`[processUpdateVeeqoDTitleTask] Failed to search Veeqo products: ${result.error}`);
    return [];
  }

  if (!Array.isArray(result.data)) {
    console.log(`[processUpdateVeeqoDTitleTask] Unexpected response format from Veeqo search`);
    return [];
  }

  // Filter results to find products with matching SKU in their sellables
  const matchingProducts = result.data.filter(product => {
    if (product.sellables && Array.isArray(product.sellables)) {
      return product.sellables.some(sellable => sellable.sku_code === sku);
    }
    return false;
  });

  console.log(`[processUpdateVeeqoDTitleTask] Found ${matchingProducts.length} products with matching SKU`);
  return matchingProducts;
}

// Function to update Veeqo product title
async function updateVeeqoProductTitle(productId, newTitle) {
  const safeTitle = getSafeTitle(newTitle);
  console.log(`[processUpdateVeeqoDTitleTask] Updating Veeqo product ${productId} title to: "${safeTitle}"`);

  const url = `https://api.veeqo.com/products/${productId}`;

  // Try PUT first
  let result = await makeVeeqoRequest(url, 'PUT', { product: { title: safeTitle } });

  if (!result.success && result.status === 400) {
    // Try PATCH fallback
    console.warn(`[processUpdateVeeqoDTitleTask] PUT returned 400 for product ${productId}; trying PATCH`);
    result = await makeVeeqoRequest(url, 'PATCH', { product: { title: safeTitle } });
  }

  if (result.success) {
    console.log(`[processUpdateVeeqoDTitleTask] Update call returned success for product ${productId}`);
    return result.data;
  }

  // Verification: fetch product and compare current title
  console.warn(`[processUpdateVeeqoDTitleTask] Update call failed (status=${result.status || 'n/a'}). Verifying current title for product ${productId}...`);
  try {
    const verify = await makeVeeqoRequest(url, 'GET');
    if (verify.success) {
      const currentTitle = extractTitle(verify.data);
      if (titlesEqual(currentTitle, safeTitle)) {
        console.log(`[processUpdateVeeqoDTitleTask] Verification shows title already set correctly for product ${productId}`);
        return verify.data;
      }
      console.warn(`[processUpdateVeeqoDTitleTask] Verification title mismatch for product ${productId}. current="${currentTitle}", desired="${safeTitle}"`);
    } else {
      console.warn(`[processUpdateVeeqoDTitleTask] Verification GET failed for product ${productId}: ${verify.error}`);
    }
  } catch (vErr) {
    console.warn(`[processUpdateVeeqoDTitleTask] Verification exception for product ${productId}: ${vErr.message}`);
  }

  throw new Error(`Failed to update Veeqo product title: ${result.error || 'Unknown error'}`);
}

// Main task processor function
export async function processUpdateVeeqoDTitleTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processUpdateVeeqoDTitleTask] Processing task ${task.id}`);

  try {
    const payload = task.payload;

    if (!payload || !payload.id) {
      throw new Error('Task payload missing required id field');
    }

    const discId = payload.id;
    console.log(`[processUpdateVeeqoDTitleTask] Disc ID: ${discId}`);
    // Title will be loaded from t_discs.g_pull at processing time

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Get the disc record to find the shopify_sku
    console.log(`[processUpdateVeeqoDTitleTask] Getting disc record for ID: ${discId}`);

    const { data: discRecord, error: discError } = await supabase
      .from('t_discs')
      .select('id, shopify_sku, shopify_uploaded_at, sold_date, g_pull')
      .eq('id', discId)
      .single();

    if (discError || !discRecord) {
      throw new Error(`Failed to get disc record: ${discError?.message || 'Disc not found'}`);
    }


    const newTitle = discRecord.g_pull;
    console.log(`[processUpdateVeeqoDTitleTask] Current g_pull (title) at processing time: "${newTitle}"`);
    if (!newTitle || newTitle.trim() === '') {
      throw new Error('New title (g_pull) is empty or null');
    }

    // Verify the disc is still eligible (unsold and uploaded to Shopify)
    if (discRecord.sold_date !== null) {
      console.log(`[processUpdateVeeqoDTitleTask] Disc ${discId} is now sold, skipping title update`);
      await updateTaskStatus(task.id, 'completed', {
        message: `Disc is now sold, skipping title update`,
        disc_id: discId,
        sold_date: discRecord.sold_date,
        new_title: newTitle
      });

      return;
    }

    if (!discRecord.shopify_uploaded_at) {
      console.log(`[processUpdateVeeqoDTitleTask] Disc ${discId} is no longer uploaded to Shopify, skipping title update`);
      await updateTaskStatus(task.id, 'completed', {
        message: `Disc is no longer uploaded to Shopify, skipping title update`,
        disc_id: discId,
        new_title: newTitle
      });
      return;
    }

    if (!discRecord.shopify_sku) {
      console.log(`[processUpdateVeeqoDTitleTask] Disc ${discId} has no shopify_sku, cannot update Veeqo title`);
      await updateTaskStatus(task.id, 'completed', {
        message: `Disc has no shopify_sku, cannot update Veeqo title`,
        disc_id: discId,
        new_title: newTitle
      });
      return;
    }

    const sku = discRecord.shopify_sku;
    console.log(`[processUpdateVeeqoDTitleTask] Looking for Veeqo product with SKU: ${sku}`);

    // Search for the Veeqo product by SKU
    const veeqoProducts = await searchVeeqoProductBySku(sku);

    if (!veeqoProducts || veeqoProducts.length === 0) {
      console.log(`[processUpdateVeeqoDTitleTask] No Veeqo product found for SKU ${sku}`);
      const attempt = typeof payload?.retry_count === 'number' ? payload.retry_count : 0;
      const delays = [2, 12, 48]; // hours
      if (attempt < delays.length) {
        const nextDelay = delays[attempt];
        const nextAttempt = attempt + 1;
        console.log(`[processUpdateVeeqoDTitleTask] Scheduling retry attempt ${nextAttempt} in ${nextDelay} hour(s)`);
        const { error: enqueueError } = await supabase
          .from('t_task_queue')
          .insert([{
            task_type: 'update_veeqo_d_title',
            payload: { id: discId, retry_count: nextAttempt },
            status: 'pending',
            scheduled_at: new Date(Date.now() + nextDelay * 60 * 60 * 1000).toISOString(),
            created_at: new Date().toISOString(),
            enqueued_by: `update_veeqo_d_title retry attempt ${nextAttempt}`
          }]);
        if (enqueueError) {
          const errMsg = `[processUpdateVeeqoDTitleTask] Failed to enqueue retry attempt ${nextAttempt}: ${enqueueError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Enqueue retry for disc_id=${discId}`);
          // Mark as error since we could not enqueue retry
          await updateTaskStatus(task.id, 'error', {
            message: `No Veeqo product found and failed to enqueue retry`,
            disc_id: discId,
            sku: sku,
            attempt: attempt,
            next_attempt: nextAttempt,
            error: enqueueError.message
          });
        } else {
          await updateTaskStatus(task.id, 'completed', {
            message: `No Veeqo product found; scheduled retry`,
            disc_id: discId,
            sku: sku,
            attempt: attempt,
            next_attempt: nextAttempt,
            next_delay_hours: nextDelay,
            new_title: newTitle
          });
        }
      } else {
        await updateTaskStatus(task.id, 'error', {
          message: `No Veeqo product found after final retry attempt`,
          disc_id: discId,
          sku: sku,
          attempt: attempt,
          new_title: newTitle
        });
      }
      return;
    }

    console.log(`[processUpdateVeeqoDTitleTask] Found ${veeqoProducts.length} Veeqo product(s) for SKU ${sku}`);

    // Update title for all Veeqo products with this SKU
    let successCount = 0;
    let failureCount = 0;
    let errors = [];
    const updatedProductIds = [];

    for (const product of veeqoProducts) {
      try {
        // If the current product title already matches, count as success and skip update
        if (typeof product.title === 'string' && titlesEqual(product.title, newTitle)) {
          console.log(`[processUpdateVeeqoDTitleTask] Title already matches for Veeqo product ID ${product.id}; skipping update`);
          successCount++;
          updatedProductIds.push(product.id);
          continue;
        }

        console.log(`[processUpdateVeeqoDTitleTask] Updating title for Veeqo product ID ${product.id} ("${product.title}")`);

        await updateVeeqoProductTitle(product.id, newTitle);

        console.log(`[processUpdateVeeqoDTitleTask] Successfully updated Veeqo product ${product.id}`);
        successCount++;
        updatedProductIds.push(product.id);

      } catch (error) {
        console.error(`[processUpdateVeeqoDTitleTask] Failed to update Veeqo product ${product.id}: ${error.message}`);

        // Verify current title; if it already matches, treat as success
        try {
          const verifyResp = await makeVeeqoRequest(`https://api.veeqo.com/products/${product.id}`);
          const currentTitle = extractTitle(verifyResp.data);
          if (titlesEqual(currentTitle, newTitle)) {
            console.log(`[processUpdateVeeqoDTitleTask] Title is already set correctly for product ${product.id}; treating as success`);
            successCount++;
            updatedProductIds.push(product.id);
            continue;
          }
        } catch (verifyErr) {
          console.warn(`[processUpdateVeeqoDTitleTask] Verification GET failed for product ${product.id}: ${verifyErr.message}`);
        }

        failureCount++;
        errors.push(`Product ${product.id}: ${error.message}`);
      }
    }

    // Determine final task status
    if (successCount > 0 && failureCount === 0) {
      // All updates successful
      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully updated title for ${successCount} Veeqo product(s)`,
        disc_id: discId,
        sku: sku,
        new_title: newTitle,
        veeqo_products_updated: successCount,
        veeqo_product_ids: updatedProductIds
      });
    } else if (successCount > 0 && failureCount > 0) {
      // Partial success
      await updateTaskStatus(task.id, 'completed', {
        message: `Partially successful: ${successCount} updated, ${failureCount} failed`,
        disc_id: discId,
        sku: sku,
        new_title: newTitle,
        veeqo_products_updated: successCount,
        veeqo_products_failed: failureCount,
        errors: errors,
        veeqo_product_ids: updatedProductIds
      });
    } else {
      // All updates failed
      throw new Error(`Failed to update any Veeqo products. Errors: ${errors.join('; ')}`);
    }

  } catch (error) {
    const errMsg = `[processUpdateVeeqoDTitleTask] Exception while processing task ${task.id}: ${error.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to update Veeqo product title due to an unexpected error.",
      error: error.message,
      disc_id: task.payload?.id
    });
  }
}

export default processUpdateVeeqoDTitleTask;
