// check_sales_orders_triggers.js - Inspect triggers and function for t_sales_orders
import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function exec(sql){
  // Try common arg names
  let res = await supabase.rpc('exec_sql', { sql_query: sql });
  if (res.error) res = await supabase.rpc('exec_sql', { sql_statement: sql });
  if (res.error) res = await supabase.rpc('execute_sql', { sql });
  return res;
}

async function main(){
  try {
    console.log('Listing triggers on public.t_sales_orders...');
    const trgSql = `
      SELECT t.tgname AS trigger_name, pg_get_triggerdef(t.oid) AS trigger_def
      FROM pg_trigger t
      JOIN pg_class c ON c.oid = t.tgrelid
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'public' AND c.relname = 't_sales_orders' AND NOT t.tgisinternal
      ORDER BY 1;`;
    let { data: trgData, error: trgErr } = await exec(trgSql);
    if (trgErr) throw new Error(trgErr.message);
    console.log(JSON.stringify(trgData, null, 2));

    console.log('\nFetching function definition for public.fn_create_movements_for_completed_sale...');
    const fnSql = `SELECT pg_get_functiondef('public.fn_create_movements_for_completed_sale'::regproc) AS fn_def;`;
    let { data: fnData, error: fnErr } = await exec(fnSql);
    if (fnErr) {
      console.warn('Could not fetch function definition:', fnErr.message);
    } else {
      console.log((fnData && fnData.fn_def) ? fnData.fn_def : JSON.stringify(fnData));
    }
  } catch (e) {
    console.error('Error:', e?.message || e);
    process.exit(1);
  }
}

main();

