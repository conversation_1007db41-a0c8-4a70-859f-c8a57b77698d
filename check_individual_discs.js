import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkIndividualDiscs() {
    console.log('Checking for individual disc products...\n');
    
    try {
        // Get individual disc titles (not sets, not mystery)
        const { data: discTitles, error: titlesError } = await supabase
            .from('it_dd_osl')
            .select('product_title, variant_title, product_vendor')
            .eq('product_product_type', 'Discs')
            .not('product_title', 'ilike', '%Set%')
            .not('product_title', 'ilike', '%Mystery%')
            .not('product_title', 'ilike', '%Box%')
            .limit(30);
        
        if (titlesError) {
            console.error('Error fetching disc titles:', titlesError);
            return;
        }
        
        console.log('Individual disc products:');
        console.log('=====================================');
        discTitles.forEach((item, index) => {
            console.log(`${index + 1}. [${item.product_vendor}] "${item.product_title}"`);
            console.log(`   Variant: "${item.variant_title}"`);
            console.log('---');
        });
        
        // Check if there are any Dynamic Discs products at all
        const { data: ddCount, error: ddError } = await supabase
            .from('it_dd_osl')
            .select('*', { count: 'exact', head: true })
            .eq('product_vendor', 'Dynamic Discs');
        
        console.log(`\nDynamic Discs products in table: ${ddCount || 0}`);
        
        // Check what brands we have molds and plastics for
        console.log('\nBrands with molds and plastics defined:');
        console.log('=====================================');
        
        const { data: brandsWithMolds, error: brandsError } = await supabase
            .from('t_brands')
            .select(`
                id, 
                brand, 
                code,
                molds:t_molds(count),
                plastics:t_plastics(count)
            `);
        
        if (!brandsError) {
            brandsWithMolds
                .filter(brand => brand.molds.length > 0 || brand.plastics.length > 0)
                .forEach(brand => {
                    console.log(`${brand.brand} (ID: ${brand.id}): ${brand.molds.length} molds, ${brand.plastics.length} plastics`);
                });
        }
        
    } catch (error) {
        console.error('Query failed:', error);
    }
}

// Run the query
checkIndividualDiscs();
