const fs = require('fs');
const path = require('path');
const file = path.resolve('admin.html');
const txt = fs.readFileSync(file, 'utf8');
const start = txt.indexOf('<script>');
const end = txt.lastIndexOf('</script>');
if (start < 0 || end < 0) { console.log('script tags not found'); process.exit(1); }
const js = txt.slice(start + '<script>'.length, end);

function countPair(open, close){
  let o=0,c=0; for (const ch of js){ if(ch===open)o++; else if(ch===close)c++; }
  return [o,c];
}
const [co,cc] = countPair('{','}');
const [po,pc] = countPair('(',')');
const [so,sc] = [ (js.match(/\[/g)||[]).length, (js.match(/\]/g)||[]).length ];
const backticks = (js.match(/`/g)||[]).length;
console.log('curly',co,cc);
console.log('paren',po,pc);
console.log('square',so,sc);
console.log('backticks',backticks);

function analyze(){
  const stack=[]; let line=1; let col=0; let inStr=false; let strCh=''; let esc=false;
  for(let i=0;i<js.length;i++){
    const ch=js[i];
    if(ch==='\n'){line++; col=0; continue;} col++;
    if(inStr){ if(esc){esc=false; continue;} if(ch==='\\'){esc=true; continue;} if(ch===strCh){inStr=false;} continue; }
    if(ch==='"' || ch==="'" ){ inStr=true; strCh=ch; continue; }
    if(ch==='`'){ if(inStr && strCh==='`'){ inStr=false; } else { inStr=true; strCh='`'; } continue; }
    if(ch==='{'||ch==='('||ch==='['){ stack.push({ch,line,col}); }
    else if((ch==='}'&&stack[stack.length-1]?.ch==='{') || (ch===')'&&stack[stack.length-1]?.ch==='(') || (ch===']'&&stack[stack.length-1]?.ch==='[')) { stack.pop(); }
    else if(ch==='}'||ch===')'||ch===']'){ stack.push({ch,line,col,err:true}); break; }
  }
  return stack;
}
const st = analyze();
console.log('stack size', st.length);
console.log(st.slice(-10));
if (st.length){
  const ln = st[st.length-1].line;
  const jsLines = js.split(/\n/);
  const startCtx = Math.max(1, ln-5);
  const endCtx = Math.min(jsLines.length, ln+60);
  console.log('Context around js line', ln);
  for(let i=startCtx;i<=endCtx;i++){
    console.log(String(i).padStart(6,' ')+': '+jsLines[i-1]);
  }
}

