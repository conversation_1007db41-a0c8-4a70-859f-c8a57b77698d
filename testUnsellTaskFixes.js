// testUnsellTaskFixes.js - Test the fixes for disc_updated_unsell task
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testUnsellTaskFixes() {
  console.log('🧪 Testing Disc Updated Unsell Task Fixes');
  console.log('==========================================');

  try {
    // Check the problematic task 192138
    console.log('\n🔍 Checking Task 192138 (the one with currentDisc error):');
    
    const { data: task192138, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 192138)
      .single();

    if (taskError) {
      console.error('❌ Error fetching task 192138:', taskError.message);
      return;
    }

    console.log(`📊 Task 192138 Status: ${task192138.status}`);
    console.log(`📄 Task 192138 Message: ${task192138.result?.message || 'No message'}`);
    console.log(`❌ Task 192138 Error: ${task192138.result?.error || 'No error'}`);
    console.log(`🔗 Next Task Enqueued: ${task192138.result?.next_task_enqueued || 'Not specified'}`);

    // Check if task 192139 was incorrectly enqueued
    console.log('\n🔍 Checking Task 192139 (the incorrectly enqueued next task):');
    
    const { data: task192139, error: nextTaskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 192139)
      .single();

    if (nextTaskError) {
      console.error('❌ Error fetching task 192139:', nextTaskError.message);
      return;
    }

    console.log(`📊 Task 192139 Type: ${task192139.task_type}`);
    console.log(`📊 Task 192139 Status: ${task192139.status}`);
    console.log(`🔗 Task 192139 Enqueued By: ${task192139.enqueued_by}`);
    console.log(`📅 Task 192139 Created: ${task192139.created_at}`);

    // Verify the issues
    const hasVariableError = task192138.result?.error?.includes('currentDisc is not defined');
    const incorrectlyEnqueued = task192139.enqueued_by?.includes('disc_updated_unsell') &&
                               task192138.status === 'error';

    console.log('\n📋 ISSUES IDENTIFIED:');
    console.log('=====================');
    if (hasVariableError) {
      console.log('❌ Issue 1: Variable scoping error (currentDisc is not defined)');
    }
    if (incorrectlyEnqueued) {
      console.log('❌ Issue 2: Next task enqueued despite error status');
    }

    console.log('\n🔧 FIXES IMPLEMENTED:');
    console.log('=====================');
    console.log('1. ✅ Fixed variable scoping:');
    console.log('   - Added currentDiscData parameter to processNextTask function');
    console.log('   - Used optional chaining: currentDiscData?.sold_channel');
    console.log('   - Passed currentDisc data to all processNextTask calls');
    console.log('');
    console.log('2. ✅ Fixed error handling:');
    console.log('   - Added return statement in catch block');
    console.log('   - Enhanced error result with workflow_stopped flag');
    console.log('   - Prevents processNextTask call on exceptions');

    console.log('\n📋 Expected Behavior After Fix:');
    console.log('===============================');
    console.log('✅ Variable errors → Fixed with proper scoping');
    console.log('✅ Task errors → Status: error, next_task_enqueued: false');
    console.log('✅ Workflow stops → No next task enqueued on error');
    console.log('✅ Success cases → Continue to work normally');

    console.log('\n🧪 Test New Workflow:');
    console.log('=====================');
    console.log('1. Create a new disc_updated_need_to_reset task');
    console.log('2. Monitor the disc_updated_unsell task');
    console.log('3. Verify proper variable handling');
    console.log('4. Check that errors properly stop the workflow');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testUnsellTaskFixes()
  .then(() => {
    console.log('\n🏁 Analysis completed');
    console.log('The disc_updated_unsell task should now handle errors properly!');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Analysis failed:', error);
    process.exit(1);
  });
