const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://ixpqhqjqjqjqjqjqjqjq.supabase.co';
const supabaseKey = 'your-anon-key-here'; // Replace with actual key
const supabase = createClient(supabaseUrl, supabaseKey);

async function testFutureOslTask() {
  try {
    console.log('Testing future OSL task logic...');

    // Create a test OSL task scheduled 1 hour in the future
    const futureTime = new Date();
    futureTime.setHours(futureTime.getHours() + 1);

    const testPayload = { id: 12345 }; // Replace with actual OSL ID for testing

    console.log('Creating test task scheduled for:', futureTime.toISOString());

    const { data: taskData, error: taskError } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'publish_product_osl',
        payload: testPayload,
        status: 'pending',
        scheduled_at: futureTime.toISOString(),
        created_at: new Date().toISOString(),
        enqueued_by: 'test_script'
      })
      .select()
      .single();

    if (taskError) {
      console.error('Error creating test task:', taskError);
      return;
    }

    console.log('Created test task:', taskData);

    // Now test the logic
    const task = taskData;
    const now = new Date();
    const scheduledAt = new Date(task.scheduled_at);
    const thirtyMinutesFromNow = new Date(now.getTime() + (30 * 60 * 1000));

    console.log('Task scheduled at:', scheduledAt.toISOString());
    console.log('Current time:', now.toISOString());
    console.log('30 minutes from now:', thirtyMinutesFromNow.toISOString());
    console.log('Is task scheduled more than 30 minutes in future?', scheduledAt > thirtyMinutesFromNow);

    // Clean up - delete the test task
    const { error: deleteError } = await supabase
      .from('t_task_queue')
      .delete()
      .eq('id', task.id);

    if (deleteError) {
      console.error('Error deleting test task:', deleteError);
    } else {
      console.log('Test task cleaned up successfully');
    }

  } catch (err) {
    console.error('Exception in test:', err.message);
  }
}

// Run the test
testFutureOslTask();
