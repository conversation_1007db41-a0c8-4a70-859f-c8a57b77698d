import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkDynamicDiscsPlastics() {
    console.log('Checking Dynamic Discs plastics...\n');
    
    try {
        // Get Dynamic Discs brand ID
        const { data: brandData, error: brandError } = await supabase
            .from('t_brands')
            .select('id, brand')
            .eq('brand', 'Dynamic Discs');
        
        if (brandError || !brandData.length) {
            console.error('Error finding Dynamic Discs brand:', brandError);
            return;
        }
        
        const ddBrandId = brandData[0].id;
        console.log(`Dynamic Discs brand ID: ${ddBrandId}`);
        
        // Get all Dynamic Discs plastics
        const { data: plastics, error: plasticsError } = await supabase
            .from('t_plastics')
            .select('plastic, code')
            .eq('brand_id', ddBrandId)
            .order('plastic');
        
        if (plasticsError) {
            console.error('Error fetching plastics:', plasticsError);
            return;
        }
        
        console.log(`\nDynamic Discs plastics (${plastics.length} total):`);
        console.log('=====================================');
        
        plastics.forEach(plastic => {
            console.log(`${plastic.plastic} (${plastic.code})`);
        });
        
        // Check specifically for Lucid variants
        console.log('\nLucid-related plastics:');
        console.log('=======================');
        const lucidPlastics = plastics.filter(p => p.plastic.toLowerCase().includes('lucid'));
        lucidPlastics.forEach(plastic => {
            console.log(`${plastic.plastic} (${plastic.code})`);
        });
        
        // Check if "Lucid Moonshine" exists
        const lucidMoonshine = plastics.find(p => p.plastic === 'Lucid Moonshine');
        if (lucidMoonshine) {
            console.log(`\n✅ "Lucid Moonshine" plastic found: ${lucidMoonshine.plastic} (${lucidMoonshine.code})`);
        } else {
            console.log(`\n❌ "Lucid Moonshine" plastic NOT found`);
            
            // Check for similar names
            const moonshineVariants = plastics.filter(p => p.plastic.toLowerCase().includes('moonshine'));
            if (moonshineVariants.length > 0) {
                console.log('\nMoonshine variants found:');
                moonshineVariants.forEach(plastic => {
                    console.log(`  ${plastic.plastic} (${plastic.code})`);
                });
            }
        }
        
    } catch (error) {
        console.error('Query failed:', error);
    }
}

// Run the query
checkDynamicDiscsPlastics();
