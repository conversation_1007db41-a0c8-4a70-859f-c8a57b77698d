import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

async function main() {
  // Initialize the Supabase client.
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_KEY;
  const supabase = createClient(supabaseUrl, supabaseKey);

  // Get the batch size from command line arguments or use default
  const batchSize = process.argv[2] ? parseInt(process.argv[2]) : 100;
  
  console.log(`Starting to enqueue generate_osl_fields tasks for OSLs with null g_code (batch size: ${batchSize})...`);
  
  // Call the function to enqueue tasks
  const { data, error } = await supabase.rpc(
    'fn_enqueue_generate_osl_fields_for_null_g_code',
    { batch_size: batchSize }
  );
  
  if (error) {
    console.error('Error calling fn_enqueue_generate_osl_fields_for_null_g_code:', error);
    process.exit(1);
  }
  
  // Log the results
  console.log(`Successfully enqueued ${data.length} tasks for OSLs with null g_code.`);
  
  if (data.length > 0) {
    console.log('\nEnqueued tasks:');
    data.forEach(row => {
      console.log(`- OSL ID: ${row.osl_id}, Task ID: ${row.task_id}`);
    });
  } else {
    console.log('\nNo OSLs with null g_code were found.');
  }
  
  process.exit(0);
}

main();
