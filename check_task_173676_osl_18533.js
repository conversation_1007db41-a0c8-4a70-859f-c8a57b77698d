// check_task_173676_osl_18533.js
// Check task 173676 and OSL 18533 status

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkTask173676AndOSL18533() {
  try {
    console.log('Checking task 173676 and OSL 18533...');
    
    // Check task 173676
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 173676)
      .single();
    
    if (taskError) {
      console.error('Error fetching task 173676:', taskError);
      return;
    }
    
    console.log('📋 Task 173676 Details:');
    console.log('Status:', task.status);
    console.log('Created:', task.created_at);
    console.log('Processed:', task.processed_at);
    console.log('Enqueued By:', task.enqueued_by);
    
    if (task.result) {
      const result = typeof task.result === 'string' ? JSON.parse(task.result) : task.result;
      console.log('\nTask Result:');
      console.log('Message:', result.message);
      console.log('Error:', result.error);
      
      if (result.error === 'Unknown error') {
        console.log('❌ ISSUE: Task shows "Unknown error" instead of detailed error message');
      } else if (result.error && result.error.includes('MANUAL FIX REQUIRED')) {
        console.log('✅ GOOD: Task has detailed MANUAL FIX REQUIRED error message');
      }
    }
    
    // Check OSL 18533
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, max_weight, ready_button, ready, shopify_uploaded_at, shopify_product_uploaded_notes')
      .eq('id', 18533)
      .single();
    
    if (oslError) {
      console.error('Error fetching OSL 18533:', oslError);
      return;
    }
    
    console.log('\n📝 OSL 18533 Details:');
    console.log('Max Weight:', osl.max_weight + 'g');
    console.log('Ready Button:', osl.ready_button);
    console.log('Ready:', osl.ready);
    console.log('Shopify Uploaded At:', osl.shopify_uploaded_at);
    console.log('Upload Notes:', osl.shopify_product_uploaded_notes);
    
    if (osl.shopify_product_uploaded_notes && osl.shopify_product_uploaded_notes.includes('MANUAL FIX REQUIRED')) {
      console.log('\n✅ CONFIRMED: OSL has detailed MANUAL FIX REQUIRED error message in database');
      console.log('🎯 PROBLEM: Task result shows "Unknown error" but database has detailed error');
    }
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

checkTask173676AndOSL18533();
