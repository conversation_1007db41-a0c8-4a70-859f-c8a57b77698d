// processOslUpdatedUnlinkDiscsTask.js - Process osl_updated_unlink_discs tasks
import { createClient } from '@supabase/supabase-js';

// Function to process an osl_updated_unlink_discs task
export default async function processOslUpdatedUnlinkDiscsTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processOslUpdatedUnlinkDiscsTask.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[processOslUpdatedUnlinkDiscsTask.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[processOslUpdatedUnlinkDiscsTask.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[processOslUpdatedUnlinkDiscsTask.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      } else if (typeof task.payload === 'string') {
        // Parse JSON string
        console.log(`[processOslUpdatedUnlinkDiscsTask.js] Task ${task.id} payload is a string, parsing as JSON`);
        payload = JSON.parse(task.payload);
      } else {
        throw new Error(`Unexpected payload format: ${typeof task.payload}`);
      }
    } catch (err) {
      const errMsg = `[processOslUpdatedUnlinkDiscsTask.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process OSL updated unlink discs task. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[processOslUpdatedUnlinkDiscsTask.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process OSL updated unlink discs task. Missing OSL id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const oslId = payload.id;
    console.log(`[processOslUpdatedUnlinkDiscsTask.js] Unlinking discs from OSL id=${oslId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Count how many discs are linked to this OSL
    const { count, error: countError } = await supabase
      .from('t_discs')
      .select('id', { count: 'exact', head: true })
      .eq('order_sheet_line_id', oslId);

    if (countError) {
      const errMsg = `[processOslUpdatedUnlinkDiscsTask.js] Error counting linked discs: ${countError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Counting linked discs for OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to count linked discs. Database error.",
        error: countError.message
      });
      return;
    }

    console.log(`[processOslUpdatedUnlinkDiscsTask.js] Found ${count} discs linked to OSL id=${oslId}`);

    if (count === 0) {
      console.log(`[processOslUpdatedUnlinkDiscsTask.js] No discs to unlink for OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `No discs to unlink for OSL id=${oslId}.`,
        osl_id: oslId,
        discs_unlinked: 0
      });
      return;
    }

    // Unlink all discs from this OSL
    const { error: updateError } = await supabase
      .from('t_discs')
      .update({
        order_sheet_line_id: null
      })
      .eq('order_sheet_line_id', oslId);

    if (updateError) {
      const errMsg = `[processOslUpdatedUnlinkDiscsTask.js] Error unlinking discs: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Unlinking discs for OSL id=${oslId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to unlink discs. Database error.",
        error: updateError.message
      });
      return;
    }

    console.log(`[processOslUpdatedUnlinkDiscsTask.js] Successfully unlinked ${count} discs from OSL id=${oslId}`);
    await updateTaskStatus(task.id, 'completed', {
      message: `Success! Unlinked ${count} discs from OSL id=${oslId}.`,
      osl_id: oslId,
      discs_unlinked: count
    });
  } catch (err) {
    const errMsg = `[processOslUpdatedUnlinkDiscsTask.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process OSL updated unlink discs task due to an unexpected error.",
      error: err.message
    });
  }
}
