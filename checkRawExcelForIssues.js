import XLSX from 'xlsx';
import path from 'path';

async function checkRawExcelForIssues() {
  try {
    console.log('🔍 Checking raw Excel data for ESP Roach and Pierce Drive...\n');
    
    const filePath = path.join(process.cwd(), 'data', 'external data', 'discraftstock.xlsx');
    console.log(`📁 Reading file: ${filePath}`);
    
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    console.log(`📊 Sheet: ${sheetName}`);
    
    // Check around line 279 for ESP Roach and line 322 for Pierce Drive
    for (let row = 275; row <= 325; row++) {
      // Read all columns for this row
      const rowData = {};
      for (let col = 0; col <= 30; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: row - 1, c: col });
        const cell = worksheet[cellAddress];
        if (cell && cell.v !== null && cell.v !== undefined && cell.v !== '') {
          rowData[`col_${col}`] = cell.v;
        }
      }
      
      // Check if this is ESP Roach or Pierce Drive
      const lineType = rowData.col_1 || ''; // Column B
      const model = rowData.col_4 || '';     // Column E
      
      if ((lineType.includes('ESP') && model.includes('Roach')) || 
          (lineType.includes('Pierce') && model.includes('Drive'))) {
        
        console.log(`\n📋 Row ${row}: Found ${lineType} | ${model}`);
        
        // Check vendor description columns (12-26)
        let vendorDesc = '';
        const vendorColumns = [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26];
        for (const col of vendorColumns) {
            const value = rowData[`col_${col}`];
            if (value && value !== 'N/A' && value !== 'n/a' && value !== 'NA' && value !== '') {
                vendorDesc = value.toString();
                break;
            }
        }
        
        console.log(`   📝 Vendor Description: "${vendorDesc}"`);
        
        // Check all columns to see where the vendor description might be
        console.log(`   🔍 All columns with data:`);
        Object.entries(rowData).forEach(([colKey, value]) => {
          const colIndex = parseInt(colKey.replace('col_', ''));
          const colLetter = String.fromCharCode(65 + colIndex);
          console.log(`      ${colLetter} (${colIndex}): "${value}"`);
          
          if (value.toString().toLowerCase().includes('white') || 
              value.toString().toLowerCase().includes('blank') ||
              value.toString().toLowerCase().includes('paige') ||
              value.toString().toLowerCase().includes('pierce') ||
              value.toString().toLowerCase().includes('zigzag') ||
              value.toString().toLowerCase().includes('stamp')) {
            console.log(`      ⭐ POTENTIAL VENDOR DESC: ${colLetter} (${colIndex}): "${value}"`);
          }
        });
        
        // Test the vendor description logic
        if (lineType.includes('ESP') && model.includes('Roach')) {
          if (vendorDesc.includes('White/Blank Top/Bottom stamp')) {
            console.log(`   ✅ ESP Roach vendor description logic would trigger: "Dye Line Blank Top Bottom"`);
          } else {
            console.log(`   ❌ ESP Roach vendor description logic would NOT trigger`);
            console.log(`   📝 Looking for: "White/Blank Top/Bottom stamp"`);
            console.log(`   📝 Found: "${vendorDesc}"`);
          }
        }
        
        if (lineType.includes('Pierce') && model.includes('Drive')) {
          if (vendorDesc.includes('Paige Pierce Drive')) {
            console.log(`   ✅ Pierce Drive vendor description logic would trigger: "Paige Pierce - PP Logo - ZigZag Pattern"`);
          } else {
            console.log(`   ❌ Pierce Drive vendor description logic would NOT trigger`);
            console.log(`   📝 Looking for: "Paige Pierce Drive"`);
            console.log(`   📝 Found: "${vendorDesc}"`);
          }
        }
      }
    }
    
    console.log('\n🎉 Raw Excel examination completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkRawExcelForIssues().catch(console.error);
