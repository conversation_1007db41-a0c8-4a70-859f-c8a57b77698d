import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkLines269to281() {
  try {
    console.log('🔍 Checking lines 269-281 parsing...\n');
    
    // Check McBeth NEW products (should be ESP with Dye Line Blank Top Bottom)
    const { data: mcbethNewProducts, error1 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .eq('raw_line_type', 'McBeth')
      .ilike('raw_model', '%NEW -%')
      .limit(10);
    
    if (error1) {
      console.error('Error querying McBeth NEW products:', error1);
    } else {
      console.log(`📋 Found ${mcbethNewProducts.length} McBeth NEW products:\n`);
      
      mcbethNewProducts.forEach((product, index) => {
        console.log(`${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
        console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        console.log(`   Expected: ESP | ${product.mold_name} | Dye Line Blank Top Bottom`);
        
        const isCorrect = product.plastic_name === 'ESP' && 
                         product.stamp_name === 'Dye Line Blank Top Bottom';
        
        console.log(`   ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
        console.log(`   Vendor Description: "${product.vendor_description}"`);
        console.log('');
      });
    }
    
    // Check ESP products with White/Blank stamp (lines 276-281)
    const { data: espWhiteProducts, error2 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .eq('raw_line_type', 'ESP')
      .ilike('vendor_description', '%White/Blank%')
      .limit(10);
    
    if (error2) {
      console.error('Error querying ESP White products:', error2);
    } else {
      console.log(`📋 Found ${espWhiteProducts.length} ESP White/Blank products:\n`);
      
      espWhiteProducts.forEach((product, index) => {
        console.log(`${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
        console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        console.log(`   Expected: ESP | ${product.mold_name} | Dye Line Blank Top Bottom`);
        
        const isCorrect = product.plastic_name === 'ESP' && 
                         product.stamp_name === 'Dye Line Blank Top Bottom';
        
        console.log(`   ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
        console.log(`   Vendor Description: "${product.vendor_description}"`);
        console.log('');
      });
    }
    
    // Check Pierce NEW products (should also be ESP with Dye Line Blank Top Bottom)
    const { data: pierceNewProducts, error3 } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
      .eq('raw_line_type', 'Pierce')
      .ilike('raw_model', '%NEW -%')
      .limit(5);
    
    if (error3) {
      console.error('Error querying Pierce NEW products:', error3);
    } else if (pierceNewProducts.length > 0) {
      console.log(`📋 Found ${pierceNewProducts.length} Pierce NEW products:\n`);
      
      pierceNewProducts.forEach((product, index) => {
        console.log(`${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
        console.log(`   Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        console.log(`   Expected: ESP | ${product.mold_name} | Dye Line Blank Top Bottom`);
        
        const isCorrect = product.plastic_name === 'ESP' && 
                         product.stamp_name === 'Dye Line Blank Top Bottom';
        
        console.log(`   ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
        console.log(`   Vendor Description: "${product.vendor_description}"`);
        console.log('');
      });
    }
    
    console.log('🎉 Check completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkLines269to281().catch(console.error);
