-- One-off: Enqueue product readiness checks for ALL products (uploaded or not)
-- This will cause the worker to re-evaluate and update t_products.todo for every product
DO $$
DECLARE
  v_enqueued_count integer := 0;
BEGIN
  INSERT INTO public.t_task_queue (task_type, payload, status, scheduled_at, created_at, enqueued_by)
  SELECT
    'check_if_product_is_ready',
    jsonb_build_object('id', p.id),
    'pending',
    NOW(),
    NOW(),
    'catchup:check_if_product_is_ready_all'
  FROM public.t_products p
  WHERE NOT EXISTS (
    SELECT 1 FROM public.t_task_queue tq
    WHERE tq.task_type = 'check_if_product_is_ready'
      AND (tq.status = 'pending' OR tq.status = 'processing')
      AND (tq.payload->>'id')::INT = p.id
  );

  GET DIAGNOSTICS v_enqueued_count = ROW_COUNT;
  RAISE NOTICE 'Enqueued % check_if_product_is_ready tasks (all products)', v_enqueued_count;
END $$;

