import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM3ODY1NDEsImV4cCI6MjA0OTM2MjU0MX0.MtBXMZt7rCqXd6c9clhrNoVtfOxEilX2C8oboMceOGg';
const supabase = createClient(supabaseUrl, supabaseKey);

const testMatchingFunction = async () => {
  try {
    console.log('Testing find_matching_osl function directly...');
    
    // Disc data
    const discId = 421349;
    const mpsId = 19105;
    const colorId = 6;
    const weight = 172.2;
    
    console.log(`Disc ID: ${discId}, MPS ID: ${mpsId}, Color ID: ${colorId}, Weight: ${weight}`);
    
    // Call the find_matching_osl function directly
    console.log('Calling find_matching_osl function...');
    const { data, error } = await supabase.rpc(
      'find_matching_osl',
      {
        mps_id_param: mpsId,
        color_id_param: colorId,
        weight_param: weight
      }
    );
    
    if (error) {
      console.error('Error calling find_matching_osl:', error);
      return;
    }
    
    console.log('Function result:', data);
    
    if (data && data.length > 0) {
      console.log('Debug info:', data[0].debug_info);
      console.log('OSL ID:', data[0].osl_id);
    } else {
      console.log('No results returned from function');
    }
    
    // Also check OSL 16890 directly
    console.log('\nChecking OSL 16890 directly...');
    const { data: oslData, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('*')
      .eq('id', 16890)
      .single();
      
    if (oslError) {
      console.error('Error fetching OSL 16890:', oslError);
      return;
    }
    
    console.log('OSL 16890 data:', oslData);
    
    // Manually check if they should match
    const roundedWeight = weight >= 172.5 ? Math.ceil(weight) : Math.floor(weight);
    console.log(`Rounded weight: ${roundedWeight}`);
    
    const mpsMatch = mpsId === oslData.mps_id;
    const weightMatch = roundedWeight >= oslData.min_weight && roundedWeight <= oslData.max_weight;
    const colorMatch = oslData.color_id === 23 || colorId === oslData.color_id;
    
    console.log(`MPS Match: ${mpsMatch} (${mpsId} === ${oslData.mps_id})`);
    console.log(`Weight Match: ${weightMatch} (${roundedWeight} >= ${oslData.min_weight} && ${roundedWeight} <= ${oslData.max_weight})`);
    console.log(`Color Match: ${colorMatch} (${oslData.color_id} === 23 || ${colorId} === ${oslData.color_id})`);
    
    const shouldMatch = mpsMatch && weightMatch && colorMatch;
    console.log(`Should Match: ${shouldMatch}`);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
};

testMatchingFunction();
