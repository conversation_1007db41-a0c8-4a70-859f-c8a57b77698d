-- Enqueue product variant readiness check when t_images.image_verified changes from FALSE to TRUE for t_product_variants
CREATE OR REPLACE FUNCTION public.enqueue_variant_ready_on_image_verified()
RETURNS TRIGGER AS $$
BEGIN
  -- Only act when image_verified changes from FALSE to TRUE for t_product_variants records
  IF (NEW.table_name = 't_product_variants' 
      AND OLD.image_verified = FALSE 
      AND NEW.image_verified = TRUE) THEN
    
    -- Insert task, avoiding duplicates already pending/processing for this variant
    INSERT INTO public.t_task_queue (task_type, payload, status, scheduled_at, created_at, enqueued_by)
    SELECT
      'check_if_product_variant_is_ready' AS task_type,
      jsonb_build_object('id', NEW.record_id) AS payload,
      'pending' AS status,
      NOW() AS scheduled_at,
      NOW() AS created_at,
      'trigger:t_images.image_verified_for_variant' AS enqueued_by
    WHERE NOT EXISTS (
      SELECT 1 FROM public.t_task_queue tq
      WHERE tq.task_type = 'check_if_product_variant_is_ready'
        AND (tq.status = 'pending' OR tq.status = 'processing')
        AND (tq.payload->>'id')::INT = NEW.record_id
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop and recreate trigger
DROP TRIGGER IF EXISTS trg_t_images_enqueue_variant_ready_on_image_verified ON public.t_images;
CREATE TRIGGER trg_t_images_enqueue_variant_ready_on_image_verified
AFTER UPDATE OF image_verified ON public.t_images
FOR EACH ROW
WHEN (NEW.table_name = 't_product_variants' AND OLD.image_verified = FALSE AND NEW.image_verified = TRUE)
EXECUTE FUNCTION public.enqueue_variant_ready_on_image_verified();
