// updateImageRecord.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import minimist from 'minimist';

// Parse command-line arguments
const args = minimist(process.argv.slice(2));
const id = args.id;

if (!id) {
  console.error('Missing required --id parameter');
  process.exit(1);
}

// Create a Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log(`Updating t_images record with id=${id}...`);
    
    // First, check if the record exists
    const { data: existingRecord, error: checkError } = await supabase
      .from('t_images')
      .select('*')
      .eq('id', id)
      .maybeSingle();
      
    if (checkError) {
      console.error(`Error checking for existing record: ${checkError.message}`);
      return;
    }
    
    if (!existingRecord) {
      console.log(`No record found with id=${id}`);
      return;
    }
    
    console.log(`Found record: ${JSON.stringify(existingRecord)}`);
    
    // Update the record with minimal fields
    const updateData = {
      image_verified: true,
      image_verified_at: new Date().toISOString(),
      image_verified_notes: 'Manually verified',
      updated_by: 'updateImageRecord'
    };
    
    console.log(`Update data: ${JSON.stringify(updateData)}`);
    
    // Try to update with a timeout setting
    console.log('Attempting update with statement_timeout=30000...');
    
    try {
      // First, set a longer statement timeout
      await supabase.rpc('set_statement_timeout', { timeout_ms: 30000 });
      
      // Then, perform the update
      const { data: updateResult, error: updateError } = await supabase
        .from('t_images')
        .update(updateData)
        .eq('id', id)
        .select();
        
      if (updateError) {
        console.error(`Error updating record: ${updateError.message}`);
      } else {
        console.log(`Update successful: ${JSON.stringify(updateResult)}`);
      }
    } catch (err) {
      console.error(`Error with timeout approach: ${err.message}`);
      
      // Try a different approach - update one field at a time
      console.log('Trying to update one field at a time...');
      
      try {
        // Update image_verified
        const { error: error1 } = await supabase
          .from('t_images')
          .update({ image_verified: true })
          .eq('id', id);
          
        if (error1) {
          console.error(`Error updating image_verified: ${error1.message}`);
        } else {
          console.log('Successfully updated image_verified');
        }
        
        // Update image_verified_at
        const { error: error2 } = await supabase
          .from('t_images')
          .update({ image_verified_at: new Date().toISOString() })
          .eq('id', id);
          
        if (error2) {
          console.error(`Error updating image_verified_at: ${error2.message}`);
        } else {
          console.log('Successfully updated image_verified_at');
        }
        
        // Update image_verified_notes
        const { error: error3 } = await supabase
          .from('t_images')
          .update({ image_verified_notes: 'Manually verified' })
          .eq('id', id);
          
        if (error3) {
          console.error(`Error updating image_verified_notes: ${error3.message}`);
        } else {
          console.log('Successfully updated image_verified_notes');
        }
        
        // Update updated_by
        const { error: error4 } = await supabase
          .from('t_images')
          .update({ updated_by: 'updateImageRecord' })
          .eq('id', id);
          
        if (error4) {
          console.error(`Error updating updated_by: ${error4.message}`);
        } else {
          console.log('Successfully updated updated_by');
        }
      } catch (fieldErr) {
        console.error(`Error updating fields individually: ${fieldErr.message}`);
      }
    }
    
    // Check if the update was successful
    console.log('Checking if update was successful...');
    
    const { data: finalRecord, error: finalError } = await supabase
      .from('t_images')
      .select('*')
      .eq('id', id)
      .maybeSingle();
      
    if (finalError) {
      console.error(`Error checking final record: ${finalError.message}`);
      return;
    }
    
    console.log(`Final record: ${JSON.stringify(finalRecord)}`);
  } catch (err) {
    console.error(`Unexpected error: ${err.message}`);
    process.exit(1);
  }
}

main();
