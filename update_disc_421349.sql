-- Create a function to update disc 421349 with OSL 16890
CREATE OR REPLACE FUNCTION update_disc_421349()
RETURNS TEXT AS $$
DECLARE
    disc_id INTEGER := 421349;
    osl_id INTEGER := 16890;
    result_text TEXT;
BEGIN
    -- Update the disc
    UPDATE t_discs
    SET order_sheet_line_id = osl_id
    WHERE id = disc_id;
    
    -- Return success message
    result_text := 'Disc 421349 updated with OSL 16890';
    
    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- Call the function
SELECT update_disc_421349();
