import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function createFundraiserRecordsFixed() {
    try {
        console.log('🔧 Creating fundraiser records with proper UUIDs...\n');
        
        // Generate proper UUIDs
        const batchId = uuidv4();
        console.log(`Using batch ID: ${batchId}`);
        
        const fundraiserRecords = [
            {
                mold_name: 'Thrasher',
                plastic_name: 'Elite Z Jawbreaker',
                min_weight: 150,
                max_weight: 180,
                color_name: 'Varies',
                stamp_name: 'Live Free - Be Happy - Funky Ben Askren Fundraiser',
                vendor_product_code: 'Elite_Z_Jawbreaker_Thrasher_150-180',
                is_orderable: true,
                is_currently_available: true,
                cost_price: 10.00,
                excel_mapping_key: 'SPECIAL|Fundraiser - <PERSON> Z Jawbreaker Thrasher (msrp $19.99, cost $10.00)|Order Qty',
                excel_row_hint: 25,
                excel_column: 'A',  // COLUMN A!
                raw_line_type: 'SPECIAL',
                raw_model: 'Fundraiser - Ben Askren Z Jawbreaker Thrasher (msrp $19.99, cost $10.00)',
                raw_weight_range: 'Order Qty',
                vendor_description: '',
                calculated_mps_id: 19704,
                import_file_hash: 'manual_fix_hash',
                import_batch_id: batchId
            },
            {
                mold_name: 'Buzzz',
                plastic_name: 'Big Z Collection',
                min_weight: 150,
                max_weight: 180,
                color_name: 'Varies',
                stamp_name: 'Live Free - Be Happy - Funky Ben Askren Fundraiser',
                vendor_product_code: 'Big_Z_Collection_Buzzz_150-180',
                is_orderable: true,
                is_currently_available: true,
                cost_price: 10.00,
                excel_mapping_key: 'SPECIAL|Fundraiser - Ben Askren Big Z Buzzz  (msrp $19.99, cost $10.00)|Order Qty',
                excel_row_hint: 28,
                excel_column: 'A',  // COLUMN A!
                raw_line_type: 'SPECIAL',
                raw_model: 'Fundraiser - Ben Askren Big Z Buzzz  (msrp $19.99, cost $10.00)',
                raw_weight_range: 'Order Qty',
                vendor_description: '',
                calculated_mps_id: null, // Will show NO_MPS
                import_file_hash: 'manual_fix_hash',
                import_batch_id: batchId
            }
        ];

        for (const record of fundraiserRecords) {
            console.log(`Creating: ${record.plastic_name} ${record.mold_name} in row ${record.excel_row_hint}, column ${record.excel_column}`);
            
            const { data: insertedData, error: insertError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .insert(record)
                .select();

            if (insertError) {
                console.error(`❌ Error inserting ${record.mold_name}:`, insertError);
            } else {
                console.log(`✅ Created ${record.mold_name} record in column ${record.excel_column}`);
                console.log(`   ID: ${insertedData[0]?.id}, MPS: ${record.calculated_mps_id || 'NO_MPS'}`);
            }
        }

        // Verify the records were created correctly
        console.log('\n📋 Verifying created records...');
        
        const { data: verifyRecords, error: verifyError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, mold_name, plastic_name, calculated_mps_id, is_orderable, excel_mapping_key')
            .in('excel_row_hint', [25, 28])
            .order('excel_row_hint');

        if (verifyError) {
            console.error('❌ Error verifying records:', verifyError);
        } else {
            console.log(`✅ Found ${verifyRecords.length} records in rows 25/28:`);
            verifyRecords.forEach(record => {
                console.log(`   Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.plastic_name} ${record.mold_name}`);
                console.log(`      Orderable: ${record.is_orderable}, MPS: ${record.calculated_mps_id || 'NO_MPS'}`);
                console.log(`      Mapping: ${record.excel_mapping_key}`);
                console.log('');
            });
        }

        // Test what will be exported
        console.log('🧪 Testing export data...');
        
        const { data: exportTest, error: exportTestError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, calculated_mps_id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .in('excel_row_hint', [22, 25, 28]);

        if (exportTestError) {
            console.error('❌ Error testing export:', exportTestError);
        } else {
            console.log(`✅ Export test: ${exportTest.length} records will be exported:`);
            exportTest.forEach(record => {
                console.log(`   Row ${record.excel_row_hint}, Col ${record.excel_column}: MPS ${record.calculated_mps_id || 'NO_MPS'}`);
            });
        }

        console.log('\n🎉 Fundraiser records created successfully!');
        console.log('\n📋 Expected results in next order:');
        console.log('   • Row 22: EMPTY (no records)');
        console.log('   • Row 25, Column A: 19704 (Thrasher MPS ID)');
        console.log('   • Row 28, Column A: NO_MPS (Buzzz needs MPS record)');
        
    } catch (error) {
        console.error('❌ Creation failed:', error.message);
    }
}

createFundraiserRecordsFixed().catch(console.error);
