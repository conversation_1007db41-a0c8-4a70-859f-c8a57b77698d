// setupWindowsTask.js - Script to set up Windows Task Scheduler

import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Configuration
const dbfFilePath = process.env.DBF_FILE_PATH || 'R:\\Rpro\\BRIDGE\\invdb.dbf';
const targetTable = process.env.TARGET_TABLE || 'imported_table_rpro';
const truncateBeforeImport = process.env.TRUNCATE_BEFORE_IMPORT === 'true' || true;

// Create a batch file for the scheduled task
const createBatchFile = () => {
  const batchContent = `@echo off
echo Starting RPRO DBF import at %date% %time%
cd "${process.cwd()}"
node batchImport.js "${dbfFilePath}" "${targetTable}" ${truncateBeforeImport}
echo Import completed at %date% %time%
`;

  const batchPath = path.join(process.cwd(), 'runRproImport.bat');
  fs.writeFileSync(batchPath, batchContent);
  console.log(`Created batch file at: ${batchPath}`);
  return batchPath;
};

// Create a scheduled task using Windows Task Scheduler
const createWindowsScheduledTask = (batchPath) => {
  // Schedule for 6:15 AM (giving a buffer after the file is generated at 6:03 AM)
  const taskName = 'DailyRproImport';
  const command = `schtasks /create /tn "${taskName}" /tr "${batchPath}" /sc DAILY /st 06:15 /ru SYSTEM /f`;
  
  console.log('Creating Windows scheduled task...');
  console.log(`Command: ${command}`);
  
  exec(command, (error, stdout, stderr) => {
    if (error) {
      console.error(`Error creating scheduled task: ${error.message}`);
      console.error('You may need to run this script with administrator privileges.');
      
      // Provide instructions for manual setup
      console.log('\nTo manually create the task:');
      console.log('1. Open Task Scheduler (taskschd.msc)');
      console.log('2. Click "Create Basic Task..."');
      console.log('3. Name: "DailyRproImport"');
      console.log('4. Trigger: Daily at 6:15 AM');
      console.log(`5. Action: Start a program - "${batchPath}"`);
      console.log('6. Finish the wizard');
      
      return;
    }
    
    console.log('Scheduled task created successfully!');
    console.log(stdout);
    
    // Display the task
    exec(`schtasks /query /tn "${taskName}" /fo LIST`, (err, out) => {
      if (!err) {
        console.log('Task details:');
        console.log(out);
      }
    });
  });
};

// Main function
const setupWindowsTask = () => {
  console.log('Setting up Windows scheduled task for RPRO DBF import...');
  console.log(`DBF File Path: ${dbfFilePath}`);
  console.log(`Target Table: ${targetTable}`);
  console.log(`Truncate Before Import: ${truncateBeforeImport}`);
  
  // Create the batch file
  const batchPath = createBatchFile();
  
  // Create the scheduled task
  createWindowsScheduledTask(batchPath);
};

// Run the setup
setupWindowsTask();
