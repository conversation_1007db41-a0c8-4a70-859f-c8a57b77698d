# Disc Updated Workflow

This document describes the new disc updated workflow that handles the process when a disc needs to be reset due to an MPS ID change.

## Overview

When a disc is updated and needs to be reset (typically due to an MPS ID change), the system enqueues a `disc_updated_need_to_reset` task that spawns 5 child tasks in a specific sequence to handle the reset process safely.

## Sequential Workflow Design

The workflow now uses a **sequential chain** where each task enqueues the next task only upon successful completion:

### **Initial Tasks (Enqueued by Master Task)**

The master task uses **intelligent optimization** based on the disc's current sold status:

#### **If Disc is NOT Sold (`sold_date` is null):**

1. **`disc_updated_sell_it`** (immediate)
   - Sets `sold_date` to current timestamp
   - Sets `sold_channel` to 'Fixed'

2. **`disc_updated_delete_from_shopify`** (2 minutes delay)
   - Waits for sell task to complete before proceeding

#### **If Disc is ALREADY Sold (`sold_date` is not null):**

1. **`disc_updated_sell_it`** - **SKIPPED ENTIRELY** ⚡
   - No need to enqueue since disc is already sold

2. **`disc_updated_delete_from_shopify`** (immediate) ⚡
   - Scheduled immediately since no sell task is needed
   - No 2-minute delay required

#### **Shopify Deletion Task Behavior:**

**If `shopify_uploaded_at` is NULL (disc never uploaded):**
   - ✅ **Skips Shopify deletion** (nothing to delete)
   - ✅ **Updates database fields** for consistency
   - ✅ **Marks as successful** and enqueues next task immediately
   - ⚡ **Optimization**: No unnecessary API calls

**If `shopify_uploaded_at` is NOT NULL (disc was uploaded):**
   - 🔄 **Calculates `shopify_sku`** as `D{disc_id}`
   - 🗑️ **Deletes variant from Shopify** using enhanced deletion functionality
   - 🗑️ **If last variant in product**: Also deletes the entire product to avoid empty shells
   - 📝 **Always updates database fields:**
     - Sets `shopify_uploaded_at` to null
     - Sets `shopify_uploaded_notes` to "Deleted from Shopify because of mps_id change."
   - ✅ **SUCCESS CRITERIA**: Both variant AND product (if last variant) must be deleted
   - ✅ **ON SUCCESS**: Enqueues `disc_updated_reset` (immediate)
   - ❌ **ON FAILURE**: Workflow stops with error status

### **Sequential Chain (Each Task Enqueues Next)**

3. **`disc_updated_reset`** (enqueued by successful `disc_updated_delete_from_shopify`)
   - Resets multiple fields to null:
     - `order_sheet_line_id`
     - `sdasin_searched_for_at`
     - `looked_for_matching_sdasin_at`
     - `tag_printed_at`
     - `g_title`
     - `g_pull`
     - `g_handle`
   - Sets `todo` to "Need to finish mps_id change over."
   - **ON SUCCESS**: Enqueues `disc_updated_unsell` (immediate)

4. **`disc_updated_unsell`** (enqueued by successful `disc_updated_reset`)
   - **Intelligent unsell logic**: Only unsells if disc was sold by current workflow
   - **Checks**: Original sold_date was null AND current sold_channel is 'Fixed'
   - **If should unsell**: Sets `sold_channel` and `sold_date` to null
   - **If should NOT unsell**: Skips unsell operation (preserves genuine customer sales)
   - **ALWAYS**: Enqueues `new_t_discs_record` regardless of unsell decision

5. **`new_t_discs_record`** (enqueued by successful `disc_updated_unsell`)
   - Triggers the existing new disc workflow
   - This spawns additional child tasks for disc processing

## Files Created

### Task Processors
- `processDiscUpdatedNeedToResetTask.js` - Main task that spawns child tasks
- `processDiscUpdatedSellItTask.js` - Handles selling the disc
- `processDiscUpdatedDeleteFromShopifyTask.js` - Handles Shopify deletion
- `processDiscUpdatedResetTask.js` - Handles field reset (most robust task)
- `processDiscUpdatedUnsellTask.js` - Handles unselling the disc

### Helper Functions
- `enqueueDiscUpdatedNeedToResetTask.js` - Helper to enqueue the main task

### Database Trigger
- **`create_disc_mps_id_updated_enqueuer.sql`** - Creates trigger that fires on mps_id updates

### Testing
- `testDiscUpdatedWorkflow.js` - Test script to verify the workflow

## Usage

### Programmatic Usage

```javascript
import { enqueueDiscUpdatedNeedToResetTask } from './enqueueDiscUpdatedNeedToResetTask.js';

// Enqueue with default 5-minute delay
const task = await enqueueDiscUpdatedNeedToResetTask(discId);

// Enqueue with custom scheduling
const scheduledTime = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes from now
const task = await enqueueDiscUpdatedNeedToResetTask(discId, scheduledTime);
```

### Command Line Usage

```bash
# Enqueue a task for disc ID 12345
node enqueueDiscUpdatedNeedToResetTask.js 12345
```

### Manual Database Insertion

```sql
INSERT INTO t_task_queue (task_type, payload, status, scheduled_at, created_at, enqueued_by)
VALUES (
  'disc_updated_need_to_reset',
  '{"id": 12345, "sold_date": null}',
  'pending',
  NOW() + INTERVAL '5 minutes',
  NOW(),
  'manual'
);
```

### Automatic Trigger

The workflow is automatically triggered when a disc's `mps_id` field is updated:

```sql
-- Trigger fires on any mps_id update
UPDATE t_discs SET mps_id = 456 WHERE id = 12345;
-- This automatically enqueues the disc_updated_need_to_reset task
```

## Testing

Run the test script to verify the workflow:

```bash
node testDiscUpdatedWorkflow.js
```

This will:
1. Find a test disc in the database
2. Enqueue the main task
3. Show the created child tasks
4. Display the expected workflow sequence

## Integration with Task Queue Worker

The new task types have been integrated into the main `taskQueueWorker.js` file:
- Added imports for all new task processors
- Added case handlers in the `processBatch()` function
- All tasks follow the existing error handling and logging patterns

## Error Handling

Each task processor includes:
- Comprehensive payload validation
- Database error handling
- Detailed logging and error reporting
- Graceful handling of missing data (e.g., no shopify_sku)

## Monitoring

To monitor the workflow execution:
1. Watch the task queue worker logs for real-time processing
2. Query the `t_task_queue` table to check task status
3. Monitor the `t_discs` table to see field changes
4. Check the `t_error_logs` table for any errors

## Enhanced Shopify Deletion Behavior

The Shopify deletion task includes intelligent product cleanup and **strict success criteria**:

### **Single Variant Products**
- ✅ Deletes the variant
- ✅ **Also deletes the entire product** to avoid empty product shells
- ✅ **SUCCESS**: Both variant AND product must be deleted
- ✅ **FAILURE**: If either deletion fails, task fails and workflow stops

### **Multi-Variant Products**
- ✅ Deletes only the specific variant
- ✅ Keeps the product and other variants intact
- ✅ **SUCCESS**: Variant deletion succeeds (product deletion not required)

### **Strict Error Handling**
- ❌ **Task fails if Shopify deletion is incomplete** - workflow stops
- ✅ **Database fields are always updated** regardless of Shopify results
- ✅ **Detailed error reporting** shows exactly what succeeded/failed
- ✅ **Workflow continuation** only happens on complete success

## **Intelligent Unsell Logic**

The `disc_updated_unsell` task includes smart logic to protect genuine customer sales:

### **Unsell Decision Matrix:**

| Original Status | Current Status | Action | Reason |
|----------------|----------------|---------|---------|
| `sold_date: null` | `sold_channel: 'Fixed'` | ✅ **UNSELL** | Disc was sold by our workflow |
| `sold_date: '2024-01-15'` | `sold_channel: 'Shopify'` | ❌ **SKIP** | Disc was already sold to customer |
| `sold_date: null` | `sold_channel: 'Shopify'` | ❌ **SKIP** | Disc sold through different channel |
| `sold_date: null` | `sold_date: null` | ❌ **SKIP** | Disc is not currently sold |

### **Protection Logic:**
- **✅ Safe to unsell**: `original_sold_date` was null AND current `sold_channel` is 'Fixed'
- **❌ Do NOT unsell**: Disc was already sold before workflow OR sold through different channel
- **🔄 Always continues**: Next task (`new_t_discs_record`) is always enqueued

### **Optimization Benefits**
- ⚡ **Faster processing for sold discs** - skips unnecessary sell task
- ⚡ **Immediate Shopify deletion** - no 2-minute delay when disc already sold
- ⚡ **Skip unnecessary deletions** - no Shopify API calls for never-uploaded discs
- 🎯 **Intelligent task selection** - only enqueues necessary tasks
- 📊 **Clear optimization reporting** - task results show which path was taken
- 🛡️ **Customer sale protection** - never unsells genuine customer purchases
- 🔄 **Consistent workflow continuation** - next task always enqueued on success

### **Sequential Chain Benefits**
- 🛡️ **Fail-fast approach** - stops at first failure
- 🔄 **Guaranteed order** - tasks run in exact sequence
- 📊 **Clear status tracking** - each task reports next task enqueueing
- 🐛 **Easy debugging** - failed workflows stop at the problem point

## Dependencies

The workflow uses existing functionality:
- Enhanced Shopify variant deletion from `processDeleteVariantFromShopifyTask.js`
- New disc record processing from `processNewTDiscsRecordTask.js`
- Standard task queue infrastructure

## Notes

- The timing delays (2, 4, 6, 8 minutes) ensure proper sequencing
- The workflow is designed to be safe and reversible
- Each task can be run independently if needed
- The system handles missing or null values gracefully
