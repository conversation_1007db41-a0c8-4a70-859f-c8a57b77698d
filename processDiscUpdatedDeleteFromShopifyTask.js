// Function to process a disc_updated_delete_from_shopify task
async function processDiscUpdatedDeleteFromShopifyTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to delete disc from Shopify. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to delete disc from Shopify. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;
    console.log(`[taskQueueWorker.js] Deleting disc ${discId} from Shopify`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // First, get the disc record to check shopify_uploaded_at
    const { data: discRecord, error: discError } = await supabase
      .from('t_discs')
      .select('shopify_uploaded_at')
      .eq('id', discId)
      .maybeSingle();

    if (discError) {
      const errMsg = `[taskQueueWorker.js] Error fetching disc record: ${discError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Fetching disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to delete disc from Shopify. Database error when retrieving disc record.",
        error: discError.message
      });
      return;
    }

    if (!discRecord) {
      const errMsg = `[taskQueueWorker.js] No disc record found for id=${discId}`;
      console.error(errMsg);
      await logError(errMsg, `No disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to delete disc from Shopify. Disc record not found.",
        error: 'Disc record not found'
      });
      return;
    }

    // Only proceed if shopify_uploaded_at is not null
    if (!discRecord.shopify_uploaded_at) {
      console.log(`[taskQueueWorker.js] Disc ${discId} has not been uploaded to Shopify (shopify_uploaded_at is null), skipping Shopify deletion and enqueueing next task`);

      // Still update database fields to be consistent
      const { error: updateError } = await supabase
        .from('t_discs')
        .update({
          shopify_uploaded_at: null, // Already null, but for consistency
          shopify_uploaded_notes: "Deleted from Shopify because of mps_id change."
        })
        .eq('id', discId);

      if (updateError) {
        const errMsg = `[taskQueueWorker.js] Error updating disc database fields: ${updateError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Updating disc database fields for id=${discId}`);
        await updateTaskStatus(task.id, 'error', {
          message: "Failed to update disc database fields.",
          error: updateError.message,
          disc_id: discId
        });
        return;
      }

      // Enqueue the next task since this was successful (nothing to delete)
      let nextTaskEnqueued = false;
      let nextTaskError = null;

      try {
        console.log(`[taskQueueWorker.js] Skipped Shopify deletion (nothing to delete), enqueueing disc_updated_reset task for disc ${discId}`);

        const { data: resetTask, error: resetError } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: 'disc_updated_reset',
            payload: {
              id: discId,
              sold_date: payload.sold_date
            },
            status: 'pending',
            scheduled_at: new Date().toISOString(), // No delay
            created_at: new Date().toISOString(),
            enqueued_by: `disc_updated_delete_from_shopify_${discId}`
          })
          .select();

        if (resetError) {
          console.error(`[taskQueueWorker.js] Error enqueueing disc_updated_reset task: ${resetError.message}`);
          await logError(`Error enqueueing disc_updated_reset task: ${resetError.message}`, `Enqueueing task for disc id=${discId}`);
          nextTaskEnqueued = false;
          nextTaskError = resetError.message;
        } else {
          console.log(`[taskQueueWorker.js] Successfully enqueued disc_updated_reset task (ID: ${resetTask[0].id}) for disc ${discId}`);
          nextTaskEnqueued = true;
          nextTaskError = null;
        }
      } catch (err) {
        console.error(`[taskQueueWorker.js] Exception enqueueing disc_updated_reset task: ${err.message}`);
        await logError(`Exception enqueueing disc_updated_reset task: ${err.message}`, `Enqueueing task for disc id=${discId}`);
        nextTaskEnqueued = false;
        nextTaskError = err.message;
      }

      await updateTaskStatus(task.id, 'completed', {
        message: `Disc ${discId} has not been uploaded to Shopify, no deletion needed. Updated database fields and enqueued next task.`,
        disc_id: discId,
        shopify_uploaded_at: null,
        shopify_deletion_attempted: false,
        shopify_deletion_success: true, // True because there was nothing to delete
        shopify_deletion_skipped: true,
        database_fields_updated: true,
        next_task_enqueued: nextTaskEnqueued,
        next_task_error: nextTaskError,
        action: 'skipped'
      });
      return;
    }

    // Calculate the shopify_sku (always D + disc_id)
    const shopifySku = `D${discId}`;
    console.log(`[taskQueueWorker.js] Disc ${discId} has been uploaded to Shopify, proceeding with deletion. shopify_sku: ${shopifySku}`);

    let shopifyDeletionResult = null;
    let shopifyDeletionError = null;

    // Attempt Shopify deletion using the calculated shopify_sku
    try {
      // Import the delete variant function
      const { deleteVariantFromShopify } = await import('./processDeleteVariantFromShopifyTask.js');

      // Call the delete function directly
      const deleteResult = await deleteVariantFromShopify(shopifySku, 'Disc updated - mps_id change');

      if (deleteResult.success) {
        console.log(`[taskQueueWorker.js] Successfully deleted variant ${shopifySku} from Shopify`);
        shopifyDeletionResult = deleteResult;
      } else {
        console.log(`[taskQueueWorker.js] Failed to delete variant ${shopifySku} from Shopify: ${deleteResult.error}`);
        shopifyDeletionError = deleteResult.error;
      }
    } catch (deleteErr) {
      const errMsg = `[taskQueueWorker.js] Exception during Shopify deletion: ${deleteErr.message}`;
      console.error(errMsg);
      await logError(errMsg, `Deleting variant ${shopifySku} from Shopify`);
      shopifyDeletionError = deleteErr.message;
    }

    // Always update the database fields regardless of Shopify deletion result
    console.log(`[taskQueueWorker.js] Updating disc ${discId} database fields: setting shopify_uploaded_at to null and shopify_uploaded_notes`);

    const { error: updateError } = await supabase
      .from('t_discs')
      .update({
        shopify_uploaded_at: null,
        shopify_uploaded_notes: "Deleted from Shopify because of mps_id change."
      })
      .eq('id', discId);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating disc database fields: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating disc database fields for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update disc database fields after Shopify deletion.",
        error: updateError.message,
        disc_id: discId,
        shopify_sku: shopifySku
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully updated disc ${discId} database fields`);

    // Determine if the task should be considered successful
    // Success requires both variant deletion AND product deletion (if it was the last variant)
    const taskSuccessful = shopifyDeletionResult && (!shopifyDeletionResult.wasLastVariant || shopifyDeletionResult.productDeleted);

    if (taskSuccessful) {
      // Task was successful - enqueue the next task in the sequence
      let nextTaskEnqueued = false;
      let nextTaskError = null;

      try {
        console.log(`[taskQueueWorker.js] Shopify deletion successful, enqueueing disc_updated_reset task for disc ${discId}`);

        const { data: resetTask, error: resetError } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: 'disc_updated_reset',
            payload: {
              id: discId,
              sold_date: payload.sold_date
            },
            status: 'pending',
            scheduled_at: new Date().toISOString(), // No delay
            created_at: new Date().toISOString(),
            enqueued_by: `disc_updated_delete_from_shopify_${discId}`
          })
          .select();

        if (resetError) {
          console.error(`[taskQueueWorker.js] Error enqueueing disc_updated_reset task: ${resetError.message}`);
          await logError(`Error enqueueing disc_updated_reset task: ${resetError.message}`, `Enqueueing task for disc id=${discId}`);
          nextTaskEnqueued = false;
          nextTaskError = resetError.message;
        } else {
          console.log(`[taskQueueWorker.js] Successfully enqueued disc_updated_reset task (ID: ${resetTask[0].id}) for disc ${discId}`);
          nextTaskEnqueued = true;
          nextTaskError = null;
        }
      } catch (err) {
        console.error(`[taskQueueWorker.js] Exception enqueueing disc_updated_reset task: ${err.message}`);
        await logError(`Exception enqueueing disc_updated_reset task: ${err.message}`, `Enqueueing task for disc id=${discId}`);
        nextTaskEnqueued = false;
        nextTaskError = err.message;
      }

      // Complete the task successfully
      const completionMessage = shopifyDeletionResult.productDeleted
        ? `Successfully deleted disc variant and product for SKU ${shopifySku} from Shopify and updated database fields.`
        : `Successfully deleted disc variant for SKU ${shopifySku} from Shopify and updated database fields.`;

      await updateTaskStatus(task.id, 'completed', {
        message: completionMessage,
        disc_id: discId,
        shopify_sku: shopifySku,
        shopify_deletion_attempted: true,
        shopify_deletion_success: true,
        shopify_deletion_error: null,
        database_fields_updated: true,
        next_task_enqueued: nextTaskEnqueued,
        next_task_error: nextTaskError
      });
    } else {
      // Task failed - do not enqueue next task
      const errorMessage = shopifyDeletionError
        ? `Shopify deletion failed: ${shopifyDeletionError}. Database fields updated but workflow cannot continue.`
        : `Shopify deletion incomplete. Database fields updated but workflow cannot continue.`;

      console.error(`[taskQueueWorker.js] ${errorMessage}`);

      await updateTaskStatus(task.id, 'error', {
        message: errorMessage,
        disc_id: discId,
        shopify_sku: shopifySku,
        shopify_deletion_attempted: true,
        shopify_deletion_success: false,
        shopify_deletion_error: shopifyDeletionError,
        database_fields_updated: true,
        next_task_enqueued: false,
        workflow_stopped: true,
        reason: "Shopify deletion was not fully successful - variant and/or product deletion failed"
      });
    }

  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to delete disc from Shopify due to an unexpected error.",
      error: err.message,
      disc_id: discId,
      next_task_enqueued: false,
      workflow_stopped: true,
      reason: "Unexpected exception during task processing"
    });
  }
}

export default processDiscUpdatedDeleteFromShopifyTask;
