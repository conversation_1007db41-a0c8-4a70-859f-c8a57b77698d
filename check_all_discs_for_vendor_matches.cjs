require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkAllDiscsForVendorMatches() {
  try {
    console.log('Checking ALL discs to see if any manufacturer weights match OSL ranges...');
    
    // Get total count first
    const { count: totalCount, error: countError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (countError) {
      console.error('Error getting count:', countError);
      return;
    }
    
    console.log(`Total discs to check: ${totalCount}`);
    
    let foundMatches = 0;
    let checkedCount = 0;
    const batchSize = 100;
    
    // Process in batches
    while (checkedCount < totalCount) {
      const { data: discs, error: discError } = await supabase
        .from('t_discs')
        .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
        .is('vendor_osl_id', null)
        .not('weight_mfg', 'is', null)
        .not('mps_id', 'is', null)
        .not('color_id', 'is', null)
        .range(checkedCount, checkedCount + batchSize - 1);
      
      if (discError) {
        console.error('Error getting discs batch:', discError);
        break;
      }
      
      if (!discs || discs.length === 0) {
        break;
      }
      
      console.log(`Processing batch ${Math.floor(checkedCount / batchSize) + 1}, checking discs ${checkedCount + 1}-${checkedCount + discs.length}...`);
      
      for (const disc of discs) {
        // Test the vendor OSL function
        const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
          'find_matching_osl_by_mfg_weight',
          {
            mps_id_param: disc.mps_id,
            color_id_param: disc.color_id,
            weight_mfg_param: disc.weight_mfg
          }
        );
        
        if (!vendorOslError && vendorOslData && vendorOslData.length > 0) {
          foundMatches++;
          const vendorOslId = vendorOslData[0].osl_id;
          
          console.log(`\n✅ MATCH ${foundMatches} - Disc ${disc.id}:`);
          console.log(`  Weight: ${disc.weight}g, Weight MFG: ${disc.weight_mfg}g (rounded: ${Math.round(disc.weight_mfg)}g)`);
          console.log(`  Regular OSL: ${disc.order_sheet_line_id}, Vendor OSL: ${vendorOslId}`);
          
          if (vendorOslId !== disc.order_sheet_line_id) {
            console.log(`  🎯 DIFFERENT MAPPINGS! Regular vs Vendor OSL differ.`);
          } else {
            console.log(`  ℹ️ Same OSL for both mappings.`);
          }
          
          // Update this disc
          const { error: updateError } = await supabase
            .from('t_discs')
            .update({ vendor_osl_id: vendorOslId })
            .eq('id', disc.id);
          
          if (updateError) {
            console.error(`  ❌ Error updating disc ${disc.id}:`, updateError);
          } else {
            console.log(`  ✅ Updated disc ${disc.id} with vendor_osl_id: ${vendorOslId}`);
          }
        }
        
        checkedCount++;
      }
      
      console.log(`Batch completed. Total checked: ${checkedCount}, Total matches: ${foundMatches}`);
      
      // Small delay between batches
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    
    console.log('\n=== FINAL RESULTS ===');
    console.log(`Total discs checked: ${checkedCount}`);
    console.log(`Successful matches found and updated: ${foundMatches}`);
    console.log(`Success rate: ${((foundMatches / checkedCount) * 100).toFixed(2)}%`);
    
    if (foundMatches > 0) {
      console.log(`\n✅ SUCCESS! Updated ${foundMatches} discs with vendor_osl_id mappings.`);
      console.log('The dual mapping functionality is now working for discs where manufacturer weights fall within OSL ranges.');
    } else {
      console.log('\n📊 ANALYSIS COMPLETE: No manufacturer weights match existing OSL ranges.');
      console.log('This is actually normal behavior - it means manufacturer weights are consistently');
      console.log('outside the weight ranges defined in your order sheet lines.');
      console.log('\nThis could indicate:');
      console.log('- Manufacturers list conservative weights');
      console.log('- OSL weight ranges are based on actual measured weights');
      console.log('- The dual mapping will work when manufacturer and actual weights align better');
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

checkAllDiscsForVendorMatches();
