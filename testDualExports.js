import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function getOrderSummaryData() {
    // Get ALL orderable products from the base table (same logic as updated daily automation)
    let allOrderableData = [];
    let from = 0;
    const pageSize = 1000;
    
    while (true) {
        const { data: batch, error: orderableError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .range(from, from + pageSize - 1)
            .order('excel_mapping_key');

        if (orderableError) {
            throw new Error(`Failed to query orderable data: ${orderableError.message}`);
        }

        if (batch.length === 0) break;
        
        allOrderableData = allOrderableData.concat(batch);
        from += pageSize;
        
        if (batch.length < pageSize) break;
    }

    // Get order quantities from the view
    let viewOrderData = [];
    from = 0;
    
    while (true) {
        const { data: batch, error: viewError } = await supabase
            .from('v_stats_by_osl_discraft')
            .select('excel_mapping_key, "order", mold_name, plastic_name, is_currently_available')
            .not('excel_mapping_key', 'is', null)
            .range(from, from + pageSize - 1);

        if (viewError) {
            throw new Error(`Failed to query view order data: ${viewError.message}`);
        }

        if (batch.length === 0) break;
        
        viewOrderData = viewOrderData.concat(batch);
        from += pageSize;
        
        if (batch.length < pageSize) break;
    }

    // Create a map of order data by excel_mapping_key
    const orderMap = {};
    viewOrderData.forEach(item => {
        orderMap[item.excel_mapping_key] = {
            order: item.order || 0,
            mold_name: item.mold_name,
            plastic_name: item.plastic_name,
            is_currently_available: item.is_currently_available
        };
    });

    // Combine all orderable products with their order quantities (defaulting to 0)
    const orderableData = allOrderableData.map(item => {
        const orderInfo = orderMap[item.excel_mapping_key] || {};
        return {
            excel_mapping_key: item.excel_mapping_key,
            excel_column: item.excel_column,
            excel_row_hint: item.excel_row_hint,
            order: orderInfo.order || 0,
            calculated_mps_id: item.calculated_mps_id,
            mold_name: orderInfo.mold_name || 'Unknown',
            plastic_name: orderInfo.plastic_name || 'Unknown',
            is_currently_available: orderInfo.is_currently_available || false
        };
    });

    return orderableData;
}

async function testDualExports() {
  try {
    console.log('🧪 Testing dual exports (Order Qty + MPS ID)...\n');
    
    // Get the order summary data
    console.log('1. Getting order summary data...');
    const orderData = await getOrderSummaryData();
    
    console.log(`✅ Order summary completed: ${orderData.length} records`);
    
    // Create timestamp for consistent filenames
    const timestamp = new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0];
    
    // 1. Export ORDER QUANTITY copy
    console.log('\n2. Creating Order Quantity export...');
    const orderResponse = await fetch('http://localhost:3001/api/discraft/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        includeHeader: false,
        filename: `test_order_qty_${timestamp}.xlsx`,
        orderData: orderData
      })
    });

    if (!orderResponse.ok) {
      throw new Error(`Order export API returned ${orderResponse.status}: ${orderResponse.statusText}`);
    }

    const orderResult = await orderResponse.json();
    console.log('✅ Order Quantity export completed!');
    console.log(`   📄 Filename: ${orderResult.filename}`);
    console.log(`   📊 Records: ${orderResult.totalRecords}`);

    // 2. Export MPS ID copy
    console.log('\n3. Creating MPS ID export...');
    
    // Create MPS data by replacing order quantities with mps_id
    const mpsData = orderData.map(item => ({
        ...item,
        order: item.calculated_mps_id || 'NO_MPS' // Use calculated_mps_id or show NO_MPS
    }));

    const mpsResponse = await fetch('http://localhost:3001/api/discraft/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        includeHeader: false,
        filename: `test_mps_${timestamp}.xlsx`,
        orderData: mpsData
      })
    });

    if (!mpsResponse.ok) {
      throw new Error(`MPS export API returned ${mpsResponse.status}: ${mpsResponse.statusText}`);
    }

    const mpsResult = await mpsResponse.json();
    console.log('✅ MPS ID export completed!');
    console.log(`   📄 Filename: ${mpsResult.filename}`);
    console.log(`   📊 Records: ${mpsResult.totalRecords}`);
    
    // Show some sample MPS data
    console.log('\n📋 Sample MPS data:');
    const samplesAfter332 = mpsData.filter(item => item.excel_row_hint > 332).slice(0, 10);
    samplesAfter332.forEach((record, index) => {
        console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: MPS=${record.order}`);
    });
    
    console.log('\n🎉 Dual export test completed!');
    console.log('\n📋 Summary:');
    console.log(`   • Order Qty file: Shows order quantities and 0s for troubleshooting`);
    console.log(`   • MPS ID file: Shows MPS IDs to verify which products are being processed`);
    console.log(`   • Both files process the same ${orderData.length} records`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testDualExports().catch(console.error);
