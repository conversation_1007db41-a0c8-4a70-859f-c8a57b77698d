// Script to check processing tasks
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key exists:', !!supabaseKey);

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkProcessingTasks() {
  try {
    const { data, error } = await supabase
      .from('t_task_queue')
      .select('id, task_type, status, created_at, scheduled_at')
      .eq('status', 'processing')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching processing tasks:', error);
      return;
    }

    console.log('Processing tasks:', JSON.stringify(data, null, 2));
    console.log(`Total processing tasks: ${data.length}`);
  } catch (err) {
    console.error('Exception:', err.message);
  }
}

// Run the function
checkProcessingTasks();
