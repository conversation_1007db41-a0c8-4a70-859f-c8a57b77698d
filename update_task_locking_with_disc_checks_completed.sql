-- Update the lock_pending_tasks function to handle disc_checks_completed status
-- This prevents future OSL tasks from constantly cycling between pending and processing

CREATE OR REPLACE FUNCTION lock_pending_tasks(
    worker_id TEXT,
    max_tasks INTEGER,
    task_time TIMESTAMP WITH TIME ZONE
)
R<PERSON><PERSON>NS SETOF t_task_queue AS $$
DECLARE
    lock_timeout INTERVAL := INTERVAL '5 minutes';
BEGIN
    RETURN QUERY
    WITH locked_tasks AS (
        UPDATE t_task_queue
        SET
            status = 'processing',
            locked_at = task_time,
            locked_by = worker_id
        WHERE id IN (
            SELECT id
            FROM t_task_queue
            WHERE
                (
                    -- Normal pending tasks: scheduled for now or in the past
                    (status = 'pending' AND scheduled_at <= task_time)
                    OR
                    -- Future publish_product_osl tasks that need disc checks (haven't been processed yet)
                    (status = 'pending' AND task_type = 'publish_product_osl' AND scheduled_at > task_time)
                    OR
                    -- Disc checks completed tasks that are now ready to run
                    (status = 'disc_checks_completed' AND scheduled_at <= task_time)
                )
                AND (locked_at IS NULL OR locked_at < task_time - lock_timeout)
            ORDER BY 
                -- Priority: 1) Ready tasks, 2) Disc checks completed tasks ready to run, 3) Future OSL tasks needing disc checks
                CASE 
                    WHEN status = 'pending' AND scheduled_at <= task_time THEN 0  -- Ready tasks first
                    WHEN status = 'disc_checks_completed' AND scheduled_at <= task_time THEN 1  -- Completed disc checks ready to run
                    WHEN status = 'pending' AND task_type = 'publish_product_osl' AND scheduled_at > task_time THEN 2  -- Future OSL tasks needing disc checks
                    ELSE 3
                END,
                scheduled_at ASC
            LIMIT max_tasks
            FOR UPDATE SKIP LOCKED
        )
        RETURNING *
    )
    SELECT * FROM locked_tasks;
END;
$$ LANGUAGE plpgsql;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Updated lock_pending_tasks function to handle disc_checks_completed status.';
END $$;
