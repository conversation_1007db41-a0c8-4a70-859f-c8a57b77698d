@echo off
echo Starting DZ Services with PM2...
cd /d %~dp0

echo Starting Admin Server...
pm2 start adminserver.js --name "AdminServer" -f

echo Starting Worker Daemon...
pm2 start taskQueueWorker.js --name "WorkerDaemon" -f -- --daemon

echo Saving PM2 configuration...
pm2 save

echo Waiting for services to initialize...
timeout /t 10 /nobreak

echo Opening admin interface...
start http://localhost:3000/admin.html

echo All services started successfully!
