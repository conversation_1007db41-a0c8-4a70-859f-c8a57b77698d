// createSqlFunctions.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Create a Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function executeSqlFile(filePath) {
  try {
    console.log(`Reading SQL file: ${filePath}`);
    const sql = fs.readFileSync(filePath, 'utf8');
    
    // Split the SQL file into individual statements
    const statements = sql.split(';').filter(stmt => stmt.trim() !== '');
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim();
      if (statement) {
        console.log(`Executing SQL statement ${i + 1}/${statements.length}...`);
        
        // Execute the SQL statement
        const { error } = await supabase.rpc('execute_sql', { sql: statement });
        
        if (error) {
          // If execute_sql function doesn't exist yet, try a raw query
          if (error.message.includes('function execute_sql() does not exist')) {
            console.log('execute_sql function not found, trying raw query...');
            
            // For the first statement (creating execute_sql function)
            if (i === 0 && filePath.includes('create_execute_sql_function.sql')) {
              const { error: rawError } = await supabase.from('_temp_sql_execution').rpc('execute_raw_sql', { sql: statement });
              
              if (rawError) {
                console.error(`Error executing statement: ${rawError.message}`);
                console.error(`Statement: ${statement}`);
              } else {
                console.log('Statement executed successfully');
              }
            } else {
              console.error('Cannot execute statement without execute_sql function');
              console.error('Please create the execute_sql function first');
              process.exit(1);
            }
          } else {
            console.error(`Error executing statement: ${error.message}`);
            console.error(`Statement: ${statement}`);
          }
        } else {
          console.log('Statement executed successfully');
        }
      }
    }
    
    console.log(`Finished executing SQL file: ${filePath}`);
  } catch (err) {
    console.error(`Error reading or executing SQL file: ${err.message}`);
    process.exit(1);
  }
}

async function main() {
  // First, create the execute_sql function
  await executeSqlFile('create_execute_sql_function.sql');
  
  // Then, create the other functions
  await executeSqlFile('create_direct_sql_function.sql');
  
  console.log('All SQL functions created successfully');
}

main();
