-- Helper functions to introspect triggers and function source
CREATE OR REPLACE FUNCTION public.get_trigger_defs_for_table(p_schema text, p_table text)
RETURNS TABLE(trigger_name text, trigger_def text) AS $$
  SELECT t.tgname::text AS trigger_name, pg_get_triggerdef(t.oid)::text AS trigger_def
  FROM pg_trigger t
  JOIN pg_class c ON c.oid = t.tgrelid
  JOIN pg_namespace n ON n.oid = c.relnamespace
  WHERE n.nspname = p_schema
    AND c.relname = p_table
    AND NOT t.tgisinternal
  ORDER BY 1;
$$ LANGUAGE sql STABLE SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.get_function_def(p_schema text, p_func text)
RETURNS text AS $$
DECLARE
  full_name text := quote_ident(p_schema) || '.' || quote_ident(p_func);
  ddl text;
BEGIN
  -- Try without args (not overloaded)
  BEGIN
    EXECUTE 'SELECT pg_get_functiondef('|| full_name ||'::regproc)' INTO ddl;
    RETURN ddl;
  EXCEPTION WHEN undefined_function THEN
    RETURN NULL; -- caller can handle
  END;
END;$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

