<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Discraft OSL Map Import</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h2 {
            margin-top: 0;
            color: #555;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #005a87;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .loading {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .instructions {
            background-color: #e2e3e5;
            border: 1px solid #d6d8db;
            color: #383d41;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
        }
        .instructions ol {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Discraft OSL Map Import Test</h1>
        
        <div class="instructions">
            <h3>📋 Instructions</h3>
            <p>This tool will import Discraft OSL map data from private Google Sheets using the Google Sheets API. Before running:</p>
            <ol>
                <li>Set up Google Sheets API authentication (see <code>GOOGLE_SHEETS_API_SETUP.md</code>)</li>
                <li>Share the Google Sheet with your service account email (Viewer permission)</li>
                <li>The tool will scan the 'Map' sheet for cells starting with 'OS'</li>
                <li>It will then check the 'New' sheet for status information</li>
                <li>Data will be imported into the <code>it_discraft_osl_map</code> table</li>
            </ol>
            <p><strong>🔒 No need to make sheets public!</strong> The API uses secure authentication.</p>
        </div>

        <div class="section">
            <h2>🚀 Import Discraft OSL Map</h2>
            <p>Click the button below to enqueue a task that will import OSL map data from the Google Sheets.</p>
            <button onclick="importOslMap()" id="importBtn">Import OSL Map & Status</button>
            <div id="importResult"></div>
        </div>

        <div class="section">
            <h2>📊 Check Import Results</h2>
            <p>After the import task completes, check the results in the database.</p>
            <button onclick="checkResults()" id="checkBtn">Check Import Results</button>
            <div id="checkResult"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001'; // Adjust if your admin server runs on a different port

        async function importOslMap() {
            const btn = document.getElementById('importBtn');
            const resultDiv = document.getElementById('importResult');
            
            btn.disabled = true;
            btn.textContent = 'Importing...';
            resultDiv.innerHTML = '<div class="result loading">🔄 Enqueueing import task...</div>';

            try {
                const response = await fetch(`${API_BASE}/api/discraft/import-osl-map`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });

                const data = await response.json();

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ ${data.message}
                            
                            Task ID: ${data.taskId}
                            
                            ${data.note}
                            
                            The task is now in the queue and will be processed by the task queue worker.
                            You can check the worker logs or use the "Check Import Results" button below.
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            ❌ Error: ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ Network Error: ${error.message}
                        
                        Make sure the admin server is running on ${API_BASE}
                    </div>
                `;
            } finally {
                btn.disabled = false;
                btn.textContent = 'Import OSL Map & Status';
            }
        }

        async function checkResults() {
            const btn = document.getElementById('checkBtn');
            const resultDiv = document.getElementById('checkResult');
            
            btn.disabled = true;
            btn.textContent = 'Checking...';
            resultDiv.innerHTML = '<div class="result loading">🔍 Checking import results...</div>';

            try {
                // This would need to be implemented as an API endpoint
                // For now, we'll show a placeholder message
                resultDiv.innerHTML = `
                    <div class="result info">
                        ℹ️ To check the import results, you can:
                        
                        1. Query the it_discraft_osl_map table in your database
                        2. Check the task queue worker logs
                        3. Look at the t_task_queue table for task status
                        
                        Example SQL query:
                        SELECT COUNT(*) as total_records, 
                               status, 
                               COUNT(*) as count_by_status 
                        FROM it_discraft_osl_map 
                        GROUP BY status;
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ Error: ${error.message}
                    </div>
                `;
            } finally {
                btn.disabled = false;
                btn.textContent = 'Check Import Results';
            }
        }
    </script>
</body>
</html>
