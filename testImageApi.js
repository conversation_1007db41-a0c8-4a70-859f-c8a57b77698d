// testImageApi.js - Test script for the Image API
import fetch from 'node-fetch';

const API_BASE_URL = 'http://localhost:3005';

async function testApi() {
  console.log('Testing Image API...');
  
  try {
    // Test the root endpoint
    console.log('\n1. Testing root endpoint...');
    const rootResponse = await fetch(`${API_BASE_URL}/`);
    if (rootResponse.ok) {
      const rootData = await rootResponse.json();
      console.log('✅ Root endpoint works!');
      console.log('API Info:', rootData);
    } else {
      console.error('❌ Root endpoint failed:', rootResponse.status, rootResponse.statusText);
    }
    
    // Test the health endpoint
    console.log('\n2. Testing health endpoint...');
    const healthResponse = await fetch(`${API_BASE_URL}/health`);
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('✅ Health endpoint works!');
      console.log('Health status:', healthData);
    } else {
      console.error('❌ Health endpoint failed:', healthResponse.status, healthResponse.statusText);
    }
    
    // Test the list endpoint
    console.log('\n3. Testing list endpoint (root directory)...');
    const listResponse = await fetch(`${API_BASE_URL}/api/list`);
    if (listResponse.ok) {
      const listData = await listResponse.json();
      console.log('✅ List endpoint works!');
      console.log(`Found ${listData.contents.directories.length} directories and ${listData.contents.files.length} files`);
      
      // Show first few directories and files
      if (listData.contents.directories.length > 0) {
        console.log('\nSample directories:');
        listData.contents.directories.slice(0, 3).forEach(dir => {
          console.log(`- ${dir.name} (${dir.path})`);
        });
      }
      
      if (listData.contents.files.length > 0) {
        console.log('\nSample files:');
        listData.contents.files.slice(0, 3).forEach(file => {
          console.log(`- ${file.name} (${file.path})`);
        });
        
        // Test metadata for the first image file
        const imageFile = listData.contents.files.find(file => file.isImage);
        if (imageFile) {
          console.log('\n4. Testing metadata endpoint with image file:', imageFile.path);
          const metadataResponse = await fetch(`${API_BASE_URL}/api/metadata?path=${encodeURIComponent(imageFile.path)}`);
          if (metadataResponse.ok) {
            const metadataData = await metadataResponse.json();
            console.log('✅ Metadata endpoint works!');
            console.log('Image metadata:', metadataData);
          } else {
            console.error('❌ Metadata endpoint failed:', metadataResponse.status, metadataResponse.statusText);
          }
          
          // Test static file endpoint
          console.log('\n5. Testing static file endpoint with image file:', imageFile.path);
          const staticResponse = await fetch(`${API_BASE_URL}/static/${encodeURIComponent(imageFile.path)}`);
          if (staticResponse.ok) {
            const contentType = staticResponse.headers.get('content-type');
            const contentLength = staticResponse.headers.get('content-length');
            console.log('✅ Static file endpoint works!');
            console.log(`Image content type: ${contentType}`);
            console.log(`Image size: ${contentLength} bytes`);
          } else {
            console.error('❌ Static file endpoint failed:', staticResponse.status, staticResponse.statusText);
          }
        } else {
          console.log('No image files found to test metadata and static endpoints');
        }
      }
    } else {
      console.error('❌ List endpoint failed:', listResponse.status, listResponse.statusText);
    }
    
  } catch (error) {
    console.error('Error testing API:', error.message);
  }
}

// Run the tests
testApi().catch(console.error);
