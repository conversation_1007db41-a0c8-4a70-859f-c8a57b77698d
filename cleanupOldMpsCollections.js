import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Shopify configuration
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

// Extract domain from the endpoint URL
let shopifyDomain = 'dzdiscs-new-releases.myshopify.com';
if (shopifyEndpoint) {
  const match = shopifyEndpoint.match(/https:\/\/([^\/]+)/);
  if (match) {
    shopifyDomain = match[1];
  }
}

/**
 * Check if a collection is an MPS collection based on its rules
 */
function isMpsCollection(collection) {
  if (!collection.rules || collection.rules.length < 3) {
    return false;
  }
  
  const hasDiscMoldTag = collection.rules.some(rule => 
    rule.column === 'tag' && 
    rule.relation === 'equals' && 
    rule.condition && 
    rule.condition.startsWith('disc_mold_')
  );
  
  const hasDiscPlasticTag = collection.rules.some(rule => 
    rule.column === 'tag' && 
    rule.relation === 'equals' && 
    rule.condition && 
    rule.condition.startsWith('disc_plastic_')
  );
  
  const hasDiscStampTag = collection.rules.some(rule => 
    rule.column === 'tag' && 
    rule.relation === 'equals' && 
    rule.condition && 
    rule.condition.startsWith('disc_stamp_')
  );
  
  return hasDiscMoldTag && hasDiscPlasticTag && hasDiscStampTag;
}

/**
 * Check if a collection has any products
 */
async function checkCollectionProducts(collectionId) {
  try {
    const response = await fetch(
      `https://${shopifyDomain}/admin/api/2024-01/products.json?collection_id=${collectionId}&limit=1`,
      {
        headers: {
          'X-Shopify-Access-Token': shopifyAccessToken,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (!response.ok) {
      console.warn(`⚠️  Could not check products for collection ${collectionId}: ${response.status}`);
      return null;
    }
    
    const data = await response.json();
    return (data.products && data.products.length > 0);
    
  } catch (error) {
    console.warn(`⚠️  Error checking products for collection ${collectionId}:`, error.message);
    return null;
  }
}

/**
 * Delete a Shopify collection
 */
async function deleteShopifyCollection(collectionId) {
  try {
    const response = await fetch(
      `https://${shopifyDomain}/admin/api/2024-01/smart_collections/${collectionId}.json`,
      {
        method: 'DELETE',
        headers: {
          'X-Shopify-Access-Token': shopifyAccessToken,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    
    return true;
  } catch (error) {
    console.error(`Error deleting Shopify collection ${collectionId}:`, error.message);
    throw error;
  }
}

/**
 * Get oldest empty MPS collections for preview
 */
async function getOldestEmptyMpsCollections(limit = 10) {
  console.log('📊 Fetching Shopify collections for analysis...');
  
  // Fetch first few pages to get oldest collections
  const collections = [];
  let pageCount = 0;
  let nextPageInfo = null;
  
  // Fetch enough pages to get a good sample of old collections
  while (pageCount < 5) {
    pageCount++;
    
    let url = `https://${shopifyDomain}/admin/api/2024-01/smart_collections.json?limit=250`;
    if (nextPageInfo) {
      url += `&page_info=${nextPageInfo}`;
    }
    
    const response = await fetch(url, {
      headers: {
        'X-Shopify-Access-Token': shopifyAccessToken,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    collections.push(...(data.smart_collections || []));
    
    // Check for next page
    const linkHeader = response.headers.get('Link');
    nextPageInfo = null;
    if (linkHeader) {
      const nextMatch = linkHeader.match(/<[^>]*[?&]page_info=([^&>]+)[^>]*>;\s*rel="next"/);
      if (nextMatch) {
        nextPageInfo = nextMatch[1];
      }
    }
    
    if (!nextPageInfo) break;
    
    // Rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log(`📊 Fetched ${collections.length} collections, filtering for MPS...`);
  
  // Filter to only MPS collections
  const mpsCollections = collections.filter(isMpsCollection);
  console.log(`📊 Found ${mpsCollections.length} MPS collections`);
  
  // Sort by updated_at (oldest first)
  mpsCollections.sort((a, b) => new Date(a.updated_at) - new Date(b.updated_at));
  
  // Check the oldest ones for products
  const emptyCollections = [];
  const checkLimit = Math.min(limit * 3, mpsCollections.length); // Check more than needed
  
  console.log(`📊 Checking ${checkLimit} oldest MPS collections for products...`);
  
  for (let i = 0; i < checkLimit && emptyCollections.length < limit; i++) {
    const collection = mpsCollections[i];
    console.log(`   Checking ${i + 1}/${checkLimit}: ${collection.title}`);
    
    const hasProducts = await checkCollectionProducts(collection.id);
    
    if (hasProducts === false) {
      emptyCollections.push({
        id: collection.id,
        title: collection.title,
        handle: collection.handle,
        updated_at: collection.updated_at,
        created_at: collection.created_at
      });
    }
    
    // Rate limiting
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log(`✅ Found ${emptyCollections.length} empty MPS collections`);
  return emptyCollections.slice(0, limit);
}

/**
 * Clean up old MPS collections
 */
async function cleanupOldMpsCollections(collectionsToDelete) {
  const results = {
    success: [],
    errors: [],
    dbUpdates: []
  };
  
  console.log(`🧹 Starting cleanup of ${collectionsToDelete.length} MPS collections...`);
  
  for (const collection of collectionsToDelete) {
    try {
      console.log(`🗑️  Deleting collection: ${collection.title} (ID: ${collection.id})`);
      
      // Delete from Shopify
      await deleteShopifyCollection(collection.id);
      console.log(`✅ Deleted from Shopify: ${collection.title}`);
      
      // Check if there's a corresponding MPS record by handle
      const { data: mpsRecords, error: mpsError } = await supabase
        .from('t_mps')
        .select('id, g_handle, shopify_collection_uploaded_at, active')
        .eq('g_handle', collection.handle);
      
      if (mpsError) {
        console.warn(`⚠️  Error checking MPS records for ${collection.handle}:`, mpsError.message);
      } else if (mpsRecords && mpsRecords.length > 0) {
        // Update MPS record(s)
        for (const mpsRecord of mpsRecords) {
          console.log(`📝 Updating MPS record ${mpsRecord.id} for handle: ${collection.handle}`);
          
          const { error: updateError } = await supabase
            .from('t_mps')
            .update({
              shopify_collection_uploaded_at: null,
              active: false
            })
            .eq('id', mpsRecord.id);
          
          if (updateError) {
            console.error(`❌ Error updating MPS record ${mpsRecord.id}:`, updateError.message);
            results.errors.push({
              collection: collection,
              mpsId: mpsRecord.id,
              error: `Database update failed: ${updateError.message}`
            });
          } else {
            console.log(`✅ Updated MPS record ${mpsRecord.id}`);
            results.dbUpdates.push({
              mpsId: mpsRecord.id,
              handle: collection.handle,
              action: 'Set shopify_collection_uploaded_at to null and active to false'
            });
          }
        }
      } else {
        console.log(`ℹ️  No MPS record found for handle: ${collection.handle}`);
      }
      
      results.success.push(collection);
      
      // Rate limiting between deletions
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error(`❌ Error deleting collection ${collection.title}:`, error.message);
      results.errors.push({
        collection: collection,
        error: error.message
      });
    }
  }
  
  console.log(`🏁 Cleanup complete!`);
  console.log(`   ✅ Successfully deleted: ${results.success.length} collections`);
  console.log(`   📝 Database updates: ${results.dbUpdates.length} MPS records`);
  console.log(`   ❌ Errors: ${results.errors.length}`);
  
  return results;
}

// Export functions for use in admin server
export {
  getOldestEmptyMpsCollections,
  cleanupOldMpsCollections
};
