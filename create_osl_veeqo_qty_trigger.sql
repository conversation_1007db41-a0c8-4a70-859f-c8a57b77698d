-- Function to enqueue a task for updating Veeqo quantity for OSL
CREATE OR REPLACE FUNCTION fn_enqueue_update_veeqo_osl_qty_task()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert a task into the task queue
    INSERT INTO t_task_queue (
        task_type,
        payload,
        status,
        scheduled_at,
        created_at
    ) VALUES (
        'update_veeqo_osl_qty',
        jsonb_build_object('id', NEW.id, 'available_quantity', NEW.available_quantity),
        'pending',
        NOW(),
        NOW()
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the old trigger
DROP TRIGGER IF EXISTS trg_update_veeqo_qty_osl ON t_inv_osl;

-- Create the new trigger
CREATE TRIGGER trg_update_veeqo_qty_osl
AFTER UPDATE ON t_inv_osl 
FOR EACH ROW 
WHEN (NEW.available_quantity IS DISTINCT FROM OLD.available_quantity
      OR NEW.edit_this_to_refresh_veeqo_qty IS DISTINCT FROM OLD.edit_this_to_refresh_veeqo_qty)
EXECUTE FUNCTION fn_enqueue_update_veeqo_osl_qty_task();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger trg_update_veeqo_qty_osl has been updated to enqueue a task.';
END $$;
