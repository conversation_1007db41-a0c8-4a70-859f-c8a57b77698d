import os
import sys
import time
import subprocess

# Use LibreOffice's bundled Python to ensure pyuno is available when possible
# Fallback to system python if necessary (assuming pyuno is available)

ACCEPT = "socket,host=127.0.0.1,port=2002;urp;StarOffice.ServiceManager"
UNO_URL = "uno:socket,host=127.0.0.1,port=2002;urp;StarOffice.ComponentContext"


def start_office_listener():
    soffice_candidates = [
        r"C:\\Program Files\\LibreOffice\\program\\soffice.exe",
        r"C:\\Program Files (x86)\\LibreOffice\\program\\soffice.exe",
        "soffice",
    ]
    for exe in soffice_candidates:
        try:
            subprocess.Popen([
                exe,
                "--headless", "--nologo", "--nodefault", "--norestore", "--invisible",
                f"--accept={ACCEPT}",
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            return True
        except Exception:
            continue
    return False


def hard_recalc(path):
    try:
        import uno
        from com.sun.star.beans import PropertyValue
    except Exception as e:
        print("UNO_IMPORT_ERROR:", e, file=sys.stderr)
        return 2

    def prop(name, value):
        p = PropertyValue()
        p.Name = name
        p.Value = value
        return p

    # Try connect; if it fails, start a listener and retry
    def connect(max_wait=10.0):
        local_ctx = uno.getComponentContext()
        resolver = local_ctx.ServiceManager.createInstanceWithContext(
            "com.sun.star.bridge.UnoUrlResolver", local_ctx
        )
        start = time.time()
        while True:
            try:
                ctx = resolver.resolve(UNO_URL)
                return ctx
            except Exception:
                if time.time() - start > max_wait:
                    raise
                time.sleep(0.5)

    try:
        try:
            ctx = connect(3.0)
        except Exception:
            started = start_office_listener()
            if not started:
                print("SOFFICE_START_FAILED", file=sys.stderr)
                return 3
            ctx = connect(12.0)

        smgr = ctx.ServiceManager
        desktop = smgr.createInstanceWithContext("com.sun.star.frame.Desktop", ctx)
        url = uno.systemPathToFileUrl(os.path.abspath(path))
        args = (prop("Hidden", True), prop("ReadOnly", False))
        doc = desktop.loadComponentFromURL(url, "_blank", 0, args)

        # Dispatch Hard Recalc
        dispatcher = smgr.createInstanceWithContext("com.sun.star.frame.DispatchHelper", ctx)
        frame = doc.getCurrentController().getFrame()
        try:
            dispatcher.executeDispatch(frame, ".uno:CalculateHard", "", 0, tuple())
        except Exception:
            pass

        # As a safety measure, trigger full recalc via API if available
        try:
            doc.calculateAll()
        except Exception:
            pass

        # Store and close
        try:
            doc.store()
        except Exception:
            pass
        try:
            doc.close(True)
        except Exception:
            pass

        # Try to terminate the headless instance (won't close user GUI sessions)
        try:
            desktop.terminate()
        except Exception:
            pass

        print("LO_HARD_RECALC: OK")
        return 0
    except Exception as e:
        print("LO_HARD_RECALC_ERROR:", e, file=sys.stderr)
        return 1


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: lo_hard_recalc.py <path-to-xlsx>", file=sys.stderr)
        sys.exit(1)
    sys.exit(hard_recalc(sys.argv[1]))

