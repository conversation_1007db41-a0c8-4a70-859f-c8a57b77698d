-- First drop the existing function
DROP FUNCTION IF EXISTS find_matching_osl(integer, integer, numeric);

-- Then create the new function with enhanced debugging
CREATE OR REPLACE FUNCTION find_matching_osl(
    mps_id_param INTEGER,
    color_id_param INTEGER,
    weight_param NUMERIC
)
RETURNS TABLE (
    osl_id INTEGER,
    debug_info TEXT
) AS $$
DECLARE
    rounded_weight NUMERIC;
    debug_text TEXT;
    decimal_part NUMERIC;
    matching_osls INTEGER;
    specific_osl RECORD;
BEGIN
    -- Ensure weight_param is treated as NUMERIC
    weight_param := weight_param::NUMERIC;

    -- Custom rounding logic:
    -- X.5 and up rounds to X+1, X.4 and down rounds to X
    decimal_part := weight_param - FLOOR(weight_param);

    IF decimal_part >= 0.5 THEN
        rounded_weight := CEIL(weight_param);
    ELSE
        rounded_weight := FLOOR(weight_param);
    END IF;

    -- Count how many OSLs match the criteria without the weight check
    SELECT COUNT(*) INTO matching_osls
    FROM t_order_sheet_lines osl
    WHERE osl.mps_id = mps_id_param
      AND (osl.color_id = color_id_param OR osl.color_id = 23);

    -- Check specifically for OSL 16890
    SELECT * INTO specific_osl
    FROM t_order_sheet_lines
    WHERE id = 16890;

    -- Create debug info
    debug_text := 'Input weight: ' || weight_param ||
                  ', Decimal part: ' || decimal_part ||
                  ', Rounded weight: ' || rounded_weight ||
                  ', Matching OSLs before weight check: ' || matching_osls;

    -- Add specific OSL info if found
    IF specific_osl IS NOT NULL THEN
        debug_text := debug_text ||
                     ', OSL 16890 exists with mps_id=' || specific_osl.mps_id ||
                     ', min_weight=' || specific_osl.min_weight ||
                     ', max_weight=' || specific_osl.max_weight ||
                     ', color_id=' || specific_osl.color_id;

        -- Check if OSL 16890 would match
        IF specific_osl.mps_id = mps_id_param AND
           (specific_osl.color_id = color_id_param OR specific_osl.color_id = 23) AND
           rounded_weight >= specific_osl.min_weight AND
           rounded_weight <= specific_osl.max_weight THEN
            debug_text := debug_text || ', OSL 16890 SHOULD MATCH!';
        ELSE
            debug_text := debug_text || ', OSL 16890 does not match because: ';

            IF specific_osl.mps_id != mps_id_param THEN
                debug_text := debug_text || 'MPS mismatch, ';
            END IF;

            IF NOT (specific_osl.color_id = color_id_param OR specific_osl.color_id = 23) THEN
                debug_text := debug_text || 'Color mismatch, ';
            END IF;

            IF NOT (rounded_weight >= specific_osl.min_weight) THEN
                debug_text := debug_text || 'Weight below min, ';
            END IF;

            IF NOT (rounded_weight <= specific_osl.max_weight) THEN
                debug_text := debug_text || 'Weight above max, ';
            END IF;
        END IF;
    ELSE
        debug_text := debug_text || ', OSL 16890 does not exist in the database';
    END IF;

    -- Try the normal query
    RETURN QUERY
    SELECT
        osl.id AS osl_id,
        debug_text ||
        ', OSL ID: ' || osl.id ||
        ', MPS match: ' || (osl.mps_id = mps_id_param)::TEXT ||
        ', Color match: ' || ((osl.color_id = color_id_param OR osl.color_id = 23))::TEXT ||
        ', Weight range: ' || osl.min_weight || '-' || osl.max_weight ||
        ', Weight in range: ' || ((rounded_weight >= osl.min_weight AND rounded_weight <= osl.max_weight))::TEXT
        AS debug_info
    FROM t_order_sheet_lines osl
    WHERE osl.mps_id = mps_id_param
      AND (osl.color_id = color_id_param OR osl.color_id = 23)
      AND rounded_weight >= osl.min_weight
      AND rounded_weight <= osl.max_weight
    LIMIT 1;

    -- If no rows returned, return a debug row
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT
            NULL::INTEGER AS osl_id,
            debug_text ||
            ', No matching OSL found for mps_id=' || mps_id_param ||
            ', color_id=' || color_id_param ||
            ', rounded_weight=' || rounded_weight
            AS debug_info;
    END IF;
END;
$$ LANGUAGE plpgsql;
