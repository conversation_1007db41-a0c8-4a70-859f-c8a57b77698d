import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkMissedRecords() {
  try {
    console.log('Checking for missed special case records...\n');

    // Check the specific records mentioned
    const { data: records, error } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .in('id', [69302, 42907]);
    
    if (error) {
      console.error('Error:', error);
      return;
    }
    
    console.log('Specific records mentioned:');
    records.forEach(record => {
      console.log(`ID ${record.id}: ${record.notes}`);
      console.log(`  Raw notes: ${record.raw_notes || 'NULL'}`);
    });
    
    // Check for any other records with these patterns that weren't caught
    console.log('\nChecking for other missed Starter Set records...');
    const { data: starterRecords, error: starterError } = await supabase
      .from('t_sdasins')
      .select('id, notes')
      .like('notes', '%Starter Set%')
      .not('notes', 'like', 'XXXX%')
      .limit(10);
    
    if (!starterError && starterRecords) {
      console.log(`Found ${starterRecords.length} Starter Set records not marked as XXXX:`);
      starterRecords.forEach(record => {
        console.log(`  ID ${record.id}: ${record.notes}`);
      });
    }
    
    console.log('\nChecking for other missed Golf Basket records...');
    const { data: basketRecords, error: basketError } = await supabase
      .from('t_sdasins')
      .select('id, notes')
      .like('notes', '%Golf Basket%')
      .not('notes', 'like', 'XXXX%')
      .limit(10);
    
    if (!basketError && basketRecords) {
      console.log(`Found ${basketRecords.length} Golf Basket records not marked as XXXX:`);
      basketRecords.forEach(record => {
        console.log(`  ID ${record.id}: ${record.notes}`);
      });
    }

    console.log('\nChecking for other missed Disc Storage Rack records...');
    const { data: rackRecords, error: rackError } = await supabase
      .from('t_sdasins')
      .select('id, notes')
      .like('notes', '%Disc Storage Rack%')
      .not('notes', 'like', 'XXXX%')
      .limit(10);
    
    if (!rackError && rackRecords) {
      console.log(`Found ${rackRecords.length} Disc Storage Rack records not marked as XXXX:`);
      rackRecords.forEach(record => {
        console.log(`  ID ${record.id}: ${record.notes}`);
      });
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

checkMissedRecords();
