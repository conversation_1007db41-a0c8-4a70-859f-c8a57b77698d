import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkOSLCounts() {
  console.log('Checking OSL counts...');
  
  // Check OSL records that need player tag updates
  const { data: oslData, error: oslError } = await supabase
    .from('t_order_sheet_lines')
    .select(`
      id,
      mps_id,
      shopify_uploaded_at,
      t_mps!inner (
        id,
        stamp_id,
        t_stamps!inner (
          id,
          stamp,
          player_id,
          t_players!inner (
            id,
            name
          )
        )
      )
    `)
    .not('shopify_uploaded_at', 'is', null)
    .gte('shopify_uploaded_at', '2025-02-01T00:00:00Z')
    .not('t_mps.t_stamps.player_id', 'is', null);
    
  if (oslError) {
    console.error('Error with OSL query:', oslError);
    return;
  }
  
  console.log('OSL records uploaded after 2/1/2025 with stamps that have players:', oslData.length);
  
  // Show some examples
  if (oslData.length > 0) {
    console.log('\nFirst 10 examples:');
    oslData.slice(0, 10).forEach(osl => {
      console.log(`  OSL ${osl.id}: ${osl.t_mps.t_stamps.t_players.name} (uploaded: ${osl.shopify_uploaded_at})`);
    });
  }
  
  // Group by player to see distribution
  const playerCounts = {};
  oslData.forEach(osl => {
    const playerName = osl.t_mps.t_stamps.t_players.name;
    playerCounts[playerName] = (playerCounts[playerName] || 0) + 1;
  });
  
  console.log('\nPlayer distribution:');
  Object.entries(playerCounts)
    .sort(([,a], [,b]) => b - a)
    .forEach(([player, count]) => {
      console.log(`  ${player}: ${count} OSL records`);
    });
}

checkOSLCounts().catch(console.error);
