// Test script for update_b2f_location task
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Create a Supabase client using environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testUpdateB2fLocationTask() {
  console.log('Testing update_b2f_location task...');

  try {
    // First, let's check if there are any discs with B2F locations
    const { data: b2fDiscs, error: checkError } = await supabase
      .from('t_discs')
      .select('id, location')
      .like('location', 'B2F%')
      .limit(5);

    if (checkError) {
      console.error('Error checking for B2F discs:', checkError);
      return;
    }

    console.log(`Found ${b2fDiscs?.length || 0} discs with B2F locations:`);
    if (b2fDiscs && b2fDiscs.length > 0) {
      b2fDiscs.forEach(disc => {
        console.log(`  Disc ID ${disc.id}: location = '${disc.location}'`);
      });
    }

    // Enqueue a test task with the sample payload
    const testPayload = {
      location: "B2F 08-09"
    };

    console.log('\nEnqueueing update_b2f_location task with payload:', JSON.stringify(testPayload));

    const { data: taskData, error: taskError } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'update_b2f_location',
        payload: testPayload,
        status: 'pending',
        scheduled_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        enqueued_by: 'test_update_b2f_location'
      })
      .select();

    if (taskError) {
      console.error('Error creating task:', taskError);
      return;
    }

    if (!taskData || taskData.length === 0) {
      console.log('No task created');
      return;
    }

    const task = taskData[0];
    console.log(`Task created successfully with ID: ${task.id}`);
    console.log('Task details:', JSON.stringify(task, null, 2));

    console.log('\nTask has been enqueued. Run the worker to process it.');
    console.log('You can check the task status in the t_task_queue table.');

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the test
testUpdateB2fLocationTask();
