require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function enqueueTasksSoldDiscs() {
  try {
    console.log('Getting ALL sold discs with null vendor_osl_id and enqueueing match_disc_to_osl tasks...');
    
    // Get ALL sold discs with null vendor_osl_id
    const { data: soldDiscs, error: fetchError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .is('vendor_osl_id', null)
      .not('sold_date', 'is', null)  // Sold discs only
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null)
      .order('id');
    
    if (fetchError) {
      console.error('Error getting sold discs:', fetchError);
      return;
    }
    
    console.log(`Found ${soldDiscs.length} sold discs with null vendor_osl_id`);
    
    if (soldDiscs.length === 0) {
      console.log('✅ No sold discs with null vendor_osl_id found!');
      return;
    }
    
    console.log('\nFirst 10 sold discs as sample:');
    soldDiscs.slice(0, 10).forEach((disc, index) => {
      console.log(`${index + 1}. Disc ${disc.id}: MPS ${disc.mps_id}, Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id}, Sold: ${disc.sold_date}`);
    });
    
    if (soldDiscs.length > 10) {
      console.log(`... and ${soldDiscs.length - 10} more sold discs`);
    }
    
    // Enqueue match_disc_to_osl tasks for each sold disc
    console.log('\nEnqueueing match_disc_to_osl tasks for all sold discs...');
    
    const tasksToEnqueue = soldDiscs.map(disc => ({
      task_type: 'match_disc_to_osl',
      payload: {
        id: disc.id,
        operation: 'UPDATE',
        old_data: disc
      },
      status: 'pending',
      scheduled_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      enqueued_by: 'sold_discs_vendor_osl_check'
    }));
    
    // Insert tasks in batches to avoid potential issues with large inserts
    const batchSize = 100;
    let totalEnqueued = 0;
    let allEnqueuedTasks = [];
    
    for (let i = 0; i < tasksToEnqueue.length; i += batchSize) {
      const batch = tasksToEnqueue.slice(i, i + batchSize);
      
      const { data: enqueuedBatch, error: enqueueError } = await supabase
        .from('t_task_queue')
        .insert(batch)
        .select('id, task_type, payload');
      
      if (enqueueError) {
        console.error(`Error enqueueing batch ${Math.floor(i / batchSize) + 1}:`, enqueueError);
        continue;
      }
      
      totalEnqueued += enqueuedBatch.length;
      allEnqueuedTasks = allEnqueuedTasks.concat(enqueuedBatch);
      
      console.log(`Batch ${Math.floor(i / batchSize) + 1}: Enqueued ${enqueuedBatch.length} tasks (Total: ${totalEnqueued}/${soldDiscs.length})`);
    }
    
    console.log(`\n✅ Successfully enqueued ${totalEnqueued} match_disc_to_osl tasks for sold discs!`);
    
    console.log('\nFirst 5 task details:');
    allEnqueuedTasks.slice(0, 5).forEach((task, index) => {
      const discId = task.payload.id;
      console.log(`${index + 1}. Task ${task.id}: match_disc_to_osl for disc ${discId}`);
    });
    
    if (allEnqueuedTasks.length > 5) {
      console.log(`... and ${allEnqueuedTasks.length - 5} more tasks`);
    }
    
    console.log('\n🔄 Tasks have been enqueued and will be processed by the task queue worker.');
    console.log('The worker will:');
    console.log('1. Test both regular OSL mapping (using weight)');
    console.log('2. Test vendor OSL mapping (using weight_mfg)');
    console.log('3. Update both order_sheet_line_id and vendor_osl_id if matches are found');
    console.log('4. Provide detailed debug information in the task results');
    
    console.log('\nNote: These are SOLD discs, so they are not part of active inventory,');
    console.log('but completing their vendor OSL mappings will provide comprehensive data coverage.');
    
    console.log('\n📋 To check task status for sold discs, run:');
    console.log(`
SELECT 
  COUNT(*) as total_tasks,
  status,
  COUNT(CASE WHEN result->>'vendor_osl_id' IS NOT NULL THEN 1 END) as found_vendor_mappings
FROM t_task_queue 
WHERE enqueued_by = 'sold_discs_vendor_osl_check' 
GROUP BY status
ORDER BY status;
    `);
    
    console.log('\nTo see detailed results:');
    console.log(`
SELECT 
  id, 
  status, 
  result->>'message' as message,
  result->>'vendor_osl_id' as vendor_osl_id,
  payload->>'id' as disc_id,
  processed_at
FROM t_task_queue 
WHERE enqueued_by = 'sold_discs_vendor_osl_check' 
  AND status = 'completed'
  AND result->>'vendor_osl_id' IS NOT NULL
ORDER BY processed_at
LIMIT 10;
    `);
    
    // Show current counts
    const { count: soldNullCount, error: soldCountError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('vendor_osl_id', null)
      .not('sold_date', 'is', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    const { count: soldWithVendorCount, error: soldVendorError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .not('vendor_osl_id', 'is', null)
      .not('sold_date', 'is', null);
    
    if (!soldCountError && !soldVendorError) {
      console.log(`\n📊 CURRENT SOLD DISCS STATISTICS:`);
      console.log(`Sold discs with null vendor_osl_id: ${soldNullCount}`);
      console.log(`Sold discs with vendor_osl_id: ${soldWithVendorCount}`);
      const totalSold = soldNullCount + soldWithVendorCount;
      const soldSuccessRate = ((soldWithVendorCount / totalSold) * 100).toFixed(1);
      console.log(`Current sold discs vendor mapping success rate: ${soldSuccessRate}%`);
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

enqueueTasksSoldDiscs();
