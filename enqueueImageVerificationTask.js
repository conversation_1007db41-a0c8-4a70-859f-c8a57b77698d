// enqueueImageVerificationTask.js

// Suppress Node.js deprecation warnings
process.noDeprecation = true;

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import minimist from 'minimist';

// Parse command-line arguments (e.g., --id=12345 --delay=5)
const args = minimist(process.argv.slice(2));
const imageId = args.id;
const delayMinutes = args.delay || 0; // Default to 0 (no delay)

if (!imageId) {
  console.error('[enqueueImageVerificationTask.js] Missing required --id= argument.');
  process.exit(1);
}

// Calculate scheduled time (default to current time if no delay specified)
let scheduledAt = new Date().toISOString(); // Default to current time
if (delayMinutes > 0) {
  scheduledAt = new Date(Date.now() + delayMinutes * 60 * 1000).toISOString();
  console.log(`[enqueueImageVerificationTask.js] Task will be scheduled for execution at ${scheduledAt} (${delayMinutes} minutes from now)`);
} else {
  console.log(`[enqueueImageVerificationTask.js] Task will be scheduled for immediate execution`);
}

// Create a Supabase client using environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('[enqueueImageVerificationTask.js] Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  console.log(`[enqueueImageVerificationTask.js] Enqueueing verification task for id=${imageId}`);

  try {
    // Create a task in the t_task_queue table
    const taskData = {
      task_type: 'verify_t_images_image',
      status: 'pending',
      payload: JSON.stringify({ id: imageId }),
      created_at: new Date().toISOString(),
      scheduled_at: scheduledAt // Always include scheduled_at (now required)
    };

    const { data, error } = await supabase
      .from('t_task_queue')
      .insert(taskData)
      .select();

    if (error) {
      console.error(`[enqueueImageVerificationTask.js] Error creating task: ${error.message}`);
      process.exit(1);
    }

    console.log(`[enqueueImageVerificationTask.js] Task created successfully with id=${data[0].id}`);
  } catch (err) {
    console.error(`[enqueueImageVerificationTask.js] Exception: ${err.message}`);
    process.exit(1);
  }
}

main();
