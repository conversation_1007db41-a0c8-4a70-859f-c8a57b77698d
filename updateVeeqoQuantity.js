import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Update product quantity in Veeqo
 * @param {string|number} veeqoId - Veeqo product ID
 * @param {number} qty - New quantity value
 * @param {string} table - Table name
 * @param {string|number} recordId - Record ID in the table
 * @returns {Promise<{success: boolean, error: string|null}>} - Result with success status and error message
 */
async function updateVeeqoQuantity(veeqoId, qty, table, recordId) {
  console.log(`🔄 Updating Veeqo quantity for product ID ${veeqoId} to ${qty}`);
  
  // Get API key from environment variables
  const apiKey = process.env.VEEQO_API_KEY;
  
  if (!apiKey) {
    console.error('❌ VEEQO_API_KEY environment variable is not set');
    return { success: false, error: 'Veeqo API key not set' };
  }

  // Construct API URL for the warehouse stock entry
  // Using warehouse ID 99881 as seen in other files
  const url = `https://api.veeqo.com/sellables/${veeqoId}/warehouses/99881/stock_entry`;
  console.log(`🔍 Using Veeqo API URL: ${url}`);

  // Construct payload
  const payload = {
    stock_entry: {
      physical_stock_level: qty,
      infinite: false
    }
  };
  console.log(`📦 Request payload: ${JSON.stringify(payload)}`);

  const options = {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': apiKey
    },
    body: JSON.stringify(payload),
    // Add timeout to fetch request
    timeout: 3000
  };

  try {
    console.log(`🚀 Sending request to Veeqo API...`);
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000);
    
    const response = await fetch(url, {
      ...options,
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      console.error(`❌ Veeqo API error: ${response.status}`);
      let responseText;
      try {
        // Try to parse as JSON first
        const responseData = await response.json();
        responseText = JSON.stringify(responseData);
        console.error(`Response JSON: ${responseText}`);
        
        // Check for specific error messages in the response
        if (responseData.errors && Array.isArray(responseData.errors)) {
          console.error(`Detailed errors: ${responseData.errors.join(', ')}`);
        }
      } catch (e) {
        // If not JSON, get as text
        responseText = await response.text();
        console.error(`Response: ${responseText}`);
      }
      
      // Check if it's a 404 error specifically
      if (response.status === 404) {
        console.error(`❌ Product with ID ${veeqoId} not found in Veeqo. Verify the ID is correct.`);
        
        // Try to get more information about this product ID
        try {
          console.log(`🔍 Attempting to get more information about product ID ${veeqoId}...`);
          const productInfoUrl = `https://api.veeqo.com/products/${veeqoId}`;
          const productResponse = await fetch(productInfoUrl, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': apiKey
            }
          });
          
          if (productResponse.ok) {
            const productData = await productResponse.json();
            console.log(`ℹ️ Product exists but might not be configured for warehouse stock: ${JSON.stringify(productData.title)}`);
          } else {
            console.log(`ℹ️ Product ID ${veeqoId} does not exist in Veeqo`);
          }
        } catch (infoError) {
          console.error(`❌ Error getting product info: ${infoError.message}`);
        }
        
        return { 
          success: false, 
          error: `Product with ID ${veeqoId} not found in Veeqo`,
          invalidVeeqoId: true 
        };
      }
      
      return { success: false, error: `Veeqo API error: ${response.status}` };
    }
    
    console.log(`✅ Successfully updated quantity for Veeqo product ID ${veeqoId}`);
    return { success: true, error: null };
  } catch (error) {
    if (error.name === 'AbortError') {
      console.error(`⏱️ Request to Veeqo API timed out after 3 seconds`);
      return { success: false, error: 'Request to Veeqo API timed out' };
    }
    console.error(`❌ Error updating Veeqo quantity:`, error);
    return { success: false, error: error.message };
  }
}

// Only run the command-line portion if this file is being executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  // Handle command line arguments
  const veeqoId = process.argv[2];
  const qty = parseInt(process.argv[3], 10);

  // Validate arguments
  if (!veeqoId || isNaN(qty)) {
    console.error('❌ Usage: node updateVeeqoQuantity.js <veeqo_id> <qty>');
    process.exit(1);
  }

  // Execute the update
  updateVeeqoQuantity(veeqoId, qty)
    .then(result => {
      if (result.success) {
        console.log('✅ Operation completed successfully');
        process.exit(0);
      } else {
        console.error('❌ Operation failed');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('❌ Unexpected error:', error);
      process.exit(1);
    });
}

// Change from CommonJS to ES Module export
export { updateVeeqoQuantity };



