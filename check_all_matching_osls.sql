-- Create a function to check all potential matching OSLs for disc 421363
CREATE OR REPLACE FUNCTION check_all_matching_osls()
RETURNS TABLE (
    osl_id INTEGER,
    mps_id INTEGER,
    min_weight SMALLINT,
    max_weight SMALLINT,
    color_id SMALLINT,
    rounded_weight NUMERIC,
    mps_match <PERSON>OOLEAN,
    color_match BOOLEAN,
    weight_match <PERSON><PERSON><PERSON>EAN,
    should_match BOOLEAN
) AS $$
DECLARE
    disc_id INTEGER := 421363;
    disc_mps_id INTEGER := 17696;
    disc_color_id INTEGER := 4;
    disc_weight NUMERIC := 169.7;

    rounded_weight NUMERIC;
    decimal_part NUMERIC;
BEGIN
    -- Custom rounding logic
    decimal_part := disc_weight - FLOOR(disc_weight);

    IF decimal_part >= 0.5 THEN
        rounded_weight := CEIL(disc_weight);
    ELSE
        rounded_weight := FLOOR(disc_weight);
    END IF;

    -- Return all potential matching OSLs
    RETURN QUERY
    SELECT
        osl.id AS osl_id,
        osl.mps_id,
        osl.min_weight,
        osl.max_weight,
        osl.color_id,
        rounded_weight,
        (osl.mps_id = disc_mps_id) AS mps_match,
        (osl.color_id = disc_color_id OR osl.color_id = 23) AS color_match,
        (rounded_weight >= osl.min_weight AND rounded_weight <= osl.max_weight) AS weight_match,
        (
            osl.mps_id = disc_mps_id AND
            (osl.color_id = disc_color_id OR osl.color_id = 23) AND
            rounded_weight >= osl.min_weight AND
            rounded_weight <= osl.max_weight
        ) AS should_match
    FROM
        t_order_sheet_lines osl
    WHERE
        osl.mps_id = disc_mps_id
    ORDER BY
        osl.id;
END;
$$ LANGUAGE plpgsql;

-- Call the function
SELECT * FROM check_all_matching_osls();
