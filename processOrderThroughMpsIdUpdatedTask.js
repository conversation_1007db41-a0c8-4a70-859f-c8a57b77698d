/**
 * Process order_through_mps_id_updated task
 * When an MPS record's order_through_mps_id is changed, this task:
 * 1. Finds all discs related to that MPS
 * 2. Sets their vendor_osl_id to null
 * 3. Enqueues match_disc_to_osl tasks for each disc to recalculate mappings
 */
async function processOrderThroughMpsIdUpdatedTask(task, { supabase, updateTaskStatus, logError }) {
  try {
    const { id: mpsId } = task.payload;

    if (!mpsId) {
      await updateTaskStatus(task.id, 'error', {
        message: "Missing MPS ID in task payload.",
        error: 'Invalid payload'
      });
      return;
    }

    console.log(`[processOrderThroughMpsIdUpdatedTask.js] Processing order_through_mps_id_updated task for MPS id=${mpsId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Get the MPS record to understand the change
    const { data: mpsRecord, error: mpsError } = await supabase
      .from('t_mps')
      .select('id, order_through_mps_id')
      .eq('id', mpsId)
      .single();

    if (mpsError) {
      const errMsg = `[processOrderThroughMpsIdUpdatedTask.js] Error retrieving MPS record: ${mpsError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving MPS record for id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to retrieve MPS record. Database error.",
        error: mpsError.message
      });
      return;
    }

    if (!mpsRecord) {
      const errMsg = `[processOrderThroughMpsIdUpdatedTask.js] MPS record not found for id=${mpsId}`;
      console.error(errMsg);
      await logError(errMsg, `MPS record not found for id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process order_through_mps_id update. MPS record not found.",
        error: 'MPS record not found'
      });
      return;
    }

    console.log(`[processOrderThroughMpsIdUpdatedTask.js] MPS ${mpsId} order_through_mps_id: ${mpsRecord.order_through_mps_id || 'NULL'}`);

    // Find all discs related to this MPS
    const { data: relatedDiscs, error: discsError } = await supabase
      .from('t_discs')
      .select('id, vendor_osl_id')
      .eq('mps_id', mpsId);

    if (discsError) {
      const errMsg = `[processOrderThroughMpsIdUpdatedTask.js] Error finding related discs: ${discsError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Finding discs for MPS id=${mpsId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to find related discs. Database error.",
        error: discsError.message
      });
      return;
    }

    console.log(`[processOrderThroughMpsIdUpdatedTask.js] Found ${relatedDiscs ? relatedDiscs.length : 0} discs related to MPS ${mpsId}`);

    if (!relatedDiscs || relatedDiscs.length === 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: `No discs found related to MPS ${mpsId}. No action needed.`,
        mps_id: mpsId,
        discs_found: 0,
        discs_updated: 0,
        tasks_enqueued: 0
      });
      return;
    }

    // Step 1: Set vendor_osl_id to null for all related discs
    console.log(`[processOrderThroughMpsIdUpdatedTask.js] Setting vendor_osl_id to null for ${relatedDiscs.length} discs...`);

    const discIds = relatedDiscs.map(disc => disc.id);
    const { error: updateError } = await supabase
      .from('t_discs')
      .update({ vendor_osl_id: null })
      .in('id', discIds);

    if (updateError) {
      const errMsg = `[processOrderThroughMpsIdUpdatedTask.js] Error updating discs vendor_osl_id: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating vendor_osl_id for MPS ${mpsId} discs`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to update discs vendor_osl_id. Database error.",
        error: updateError.message
      });
      return;
    }

    console.log(`[processOrderThroughMpsIdUpdatedTask.js] Successfully set vendor_osl_id to null for ${relatedDiscs.length} discs`);

    // Step 2: Enqueue match_disc_to_osl tasks for each disc
    console.log(`[processOrderThroughMpsIdUpdatedTask.js] Enqueueing match_disc_to_osl tasks for ${relatedDiscs.length} discs...`);

    const tasksToEnqueue = relatedDiscs.map(disc => ({
      task_type: 'match_disc_to_osl',
      payload: {
        id: disc.id,
        operation: 'UPDATE',
        old_data: {
          vendor_osl_id: disc.vendor_osl_id // Include the old vendor_osl_id that we're clearing
        }
      },
      status: 'pending',
      scheduled_at: new Date().toISOString(), // Schedule immediately
      created_at: new Date().toISOString(),
      enqueued_by: `order_through_mps_id_updated_mps_${mpsId}`
    }));

    // Insert tasks in batches to avoid overwhelming the database
    const batchSize = 100;
    let enqueuedCount = 0;
    let errorCount = 0;

    for (let i = 0; i < tasksToEnqueue.length; i += batchSize) {
      const batch = tasksToEnqueue.slice(i, i + batchSize);
      
      const { error: enqueueError } = await supabase
        .from('t_task_queue')
        .insert(batch);

      if (enqueueError) {
        console.error(`[processOrderThroughMpsIdUpdatedTask.js] Error enqueueing batch ${Math.floor(i/batchSize) + 1}:`, enqueueError);
        errorCount += batch.length;
      } else {
        enqueuedCount += batch.length;
        console.log(`[processOrderThroughMpsIdUpdatedTask.js] Enqueued batch ${Math.floor(i/batchSize) + 1}: ${batch.length} tasks`);
      }
    }

    console.log(`[processOrderThroughMpsIdUpdatedTask.js] Task enqueueing complete: ${enqueuedCount} successful, ${errorCount} errors`);

    // Complete the task
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully processed order_through_mps_id update for MPS ${mpsId}. Updated ${relatedDiscs.length} discs and enqueued ${enqueuedCount} match_disc_to_osl tasks.`,
      mps_id: mpsId,
      mps_order_through_mps_id: mpsRecord.order_through_mps_id,
      discs_found: relatedDiscs.length,
      discs_updated: relatedDiscs.length,
      tasks_enqueued: enqueuedCount,
      tasks_failed: errorCount
    });

    console.log(`[processOrderThroughMpsIdUpdatedTask.js] Successfully completed order_through_mps_id_updated task for MPS ${mpsId}`);

  } catch (err) {
    const errMsg = `[processOrderThroughMpsIdUpdatedTask.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process order_through_mps_id_updated task due to an unexpected error.",
      error: err.message
    });
  }
}

export default processOrderThroughMpsIdUpdatedTask;
