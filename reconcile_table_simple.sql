-- Create a reconciliation table to compare RPRO and Veeqo quantities
CREATE TABLE IF NOT EXISTS public.reconcile_rpro_counts_to_veeqo (
  id SERIAL PRIMARY KEY,
  rpro_id INTEGER,
  veeqo_id BIGINT,
  sku_code TEXT,
  product_title TEXT,
  rpro_qty NUMERIC(15, 2),
  veeqo_qty NUMERIC(15, 2),
  quantity_difference NUMERIC(15, 2),
  last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_reconcile_rpro_id ON public.reconcile_rpro_counts_to_veeqo(rpro_id);
CREATE INDEX IF NOT EXISTS idx_reconcile_veeqo_id ON public.reconcile_rpro_counts_to_veeqo(veeqo_id);
CREATE INDEX IF NOT EXISTS idx_reconcile_quantity_difference ON public.reconcile_rpro_counts_to_veeqo(quantity_difference);

-- Create a view for easy querying of discrepancies
CREATE OR REPLACE VIEW public.v_reconcile_rpro_counts_to_veeqo AS
SELECT
  id,
  rpro_id,
  veeqo_id,
  sku_code,
  product_title,
  rpro_qty,
  veeqo_qty,
  quantity_difference,
  last_updated,
  CASE
    WHEN quantity_difference > 0 THEN 'RPRO has more'
    WHEN quantity_difference < 0 THEN 'Veeqo has more'
    ELSE 'Equal'
  END AS status
FROM
  public.reconcile_rpro_counts_to_veeqo
ORDER BY
  ABS(quantity_difference) DESC;

-- Clear the existing data
TRUNCATE TABLE public.reconcile_rpro_counts_to_veeqo;

-- Insert data from both tables with matching SKUs
INSERT INTO public.reconcile_rpro_counts_to_veeqo (
  rpro_id,
  veeqo_id,
  sku_code,
  product_title,
  rpro_qty,
  veeqo_qty,
  quantity_difference
)
SELECT
  rpro.ivno AS rpro_id,
  veeqo.product_id AS veeqo_id,
  veeqo.sku_code,
  veeqo.product_title,
  rpro.ivqtylaw AS rpro_qty,
  CASE
    WHEN CAST(veeqo.total_qty AS NUMERIC) > 1000000 THEN 0
    ELSE CAST(veeqo.total_qty AS NUMERIC)
  END AS veeqo_qty,
  CASE
    WHEN rpro.ivqtylaw > 1000000 OR CAST(veeqo.total_qty AS NUMERIC) > 1000000 THEN 0
    ELSE rpro.ivqtylaw - CAST(veeqo.total_qty AS NUMERIC)
  END AS quantity_difference
FROM
  public.imported_table_rpro rpro
JOIN
  public.imported_table_veeqo_sellables_export veeqo
  ON 'R' || LPAD(rpro.ivno::TEXT, 5, '0') = veeqo.sku_code
WHERE
  rpro.ivqtylaw IS NOT NULL
  AND veeqo.total_qty IS NOT NULL;

-- Insert RPRO records that don't have matching Veeqo records
INSERT INTO public.reconcile_rpro_counts_to_veeqo (
  rpro_id,
  sku_code,
  rpro_qty,
  veeqo_qty,
  quantity_difference
)
SELECT
  rpro.ivno AS rpro_id,
  'R' || LPAD(rpro.ivno::TEXT, 5, '0') AS sku_code,
  rpro.ivqtylaw AS rpro_qty,
  0 AS veeqo_qty,
  CASE
    WHEN rpro.ivqtylaw > 1000000 THEN 0
    ELSE rpro.ivqtylaw
  END AS quantity_difference
FROM
  public.imported_table_rpro rpro
WHERE
  rpro.ivqtylaw IS NOT NULL
  AND rpro.ivqtylaw > 0
  AND NOT EXISTS (
    SELECT 1
    FROM public.imported_table_veeqo_sellables_export veeqo
    WHERE 'R' || LPAD(rpro.ivno::TEXT, 5, '0') = veeqo.sku_code
  );

-- Insert Veeqo records that don't have matching RPRO records
INSERT INTO public.reconcile_rpro_counts_to_veeqo (
  veeqo_id,
  sku_code,
  product_title,
  rpro_qty,
  veeqo_qty,
  quantity_difference
)
SELECT
  veeqo.product_id AS veeqo_id,
  veeqo.sku_code,
  veeqo.product_title,
  0 AS rpro_qty,
  CASE
    WHEN CAST(veeqo.total_qty AS NUMERIC) > 1000000 THEN 0
    ELSE CAST(veeqo.total_qty AS NUMERIC)
  END AS veeqo_qty,
  CASE
    WHEN CAST(veeqo.total_qty AS NUMERIC) > 1000000 THEN 0
    ELSE 0 - CAST(veeqo.total_qty AS NUMERIC)
  END AS quantity_difference
FROM
  public.imported_table_veeqo_sellables_export veeqo
WHERE
  veeqo.total_qty IS NOT NULL
  AND CAST(veeqo.total_qty AS NUMERIC) > 0
  AND veeqo.sku_code LIKE 'R%'
  AND NOT EXISTS (
    SELECT 1
    FROM public.imported_table_rpro rpro
    WHERE 'R' || LPAD(rpro.ivno::TEXT, 5, '0') = veeqo.sku_code
  );

-- Update the last_updated timestamp for all records
UPDATE public.reconcile_rpro_counts_to_veeqo
SET last_updated = NOW()
WHERE id > 0;
