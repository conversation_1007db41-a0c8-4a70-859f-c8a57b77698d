-- Function to get MPS review data for the ToDo interface
-- This function returns active MPS records that have no discs in stock

CREATE OR REPLACE FUNCTION get_mps_review_data()
RETURNS TABLE (
  id integer,
  g_code text,
  sold_date_last date,
  received_date_last timestamp without time zone
) 
LANGUAGE sql
AS $$
  SELECT 
    m.id,
    m.g_code,
    v.sold_date_last,
    v.received_date_last
  FROM t_mps m
  INNER JOIN v_stats_by_mps v ON m.id = v.mps_id
  WHERE m.active = true 
    AND v.discs_in_stock_total = 0
  ORDER BY m.id ASC;
$$;

-- Grant execute permission to the service role
GRANT EXECUTE ON FUNCTION get_mps_review_data() TO service_role;
