import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkTask() {
  try {
    console.log('🔍 Investigating task 537062...\n');
    
    // Get the specific task details
    const { data: task, error } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 537062)
      .single();
    
    if (error) {
      console.error('❌ Error fetching task:', error);
      return;
    }
    
    if (!task) {
      console.log('❌ Task 537062 not found');
      return;
    }
    
    console.log('📋 Task Details:');
    console.log('ID:', task.id);
    console.log('Task Type:', task.task_type);
    console.log('Status:', task.status);
    console.log('Created At:', task.created_at);
    console.log('Scheduled At:', task.scheduled_at);
    console.log('Processed At:', task.processed_at);
    console.log('Locked At:', task.locked_at);
    console.log('Locked By:', task.locked_by);
    console.log('Parameters:', JSON.stringify(task.parameters, null, 2));
    console.log('Result:', JSON.stringify(task.result, null, 2));
    
    // Check if there are other verify_t_images_image tasks
    const { data: similarTasks, error: similarError } = await supabase
      .from('t_task_queue')
      .select('id, status, created_at, processed_at, locked_at, locked_by')
      .eq('task_type', 'verify_t_images_image')
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (!similarError && similarTasks) {
      console.log('\n📊 Recent verify_t_images_image tasks:');
      for (const t of similarTasks) {
        console.log(`   - Task ${t.id}: ${t.status} (created: ${t.created_at}, processed: ${t.processed_at || 'null'}, locked: ${t.locked_at || 'null'})`);
      }
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

checkTask();
