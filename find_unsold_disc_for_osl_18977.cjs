require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function findUnsoldDiscForOsl18977() {
  try {
    console.log('Finding unsold discs that should match OSL 18977...');
    
    // OSL 18977: MPS 15400, Weight 100-129g, Color 23 (any color)
    const oslId = 18977;
    const mpsId = 15400;
    const minWeight = 100;
    const maxWeight = 129;
    const colorId = 23; // any color
    
    console.log(`\nSearching for unsold discs with MPS ${mpsId}, weight ${minWeight}-${maxWeight}g, any color...`);
    
    // Find unsold discs that should match using regular weight
    const { data: regularMatches, error: regError } = await supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .eq('mps_id', mpsId)
      .gte('weight', minWeight)
      .lte('weight', maxWeight)
      .is('sold_date', null)  // Unsold only
      .limit(10);
    
    if (regError) {
      console.error('Error finding regular matches:', regError);
    } else {
      console.log(`\n=== REGULAR WEIGHT MATCHES (${regularMatches.length} found) ===`);
      regularMatches.forEach(disc => {
        console.log(`Disc ${disc.id}: Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id}, Regular OSL: ${disc.order_sheet_line_id}, Vendor OSL: ${disc.vendor_osl_id}`);
      });
    }
    
    // Find unsold discs that should match using manufacturer weight
    const { data: vendorCandidates, error: vendorError } = await supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .eq('mps_id', mpsId)
      .not('weight_mfg', 'is', null)
      .is('sold_date', null)  // Unsold only
      .limit(20);
    
    if (vendorError) {
      console.error('Error finding vendor candidates:', vendorError);
    } else {
      // Filter by rounded manufacturer weight
      const vendorMatches = vendorCandidates.filter(disc => {
        const roundedWeightMfg = Math.round(disc.weight_mfg);
        return roundedWeightMfg >= minWeight && roundedWeightMfg <= maxWeight;
      });
      
      console.log(`\n=== VENDOR WEIGHT MATCHES (${vendorMatches.length} found) ===`);
      vendorMatches.forEach(disc => {
        const roundedWeightMfg = Math.round(disc.weight_mfg);
        console.log(`Disc ${disc.id}: Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g (rounded: ${roundedWeightMfg}g), Color ${disc.color_id}, Regular OSL: ${disc.order_sheet_line_id}, Vendor OSL: ${disc.vendor_osl_id}`);
      });
    }
    
    // Check if there are any unsold discs with this MPS at all
    const { data: allUnsoldMps, error: allError } = await supabase
      .from('t_discs')
      .select('id, weight, weight_mfg, color_id, sold_date')
      .eq('mps_id', mpsId)
      .is('sold_date', null)
      .limit(10);
    
    if (!allError) {
      console.log(`\n=== ALL UNSOLD DISCS WITH MPS ${mpsId} (${allUnsoldMps.length} found) ===`);
      allUnsoldMps.forEach(disc => {
        const regularInRange = disc.weight >= minWeight && disc.weight <= maxWeight;
        const vendorInRange = disc.weight_mfg ? Math.round(disc.weight_mfg) >= minWeight && Math.round(disc.weight_mfg) <= maxWeight : false;
        console.log(`Disc ${disc.id}: Weight ${disc.weight}g${regularInRange ? ' ✅' : ''}, Weight MFG ${disc.weight_mfg}g${vendorInRange ? ' ✅' : ''}, Color ${disc.color_id}`);
      });
    }
    
    // Summary
    const regularCount = !regError && regularMatches ? regularMatches.length : 0;
    const vendorCount = !vendorError && vendorCandidates ? vendorCandidates.filter(disc => {
      const roundedWeightMfg = Math.round(disc.weight_mfg);
      return roundedWeightMfg >= minWeight && roundedWeightMfg <= maxWeight;
    }).length : 0;
    
    console.log(`\n=== SUMMARY ===`);
    console.log(`OSL 18977 should match:`);
    console.log(`- ${regularCount} unsold discs using regular weight`);
    console.log(`- ${vendorCount} unsold discs using manufacturer weight`);
    
    if (regularCount === 0 && vendorCount === 0) {
      console.log('\n✅ The match_osl_to_discs task result was CORRECT!');
      console.log('There are no unsold discs that should match OSL 18977.');
      console.log('Disc 404583 is sold, so it correctly was not matched.');
    } else {
      console.log('\n🎯 The match_osl_to_discs task should have found matches!');
      console.log('This indicates the dual mapping logic needs to be tested with these discs.');
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

findUnsoldDiscForOsl18977();
