-- Trigger for t_mps.order_through_mps_id changes
-- When order_through_mps_id is updated (added, changed, or deleted), 
-- enqueue a task to update all related discs

-- Function to enqueue a task when order_through_mps_id changes
CREATE OR REPLACE FUNCTION fn_queue_order_through_mps_id_updated()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if order_through_mps_id was actually changed
    IF (OLD.order_through_mps_id IS DISTINCT FROM NEW.order_through_mps_id) THEN
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at,
            enqueued_by
        ) VALUES (
            'order_through_mps_id_updated',
            jsonb_build_object('id', NEW.id),
            'pending',
            NOW(),
            NOW(),
            'trigger_order_through_mps_id_updated'
        );
        
        -- Log the change for debugging
        RAISE NOTICE 'Enqueued order_through_mps_id_updated task for MPS ID %, old value: %, new value: %', 
            NEW.id, 
            COALESCE(OLD.order_through_mps_id::text, 'NULL'), 
            COALESCE(NEW.order_through_mps_id::text, 'NULL');
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the trigger if it exists
DROP TRIGGER IF EXISTS trg_queue_order_through_mps_id_updated ON t_mps;

-- Create the trigger
CREATE TRIGGER trg_queue_order_through_mps_id_updated
AFTER UPDATE OF order_through_mps_id
ON t_mps
FOR EACH ROW
EXECUTE FUNCTION fn_queue_order_through_mps_id_updated();

-- Test the trigger with a comment
-- To test: UPDATE t_mps SET order_through_mps_id = 12345 WHERE id = 16912;
