import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function applyFinalParsingRules() {
  try {
    console.log('Applying final parsing rules...\n');

    // Handle new brand special cases
    await handleNewBrandCases();

    // Handle pack listings (2 pack, 3 pack, etc.)
    await handlePackListings();

    // Remove additional filler text
    await removeFinalFillerText();

    console.log('\nFinal parsing rules applied successfully!');

  } catch (error) {
    console.error('Error:', error);
  }
}

async function handleNewBrandCases() {
  console.log('Handling new brand special cases...');

  const newBrandCases = [
    { contains: 'Dino Discs', replacement: 'XXXX Dino Discs' }
  ];

  for (const brandCase of newBrandCases) {
    // Get records that contain this brand and don't start with XXXX
    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .like('notes', `%${brandCase.contains}%`)
      .not('notes', 'like', 'XXXX%');

    if (fetchError) {
      console.error(`Error fetching records for ${brandCase.contains}:`, fetchError);
      continue;
    }

    let brandUpdated = 0;

    for (const record of matchingRecords || []) {
      const updateData = {
        notes: brandCase.replacement
      };

      // Store original notes in raw_notes if not already stored
      if (!record.raw_notes) {
        updateData.raw_notes = record.notes;
      }

      const { error } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', record.id);

      if (error) {
        console.error(`Error updating record ${record.id}:`, error);
      } else {
        brandUpdated++;
        if (brandUpdated <= 5) { // Show first 5 updates
          console.log(`  Updated ID ${record.id}: ${brandCase.replacement}`);
        }
      }
    }

    console.log(`Updated ${brandUpdated} records containing "${brandCase.contains}"`);
  }

  console.log('New brand special cases handled.\n');
}

async function handlePackListings() {
  console.log('Handling pack listings (2 pack, 3 pack, etc.)...');

  // Pattern to match pack listings: number + "pack" or "Pack"
  const packPatterns = [
    '\\d+\\s*pack',
    '\\d+\\s*Pack',
    '\\d+-pack',
    '\\d+-Pack'
  ];

  let totalPackUpdates = 0;

  for (const pattern of packPatterns) {
    console.log(`Processing pattern "${pattern}"...`);

    // Get records that match this pack pattern and don't start with XXXX
    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .not('notes', 'like', 'XXXX%');

    if (fetchError) {
      console.error(`Error fetching records for pattern "${pattern}":`, fetchError);
      continue;
    }

    let patternUpdated = 0;

    for (const record of matchingRecords || []) {
      const regex = new RegExp(pattern, 'i');
      if (regex.test(record.notes)) {
        const updateData = {
          notes: 'XXXX Pack'
        };

        // Store original notes in raw_notes if not already stored
        if (!record.raw_notes) {
          updateData.raw_notes = record.notes;
        }

        const { error } = await supabase
          .from('t_sdasins')
          .update(updateData)
          .eq('id', record.id);

        if (error) {
          console.error(`Error updating record ${record.id}:`, error);
        } else {
          patternUpdated++;
          totalPackUpdates++;
          if (patternUpdated <= 3) { // Show first 3 updates per pattern
            console.log(`  Updated ID ${record.id}: XXXX Pack`);
          }
        }
      }
    }

    console.log(`  Updated ${patternUpdated} records with pattern "${pattern}"`);
  }

  console.log(`Total pack listing updates: ${totalPackUpdates}\n`);
}

async function removeFinalFillerText() {
  console.log('Removing final filler text from existing records...');

  // Final filler phrases to remove (longest to shortest)
  const finalFillerPhrases = [
    'Straight-Flying & Lightweight Frisbee Golf Putter',
    'Great Disc Golf Disc for Beginners',
    'Beginner Friendly Frisbee Golf Disc',
    'Putt and Approach Disc Golf Disc',
    'Made for Putt & Approach Shots',
    'Mid-Range Golf Disc',
    'Extremely Durable',
    'Understable'
  ];

  let totalUpdated = 0;

  for (const phrase of finalFillerPhrases) {
    console.log(`Removing "${phrase}"...`);

    // Get records that contain this phrase
    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .like('notes', `%${phrase}%`)
      .not('notes', 'like', 'XXXX%'); // Skip already processed records

    if (fetchError) {
      console.error(`Error fetching records for "${phrase}":`, fetchError);
      continue;
    }

    let phraseUpdated = 0;

    for (const record of matchingRecords || []) {
      // Remove the phrase from notes
      const updatedNotes = record.notes.replace(new RegExp(escapeRegex(phrase), 'gi'), '').trim();
      
      // Clean up extra spaces and punctuation
      const cleanedNotes = updatedNotes
        .replace(/\s+/g, ' ')
        .replace(/^\s*[-|,]\s*/, '')
        .replace(/\s*[-|,]\s*$/, '')
        .trim();

      const updateData = {
        notes: cleanedNotes
      };

      // Store original notes in raw_notes if not already stored
      if (!record.raw_notes) {
        updateData.raw_notes = record.notes;
      }

      const { error } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', record.id);

      if (error) {
        console.error(`Error updating record ${record.id}:`, error);
      } else {
        phraseUpdated++;
        totalUpdated++;
        if (phraseUpdated <= 3) { // Show first 3 updates per phrase
          console.log(`  Updated ID ${record.id}: removed "${phrase}"`);
        }
      }
    }

    console.log(`  Removed from ${phraseUpdated} records`);
  }

  console.log(`\nTotal records updated with final filler text removal: ${totalUpdated}`);
}

function escapeRegex(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Run the script
applyFinalParsingRules();
