-- Fix the fn_enqueue_image_verification_task function
CREATE OR REPLACE FUNCTION fn_enqueue_image_verification_task()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  INSERT INTO t_task_queue (task_type, payload, status, scheduled_at, created_at, enqueued_by)
  VALUES (
    'verify_t_images_image',
    json_build_object('id', NEW.id), -- Remove the ::text cast to keep it as JSONB
    'pending',
    NOW() + INTERVAL '1 minute',
    NOW(),
    't_images insert_trigger_' || NEW.id
  );
  RETURN NEW;
END;
$$;
