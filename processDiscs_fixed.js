// processDiscs_fixed.js
// Script to process t_discs records with null image_verified_notes
// For each disc, either mark it as "Nothing to migrate" or copy data from matching t_images record
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Create a Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('=== PROCESSING DISCS WITH NULL IMAGE_VERIFIED_NOTES ===');
    
    let batchNumber = 1;
    let totalProcessed = 0;
    let totalMigrated = 0;
    let totalMarkedNoMigration = 0;
    let hasMoreRecords = true;
    
    while (hasMoreRecords) {
      console.log(`\n--- Processing Batch ${batchNumber} ---`);
      
      // Step 1: Get up to 500 discs with null image_verified_notes
      console.log('Finding discs with null image_verified_notes...');
      const { data: discsWithNullNotes, error: discsError } = await supabase
        .from('t_discs')
        .select('id')
        .is('image_verified_notes', null)
        .limit(500);
        
      if (discsError) {
        console.error(`Error fetching discs: ${discsError.message}`);
        break;
      }
      
      if (!discsWithNullNotes || discsWithNullNotes.length === 0) {
        console.log('No more discs with null notes found. Done!');
        hasMoreRecords = false;
        break;
      }
      
      console.log(`Found ${discsWithNullNotes.length} discs with null notes.`);
      
      // Reset batch counters
      let batchMigrated = 0;
      let batchMarkedNoMigration = 0;
      let batchProcessed = 0;
      
      // Process each disc individually
      for (const disc of discsWithNullNotes) {
        // Step 2: Check if there's a matching t_images record
        const { data: matchingImages, error: imagesError } = await supabase
          .from('t_images')
          .select('*')
          .eq('table_name', 't_discs')
          .eq('record_id', disc.id);
          
        if (imagesError) {
          console.error(`Error fetching matching images for disc ${disc.id}: ${imagesError.message}`);
          continue;
        }
        
        if (!matchingImages || matchingImages.length === 0) {
          // No matching t_images record - mark as "Nothing to migrate"
          const { error: updateError } = await supabase
            .from('t_discs')
            .update({ image_verified_notes: 'Nothing to migrate' })
            .eq('id', disc.id);
            
          if (updateError) {
            console.error(`Error marking disc ${disc.id}: ${updateError.message}`);
          } else {
            batchMarkedNoMigration++;
            totalMarkedNoMigration++;
          }
        } else {
          // Found matching t_images record(s) - use the most recent one
          const mostRecentImage = matchingImages.reduce((latest, current) => {
            return new Date(current.updated_at) > new Date(latest.updated_at) ? current : latest;
          }, matchingImages[0]);
          
          // Prepare the migration note
          const migrationNote = `Migrated from t_images on ${new Date().toISOString()}`;
          const combinedNotes = mostRecentImage.image_verified_notes 
            ? `${mostRecentImage.image_verified_notes}\n${migrationNote}` 
            : migrationNote;
          
          // Update the disc with data from t_images
          const { error: updateError } = await supabase
            .from('t_discs')
            .update({
              image_verified: mostRecentImage.image_verified,
              image_verified_at: mostRecentImage.image_verified_at,
              image_verified_by: mostRecentImage.updated_by,
              image_verified_notes: combinedNotes
            })
            .eq('id', disc.id);
            
          if (updateError) {
            console.error(`Error updating disc ${disc.id}: ${updateError.message}`);
          } else {
            batchMigrated++;
            totalMigrated++;
          }
        }
        
        batchProcessed++;
        totalProcessed++;
        
        // Log progress every 50 records
        if (batchProcessed % 50 === 0) {
          console.log(`Progress: ${batchProcessed}/${discsWithNullNotes.length} discs processed in current batch.`);
        }
      }
      
      console.log(`Batch ${batchNumber} complete: ${batchProcessed} discs processed.`);
      console.log(`- ${batchMigrated} discs migrated from t_images`);
      console.log(`- ${batchMarkedNoMigration} discs marked as "Nothing to migrate"`);
      
      batchNumber++;
      
      // If we got fewer than 500 records, we're done
      if (discsWithNullNotes.length < 500) {
        hasMoreRecords = false;
      }
    }
    
    console.log(`\n=== PROCESSING COMPLETE ===`);
    console.log(`Total discs processed: ${totalProcessed}`);
    console.log(`- ${totalMigrated} discs migrated from t_images`);
    console.log(`- ${totalMarkedNoMigration} discs marked as "Nothing to migrate"`);
    
    // Final check
    const { count, error: countError } = await supabase
      .from('t_discs')
      .select('id', { count: 'exact', head: true })
      .is('image_verified_notes', null);
      
    if (countError) {
      console.error(`Error checking remaining discs: ${countError.message}`);
    } else {
      console.log(`Remaining discs with null image_verified_notes: ${count || 0}`);
    }
    
  } catch (err) {
    console.error(`Unexpected error: ${err.message}`);
    console.error(err.stack);
  }
}

main();
