// processDeleteVariantFromShopifyTask.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Shopify credentials
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

if (!shopifyEndpoint || !shopifyAccessToken) {
  console.error('ERROR: Missing Shopify endpoint or access token in environment variables.');
  process.exit(1);
}

/**
 * Execute a GraphQL request to Shopify
 * @param {string} query - The GraphQL query or mutation
 * @param {Object} variables - Variables for the GraphQL query
 * @returns {Promise<Object>} - The response data
 */
async function executeShopifyGraphQL(query, variables = {}) {
  try {
    const response = await fetch(shopifyEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      },
      body: JSON.stringify({ query, variables })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();

    if (result.errors) {
      throw new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`);
    }

    return result.data;
  } catch (error) {
    console.error('Error executing Shopify GraphQL request:', error);
    throw error;
  }
}

/**
 * Find a product variant by SKU using GraphQL
 * @param {string} sku - The SKU to search for
 * @returns {Promise<Object|null>} - The variant and product information or null if not found
 */
async function findVariantBySku(sku) {
  try {
    console.log(`[processDeleteVariantFromShopifyTask] Finding variant by SKU: ${sku}`);

    const query = `
      query getVariantBySku($query: String!) {
        productVariants(first: 1, query: $query) {
          edges {
            node {
              id
              sku
              product {
                id
                title
                handle
                variants(first: 10) {
                  edges {
                    node {
                      id
                      sku
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;

    const variables = {
      query: `sku:${sku}`
    };

    const result = await executeShopifyGraphQL(query, variables);

    if (!result.productVariants.edges.length) {
      console.log(`[processDeleteVariantFromShopifyTask] No variant found with SKU: ${sku}`);
      return null;
    }

    const variant = result.productVariants.edges[0].node;
    
    console.log(`[processDeleteVariantFromShopifyTask] Found variant ${variant.id} for SKU: ${sku}`);
    console.log(`[processDeleteVariantFromShopifyTask] Product: ${variant.product.title} (${variant.product.id})`);
    console.log(`[processDeleteVariantFromShopifyTask] Product has ${variant.product.variants.edges.length} total variants`);

    return {
      variantId: variant.id,
      sku: variant.sku,
      product: {
        id: variant.product.id,
        title: variant.product.title,
        handle: variant.product.handle,
        totalVariants: variant.product.variants.edges.length,
        allVariants: variant.product.variants.edges.map(edge => ({
          id: edge.node.id,
          sku: edge.node.sku
        }))
      }
    };
  } catch (error) {
    console.error(`[processDeleteVariantFromShopifyTask] Error finding variant by SKU ${sku}:`, error);
    throw error;
  }
}

/**
 * Delete a variant by its ID using REST API, and optionally delete the product if it's the last variant
 * @param {string} variantId - The Shopify variant ID (gid://shopify/ProductVariant/...)
 * @param {string} productId - The Shopify product ID (gid://shopify/Product/...)
 * @param {boolean} isLastVariant - Whether this is the last variant in the product
 * @returns {Promise<Object>} - The deletion result
 */
async function deleteVariant(variantId, productId, isLastVariant = false) {
  try {
    // Extract numeric IDs from GraphQL IDs
    const numericVariantId = variantId.split('/').pop();
    const numericProductId = productId.split('/').pop();

    // Extract the base URL for REST API calls
    const baseUrl = shopifyEndpoint.replace('/admin/api/2024-01/graphql.json', '');

    console.log(`[processDeleteVariantFromShopifyTask] Deleting variant: ${numericVariantId} from product: ${numericProductId}`);

    // Use REST API for variant deletion
    const variantEndpoint = `${baseUrl}/admin/api/2024-01/products/${numericProductId}/variants/${numericVariantId}.json`;

    console.log(`[processDeleteVariantFromShopifyTask] Variant deletion URL: ${variantEndpoint}`);

    const variantResponse = await fetch(variantEndpoint, {
      method: 'DELETE',
      headers: {
        'X-Shopify-Access-Token': shopifyAccessToken
      }
    });

    if (!variantResponse.ok) {
      const errorText = await variantResponse.text();
      throw new Error(`Shopify REST API error deleting variant (${variantResponse.status}): ${errorText}`);
    }

    console.log(`[processDeleteVariantFromShopifyTask] Successfully deleted variant: ${numericVariantId}`);

    let productDeleted = false;
    let productDeletionError = null;

    // If this was the last variant, also delete the product to avoid empty product shells
    if (isLastVariant) {
      try {
        console.log(`[processDeleteVariantFromShopifyTask] This was the last variant. Waiting 2 seconds then deleting product: ${numericProductId} to avoid empty product shell`);

        // Wait a moment for Shopify to process the variant deletion
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Try GraphQL first (proven to work)
        console.log(`[processDeleteVariantFromShopifyTask] Attempting product deletion via GraphQL`);

        try {
          const graphqlMutation = `
            mutation productDelete($input: ProductDeleteInput!) {
              productDelete(input: $input) {
                deletedProductId
                userErrors {
                  field
                  message
                }
              }
            }
          `;

          const graphqlVariables = {
            input: {
              id: productId // Use the full GraphQL ID
            }
          };

          const graphqlResult = await executeShopifyGraphQL(graphqlMutation, graphqlVariables);

          if (graphqlResult.productDelete.userErrors.length > 0) {
            const errors = graphqlResult.productDelete.userErrors.map(e => `${e.field}: ${e.message}`).join(', ');
            productDeletionError = `GraphQL product deletion errors: ${errors}`;
            console.error(`[processDeleteVariantFromShopifyTask] ${productDeletionError}`);
          } else if (graphqlResult.productDelete.deletedProductId) {
            productDeleted = true;
            console.log(`[processDeleteVariantFromShopifyTask] Successfully deleted product via GraphQL: ${graphqlResult.productDelete.deletedProductId}`);
          } else {
            productDeletionError = `GraphQL product deletion returned no result`;
            console.error(`[processDeleteVariantFromShopifyTask] ${productDeletionError}`);
          }
        } catch (graphqlError) {
          console.error(`[processDeleteVariantFromShopifyTask] GraphQL deletion failed: ${graphqlError.message}`);

          // Fallback to REST API
          console.log(`[processDeleteVariantFromShopifyTask] Trying REST API as fallback`);

          try {
            const productEndpoint = `${baseUrl}/admin/api/2024-01/products/${numericProductId}.json`;
            console.log(`[processDeleteVariantFromShopifyTask] REST API URL: ${productEndpoint}`);

            const productResponse = await fetch(productEndpoint, {
              method: 'DELETE',
              headers: {
                'X-Shopify-Access-Token': shopifyAccessToken
              }
            });

            console.log(`[processDeleteVariantFromShopifyTask] REST API response status: ${productResponse.status}`);

            if (!productResponse.ok) {
              const errorText = await productResponse.text();
              productDeletionError = `Both GraphQL and REST failed. GraphQL: ${graphqlError.message}, REST: ${errorText}`;
              console.error(`[processDeleteVariantFromShopifyTask] ${productDeletionError}`);
            } else {
              productDeleted = true;
              console.log(`[processDeleteVariantFromShopifyTask] Successfully deleted product via REST API: ${numericProductId}`);
            }
          } catch (restError) {
            productDeletionError = `Both GraphQL and REST failed. GraphQL: ${graphqlError.message}, REST: ${restError.message}`;
            console.error(`[processDeleteVariantFromShopifyTask] ${productDeletionError}`);
          }
        }
      } catch (error) {
        productDeletionError = `Exception deleting product: ${error.message}`;
        console.error(`[processDeleteVariantFromShopifyTask] ${productDeletionError}`);
        console.error(`[processDeleteVariantFromShopifyTask] Full error:`, error);
      }
    }

    return {
      success: true,
      deletedVariantId: variantId,
      productId: productId,
      wasLastVariant: isLastVariant,
      productDeleted: productDeleted,
      productDeletionError: productDeletionError
    };
  } catch (error) {
    console.error(`[processDeleteVariantFromShopifyTask] Error deleting variant ${variantId}:`, error);
    throw error;
  }
}

/**
 * Process a delete_variant_from_shopify task
 * @param {Object} task - The task object from the queue
 * @param {Object} context - Context object with supabase, updateTaskStatus, and logError functions
 */
export default async function processDeleteVariantFromShopifyTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processDeleteVariantFromShopifyTask] Processing task ${task.id} to delete variant from Shopify`);

  try {
    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Extract payload
    const payload = task.payload;
    if (!payload || !payload.sku) {
      throw new Error('Task payload must contain a SKU to delete');
    }

    const { sku, reason } = payload;
    
    console.log(`[processDeleteVariantFromShopifyTask] Deleting Shopify variant for SKU: ${sku}`);
    if (reason) {
      console.log(`[processDeleteVariantFromShopifyTask] Deletion reason: ${reason}`);
    }

    // Step 1: Find the variant by SKU
    const variantInfo = await findVariantBySku(sku);
    
    if (!variantInfo) {
      // SKU not found in Shopify - this might be expected
      console.log(`[processDeleteVariantFromShopifyTask] SKU ${sku} not found in Shopify - nothing to delete`);
      
      await updateTaskStatus(task.id, 'completed', {
        message: `SKU ${sku} not found in Shopify - nothing to delete`,
        sku: sku,
        found: false,
        reason: reason || 'No reason provided'
      });
      return;
    }

    // Step 2: Check if this is the only variant in the product
    const isLastVariant = variantInfo.product.totalVariants === 1;
    if (isLastVariant) {
      console.log(`[processDeleteVariantFromShopifyTask] SKU ${sku} is the only variant in product ${variantInfo.product.title}. Will delete the entire product to avoid empty shell.`);
    } else {
      console.log(`[processDeleteVariantFromShopifyTask] Product has ${variantInfo.product.totalVariants} variants. Deleting variant will leave ${variantInfo.product.totalVariants - 1} variants.`);
    }

    // Step 3: Delete the variant (and product if it's the last variant)
    const deletionResult = await deleteVariant(variantInfo.variantId, variantInfo.product.id, isLastVariant);
    
    // Step 4: Mark task as completed
    const completionMessage = deletionResult.productDeleted
      ? `Successfully deleted variant and product for SKU ${sku} from Shopify`
      : deletionResult.wasLastVariant && deletionResult.productDeletionError
        ? `Successfully deleted variant for SKU ${sku} from Shopify, but failed to delete empty product: ${deletionResult.productDeletionError}`
        : `Successfully deleted variant for SKU ${sku} from Shopify`;

    await updateTaskStatus(task.id, 'completed', {
      message: completionMessage,
      sku: sku,
      deletedVariantId: deletionResult.deletedVariantId,
      productId: deletionResult.productId,
      productTitle: variantInfo.product.title,
      productHandle: variantInfo.product.handle,
      wasOnlyVariant: deletionResult.wasLastVariant,
      productDeleted: deletionResult.productDeleted,
      productDeletionError: deletionResult.productDeletionError,
      remainingVariants: deletionResult.wasLastVariant ? 0 : variantInfo.product.totalVariants - 1,
      reason: reason || 'No reason provided'
    });

    console.log(`[processDeleteVariantFromShopifyTask] Successfully completed variant deletion task for SKU: ${sku}`);

  } catch (error) {
    const errorMessage = `Failed to delete variant for SKU ${task.payload?.sku || 'unknown'} from Shopify: ${error.message}`;
    console.error(`[processDeleteVariantFromShopifyTask] ${errorMessage}`);
    
    await logError(errorMessage, `Processing delete_variant_from_shopify task ${task.id}`);
    
    await updateTaskStatus(task.id, 'error', {
      message: errorMessage,
      error: error.message,
      sku: task.payload?.sku || 'unknown',
      reason: task.payload?.reason || 'No reason provided'
    });
  }
}

/**
 * Standalone function to delete a variant from Shopify by SKU
 * @param {string} sku - The SKU to delete
 * @param {string} reason - Optional reason for deletion
 * @returns {Promise<Object>} - The deletion result
 */
export async function deleteVariantFromShopify(sku, reason = null) {
  try {
    console.log(`[deleteVariantFromShopify] Deleting Shopify variant for SKU: ${sku}`);
    if (reason) {
      console.log(`[deleteVariantFromShopify] Deletion reason: ${reason}`);
    }

    // Step 1: Find the variant by SKU
    const variantInfo = await findVariantBySku(sku);

    if (!variantInfo) {
      // SKU not found in Shopify - this might be expected
      console.log(`[deleteVariantFromShopify] SKU ${sku} not found in Shopify - nothing to delete`);

      return {
        success: true,
        found: false,
        message: `SKU ${sku} not found in Shopify - nothing to delete`,
        sku: sku,
        reason: reason || 'No reason provided'
      };
    }

    // Step 2: Check if this is the only variant in the product
    const isLastVariant = variantInfo.product.totalVariants === 1;
    if (isLastVariant) {
      console.log(`[deleteVariantFromShopify] SKU ${sku} is the only variant in product ${variantInfo.product.title}. Will delete the entire product to avoid empty shell.`);
    } else {
      console.log(`[deleteVariantFromShopify] Product has ${variantInfo.product.totalVariants} variants. Deleting variant will leave ${variantInfo.product.totalVariants - 1} variants.`);
    }

    // Step 3: Delete the variant (and product if it's the last variant)
    const deletionResult = await deleteVariant(variantInfo.variantId, variantInfo.product.id, isLastVariant);

    console.log(`[deleteVariantFromShopify] Successfully deleted variant for SKU: ${sku}`);
    if (deletionResult.productDeleted) {
      console.log(`[deleteVariantFromShopify] Also deleted product ${variantInfo.product.title} to avoid empty shell`);
    }

    const completionMessage = deletionResult.productDeleted
      ? `Successfully deleted variant and product for SKU ${sku} from Shopify`
      : deletionResult.wasLastVariant && deletionResult.productDeletionError
        ? `Successfully deleted variant for SKU ${sku} from Shopify, but failed to delete empty product: ${deletionResult.productDeletionError}`
        : `Successfully deleted variant for SKU ${sku} from Shopify`;

    return {
      success: true,
      found: true,
      message: completionMessage,
      sku: sku,
      deletedVariantId: deletionResult.deletedVariantId,
      productId: deletionResult.productId,
      productTitle: variantInfo.product.title,
      productHandle: variantInfo.product.handle,
      wasOnlyVariant: deletionResult.wasLastVariant,
      productDeleted: deletionResult.productDeleted,
      productDeletionError: deletionResult.productDeletionError,
      remainingVariants: deletionResult.wasLastVariant ? 0 : variantInfo.product.totalVariants - 1,
      reason: reason || 'No reason provided'
    };

  } catch (error) {
    const errorMessage = `Failed to delete variant for SKU ${sku} from Shopify: ${error.message}`;
    console.error(`[deleteVariantFromShopify] ${errorMessage}`);

    return {
      success: false,
      found: null,
      error: error.message,
      message: errorMessage,
      sku: sku,
      reason: reason || 'No reason provided'
    };
  }
}

/**
 * Helper function to enqueue a delete variant from Shopify task
 * @param {Object} supabase - Supabase client
 * @param {string} sku - The SKU to delete
 * @param {string} reason - Optional reason for deletion
 * @param {Date} scheduledAt - Optional scheduled time (defaults to 5 minutes from now)
 */
export async function enqueueDeleteVariantFromShopifyTask(supabase, sku, reason = null, scheduledAt = null) {
  if (!sku) {
    throw new Error('SKU is required to enqueue delete variant from Shopify task');
  }
  
  const now = new Date();
  const defaultScheduledAt = new Date(now.getTime() + 5 * 60 * 1000); // 5 minutes from now
  
  const task = {
    task_type: 'delete_variant_from_shopify',
    payload: {
      sku: sku,
      reason: reason || 'Scheduled variant deletion'
    },
    status: 'pending',
    scheduled_at: (scheduledAt || defaultScheduledAt).toISOString(),
    created_at: now.toISOString(),
    enqueued_by: 'enqueueDeleteVariantFromShopifyTask'
  };
  
  console.log(`[enqueueDeleteVariantFromShopifyTask] Enqueueing delete variant from Shopify task for SKU: ${sku}`);
  
  const { data, error } = await supabase
    .from('t_task_queue')
    .insert(task)
    .select()
    .single();
  
  if (error) {
    console.error(`[enqueueDeleteVariantFromShopifyTask] Error enqueueing task:`, error);
    throw error;
  }
  
  console.log(`[enqueueDeleteVariantFromShopifyTask] Successfully enqueued task ${data.id} for SKU: ${sku}`);
  return data;
}
