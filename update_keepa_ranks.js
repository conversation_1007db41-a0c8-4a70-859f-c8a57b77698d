import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Keepa API configuration
const KEEPA_API_KEY = 'urmlcoc8brbp56celtr1i100gqmo43to7a0q9ospldotdfl5k8idmf6tbt8g7al1';
const KEEPA_API_BASE_URL = 'https://api.keepa.com';
const CATEGORY_ID = '3406051'; // Remove the domain prefix, just use the category number
const DOMAIN_ID = 1; // US domain

/**
 * Fetch Best Sellers data from Keepa API
 * @returns {Promise<string[]>} Array of ASINs ordered by sales rank
 */
async function fetchKeepabestSellers() {
  const url = `${KEEPA_API_BASE_URL}/bestsellers/?key=${KEEPA_API_KEY}&domain=${DOMAIN_ID}&category=${CATEGORY_ID}`;
  
  console.log('Fetching Best Sellers data from Keepa API...');
  console.log('URL:', url);
  
  try {
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
    }
    
    const data = await response.json();
    
    // Log the response structure for debugging
    console.log('Keepa API response structure:', Object.keys(data));
    console.log('Full response:', JSON.stringify(data, null, 2));

    // The response should contain a bestSellersList object with asinList
    if (data.bestSellersList && data.bestSellersList.asinList && Array.isArray(data.bestSellersList.asinList)) {
      console.log(`Received ${data.bestSellersList.asinList.length} ASINs from Keepa API`);
      console.log(`Category ID in response: ${data.bestSellersList.categoryId}`);
      console.log(`Domain ID in response: ${data.bestSellersList.domainId}`);
      return data.bestSellersList.asinList;
    } else if (data.asinList && Array.isArray(data.asinList)) {
      console.log(`Received ${data.asinList.length} ASINs from Keepa API (direct asinList)`);
      return data.asinList;
    } else if (Array.isArray(data)) {
      console.log(`Received ${data.length} ASINs from Keepa API (direct array)`);
      return data;
    } else {
      console.error('Unexpected response format from Keepa API. Expected bestSellersList.asinList');
      console.error('Available fields:', Object.keys(data));
      if (data.bestSellersList) {
        console.error('bestSellersList fields:', Object.keys(data.bestSellersList));
      }
      throw new Error('Unexpected response format from Keepa API');
    }
  } catch (error) {
    console.error('Error fetching data from Keepa API:', error);
    throw error;
  }
}

/**
 * Update sales ranks in the database
 * @param {string[]} asinList - Array of ASINs ordered by sales rank
 */
async function updateSalesRanks(asinList) {
  console.log('Starting database updates...');
  
  const currentTimestamp = new Date().toISOString();
  let updatedCount = 0;
  let notFoundCount = 0;
  let newAsinCount = 0;
  
  // Process ASINs in batches to avoid overwhelming the database
  const batchSize = 50;
  
  for (let i = 0; i < asinList.length; i += batchSize) {
    const batch = asinList.slice(i, i + batchSize);
    console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(asinList.length / batchSize)} (ASINs ${i + 1}-${Math.min(i + batchSize, asinList.length)})`);
    
    // Create updates for this batch
    const updates = batch.map((asin, batchIndex) => {
      const rank = i + batchIndex + 1; // Position in the overall list (1-based)
      return {
        asin: asin,
        rank: rank,
        timestamp: currentTimestamp
      };
    });
    
    // Update each ASIN in the batch
    for (const update of updates) {
      try {
        const { data, error } = await supabase
          .from('t_sdasins')
          .update({
            so_rank_30day_avg: update.rank,
            so_rank_30day_avg_date: update.timestamp
          })
          .eq('asin', update.asin)
          .select('id, asin');
        
        if (error) {
          console.error(`Error updating ASIN ${update.asin}:`, error);
          continue;
        }
        
        if (data && data.length > 0) {
          updatedCount++;
          console.log(`Updated ASIN ${update.asin} with rank ${update.rank}`);
        } else {
          // ASIN not found - try to add as new competitor product
          const newAsinResult = await createNewCompetitorAsin(update);
          if (newAsinResult.success) {
            newAsinCount++;
            console.log(`Added new competitor ASIN ${update.asin} with rank ${update.rank}`);
          } else {
            notFoundCount++;
            console.log(`ASIN ${update.asin} not found in t_sdasins table (failed to add: ${newAsinResult.error})`);
          }
        }
      } catch (err) {
        console.error(`Exception updating ASIN ${update.asin}:`, err);
      }
    }
    
    // Small delay between batches to be gentle on the database
    if (i + batchSize < asinList.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  console.log('\nUpdate Summary:');
  console.log(`Total ASINs processed: ${asinList.length}`);
  console.log(`Successfully updated: ${updatedCount}`);
  console.log(`New competitor ASINs added: ${newAsinCount}`);
  console.log(`Not found in database: ${notFoundCount}`);
  console.log(`Update timestamp: ${currentTimestamp}`);
}

/**
 * Fetch product title from Keepa API
 */
async function fetchProductTitle(asin) {
  try {
    const url = `https://api.keepa.com/product?key=${KEEPA_API_KEY}&domain=1&asin=${asin}`;
    const response = await fetch(url);

    if (!response.ok) {
      console.log(`Failed to fetch title for ASIN ${asin}: ${response.status}`);
      return null;
    }

    const data = await response.json();

    if (data.products && data.products.length > 0 && data.products[0].title) {
      return data.products[0].title;
    }

    return null;
  } catch (error) {
    console.log(`Error fetching title for ASIN ${asin}:`, error.message);
    return null;
  }
}

/**
 * Create a new competitor ASIN record in t_sdasins
 * @param {Object} update - ASIN update data with asin, rank, timestamp
 * @returns {Object} - Success/error result
 */
async function createNewCompetitorAsin(update) {
  try {
    // Fetch product title from Keepa
    const title = await fetchProductTitle(update.asin);

    // Use only the title as notes, or a fallback message if title not available
    const notes = title || 'Product title not available';

    // Insert new record with minimal required fields
    const { data, error } = await supabase
      .from('t_sdasins')
      .insert({
        asin: update.asin,
        notes: notes,
        so_rank_30day_avg: update.rank,
        so_rank_30day_avg_date: update.timestamp
      })
      .select('id')
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true, id: data.id, title: title };
  } catch (err) {
    return { success: false, error: err.message };
  }
}

/**
 * Update existing records that have generic Keepa notes with actual product titles
 */
async function updateExistingKeepaRecordsWithTitles() {
  try {
    console.log('\nChecking for existing Keepa records without titles...');

    // Find records with generic Keepa notes that need title updates
    const { data: recordsToUpdate, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, asin, notes')
      .like('notes', 'Competitor product discovered via Keepa%')
      .not('notes', 'like', '%Title:%');

    if (fetchError) {
      console.error('Error fetching records to update:', fetchError);
      return;
    }

    if (!recordsToUpdate || recordsToUpdate.length === 0) {
      console.log('No existing Keepa records found that need title updates.');
      return;
    }

    console.log(`Found ${recordsToUpdate.length} existing Keepa records that need title updates.`);

    let updatedCount = 0;
    let errorCount = 0;

    // Process in batches to avoid overwhelming the API
    for (let i = 0; i < recordsToUpdate.length; i += 10) {
      const batch = recordsToUpdate.slice(i, i + 10);

      for (const record of batch) {
        try {
          // Fetch title from Keepa
          const title = await fetchProductTitle(record.asin);

          if (title) {
            // Update the notes to include the title
            const updatedNotes = record.notes.replace(
              /^(Competitor product discovered via Keepa[^)]*\))/,
              `$1\nTitle: ${title}`
            );

            const { error: updateError } = await supabase
              .from('t_sdasins')
              .update({ notes: updatedNotes })
              .eq('id', record.id);

            if (updateError) {
              console.error(`Error updating ASIN ${record.asin}:`, updateError);
              errorCount++;
            } else {
              console.log(`Updated ASIN ${record.asin} with title: ${title.substring(0, 50)}...`);
              updatedCount++;
            }
          } else {
            console.log(`Could not fetch title for ASIN ${record.asin}`);
          }

          // Small delay to be respectful to the API
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
          console.error(`Error processing ASIN ${record.asin}:`, error);
          errorCount++;
        }
      }

      // Longer delay between batches
      if (i + 10 < recordsToUpdate.length) {
        console.log(`Processed batch ${Math.floor(i/10) + 1}, waiting before next batch...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`\nTitle update summary: ${updatedCount} updated, ${errorCount} errors`);

  } catch (error) {
    console.error('Error in updateExistingKeepaRecordsWithTitles:', error);
  }
}

/**
 * Main function to orchestrate the Keepa rank update process
 */
async function main() {
  console.log('=== Keepa Best Sellers Rank Update ===');
  console.log(`Category ID: ${CATEGORY_ID}`);
  console.log(`Domain ID: ${DOMAIN_ID} (US)`);
  console.log(`Started at: ${new Date().toISOString()}\n`);

  try {
    // Step 1: Fetch Best Sellers data from Keepa API
    const asinList = await fetchKeepabestSellers();

    if (!asinList || asinList.length === 0) {
      console.log('No ASINs received from Keepa API. Exiting.');
      return;
    }

    // Step 2: Update sales ranks in the database
    await updateSalesRanks(asinList);

    console.log('\n=== Update Complete ===');

  } catch (error) {
    console.error('Fatal error in main process:', error);
    process.exit(1);
  }
}

// Run the script
main();
