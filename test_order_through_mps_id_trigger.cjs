require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testOrderThroughMpsIdTrigger() {
  try {
    console.log('Testing order_through_mps_id trigger and worker...');
    
    // Test MPS ID - using 16912 from our previous example
    const testMpsId = 16912;
    
    console.log('\n=== SETUP ===');
    
    // Get current MPS state
    const { data: currentMps, error: mpsError } = await supabase
      .from('t_mps')
      .select('id, order_through_mps_id')
      .eq('id', testMpsId)
      .single();
    
    if (mpsError) {
      console.error('Error getting MPS:', mpsError);
      return;
    }
    
    console.log(`MPS ${testMpsId} current order_through_mps_id: ${currentMps.order_through_mps_id || 'NULL'}`);
    
    // Count discs related to this MPS
    const { data: relatedDiscs, error: discsError } = await supabase
      .from('t_discs')
      .select('id, vendor_osl_id', { count: 'exact' })
      .eq('mps_id', testMpsId);
    
    if (discsError) {
      console.error('Error counting discs:', discsError);
      return;
    }
    
    const discCount = relatedDiscs ? relatedDiscs.length : 0;
    console.log(`Found ${discCount} discs related to MPS ${testMpsId}`);
    
    if (discCount === 0) {
      console.log('❌ No discs found for this MPS. Cannot test trigger.');
      return;
    }
    
    // Show some example discs
    console.log('\nExample discs:');
    relatedDiscs.slice(0, 5).forEach(disc => {
      console.log(`  Disc ${disc.id}: vendor_osl_id = ${disc.vendor_osl_id || 'NULL'}`);
    });
    
    // TEST 1: Set order_through_mps_id to a new value
    console.log('\n=== TEST 1: Setting order_through_mps_id ===');
    
    const newRedirectValue = 19687; // The redirect target from our example
    console.log(`Setting MPS ${testMpsId} order_through_mps_id to ${newRedirectValue}...`);
    
    const { error: updateError1 } = await supabase
      .from('t_mps')
      .update({ order_through_mps_id: newRedirectValue })
      .eq('id', testMpsId);
    
    if (updateError1) {
      console.error('Error updating MPS:', updateError1);
      return;
    }
    
    console.log('✅ MPS updated - trigger should have fired!');
    
    // Check if task was created
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    
    const { data: triggeredTasks1, error: taskError1 } = await supabase
      .from('t_task_queue')
      .select('id, task_type, payload, status, created_at')
      .eq('task_type', 'order_through_mps_id_updated')
      .eq('enqueued_by', 'trigger_order_through_mps_id_updated')
      .gte('created_at', new Date(Date.now() - 60000).toISOString()) // Last minute
      .order('created_at', { ascending: false });
    
    if (taskError1) {
      console.error('Error checking tasks:', taskError1);
    } else {
      console.log(`Found ${triggeredTasks1.length} order_through_mps_id_updated tasks from trigger`);
      if (triggeredTasks1.length > 0) {
        const latestTask = triggeredTasks1[0];
        console.log(`Latest task: ID ${latestTask.id}, payload: ${JSON.stringify(latestTask.payload)}, status: ${latestTask.status}`);
      }
    }
    
    // TEST 2: Change order_through_mps_id to a different value
    console.log('\n=== TEST 2: Changing order_through_mps_id ===');
    
    const anotherRedirectValue = 12345; // Different value
    console.log(`Changing MPS ${testMpsId} order_through_mps_id to ${anotherRedirectValue}...`);
    
    const { error: updateError2 } = await supabase
      .from('t_mps')
      .update({ order_through_mps_id: anotherRedirectValue })
      .eq('id', testMpsId);
    
    if (updateError2) {
      console.error('Error updating MPS:', updateError2);
      return;
    }
    
    console.log('✅ MPS updated again - trigger should have fired again!');
    
    // TEST 3: Remove order_through_mps_id (set to null)
    console.log('\n=== TEST 3: Removing order_through_mps_id ===');
    
    console.log(`Setting MPS ${testMpsId} order_through_mps_id to NULL...`);
    
    const { error: updateError3 } = await supabase
      .from('t_mps')
      .update({ order_through_mps_id: null })
      .eq('id', testMpsId);
    
    if (updateError3) {
      console.error('Error updating MPS:', updateError3);
      return;
    }
    
    console.log('✅ MPS updated to NULL - trigger should have fired again!');
    
    // Check all triggered tasks
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
    
    console.log('\n=== FINAL RESULTS ===');
    
    const { data: allTriggeredTasks, error: allTaskError } = await supabase
      .from('t_task_queue')
      .select('id, task_type, payload, status, created_at, result')
      .eq('task_type', 'order_through_mps_id_updated')
      .eq('enqueued_by', 'trigger_order_through_mps_id_updated')
      .gte('created_at', new Date(Date.now() - 300000).toISOString()) // Last 5 minutes
      .order('created_at', { ascending: false });
    
    if (allTaskError) {
      console.error('Error checking all tasks:', allTaskError);
    } else {
      console.log(`\nTotal order_through_mps_id_updated tasks created: ${allTriggeredTasks.length}`);
      
      allTriggeredTasks.forEach((task, index) => {
        console.log(`\nTask ${index + 1}:`);
        console.log(`  ID: ${task.id}`);
        console.log(`  Payload: ${JSON.stringify(task.payload)}`);
        console.log(`  Status: ${task.status}`);
        console.log(`  Created: ${task.created_at}`);
        if (task.result) {
          console.log(`  Result: ${JSON.stringify(task.result)}`);
        }
      });
    }
    
    // Restore original value
    console.log('\n=== CLEANUP ===');
    console.log(`Restoring MPS ${testMpsId} to original order_through_mps_id: ${currentMps.order_through_mps_id || 'NULL'}`);
    
    const { error: restoreError } = await supabase
      .from('t_mps')
      .update({ order_through_mps_id: currentMps.order_through_mps_id })
      .eq('id', testMpsId);
    
    if (restoreError) {
      console.error('Error restoring MPS:', restoreError);
    } else {
      console.log('✅ MPS restored to original state');
    }
    
    console.log('\n🎯 EXPECTED BEHAVIOR:');
    console.log('1. Each MPS update should trigger one order_through_mps_id_updated task');
    console.log('2. Worker should process each task by:');
    console.log(`   - Finding all ${discCount} discs related to MPS ${testMpsId}`);
    console.log('   - Setting their vendor_osl_id to null');
    console.log(`   - Enqueueing ${discCount} match_disc_to_osl tasks`);
    console.log('3. The match_disc_to_osl tasks should recalculate vendor mappings with redirect logic');
    
    console.log('\n📊 To monitor worker processing:');
    console.log(`
-- Check task processing
SELECT 
  id, task_type, status, 
  result->>'message' as message,
  result->>'discs_found' as discs_found,
  result->>'tasks_enqueued' as tasks_enqueued,
  processed_at
FROM t_task_queue 
WHERE task_type = 'order_through_mps_id_updated'
  AND enqueued_by = 'trigger_order_through_mps_id_updated'
ORDER BY created_at DESC;

-- Check spawned match_disc_to_osl tasks
SELECT COUNT(*) as spawned_tasks
FROM t_task_queue 
WHERE task_type = 'match_disc_to_osl'
  AND enqueued_by LIKE 'order_through_mps_id_updated_mps_${testMpsId}';
    `);
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

testOrderThroughMpsIdTrigger();
