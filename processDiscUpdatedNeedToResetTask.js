// Function to process a disc_updated_need_to_reset task
async function processDiscUpdatedNeedToResetTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc update reset. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to process disc update reset. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;
    const currentSoldDate = payload.sold_date || null;
    console.log(`[taskQueueWorker.js] Processing disc update reset for disc id=${discId}, current sold_date=${currentSoldDate}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Get the current time for scheduling
    const now = new Date();

    // Create initial tasks based on disc's current sold status
    const tasks = [];

    if (!currentSoldDate) {
      // Disc is not sold - enqueue sell task
      tasks.push({
        task_type: 'disc_updated_sell_it',
        scheduled_at: new Date(now), // Immediately
        minutes_delay: 0
      });

      // Schedule delete task with 2-minute delay to wait for sell task
      tasks.push({
        task_type: 'disc_updated_delete_from_shopify',
        scheduled_at: new Date(now.getTime() + 2 * 60 * 1000), // 2 minutes later
        minutes_delay: 2
      });
    } else {
      // Disc is already sold - skip sell task and schedule delete immediately
      console.log(`[taskQueueWorker.js] Disc ${discId} is already sold (sold_date=${currentSoldDate}), skipping disc_updated_sell_it task`);

      tasks.push({
        task_type: 'disc_updated_delete_from_shopify',
        scheduled_at: new Date(now), // Immediately since no sell task needed
        minutes_delay: 0
      });
    }

    // Enqueue all tasks
    const results = [];
    for (const taskInfo of tasks) {
      try {
        // Prepare the payload - all tasks need the disc ID and current sold_date
        const taskPayload = {
          id: discId,
          sold_date: currentSoldDate
        };

        // Create the enqueued_by identifier with disc ID
        const enqueuedBy = `disc_updated_need_to_reset_${discId}`;

        const { data, error } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: taskInfo.task_type,
            payload: taskPayload,
            status: 'pending',
            scheduled_at: taskInfo.scheduled_at.toISOString(),
            created_at: now.toISOString(),
            enqueued_by: enqueuedBy
          })
          .select();

        if (error) {
          const errMsg = `[taskQueueWorker.js] Error enqueueing ${taskInfo.task_type} task: ${error.message}`;
          console.error(errMsg);
          await logError(errMsg, `Enqueueing ${taskInfo.task_type} task`);
          results.push({
            task_type: taskInfo.task_type,
            success: false,
            error: error.message
          });
        } else {
          console.log(`[taskQueueWorker.js] Successfully enqueued ${taskInfo.task_type} task for disc ${discId}, scheduled ${taskInfo.minutes_delay} minutes later`);
          results.push({
            task_type: taskInfo.task_type,
            success: true,
            task_id: data[0].id,
            scheduled_at: taskInfo.scheduled_at.toISOString()
          });
        }
      } catch (err) {
        const errMsg = `[taskQueueWorker.js] Exception enqueueing ${taskInfo.task_type} task: ${err.message}`;
        console.error(errMsg);
        await logError(errMsg, `Enqueueing ${taskInfo.task_type} task`);
        results.push({
          task_type: taskInfo.task_type,
          success: false,
          error: err.message
        });
      }
    }

    // Check if all tasks were successfully enqueued
    const successfulTasks = results.filter(r => r.success);
    const failedTasks = results.filter(r => !r.success);

    let message = `Successfully enqueued ${successfulTasks.length} out of ${tasks.length} child tasks for disc ${discId}.`;
    if (failedTasks.length > 0) {
      message += ` ${failedTasks.length} tasks failed to enqueue.`;
    }

    // Add optimization info to message
    if (currentSoldDate) {
      message += ` Optimized: Skipped disc_updated_sell_it (disc already sold) and scheduled disc_updated_delete_from_shopify immediately.`;
    } else {
      message += ` Standard: Enqueued disc_updated_sell_it immediately and disc_updated_delete_from_shopify with 2-minute delay.`;
    }

    // Mark the task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: message,
      disc_id: discId,
      current_sold_date: currentSoldDate,
      optimization_applied: !!currentSoldDate,
      sell_task_skipped: !!currentSoldDate,
      delete_task_immediate: !!currentSoldDate,
      results: results,
      successful_tasks: successfulTasks.length,
      failed_tasks: failedTasks.length
    });

  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to process disc update reset due to an unexpected error.",
      error: err.message
    });
  }
}

export default processDiscUpdatedNeedToResetTask;
