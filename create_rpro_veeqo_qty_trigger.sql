-- Enqueue update_veeqo_rpro_qty when a new change row for ivqtylaw is inserted
-- and update the status on t_rpro_changes accordingly

CREATE OR REPLACE FUNCTION public.fn_enqueue_update_veeqo_rpro_qty_task()
RETURNS trigger
LANGUAGE plpgsql
AS $$
BEGIN
  -- Insert a task into the queue for RPRO qty update
  INSERT INTO public.t_task_queue (
    task_type,
    payload,
    status,
    scheduled_at,
    created_at,
    enqueued_by
  ) VALUES (
    'update_veeqo_rpro_qty',
    jsonb_build_object(
      'rpro_change_id', NEW.id,
      'ivno', NEW.ivno,
      'is_now', NEW.is_now
    ),
    'pending',
    NOW(),
    NOW(),
    't_rpro_changes insert_trigger_' || NEW.id
  );

  -- Reflect enqueue on the source row's status
  UPDATE public.t_rpro_changes
  SET status = 'update_veeqo_rpro_qty task enqueued on ' || to_char(NOW(), 'YYYY-MM-DD')
  WHERE id = NEW.id;

  RETURN NEW;
END;
$$;

-- Drop and recreate trigger
DROP TRIGGER IF EXISTS trg_update_veeqo_qty_rpro ON public.t_rpro_changes;
CREATE TRIGGER trg_update_veeqo_qty_rpro
AFTER INSERT ON public.t_rpro_changes
FOR EACH ROW
WHEN (NEW.field_changed = 'ivqtylaw')
EXECUTE FUNCTION public.fn_enqueue_update_veeqo_rpro_qty_task();

-- Optional notice
DO $$ BEGIN RAISE NOTICE 'Trigger trg_update_veeqo_qty_rpro created/updated.'; END $$;

-- One-time catch-up: enqueue tasks for existing rows where field_changed = 'ivqtylaw'
-- Only for rows not already marked as enqueued/updated
INSERT INTO public.t_task_queue (
  task_type,
  payload,
  status,
  scheduled_at,
  created_at,
  enqueued_by
)
SELECT
  'update_veeqo_rpro_qty' AS task_type,
  jsonb_build_object('rpro_change_id', c.id, 'ivno', c.ivno, 'is_now', c.is_now) AS payload,
  'pending' AS status,
  NOW() AS scheduled_at,
  NOW() AS created_at,
  'rpro_qty_catch_up' AS enqueued_by
FROM public.t_rpro_changes c
WHERE c.field_changed = 'ivqtylaw'
  AND (c.status IS NULL OR (
       c.status NOT LIKE 'update_veeqo_rpro_qty task enqueued%' AND
       c.status NOT LIKE 'veeqo qty updated%'));

-- Mark those rows as enqueued now
UPDATE public.t_rpro_changes c
SET status = 'update_veeqo_rpro_qty task enqueued on ' || to_char(NOW(), 'YYYY-MM-DD')
WHERE c.field_changed = 'ivqtylaw'
  AND (c.status IS NULL OR (
       c.status NOT LIKE 'update_veeqo_rpro_qty task enqueued%' AND
       c.status NOT LIKE 'veeqo qty updated%'));
