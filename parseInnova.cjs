// parseInnova.cjs
const XLSX = require('xlsx');
const path = require('path');

// Define the path to your Excel file
const filePath = path.join(__dirname, 'data', 'innovaorderform.xlsx');

// Read the Excel file
const workbook = XLSX.readFile(filePath);

// Log all sheet names
console.log("All sheet names in the workbook:");
console.log(workbook.SheetNames);

// Initialize variable to store the hidden sheet name (if found)
let hiddenSheetName = null;

// Check for hidden sheet metadata (if available)
if (workbook.Workbook && workbook.Workbook.Sheets) {
  workbook.Workbook.Sheets.forEach(sheetMeta => {
    // The "Hidden" property: 0 = visible, 1 or 2 = hidden/very hidden
    if (sheetMeta.Hidden === 1 || sheetMeta.Hidden === 2) {
      hiddenSheetName = sheetMeta.name;
      console.log(`Found hidden sheet: ${hiddenSheetName}`);
    }
  });
} else {
  console.log("No workbook metadata available to identify hidden sheets.");
}

// If a hidden sheet is found, extract its data
if (hiddenSheetName) {
  const worksheet = workbook.Sheets[hiddenSheetName];
  
  // Convert the worksheet to JSON.
  // Using { header: 1 } returns a 2D array where each sub-array is a row.
  const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
  
  console.log("Data from the hidden sheet:");
  console.log(data);
} else {
  console.log("No hidden sheet found in the workbook.");
}
