-- Function to enqueue a task for finding discs to match with an updated SDASIN
CREATE OR REPLACE FUNCTION fn_enqueue_sdasin_updated_find_discs_to_match()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert a task into the task queue
    INSERT INTO t_task_queue (
        task_type,
        payload,
        status,
        scheduled_at,
        created_at
    ) VALUES (
        'sdasin_updated_find_discs_to_match',
        jsonb_build_object('id', NEW.id),
        'pending',
        NOW(),
        NOW()
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the new INSERT trigger
DROP TRIGGER IF EXISTS tr_enqueue_sdasin_updated_find_discs_to_match_insert ON t_sdasins;

CREATE TRIGGER tr_enqueue_sdasin_updated_find_discs_to_match_insert
AFTER INSERT ON t_sdasins
FOR EACH ROW
WHEN (
    NEW.min_weight IS NOT NULL
    AND NEW.max_weight IS NOT NULL
    AND (NEW.mps_id IS NOT NULL OR NEW.mps_id2 IS NOT NULL)
    AND NEW.color_id IS NOT NULL
)
EXECUTE FUNCTION fn_enqueue_sdasin_updated_find_discs_to_match();

-- Create the new UPDATE trigger for min_weight, max_weight, mps_id, mps_id2, color_id
DROP TRIGGER IF EXISTS tr_enqueue_sdasin_updated_find_discs_to_match_update ON t_sdasins;

CREATE TRIGGER tr_enqueue_sdasin_updated_find_discs_to_match_update
AFTER UPDATE OF min_weight, max_weight, mps_id, mps_id2, color_id ON t_sdasins
FOR EACH ROW
WHEN (
    NEW.min_weight IS NOT NULL
    AND NEW.max_weight IS NOT NULL
    AND (NEW.mps_id IS NOT NULL OR NEW.mps_id2 IS NOT NULL)
    AND NEW.color_id IS NOT NULL
)
EXECUTE FUNCTION fn_enqueue_sdasin_updated_find_discs_to_match();

-- Create the new UPDATE trigger for looked_for_matching_discs_at
DROP TRIGGER IF EXISTS tr_enqueue_sdasin_updated_find_discs_to_match_reset ON t_sdasins;

CREATE TRIGGER tr_enqueue_sdasin_updated_find_discs_to_match_reset
AFTER UPDATE OF looked_for_matching_discs_at ON t_sdasins
FOR EACH ROW
WHEN (
    OLD.looked_for_matching_discs_at IS NOT NULL
    AND NEW.looked_for_matching_discs_at IS NULL
)
EXECUTE FUNCTION fn_enqueue_sdasin_updated_find_discs_to_match();

-- Drop the old triggers
DROP TRIGGER IF EXISTS tr_match_on_sdasin_in ON t_sdasins;
DROP TRIGGER IF EXISTS tr_match_on_sdasin_up ON t_sdasins;
DROP TRIGGER IF EXISTS tr_match_on_sdasin_update_looked_for ON t_sdasins;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'SDASIN update enqueuer function and triggers created.';
END $$;
