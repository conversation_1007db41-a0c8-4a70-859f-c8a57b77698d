-- Clean up any tasks that might be stuck in disc_checks_completed status
-- Reset them back to pending so they work with the original logic

UPDATE t_task_queue 
SET 
    status = 'pending',
    locked_at = NULL,
    locked_by = NULL
WHERE 
    status = 'disc_checks_completed'
    AND task_type = 'publish_product_osl';

-- Show how many tasks were updated
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Reset % disc_checks_completed tasks back to pending status', updated_count;
END $$;
