-- Update v_todo_molds view to include active check
-- This adds the requirement that t_molds.active must be true for molds to be ready

CREATE OR REPLACE VIEW v_todo_molds AS
SELECT 't_molds'::text AS "table",
    m.id,
    (COALESCE(b.brand, ''::text) || ' '::text) || m.mold AS record_name,
        CASE
            WHEN m.active IS NOT TRUE THEN 'Mold is not active.'::text
            WHEN i.image_file_name IS NULL THEN 'Mold has no image file name.'::text
            WHEN i.image_file_name IS NOT NULL AND (i.image_verified IS FALSE OR i.image_verified IS NULL) THEN 'There is an image file name but its existence has not yet been confirmed.'::text
            WHEN m.speed IS NULL OR m.glide IS NULL OR m.turn IS NULL OR m.fade IS NULL THEN 'Flight numbers are not complete.'::text
            WHEN m.description IS NULL OR length(m.description) < cfg.min_length THEN 'Mold doesn''t have a description.'::text
            ELSE NULL::text
        END AS issue,
        CASE
            WHEN m.active IS NOT TRUE THEN 'Inactive molds cannot be published to Shopify.'::text
            WHEN i.image_file_name IS NULL THEN 'If we don''t have a mold image, then the mold collection can''t upload.'::text
            WHEN i.image_file_name IS NOT NULL AND (i.image_verified IS FALSE OR i.image_verified IS NULL) THEN 'Mold collections won''t upload without a verified image file.'::text
            WHEN m.speed IS NULL OR m.glide IS NULL OR m.turn IS NULL OR m.fade IS NULL THEN 'Molds must have flight numbers for proper upload and filtering.'::text
            WHEN m.description IS NULL OR length(m.description) < cfg.min_length THEN 'Mold collections don''t upload without a description.'::text
            ELSE NULL::text
        END AS effect,
        CASE
            WHEN m.active IS NOT TRUE THEN
            CASE
                WHEN count(d.id) = 0 THEN 'Important. Mold must be active to be published.'::text
                ELSE ('Critical! We have '::text || count(d.id)::text) || ' discs in stock right now that can''t upload because this mold is inactive.'::text
            END
            WHEN i.image_file_name IS NULL THEN
            CASE
                WHEN count(d.id) = 0 THEN 'Important. We need a mold image so the mold collection can upload.'::text
                ELSE ('Critical! We have '::text || count(d.id)::text) || ' discs in stock right now that can''t upload because we don''t have this mold image.'::text
            END
            WHEN i.image_file_name IS NOT NULL AND (i.image_verified IS FALSE OR i.image_verified IS NULL) THEN
            CASE
                WHEN count(d.id) = 0 THEN 'Important. We need mold image verified so the mold collection can upload.'::text
                ELSE ('Critical! We have '::text || count(d.id)::text) || ' discs in stock right now that can''t upload because we don''t have this mold image verified.'::text
            END
            WHEN m.speed IS NULL OR m.glide IS NULL OR m.turn IS NULL OR m.fade IS NULL THEN
            CASE
                WHEN count(d.id) = 0 THEN 'Important. We need mold flight numbers so the mold collection can upload and filters work correctly.'::text
                ELSE ('Critical! We have '::text || count(d.id)::text) || ' discs in stock right now that can''t upload because we don''t have mold flight numbers.'::text
            END
            WHEN m.description IS NULL OR length(m.description) < cfg.min_length THEN
            CASE
                WHEN count(d.id) = 0 THEN 'Important. We need a mold description so the mold collection can upload.'::text
                ELSE ('Critical! We have '::text || count(d.id)::text) || ' discs in stock right now that can''t upload because we don''t have a mold description.'::text
            END
            ELSE NULL::text
        END AS severity
   FROM t_molds m
     LEFT JOIN t_brands b ON m.brand_id = b.id
     LEFT JOIN t_images i ON i.table_name = 't_molds'::text AND i.record_id = m.id
     LEFT JOIN t_mps mp ON mp.mold_id = m.id
     LEFT JOIN t_discs d ON d.mps_id = mp.id AND d.sold_date IS NULL
     CROSS JOIN cfg
  WHERE (m.active IS NOT TRUE OR i.image_file_name IS NULL OR i.image_file_name IS NOT NULL AND (i.image_verified IS FALSE OR i.image_verified IS NULL) OR m.speed IS NULL OR m.glide IS NULL OR m.turn IS NULL OR m.fade IS NULL OR m.description IS NULL OR length(m.description) < cfg.min_length) AND m.shopify_collection_created_at IS NULL
  GROUP BY m.id, b.brand, m.mold, i.image_file_name, i.image_verified, m.speed, m.glide, m.turn, m.fade, m.description, m.active, cfg.min_length;
