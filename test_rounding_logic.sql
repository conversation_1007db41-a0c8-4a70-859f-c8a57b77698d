-- Create a function to test the rounding logic
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION test_rounding_logic()
RETURNS TABLE (
    weight NUMERIC,
    decimal_part NUMERIC,
    rounded_weight NUMERIC
) AS $$
DECLARE
    test_weights NUMERIC[] := ARRAY[169.4, 169.5, 169.6, 169.7, 169.8, 169.9, 170.0, 170.1, 170.2, 170.3, 170.4, 170.5, 170.6];
    w NUMERIC;
    dp NUMERIC;
    rw NUMERIC;
BEGIN
    FOREACH w IN ARRAY test_weights
    LOOP
        -- Custom rounding logic
        dp := w - FLOOR(w);

        IF dp >= 0.5 THEN
            rw := CEIL(w);
        ELSE
            rw := FLOOR(w);
        END IF;

        -- Return the results
        RETURN QUERY SELECT w, dp, rw;
    END LOOP;
    RETURN;
END;
$$ LANGUAGE plpgsql;

-- Call the function
SELECT * FROM test_rounding_logic();
