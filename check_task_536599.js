import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkTask536599() {
  try {
    console.log('🔍 Investigating Task 536599...\n');
    
    // Get task 536599
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 536599)
      .single();
    
    if (taskError) {
      console.error('❌ Error fetching task:', taskError);
      return;
    }
    
    console.log('=== TASK 536599 DETAILS ===');
    console.log('ID:', task.id);
    console.log('Task Type:', task.task_type);
    console.log('Status:', task.status);
    console.log('Created At:', task.created_at);
    console.log('Scheduled At:', task.scheduled_at);
    console.log('Processed At:', task.processed_at);
    console.log('Locked At:', task.locked_at);
    console.log('Locked By:', task.locked_by);
    console.log('Enqueued By:', task.enqueued_by);
    console.log('Payload:', JSON.stringify(task.payload, null, 2));
    
    if (task.result) {
      console.log('\n=== TASK RESULT ===');
      const result = typeof task.result === 'string' ? JSON.parse(task.result) : task.result;
      console.log(JSON.stringify(result, null, 2));
    }
    
    // If it's a mold collection task, get the mold details
    if ((task.task_type === 'publish_collection_mold' || task.task_type === 'publish_mold_collection') && task.payload && task.payload.id) {
      const moldId = task.payload.id;
      console.log('\n=== MOLD DETAILS ===');
      
      const { data: mold, error: moldError } = await supabase
        .from('t_molds')
        .select('id, mold, brand_id, shopify_collection_created_at, shopify_collection_uploaded_notes')
        .eq('id', moldId)
        .single();
      
      if (moldError) {
        console.error('❌ Error fetching mold:', moldError);
      } else {
        console.log('Mold ID:', mold.id);
        console.log('Mold Name:', mold.mold);
        console.log('Brand ID:', mold.brand_id);
        console.log('Shopify Collection Created At:', mold.shopify_collection_created_at);
        console.log('Upload Notes:', mold.shopify_collection_uploaded_notes);
        
        // Get brand details
        const { data: brand, error: brandError } = await supabase
          .from('t_brands')
          .select('id, brand, handle')
          .eq('id', mold.brand_id)
          .single();
        
        if (!brandError && brand) {
          console.log('\n=== BRAND DETAILS ===');
          console.log('Brand ID:', brand.id);
          console.log('Brand Name:', brand.brand);
          console.log('Brand Handle:', brand.handle);
        }
      }
    }
    
    // Check for related error logs around the time this task was processed
    if (task.processed_at) {
      console.log('\n=== CHECKING ERROR LOGS ===');
      const processedTime = new Date(task.processed_at);
      const startTime = new Date(processedTime.getTime() - 5 * 60 * 1000); // 5 minutes before
      const endTime = new Date(processedTime.getTime() + 5 * 60 * 1000); // 5 minutes after
      
      const { data: errorLogs, error: errorLogError } = await supabase
        .from('t_error_logs')
        .select('*')
        .gte('created_at', startTime.toISOString())
        .lte('created_at', endTime.toISOString())
        .order('created_at', { ascending: false });
      
      if (!errorLogError && errorLogs && errorLogs.length > 0) {
        console.log(`Found ${errorLogs.length} error logs around task processing time:`);
        errorLogs.forEach((log, index) => {
          console.log(`\n--- Error Log ${index + 1} ---`);
          console.log('Created At:', log.created_at);
          console.log('Error Message:', log.error_message);
          console.log('Context:', log.context);
        });
      } else {
        console.log('No error logs found around task processing time');
      }
    }
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

checkTask536599();
