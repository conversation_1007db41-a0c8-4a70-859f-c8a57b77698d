import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkSpecificRows() {
    try {
        console.log('🔍 Checking specific rows from screenshot (367-380)...\n');
        
        // Check the specific rows visible in the screenshot
        const rowsToCheck = [367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380];
        
        for (const row of rowsToCheck) {
            console.log(`\n📋 Checking row ${row}:`);
            
            const { data: rowData, error } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id, mold_name, plastic_name, is_orderable')
                .eq('excel_row_hint', row)
                .eq('is_orderable', true)
                .not('excel_mapping_key', 'is', null);

            if (error) {
                console.error(`❌ Error querying row ${row}:`, error);
                continue;
            }

            if (rowData.length === 0) {
                console.log(`   No orderable records found for row ${row}`);
                continue;
            }

            console.log(`   Found ${rowData.length} orderable records:`);
            rowData.forEach((record, index) => {
                const mpsStatus = record.calculated_mps_id ? `MPS=${record.calculated_mps_id}` : 'NO_MPS';
                console.log(`     ${index + 1}. Col ${record.excel_column}: ${mpsStatus} | ${record.mold_name} | ${record.plastic_name}`);
            });
        }

        // Also check if there are any records with calculated_mps_id = null in this range
        console.log('\n🔍 Checking for records with NULL calculated_mps_id in rows 367-380...');
        const { data: nullMpsData, error: nullError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id, mold_name, plastic_name')
            .gte('excel_row_hint', 367)
            .lte('excel_row_hint', 380)
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .is('calculated_mps_id', null);

        if (nullError) {
            console.error('❌ Error querying null MPS data:', nullError);
        } else {
            console.log(`✅ Found ${nullMpsData.length} records with NULL calculated_mps_id in rows 367-380`);
            if (nullMpsData.length > 0) {
                console.log('Records with NULL calculated_mps_id:');
                nullMpsData.forEach((record, index) => {
                    console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.mold_name} | ${record.plastic_name}`);
                });
            }
        }

        console.log('\n🎉 Check completed!');
        
    } catch (error) {
        console.error('❌ Check failed:', error.message);
    }
}

checkSpecificRows().catch(console.error);
