import ExcelJS from 'exceljs';

const originalFile = 'data/external data/discraftstock.xlsx';
const exportedFile = 'data/external data/discraftstock_2025-06-11.xlsx';

async function checkFormatting() {
  try {
    console.log('Checking ExcelJS formatting preservation...\n');
    
    // Read original file
    console.log('Reading original file...');
    const originalWorkbook = new ExcelJS.Workbook();
    await originalWorkbook.xlsx.readFile(originalFile);
    const originalSheet = originalWorkbook.getWorksheet(1);
    
    // Read exported file
    console.log('Reading exported file...');
    const exportedWorkbook = new ExcelJS.Workbook();
    await exportedWorkbook.xlsx.readFile(exportedFile);
    const exportedSheet = exportedWorkbook.getWorksheet(1);
    
    // Check a few specific cells that should have been updated
    const testCells = ['Q132', 'P178'];
    
    console.log('Comparing cell properties...\n');
    
    testCells.forEach(cellAddress => {
      console.log(`--- Cell ${cellAddress} ---`);
      
      const originalCell = originalSheet.getCell(cellAddress);
      const exportedCell = exportedSheet.getCell(cellAddress);
      
      console.log('Original cell:');
      console.log('  Value:', originalCell.value);
      console.log('  Type:', originalCell.type);
      console.log('  Style:', JSON.stringify(originalCell.style, null, 2));
      
      console.log('Exported cell:');
      console.log('  Value:', exportedCell.value);
      console.log('  Type:', exportedCell.type);
      console.log('  Style:', JSON.stringify(exportedCell.style, null, 2));
      
      // Check if styles match
      const stylesMatch = JSON.stringify(originalCell.style) === JSON.stringify(exportedCell.style);
      console.log('  Styles match:', stylesMatch);
      
      console.log('');
    });
    
  } catch (err) {
    console.error('Error:', err.message);
  }
}

checkFormatting();
