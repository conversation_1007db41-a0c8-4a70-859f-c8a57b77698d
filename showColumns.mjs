// showColumns.mjs

// Import required modules
import XLSX from 'xlsx';
import path from 'path';
import fs from 'fs';
import dotenv from 'dotenv';

dotenv.config();

// Define the path to your Excel file in the data folder
const filePath = path.join(process.cwd(), 'data', 'innovaorderform.xlsx');

// Verify the file exists
if (!fs.existsSync(filePath)) {
  console.error(`File not found: ${filePath}`);
  process.exit(1);
}

// Read the Excel workbook
const workbook = XLSX.readFile(filePath);

// Specify the sheet we want to inspect: "Order_Table"
const sheetName = 'Order_Table';
const worksheet = workbook.Sheets[sheetName];

if (!worksheet) {
  console.error(`Sheet "${sheetName}" not found in the workbook.`);
  process.exit(1);
}

// Convert the worksheet to JSON.
// The first row will be used as keys for the records.
const jsonData = XLSX.utils.sheet_to_json(worksheet, { defval: null });

// Check if there is data
if (jsonData.length === 0) {
  console.error('No data found in the sheet.');
  process.exit(1);
}

// Log the column names from the first row
const columns = Object.keys(jsonData[0]);
console.log('Columns found in the Order_Table sheet:');
console.log(columns);
