import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Shopify GraphQL Admin API credentials
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

console.log('Testing Shopify connection...');
console.log('Endpoint:', shopifyEndpoint);
console.log('Token:', shopifyAccessToken ? `${shopifyAccessToken.substring(0, 10)}...` : 'Not found');

async function testConnection() {
  if (!shopifyEndpoint || !shopifyAccessToken) {
    console.error('❌ Missing Shopify credentials');
    return false;
  }

  const query = `
    query {
      shop {
        name
        myshopifyDomain
      }
      products(first: 5) {
        edges {
          node {
            id
            title
            handle
          }
        }
      }
    }
  `;

  try {
    console.log('Making GraphQL request...');
    
    const response = await fetch(shopifyEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      },
      body: JSON.stringify({ query })
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      console.error('❌ HTTP error:', response.status, response.statusText);
      return false;
    }

    const result = await response.json();
    console.log('Response received');

    if (result.errors) {
      console.error('❌ GraphQL errors:', result.errors);
      return false;
    }

    console.log('✅ Connection successful!');
    console.log('Shop name:', result.data.shop.name);
    console.log('Domain:', result.data.shop.myshopifyDomain);
    console.log('Sample products:', result.data.products.edges.length);
    
    // Show first product as example
    if (result.data.products.edges.length > 0) {
      const firstProduct = result.data.products.edges[0].node;
      console.log('First product:', firstProduct.title, '(' + firstProduct.handle + ')');
    }

    return true;
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    return false;
  }
}

testConnection().then(success => {
  process.exit(success ? 0 : 1);
});
