// deleteVeeqoProduct.js

async function deleteProductById(productId) {
  const url = `https://api.veeqo.com/products/${productId}`;

  const options = {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': 'Vqt/16c3acd642be598d3ca079590a8aae87'
    }
  };

  try {
    const response = await fetch(url, options);
    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }
    console.log(`Product with ID ${productId} deleted successfully.`);
  } catch (error) {
    console.error(`Error deleting product with ID ${productId}:`, error);
  }
}

const productId = process.argv[2]; // Gets the product ID from the command line argument

if (!productId) {
  console.error('Please provide a product ID as an argument.');
  process.exit(1);
}

deleteProductById(productId);
