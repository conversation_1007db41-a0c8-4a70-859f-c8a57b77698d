// apply_stock_stamp_equiv.js
// Applies stock-stamp equivalent MPS matching (both stamps is_sdasin_stock=true and mold/plastic equal)

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs/promises';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function readFunctionSqlOnly(path) {
  const content = await fs.readFile(path, 'utf8');
  const splitOnConfirm = content.split(/\n-- Confirmation message[\s\S]*/i)[0];
  const splitOnDO = splitOnConfirm.split(/\nDO \$\$[\s\S]*?\$\$;?/i)[0];
  return splitOnDO.trim();
}

async function applyFunction(sql, name) {
  console.log(`\nApplying function: ${name} ...`);
  const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
  if (error) {
    console.error(`❌ Failed applying ${name}:`, error);
    throw new Error(`exec_sql failed for ${name}: ${error.message}`);
  }
  console.log(`✅ Applied ${name}`);
}

async function main() {
  try {
    console.log('Applying stock-stamp equivalent matching...');
    const f1 = await readFunctionSqlOnly('create_match_disc_to_all_sdasins_function.sql');
    const f2 = await readFunctionSqlOnly('create_match_sdasin_to_all_discs_function.sql');
    const f3 = await readFunctionSqlOnly('create_disc_sdasin_match_function.sql');

    await applyFunction(f1, 'match_disc_to_all_sdasins');
    await applyFunction(f2, 'match_sdasin_to_all_discs');
    await applyFunction(f3, 'check_disc_sdasin_match');

    console.log('\nDone.');
  } catch (e) {
    console.error('apply_stock_stamp_equiv failed:', e.message);
    process.exit(1);
  }
}

main();

