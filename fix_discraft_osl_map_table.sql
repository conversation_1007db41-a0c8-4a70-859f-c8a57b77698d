-- Fix it_discraft_osl_map table to handle larger ID values
-- The current smallint type can only handle values up to 32,767
-- But we need to store values like 187810

-- First, let's see the current table structure
-- SELECT column_name, data_type, is_nullable 
-- FROM information_schema.columns 
-- WHERE table_name = 'it_discraft_osl_map';

-- Change the id column from smallint to integer
ALTER TABLE public.it_discraft_osl_map 
ALTER COLUMN id TYPE integer;

-- Also change qty from smallint to integer for consistency
ALTER TABLE public.it_discraft_osl_map 
ALTER COLUMN qty TYPE integer;

-- Verify the changes
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'it_discraft_osl_map'
ORDER BY ordinal_position;
