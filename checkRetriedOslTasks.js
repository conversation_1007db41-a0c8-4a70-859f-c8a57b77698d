import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkRetriedTasks() {
  try {
    // Check the status of the retried tasks
    const { data: retriedTasks, error } = await supabase
      .from('t_task_queue')
      .select('id, status, result')
      .in('id', [223422, 223492, 220383, 220510, 220881])
      .order('id');

    if (error) {
      console.error('Error fetching tasks:', error);
      return;
    }

    console.log('Status of retried tasks:');
    retriedTasks.forEach(task => {
      const result = task.result ? (typeof task.result === 'string' ? JSON.parse(task.result) : task.result) : {};
      console.log(`Task ${task.id}: ${task.status} - ${result.message || result.error || 'No message'}`);
    });

    // Check if any OSL records were marked as not uploaded
    const { data: oslRecords, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, shopify_uploaded_at')
      .in('id', [11799, 11897, 4148, 4753, 5895]);

    if (oslError) {
      console.error('Error fetching OSL records:', oslError);
      return;
    }

    console.log('\nOSL records shopify_uploaded_at status:');
    oslRecords.forEach(osl => {
      console.log(`OSL ${osl.id}: shopify_uploaded_at = ${osl.shopify_uploaded_at}`);
    });

    // Get overall statistics
    const { data: allRetried, error: allError } = await supabase
      .from('t_task_queue')
      .select('status')
      .eq('task_type', 'fix_osl_weight_range')
      .gte('id', 220000); // Approximate range for the retried tasks

    if (!allError) {
      const statusCounts = allRetried.reduce((acc, task) => {
        acc[task.status] = (acc[task.status] || 0) + 1;
        return acc;
      }, {});
      
      console.log('\nOverall OSL task status:');
      Object.entries(statusCounts).forEach(([status, count]) => {
        console.log(`${status}: ${count}`);
      });
    }

  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkRetriedTasks();
