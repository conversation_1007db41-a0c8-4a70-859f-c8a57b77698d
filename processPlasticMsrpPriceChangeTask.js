/**
 * Process plastic_msrp_price_change task
 * 
 * This task is triggered when a plastic's MSRP price changes.
 * It finds all related discs and OSLs that are not sold and already uploaded to Shopify,
 * and enqueues individual MSRP price update tasks for each item.
 * 
 * Only processes items where the MPS does NOT have val_override_msrp,
 * because if there's an override, the plastic MSRP change doesn't affect those items.
 */

/**
 * Process plastic MSRP price change task
 * @param {Object} task - The task object from the queue
 * @param {Object} context - Context object containing supabase client and helper functions
 */
export default async function processPlasticMsrpPriceChangeTask(task, context = {}) {
  const { supabase, updateTaskStatus, logError } = context;
  
  if (!supabase) {
    console.error('[processPlasticMsrpPriceChangeTask] Supabase client not provided in context');
    return;
  }

  const { payload } = task;
  const plasticId = payload.id;

  console.log(`[processPlasticMsrpPriceChangeTask] Processing plastic MSRP price change for plastic id=${plasticId}`);

  try {
    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Get the new MSRP price for this plastic
    const { data: plasticData, error: plasticError } = await supabase
      .from('t_plastics')
      .select('id, plastic, val_msrp')
      .eq('id', plasticId)
      .single();

    if (plasticError) {
      throw new Error(`Failed to fetch plastic data: ${plasticError.message}`);
    }

    if (!plasticData) {
      throw new Error(`Plastic with id=${plasticId} not found`);
    }

    const newMsrpPrice = plasticData.val_msrp;
    console.log(`[processPlasticMsrpPriceChangeTask] Plastic "${plasticData.plastic}" new MSRP price: ${newMsrpPrice}`);

    // Find all discs that need MSRP updates
    // Criteria:
    // 1. Not sold (sold_date IS NULL)
    // 2. Already uploaded to Shopify (shopify_uploaded_at IS NOT NULL)
    // 3. MPS has the specified plastic_id
    // 4. MPS does NOT have val_override_msrp (MSRP is not overridden)
    const { data: discsToUpdate, error: discsError } = await supabase
      .from('t_discs')
      .select(`
        id,
        mps_id,
        t_mps!inner (
          id,
          plastic_id,
          val_override_msrp
        )
      `)
      .is('sold_date', null)
      .not('shopify_uploaded_at', 'is', null)
      .eq('t_mps.plastic_id', plasticId)
      .is('t_mps.val_override_msrp', null);

    if (discsError) {
      throw new Error(`Failed to fetch discs to update: ${discsError.message}`);
    }

    console.log(`[processPlasticMsrpPriceChangeTask] Found ${discsToUpdate.length} discs that need MSRP updates`);

    // Also find t_order_sheet_lines (OS products) that need MSRP updates
    // Criteria:
    // 1. Already uploaded to Shopify (shopify_uploaded_at IS NOT NULL)
    // 2. MPS has the specified plastic_id
    // 3. MPS does NOT have val_override_msrp (MSRP is not overridden)
    const { data: oslsToUpdate, error: oslsError } = await supabase
      .from('t_order_sheet_lines')
      .select(`
        id,
        mps_id,
        t_mps!inner (
          id,
          plastic_id,
          val_override_msrp
        )
      `)
      .not('shopify_uploaded_at', 'is', null)
      .eq('t_mps.plastic_id', plasticId)
      .is('t_mps.val_override_msrp', null);

    if (oslsError) {
      throw new Error(`Failed to fetch OSLs to update: ${oslsError.message}`);
    }

    console.log(`[processPlasticMsrpPriceChangeTask] Found ${oslsToUpdate.length} OSLs that need MSRP updates`);

    const totalItemsToUpdate = discsToUpdate.length + oslsToUpdate.length;

    if (totalItemsToUpdate === 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: `No discs or OSLs found that need MSRP updates for plastic id=${plasticId}`,
        plastic_id: plasticId,
        plastic_name: plasticData.plastic,
        new_msrp_price: newMsrpPrice,
        discs_found: discsToUpdate.length,
        osls_found: oslsToUpdate.length,
        total_items_processed: 0
      });
      return;
    }

    // Enqueue individual MSRP update tasks for each disc and OSL
    let enqueuedCount = 0;
    let errorCount = 0;

    // Process discs
    for (const disc of discsToUpdate) {
      try {
        console.log(`[processPlasticMsrpPriceChangeTask] Enqueueing MSRP update task for disc id=${disc.id}`);

        const { error: enqueueError } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: 'update_disc_variant_msrp_on_shopify',
            payload: {
              id: disc.id,
              item_type: 'disc',
              new_msrp_price: newMsrpPrice
            },
            status: 'pending',
            scheduled_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
            enqueued_by: `plastic_msrp_price_change_${plasticId}`
          });

        if (enqueueError) {
          console.error(`[processPlasticMsrpPriceChangeTask] Error enqueueing task for disc id=${disc.id}:`, enqueueError);
          errorCount++;
        } else {
          enqueuedCount++;
        }
      } catch (error) {
        console.error(`[processPlasticMsrpPriceChangeTask] Error processing disc id=${disc.id}:`, error.message);
        errorCount++;
      }
    }

    // Process OSLs
    for (const osl of oslsToUpdate) {
      try {
        console.log(`[processPlasticMsrpPriceChangeTask] Enqueueing MSRP update task for OSL id=${osl.id}`);

        const { error: enqueueError } = await supabase
          .from('t_task_queue')
          .insert({
            task_type: 'update_disc_variant_msrp_on_shopify',
            payload: {
              id: osl.id,
              item_type: 'osl',
              new_msrp_price: newMsrpPrice
            },
            status: 'pending',
            scheduled_at: new Date().toISOString(),
            created_at: new Date().toISOString(),
            enqueued_by: `plastic_msrp_price_change_${plasticId}`
          });

        if (enqueueError) {
          console.error(`[processPlasticMsrpPriceChangeTask] Error enqueueing task for OSL id=${osl.id}:`, enqueueError);
          errorCount++;
        } else {
          enqueuedCount++;
        }
      } catch (error) {
        console.error(`[processPlasticMsrpPriceChangeTask] Error processing OSL id=${osl.id}:`, error.message);
        errorCount++;
      }
    }

    // Mark task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully enqueued ${enqueuedCount} MSRP update tasks (${discsToUpdate.length} discs + ${oslsToUpdate.length} OSLs), ${errorCount} errors`,
      plastic_id: plasticId,
      plastic_name: plasticData.plastic,
      new_msrp_price: newMsrpPrice,
      discs_found: discsToUpdate.length,
      osls_found: oslsToUpdate.length,
      total_items_found: totalItemsToUpdate,
      tasks_enqueued: enqueuedCount,
      errors: errorCount
    });

    console.log(`[processPlasticMsrpPriceChangeTask] Completed processing plastic id=${plasticId}. Found ${discsToUpdate.length} discs + ${oslsToUpdate.length} OSLs. Enqueued ${enqueuedCount} tasks, ${errorCount} errors`);

  } catch (error) {
    console.error(`[processPlasticMsrpPriceChangeTask] Error processing plastic MSRP price change for plastic id=${plasticId}:`, error.message);
    
    await updateTaskStatus(task.id, 'failed', {
      error: error.message,
      plastic_id: plasticId
    });
  }
}
