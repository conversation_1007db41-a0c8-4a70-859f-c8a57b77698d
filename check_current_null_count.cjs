require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkCurrentNullCount() {
  try {
    console.log('Checking current count of discs with null vendor_osl_id...');
    
    // Get current count of discs with null vendor_osl_id
    const { count: nullCount, error: countError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null);
    
    if (countError) {
      console.error('Error getting count:', countError);
      return;
    }
    
    console.log(`Current discs with null vendor_osl_id: ${nullCount}`);
    
    if (nullCount === 0) {
      console.log('✅ No discs remaining with null vendor_osl_id!');
      return;
    }
    
    // Get a sample of these discs to see what we're dealing with
    const { data: sampleDiscs, error: sampleError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null)
      .limit(10);
    
    if (sampleError) {
      console.error('Error getting sample:', sampleError);
      return;
    }
    
    console.log(`\nSample of ${sampleDiscs.length} discs with null vendor_osl_id:`);
    
    let shouldHaveMatches = 0;
    
    for (const disc of sampleDiscs) {
      console.log(`\nDisc ${disc.id}: MPS ${disc.mps_id}, Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id}`);
      
      // Test if this disc should have a vendor OSL match
      const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
        'find_matching_osl_by_mfg_weight',
        {
          mps_id_param: disc.mps_id,
          color_id_param: disc.color_id,
          weight_mfg_param: disc.weight_mfg
        }
      );
      
      if (!vendorOslError && vendorOslData && vendorOslData.length > 0) {
        const vendorOslId = vendorOslData[0].osl_id;
        console.log(`  🎯 SHOULD MATCH OSL ${vendorOslId}!`);
        shouldHaveMatches++;
      } else {
        console.log(`  ❌ No match found (expected for some discs)`);
      }
    }
    
    console.log(`\n📊 ANALYSIS:`);
    console.log(`Total remaining discs: ${nullCount}`);
    console.log(`Sample discs that should have matches: ${shouldHaveMatches} out of ${sampleDiscs.length}`);
    
    if (shouldHaveMatches > 0) {
      const estimatedMatches = Math.round((shouldHaveMatches / sampleDiscs.length) * nullCount);
      console.log(`Estimated additional matches possible: ~${estimatedMatches}`);
      console.log('\n🔍 There are still discs that should have vendor OSL mappings!');
    } else {
      console.log('\n✅ All remaining discs correctly have no vendor OSL matches.');
    }
    
    // Also check total vendor mappings
    const { count: totalWithVendorOsl, error: totalError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .not('vendor_osl_id', 'is', null);
    
    if (!totalError) {
      console.log(`\nCurrent total discs with vendor_osl_id: ${totalWithVendorOsl}`);
    }
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

checkCurrentNullCount();
