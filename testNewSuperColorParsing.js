// Test SuperColor parsing functions directly

function extractMoldName(model) {
    if (!model) return 'Unknown';
    const modelStr = model.toString().trim();
    
    // Handle special cases first
    if (modelStr.includes('Cigarra')) return 'Cigarra';
    if (modelStr.includes('Buzzz SS')) return 'BuzzzSS';
    if (modelStr.includes('Surge SS')) return 'Surge SS';
    if (modelStr.includes('Avenger SS')) return 'Avenger SS';
    if (modelStr.includes('Crank SS')) return 'CrankSS';
    if (modelStr.includes('Buzzz OS')) return 'BuzzzOS';
    if (modelStr.includes('Nuke OS')) return 'NukeOS';
    if (modelStr.includes('Nuke SS')) return 'Nuke SS';
    if (modelStr.includes('Zone OS')) return 'Zone OS';
    if (modelStr.includes('Banger GT')) return 'Banger GT';
    if (modelStr.includes('Challenger OS')) return 'Challenger OS';
    if (modelStr.includes('Challenger SS')) return 'Challenger SS';
    if (modelStr.includes('Ringer GT')) return 'Ringer GT';

    // Remove common prefixes and suffixes
    let mold = modelStr
        .replace(/^(Fly Dye Z|Z Glo|Z Lite|Z|ESP|Jawbreaker|X)\s+/i, '')
        .replace(/\s+(Discontinued|Retired stamp|NEW).*$/i, '')
        .replace(/^(PS|RW|PP)\s+(Z|ESP)\s+/i, '')
        .replace(/^Soft\s+/i, '')
        .replace(/^Hard\s+/i, '')
        .replace(/^Big Z\s+/i, '')
        .trim();

    // Handle "NEW -" prefix
    if (mold.startsWith('NEW -')) {
        mold = mold.replace(/^NEW\s*-\s*/, '').trim();
    }

    // Extract first word as mold name
    const words = mold.split(/\s+/);
    return words[0] || 'Unknown';
}

function standardizePlasticName(rawPlastic, rawModel, vendorDescription = '') {
    const plastic = rawPlastic?.toString().trim() || '';
    
    // Handle SuperColor series (lines 401-414)
    if (plastic === 'SuperColor' || plastic.includes('SuperColor')) {
        // Check vendor description for Full Foil variants
        if (vendorDescription && vendorDescription.toString().toLowerCase().includes('full foil')) {
            return 'ESP Full Foil SuperColor';
        }
        return 'ESP SuperColor';
    }
    
    return plastic || 'Unknown';
}

function parseMoldAndStamp(rawModel, rawPlastic, vendorDescription = '') {
    const model = rawModel?.toString().trim() || '';
    const plastic = rawPlastic?.toString().trim() || '';

    // Handle SuperColor special parsing (lines 401-414)
    if (plastic === 'SuperColor') {
        // For SuperColor discs, use vendor description to determine stamp
        if (vendorDescription) {
            const desc = vendorDescription.toString().toLowerCase();
            
            // SuperColor Paul McBeth Luna
            if (desc.includes('supercolor paul mcbeth luna')) {
                return {
                    mold_name: 'Luna',
                    stamp_name: 'SuperColor Paul McBeth Luna'
                };
            }
            
            // SuperColor Gallery Buzzz variants
            if (desc.includes('supercolor gallery buzzz')) {
                if (desc.includes('bali')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Bali' };
                if (desc.includes('bunksy')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Bunsky' };
                if (desc.includes('demise')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Demise' };
                if (desc.includes('earth')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Earth' };
                if (desc.includes('fire')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Nebula Fire' };
                if (desc.includes('ancient alien')) return { mold_name: 'Buzzz', stamp_name: 'Brian Allen Ancient Alien' };
                if (desc.includes('astronaut')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Astronaut' };
                if (desc.includes('owl')) return { mold_name: 'Buzzz', stamp_name: 'Brian Allen Owl' };
                if (desc.includes('lichten')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Lichten' };
                if (desc.includes('moon')) return { mold_name: 'Buzzz', stamp_name: 'SuperColor Moon' };
            }
            
            // Full Foil SuperColor variants
            if (desc.includes('full foil supercolor')) {
                if (desc.includes('chains green')) return { mold_name: 'Buzzz', stamp_name: 'Chains Green' };
                if (desc.includes('chains pink')) return { mold_name: 'Buzzz', stamp_name: 'Chains Pink' };
                if (desc.includes('chains blue')) return { mold_name: 'Buzzz', stamp_name: 'Chains Blue' };
            }
        }
        
        // Default SuperColor parsing if no specific match
        return {
            mold_name: extractMoldName(model),
            stamp_name: 'SuperColor'
        };
    }

    return {
        mold_name: extractMoldName(model),
        stamp_name: 'Stock'
    };
}

async function testSuperColorParsing() {
    console.log('🧪 Testing SuperColor parsing...\n');

    // Test cases based on the user's requirements
    const testCases = [
        {
            name: 'SuperColor Paul McBeth Luna',
            plastic: 'SuperColor',
            model: 'Luna',
            vendorDescription: 'SuperColor Paul McBeth Luna',
            expected: {
                plastic_name: 'ESP SuperColor',
                mold_name: 'Luna',
                stamp_name: 'SuperColor Paul McBeth Luna'
            }
        },
        {
            name: 'SuperColor Gallery Buzzz - Bali',
            plastic: 'SuperColor',
            model: 'Buzzz',
            vendorDescription: 'SuperColor Gallery Buzzz - Bali',
            expected: {
                plastic_name: 'ESP SuperColor',
                mold_name: 'Buzzz',
                stamp_name: 'SuperColor Bali'
            }
        },
        {
            name: 'SuperColor Gallery Buzzz - Bunksy',
            plastic: 'SuperColor',
            model: 'Buzzz',
            vendorDescription: 'SuperColor Gallery Buzzz - Bunksy',
            expected: {
                plastic_name: 'ESP SuperColor',
                mold_name: 'Buzzz',
                stamp_name: 'SuperColor Bunsky'
            }
        },
        {
            name: 'SuperColor Gallery Buzzz - Fire',
            plastic: 'SuperColor',
            model: 'Buzzz',
            vendorDescription: 'SuperColor Gallery Buzzz - Fire',
            expected: {
                plastic_name: 'ESP SuperColor',
                mold_name: 'Buzzz',
                stamp_name: 'SuperColor Nebula Fire'
            }
        },
        {
            name: 'SuperColor Gallery Buzzz - Ancient Alien',
            plastic: 'SuperColor',
            model: 'Buzzz',
            vendorDescription: 'SuperColor Gallery Buzzz - Ancient Alien',
            expected: {
                plastic_name: 'ESP SuperColor',
                mold_name: 'Buzzz',
                stamp_name: 'Brian Allen Ancient Alien'
            }
        },
        {
            name: 'FULL FOIL SuperColor Buzzz - Chains Green',
            plastic: 'SuperColor',
            model: 'Buzzz',
            vendorDescription: 'FULL FOIL SuperColor Buzzz - Chains Green',
            expected: {
                plastic_name: 'ESP Full Foil SuperColor',
                mold_name: 'Buzzz',
                stamp_name: 'Chains Green'
            }
        },
        {
            name: 'FULL FOIL SuperColor Buzzz - Chains Pink',
            plastic: 'SuperColor',
            model: 'Buzzz',
            vendorDescription: 'FULL FOIL SuperColor Buzzz - Chains Pink',
            expected: {
                plastic_name: 'ESP Full Foil SuperColor',
                mold_name: 'Buzzz',
                stamp_name: 'Chains Pink'
            }
        },
        {
            name: 'FULL FOIL SuperColor Buzzz - Chains Blue',
            plastic: 'SuperColor',
            model: 'Buzzz',
            vendorDescription: 'FULL FOIL SuperColor Buzzz - Chains Blue',
            expected: {
                plastic_name: 'ESP Full Foil SuperColor',
                mold_name: 'Buzzz',
                stamp_name: 'Chains Blue'
            }
        }
    ];

    testCases.forEach((testCase, index) => {
        console.log(`${index + 1}. Testing ${testCase.name}:`);
        console.log(`   Input: "${testCase.plastic}" | "${testCase.model}" | "${testCase.vendorDescription}"`);
        
        const plasticName = standardizePlasticName(testCase.plastic, testCase.model, testCase.vendorDescription);
        const { mold_name, stamp_name } = parseMoldAndStamp(testCase.model, testCase.plastic, testCase.vendorDescription);
        
        console.log(`   Output: ${plasticName} | ${mold_name} | ${stamp_name}`);
        console.log(`   Expected: ${testCase.expected.plastic_name} | ${testCase.expected.mold_name} | ${testCase.expected.stamp_name}`);
        
        const isCorrect = plasticName === testCase.expected.plastic_name && 
                         mold_name === testCase.expected.mold_name && 
                         stamp_name === testCase.expected.stamp_name;
        
        console.log(`   ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}\n`);
    });

    console.log('🎉 SuperColor parsing test completed!');
}

testSuperColorParsing().catch(console.error);
