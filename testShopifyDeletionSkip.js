// testShopifyDeletionSkip.js - Test that disc_updated_delete_from_shopify skips when shopify_uploaded_at is null
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testShopifyDeletionSkip() {
  console.log('🧪 Testing Shopify Deletion Skip Optimization');
  console.log('==============================================');

  try {
    // Find discs with null shopify_uploaded_at
    console.log('\n🔍 Looking for discs with null shopify_uploaded_at...');
    
    const { data: neverUploadedDiscs, error: neverUploadedError } = await supabase
      .from('t_discs')
      .select('id, shopify_uploaded_at, shopify_uploaded_notes')
      .is('shopify_uploaded_at', null)
      .limit(5);

    if (neverUploadedError) {
      console.error('❌ Error fetching never-uploaded discs:', neverUploadedError.message);
      return;
    }

    console.log(`📊 Found ${neverUploadedDiscs?.length || 0} discs with null shopify_uploaded_at:`);
    if (neverUploadedDiscs && neverUploadedDiscs.length > 0) {
      neverUploadedDiscs.forEach(disc => {
        console.log(`   Disc ${disc.id}: shopify_uploaded_at=${disc.shopify_uploaded_at}, notes="${disc.shopify_uploaded_notes || 'null'}"`);
      });
    }

    // Find discs with non-null shopify_uploaded_at
    console.log('\n🔍 Looking for discs with non-null shopify_uploaded_at...');
    
    const { data: uploadedDiscs, error: uploadedError } = await supabase
      .from('t_discs')
      .select('id, shopify_uploaded_at, shopify_uploaded_notes')
      .not('shopify_uploaded_at', 'is', null)
      .limit(5);

    if (uploadedError) {
      console.error('❌ Error fetching uploaded discs:', uploadedError.message);
      return;
    }

    console.log(`📊 Found ${uploadedDiscs?.length || 0} discs with non-null shopify_uploaded_at:`);
    if (uploadedDiscs && uploadedDiscs.length > 0) {
      uploadedDiscs.forEach(disc => {
        console.log(`   Disc ${disc.id}: shopify_uploaded_at=${disc.shopify_uploaded_at}, notes="${disc.shopify_uploaded_notes || 'null'}"`);
      });
    }

    console.log('\n🔧 OPTIMIZATION IMPLEMENTED:');
    console.log('============================');
    console.log('✅ Added shopify_uploaded_at null check');
    console.log('✅ Skips Shopify API calls for never-uploaded discs');
    console.log('✅ Still updates database fields for consistency');
    console.log('✅ Enqueues next task on successful skip');
    console.log('✅ Marks task as completed with skip indicators');

    console.log('\n📋 Two Processing Paths:');
    console.log('========================');
    console.log('🔄 **Path 1: shopify_uploaded_at is NULL**');
    console.log('   1. Skip Shopify deletion (nothing to delete)');
    console.log('   2. Update database fields for consistency');
    console.log('   3. Mark as successful (shopify_deletion_success: true)');
    console.log('   4. Enqueue disc_updated_reset task');
    console.log('   5. Complete with action: "skipped"');
    console.log('');
    console.log('🗑️ **Path 2: shopify_uploaded_at is NOT NULL**');
    console.log('   1. Calculate shopify_sku (D{disc_id})');
    console.log('   2. Call Shopify API to delete variant/product');
    console.log('   3. Update database fields');
    console.log('   4. Check deletion success criteria');
    console.log('   5. Enqueue next task only on success');

    console.log('\n🎯 Expected Task Results:');
    console.log('=========================');
    console.log('**For Never-Uploaded Discs:**');
    console.log('```javascript');
    console.log('{');
    console.log('  message: "Disc 123 has not been uploaded to Shopify, no deletion needed...",');
    console.log('  shopify_deletion_attempted: false,');
    console.log('  shopify_deletion_success: true,');
    console.log('  shopify_deletion_skipped: true,');
    console.log('  database_fields_updated: true,');
    console.log('  next_task_enqueued: true,');
    console.log('  action: "skipped"');
    console.log('}');
    console.log('```');
    console.log('');
    console.log('**For Previously-Uploaded Discs:**');
    console.log('```javascript');
    console.log('{');
    console.log('  message: "Successfully deleted disc variant...",');
    console.log('  shopify_deletion_attempted: true,');
    console.log('  shopify_deletion_success: true,');
    console.log('  shopify_deletion_skipped: false,');
    console.log('  database_fields_updated: true,');
    console.log('  next_task_enqueued: true');
    console.log('}');
    console.log('```');

    console.log('\n⚡ Performance Benefits:');
    console.log('=======================');
    console.log('🚀 **Faster processing** - No unnecessary Shopify API calls');
    console.log('💰 **API efficiency** - Reduces Shopify API usage');
    console.log('🔄 **Consistent workflow** - Next task always enqueued on success');
    console.log('📊 **Clear reporting** - Skip status clearly indicated');
    console.log('🛡️ **Error reduction** - No API errors for non-existent variants');

    console.log('\n🧪 Test Scenarios:');
    console.log('==================');
    if (neverUploadedDiscs && neverUploadedDiscs.length > 0) {
      const testDiscId = neverUploadedDiscs[0].id;
      console.log(`1. Create disc_updated_delete_from_shopify task for disc ${testDiscId}`);
      console.log(`2. Verify it skips Shopify deletion`);
      console.log(`3. Check database fields are updated`);
      console.log(`4. Confirm next task is enqueued`);
      console.log(`5. Verify task result shows action: "skipped"`);
    } else {
      console.log('No never-uploaded discs available for testing');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testShopifyDeletionSkip()
  .then(() => {
    console.log('\n🏁 Analysis completed');
    console.log('The disc_updated_delete_from_shopify task now efficiently skips unnecessary deletions!');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Analysis failed:', error);
    process.exit(1);
  });
