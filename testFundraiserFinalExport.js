import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testFundraiserFinalExport() {
    try {
        console.log('🧪 Testing fundraiser final export with correct structure...\n');
        
        // 1. Check what will be exported for the fundraiser section
        console.log('1. Checking fundraiser section export data...');
        const { data: fundraiserExportData, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, calculated_mps_id, mold_name, plastic_name')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .gte('excel_row_hint', 22)
            .lte('excel_row_hint', 30)
            .order('excel_row_hint, excel_column');

        if (error) {
            console.error('❌ Error querying fundraiser export data:', error);
            return;
        }

        console.log(`✅ Found ${fundraiserExportData.length} records in fundraiser section (rows 22-30):`);
        fundraiserExportData.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.plastic_name} ${record.mold_name}`);
            console.log(`      MPS: ${record.calculated_mps_id || 'NO_MPS'}`);
        });

        // 2. Get all orderable data for full export test
        console.log('\n2. Getting all orderable data for export test...');
        const { data: allOrderableData, error: allError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null);

        if (allError) {
            console.error('❌ Error getting all orderable data:', allError);
            return;
        }

        console.log(`✅ Found ${allOrderableData.length} total orderable records`);

        // 3. Create test data with some quantities for fundraiser items
        console.log('\n3. Creating test export data...');
        
        const orderData = allOrderableData.map(item => {
            let testOrder = 0;
            
            // Set test quantities for fundraiser items
            if (item.excel_row_hint === 25 && item.excel_column === 'B') {
                testOrder = 5; // Test quantity for Thrasher
            } else if (item.excel_row_hint === 28 && item.excel_column === 'B') {
                testOrder = 3; // Test quantity for Buzzz
            }
            
            return {
                ...item,
                order: testOrder
            };
        });

        // 4. Create MPS data
        const mpsData = allOrderableData.map(item => ({
            ...item,
            order: item.calculated_mps_id || 'NO_MPS'
        }));

        // 5. Test both exports
        const timestamp = new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0];
        
        console.log('\n4. Testing order quantity export...');
        const orderResponse = await fetch('http://localhost:3001/api/discraft/export', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                includeHeader: false,
                filename: `test_fundraiser_order_${timestamp}.xlsx`,
                orderData: orderData
            })
        });

        if (!orderResponse.ok) {
            throw new Error(`Order export API returned ${orderResponse.status}: ${orderResponse.statusText}`);
        }

        const orderResult = await orderResponse.json();
        console.log('✅ Order quantity export completed!');
        console.log(`📄 Filename: ${orderResult.filename}`);

        console.log('\n5. Testing MPS export...');
        const mpsResponse = await fetch('http://localhost:3001/api/discraft/export', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                includeHeader: false,
                filename: `test_fundraiser_mps_${timestamp}.xlsx`,
                orderData: mpsData
            })
        });

        if (!mpsResponse.ok) {
            throw new Error(`MPS export API returned ${mpsResponse.status}: ${mpsResponse.statusText}`);
        }

        const mpsResult = await mpsResponse.json();
        console.log('✅ MPS export completed!');
        console.log(`📄 Filename: ${mpsResult.filename}`);

        console.log('\n🎯 Final Test Results:');
        console.log('   ✅ Row 22: Should be EMPTY (header not parsed)');
        console.log('   ✅ Row 24: Should be EMPTY (header not parsed)');
        console.log('   ✅ Row 25, Column B: Should show 5 (order) or 19704 (MPS)');
        console.log('   ✅ Row 27: Should be EMPTY (header not parsed)');
        console.log('   ✅ Row 28, Column B: Should show 3 (order) or NO_MPS (MPS)');
        
        console.log(`\n📁 Check these files:`);
        console.log(`   Order quantities: ${orderResult.filePath}`);
        console.log(`   MPS verification: ${mpsResult.filePath}`);
        
        console.log('\n🔍 Look specifically at:');
        console.log('   • Row 22: Should be empty (no red header with values)');
        console.log('   • Row 24: Should be empty (no "Order qty" header with values)');
        console.log('   • Row 25, Column B: Should have the order qty/MPS value');
        console.log('   • Row 27: Should be empty (no "Order qty" header with values)');
        console.log('   • Row 28, Column B: Should have the order qty/MPS value');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testFundraiserFinalExport().catch(console.error);
