import runDailyAutomation from './discraftDailyAutomation.js';

console.log('🧪 Testing Discraft Daily Automation...');
console.log('This will run the full automation process once for testing.\n');

// Run the automation
runDailyAutomation()
  .then(() => {
    console.log('\n✅ Test completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  });
