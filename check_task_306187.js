// Check the result of task 306187
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Create a Supabase client using environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkTask306187() {
  console.log('Checking task 306187...');

  try {
    // Get the task details
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', 306187)
      .single();

    if (taskError) {
      console.error('Error getting task:', taskError);
      return;
    }

    console.log('\n=== TASK 306187 DETAILS ===');
    console.log(`Task Type: ${task.task_type}`);
    console.log(`Status: ${task.status}`);
    console.log(`Payload:`, task.payload);
    console.log(`Result:`, task.result);
    console.log(`Created At: ${task.created_at}`);
    console.log(`Processed At: ${task.processed_at}`);
    console.log(`Enqueued By: ${task.enqueued_by}`);

    // Check what location was being searched for
    const searchLocation = task.payload?.location || task.payload;
    console.log(`\nSearched for location: '${searchLocation}'`);

    // Check if there are any discs with that exact location
    const { data: exactMatches, error: exactError } = await supabase
      .from('t_discs')
      .select('id, location')
      .eq('location', searchLocation)
      .limit(5);

    if (exactError) {
      console.error('Error checking for exact matches:', exactError);
    } else {
      console.log(`\nFound ${exactMatches?.length || 0} discs with exact location '${searchLocation}':`);
      if (exactMatches && exactMatches.length > 0) {
        exactMatches.forEach(disc => {
          console.log(`  Disc ID ${disc.id}: location = '${disc.location}'`);
        });
      }
    }

    // Check for similar B2F locations
    const { data: similarMatches, error: similarError } = await supabase
      .from('t_discs')
      .select('id, location')
      .like('location', 'B2F%')
      .limit(10);

    if (similarError) {
      console.error('Error checking for similar matches:', similarError);
    } else {
      console.log(`\nFound ${similarMatches?.length || 0} discs with B2F locations:`);
      if (similarMatches && similarMatches.length > 0) {
        // Group by location
        const locationCounts = {};
        similarMatches.forEach(disc => {
          locationCounts[disc.location] = (locationCounts[disc.location] || 0) + 1;
        });
        
        Object.entries(locationCounts).forEach(([location, count]) => {
          console.log(`  '${location}': ${count} disc(s)`);
        });
      }
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Run the check
checkTask306187();
