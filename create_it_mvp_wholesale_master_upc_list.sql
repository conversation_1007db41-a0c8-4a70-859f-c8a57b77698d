-- Creates the MVP UPC master table used by the admin import (admin.html > MVP > Import)
-- Run this in Supabase SQL editor once before using the import UI

CREATE TABLE IF NOT EXISTS public.it_mvp_wholesale_master_upc_list (
  sku_number text PRIMARY KEY,
  asin text,
  upc text,
  description text,
  notes text,
  imported_at timestamptz DEFAULT now(),
  last_seen_at timestamptz
);

-- Ensure column exists when upgrading older installs
ALTER TABLE public.it_mvp_wholesale_master_upc_list
  ADD COLUMN IF NOT EXISTS last_seen_at timestamptz;


-- Optional helper index for fast ASIN lookups
CREATE INDEX IF NOT EXISTS idx_it_mvp_wholesale_master_upc_list_asin
  ON public.it_mvp_wholesale_master_upc_list (asin);

-- Optional helper index for recent presence tracking
CREATE INDEX IF NOT EXISTS idx_it_mvp_wholesale_master_upc_list_last_seen
  ON public.it_mvp_wholesale_master_upc_list (last_seen_at);

-- View: recent note changes (last 30 days)
CREATE OR REPLACE VIEW public.v_mvp_notes_recent_30d AS
SELECT
  sku_number,
  asin,
  upc,
  description,
  notes,
  imported_at,
  last_seen_at,
  CASE
    WHEN notes IS NOT NULL AND length(notes) >= 10 THEN to_date(substr(notes,1,10), 'YYYY-MM-DD')
    ELSE NULL
  END AS latest_note_date
FROM public.it_mvp_wholesale_master_upc_list
WHERE notes IS NOT NULL
  AND length(notes) >= 10
  AND to_date(substr(notes,1,10), 'YYYY-MM-DD') >= (current_date - interval '30 days')
ORDER BY latest_note_date DESC, sku_number;
