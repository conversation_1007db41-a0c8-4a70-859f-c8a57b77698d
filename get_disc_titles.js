import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function getDiscTitles() {
    console.log('Getting actual disc titles from Dynamic Discs data...\n');
    
    try {
        // Get product titles that are likely to be discs
        const { data: discTitles, error: titlesError } = await supabase
            .from('it_dd_osl')
            .select('product_title, variant_title, product_product_type')
            .eq('product_vendor', 'Dynamic Discs')
            .eq('product_product_type', 'Discs')
            .not('product_title', 'ilike', '%DyeMax%')
            .not('product_title', 'ilike', '%Set%')
            .not('product_title', 'ilike', '%Overweight%')
            .limit(30);
        
        if (titlesError) {
            console.error('Error fetching disc titles:', titlesError);
            return;
        }
        
        console.log('Dynamic Discs disc titles:');
        console.log('=====================================');
        discTitles.forEach((item, index) => {
            console.log(`${index + 1}. Product: "${item.product_title}"`);
            console.log(`   Variant: "${item.variant_title}"`);
            console.log('---');
        });
        
    } catch (error) {
        console.error('Query failed:', error);
    }
}

// Run the query
getDiscTitles();
