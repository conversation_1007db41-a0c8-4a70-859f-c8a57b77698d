import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM3ODY1NDEsImV4cCI6MjA0OTM2MjU0MX0.MtBXMZt7rCqXd6c9clhrNoVtfOxEilX2C8oboMceOGg';
const supabase = createClient(supabaseUrl, supabaseKey);

const updateDiscOsl = async () => {
  try {
    console.log('Attempting to update disc 421349 with order_sheet_line_id 16890...');
    
    // First, check if the disc exists
    const { count: discCount, error: discCountError } = await supabase
      .from('t_discs')
      .select('*', { count: 'exact', head: true })
      .eq('id', 421349);
      
    if (discCountError) {
      console.error('Error checking if disc exists:', discCountError);
      return;
    }
    
    if (discCount === 0) {
      console.log('Disc with ID 421349 does not exist in the database.');
      return;
    }
    
    // Check if the order sheet line exists
    const { count: oslCount, error: oslCountError } = await supabase
      .from('t_order_sheet_lines')
      .select('*', { count: 'exact', head: true })
      .eq('id', 16890);
      
    if (oslCountError) {
      console.error('Error checking if order sheet line exists:', oslCountError);
      return;
    }
    
    if (oslCount === 0) {
      console.log('Order sheet line with ID 16890 does not exist in the database.');
      return;
    }
    
    // Both records exist, so update the disc
    const { data, error } = await supabase
      .from('t_discs')
      .update({ order_sheet_line_id: 16890 })
      .eq('id', 421349);
      
    if (error) {
      console.error('Error updating disc:', error);
      return;
    }
    
    console.log('Successfully updated disc 421349 with order_sheet_line_id 16890');
    
    // Verify the update
    const { data: verifyData, error: verifyError } = await supabase
      .from('t_discs')
      .select('id, order_sheet_line_id')
      .eq('id', 421349)
      .single();
      
    if (verifyError) {
      console.error('Error verifying update:', verifyError);
      return;
    }
    
    console.log('Verification:', verifyData);
  } catch (error) {
    console.error('Unexpected error:', error);
  }
};

updateDiscOsl();
