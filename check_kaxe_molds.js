import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkKaxeMolds() {
    console.log('Checking Kaxe molds...\n');
    
    try {
        // Get Kastaplast brand ID
        const { data: brandData, error: brandError } = await supabase
            .from('t_brands')
            .select('id, brand')
            .eq('brand', 'Kastaplast');
        
        const kastaBrandId = brandData[0].id;
        console.log(`Kastaplast brand ID: ${kastaBrandId}`);
        
        // Get all Kastaplast molds containing "Kaxe"
        const { data: kaxeMolds, error: kaxeError } = await supabase
            .from('t_molds')
            .select('mold')
            .eq('brand_id', kastaBrandId)
            .ilike('mold', '%kaxe%')
            .order('mold');
        
        console.log('\nKaxe-related molds:');
        console.log('==================');
        kaxeMolds.forEach(mold => {
            console.log(`- "${mold.mold}" (length: ${mold.mold.length})`);
        });
        
        // Test the parsing logic
        const title = "K1 Kaxe (new)";
        const plastic = "K1";
        const remainingAfterPlastic = title.substring(plastic.length).trim(); // "Kaxe (new)"
        
        console.log(`\nTitle: "${title}"`);
        console.log(`After removing plastic "${plastic}": "${remainingAfterPlastic}"`);
        
        // No dash, so moldPart = remainingAfterPlastic
        const moldPart = remainingAfterPlastic; // "Kaxe (new)"
        console.log(`Mold part to search: "${moldPart}"`);
        
        // Sort molds by length (longest first)
        const sortedMolds = kaxeMolds.map(m => m.mold).sort((a, b) => b.length - a.length);
        console.log('\nMolds sorted by length (longest first):');
        sortedMolds.forEach(mold => {
            console.log(`- "${mold}" (length: ${mold.length})`);
        });
        
        // Test exact match
        console.log('\nTesting exact match:');
        for (const mold of sortedMolds) {
            const isExact = moldPart.toLowerCase() === mold.toLowerCase();
            console.log(`"${moldPart}".toLowerCase() === "${mold}".toLowerCase(): ${isExact}`);
        }
        
        // Test starts with match
        console.log('\nTesting starts with match:');
        for (const mold of sortedMolds) {
            const startsWith = moldPart.toLowerCase().startsWith(mold.toLowerCase() + ' ');
            console.log(`"${moldPart}".toLowerCase().startsWith("${mold.toLowerCase()} "): ${startsWith}`);
        }
        
        // Test if mold appears at start
        console.log('\nTesting if mold appears at start:');
        for (const mold of sortedMolds) {
            const startsWithMold = moldPart.toLowerCase().startsWith(mold.toLowerCase());
            console.log(`"${moldPart}".toLowerCase().startsWith("${mold.toLowerCase()}"): ${startsWithMold}`);
            if (startsWithMold) {
                const afterMold = moldPart.substring(mold.length).trim();
                console.log(`  After removing mold: "${afterMold}"`);
            }
        }
        
    } catch (error) {
        console.error('Debug failed:', error);
    }
}

// Run the debug
checkKaxeMolds();
