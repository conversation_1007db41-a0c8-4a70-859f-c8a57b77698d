-- Create table to track Amazon listing deletions
-- This table is optional but useful for logging and auditing Amazon listing deletions

CREATE TABLE IF NOT EXISTS public.t_amazon_listing_deletions (
    id SERIAL PRIMARY KEY,
    sku VARCHAR(255) NOT NULL,
    marketplace_ids TEXT[] NOT NULL DEFAULT ARRAY['ATVPDKIKX0DER'],
    reason TEXT,
    listing_existed BOOLEAN DEFAULT NULL,
    deletion_result JSONB,
    deleted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    task_id INTEGER REFERENCES public.t_task_queue(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_by VA<PERSON>HA<PERSON>(255) DEFAULT 'system'
);

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_amazon_deletions_sku ON public.t_amazon_listing_deletions(sku);
CREATE INDEX IF NOT EXISTS idx_amazon_deletions_deleted_at ON public.t_amazon_listing_deletions(deleted_at);
CREATE INDEX IF NOT EXISTS idx_amazon_deletions_task_id ON public.t_amazon_listing_deletions(task_id);

-- Add comments for documentation
COMMENT ON TABLE public.t_amazon_listing_deletions IS 'Tracks Amazon listing deletions performed via SP-API';
COMMENT ON COLUMN public.t_amazon_listing_deletions.sku IS 'Amazon SKU that was deleted';
COMMENT ON COLUMN public.t_amazon_listing_deletions.marketplace_ids IS 'Array of Amazon marketplace IDs where deletion was attempted';
COMMENT ON COLUMN public.t_amazon_listing_deletions.reason IS 'Reason provided for the deletion';
COMMENT ON COLUMN public.t_amazon_listing_deletions.listing_existed IS 'Whether the listing existed when deletion was attempted';
COMMENT ON COLUMN public.t_amazon_listing_deletions.deletion_result IS 'JSON response from Amazon SP-API deletion request';
COMMENT ON COLUMN public.t_amazon_listing_deletions.deleted_at IS 'When the deletion was performed';
COMMENT ON COLUMN public.t_amazon_listing_deletions.task_id IS 'Reference to the task queue task that performed the deletion';

-- Create a view for easy reporting
CREATE OR REPLACE VIEW public.v_amazon_listing_deletions AS
SELECT 
    d.id,
    d.sku,
    d.marketplace_ids,
    d.reason,
    d.listing_existed,
    d.deleted_at,
    d.task_id,
    t.status as task_status,
    t.result as task_result,
    t.processed_at as task_processed_at,
    CASE 
        WHEN d.listing_existed = true THEN 'Deleted'
        WHEN d.listing_existed = false THEN 'Not Found'
        ELSE 'Unknown'
    END as deletion_status
FROM public.t_amazon_listing_deletions d
LEFT JOIN public.t_task_queue t ON d.task_id = t.id
ORDER BY d.deleted_at DESC;

COMMENT ON VIEW public.v_amazon_listing_deletions IS 'View combining Amazon listing deletions with task queue information';

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE ON public.t_amazon_listing_deletions TO your_app_user;
-- GRANT SELECT ON public.v_amazon_listing_deletions TO your_app_user;
-- GRANT USAGE ON SEQUENCE public.t_amazon_listing_deletions_id_seq TO your_app_user;
