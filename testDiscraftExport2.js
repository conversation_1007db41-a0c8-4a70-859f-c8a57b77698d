import http from 'http';

function testExport() {
  const postData = JSON.stringify({});
  
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: '/api/discraft/export-orders',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    console.log(`Headers: ${JSON.stringify(res.headers)}`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const result = JSON.parse(data);
        console.log('Export result:', JSON.stringify(result, null, 2));
      } catch (err) {
        console.log('Raw response:', data);
      }
    });
  });

  req.on('error', (err) => {
    console.error('Error:', err.message);
  });

  req.write(postData);
  req.end();
}

console.log('Testing Discraft export...');
testExport();
