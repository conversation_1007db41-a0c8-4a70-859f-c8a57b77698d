-- create_innova_internal_counts_procedure.sql
-- RPC: calculate_innova_internal_id_disc_counts
-- Calculates in_stock and sold_last_90 for each it_innova_order_sheet_lines row
-- Joins t_discs via t_order_sheet_lines where t_order_sheet_lines.vendor_internal_id = it_innova_order_sheet_lines.internal_id

-- Add needed columns if missing
DO $$
BEGIN
  BEGIN
    ALTER TABLE public.it_innova_order_sheet_lines
      ADD COLUMN IF NOT EXISTS in_stock INTEGER,
      ADD COLUMN IF NOT EXISTS sold_last_90 INTEGER,
      ADD COLUMN IF NOT EXISTS qty INTEGER;
  EXCEPTION WHEN OTHERS THEN
    -- ignore
    NULL;
  END;
END $$;

-- Create or replace the RPC function
CREATE OR REPLACE FUNCTION public.calculate_innova_internal_id_disc_counts()
RETURNS TABLE(
  rows_updated INTEGER,
  in_stock_sum INTEGER,
  sold_last_90_sum INTEGER
) LANGUAGE plpgsql SECURITY DEFINER SET search_path = public AS $$
DECLARE
  lookback_days INTEGER := 60;
BEGIN
  -- Read lookback days from config if available
  BEGIN
    SELECT COALESCE(NULLIF(value, '')::INTEGER, 60) INTO lookback_days
    FROM t_config WHERE key = 'innova_disc_order_look_back_days';
  EXCEPTION WHEN OTHERS THEN
    lookback_days := 60;
  END;

  RETURN QUERY
  WITH ids AS (
    SELECT DISTINCT it_innova_order_sheet_lines.internal_id AS internal_id
    FROM it_innova_order_sheet_lines
    WHERE it_innova_order_sheet_lines.internal_id IS NOT NULL
      AND it_innova_order_sheet_lines.internal_id > 0
  ),
  counts AS (
    SELECT
      i.internal_id,
      -- in stock = unsold discs joined through OSLs whose vendor_internal_id = internal_id
      (
        SELECT COUNT(*)
        FROM t_discs d
        JOIN t_order_sheet_lines osl ON d.vendor_osl_id = osl.id
        WHERE osl.vendor_internal_id = i.internal_id::text
          AND d.sold_date IS NULL
      ) AS in_stock,
      (
        SELECT COUNT(*)
        FROM t_discs d
        JOIN t_order_sheet_lines osl ON d.vendor_osl_id = osl.id
        WHERE osl.vendor_internal_id = i.internal_id::text
          AND d.sold_date::date >= (CURRENT_DATE - lookback_days)
      ) AS sold_last_90
    FROM ids i
  ),
  updated AS (
    UPDATE public.it_innova_order_sheet_lines AS t
    SET in_stock = c.in_stock,
        sold_last_90 = c.sold_last_90,
        updated_at = NOW()
    FROM counts AS c
    WHERE t.internal_id = c.internal_id
    RETURNING t.in_stock, t.sold_last_90
  )
  SELECT COUNT(*)::integer AS rows_updated,
         COALESCE(SUM(updated.in_stock), 0)::integer AS in_stock_sum,
         COALESCE(SUM(updated.sold_last_90), 0)::integer AS sold_last_90_sum
  FROM updated;
END $$;

GRANT EXECUTE ON FUNCTION public.calculate_innova_internal_id_disc_counts() TO anon, authenticated;

-- Grant execute to anon/authenticated if needed (adjust roles as appropriate)
-- GRANT EXECUTE ON FUNCTION public.calculate_innova_internal_id_disc_counts() TO anon, authenticated;

