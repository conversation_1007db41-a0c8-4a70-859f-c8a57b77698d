import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0NzI4NzQsImV4cCI6MjA1MDA0ODg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyPPZLiteFix() {
    try {
        console.log('🔍 Verifying PP Z Lite weight ranges after fix...\n');
        
        // Get PP Z Lite products
        const { data: ppzLiteProducts, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*')
            .eq('plastic_name', 'Elite Z Lite')
            .ilike('raw_line_type', '%PP Z Lite%')
            .order('mold_name');
        
        if (error) {
            console.error('❌ Error fetching PP Z Lite products:', error);
            return;
        }
        
        console.log(`Found ${ppzLiteProducts.length} PP Z Lite products:\n`);
        
        ppzLiteProducts.forEach(product => {
            console.log(`🥏 ${product.mold_name}:`);
            console.log(`   Weight Range: ${product.min_weight}-${product.max_weight}g (${product.raw_weight_range})`);
            console.log(`   Excel Mapping: ${product.excel_mapping_key}`);
            console.log(`   Excel Column: ${product.excel_column}`);
            console.log(`   Row Hint: ${product.excel_row_hint}`);
            console.log('');
        });
        
        // Check if we have the correct weight ranges
        const correctRanges = ppzLiteProducts.filter(p => 
            (p.min_weight === 141 && p.max_weight === 150) ||
            (p.min_weight === 151 && p.max_weight === 159) ||
            (p.min_weight === 160 && p.max_weight === 166)
        );
        
        const incorrectRanges = ppzLiteProducts.filter(p => 
            !((p.min_weight === 141 && p.max_weight === 150) ||
              (p.min_weight === 151 && p.max_weight === 159) ||
              (p.min_weight === 160 && p.max_weight === 166))
        );
        
        console.log('📊 Weight Range Analysis:');
        console.log(`✅ Correct ranges (141-150, 151-159, 160-166): ${correctRanges.length} products`);
        console.log(`❌ Incorrect ranges: ${incorrectRanges.length} products`);
        
        if (incorrectRanges.length > 0) {
            console.log('\n❌ Products with incorrect weight ranges:');
            incorrectRanges.forEach(product => {
                console.log(`   ${product.mold_name}: ${product.min_weight}-${product.max_weight}g`);
            });
        } else {
            console.log('\n🎉 All PP Z Lite products have correct weight ranges!');
        }
        
        // Also check Z Lite products for comparison
        console.log('\n🔍 Checking Z Lite products for comparison...');
        
        const { data: zLiteProducts, error: zLiteError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*')
            .eq('plastic_name', 'Elite Z Lite')
            .eq('raw_line_type', 'Z Lite')
            .order('mold_name');
        
        if (zLiteError) {
            console.error('❌ Error fetching Z Lite products:', zLiteError);
            return;
        }
        
        console.log(`\nFound ${zLiteProducts.length} Z Lite products:`);
        
        const zLiteCorrect = zLiteProducts.filter(p => 
            (p.min_weight === 141 && p.max_weight === 150) ||
            (p.min_weight === 151 && p.max_weight === 159) ||
            (p.min_weight === 160 && p.max_weight === 166)
        );
        
        console.log(`✅ Z Lite products with correct ranges: ${zLiteCorrect.length}/${zLiteProducts.length}`);
        
        if (zLiteCorrect.length === zLiteProducts.length) {
            console.log('🎉 All Z Lite products also have correct weight ranges!');
        }
        
    } catch (error) {
        console.error('❌ Verification error:', error);
    }
}

verifyPPZLiteFix();
