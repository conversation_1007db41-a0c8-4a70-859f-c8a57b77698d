require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function debugDisc425777Osl19033() {
  try {
    console.log('Debugging why disc 425777 does not match to OSL 19033...');
    
    const discId = 425777;
    const oslId = 19033;
    
    // Get disc details
    const { data: disc, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .eq('id', discId)
      .single();
    
    if (discError) {
      console.error('Error getting disc:', discError);
      return;
    }
    
    // Get OSL details
    const { data: osl, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, min_weight, max_weight, color_id, vendor_id')
      .eq('id', oslId)
      .single();
    
    if (oslError) {
      console.error('Error getting OSL:', oslError);
      return;
    }
    
    console.log('\n=== DISC 425777 DETAILS ===');
    console.log(`MPS ID: ${disc.mps_id}`);
    console.log(`Weight: ${disc.weight}g`);
    console.log(`Weight MFG: ${disc.weight_mfg}g`);
    console.log(`Color ID: ${disc.color_id}`);
    console.log(`Sold Date: ${disc.sold_date || 'NULL (unsold)'}`);
    console.log(`Current order_sheet_line_id: ${disc.order_sheet_line_id}`);
    console.log(`Current vendor_osl_id: ${disc.vendor_osl_id}`);
    
    console.log('\n=== OSL 19033 DETAILS ===');
    console.log(`MPS ID: ${osl.mps_id}`);
    console.log(`Weight Range: ${osl.min_weight}-${osl.max_weight}g`);
    console.log(`Color ID: ${osl.color_id}`);
    console.log(`Vendor ID: ${osl.vendor_id}`);
    
    // Check matching criteria
    console.log('\n=== MATCHING CRITERIA ANALYSIS ===');
    
    const mpsMatch = disc.mps_id === osl.mps_id;
    console.log(`1. MPS ID match (${disc.mps_id} === ${osl.mps_id}): ${mpsMatch ? '✅' : '❌'}`);
    
    const colorMatch = disc.color_id === osl.color_id || osl.color_id === 23;
    console.log(`2. Color match (${disc.color_id} === ${osl.color_id} OR ${osl.color_id} === 23): ${colorMatch ? '✅' : '❌'}`);
    
    const soldMatch = disc.sold_date === null;
    console.log(`3. Unsold disc (sold_date IS NULL): ${soldMatch ? '✅' : '❌'}`);
    
    // Regular weight matching
    const regularWeightMatch = disc.weight >= osl.min_weight && disc.weight <= osl.max_weight;
    console.log(`4. Regular weight match (${disc.weight} >= ${osl.min_weight} AND <= ${osl.max_weight}): ${regularWeightMatch ? '✅' : '❌'}`);
    
    // Manufacturer weight matching
    if (disc.weight_mfg !== null) {
      const roundedWeightMfg = Math.round(disc.weight_mfg);
      const vendorWeightMatch = roundedWeightMfg >= osl.min_weight && roundedWeightMfg <= osl.max_weight;
      console.log(`5. Vendor weight match (ROUND(${disc.weight_mfg}) = ${roundedWeightMfg} >= ${osl.min_weight} AND <= ${osl.max_weight}): ${vendorWeightMatch ? '✅' : '❌'}`);
      
      const regularAllMatch = mpsMatch && colorMatch && soldMatch && regularWeightMatch;
      const vendorAllMatch = mpsMatch && colorMatch && soldMatch && vendorWeightMatch;
      
      console.log(`\nREGULAR MAPPING MATCH: ${regularAllMatch ? '✅' : '❌'}`);
      console.log(`VENDOR MAPPING MATCH: ${vendorAllMatch ? '✅' : '❌'}`);
      
      if (!regularAllMatch && !vendorAllMatch) {
        console.log('\n❌ This disc should NOT match - criteria not met:');
        if (!mpsMatch) console.log('   - MPS mismatch');
        if (!colorMatch) console.log('   - Color mismatch');
        if (!soldMatch) console.log('   - Disc is sold');
        if (!regularWeightMatch) console.log('   - Regular weight outside range');
        if (!vendorWeightMatch) console.log('   - Vendor weight outside range');
      }
    } else {
      console.log('5. Weight MFG is null - cannot do vendor weight matching');
      
      const regularAllMatch = mpsMatch && colorMatch && soldMatch && regularWeightMatch;
      console.log(`\nREGULAR MAPPING MATCH: ${regularAllMatch ? '✅' : '❌'}`);
      
      if (!regularAllMatch) {
        console.log('\n❌ This disc should NOT match - criteria not met:');
        if (!mpsMatch) console.log('   - MPS mismatch');
        if (!colorMatch) console.log('   - Color mismatch');
        if (!soldMatch) console.log('   - Disc is sold');
        if (!regularWeightMatch) console.log('   - Regular weight outside range');
      }
    }
    
    // Check if OSL's MPS has redirect
    console.log('\n=== REDIRECT ANALYSIS ===');
    const { data: mpsRecord, error: mpsError } = await supabase
      .from('t_mps')
      .select('id, order_through_mps_id')
      .eq('id', osl.mps_id)
      .single();
    
    if (!mpsError && mpsRecord) {
      if (mpsRecord.order_through_mps_id) {
        console.log(`⚠️  OSL ${oslId} belongs to MPS ${mpsRecord.id} which redirects to MPS ${mpsRecord.order_through_mps_id}`);
        console.log('   → This OSL should NOT be used for vendor mapping');
        console.log('   → Disc should be mapped to an OSL in the redirect MPS instead');
        
        // Find the correct OSL in the redirect MPS
        console.log(`\n=== FINDING CORRECT OSL IN REDIRECT MPS ${mpsRecord.order_through_mps_id} ===`);
        
        const { data: redirectOsls, error: redirectError } = await supabase
          .from('t_order_sheet_lines')
          .select('id, mps_id, min_weight, max_weight, color_id')
          .eq('mps_id', mpsRecord.order_through_mps_id)
          .in('color_id', disc.color_id === 23 ? [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23] : [disc.color_id, 23]);
        
        if (!redirectError && redirectOsls) {
          console.log(`Found ${redirectOsls.length} OSLs in redirect MPS ${mpsRecord.order_through_mps_id}:`);
          
          redirectOsls.forEach(redirectOsl => {
            const regularMatch = disc.weight >= redirectOsl.min_weight && disc.weight <= redirectOsl.max_weight;
            const vendorMatch = disc.weight_mfg ? Math.round(disc.weight_mfg) >= redirectOsl.min_weight && Math.round(disc.weight_mfg) <= redirectOsl.max_weight : false;
            
            console.log(`  OSL ${redirectOsl.id}: Weight ${redirectOsl.min_weight}-${redirectOsl.max_weight}g, Color ${redirectOsl.color_id}`);
            console.log(`    Regular match: ${regularMatch ? '✅' : '❌'}, Vendor match: ${vendorMatch ? '✅' : '❌'}`);
            
            if (regularMatch || vendorMatch) {
              console.log(`    🎯 CORRECT OSL: ${redirectOsl.id} should be used instead of ${oslId}`);
            }
          });
        }
      } else {
        console.log(`✅ OSL ${oslId} belongs to MPS ${mpsRecord.id} which has no redirect`);
        console.log('   → This OSL can be used for both regular and vendor mapping');
      }
    }
    
    // Check task 311725 details
    console.log('\n=== TASK 311725 ANALYSIS ===');
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .select('id, task_type, payload, status, result, processed_at')
      .eq('id', 311725)
      .single();
    
    if (!taskError && task) {
      console.log(`Task ${task.id}: ${task.task_type}`);
      console.log(`Payload: ${JSON.stringify(task.payload)}`);
      console.log(`Status: ${task.status}`);
      if (task.result) {
        console.log(`Result: ${JSON.stringify(task.result)}`);
      }
      console.log(`Processed: ${task.processed_at || 'Not yet processed'}`);
    }
    
    console.log('\n=== SUMMARY ===');
    console.log('Possible reasons disc 425777 does not match OSL 19033:');
    console.log('1. MPS mismatch');
    console.log('2. Color mismatch');
    console.log('3. Weight outside range');
    console.log('4. Disc is sold');
    console.log('5. OSL belongs to MPS with redirect (vendor mapping skipped)');
    console.log('6. Task processing error');
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

debugDisc425777Osl19033();
