require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function createNewTestTask() {
  try {
    console.log('Creating a new test task to verify updated functionality...');
    
    // Find a disc with weight_mfg data that doesn't have vendor_osl_id set
    const { data: testDisc, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null)
      .is('vendor_osl_id', null)
      .limit(1)
      .single();
    
    if (discError) {
      console.error('Error finding test disc:', discError);
      return;
    }
    
    console.log('Test disc found:', testDisc);
    
    // Create a new test task
    const { data: taskData, error: taskError } = await supabase
      .from('t_task_queue')
      .insert([
        {
          task_type: 'match_disc_to_osl',
          payload: { 
            id: testDisc.id, 
            operation: 'UPDATE',
            old_data: testDisc
          },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: 'test_updated_functionality'
        }
      ])
      .select();
      
    if (taskError) {
      console.error('Error creating test task:', taskError);
      return;
    }
    
    console.log('New test task created:', taskData[0]);
    console.log('\n✅ Task created successfully!');
    console.log('The updated task queue worker should now process this task and set both order_sheet_line_id and vendor_osl_id fields.');
    console.log('\nTo check the result later, run: node check_updated_task_status.cjs');
    
  } catch (err) {
    console.error('Error:', err.message);
  }
}

createNewTestTask();
