import fetch from 'node-fetch';

async function testDiscraftExportWithZeros() {
  try {
    console.log('🧪 Testing Discraft export with 0 values...\n');
    
    // Call the export API
    const response = await fetch('http://localhost:3001/api/discraft/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        includeHeader: false,
        filename: `test_discraft_export_with_zeros_${new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0]}.xlsx`
      })
    });

    if (!response.ok) {
      throw new Error(`Export API returned ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Export completed successfully!');
    console.log(`📄 Filename: ${result.filename}`);
    console.log(`📁 File path: ${result.filePath}`);
    console.log(`📊 Total records processed: ${result.totalRecords}`);
    console.log(`📋 Header included: ${result.headerIncluded}`);
    
    console.log('\n🎉 Test completed! Check the exported file to verify 0 values are included.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testDiscraftExportWithZeros().catch(console.error);
