// check_osl_18494.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkOSL18494() {
  try {
    // Get OSL 18494 details
    const { data: oslData, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('*')
      .eq('id', 18494)
      .single();
    
    if (oslError) {
      console.error('OSL Error:', oslError);
      return;
    }
    
    console.log('OSL 18494 Details:');
    console.log('ID:', oslData.id);
    console.log('MPS ID:', oslData.mps_id);
    console.log('Min Weight:', oslData.min_weight + 'g');
    console.log('Max Weight:', oslData.max_weight + 'g');
    console.log('Color ID:', oslData.color_id);
    console.log('Ready Button:', oslData.ready_button);
    console.log('Ready:', oslData.ready);
    console.log('Todo:', oslData.todo);
    console.log('Shopify Uploaded At:', oslData.shopify_uploaded_at);
    console.log('Shopify Product Uploaded Notes:', oslData.shopify_product_uploaded_notes);
    
    // Check if this would fail the new max_weight criteria
    const maxWeightValid = oslData.max_weight >= 10;
    console.log('\nMax Weight Analysis:');
    console.log('Max Weight (' + oslData.max_weight + 'g) >= 10g:', maxWeightValid);
    
    if (!maxWeightValid) {
      console.log('⚠️  This OSL would be considered NOT READY due to max_weight < 10g (placeholder OSL)');
    } else {
      console.log('✅ This OSL passes the max_weight check');
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkOSL18494();
