CREATE OR REPLACE FUNCTION fn_handle_image_file_name_change()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  RAISE NOTICE 'fn_handle_image_file_name_change fired for t_discs id %', NEW.id;

  -- Case 1: Image added (was NULL, now NOT NULL)
  IF OLD.image_file_name IS NULL AND NEW.image_file_name IS NOT NULL THEN
    BEGIN
      INSERT INTO t_task_queue(task_type, payload, status, scheduled_at, created_at)
      VALUES (
        'insert_new_t_images_record',
        jsonb_build_object('table_name', 't_discs', 'record_id', NEW.id),
        'pending',
        NOW(),
        NOW()
      );
    EXCEPTION WHEN OTHERS THEN
      INSERT INTO t_error_logs(error_message, created_at, context, created_by)
      VALUES (SQLERRM, NOW(), 'Insert task failed (insert image) for t_discs id ' || NEW.id, 'fn_handle_image_file_name_change');
    END;

  -- Case 2: Image removed (was NOT NULL, now NULL)
  ELSIF OLD.image_file_name IS NOT NULL AND NEW.image_file_name IS NULL THEN
    BEGIN
      INSERT INTO t_task_queue(task_type, payload, status, scheduled_at, created_at)
      VALUES (
        'delete_t_images_record',
        jsonb_build_object('table_name', 't_discs', 'record_id', NEW.id),
        'pending',
        NOW(),
        NOW()
      );
    EXCEPTION WHEN OTHERS THEN
      INSERT INTO t_error_logs(error_message, created_at, context, created_by)
      VALUES (SQLERRM, NOW(), 'Insert task failed (delete image) for t_discs id ' || NEW.id, 'fn_handle_image_file_name_change');
    END;

  -- Case 3: Image changed (both not null and different)
  ELSIF OLD.image_file_name IS NOT NULL AND NEW.image_file_name IS NOT NULL
        AND OLD.image_file_name <> NEW.image_file_name THEN
    BEGIN
      INSERT INTO t_task_queue(task_type, payload, status, scheduled_at, created_at)
      VALUES (
        'delete_t_images_record',
        jsonb_build_object('table_name', 't_discs', 'record_id', NEW.id),
        'pending',
        NOW(),
        NOW()
      );

      INSERT INTO t_task_queue(task_type, payload, status, scheduled_at, created_at)
      VALUES (
        'insert_new_t_images_record',
        jsonb_build_object('table_name', 't_discs', 'record_id', NEW.id),
        'pending',
        NOW() + INTERVAL '1 minute',
        NOW()
      );
    EXCEPTION WHEN OTHERS THEN
      INSERT INTO t_error_logs(error_message, created_at, context, created_by)
      VALUES (SQLERRM, NOW(), 'Insert task failed (replace image) for t_discs id ' || NEW.id, 'fn_handle_image_file_name_change');
    END;
  END IF;

  RETURN NEW;
END;
$$;
