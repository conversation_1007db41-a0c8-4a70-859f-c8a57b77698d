import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function fixTask536599AndImage() {
  try {
    console.log('🔧 Fixing Task 536599 and marking image as unverified...\n');
    
    const taskId = 536599;
    const moldId = 300;
    
    // Get the current task details
    const { data: task, error: taskError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('id', taskId)
      .single();
    
    if (taskError) {
      console.error('❌ Error fetching task:', taskError);
      return;
    }
    
    // Get the actual error from the mold record
    const { data: mold, error: moldError } = await supabase
      .from('t_molds')
      .select('shopify_collection_uploaded_notes')
      .eq('id', moldId)
      .single();
    
    if (moldError) {
      console.error('❌ Error fetching mold:', moldError);
      return;
    }
    
    const actualError = mold.shopify_collection_uploaded_notes;
    console.log('📋 Actual error from database:', actualError);
    
    // Update the task result with the correct error message
    const currentResult = typeof task.result === 'string' ? JSON.parse(task.result) : task.result;
    const updatedResult = {
      ...currentResult,
      error: actualError,
      error_source: 'extracted_from_database',
      fixed_at: new Date().toISOString(),
      original_error: currentResult.error
    };
    
    console.log('\n🔄 Updating task result with detailed error...');
    const { error: updateTaskError } = await supabase
      .from('t_task_queue')
      .update({ result: updatedResult })
      .eq('id', taskId);
    
    if (updateTaskError) {
      console.error('❌ Error updating task:', updateTaskError);
      return;
    }
    
    console.log('✅ Task result updated successfully');
    
    // Mark the image as unverified since it's missing
    console.log('\n🖼️ Marking image as unverified...');
    const { data: imageUpdate, error: imageUpdateError } = await supabase
      .from('t_images')
      .update({ 
        image_verified: false,
        updated_at: new Date().toISOString()
      })
      .eq('table_name', 't_molds')
      .eq('record_id', moldId)
      .select();
    
    if (imageUpdateError) {
      console.error('❌ Error updating image record:', imageUpdateError);
      return;
    }
    
    if (imageUpdate && imageUpdate.length > 0) {
      console.log('✅ Image record marked as unverified');
      console.log('Updated image record:', imageUpdate[0]);
    } else {
      console.log('⚠️ No image record found to update');
    }
    
    // Update the mold's todo field with a clear action item
    console.log('\n📝 Updating mold todo with action item...');
    const todoMessage = `IMAGE MISSING: Upload image to http://s3.amazonaws.com/paintball/shopify/molds/300.jpg then retry collection publishing`;
    
    const { error: todoUpdateError } = await supabase
      .from('t_molds')
      .update({ todo: todoMessage })
      .eq('id', moldId);
    
    if (todoUpdateError) {
      console.error('❌ Error updating mold todo:', todoUpdateError);
      return;
    }
    
    console.log('✅ Mold todo updated with action item');
    
    // Check if there are other molds with similar image issues
    console.log('\n🔍 Checking for other molds with similar image issues...');
    const { data: otherMolds, error: otherMoldsError } = await supabase
      .from('t_molds')
      .select('id, mold, shopify_collection_uploaded_notes')
      .like('shopify_collection_uploaded_notes', '%Image upload failed%')
      .neq('id', moldId);
    
    if (otherMoldsError) {
      console.error('❌ Error checking other molds:', otherMoldsError);
    } else if (otherMolds && otherMolds.length > 0) {
      console.log(`⚠️ Found ${otherMolds.length} other molds with image upload failures:`);
      for (const otherMold of otherMolds) {
        console.log(`   - Mold ${otherMold.id} (${otherMold.mold}): ${otherMold.shopify_collection_uploaded_notes.substring(0, 100)}...`);
        
        // Mark these images as unverified too
        const { error: otherImageError } = await supabase
          .from('t_images')
          .update({ 
            image_verified: false,
            updated_at: new Date().toISOString()
          })
          .eq('table_name', 't_molds')
          .eq('record_id', otherMold.id);
        
        if (otherImageError) {
          console.log(`     ❌ Error marking image as unverified for mold ${otherMold.id}: ${otherImageError.message}`);
        } else {
          console.log(`     ✅ Marked image as unverified for mold ${otherMold.id}`);
        }
      }
    } else {
      console.log('✅ No other molds found with image upload failures');
    }
    
    console.log('\n🎉 Fix completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Task ${taskId} error message updated with detailed information`);
    console.log(`   - Image for mold ${moldId} marked as unverified`);
    console.log(`   - Mold todo updated with clear action item`);
    console.log(`   - Checked and fixed other molds with similar issues`);
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

fixTask536599AndImage();
