import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function run(discId, sdasinId) {
  console.log(`\nTesting match for disc ${discId} vs SDASIN ${sdasinId}`);
  const { data: disc, error: de } = await supabase
    .from('t_discs').select('id, mps_id, weight, color_id, sold_date').eq('id', discId).single();
  const { data: s, error: se } = await supabase
    .from('t_sdasins').select('id, mps_id, mps_id2, min_weight, max_weight, color_id').eq('id', sdasinId).single();
  if (de || se) {
    console.error('Fetch error', de || se);
    return;
  }
  console.log('Disc:', disc); console.log('SDASIN:', s);
  const { data: res, error: err } = await supabase.rpc('check_disc_sdasin_match', { disc_id_param: discId, sdasin_id_param: sdasinId });
  console.log('check_disc_sdasin_match:', err || res);
  const { data: after, error: e2 } = await supabase.rpc('match_disc_to_all_sdasins', { disc_id_param: discId });
  if (e2) console.error('recompute error:', e2);
  const { data: join } = await supabase.from('tjoin_discs_sdasins').select('*').eq('disc_id', discId).eq('sdasin_id', sdasinId).order('created_at', { ascending: false }).limit(5);
  console.log('join rows:', join);
}

run(426619, 35390).catch(console.error);

