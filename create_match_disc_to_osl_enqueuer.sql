-- Function to enqueue a task for matching a disc to order sheet lines
CREATE OR REPLACE FUNCTION fn_enqueue_match_disc_to_osl()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert a task into the task queue
    INSERT INTO t_task_queue (
        task_type,
        payload,
        status,
        scheduled_at,
        created_at
    ) VALUES (
        'match_disc_to_osl',
        CASE 
            WHEN TG_OP = 'DELETE' THEN jsonb_build_object('id', OLD.id, 'operation', 'DELETE', 'old_data', row_to_json(OLD)::jsonb)
            WHEN TG_OP = 'UPDATE' THEN jsonb_build_object('id', NEW.id, 'operation', 'UPDATE', 'old_data', row_to_json(OLD)::jsonb)
            ELSE jsonb_build_object('id', NEW.id, 'operation', 'INSERT')
        END,
        'pending',
        NOW(),
        NOW()
    );
    
    RETURN NULL; -- For AFTER triggers
END;
$$ LANGUAGE plpgsql;

-- Create the new trigger
DROP TRIGGER IF EXISTS tr_enqueue_match_disc_to_osl ON t_discs;

CREATE TRIGGER tr_enqueue_match_disc_to_osl
AFTER INSERT OR DELETE OR UPDATE OF 
    mps_id,
    weight,
    color_id,
    color_modifier,
    location,
    sold_date,
    sold_channel,
    order_sheet_line_id,
    veeqo_id,
    veeqo_id_notes,
    shopify_uploaded_notes
ON t_discs
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_match_disc_to_osl();

-- Drop the old trigger
DROP TRIGGER IF EXISTS tr_osl_on_disc_in_up_del ON t_discs;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Disc-to-OSL matching enqueuer function and trigger created.';
END $$;
