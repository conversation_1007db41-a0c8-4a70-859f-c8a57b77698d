import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function cleanupRow22() {
    try {
        console.log('🧹 Cleaning up row 22...\n');
        
        // 1. Check what's in row 22
        console.log('1. Checking what\'s currently in row 22...');
        
        const { data: row22Data, error: row22Error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, excel_row_hint, excel_column, mold_name, plastic_name, raw_model, raw_line_type, excel_mapping_key')
            .eq('excel_row_hint', 22);

        if (row22Error) {
            console.error('❌ Error querying row 22:', row22Error);
            return;
        }

        console.log(`✅ Found ${row22Data.length} records in row 22:`);
        row22Data.forEach((record, index) => {
            console.log(`   ${index + 1}. ID: ${record.id}, Col: ${record.excel_column}, Mold: ${record.mold_name || 'Unknown'}`);
            console.log(`      Raw Model: ${record.raw_model || 'N/A'}`);
            console.log(`      Raw Line Type: ${record.raw_line_type || 'N/A'}`);
            console.log(`      Excel Mapping: ${record.excel_mapping_key || 'N/A'}`);
            console.log('');
        });

        if (row22Data.length === 0) {
            console.log('✅ Row 22 is already clean!');
            return;
        }

        // 2. Delete all records in row 22 (they should not exist)
        console.log('2. Deleting all records in row 22...');
        
        const { data: deletedData, error: deleteError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .delete()
            .eq('excel_row_hint', 22)
            .select();

        if (deleteError) {
            console.error('❌ Error deleting row 22 records:', deleteError);
            return;
        }

        console.log(`✅ Deleted ${deletedData?.length || 0} records from row 22`);

        // 3. Verify row 22 is now clean
        console.log('\n3. Verifying row 22 is now clean...');
        
        const { data: verifyData, error: verifyError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, excel_row_hint, excel_column')
            .eq('excel_row_hint', 22);

        if (verifyError) {
            console.error('❌ Error verifying row 22:', verifyError);
            return;
        }

        if (verifyData.length === 0) {
            console.log('✅ Row 22 is now completely clean!');
        } else {
            console.log(`❌ Row 22 still has ${verifyData.length} records - something went wrong`);
            verifyData.forEach((record, index) => {
                console.log(`   ${index + 1}. ID: ${record.id}, Col: ${record.excel_column}`);
            });
        }

        // 4. Check other header rows too
        console.log('\n4. Checking other header rows...');
        
        const headerRows = [24, 27, 126, 131, 134];
        
        for (const row of headerRows) {
            const { data: headerData, error: headerError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .select('id, excel_row_hint, excel_column, mold_name')
                .eq('excel_row_hint', row);

            if (headerError) {
                console.error(`❌ Error checking row ${row}:`, headerError);
                continue;
            }

            if (headerData.length > 0) {
                console.log(`⚠️  Row ${row} has ${headerData.length} records (should be empty for headers)`);
                headerData.forEach((record, index) => {
                    console.log(`   ${index + 1}. ID: ${record.id}, Col: ${record.excel_column}, Mold: ${record.mold_name || 'Unknown'}`);
                });
            } else {
                console.log(`✅ Row ${row} is clean (no records)`);
            }
        }

        console.log('\n🎉 Row 22 cleanup completed!');
        
    } catch (error) {
        console.error('❌ Cleanup failed:', error.message);
    }
}

cleanupRow22().catch(console.error);
