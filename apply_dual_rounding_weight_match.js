// apply_dual_rounding_weight_match.js
// Applies dual rounding (.50–.85 also checks floor(weight)) to matching functions
// Functions affected:
//  - create_disc_sdasin_match_function.sql
//  - create_match_disc_to_all_sdasins_function.sql
//  - create_match_sdasin_to_all_discs_function.sql

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs/promises';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function readFunctionSqlOnly(path) {
  const content = await fs.readFile(path, 'utf8');
  // Remove any trailing confirmation DO $$ blocks
  const splitOnConfirm = content.split(/\n-- Confirmation message[\s\S]*/i)[0];
  const splitOnDO = splitOnConfirm.split(/\nDO \$\$[\s\S]*?\$\$;?/i)[0];
  return splitOnDO.trim();
}

async function ensureExecSqlExists() {
  try {
    console.log('Ensuring exec_sql() exists...');
    const sql = await fs.readFile('create_exec_sql_function.sql', 'utf8');
    const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
    if (error) {
      // exec_sql may not exist yet; try to create it via Postgres function creation context not available.
      // In past flows, this SQL needed to be applied manually with elevated perms.
      console.warn('exec_sql() call failed; attempting to apply create_exec_sql_function.sql directly via exec_sql (may fail if missing perms).');
      throw error;
    }
    console.log('exec_sql() is available.');
  } catch (err) {
    console.warn('Could not confirm/create exec_sql via RPC. If exec_sql already exists, we can proceed. Otherwise, the next calls will fail with a clear error.');
  }
}

async function applyFunctionFromFile(path, name) {
  const sql = await readFunctionSqlOnly(path);
  console.log(`\nApplying function: ${name}`);
  const { error } = await supabase.rpc('exec_sql', { sql_query: sql });
  if (error) {
    console.error(`❌ Failed applying ${name}: ${error.message}`);
    throw error;
  }
  console.log(`✅ Applied ${name}`);
}

async function main() {
  try {
    if (!process.env.SUPABASE_URL || !process.env.SUPABASE_KEY) {
      throw new Error('Missing SUPABASE_URL or SUPABASE_KEY in environment.');
    }

    await ensureExecSqlExists();

    // Apply the three updated functions
    await applyFunctionFromFile('create_disc_sdasin_match_function.sql', 'check_disc_sdasin_match');
    await applyFunctionFromFile('create_match_disc_to_all_sdasins_function.sql', 'match_disc_to_all_sdasins');
    await applyFunctionFromFile('create_match_sdasin_to_all_discs_function.sql', 'match_sdasin_to_all_discs');

    console.log('\nAll functions applied successfully.');
  } catch (err) {
    console.error('\n💥 Error applying dual rounding weight match updates:', err.message || err);
    process.exit(1);
  }
}

main();

