<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Browser</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            margin-top: 0;
        }
        .breadcrumb {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f0f0f0;
            border-radius: 5px;
        }
        .breadcrumb a {
            color: #0066cc;
            text-decoration: none;
        }
        .breadcrumb a:hover {
            text-decoration: underline;
        }
        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 10px;
        }
        .new-folder-form {
            display: flex;
            align-items: center;
        }
        .new-folder-form input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-right: 10px;
        }
        .view-options {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .view-options select, .view-options button {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
        }
        .view-options button {
            cursor: pointer;
            background-color: #f0f0f0;
        }
        .view-options button:hover {
            background-color: #e0e0e0;
        }
        .view-options button.active {
            background-color: #4CAF50;
            color: white;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 10px;
        }
        .pagination button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            cursor: pointer;
        }
        .pagination button:hover {
            background-color: #f0f0f0;
        }
        .pagination button:disabled {
            background-color: #f0f0f0;
            color: #aaa;
            cursor: not-allowed;
        }
        .pagination-info {
            margin-top: 10px;
            text-align: center;
            color: #666;
        }
        .new-folder-form button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .new-folder-form button:hover {
            background-color: #45a049;
        }
        .message {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .content {
            display: flex;
            flex-wrap: wrap;
        }
        .directory, .file {
            margin: 10px;
            padding: 15px;
            border-radius: 5px;
            width: 200px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .directory:hover, .file:hover {
            transform: scale(1.05);
        }
        .directory {
            background-color: #e6f2ff;
            border: 1px solid #b3d9ff;
        }
        .file {
            background-color: #f0f0f0;
            border: 1px solid #ddd;
        }
        .image-preview {
            width: 100%;
            height: 150px;
            object-fit: contain;
            margin-bottom: 10px;
        }
        .loading {
            text-align: center;
            padding: 20px;
            font-style: italic;
            color: #666;
        }
        .error {
            color: red;
            padding: 20px;
            text-align: center;
        }
        .metadata {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .metadata h3 {
            margin-top: 0;
        }
        .metadata-table {
            width: 100%;
            border-collapse: collapse;
        }
        .metadata-table th, .metadata-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .metadata-table th {
            background-color: #f2f2f2;
        }
        .image-viewer {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .image-viewer img {
            max-width: 90%;
            max-height: 90%;
        }
        .image-viewer .close {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 30px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Image Browser</h1>
        <div class="breadcrumb" id="breadcrumb">
            <a href="#" data-path="">Home</a>
        </div>

        <div class="controls">
            <div class="new-folder-form">
                <input type="text" id="folderName" placeholder="New folder name">
                <button id="createFolderBtn">Create Folder</button>
            </div>

            <div class="view-options">
                <button id="latestFilesBtn" title="Show only the latest files">Latest Files</button>

                <select id="sortSelect" title="Sort by">
                    <option value="name-asc">Name (A-Z)</option>
                    <option value="name-desc">Name (Z-A)</option>
                    <option value="date-desc">Date (Newest)</option>
                    <option value="date-asc">Date (Oldest)</option>
                    <option value="size-desc">Size (Largest)</option>
                    <option value="size-asc">Size (Smallest)</option>
                </select>

                <select id="pageSizeSelect" title="Items per page">
                    <option value="20">20 per page</option>
                    <option value="50" selected>50 per page</option>
                    <option value="100">100 per page</option>
                    <option value="200">200 per page</option>
                </select>
            </div>
        </div>

        <div id="message" class="message"></div>

        <div id="content">
            <div class="loading">Loading...</div>
        </div>

        <div id="pagination" class="pagination" style="display: none;">
            <button id="prevPageBtn" disabled>&laquo; Previous</button>
            <button id="nextPageBtn" disabled>Next &raquo;</button>
        </div>
        <div id="paginationInfo" class="pagination-info" style="display: none;"></div>

        <div id="metadata" class="metadata" style="display: none;">
            <h3>Metadata</h3>
            <table class="metadata-table" id="metadata-table">
                <tr>
                    <th>Property</th>
                    <th>Value</th>
                </tr>
            </table>
        </div>
    </div>

    <div class="image-viewer" id="image-viewer">
        <span class="close" id="close-viewer">&times;</span>
        <img id="viewer-image" src="" alt="Full size image">
    </div>

    <script>
        // API base URL
        const API_BASE_URL = 'http://localhost:3005';

        // Current path and view state
        let currentPath = '';
        let currentPage = 1;
        let currentPageSize = 50;
        let currentSort = 'name';
        let currentOrder = 'asc';
        let showLatestOnly = false;
        let latestCount = 20;

        // DOM elements
        const contentElement = document.getElementById('content');
        const breadcrumbElement = document.getElementById('breadcrumb');
        const metadataElement = document.getElementById('metadata');
        const metadataTableElement = document.getElementById('metadata-table');
        const imageViewer = document.getElementById('image-viewer');
        const viewerImage = document.getElementById('viewer-image');
        const closeViewer = document.getElementById('close-viewer');
        const folderNameInput = document.getElementById('folderName');
        const createFolderBtn = document.getElementById('createFolderBtn');
        const messageElement = document.getElementById('message');
        const latestFilesBtn = document.getElementById('latestFilesBtn');
        const sortSelect = document.getElementById('sortSelect');
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        const paginationElement = document.getElementById('pagination');
        const paginationInfoElement = document.getElementById('paginationInfo');
        const prevPageBtn = document.getElementById('prevPageBtn');
        const nextPageBtn = document.getElementById('nextPageBtn');

        // Load directory contents
        async function loadDirectory(path = '', resetPage = true) {
            try {
                contentElement.innerHTML = '<div class="loading">Loading...</div>';
                metadataElement.style.display = 'none';
                paginationElement.style.display = 'none';
                paginationInfoElement.style.display = 'none';

                // Reset page when changing directories
                if (resetPage && path !== currentPath) {
                    currentPage = 1;
                }

                // Build query parameters
                const params = new URLSearchParams({
                    path: path,
                    page: currentPage,
                    pageSize: currentPageSize,
                    sort: currentSort,
                    order: currentOrder,
                    latestOnly: showLatestOnly,
                    latestCount: latestCount
                });

                const response = await fetch(`${API_BASE_URL}/api/list?${params.toString()}`);
                if (!response.ok) {
                    throw new Error(`Error: ${response.status} ${response.statusText}`);
                }

                const data = await response.json();
                currentPath = data.path;

                // Update breadcrumb
                updateBreadcrumb(currentPath);

                // Display contents
                displayContents(data);

                // Update UI state based on response
                updateUIState(data);
            } catch (error) {
                contentElement.innerHTML = `<div class="error">Error loading directory: ${error.message}</div>`;
            }
        }

        // Update UI state based on response data
        function updateUIState(data) {
            // Update pagination controls
            if (data.pagination) {
                const { page, pageSize, totalItems, totalPages, hasNextPage, hasPrevPage, totalDirectories, totalFiles } = data.pagination;

                // Update pagination buttons
                prevPageBtn.disabled = !hasPrevPage;
                nextPageBtn.disabled = !hasNextPage;

                // Show pagination if there are multiple pages
                paginationElement.style.display = totalPages > 1 ? 'flex' : 'none';

                // Update pagination info
                paginationInfoElement.textContent = `Page ${page} of ${totalPages} (${totalDirectories} directories, ${totalFiles} files)`;
                paginationInfoElement.style.display = 'block';

                // Update current state
                currentPage = page;
                currentPageSize = pageSize;
            }

            // Update sorting and filtering UI
            if (data.sorting) {
                const { sort, order } = data.sorting;
                currentSort = sort;
                currentOrder = order;

                // Update sort select
                sortSelect.value = `${sort}-${order}`;
            }

            if (data.filtering) {
                const { latestOnly, latestCount: count } = data.filtering;
                showLatestOnly = latestOnly;
                latestCount = count;

                // Update latest files button
                latestFilesBtn.classList.toggle('active', showLatestOnly);
            }
        }

        // Update breadcrumb navigation
        function updateBreadcrumb(path) {
            // Start with home link
            let breadcrumbHtml = '<a href="#" data-path="">Home</a>';

            if (path) {
                // Split path into parts
                const parts = path.split('/');
                let currentPath = '';

                // Add each part as a breadcrumb link
                for (let i = 0; i < parts.length; i++) {
                    currentPath += (i > 0 ? '/' : '') + parts[i];
                    breadcrumbHtml += ` &gt; <a href="#" data-path="${currentPath}">${parts[i]}</a>`;
                }
            }

            breadcrumbElement.innerHTML = breadcrumbHtml;

            // Add event listeners to breadcrumb links
            const links = breadcrumbElement.querySelectorAll('a');
            links.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    loadDirectory(link.getAttribute('data-path'));
                });
            });
        }

        // Display directory contents
        function displayContents(data) {
            const dirList = data.directories || [];
            const fileList = data.files || [];

            if (!dirList.length && !fileList.length) {
                contentElement.innerHTML = '<div class="loading">No files or directories found</div>';
                return;
            }

            let html = '<div class="content">';

            // Add directories
            dirList.forEach(dir => {
                html += `
                    <div class="directory" data-path="${dir.path}">
                        <div style="font-size: 48px;">📁</div>
                        <div>${dir.name}</div>
                    </div>
                `;
            });

            // Add files
            fileList.forEach(file => {
                if (file.isImage) {
                    html += `
                        <div class="file" data-path="${file.path}" data-metadata-url="${file.metadataUrl}">
                            <img src="${API_BASE_URL}${file.url}" class="image-preview" alt="${file.name}">
                            <div>${file.name}</div>
                        </div>
                    `;
                } else {
                    html += `
                        <div class="file" data-path="${file.path}" data-metadata-url="${file.metadataUrl}">
                            <div style="font-size: 48px;">📄</div>
                            <div>${file.name}</div>
                        </div>
                    `;
                }
            });

            html += '</div>';
            contentElement.innerHTML = html;

            // Add event listeners to directories
            const dirElements = contentElement.querySelectorAll('.directory');
            dirElements.forEach(dir => {
                dir.addEventListener('click', () => {
                    loadDirectory(dir.getAttribute('data-path'));
                });
            });

            // Add event listeners to files
            const fileElements = contentElement.querySelectorAll('.file');
            fileElements.forEach(file => {
                file.addEventListener('click', async () => {
                    const path = file.getAttribute('data-path');
                    const metadataUrl = file.getAttribute('data-metadata-url');

                    try {
                        // Load metadata
                        const response = await fetch(`${API_BASE_URL}${metadataUrl}`);
                        if (!response.ok) {
                            throw new Error(`Error: ${response.status} ${response.statusText}`);
                        }

                        const data = await response.json();
                        displayMetadata(data.metadata);

                        // If it's an image, make it clickable to open in viewer
                        const img = file.querySelector('img');
                        if (img) {
                            img.addEventListener('click', (e) => {
                                e.stopPropagation(); // Prevent triggering the file click event
                                openImageViewer(`${API_BASE_URL}/static/${encodeURIComponent(path)}`);
                            });
                        }
                    } catch (error) {
                        console.error('Error loading metadata:', error);
                    }
                });
            });
        }

        // Display metadata
        function displayMetadata(metadata) {
            let tableHtml = '<tr><th>Property</th><th>Value</th></tr>';

            for (const [key, value] of Object.entries(metadata)) {
                tableHtml += `
                    <tr>
                        <td>${key}</td>
                        <td>${formatMetadataValue(key, value)}</td>
                    </tr>
                `;
            }

            metadataTableElement.innerHTML = tableHtml;
            metadataElement.style.display = 'block';
        }

        // Format metadata value based on key
        function formatMetadataValue(key, value) {
            if (value === null || value === undefined) {
                return 'N/A';
            }

            if (key === 'size') {
                return formatFileSize(value);
            }

            if (key === 'created' || key === 'modified') {
                return new Date(value).toLocaleString();
            }

            if (typeof value === 'boolean') {
                return value ? 'Yes' : 'No';
            }

            return value.toString();
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';

            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Open image viewer
        function openImageViewer(src) {
            viewerImage.src = src;
            imageViewer.style.display = 'flex';
        }

        // Close image viewer
        closeViewer.addEventListener('click', () => {
            imageViewer.style.display = 'none';
        });

        // Close viewer when clicking outside the image
        imageViewer.addEventListener('click', (e) => {
            if (e.target === imageViewer) {
                imageViewer.style.display = 'none';
            }
        });

        // Create a new folder
        async function createFolder(name) {
            try {
                // Clear previous messages
                hideMessage();

                // Validate folder name
                if (!name || name.trim() === '') {
                    showMessage('Please enter a folder name', 'error');
                    return;
                }

                // Send request to create folder
                const response = await fetch(`${API_BASE_URL}/api/directory`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        path: currentPath,
                        name: name.trim()
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showMessage(data.message, 'success');
                    folderNameInput.value = ''; // Clear input
                    loadDirectory(currentPath); // Reload current directory
                } else {
                    showMessage(data.error || data.message || 'Error creating folder', 'error');
                }
            } catch (error) {
                showMessage(`Error: ${error.message}`, 'error');
            }
        }

        // Show a message
        function showMessage(text, type) {
            messageElement.textContent = text;
            messageElement.className = `message ${type}`;
            messageElement.style.display = 'block';

            // Auto-hide after 5 seconds
            setTimeout(() => {
                hideMessage();
            }, 5000);
        }

        // Hide the message
        function hideMessage() {
            messageElement.style.display = 'none';
        }

        // Event listener for create folder button
        createFolderBtn.addEventListener('click', () => {
            createFolder(folderNameInput.value);
        });

        // Event listener for Enter key in folder name input
        folderNameInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                createFolder(folderNameInput.value);
            }
        });

        // Event listener for pagination
        prevPageBtn.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                loadDirectory(currentPath, false);
            }
        });

        nextPageBtn.addEventListener('click', () => {
            currentPage++;
            loadDirectory(currentPath, false);
        });

        // Event listener for sort select
        sortSelect.addEventListener('change', () => {
            const [sort, order] = sortSelect.value.split('-');
            currentSort = sort;
            currentOrder = order;
            loadDirectory(currentPath, false);
        });

        // Event listener for page size select
        pageSizeSelect.addEventListener('change', () => {
            currentPageSize = parseInt(pageSizeSelect.value);
            currentPage = 1; // Reset to first page
            loadDirectory(currentPath, false);
        });

        // Event listener for latest files button
        latestFilesBtn.addEventListener('click', () => {
            showLatestOnly = !showLatestOnly;
            latestFilesBtn.classList.toggle('active', showLatestOnly);
            currentPage = 1; // Reset to first page
            loadDirectory(currentPath, false);
        });

        // Load initial directory
        document.addEventListener('DOMContentLoaded', () => {
            loadDirectory();
        });
    </script>
</body>
</html>
