// publishCollectionBrand.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import minimist from 'minimist';

// For Node 18+ the global fetch is available.
// If needed, install and import node-fetch for earlier versions.
// import fetch from 'node-fetch';

// Parse command-line arguments (expecting --id=<brandId>)
const args = minimist(process.argv.slice(2));
const brandId = args.id;
if (!brandId) {
  console.error('No brand id provided. Use --id=<brandId>');
  process.exit(1);
}

console.log(`INFO: Received brand id: ${brandId}`);

// Initialize Supabase client using your service key.
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('ERROR: Missing Supabase URL or key in environment variables.');
  process.exit(1);
}
console.log(`INFO: Initializing Supabase client...`);
const supabase = createClient(supabaseUrl, supabaseKey);

// Derive Shopify REST endpoint for smart collections.
// Your SHOPIFY_ENDPOINT is assumed to be the GraphQL endpoint;
// here we replace "graphql.json" with "smart_collections.json".
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
if (!shopifyEndpoint || !shopifyAccessToken) {
  console.error('ERROR: Missing Shopify endpoint or access token in environment variables.');
  process.exit(1);
}
const smartCollectionsEndpoint = shopifyEndpoint.replace('graphql.json', 'smart_collections.json');
console.log(`INFO: Shopify smart collections endpoint: ${smartCollectionsEndpoint}`);

/**
 * Generates a handle from the given title.
 * Mimics the transformation:
 *   LCase(Replace(Replace(Replace(Replace(Replace(Replace(Replace(Replace(Replace([Title]," ","-"),"'",""),"/",""),"&","-"),"(",""),")",""),Chr(34),""),"--","-"),"--","-"))
 *
 * @param {string} title - The original title.
 * @returns {string} - The transformed handle.
 */
function generateHandle(title) {
  let handle = title.toLowerCase();
  handle = handle.replace(/ /g, '-');    // Replace spaces with dashes
  handle = handle.replace(/'/g, '');     // Remove apostrophes
  handle = handle.replace(/\//g, '');    // Remove forward slashes
  handle = handle.replace(/&/g, '-');    // Replace ampersands with dashes
  handle = handle.replace(/\(/g, '');    // Remove (
  handle = handle.replace(/\)/g, '');    // Remove )
  handle = handle.replace(/"/g, '');     // Remove double quotes
  while (handle.includes('--')) {
    handle = handle.replace(/--/g, '-');
  }
  return handle;
}

/**
 * Creates a smart collection on Shopify using the REST Admin API.
 *
 * Adds two rules:
 * 1) Tag == brandRecord.shopify_tag
 * 2) variant_inventory > 0
 * and uses disjunctive: false, meaning both rules must be satisfied.
 *
 * @param {object} brandRecord - A record from t_brands.
 * @returns {Promise<object>} - The created smart collection details.
 */
async function createShopifySmartCollection(brandRecord) {
  const payload = {
    smart_collection: {
      title: brandRecord.brand,
      body_html: brandRecord.seo_meta_description, // "body_html" is the description in REST.
      handle: generateHandle(brandRecord.brand),
      sort_order: "best-selling",     // Valid Shopify sort order
      published: true,
      published_scope: "global",    // Ensures updates on all sales changes

      // All rules must match => AND logic
      disjunctive: false,
      rules: [
        {
          column: "tag",
          relation: "equals",
          condition: brandRecord.shopify_tag
        },
        {
          column: "variant_inventory",
          relation: "greater_than",
          condition: "0"
        }
      ]
    }
  };

  const response = await fetch(smartCollectionsEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken
    },
    body: JSON.stringify(payload)
  });
  
  const result = await response.json();
  if (!response.ok) {
    throw new Error(`Error creating smart collection for "${brandRecord.brand}": ${JSON.stringify(result)}`);
  }
  return result.smart_collection;
}

/**
 * A helper function that returns a promise resolving after the given number of milliseconds.
 * @param {number} ms - Milliseconds to wait.
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function main() {
  // Always wait 5 seconds to allow the view (v_todo_brands) to refresh.
  console.log("INFO: Waiting 5 seconds for v_todo_brands to update...");
  await delay(5000);
  
  // Check if the brand id is present in v_todo_brands.
  console.log(`INFO: Checking if brand id ${brandId} is present in v_todo_brands...`);
  const { data: todoRecords, error: todoError } = await supabase
    .from('v_todo_brands')
    .select('id')
    .eq('id', brandId);
  
  if (todoError) {
    console.error('ERROR checking v_todo_brands:', todoError);
    process.exit(1);
  }
  
  if (todoRecords && todoRecords.length > 0) {
    const noteMessage = `After delay: Brand id ${brandId} is present in v_todo_brands and is NOT ready to be processed.`;
    console.log(`INFO: ${noteMessage}`);
    const { error: noteError } = await supabase
      .from('t_brands')
      .update({ shopify_publish_collection_notes: noteMessage })
      .eq('id', brandId);
    if (noteError) {
      console.error(`ERROR: Failed to update shopify_publish_collection_notes for brand id ${brandId}: ${noteError.message}`);
    } else {
      console.log(`INFO: Successfully updated shopify_publish_collection_notes for brand id ${brandId}.`);
    }
    process.exit(0);
  }
  
  // Retrieve the brand record from t_brands.
  console.log(`INFO: Fetching brand record with id ${brandId} from t_brands...`);
  const { data: brandRecord, error: brandError } = await supabase
    .from('t_brands')
    .select('*')
    .eq('id', brandId)
    .single();
  
  if (brandError) {
    console.error('ERROR: fetching brand from t_brands:', brandError);
    process.exit(1);
  }
  
  // Verify the record is eligible for publishing:
  // - shopify must be true
  // - shopify_collection_created_at must be null.
  console.log("INFO: Verifying brand eligibility for publishing...");
  if (!brandRecord.shopify || brandRecord.shopify_collection_created_at !== null) {
    const noteMessage = 'Brand is not eligible for publishing.';
    console.error(`ERROR: ${noteMessage}`);
    const { error: noteError } = await supabase
      .from('t_brands')
      .update({ shopify_publish_collection_notes: noteMessage })
      .eq('id', brandId);
    if (noteError) {
      console.error(`ERROR: Failed to update shopify_publish_collection_notes for brand ${brandRecord.brand}: ${noteError.message}`);
    }
    process.exit(1);
  }
  
  // Attempt to create the smart collection on Shopify.
  try {
    console.log("INFO: Creating Shopify smart collection for brand...");
    const collection = await createShopifySmartCollection(brandRecord);
    console.log(`INFO: Successfully created collection for brand ${brandRecord.brand}:`, collection);
    
    // Update t_brands: set shopify_collection_created_at to now and update the success note.
    const successNote = "Success! Brand Collection created on Shopify through webhook > .js > Shopify API.";
    console.log("INFO: Updating t_brands record with success note and current timestamp...");
    const { error: updateError } = await supabase
      .from('t_brands')
      .update({
        shopify_collection_created_at: new Date().toISOString(),
        shopify_publish_collection_notes: successNote
      })
      .eq('id', brandId);
    if (updateError) {
      console.error(`ERROR: Failed to update shopify_collection_created_at for brand ${brandRecord.brand}:`, updateError);
      process.exit(1);
    }
    console.log(`INFO: Updated shopify_collection_created_at for brand ${brandRecord.brand} with success note.`);
  } catch (err) {
    console.error(`ERROR: Failed to create collection for brand ${brandRecord.brand}: ${err.message}`);
    // On error, update shopify_publish_collection_notes with the error message.
    const { error: noteError } = await supabase
      .from('t_brands')
      .update({ shopify_publish_collection_notes: err.message })
      .eq('id', brandId);
    if (noteError) {
      console.error(`ERROR: Failed to update shopify_publish_collection_notes for brand ${brandRecord.brand}: ${noteError.message}`);
    }
    process.exit(1);
  }
  
  console.log("INFO: publishCollectionBrand process completed successfully.");
}

main().catch(err => {
  console.error('ERROR: Unexpected error:', err);
  process.exit(1);
});
