-- Function to create an Informed Repricer data snapshot
-- This function analyzes the current data in it_infor_all_fields and creates a summary record in rpt_informed

CREATE OR REPLACE FUNCTION public.fn_create_informed_snapshot(
    p_notes TEXT DEFAULT NULL
)
RETURNS TABLE(
    snapshot_id INTEGER,
    snapshot_date TIMESTAMPTZ,
    active_fbm_listing_count INTEGER,
    active_fba_listing_count INTEGER,
    active_fbm_buybox_count_yes INTEGER,
    active_fbm_buybox_count_no INTEGER,
    active_fba_buybox_count_yes INTEGER,
    active_fba_buybox_count_no INTEGER,
    total_active_listings INTEGER,
    fbm_buybox_rate DECIMAL,
    fba_buybox_rate DECIMAL,
    overall_buybox_rate DECIMAL
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_snapshot_id INTEGER;
    v_fbm_listing_count INTEGER := 0;
    v_fba_listing_count INTEGER := 0;
    v_fbm_buybox_yes INTEGER := 0;
    v_fbm_buybox_no INTEGER := 0;
    v_fba_buybox_yes INTEGER := 0;
    v_fba_buybox_no INTEGER := 0;
    v_snapshot_date TIMESTAMPTZ;
BEGIN
    -- Set snapshot timestamp
    v_snapshot_date := NOW();
    
    -- Log the start of snapshot creation
    RAISE NOTICE 'Creating Informed snapshot at %', v_snapshot_date;
    
    -- Count active FBM listings (stock > 0 and LISTING_TYPE = 'Amazon MFN')
    SELECT COUNT(*)
    INTO v_fbm_listing_count
    FROM public.it_infor_all_fields
    WHERE COALESCE("STOCK", 0) > 0 
      AND UPPER(TRIM(COALESCE("LISTING_TYPE", ''))) = 'AMAZON MFN';
    
    -- Count active FBA listings (stock > 0 and LISTING_TYPE = 'Amazon FBA')
    SELECT COUNT(*)
    INTO v_fba_listing_count
    FROM public.it_infor_all_fields
    WHERE COALESCE("STOCK", 0) > 0 
      AND UPPER(TRIM(COALESCE("LISTING_TYPE", ''))) = 'AMAZON FBA';
    
    -- Count FBM listings with buy box (BUYBOX_WINNER = 'YES')
    SELECT COUNT(*)
    INTO v_fbm_buybox_yes
    FROM public.it_infor_all_fields
    WHERE COALESCE("STOCK", 0) > 0 
      AND UPPER(TRIM(COALESCE("LISTING_TYPE", ''))) = 'AMAZON MFN'
      AND UPPER(TRIM(COALESCE("BUYBOX_WINNER", ''))) = 'YES';
    
    -- Count FBM listings without buy box (BUYBOX_WINNER = 'NO')
    SELECT COUNT(*)
    INTO v_fbm_buybox_no
    FROM public.it_infor_all_fields
    WHERE COALESCE("STOCK", 0) > 0 
      AND UPPER(TRIM(COALESCE("LISTING_TYPE", ''))) = 'AMAZON MFN'
      AND UPPER(TRIM(COALESCE("BUYBOX_WINNER", ''))) = 'NO';
    
    -- Count FBA listings with buy box (BUYBOX_WINNER = 'YES')
    SELECT COUNT(*)
    INTO v_fba_buybox_yes
    FROM public.it_infor_all_fields
    WHERE COALESCE("STOCK", 0) > 0 
      AND UPPER(TRIM(COALESCE("LISTING_TYPE", ''))) = 'AMAZON FBA'
      AND UPPER(TRIM(COALESCE("BUYBOX_WINNER", ''))) = 'YES';
    
    -- Count FBA listings without buy box (BUYBOX_WINNER = 'NO')
    SELECT COUNT(*)
    INTO v_fba_buybox_no
    FROM public.it_infor_all_fields
    WHERE COALESCE("STOCK", 0) > 0 
      AND UPPER(TRIM(COALESCE("LISTING_TYPE", ''))) = 'AMAZON FBA'
      AND UPPER(TRIM(COALESCE("BUYBOX_WINNER", ''))) = 'NO';
    
    -- Log the counts
    RAISE NOTICE 'FBM Listings: %, FBA Listings: %', v_fbm_listing_count, v_fba_listing_count;
    RAISE NOTICE 'FBM BuyBox Yes: %, No: %', v_fbm_buybox_yes, v_fbm_buybox_no;
    RAISE NOTICE 'FBA BuyBox Yes: %, No: %', v_fba_buybox_yes, v_fba_buybox_no;
    
    -- Insert the snapshot record
    INSERT INTO public.rpt_informed (
        snapshot_date,
        active_fbm_listing_count,
        active_fba_listing_count,
        active_fbm_buybox_count_yes,
        active_fbm_buybox_count_no,
        active_fba_buybox_count_yes,
        active_fba_buybox_count_no,
        notes
    ) VALUES (
        v_snapshot_date,
        v_fbm_listing_count,
        v_fba_listing_count,
        v_fbm_buybox_yes,
        v_fbm_buybox_no,
        v_fba_buybox_yes,
        v_fba_buybox_no,
        p_notes
    )
    RETURNING id INTO v_snapshot_id;
    
    -- Return the snapshot data
    RETURN QUERY
    SELECT 
        r.id,
        r.snapshot_date,
        r.active_fbm_listing_count,
        r.active_fba_listing_count,
        r.active_fbm_buybox_count_yes,
        r.active_fbm_buybox_count_no,
        r.active_fba_buybox_count_yes,
        r.active_fba_buybox_count_no,
        r.total_active_listings,
        r.fbm_buybox_rate,
        r.fba_buybox_rate,
        r.overall_buybox_rate
    FROM public.rpt_informed r
    WHERE r.id = v_snapshot_id;
    
    RAISE NOTICE 'Created Informed snapshot with ID: %', v_snapshot_id;
END;
$$;
