-- Create table for Dynamic Discs Distribution test data
-- This table will store both product and variant information in a flattened structure

CREATE TABLE test_dd_data (
    id SERIAL PRIMARY KEY,
    
    -- Product level fields
    product_id BIGINT,
    product_title TEXT,
    product_handle TEXT,
    product_body_html TEXT,
    product_published_at TIMESTAMPTZ,
    product_created_at TIMESTAMPTZ,
    product_updated_at TIMESTAMPTZ,
    product_vendor TEXT,
    product_product_type TEXT,
    product_tags TEXT[], -- Array of tags
    product_available BOOLEAN,
    
    -- Variant level fields
    variant_id BIGINT,
    variant_title TEXT,
    variant_option1 TEXT, -- Usually color
    variant_option2 TEXT, -- Usually weight
    variant_option3 TEXT, -- Usually plastic type or other
    variant_sku TEXT,
    variant_requires_shipping BOOLEAN,
    variant_taxable BOOLEAN,
    variant_available BOOLEAN,
    variant_price DECIMAL(10,2),
    variant_grams INTEGER,
    variant_compare_at_price DECIMAL(10,2),
    variant_position INTEGER,
    variant_created_at TIMESTAMPTZ,
    variant_updated_at TIMESTAMPTZ,
    
    -- Featured image for variant
    variant_featured_image_id BIGINT,
    variant_featured_image_src TEXT,
    variant_featured_image_width INTEGER,
    variant_featured_image_height INTEGER,
    
    -- Product options (for reference)
    option1_name TEXT,
    option1_values TEXT[],
    option2_name TEXT,
    option2_values TEXT[],
    option3_name TEXT,
    option3_values TEXT[],
    
    -- Metadata
    source_file TEXT, -- Which ddp file this came from
    imported_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX idx_test_dd_data_product_id ON test_dd_data(product_id);
CREATE INDEX idx_test_dd_data_variant_id ON test_dd_data(variant_id);
CREATE INDEX idx_test_dd_data_vendor ON test_dd_data(product_vendor);
CREATE INDEX idx_test_dd_data_product_type ON test_dd_data(product_product_type);
CREATE INDEX idx_test_dd_data_variant_sku ON test_dd_data(variant_sku);
CREATE INDEX idx_test_dd_data_variant_available ON test_dd_data(variant_available);
CREATE INDEX idx_test_dd_data_source_file ON test_dd_data(source_file);

-- Add comments for documentation
COMMENT ON TABLE test_dd_data IS 'Test table for Dynamic Discs Distribution product data from JSON files';
COMMENT ON COLUMN test_dd_data.product_id IS 'Shopify product ID';
COMMENT ON COLUMN test_dd_data.variant_id IS 'Shopify variant ID';
COMMENT ON COLUMN test_dd_data.product_tags IS 'Array of product tags';
COMMENT ON COLUMN test_dd_data.variant_price IS 'Variant price in dollars';
COMMENT ON COLUMN test_dd_data.variant_grams IS 'Variant weight in grams';
COMMENT ON COLUMN test_dd_data.source_file IS 'Source file name (ddp1.txt, ddp2.txt, etc.)';
