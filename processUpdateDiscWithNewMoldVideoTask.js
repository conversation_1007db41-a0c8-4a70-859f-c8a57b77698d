// processUpdateDiscWithNewMoldVideoTask.js
// Update a Shopify product for a disc to use the video template and set videourl metafield

import fetch from 'node-fetch';

const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

// GraphQL helper
async function shopifyGraphQLRequest(query, variables = {}) {
  const response = await fetch(shopifyEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken
    },
    body: JSON.stringify({ query, variables })
  });
  const result = await response.json();
  if (result.errors) {
    throw new Error(`Shopify GraphQL errors: ${JSON.stringify(result.errors)}`);
  }
  return result.data;
}

// Find product by disc SKU (variant SKU = 'D' + discId)
async function findProductByDiscId(discId) {
  const sku = `D${discId}`;
  const query = `
    query getVariantBySku($query: String!) {
      productVariants(first: 1, query: $query) {
        edges { node { id sku product { id handle } } }
      }
    }
  `;
  const data = await shopifyGraphQLRequest(query, { query: `sku:${sku}` });
  const edge = data?.productVariants?.edges?.[0];
  if (!edge) return null;
  const productGid = edge.node.product.id; // gid://shopify/Product/123
  const numericId = productGid.split('/').pop();
  const handle = edge.node.product.handle;
  return { productGid, productId: numericId, handle };
}

// Update product template_suffix via REST
async function updateProductTemplate(productId, templateSuffix) {
  const productsRest = shopifyEndpoint.replace('graphql.json', 'products');
  const url = `${productsRest}/${productId}.json`;
  const payload = { product: { id: productId, template_suffix: templateSuffix } };
  const res = await fetch(url, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json', 'X-Shopify-Access-Token': shopifyAccessToken },
    body: JSON.stringify(payload)
  });
  if (!res.ok) {
    const text = await res.text();
    throw new Error(`Failed to update product template: ${res.status} ${text}`);
  }
}

// Upsert metafield videourl
async function upsertVideoUrlMetafield(productGid, videoUrl) {
  const mutation = `
    mutation metafieldsSet($metafields: [MetafieldsSetInput!]!) {
      metafieldsSet(metafields: $metafields) {
        metafields { id key namespace value }
        userErrors { field message }
      }
    }
  `;
  const variables = {
    metafields: [
      {
        ownerId: productGid,
        namespace: 'my_fields',
        key: 'videourl',
        type: 'single_line_text_field',
        value: videoUrl
      }
    ]
  };
  const data = await shopifyGraphQLRequest(mutation, variables);
  const errs = data?.metafieldsSet?.userErrors;
  if (errs && errs.length) {
    throw new Error(`metafieldsSet userErrors: ${JSON.stringify(errs)}`);
  }
}

export default async function processUpdateDiscWithNewMoldVideoTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[processUpdateDiscWithNewMoldVideoTask] Processing task ${task.id}`);
  try {
    const payload = typeof task.payload === 'string' ? JSON.parse(task.payload) : task.payload || {};
    const discId = payload.id;
    const videoUrl = payload.video_url;
    if (!discId) throw new Error('Missing disc id');
    if (!videoUrl) throw new Error('Missing video_url');

    await updateTaskStatus(task.id, 'processing');

    // Ensure disc is uploaded and unsold
    const { data: disc, error: discError } = await supabase
      .from('t_discs')
      .select('id, sold_date, shopify_uploaded_at')
      .eq('id', discId)
      .maybeSingle();
    if (discError) throw new Error(`DB error fetching disc: ${discError.message}`);
    if (!disc || disc.sold_date !== null || disc.shopify_uploaded_at == null) {
      await updateTaskStatus(task.id, 'completed', { message: 'Disc not eligible (not uploaded or sold)', disc_id: discId });
      return;
    }

    const product = await findProductByDiscId(discId);
    if (!product) throw new Error('Shopify product not found for disc');

    await updateProductTemplate(product.productId, 'disc-with-image-with-vid');
    await upsertVideoUrlMetafield(product.productGid, videoUrl);

    await updateTaskStatus(task.id, 'completed', {
      message: 'Updated product template and videourl metafield',
      disc_id: discId,
      product_id: product.productId
    });
  } catch (err) {
    console.error('[processUpdateDiscWithNewMoldVideoTask] Error:', err);
    await logError(err.message, 'processUpdateDiscWithNewMoldVideoTask');
    await updateTaskStatus(task.id, 'error', { message: err.message });
  }
}

