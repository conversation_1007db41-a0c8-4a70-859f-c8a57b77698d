// testDiscraftOslMapImport.js - Test script for Discraft OSL map import functionality

import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import { processImportDiscraftOslMapAndStatusTask } from './processImportDiscraftOslMapAndStatusTask.js';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Missing Supabase environment variables');
    console.error('Please ensure SUPABASE_URL and SUPABASE_KEY are set in your .env file');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Helper functions for testing
async function updateTaskStatus(taskId, status, result = null) {
    console.log(`[TEST] Task ${taskId} status updated to: ${status}`);
    if (result) {
        console.log(`[TEST] Result: ${JSON.stringify(result, null, 2)}`);
    }
}

async function logError(message, context) {
    console.error(`[TEST ERROR] ${context}: ${message}`);
}

// Test function
async function testDiscraftOslMapImport() {
    console.log('🧪 Testing Discraft OSL Map Import...');
    console.log('=====================================');

    // Create a mock task
    const mockTask = {
        id: 'test-task-' + Date.now(),
        task_type: 'import_discraft_osl_map_and_status',
        payload: {}
    };

    try {
        // Run the task handler
        await processImportDiscraftOslMapAndStatusTask(mockTask, {
            supabase,
            updateTaskStatus,
            logError
        });

        console.log('\n✅ Test completed successfully!');
        
        // Query the table to see what was imported
        console.log('\n📊 Checking imported data...');
        const { data: importedData, error: queryError } = await supabase
            .from('it_discraft_osl_map')
            .select('*')
            .order('id')
            .limit(10);

        if (queryError) {
            console.error('❌ Error querying imported data:', queryError);
        } else {
            console.log(`📈 Found ${importedData.length} records (showing first 10):`);
            console.table(importedData);
        }

        // Get total count
        const { count, error: countError } = await supabase
            .from('it_discraft_osl_map')
            .select('*', { count: 'exact', head: true });

        if (!countError) {
            console.log(`\n📊 Total records in table: ${count}`);
        }

        // Get status breakdown
        const { data: statusBreakdown, error: statusError } = await supabase
            .from('it_discraft_osl_map')
            .select('status')
            .order('status');

        if (!statusError && statusBreakdown) {
            const statusCounts = statusBreakdown.reduce((acc, item) => {
                acc[item.status] = (acc[item.status] || 0) + 1;
                return acc;
            }, {});
            
            console.log('\n📈 Status breakdown:');
            console.table(statusCounts);
        }

    } catch (error) {
        console.error('❌ Test failed:', error);
        console.error('Stack trace:', error.stack);
    }
}

// Run the test
testDiscraftOslMapImport()
    .then(() => {
        console.log('\n🎉 Test script completed');
        process.exit(0);
    })
    .catch(error => {
        console.error('💥 Test script failed:', error);
        process.exit(1);
    });
