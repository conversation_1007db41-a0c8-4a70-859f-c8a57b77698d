import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function getAllTaskStatus() {
  console.log('📊 FULL STATUS REPORT (CHUNKED)');
  console.log('='.repeat(50));
  
  let allTasks = [];
  let lastId = 0;
  const CHUNK_SIZE = 1000;
  let totalFetched = 0;
  
  console.log('🔍 Fetching all tasks in chunks...');
  
  while (true) {
    console.log(`   Fetching chunk starting from task ID ${lastId}...`);
    
    const { data, error } = await supabase
      .from('t_task_queue')
      .select('id, status')
      .eq('task_type', 'update_veeqo_d_title')
      .eq('enqueued_by', 'enqueueVeeqoTitleUpdateTasks.js_initial_sync')
      .gt('id', lastId)
      .order('id', { ascending: true })
      .limit(CHUNK_SIZE);
    
    if (error) {
      console.error('❌ Error fetching tasks:', error);
      throw error;
    }
    
    if (data.length === 0) {
      console.log('   No more tasks found');
      break;
    }
    
    allTasks.push(...data);
    totalFetched += data.length;
    console.log(`   Found ${data.length} tasks (total: ${totalFetched})`);
    
    // If we got less than CHUNK_SIZE, we're done
    if (data.length < CHUNK_SIZE) {
      console.log('   Reached end of data');
      break;
    }
    
    // Set next start ID to the last ID
    lastId = data[data.length - 1].id;
  }
  
  console.log(`✅ Found ${allTasks.length} total tasks`);
  
  // Calculate status breakdown
  const statusCounts = allTasks.reduce((acc, task) => {
    acc[task.status] = (acc[task.status] || 0) + 1;
    return acc;
  }, {});
  
  console.log('\n📊 Status breakdown:');
  Object.entries(statusCounts).forEach(([status, count]) => {
    const percentage = ((count / allTasks.length) * 100).toFixed(1);
    console.log(`   ${status}: ${count} (${percentage}%)`);
  });
  
  const completed = statusCounts.completed || 0;
  const pending = statusCounts.pending || 0;
  const errors = statusCounts.error || 0;
  const processing = statusCounts.processing || 0;
  
  console.log('\n🎯 Progress Summary:');
  console.log(`   ✅ Completed: ${completed}`);
  console.log(`   ⏳ Pending: ${pending}`);
  console.log(`   🔄 Processing: ${processing}`);
  console.log(`   ❌ Errors: ${errors}`);
  console.log(`   📈 Progress: ${((completed / allTasks.length) * 100).toFixed(1)}%`);
  
  if (pending > 0) {
    const estimatedMinutes = Math.ceil(pending / 4);
    console.log(`   ⏱️  Estimated time remaining: ~${estimatedMinutes} minutes`);
  }
  
  // Check for duplicates
  console.log('\n🔍 Checking for duplicates...');
  
  let duplicateCount = 0;
  const discCounts = {};
  
  // We need to get payload data to check disc IDs
  let allTasksWithPayload = [];
  lastId = 0;
  
  while (true) {
    const { data, error } = await supabase
      .from('t_task_queue')
      .select('id, payload')
      .eq('task_type', 'update_veeqo_d_title')
      .eq('enqueued_by', 'enqueueVeeqoTitleUpdateTasks.js_initial_sync')
      .gt('id', lastId)
      .order('id', { ascending: true })
      .limit(CHUNK_SIZE);
    
    if (error || data.length === 0) break;
    
    allTasksWithPayload.push(...data);
    
    if (data.length < CHUNK_SIZE) break;
    lastId = data[data.length - 1].id;
  }
  
  allTasksWithPayload.forEach(task => {
    const discId = task.payload.id;
    discCounts[discId] = (discCounts[discId] || 0) + 1;
  });
  
  const duplicates = Object.entries(discCounts).filter(([discId, count]) => count > 1);
  
  console.log(`📋 Unique discs: ${Object.keys(discCounts).length}`);
  console.log(`📋 Discs with duplicates: ${duplicates.length}`);
  
  if (duplicates.length > 0) {
    console.log('📋 Sample duplicates:');
    duplicates.slice(0, 5).forEach(([discId, count]) => {
      console.log(`   Disc ${discId}: ${count} tasks`);
    });
    
    const totalDuplicateTasks = duplicates.reduce((sum, [discId, count]) => sum + (count - 1), 0);
    console.log(`📋 Total duplicate tasks: ${totalDuplicateTasks}`);
  }
}

getAllTaskStatus().catch(console.error);
