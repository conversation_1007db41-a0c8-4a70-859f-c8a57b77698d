import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function queryDynamicDiscs() {
    console.log('Querying Dynamic Discs data...\n');
    
    try {
        // 1. Get Dynamic Discs product count
        const { data: ddCount, error: ddError } = await supabase
            .from('test_dd_data')
            .select('*', { count: 'exact', head: true })
            .eq('product_vendor', 'Dynamic Discs');
        
        if (ddError) {
            console.error('Error querying Dynamic Discs count:', ddError);
            return;
        }
        
        console.log(`Total Dynamic Discs variants: ${ddCount}`);
        
        // 2. Get sample Dynamic Discs products
        const { data: ddProducts, error: ddProductsError } = await supabase
            .from('test_dd_data')
            .select('product_title, variant_title, variant_sku, variant_price, variant_available, variant_option1, variant_option2')
            .eq('product_vendor', 'Dynamic Discs')
            .eq('product_product_type', 'Discs')
            .limit(20);
        
        if (ddProductsError) {
            console.error('Error querying Dynamic Discs products:', ddProductsError);
            return;
        }
        
        console.log('\nSample Dynamic Discs products:');
        console.log('=====================================');
        ddProducts.forEach(product => {
            console.log(`${product.product_title}`);
            console.log(`  Variant: ${product.variant_title}`);
            console.log(`  SKU: ${product.variant_sku}`);
            console.log(`  Price: $${product.variant_price}`);
            console.log(`  Available: ${product.variant_available ? 'Yes' : 'No'}`);
            console.log(`  Color: ${product.variant_option1}`);
            console.log(`  Weight: ${product.variant_option2}`);
            console.log('---');
        });
        
        // 3. Get vendor summary
        const { data: vendorSummary, error: vendorError } = await supabase
            .from('test_dd_data')
            .select('product_vendor')
            .not('product_vendor', 'is', null);
        
        if (!vendorError && vendorSummary) {
            const vendorCounts = {};
            vendorSummary.forEach(row => {
                vendorCounts[row.product_vendor] = (vendorCounts[row.product_vendor] || 0) + 1;
            });
            
            console.log('\nVendor breakdown:');
            console.log('==================');
            Object.entries(vendorCounts)
                .sort((a, b) => b[1] - a[1])
                .forEach(([vendor, count]) => {
                    console.log(`${vendor}: ${count} variants`);
                });
        }
        
        // 4. Get available Dynamic Discs count
        const { data: availableDD, error: availableError } = await supabase
            .from('test_dd_data')
            .select('*', { count: 'exact', head: true })
            .eq('product_vendor', 'Dynamic Discs')
            .eq('variant_available', true);
        
        if (!availableError) {
            console.log(`\nAvailable Dynamic Discs variants: ${availableDD}`);
        }
        
    } catch (error) {
        console.error('Query failed:', error);
    }
}

// Run the query
queryDynamicDiscs();
