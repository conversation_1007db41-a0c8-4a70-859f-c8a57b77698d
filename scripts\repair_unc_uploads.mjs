import fs from 'fs';
import path from 'path';

const LOCAL_DIR = 'C:\\NANCY\\nancyv8\\Images\\DG Accessory Products\\Uploaded to AWS';
const UNC_DIR = "\\\\NANCY\\nancyv8\\Images\\DG Accessory Products\\Uploaded to AWS";

let moved = 0, skipped = 0, errors = 0;

if (!fs.existsSync(LOCAL_DIR)) {
  console.log('Local directory does not exist:', LOCAL_DIR);
  process.exit(0);
}

const files = fs.readdirSync(LOCAL_DIR).filter(f => /\.jpg$/i.test(f));
for (const f of files) {
  const src = path.win32.join(LOCAL_DIR, f);
  const dst = path.win32.join(UNC_DIR, f);
  try {
    if (fs.existsSync(dst)) { skipped++; continue; }
    const buf = fs.readFileSync(src);
    fs.writeFileSync(dst, buf);
    fs.unlinkSync(src);
    moved++;
  } catch (e) {
    errors++;
  }
}

console.log({ moved, skipped, errors, localCount: files.length });

