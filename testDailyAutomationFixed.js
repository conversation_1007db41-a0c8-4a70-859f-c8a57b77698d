import runDailyAutomation from './discraftDailyAutomation.js';

async function testDailyAutomationFixed() {
    try {
        console.log('🧪 Testing full daily automation after fixes...\n');
        console.log('📧 This will test if email is working and export generates correctly');
        console.log('⏰ Starting automation test...\n');
        
        // Run the full automation
        await runDailyAutomation();
        
        console.log('\n✅ Daily automation test completed!');
        console.log('\n📋 Check the following:');
        console.log('1. Did you receive an email?');
        console.log('2. Were Excel files generated in data/external data/?');
        console.log('3. Does row 22 show as empty in the Excel files?');
        console.log('4. Do the special sections (fundraiser, new release) work correctly?');
        
    } catch (error) {
        console.error('❌ Daily automation test failed:', error.message);
        console.log('\n📋 If this failed, the issues might be:');
        console.log('1. Email configuration problem');
        console.log('2. Export API issue');
        console.log('3. Database connection problem');
    }
}

console.log('🚀 Testing Daily Automation After Fixes');
console.log('==========================================');
testDailyAutomationFixed().catch(console.error);
