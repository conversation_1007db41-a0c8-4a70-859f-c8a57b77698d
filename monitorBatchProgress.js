import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function getTaskCounts() {
  // Get counts by status (chunked to avoid 1000 limit)
  let allTasks = [];
  let lastId = 0;
  const CHUNK_SIZE = 1000;
  
  while (true) {
    const { data, error } = await supabase
      .from('t_task_queue')
      .select('id, status')
      .eq('task_type', 'update_veeqo_d_title')
      .eq('enqueued_by', 'enqueueVeeqoTitleUpdateTasks.js_initial_sync')
      .gt('id', lastId)
      .order('id', { ascending: true })
      .limit(CHUNK_SIZE);
    
    if (error || data.length === 0) break;
    
    allTasks.push(...data);
    
    if (data.length < CHUNK_SIZE) break;
    lastId = data[data.length - 1].id;
  }
  
  const statusCounts = allTasks.reduce((acc, task) => {
    acc[task.status] = (acc[task.status] || 0) + 1;
    return acc;
  }, {});
  
  return {
    total: allTasks.length,
    completed: statusCounts.completed || 0,
    pending: statusCounts.pending || 0,
    processing: statusCounts.processing || 0,
    error: statusCounts.error || 0
  };
}

async function monitorProgress() {
  console.log('🚀 BATCH PROCESSING MONITOR');
  console.log('='.repeat(50));
  console.log('Monitoring Veeqo title update task progress...');
  console.log('Press Ctrl+C to stop monitoring\n');
  
  let previousCompleted = 0;
  let startTime = Date.now();
  let lastCheckTime = startTime;
  
  const checkProgress = async () => {
    try {
      const counts = await getTaskCounts();
      const now = Date.now();
      const totalElapsed = (now - startTime) / 1000; // seconds
      const intervalElapsed = (now - lastCheckTime) / 1000; // seconds
      
      const completedSinceLastCheck = counts.completed - previousCompleted;
      const tasksPerSecond = intervalElapsed > 0 ? (completedSinceLastCheck / intervalElapsed).toFixed(2) : '0.00';
      const overallTasksPerSecond = totalElapsed > 0 ? (counts.completed / totalElapsed).toFixed(2) : '0.00';
      
      const progressPercent = ((counts.completed / counts.total) * 100).toFixed(1);
      
      console.log(`📊 ${new Date().toLocaleTimeString()}`);
      console.log(`   Total: ${counts.total}`);
      console.log(`   ✅ Completed: ${counts.completed} (${progressPercent}%)`);
      console.log(`   ⏳ Pending: ${counts.pending}`);
      console.log(`   🔄 Processing: ${counts.processing}`);
      console.log(`   ❌ Errors: ${counts.error}`);
      console.log(`   📈 Speed: ${tasksPerSecond} tasks/sec (last 30s), ${overallTasksPerSecond} tasks/sec (overall)`);
      
      if (counts.pending > 0) {
        const estimatedSecondsRemaining = overallTasksPerSecond > 0 ? (counts.pending / parseFloat(overallTasksPerSecond)) : 0;
        const estimatedMinutesRemaining = Math.ceil(estimatedSecondsRemaining / 60);
        console.log(`   ⏱️  Estimated time remaining: ~${estimatedMinutesRemaining} minutes`);
      }
      
      console.log('');
      
      previousCompleted = counts.completed;
      lastCheckTime = now;
      
      // Check if we're done
      if (counts.pending === 0 && counts.processing === 0) {
        console.log('🎉 All tasks completed!');
        process.exit(0);
      }
      
    } catch (error) {
      console.error('❌ Error checking progress:', error.message);
    }
  };
  
  // Initial check
  await checkProgress();
  
  // Check every 30 seconds
  setInterval(checkProgress, 30000);
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n👋 Monitoring stopped by user');
  process.exit(0);
});

monitorProgress().catch(console.error);
