// verify_cleanup.js
// Verify the cleanup was successful and show current status

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function verifyCleanup() {
  try {
    console.log('🔍 Verifying cleanup results...\n');
    
    // 1. Check for remaining error tasks for publish_product_osl
    const { data: remainingErrorTasks, error: tasksError } = await supabase
      .from('t_task_queue')
      .select('id, payload')
      .eq('task_type', 'publish_product_osl')
      .eq('status', 'error');
    
    if (tasksError) {
      console.error('❌ Error fetching remaining tasks:', tasksError);
      return;
    }
    
    console.log(`📋 Remaining error tasks for publish_product_osl: ${remainingErrorTasks.length}`);
    
    // Check if any of the remaining tasks are for placeholder OSLs
    let placeholderTasksRemaining = 0;
    for (const task of remainingErrorTasks) {
      try {
        const payload = typeof task.payload === 'string' ? JSON.parse(task.payload) : task.payload;
        const oslId = payload.id;
        
        if (oslId) {
          const { data: oslData } = await supabase
            .from('t_order_sheet_lines')
            .select('max_weight')
            .eq('id', oslId)
            .single();
          
          if (oslData && oslData.max_weight < 10) {
            placeholderTasksRemaining++;
            console.log(`⚠️  Task ${task.id} still exists for placeholder OSL ${oslId} (${oslData.max_weight}g)`);
          }
        }
      } catch (err) {
        // Skip invalid tasks
      }
    }
    
    if (placeholderTasksRemaining === 0) {
      console.log('✅ No placeholder OSL tasks remaining in error status');
    } else {
      console.log(`❌ ${placeholderTasksRemaining} placeholder OSL tasks still in error status`);
    }
    
    // 2. Check OSLs with max_weight < 10g and their current status
    const { data: placeholderOSLs, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, max_weight, ready_button, ready, todo')
      .lt('max_weight', 10)
      .order('id');
    
    if (oslError) {
      console.error('❌ Error fetching placeholder OSLs:', oslError);
      return;
    }
    
    console.log(`\n📊 Current status of OSLs with max_weight < 10g (${placeholderOSLs.length} total):`);
    
    let correctlySet = 0;
    let needsUpdate = 0;
    
    placeholderOSLs.forEach(osl => {
      const isCorrect = osl.ready_button === false && osl.ready === false;
      if (isCorrect) {
        correctlySet++;
      } else {
        needsUpdate++;
        console.log(`⚠️  OSL ${osl.id} (${osl.max_weight}g): ready_button=${osl.ready_button}, ready=${osl.ready} - needs update`);
      }
    });
    
    console.log(`✅ Correctly set (ready_button=false, ready=false): ${correctlySet}`);
    console.log(`⚠️  Need updates: ${needsUpdate}`);
    
    // 3. Summary of cleaned up OSLs
    const cleanedUpOSLs = placeholderOSLs.filter(osl => 
      osl.todo && osl.todo.includes('Placeholder OSL') && osl.todo.includes('not ready for publishing')
    );
    
    console.log(`\n🧹 OSLs cleaned up by script: ${cleanedUpOSLs.length}`);
    
    if (cleanedUpOSLs.length > 0) {
      console.log('📝 Sample cleaned up OSLs:');
      cleanedUpOSLs.slice(0, 5).forEach(osl => {
        console.log(`   OSL ${osl.id} (${osl.max_weight}g): ${osl.todo}`);
      });
      if (cleanedUpOSLs.length > 5) {
        console.log(`   ... and ${cleanedUpOSLs.length - 5} more`);
      }
    }
    
    console.log('\n🎯 Cleanup Verification Summary:');
    console.log(`✅ Error tasks deleted: 35`);
    console.log(`✅ OSLs updated: 35`);
    console.log(`✅ Placeholder OSL error tasks remaining: ${placeholderTasksRemaining}`);
    console.log(`✅ OSLs correctly set to not ready: ${correctlySet}/${placeholderOSLs.length}`);
    
    if (placeholderTasksRemaining === 0 && needsUpdate === 0) {
      console.log('\n🎉 Cleanup verification PASSED! All placeholder OSLs are properly configured.');
    } else {
      console.log('\n⚠️  Some issues remain. Please review the details above.');
    }
    
  } catch (error) {
    console.error('❌ Unexpected error during verification:', error.message);
  }
}

// Run the verification
verifyCleanup();
