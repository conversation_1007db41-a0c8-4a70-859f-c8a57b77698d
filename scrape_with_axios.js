import axios from 'axios';
import fs from 'fs';
import { wrapper } from 'axios-cookiejar-support';
import { <PERSON>ieJar } from 'tough-cookie';

// Create axios instance with cookie support
const jar = new CookieJar();
const client = wrapper(axios.create({ jar }));

async function scrapeWithAxios() {
    console.log('Starting scraper with axios...');
    
    try {
        // Set headers to mimic a real browser
        client.defaults.headers.common['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
        client.defaults.headers.common['Accept'] = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8';
        client.defaults.headers.common['Accept-Language'] = 'en-US,en;q=0.5';
        client.defaults.headers.common['Accept-Encoding'] = 'gzip, deflate, br';
        client.defaults.headers.common['Connection'] = 'keep-alive';
        client.defaults.headers.common['Upgrade-Insecure-Requests'] = '1';
        
        console.log('Getting login page...');
        const loginPageResponse = await client.get('https://discgolfdistribution.com/account/login');
        console.log('Login page status:', loginPageResponse.status);
        
        // Extract CSRF token or form token if needed
        const loginPageHtml = loginPageResponse.data;
        const tokenMatch = loginPageHtml.match(/name="authenticity_token"[^>]*value="([^"]+)"/);
        const authenticityToken = tokenMatch ? tokenMatch[1] : null;
        
        console.log('Authenticity token found:', !!authenticityToken);
        
        // Prepare login data
        const loginData = new URLSearchParams();
        loginData.append('customer[email]', '<EMAIL>');
        loginData.append('customer[password]', 'Sdisplatgun9!');
        if (authenticityToken) {
            loginData.append('authenticity_token', authenticityToken);
        }
        
        console.log('Submitting login...');
        const loginResponse = await client.post('https://discgolfdistribution.com/account/login', loginData, {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Referer': 'https://discgolfdistribution.com/account/login'
            },
            maxRedirects: 5
        });
        
        console.log('Login response status:', loginResponse.status);
        console.log('Login response URL:', loginResponse.request.res.responseUrl);
        
        // Check if login was successful by looking for account-specific content
        if (loginResponse.data.includes('account') || loginResponse.data.includes('logout') || loginResponse.status === 200) {
            console.log('Login appears successful');
            
            // Now try to access the products JSON endpoint
            console.log('Accessing products JSON endpoint...');
            
            let allProducts = [];
            let pageNum = 1;
            let hasMoreProducts = true;
            
            while (hasMoreProducts && pageNum <= 50) { // Safety limit
                console.log(`Fetching page ${pageNum}...`);
                
                try {
                    const productsResponse = await client.get(`https://discgolfdistribution.com/products.json?page=${pageNum}&limit=250`);
                    
                    if (productsResponse.status === 200) {
                        const productsData = productsResponse.data;
                        
                        if (productsData.products && productsData.products.length > 0) {
                            console.log(`Found ${productsData.products.length} products on page ${pageNum}`);
                            allProducts.push(...productsData.products);
                            pageNum++;
                        } else {
                            hasMoreProducts = false;
                            console.log('No more products found');
                        }
                    } else {
                        console.log(`Products endpoint returned status ${productsResponse.status}`);
                        hasMoreProducts = false;
                    }
                } catch (error) {
                    console.log(`Error fetching page ${pageNum}:`, error.message);
                    hasMoreProducts = false;
                }
                
                // Add delay to be respectful
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            console.log(`Total products found: ${allProducts.length}`);
            
            // Filter for Dynamic Discs products
            const dynamicDiscsProducts = allProducts.filter(product => {
                const vendor = product.vendor?.toLowerCase() || '';
                const title = product.title?.toLowerCase() || '';
                const tags = product.tags || [];
                
                return vendor.includes('dynamic') || 
                       title.includes('dynamic') ||
                       tags.some(tag => tag.toLowerCase().includes('dynamic'));
            });
            
            console.log(`Found ${dynamicDiscsProducts.length} Dynamic Discs products`);
            
            // Save all products
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const allProductsFilename = `all_products_${timestamp}.json`;
            fs.writeFileSync(allProductsFilename, JSON.stringify(allProducts, null, 2));
            console.log(`All products saved to ${allProductsFilename}`);
            
            // Save Dynamic Discs products
            const dynamicDiscsFilename = `dynamic_discs_products_${timestamp}.json`;
            fs.writeFileSync(dynamicDiscsFilename, JSON.stringify(dynamicDiscsProducts, null, 2));
            console.log(`Dynamic Discs products saved to ${dynamicDiscsFilename}`);
            
            // Save as CSV for easier viewing
            const csvFilename = `dynamic_discs_products_${timestamp}.csv`;
            const csvContent = convertToCSV(dynamicDiscsProducts);
            fs.writeFileSync(csvFilename, csvContent);
            console.log(`Dynamic Discs products also saved to ${csvFilename}`);
            
            // Print summary
            console.log('\n=== SUMMARY ===');
            console.log(`Total products scraped: ${allProducts.length}`);
            console.log(`Dynamic Discs products: ${dynamicDiscsProducts.length}`);
            
            if (dynamicDiscsProducts.length > 0) {
                console.log('\nSample Dynamic Discs products:');
                dynamicDiscsProducts.slice(0, 5).forEach((product, index) => {
                    console.log(`${index + 1}. ${product.title} - ${product.vendor} - $${product.variants?.[0]?.price || 'N/A'}`);
                });
            }
            
            return { allProducts, dynamicDiscsProducts };
            
        } else {
            console.log('Login failed - response does not contain expected account content');
            console.log('Response preview:', loginResponse.data.substring(0, 500));
        }
        
    } catch (error) {
        console.error('Error during scraping:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response headers:', error.response.headers);
        }
        throw error;
    }
}

function convertToCSV(products) {
    if (products.length === 0) return '';
    
    // Define the columns we want in the CSV
    const headers = [
        'id', 'title', 'vendor', 'product_type', 'created_at', 'updated_at',
        'published_at', 'available', 'tags', 'variant_id', 'variant_title',
        'variant_price', 'variant_sku', 'variant_inventory_quantity',
        'variant_available', 'variant_weight', 'variant_option1', 'variant_option2', 'variant_option3'
    ];
    
    const csvRows = [headers.join(',')];
    
    products.forEach(product => {
        // Handle products with multiple variants
        if (product.variants && product.variants.length > 0) {
            product.variants.forEach(variant => {
                const row = [
                    product.id || '',
                    `"${(product.title || '').replace(/"/g, '""')}"`,
                    `"${(product.vendor || '').replace(/"/g, '""')}"`,
                    `"${(product.product_type || '').replace(/"/g, '""')}"`,
                    product.created_at || '',
                    product.updated_at || '',
                    product.published_at || '',
                    product.available || false,
                    `"${(product.tags || []).join('; ').replace(/"/g, '""')}"`,
                    variant.id || '',
                    `"${(variant.title || '').replace(/"/g, '""')}"`,
                    variant.price || '',
                    `"${(variant.sku || '').replace(/"/g, '""')}"`,
                    variant.inventory_quantity || '',
                    variant.available || false,
                    variant.weight || '',
                    `"${(variant.option1 || '').replace(/"/g, '""')}"`,
                    `"${(variant.option2 || '').replace(/"/g, '""')}"`,
                    `"${(variant.option3 || '').replace(/"/g, '""')}"`
                ];
                csvRows.push(row.join(','));
            });
        } else {
            // Product without variants
            const row = [
                product.id || '',
                `"${(product.title || '').replace(/"/g, '""')}"`,
                `"${(product.vendor || '').replace(/"/g, '""')}"`,
                `"${(product.product_type || '').replace(/"/g, '""')}"`,
                product.created_at || '',
                product.updated_at || '',
                product.published_at || '',
                product.available || false,
                `"${(product.tags || []).join('; ').replace(/"/g, '""')}"`,
                '', '', '', '', '', '', '', '', '', ''
            ];
            csvRows.push(row.join(','));
        }
    });
    
    return csvRows.join('\n');
}

// Run the scraper
scrapeWithAxios()
    .then(result => {
        console.log('Scraping completed successfully');
        process.exit(0);
    })
    .catch(error => {
        console.error('Scraping failed:', error);
        process.exit(1);
    });
