-- Function to delete a record from t_images
CREATE OR REPLACE FUNCTION delete_t_images_record(
  p_table_name TEXT,
  p_record_id INTEGER
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_result JSONB;
  v_count INTEGER;
BEGIN
  -- Delete the record
  DELETE FROM t_images
  WHERE table_name = p_table_name
  AND record_id = p_record_id
  RETURNING 1 INTO v_count;
  
  -- Return the result
  IF v_count IS NULL THEN
    v_result := jsonb_build_object(
      'success', FALSE,
      'message', 'Record not found',
      'table_name', p_table_name,
      'record_id', p_record_id
    );
  ELSE
    v_result := jsonb_build_object(
      'success', TRUE,
      'message', 'Record deleted',
      'table_name', p_table_name,
      'record_id', p_record_id
    );
  END IF;
  
  RETURN v_result;
EXCEPTION WHEN OTHERS THEN
  v_result := jsonb_build_object(
    'success', FALSE,
    'error', SQLERRM,
    'detail', SQLSTATE
  );
  
  RETURN v_result;
END;
$$;
