-- Function to enqueue a task for updating Veeqo quantity for SDASIN
CREATE OR REPLACE FUNCTION fn_enqueue_update_veeqo_sdasin_qty_task()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert a task into the task queue
    INSERT INTO t_task_queue (
        task_type,
        payload,
        status,
        scheduled_at,
        created_at,
        enqueued_by
    ) VALUES (
        'update_veeqo_sdasin_qty',
        jsonb_build_object('id', NEW.id, 'available_quantity', NEW.available_quantity),
        'pending',
        NOW(),
        NOW(),
        't_inv_sdasin update_trigger_' || NEW.id
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the old trigger
DROP TRIGGER IF EXISTS trg_update_veeqo_qty_sdasin ON t_inv_sdasin;

-- Create the new trigger
CREATE TRIGGER trg_update_veeqo_qty_sdasin
AFTER UPDATE OF available_quantity ON t_inv_sdasin 
FOR EACH ROW 
WHEN (NEW.available_quantity IS DISTINCT FROM OLD.available_quantity)
EXECUTE FUNCTION fn_enqueue_update_veeqo_sdasin_qty_task();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger trg_update_veeqo_qty_sdasin has been updated to enqueue a task.';
END $$;
