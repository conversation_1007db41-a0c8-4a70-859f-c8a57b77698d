// Test the new filename format with date and time

function testFilenameFormat() {
    console.log('🧪 Testing new filename format with date and time...\n');
    
    // Test the format used in discraftDailyAutomation.js
    const automationFilename = `discraft_order_${new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0]}.xlsx`;
    console.log('📄 Automation filename format:');
    console.log(`   ${automationFilename}`);
    
    // Test the format used in adminServer.js
    const now = new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0];
    const adminFilename = `discraftstock_${now}.xlsx`;
    console.log('\n📄 Admin server filename format:');
    console.log(`   ${adminFilename}`);
    
    // Show the breakdown
    const isoString = new Date().toISOString();
    console.log('\n🔧 Format breakdown:');
    console.log(`   Original ISO string: ${isoString}`);
    console.log(`   After replacing T with -: ${isoString.replace(/T/, '-')}`);
    console.log(`   After replacing : with -: ${isoString.replace(/T/, '-').replace(/:/g, '-')}`);
    console.log(`   After splitting on . and taking first part: ${isoString.replace(/T/, '-').replace(/:/g, '-').split('.')[0]}`);
    
    // Show expected format
    console.log('\n✅ Expected format: YYYY-MM-DD-HH-MM-SS');
    console.log('   Example: discraft_order_2025-06-16-08-24-30.xlsx');
    
    // Test a few more examples
    console.log('\n📋 Sample filenames:');
    for (let i = 0; i < 3; i++) {
        setTimeout(() => {
            const testFilename = `discraft_order_${new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0]}.xlsx`;
            console.log(`   ${testFilename}`);
        }, i * 1000);
    }
}

testFilenameFormat();
