import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Resetting update_veeqo_d_title tasks in error to pending...');

    const nowIso = new Date().toISOString();

    const { data, error } = await supabase
      .from('t_task_queue')
      .update({
        status: 'pending',
        scheduled_at: nowIso,
        locked_at: null,
        locked_by: null,
        processed_at: null,
      })
      .eq('task_type', 'update_veeqo_d_title')
      .eq('status', 'error')
      .select('id');

    if (error) {
      console.error('Error resetting tasks:', error);
      process.exit(1);
    }

    const count = Array.isArray(data) ? data.length : 0;
    console.log(`Reset ${count} task(s) to pending.`);

    process.exit(0);
  } catch (e) {
    console.error('Unexpected error:', e);
    process.exit(1);
  }
}

main();

