import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function fixFundraiserPermanently() {
    try {
        console.log('🔧 Fixing fundraiser section PERMANENTLY...\n');
        
        // 1. Delete ALL row 22 records (they should not exist at all)
        console.log('1. Deleting ALL row 22 header records...');
        const { data: deletedRow22, error: deleteRow22Error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .delete()
            .eq('excel_row_hint', 22)
            .select();

        if (deleteRow22Error) {
            console.error('❌ Error deleting row 22 records:', deleteRow22Error);
        } else {
            console.log(`✅ Deleted ${deletedRow22?.length || 0} row 22 header records`);
        }

        // 2. Delete ALL existing records in rows 25 and 28 (cleanup)
        console.log('\n2. Deleting ALL existing records in rows 25 and 28...');
        const { data: deletedRows2528, error: deleteRows2528Error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .delete()
            .in('excel_row_hint', [25, 28])
            .select();

        if (deleteRows2528Error) {
            console.error('❌ Error deleting rows 25/28 records:', deleteRows2528Error);
        } else {
            console.log(`✅ Deleted ${deletedRows2528?.length || 0} existing records from rows 25 and 28`);
        }

        // 3. Create the correct fundraiser records in column A (not B!)
        console.log('\n3. Creating correct fundraiser records in column A...');
        
        const fundraiserRecords = [
            {
                mold_name: 'Thrasher',
                plastic_name: 'Elite Z Jawbreaker',
                min_weight: 150,
                max_weight: 180,
                color_name: 'Varies',
                stamp_name: 'Live Free - Be Happy - Funky Ben Askren Fundraiser',
                vendor_product_code: 'Elite_Z_Jawbreaker_Thrasher_150-180',
                is_orderable: true,
                is_currently_available: true,
                cost_price: 10.00,
                excel_mapping_key: 'SPECIAL|Fundraiser - Ben Askren Z Jawbreaker Thrasher (msrp $19.99, cost $10.00)|Order Qty',
                excel_row_hint: 25,
                excel_column: 'A',  // COLUMN A, NOT B!
                raw_line_type: 'SPECIAL',
                raw_model: 'Fundraiser - Ben Askren Z Jawbreaker Thrasher (msrp $19.99, cost $10.00)',
                raw_weight_range: 'Order Qty',
                vendor_description: '',
                calculated_mps_id: 19704,
                import_file_hash: 'manual_fix',
                import_batch_id: 'manual_fundraiser_fix'
            },
            {
                mold_name: 'Buzzz',
                plastic_name: 'Big Z Collection',
                min_weight: 150,
                max_weight: 180,
                color_name: 'Varies',
                stamp_name: 'Live Free - Be Happy - Funky Ben Askren Fundraiser',
                vendor_product_code: 'Big_Z_Collection_Buzzz_150-180',
                is_orderable: true,
                is_currently_available: true,
                cost_price: 10.00,
                excel_mapping_key: 'SPECIAL|Fundraiser - Ben Askren Big Z Buzzz  (msrp $19.99, cost $10.00)|Order Qty',
                excel_row_hint: 28,
                excel_column: 'A',  // COLUMN A, NOT B!
                raw_line_type: 'SPECIAL',
                raw_model: 'Fundraiser - Ben Askren Big Z Buzzz  (msrp $19.99, cost $10.00)',
                raw_weight_range: 'Order Qty',
                vendor_description: '',
                calculated_mps_id: null,
                import_file_hash: 'manual_fix',
                import_batch_id: 'manual_fundraiser_fix'
            }
        ];

        for (const record of fundraiserRecords) {
            console.log(`Creating: ${record.plastic_name} ${record.mold_name} in row ${record.excel_row_hint}, column ${record.excel_column}`);
            
            const { error: insertError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .insert(record);

            if (insertError) {
                console.error(`❌ Error inserting ${record.mold_name}:`, insertError);
            } else {
                console.log(`✅ Created ${record.mold_name} record in column ${record.excel_column}`);
            }
        }

        // 4. Verify the fixes with detailed checking
        console.log('\n4. Verifying fixes...');
        
        // Check row 22 - should be completely empty
        const { data: row22Check, error: row22Error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*')
            .eq('excel_row_hint', 22);

        if (row22Error) {
            console.error('❌ Error checking row 22:', row22Error);
        } else {
            console.log(`✅ Row 22: ${row22Check.length} total records (should be 0)`);
            if (row22Check.length > 0) {
                console.log('❌ WARNING: Row 22 still has records!');
                row22Check.forEach(record => {
                    console.log(`   Col ${record.excel_column}: ${record.mold_name} | Orderable: ${record.is_orderable}`);
                });
            }
        }

        // Check rows 25/28 - should only have records in column A
        const { data: rows2528Check, error: rows2528Error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, mold_name, plastic_name, calculated_mps_id, is_orderable')
            .in('excel_row_hint', [25, 28])
            .order('excel_row_hint, excel_column');

        if (rows2528Error) {
            console.error('❌ Error checking rows 25/28:', rows2528Error);
        } else {
            console.log(`✅ Rows 25/28: ${rows2528Check.length} total records (should be 2, both in column A)`);
            rows2528Check.forEach(record => {
                console.log(`   Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.plastic_name} ${record.mold_name}`);
                console.log(`      Orderable: ${record.is_orderable}, MPS: ${record.calculated_mps_id || 'NO_MPS'}`);
                
                if (record.excel_column !== 'A') {
                    console.log(`   ❌ WARNING: Record in wrong column! Should be A, got ${record.excel_column}`);
                }
            });
        }

        // Check what will be exported
        const { data: exportCheck, error: exportError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, calculated_mps_id, excel_mapping_key')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .in('excel_row_hint', [22, 25, 28]);

        if (exportError) {
            console.error('❌ Error checking export data:', exportError);
        } else {
            console.log(`\n✅ Export check: ${exportCheck.length} records will be exported from rows 22, 25, 28:`);
            exportCheck.forEach(record => {
                console.log(`   Row ${record.excel_row_hint}, Col ${record.excel_column}: MPS ${record.calculated_mps_id || 'NO_MPS'}`);
            });
        }

        console.log('\n🎉 Permanent fundraiser fix completed!');
        console.log('\n📋 Expected results in next order:');
        console.log('   • Row 22: COMPLETELY EMPTY (no records at all)');
        console.log('   • Row 25, Column A: Thrasher with MPS ID 19704');
        console.log('   • Row 28, Column A: Buzzz with NO_MPS');
        console.log('\n⚠️  If this gets overwritten again, check if there\'s an automated import running!');
        
    } catch (error) {
        console.error('❌ Fix failed:', error.message);
    }
}

fixFundraiserPermanently().catch(console.error);
