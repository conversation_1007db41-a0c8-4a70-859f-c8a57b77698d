import XLSX from 'xlsx';

const inputFile = 'data/external data/discraftstock.xlsx';

try {
  console.log('Reading Excel file:', inputFile);
  const workbook = XLSX.readFile(inputFile);
  const sheetName = workbook.SheetNames[0];
  console.log('Sheet name:', sheetName);
  
  const worksheet = workbook.Sheets[sheetName];
  const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
  
  console.log(`Excel file has ${data.length} rows`);
  
  // Show first 20 rows to understand structure
  for (let i = 0; i < Math.min(20, data.length); i++) {
    console.log(`Row ${i}:`, data[i]);
  }
  
  // Look for rows that might contain product data
  console.log('\n--- Looking for product data rows ---');
  for (let i = 0; i < Math.min(50, data.length); i++) {
    const row = data[i];
    if (row && row.length > 3 && row[0] && typeof row[0] === 'string') {
      // Skip header rows
      if (row[0].toLowerCase().includes('stock') || 
          row[0].toLowerCase().includes('phone') ||
          row[0].toLowerCase().includes('website') ||
          row[0].toLowerCase().includes('billing') ||
          row[0].toLowerCase().includes('name') ||
          row[0].toLowerCase().includes('customer') ||
          row[0].toLowerCase().includes('order date')) {
        continue;
      }
      
      // Look for rows that might be products
      if (row[0].length > 2 && !row[0].includes('|')) {
        console.log(`Potential product row ${i}:`, row.slice(0, 10));
      }
    }
  }
  
} catch (err) {
  console.error('Error reading Excel file:', err.message);
}
