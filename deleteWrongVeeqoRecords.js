// deleteWrongVeeqoRecords.js - <PERSON><PERSON>t to delete Veeqo records with variant titles containing " (D#"

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Parse command line arguments
const args = process.argv.slice(2);
let limit = null; // null means no limit (process all records)

// Check for limit argument
const limitArg = args.find(arg => arg.startsWith('--limit='));
if (limitArg) {
  const limitValue = limitArg.split('=')[1];
  if (!isNaN(parseInt(limitValue))) {
    limit = parseInt(limitValue);
    console.log(`Limiting to ${limit} records`);
  }
}

// Create a log file
const logFile = 'delete_wrong_veeqo_records.log';
fs.writeFileSync(logFile, `Starting deletion of wrong Veeqo records at ${new Date().toISOString()}\n`);

// Load environment variables
dotenv.config();
fs.appendFileSync(logFile, `Environment variables loaded\n`);

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

// Veeqo API configuration
const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!supabaseUrl || !supabaseKey) {
  const errorMsg = 'Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file';
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

if (!veeqoApiKey) {
  const errorMsg = 'Error: VEEQO_API_KEY must be set in .env file';
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

fs.appendFileSync(logFile, `Supabase and Veeqo credentials found\n`);
const supabase = createClient(supabaseUrl, supabaseKey);
fs.appendFileSync(logFile, `Supabase client created\n`);

// Function to get records with variant titles containing " (D#"
async function getWrongRecords() {
  try {
    fs.appendFileSync(logFile, `Fetching records with variant titles containing " (D#"...\n`);
    console.log('Fetching records with variant titles containing " (D#"...');

    let allRecords = [];
    let hasMore = true;
    let page = 0;
    const pageSize = 1000;

    while (hasMore) {
      fs.appendFileSync(logFile, `Fetching page ${page + 1} of wrong records...\n`);
      console.log(`Fetching page ${page + 1} of wrong records...`);

      const { data, error } = await supabase
        .from('v_duplicate_veeqo_skus_details')
        .select('*')
        .ilike('variant_title', '% (D#%')
        .range(page * pageSize, (page + 1) * pageSize - 1);

      if (error) {
        fs.appendFileSync(logFile, `Error fetching wrong records on page ${page + 1}: ${error.message}\n`);
        console.error(`Error fetching wrong records on page ${page + 1}: ${error.message}`);
        return allRecords; // Return what we have so far
      }

      if (data.length === 0) {
        hasMore = false;
      } else {
        allRecords = [...allRecords, ...data];
        fs.appendFileSync(logFile, `Found ${data.length} records on page ${page + 1}, total so far: ${allRecords.length}\n`);
        console.log(`Found ${data.length} records on page ${page + 1}, total so far: ${allRecords.length}`);
        page++;
      }
    }

    fs.appendFileSync(logFile, `Found a total of ${allRecords.length} records with wrong variant titles\n`);
    console.log(`Found a total of ${allRecords.length} records with wrong variant titles`);
    return allRecords;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in getWrongRecords: ${error.message}\n`);
    console.error(`Error in getWrongRecords: ${error.message}`);
    return [];
  }
}

// Function to delete a Veeqo product variant
async function deleteVeeqoVariant(productId, variantId) {
  try {
    fs.appendFileSync(logFile, `Deleting Veeqo product ${productId} variant ${variantId}...\n`);
    console.log(`Deleting Veeqo product ${productId} variant ${variantId}...`);

    // Veeqo API endpoint for deleting a product variant
    const url = `https://api.veeqo.com/products/${productId}/variants/${variantId}`;

    // Make the API request
    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      fs.appendFileSync(logFile, `Error deleting Veeqo product ${productId} variant ${variantId}: ${response.status} ${response.statusText} - ${errorText}\n`);
      console.error(`Error deleting Veeqo product ${productId} variant ${variantId}: ${response.status} ${response.statusText} - ${errorText}`);
      return false;
    }

    fs.appendFileSync(logFile, `Successfully deleted Veeqo product ${productId} variant ${variantId}\n`);
    console.log(`Successfully deleted Veeqo product ${productId} variant ${variantId}`);
    return true;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in deleteVeeqoVariant for product ${productId} variant ${variantId}: ${error.message}\n`);
    console.error(`Error in deleteVeeqoVariant for product ${productId} variant ${variantId}: ${error.message}`);
    return false;
  }
}

// Function to get product variants
async function getProductVariants(productId) {
  try {
    fs.appendFileSync(logFile, `Getting variants for product ${productId}...\n`);
    console.log(`Getting variants for product ${productId}...`);

    // Veeqo API endpoint for getting product details
    const url = `https://api.veeqo.com/products/${productId}`;

    // Make the API request
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      fs.appendFileSync(logFile, `Error getting product ${productId} details: ${response.status} ${response.statusText} - ${errorText}\n`);
      console.error(`Error getting product ${productId} details: ${response.status} ${response.statusText} - ${errorText}`);
      return [];
    }

    const productData = await response.json();

    if (!productData.variants || productData.variants.length === 0) {
      fs.appendFileSync(logFile, `No variants found for product ${productId}\n`);
      console.error(`No variants found for product ${productId}`);
      return [];
    }

    fs.appendFileSync(logFile, `Found ${productData.variants.length} variants for product ${productId}\n`);
    console.log(`Found ${productData.variants.length} variants for product ${productId}`);
    return productData.variants;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in getProductVariants for product ${productId}: ${error.message}\n`);
    console.error(`Error in getProductVariants for product ${productId}: ${error.message}`);
    return [];
  }
}

// Function to find variant ID by title
async function findVariantIdByTitle(productId, variantTitle) {
  try {
    const variants = await getProductVariants(productId);

    if (variants.length === 0) {
      return null;
    }

    // Find the variant with the matching title
    const matchingVariant = variants.find(variant => variant.title === variantTitle);

    if (!matchingVariant) {
      fs.appendFileSync(logFile, `No variant found with title "${variantTitle}" for product ${productId}\n`);
      console.error(`No variant found with title "${variantTitle}" for product ${productId}`);
      return null;
    }

    fs.appendFileSync(logFile, `Found variant ID ${matchingVariant.id} with title "${variantTitle}" for product ${productId}\n`);
    console.log(`Found variant ID ${matchingVariant.id} with title "${variantTitle}" for product ${productId}`);
    return matchingVariant.id;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in findVariantIdByTitle for product ${productId} and title "${variantTitle}": ${error.message}\n`);
    console.error(`Error in findVariantIdByTitle for product ${productId} and title "${variantTitle}": ${error.message}`);
    return null;
  }
}

// Main function
async function main() {
  try {
    // Get records with wrong variant titles
    const wrongRecords = await getWrongRecords();

    if (wrongRecords.length === 0) {
      fs.appendFileSync(logFile, `No records found with wrong variant titles, nothing to delete\n`);
      console.log('No records found with wrong variant titles, nothing to delete');
      return;
    }

    // Apply limit if specified
    const recordsToProcess = limit ? wrongRecords.slice(0, limit) : wrongRecords;

    fs.appendFileSync(logFile, `Processing ${recordsToProcess.length} out of ${wrongRecords.length} total records\n`);
    console.log(`Processing ${recordsToProcess.length} out of ${wrongRecords.length} total records`);

    // Process each record
    let successCount = 0;
    let failureCount = 0;

    for (const record of recordsToProcess) {
      // Find the variant ID
      const variantId = await findVariantIdByTitle(record.product_id, record.variant_title);

      if (!variantId) {
        fs.appendFileSync(logFile, `Could not find variant ID for product ${record.product_id} with title "${record.variant_title}", skipping\n`);
        console.error(`Could not find variant ID for product ${record.product_id} with title "${record.variant_title}", skipping`);
        failureCount++;
        continue;
      }

      // Delete the variant
      const success = await deleteVeeqoVariant(record.product_id, variantId);

      if (success) {
        successCount++;
      } else {
        failureCount++;
      }

      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Log summary
    fs.appendFileSync(logFile, `\nDeletion summary:\n`);
    fs.appendFileSync(logFile, `Total records processed: ${wrongRecords.length}\n`);
    fs.appendFileSync(logFile, `Successful deletions: ${successCount}\n`);
    fs.appendFileSync(logFile, `Failed deletions: ${failureCount}\n`);

    console.log('\nDeletion summary:');
    console.log(`Total records processed: ${wrongRecords.length}`);
    console.log(`Successful deletions: ${successCount}`);
    console.log(`Failed deletions: ${failureCount}`);

  } catch (error) {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`Unhandled error: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the main function
console.log('Starting deletion of wrong Veeqo records...');
main()
  .then(() => {
    fs.appendFileSync(logFile, `Process completed at ${new Date().toISOString()}\n`);
    console.log('Process completed.');
  })
  .catch(error => {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    console.error(`Unhandled error: ${error.message}`);
  });
