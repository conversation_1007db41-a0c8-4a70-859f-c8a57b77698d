require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkMiniMolds() {
  try {
    console.log('Checking mini molds in the system...\n');
    
    // First, find all mini molds
    const { data: miniMolds, error: moldsError } = await supabase
      .from('t_molds')
      .select('id, mold, brand_id')
      .ilike('mold', '%mini%');
    
    if (moldsError) {
      console.error('Error fetching mini molds:', moldsError);
      return;
    }
    
    console.log(`Found ${miniMolds.length} mini molds:`);
    miniMolds.forEach(mold => {
      console.log(`- ID ${mold.id}: ${mold.mold} (brand_id: ${mold.brand_id})`);
    });
    
    if (miniMolds.length === 0) {
      console.log('No mini molds found in t_molds table');
      return;
    }
    
    // Get MPS records for these molds
    const moldIds = miniMolds.map(m => m.id);
    const { data: mpsRecords, error: mpsError } = await supabase
      .from('t_mps')
      .select('id, mold_id, plastic_id, stamp_id')
      .in('mold_id', moldIds);
    
    if (mpsError) {
      console.error('Error fetching MPS records:', mpsError);
      return;
    }
    
    console.log(`\nFound ${mpsRecords.length} MPS records for mini molds:`);
    mpsRecords.forEach(mps => {
      const mold = miniMolds.find(m => m.id === mps.mold_id);
      console.log(`- MPS ID ${mps.id}: ${mold.mold} (plastic_id: ${mps.plastic_id}, stamp_id: ${mps.stamp_id})`);
    });
    
    // Get OSL records for these MPS
    const mpsIds = mpsRecords.map(m => m.id);
    const { data: oslRecords, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, g_code')
      .in('mps_id', mpsIds);
    
    if (oslError) {
      console.error('Error fetching OSL records:', oslError);
      return;
    }
    
    console.log(`\nFound ${oslRecords.length} OSL records for mini molds:`);
    oslRecords.forEach(osl => {
      const mps = mpsRecords.find(m => m.id === osl.mps_id);
      const mold = miniMolds.find(m => m.id === mps.mold_id);
      console.log(`- OSL ID ${osl.id}: ${osl.g_code} (${mold.mold})`);
    });
    
    // Get disc records for these OSLs
    const oslIds = oslRecords.map(o => o.id);
    const { data: discRecords, error: discError } = await supabase
      .from('t_discs')
      .select('id, order_sheet_line_id, location, g_pull, sold_date')
      .in('order_sheet_line_id', oslIds)
      .is('sold_date', null);
    
    if (discError) {
      console.error('Error fetching disc records:', discError);
      return;
    }
    
    console.log(`\nFound ${discRecords.length} unsold disc records for mini molds:`);
    
    // Group by location
    const locationGroups = {};
    discRecords.forEach(disc => {
      const location = disc.location || 'NULL';
      if (!locationGroups[location]) {
        locationGroups[location] = [];
      }
      locationGroups[location].push(disc);
    });
    
    Object.keys(locationGroups).forEach(location => {
      const discs = locationGroups[location];
      console.log(`\n${location}: ${discs.length} discs`);
      discs.slice(0, 3).forEach(disc => { // Show first 3 discs per location
        const osl = oslRecords.find(o => o.id === disc.order_sheet_line_id);
        const mps = mpsRecords.find(m => m.id === osl.mps_id);
        const mold = miniMolds.find(m => m.id === mps.mold_id);
        console.log(`  - ID ${disc.id}: ${disc.g_pull} (${mold.mold})`);
      });
      if (discs.length > 3) {
        console.log(`  ... and ${discs.length - 3} more discs`);
      }
    });
    
    // Check specifically for disc 428958 that the user mentioned
    console.log('\n--- Checking specific disc 428958 ---');
    const { data: specificDisc, error: specificError } = await supabase
      .from('t_discs')
      .select(`
        id, 
        order_sheet_line_id, 
        location, 
        g_pull, 
        sold_date,
        t_order_sheet_lines!inner(
          id,
          mps_id,
          g_code,
          t_mps!inner(
            id,
            mold_id,
            plastic_id,
            stamp_id,
            t_molds!inner(
              id,
              mold
            )
          )
        )
      `)
      .eq('id', 428958)
      .single();
    
    if (specificError) {
      console.error('Error fetching specific disc:', specificError);
    } else if (specificDisc) {
      console.log('Disc 428958 details:');
      console.log(`- ID: ${specificDisc.id}`);
      console.log(`- Location: ${specificDisc.location}`);
      console.log(`- G_pull: ${specificDisc.g_pull}`);
      console.log(`- Sold date: ${specificDisc.sold_date}`);
      console.log(`- OSL: ${specificDisc.t_order_sheet_lines.g_code}`);
      console.log(`- Mold: ${specificDisc.t_order_sheet_lines.t_mps.t_molds.mold}`);
      console.log(`- Plastic ID: ${specificDisc.t_order_sheet_lines.t_mps.plastic_id}`);
      
      const moldName = specificDisc.t_order_sheet_lines.t_mps.t_molds.mold;
      const isMini = moldName.toLowerCase().includes('mini');
      console.log(`- Is mini mold: ${isMini}`);
      
      if (specificDisc.location === 'BS' && !specificDisc.sold_date && isMini) {
        console.log('✅ This disc SHOULD be selected for B2F (mini mold in back stock)');
      } else {
        console.log('❌ This disc does not meet mini mold B2F criteria');
      }
    } else {
      console.log('Disc 428958 not found');
    }
    
  } catch (err) {
    console.error('Exception:', err);
  }
}

checkMiniMolds();
