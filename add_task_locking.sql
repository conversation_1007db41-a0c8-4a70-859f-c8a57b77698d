-- Add locking columns to t_task_queue table
ALTER TABLE t_task_queue 
ADD COLUMN IF NOT EXISTS locked_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS locked_by TEXT;

-- Create an index on status and scheduled_at for better performance
CREATE INDEX IF NOT EXISTS idx_task_queue_status_scheduled 
ON t_task_queue(status, scheduled_at);

-- Create an index on locked_at for better performance
CREATE INDEX IF NOT EXISTS idx_task_queue_locked_at
ON t_task_queue(locked_at);

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Task locking columns added to t_task_queue table.';
END $$;
