// debugVerifyImage.js
import dotenv from 'dotenv';
dotenv.config();

import minimist from 'minimist';
import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';

// Parse command-line arguments (e.g., --id=68601)
// This id is the t_images record id
const args = minimist(process.argv.slice(2));
const id = args.id;
if (!id) {
  console.error('[debugVerifyImage.js] Missing required --id= argument.');
  process.exit(1);
}

console.log(`[debugVerifyImage.js] Received t_images id=${id}`);

// Create a Supabase client using your service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY; // Make sure this is your service role key!
console.log(`[debugVerifyImage.js] Supabase URL: ${supabaseUrl}`);
console.log(`[debugVerifyImage.js] Supabase Key length: ${supabaseKey ? supabaseKey.length : 'none'}`);

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to log errors to t_error_logs
async function logError(errorMessage, context) {
  try {
    const { error } = await supabase
      .from("t_error_logs")
      .insert({ error_message: errorMessage, context: context });
    if (error) {
      console.error(`[debugVerifyImage.js] Failed to log error: ${error.message}`);
    }
  } catch (err) {
    console.error(`[debugVerifyImage.js] Exception while logging error: ${err.message}`);
  }
}

async function main() {
  // 1. Retrieve the t_images record using its id
  console.log(`[debugVerifyImage.js] Retrieving t_images record for id=${id}...`);
  const { data: imageRecord, error: fetchError } = await supabase
    .from('t_images')
    .select('*')
    .eq('id', id)
    .maybeSingle();
    
  if (fetchError) {
    const errMsg = `[debugVerifyImage.js] Error retrieving t_images record: ${fetchError.message}`;
    console.error(errMsg);
    await logError(errMsg, `Retrieving t_images record for id=${id}`);
    process.exit(1);
  }
  if (!imageRecord) {
    const errMsg = `[debugVerifyImage.js] No t_images record found for id=${id}`;
    console.error(errMsg);
    await logError(errMsg, `No t_images record for id=${id}`);
    process.exit(1);
  }
  console.log(`[debugVerifyImage.js] Retrieved t_images record: ${JSON.stringify(imageRecord)}`);

  // 2. Retrieve configuration values from t_config:
  console.log(`[debugVerifyImage.js] Retrieving configuration from t_config...`);
  const { data: configData, error: configError } = await supabase
    .from('t_config')
    .select('key, value')
    .in('key', ['public_image_server', 'folder_brands', 'folder_discs', 'folder_molds', 'folder_mps', 'folder_players']);
  if (configError) {
    const errMsg = `[debugVerifyImage.js] Error retrieving t_config: ${configError.message}`;
    console.error(errMsg);
    await logError(errMsg, "Retrieving t_config values");
    process.exit(1);
  }
  
  console.log(`[debugVerifyImage.js] Retrieved config data: ${JSON.stringify(configData)}`);
  
  const publicImageServer = configData.find(c => c.key === 'public_image_server')?.value;
  if (!publicImageServer) {
    const errMsg = '[debugVerifyImage.js] public_image_server not found in t_config';
    console.error(errMsg);
    await logError(errMsg, "Missing public_image_server in t_config");
    process.exit(1);
  }
  console.log(`[debugVerifyImage.js] public_image_server: ${publicImageServer}`);

  // 3. Determine the folder key based on imageRecord.table_name
  let folderKey;
  switch (imageRecord.table_name) {
    case 't_brands':
      folderKey = 'folder_brands';
      break;
    case 't_discs':
      folderKey = 'folder_discs';
      break;
    case 't_molds':
      folderKey = 'folder_molds';
      break;
    case 't_mps':
      folderKey = 'folder_mps';
      break;
    case 't_players':
      folderKey = 'folder_players';
      break;
    default:
      const errMsg = `[debugVerifyImage.js] Unsupported table_name: ${imageRecord.table_name}`;
      console.error(errMsg);
      await logError(errMsg, "Determining folderKey from table_name");
      process.exit(1);
  }
  const folderValue = configData.find(c => c.key === folderKey)?.value;
  if (!folderValue) {
    const errMsg = `[debugVerifyImage.js] ${folderKey} not found in t_config`;
    console.error(errMsg);
    await logError(errMsg, `Missing ${folderKey} in t_config`);
    process.exit(1);
  }
  console.log(`[debugVerifyImage.js] Using folder for ${imageRecord.table_name} (${folderKey}): ${folderValue}`);

  // 4. Determine the file name:
  // For t_discs, retrieve the image_file_name using imageRecord.record_id.
  let fileName = imageRecord.record_id; // default fallback for non-discs records
  if (imageRecord.table_name === 't_discs') {
    console.log(`[debugVerifyImage.js] Retrieving t_discs record for id=${imageRecord.record_id}...`);
    const { data: discRecord, error: discError } = await supabase
      .from('t_discs')
      .select('image_file_name')
      .eq('id', imageRecord.record_id)
      .maybeSingle();
    if (discError) {
      const errMsg = `[debugVerifyImage.js] Error retrieving t_discs record for id=${imageRecord.record_id}: ${discError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving t_discs record for id=${imageRecord.record_id}`);
      process.exit(1);
    }
    console.log(`[debugVerifyImage.js] Retrieved t_discs record: ${JSON.stringify(discRecord)}`);
    if (discRecord && discRecord.image_file_name) {
      fileName = discRecord.image_file_name;
    } else {
      const errMsg = `[debugVerifyImage.js] No image_file_name found for t_discs record id=${imageRecord.record_id}`;
      console.error(errMsg);
      await logError(errMsg, `Missing image_file_name for t_discs record id=${imageRecord.record_id}`);
      process.exit(1);
    }
  }
  console.log(`[debugVerifyImage.js] Using fileName: ${fileName}`);

  // 5. Construct the image URL:
  // For t_discs, use a subfolder (first 6 characters of the fileName).
  let constructedImageUrl;
  if (imageRecord.table_name === 't_discs') {
    const subfolder = fileName.substring(0, 6);
    constructedImageUrl = `${publicImageServer}/${folderValue}/${subfolder}/${fileName}.jpg`;
  } else {
    constructedImageUrl = `${publicImageServer}/${folderValue}/${fileName}.jpg`;
  }
  console.log(`[debugVerifyImage.js] Constructed image URL: ${constructedImageUrl}`);

  // 6. Perform a HEAD request to check image accessibility
  console.log(`[debugVerifyImage.js] Performing HEAD request for URL: ${constructedImageUrl}`);
  try {
    const response = await fetch(constructedImageUrl, { method: 'HEAD' });
    console.log(`[debugVerifyImage.js] HEAD request completed. Status: ${response.status}`);
    const isOk = response.ok;
    console.log(`[debugVerifyImage.js] Image accessible: ${isOk}`);

    // 7. Build the update payload
    const updatePayload = {
      image_verified: isOk,
      image_verified_at: new Date().toISOString(),
      image_verified_notes: isOk
        ? 'Success! Image is accessible'
        : `Not Found! Image not accessible, status code: ${response.status} for URL: ${constructedImageUrl}`,
      updated_by: 'debugVerifyImage'
    };
    console.log(`[debugVerifyImage.js] Update payload for t_images record id=${id}: ${JSON.stringify(updatePayload)}`);

    // 8. Update the t_images row using its id
    console.log(`[debugVerifyImage.js] Updating t_images record for id=${id}...`);
    const { data: updateData, error: updateError } = await supabase
      .from('t_images')
      .update(updatePayload)
      .eq('id', id)
      .select();
    if (updateError) {
      const errMsg = `[debugVerifyImage.js] Error updating t_images record for id=${id}: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating t_images record for id=${id}`);
    } else {
      console.log(`[debugVerifyImage.js] Successfully updated t_images record for id=${id}`);
      console.log(`[debugVerifyImage.js] Updated record: ${JSON.stringify(updateData)}`);
    }
  } catch (fetchErr) {
    const errMsg = `[debugVerifyImage.js] Error performing HEAD request: ${fetchErr.message}`;
    console.error(errMsg);
    await logError(errMsg, `Performing HEAD request for URL: ${constructedImageUrl}`);
    
    // Still update the t_images record with the error
    const updatePayload = {
      image_verified: false,
      image_verified_at: new Date().toISOString(),
      image_verified_notes: `Error! Failed to check image: ${fetchErr.message}`,
      updated_by: 'debugVerifyImage'
    };
    console.log(`[debugVerifyImage.js] Update payload for t_images record id=${id}: ${JSON.stringify(updatePayload)}`);
    
    const { error: updateError } = await supabase
      .from('t_images')
      .update(updatePayload)
      .eq('id', id);
    if (updateError) {
      const errMsg = `[debugVerifyImage.js] Error updating t_images record for id=${id}: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating t_images record for id=${id}`);
    } else {
      console.log(`[debugVerifyImage.js] Successfully updated t_images record for id=${id}`);
    }
  }
}

main().catch(async err => {
  console.error(`[debugVerifyImage.js] Unexpected error: ${err.message}`);
  await logError(`[debugVerifyImage.js] Unexpected error: ${err.message}`, "Main function");
  process.exit(1);
});
