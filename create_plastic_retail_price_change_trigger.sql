-- Function to enqueue a task for plastic retail price changes
CREATE OR REPLACE FUNCTION fn_enqueue_plastic_retail_price_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Only enqueue a task if val_retail_price has changed
    IF OLD.val_retail_price IS DISTINCT FROM NEW.val_retail_price THEN
        
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'plastic_retail_price_change',
            jsonb_build_object(
                'id', NEW.id,
                'old_retail_price', OLD.val_retail_price,
                'new_retail_price', NEW.val_retail_price,
                'plastic_name', NEW.plastic
            ),
            'pending',
            NOW(),
            NOW()
        );
        
        -- Log the change
        RAISE NOTICE 'Enqueued plastic_retail_price_change task for plastic ID % (%) - price changed from % to %', 
            NEW.id, NEW.plastic, OLD.val_retail_price, NEW.val_retail_price;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trg_enqueue_plastic_retail_price_change ON t_plastics;

CREATE TRIGGER trg_enqueue_plastic_retail_price_change
AFTER UPDATE OF val_retail_price ON t_plastics
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_plastic_retail_price_change();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger trg_enqueue_plastic_retail_price_change has been created on t_plastics.val_retail_price updates.';
END $$;
