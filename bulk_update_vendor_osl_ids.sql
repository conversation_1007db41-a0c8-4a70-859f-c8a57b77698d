-- Bulk update all t_discs records with null vendor_osl_id
-- This uses a single SQL operation for better performance

-- First, let's see how many records need updating
SELECT 
    COUNT(*) as total_discs_needing_update,
    COUNT(CASE WHEN weight_mfg IS NOT NULL THEN 1 END) as discs_with_weight_mfg,
    COUNT(CASE WHEN mps_id IS NOT NULL THEN 1 END) as discs_with_mps_id,
    COUNT(CASE WHEN color_id IS NOT NULL THEN 1 END) as discs_with_color_id
FROM t_discs 
WHERE vendor_osl_id IS NULL;

-- Now perform the bulk update
-- This will update vendor_osl_id for all discs where:
-- 1. vendor_osl_id is currently NULL
-- 2. weight_mfg, mps_id, and color_id are not NULL
-- 3. A matching OSL exists based on the manufacturer weight

UPDATE t_discs 
SET vendor_osl_id = (
    SELECT osl.id
    FROM t_order_sheet_lines osl
    WHERE osl.mps_id = t_discs.mps_id
      AND (osl.color_id = t_discs.color_id OR osl.color_id = 23)
      AND ROUND(t_discs.weight_mfg) >= osl.min_weight
      AND ROUND(t_discs.weight_mfg) <= osl.max_weight
    LIMIT 1
)
WHERE vendor_osl_id IS NULL
  AND weight_mfg IS NOT NULL
  AND mps_id IS NOT NULL
  AND color_id IS NOT NULL
  AND EXISTS (
    SELECT 1
    FROM t_order_sheet_lines osl
    WHERE osl.mps_id = t_discs.mps_id
      AND (osl.color_id = t_discs.color_id OR osl.color_id = 23)
      AND ROUND(t_discs.weight_mfg) >= osl.min_weight
      AND ROUND(t_discs.weight_mfg) <= osl.max_weight
  );

-- Check the results
SELECT 
    COUNT(*) as total_discs,
    COUNT(CASE WHEN vendor_osl_id IS NOT NULL THEN 1 END) as discs_with_vendor_osl_id,
    COUNT(CASE WHEN vendor_osl_id IS NULL THEN 1 END) as discs_without_vendor_osl_id,
    COUNT(CASE WHEN vendor_osl_id IS NOT NULL AND vendor_osl_id != order_sheet_line_id THEN 1 END) as discs_with_different_mappings
FROM t_discs 
WHERE weight_mfg IS NOT NULL 
  AND mps_id IS NOT NULL 
  AND color_id IS NOT NULL;
