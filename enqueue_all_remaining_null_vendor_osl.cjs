require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function enqueueAllRemainingNullVendorOsl() {
  try {
    console.log('Getting ALL remaining discs with null vendor_osl_id (regardless of sold status)...');
    
    // First, check what's already been enqueued across all our previous runs
    const { data: alreadyEnqueued, error: enqueuedError } = await supabase
      .from('t_task_queue')
      .select('payload')
      .eq('task_type', 'match_disc_to_osl')
      .in('enqueued_by', [
        'final_vendor_osl_check',
        'final_vendor_osl_check_round2', 
        'sold_discs_vendor_osl_check',
        'sold_discs_vendor_osl_check_remaining'
      ]);
    
    if (enqueuedError) {
      console.error('Error checking already enqueued tasks:', enqueuedError);
      return;
    }
    
    const enqueuedDiscIds = alreadyEnqueued.map(task => parseInt(task.payload.id));
    console.log(`Found ${enqueuedDiscIds.length} already enqueued match_disc_to_osl tasks from previous runs`);
    
    // Get ALL discs with null vendor_osl_id (no sold_date filter, no limits!)
    const { data: allNullVendorDiscs, error: fetchError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .is('vendor_osl_id', null)
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null)
      .order('id');
    
    if (fetchError) {
      console.error('Error getting discs with null vendor_osl_id:', fetchError);
      return;
    }
    
    console.log(`Found ${allNullVendorDiscs.length} total discs with null vendor_osl_id`);
    
    // Filter out already enqueued discs
    const remainingDiscs = allNullVendorDiscs.filter(disc => !enqueuedDiscIds.includes(disc.id));
    
    console.log(`Remaining to enqueue: ${remainingDiscs.length} discs`);
    
    if (remainingDiscs.length === 0) {
      console.log('✅ All discs with null vendor_osl_id have already been enqueued!');
      return;
    }
    
    // Show breakdown by sold status
    const unsoldRemaining = remainingDiscs.filter(disc => disc.sold_date === null);
    const soldRemaining = remainingDiscs.filter(disc => disc.sold_date !== null);
    
    console.log(`\nBreakdown of remaining discs:`);
    console.log(`- Unsold discs: ${unsoldRemaining.length}`);
    console.log(`- Sold discs: ${soldRemaining.length}`);
    console.log(`- Total: ${remainingDiscs.length}`);
    
    console.log('\nFirst 10 remaining discs as sample:');
    remainingDiscs.slice(0, 10).forEach((disc, index) => {
      const soldStatus = disc.sold_date ? `Sold: ${disc.sold_date}` : 'Unsold';
      console.log(`${index + 1}. Disc ${disc.id}: MPS ${disc.mps_id}, Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id}, ${soldStatus}`);
    });
    
    if (remainingDiscs.length > 10) {
      console.log(`... and ${remainingDiscs.length - 10} more remaining discs`);
    }
    
    // Enqueue match_disc_to_osl tasks for all remaining discs
    console.log('\nEnqueueing match_disc_to_osl tasks for ALL remaining discs...');
    
    const tasksToEnqueue = remainingDiscs.map(disc => ({
      task_type: 'match_disc_to_osl',
      payload: {
        id: disc.id,
        operation: 'UPDATE',
        old_data: disc
      },
      status: 'pending',
      scheduled_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      enqueued_by: 'all_remaining_vendor_osl_check'
    }));
    
    // Insert tasks in batches to handle large numbers
    const batchSize = 100;
    let totalEnqueued = 0;
    let allEnqueuedTasks = [];
    
    console.log(`Processing ${tasksToEnqueue.length} tasks in batches of ${batchSize}...`);
    
    for (let i = 0; i < tasksToEnqueue.length; i += batchSize) {
      const batch = tasksToEnqueue.slice(i, i + batchSize);
      
      const { data: enqueuedBatch, error: enqueueError } = await supabase
        .from('t_task_queue')
        .insert(batch)
        .select('id, task_type, payload');
      
      if (enqueueError) {
        console.error(`Error enqueueing batch ${Math.floor(i / batchSize) + 1}:`, enqueueError);
        continue;
      }
      
      totalEnqueued += enqueuedBatch.length;
      allEnqueuedTasks = allEnqueuedTasks.concat(enqueuedBatch);
      
      const batchNum = Math.floor(i / batchSize) + 1;
      const totalBatches = Math.ceil(tasksToEnqueue.length / batchSize);
      console.log(`Batch ${batchNum}/${totalBatches}: Enqueued ${enqueuedBatch.length} tasks (Total: ${totalEnqueued}/${remainingDiscs.length})`);
    }
    
    console.log(`\n✅ Successfully enqueued ${totalEnqueued} additional match_disc_to_osl tasks!`);
    
    console.log('\nFirst 5 new task details:');
    allEnqueuedTasks.slice(0, 5).forEach((task, index) => {
      const discId = task.payload.id;
      console.log(`${index + 1}. Task ${task.id}: match_disc_to_osl for disc ${discId}`);
    });
    
    if (allEnqueuedTasks.length > 5) {
      console.log(`... and ${allEnqueuedTasks.length - 5} more new tasks`);
    }
    
    // Show comprehensive summary
    const totalTasksEnqueued = enqueuedDiscIds.length + totalEnqueued;
    console.log(`\n📊 COMPREHENSIVE TASK SUMMARY:`);
    console.log(`Previously enqueued across all runs: ${enqueuedDiscIds.length} tasks`);
    console.log(`Just enqueued: ${totalEnqueued} tasks`);
    console.log(`Total match_disc_to_osl tasks enqueued: ${totalTasksEnqueued}`);
    console.log(`Total discs with null vendor_osl_id: ${allNullVendorDiscs.length}`);
    
    if (totalTasksEnqueued >= allNullVendorDiscs.length) {
      console.log('✅ ALL discs with null vendor_osl_id have now been enqueued!');
    } else {
      console.log(`⚠️ Still missing: ${allNullVendorDiscs.length - totalTasksEnqueued} discs`);
    }
    
    console.log('\n📋 To check status for ALL vendor OSL tasks across all runs, run:');
    console.log(`
SELECT 
  enqueued_by,
  COUNT(*) as total_tasks,
  status,
  COUNT(CASE WHEN result->>'vendor_osl_id' IS NOT NULL THEN 1 END) as found_vendor_mappings
FROM t_task_queue 
WHERE task_type = 'match_disc_to_osl'
  AND enqueued_by IN (
    'final_vendor_osl_check',
    'final_vendor_osl_check_round2', 
    'sold_discs_vendor_osl_check',
    'sold_discs_vendor_osl_check_remaining',
    'all_remaining_vendor_osl_check'
  )
GROUP BY enqueued_by, status
ORDER BY enqueued_by, status;
    `);
    
    console.log('\nTo get final vendor OSL mapping statistics after processing:');
    console.log(`
SELECT 
  CASE WHEN sold_date IS NULL THEN 'Unsold' ELSE 'Sold' END as disc_status,
  COUNT(*) as total_discs,
  COUNT(CASE WHEN vendor_osl_id IS NOT NULL THEN 1 END) as with_vendor_osl,
  ROUND(COUNT(CASE WHEN vendor_osl_id IS NOT NULL THEN 1 END) * 100.0 / COUNT(*), 1) as success_rate
FROM t_discs 
WHERE weight_mfg IS NOT NULL 
  AND mps_id IS NOT NULL 
  AND color_id IS NOT NULL
GROUP BY CASE WHEN sold_date IS NULL THEN 'Unsold' ELSE 'Sold' END
ORDER BY disc_status;
    `);
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

enqueueAllRemainingNullVendorOsl();
