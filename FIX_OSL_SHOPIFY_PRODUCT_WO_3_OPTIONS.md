# Fix OSL Shopify Product Without 3 Options

## Overview
This feature automatically fixes OSL Shopify products that were created with incorrect option structures (not exactly 3 options). When the `publish_product_osl` task encounters this specific error, it automatically enqueues a fix task to resolve the issue.

## How It Works

### 1. Error Detection
When `publish_product_osl` encounters the error:
```
MANUAL FIX REQUIRED: Existing OS product (ID: 12345) has incorrect 2-option structure (...). OS products must have exactly 3 options: Weight Range, Mold, Color.
```

The task queue worker automatically:
- Detects this specific error pattern
- Extracts the MPS ID and Shopify product ID
- Enqueues a `fix_osl_shopify_product_wo_3_options` task

### 2. Fix Process
The `fix_osl_shopify_product_wo_3_options` task performs these steps:

1. **Delete Shopify Product**: Removes the incorrectly created product from Shopify
2. **Reset OSL Records**: Sets `shopify_uploaded_at = null` for all OSLs with the same `mps_id`
3. **Reset Original Task**: Changes the original `publish_product_osl` task back to 'pending' status

### 3. Automatic Retry
After the fix is complete:
- The original `publish_product_osl` task will be picked up again by the worker
- It will create a new Shopify product with the correct 3-option structure
- The OSL will be successfully published

## Files Modified

### taskQueueWorker.js
- Added error detection logic in `processPublishProductOslTask`
- Added task processor registration for `fix_osl_shopify_product_wo_3_options`
- Added import for the new task processor

### publishProductOSL.js
- Modified error message to include Shopify product ID for extraction

### New Files
- `processFixOslShopifyProductWo3OptionsTask.js`: Main task processor
- `test_fix_osl_shopify_product_wo_3_options.js`: Test script

## Task Payload Structure

```json
{
  "mps_id": 12345,
  "shopify_product_id": "8888888888888",
  "original_task_id": "task-123"
}
```

### Fields:
- `mps_id`: The MPS ID from the OSL record (required)
- `shopify_product_id`: The Shopify product ID to delete (optional)
- `original_task_id`: The ID of the original `publish_product_osl` task (required)

## Error Handling

The fix task handles various error scenarios:
- Missing Shopify product ID (skips deletion, continues with reset)
- Shopify deletion failures (logs error, continues with reset)
- Database errors (marks task as failed with detailed error info)

## Monitoring

You can monitor the fix process by checking:
- Task queue for `fix_osl_shopify_product_wo_3_options` tasks
- OSL records for reset `shopify_uploaded_at` values
- Original task status changes from 'error' back to 'pending'

## Benefits

1. **Automatic Resolution**: No manual intervention required
2. **Complete Fix**: Handles product deletion, data reset, and retry
3. **Idempotent**: Safe to run multiple times
4. **Traceable**: Full logging and task result tracking
5. **Robust**: Continues even if some steps fail

## Example Workflow

1. OSL publish fails: "Product has 2 options, needs 3"
2. Fix task enqueued automatically
3. Fix task deletes bad Shopify product
4. Fix task resets 3 OSL records with same MPS ID
5. Fix task resets original task to pending
6. Original task retries and succeeds with new product
