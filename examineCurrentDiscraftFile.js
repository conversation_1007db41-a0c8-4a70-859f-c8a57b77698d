import XLSX from 'xlsx';
import path from 'path';

async function examineCurrentDiscraftFile() {
    try {
        console.log('🔍 Examining current Discraft Excel file...\n');
        
        const filePath = path.join(process.cwd(), 'data', 'external data', 'discraftstock.xlsx');
        console.log(`📁 Reading file: ${filePath}`);
        
        // Read the Excel file
        const workbook = XLSX.readFile(filePath);
        const sheetName = 'Order Form';
        const worksheet = workbook.Sheets[sheetName];
        
        if (!worksheet) {
            throw new Error(`Sheet "${sheetName}" not found`);
        }
        
        const range = XLSX.utils.decode_range(worksheet['!ref']);
        console.log(`📊 Sheet range: ${XLSX.utils.encode_range(range)} (${range.e.r + 1} rows, ${range.e.c + 1} columns)`);
        
        // Examine rows 20-35 to understand the fundraiser section structure
        console.log('\n🔍 Examining rows 20-35 (fundraiser section):');
        
        for (let row = 19; row <= 34; row++) { // 0-based, so 19 = Excel row 20
            console.log(`\n--- Row ${row + 1} (Excel row ${row + 1}) ---`);
            
            const rowData = {};
            let hasData = false;
            
            // Read all columns for this row
            for (let col = 0; col <= Math.min(range.e.c, 25); col++) { // Limit to first 26 columns (A-Z)
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                const cell = worksheet[cellAddress];
                if (cell && cell.v !== null && cell.v !== undefined && cell.v !== '') {
                    const colLetter = String.fromCharCode(65 + col); // A=65, B=66, etc.
                    rowData[colLetter] = cell.v;
                    hasData = true;
                }
            }
            
            if (hasData) {
                // Show the data in a readable format
                Object.entries(rowData).forEach(([col, value]) => {
                    console.log(`   ${col}: "${value}"`);
                });
            } else {
                console.log('   (empty row)');
            }
        }
        
        // Look specifically for fundraiser-related content
        console.log('\n🔍 Looking for fundraiser-related content in the entire sheet...');
        
        let fundraiserRows = [];
        for (let row = 0; row <= range.e.r; row++) {
            for (let col = 0; col <= range.e.c; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                const cell = worksheet[cellAddress];
                if (cell && cell.v && cell.v.toString().toLowerCase().includes('fundraiser')) {
                    const colLetter = String.fromCharCode(65 + col);
                    fundraiserRows.push({
                        row: row + 1,
                        col: colLetter,
                        value: cell.v
                    });
                }
            }
        }
        
        console.log(`✅ Found ${fundraiserRows.length} cells containing "fundraiser":`);
        fundraiserRows.forEach((item, index) => {
            console.log(`   ${index + 1}. Row ${item.row}, Col ${item.col}: "${item.value}"`);
        });
        
        // Look for "Ben Askren" content
        console.log('\n🔍 Looking for "Ben Askren" content...');
        
        let benAskrenRows = [];
        for (let row = 0; row <= range.e.r; row++) {
            for (let col = 0; col <= range.e.c; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                const cell = worksheet[cellAddress];
                if (cell && cell.v && cell.v.toString().toLowerCase().includes('ben askren')) {
                    const colLetter = String.fromCharCode(65 + col);
                    benAskrenRows.push({
                        row: row + 1,
                        col: colLetter,
                        value: cell.v
                    });
                }
            }
        }
        
        console.log(`✅ Found ${benAskrenRows.length} cells containing "Ben Askren":`);
        benAskrenRows.forEach((item, index) => {
            console.log(`   ${index + 1}. Row ${item.row}, Col ${item.col}: "${item.value}"`);
        });
        
        console.log('\n🎯 Analysis complete! This will help understand the correct structure.');
        
    } catch (error) {
        console.error('❌ Error examining file:', error.message);
    }
}

examineCurrentDiscraftFile().catch(console.error);
