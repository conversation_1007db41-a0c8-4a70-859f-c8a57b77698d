import fetch from 'node-fetch';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

if (!veeqoApiKey) {
  console.error('Error: VEEQO_API_KEY must be set in .env file');
  process.exit(1);
}

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint) {
  try {
    const response = await fetch(endpoint, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error response: ${errorText}`);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error making request: ${error.message}`);
    return null;
  }
}

// Function to analyze a sample of products
async function analyzeSampleProducts() {
  console.log('🔍 Analyzing Veeqo products for archived characteristics...\n');
  
  // Get a sample of products
  const products = await makeVeeqoRequest('https://api.veeqo.com/products?page=1&page_size=50');
  
  if (!products || !Array.isArray(products)) {
    console.error('Failed to fetch products');
    return;
  }
  
  console.log(`📊 Analyzing ${products.length} products...\n`);
  
  // Analyze different characteristics
  const analysis = {
    total: products.length,
    deleted: 0,
    requiresReview: 0,
    noActiveChannels: 0,
    pulledFromChannels: 0,
    hasChannelProducts: 0,
    examples: {
      deleted: [],
      requiresReview: [],
      noActiveChannels: [],
      pulledFromChannels: []
    }
  };
  
  products.forEach((product, index) => {
    // Check if deleted
    if (product.deleted_at !== null) {
      analysis.deleted++;
      if (analysis.examples.deleted.length < 3) {
        analysis.examples.deleted.push({
          id: product.id,
          title: product.title,
          deleted_at: product.deleted_at
        });
      }
    }
    
    // Check if requires review
    if (product.requires_review === true) {
      analysis.requiresReview++;
      if (analysis.examples.requiresReview.length < 3) {
        analysis.examples.requiresReview.push({
          id: product.id,
          title: product.title,
          requires_review: product.requires_review
        });
      }
    }
    
    // Check active channels
    if (!product.active_channels || product.active_channels.length === 0) {
      analysis.noActiveChannels++;
      if (analysis.examples.noActiveChannels.length < 3) {
        analysis.examples.noActiveChannels.push({
          id: product.id,
          title: product.title,
          active_channels: product.active_channels
        });
      }
    }
    
    // Check channel products
    if (product.channel_products && product.channel_products.length > 0) {
      analysis.hasChannelProducts++;
      
      const pulledChannels = product.channel_products.filter(cp => cp.status === 'pulled');
      if (pulledChannels.length > 0) {
        analysis.pulledFromChannels++;
        if (analysis.examples.pulledFromChannels.length < 3) {
          analysis.examples.pulledFromChannels.push({
            id: product.id,
            title: product.title,
            totalChannels: product.channel_products.length,
            pulledChannels: pulledChannels.length,
            channelStatuses: product.channel_products.map(cp => ({
              channel: cp.channel?.short_name || 'Unknown',
              status: cp.status
            }))
          });
        }
      }
    }
  });
  
  // Display results
  console.log('📈 ANALYSIS RESULTS:');
  console.log('='.repeat(50));
  console.log(`Total products analyzed: ${analysis.total}`);
  console.log(`Products with deleted_at set: ${analysis.deleted}`);
  console.log(`Products requiring review: ${analysis.requiresReview}`);
  console.log(`Products with no active channels: ${analysis.noActiveChannels}`);
  console.log(`Products with channel products: ${analysis.hasChannelProducts}`);
  console.log(`Products pulled from channels: ${analysis.pulledFromChannels}`);
  
  console.log('\n🔍 DETAILED EXAMPLES:');
  console.log('='.repeat(50));
  
  if (analysis.examples.deleted.length > 0) {
    console.log('\n❌ DELETED PRODUCTS:');
    analysis.examples.deleted.forEach(item => {
      console.log(`  - ID: ${item.id}, Title: "${item.title}", Deleted: ${item.deleted_at}`);
    });
  }
  
  if (analysis.examples.requiresReview.length > 0) {
    console.log('\n⚠️  PRODUCTS REQUIRING REVIEW:');
    analysis.examples.requiresReview.forEach(item => {
      console.log(`  - ID: ${item.id}, Title: "${item.title}"`);
    });
  }
  
  if (analysis.examples.noActiveChannels.length > 0) {
    console.log('\n🚫 PRODUCTS WITH NO ACTIVE CHANNELS:');
    analysis.examples.noActiveChannels.forEach(item => {
      console.log(`  - ID: ${item.id}, Title: "${item.title}"`);
    });
  }
  
  if (analysis.examples.pulledFromChannels.length > 0) {
    console.log('\n📤 PRODUCTS PULLED FROM CHANNELS:');
    analysis.examples.pulledFromChannels.forEach(item => {
      console.log(`  - ID: ${item.id}, Title: "${item.title}"`);
      console.log(`    Pulled from ${item.pulledChannels}/${item.totalChannels} channels`);
      item.channelStatuses.forEach(cs => {
        console.log(`    ${cs.channel}: ${cs.status}`);
      });
      console.log('');
    });
  }
  
  // Identify potential "archived" products
  console.log('\n🗂️  POTENTIAL "ARCHIVED" PRODUCTS:');
  console.log('='.repeat(50));
  
  const potentialArchived = products.filter(product => {
    return product.deleted_at !== null || 
           (!product.active_channels || product.active_channels.length === 0) ||
           (product.channel_products && 
            product.channel_products.length > 0 && 
            product.channel_products.every(cp => cp.status === 'pulled'));
  });
  
  console.log(`Found ${potentialArchived.length} potentially archived products:`);
  potentialArchived.slice(0, 10).forEach(product => {
    const reasons = [];
    if (product.deleted_at !== null) reasons.push('deleted');
    if (!product.active_channels || product.active_channels.length === 0) reasons.push('no_active_channels');
    if (product.channel_products && product.channel_products.length > 0 && 
        product.channel_products.every(cp => cp.status === 'pulled')) reasons.push('all_channels_pulled');
    
    console.log(`  - ID: ${product.id}, Title: "${product.title}"`);
    console.log(`    Reasons: ${reasons.join(', ')}`);
  });
  
  if (potentialArchived.length > 10) {
    console.log(`  ... and ${potentialArchived.length - 10} more`);
  }
  
  return analysis;
}

// Function to create an unarchive script
function createUnarchiveScript(analysis) {
  const unarchiveScript = `import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config();

const veeqoApiKey = process.env.VEEQO_API_KEY;

// Function to unarchive a product by updating its channel status
async function unarchiveProduct(productId, channelId = 244185) {
  try {
    console.log(\`🔄 Attempting to unarchive product \${productId}...\`);
    
    // First, get the product details
    const productResponse = await fetch(\`https://api.veeqo.com/products/\${productId}\`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    });
    
    if (!productResponse.ok) {
      console.error(\`❌ Failed to get product \${productId}: \${productResponse.status}\`);
      return false;
    }
    
    const product = await productResponse.json();
    console.log(\`📋 Product: "\${product.title}"\`);
    
    // Check if product has channel products that are pulled
    if (product.channel_products && product.channel_products.length > 0) {
      const pulledChannels = product.channel_products.filter(cp => cp.status === 'pulled');
      
      if (pulledChannels.length > 0) {
        console.log(\`📤 Found \${pulledChannels.length} pulled channel(s)\`);
        
        // Note: The exact API endpoint for updating channel product status 
        // may vary. You might need to check Veeqo's API documentation for the correct endpoint.
        // This is a placeholder for the actual unarchive operation.
        
        console.log(\`⚠️  Manual action required: Update channel product status in Veeqo interface\`);
        console.log(\`   Product ID: \${productId}\`);
        console.log(\`   Product Title: \${product.title}\`);
        
        return true;
      }
    }
    
    console.log(\`✅ Product \${productId} doesn't appear to need unarchiving\`);
    return true;
    
  } catch (error) {
    console.error(\`❌ Error unarchiving product \${productId}: \${error.message}\`);
    return false;
  }
}

// Example usage:
// unarchiveProduct(211445804); // Replace with actual product ID

export { unarchiveProduct };
`;

  fs.writeFileSync('unarchiveVeeqoProduct.js', unarchiveScript);
  console.log('\n📝 Created unarchiveVeeqoProduct.js script for manual unarchiving');
}

// Main function
async function main() {
  try {
    const analysis = await analyzeSampleProducts();
    createUnarchiveScript(analysis);
    
    console.log('\n✅ Analysis complete!');
    console.log('\n💡 KEY FINDINGS:');
    console.log('- Veeqo doesn\'t have a traditional "archived" status');
    console.log('- Products can be considered "archived" if they are:');
    console.log('  • Deleted (deleted_at is not null)');
    console.log('  • Have no active channels');
    console.log('  • Are pulled from all channels (status = "pulled")');
    console.log('  • Require review (might indicate inactive state)');
    
  } catch (error) {
    console.error(`Fatal error: ${error.message}`);
    process.exit(1);
  }
}

// Run the analysis
main();
