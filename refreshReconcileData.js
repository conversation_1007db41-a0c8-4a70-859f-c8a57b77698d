// refreshReconcileData.js - <PERSON>ript to refresh the reconciliation data

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

// Create a log file
const logFile = 'refresh_reconcile.log';
fs.writeFileSync(logFile, `Starting reconciliation data refresh at ${new Date().toISOString()}\n`);

// Load environment variables
dotenv.config();
fs.appendFileSync(logFile, `Environment variables loaded\n`);

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  const errorMsg = 'Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file';
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

fs.appendFileSync(logFile, `Supabase credentials found\n`);
const supabase = createClient(supabaseUrl, supabaseKey);
fs.appendFileSync(logFile, `Supabase client created\n`);

// Read the SQL file
const sqlFilePath = './reconcile_table_simple.sql';
fs.appendFileSync(logFile, `Reading SQL file: ${sqlFilePath}\n`);

if (!fs.existsSync(sqlFilePath)) {
  const errorMsg = `SQL file not found: ${sqlFilePath}`;
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
fs.appendFileSync(logFile, `SQL file read successfully\n`);

// Function to execute SQL
async function executeSql(sql) {
  try {
    fs.appendFileSync(logFile, `Executing SQL...\n`);
    console.log('Executing SQL...');

    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql_query: sql });

    if (error) {
      fs.appendFileSync(logFile, `Error executing SQL: ${error.message}\n`);
      console.error(`Error executing SQL: ${error.message}`);

      // Try to execute the SQL in smaller chunks
      fs.appendFileSync(logFile, `Trying to execute SQL in smaller chunks...\n`);
      console.log('Trying to execute SQL in smaller chunks...');

      // Split the SQL into statements
      const statements = sql.split(';').filter(stmt => stmt.trim() !== '');

      for (let i = 0; i < statements.length; i++) {
        const statement = statements[i].trim() + ';';
        fs.appendFileSync(logFile, `Executing statement ${i + 1} of ${statements.length}...\n`);
        console.log(`Executing statement ${i + 1} of ${statements.length}...`);

        const { error: stmtError } = await supabase.rpc('exec_sql', { sql_query: statement });

        if (stmtError) {
          fs.appendFileSync(logFile, `Error executing statement ${i + 1}: ${stmtError.message}\n`);
          console.error(`Error executing statement ${i + 1}: ${stmtError.message}`);
        } else {
          fs.appendFileSync(logFile, `Statement ${i + 1} executed successfully\n`);
          console.log(`Statement ${i + 1} executed successfully`);
        }
      }

      return false;
    }

    fs.appendFileSync(logFile, `SQL executed successfully\n`);
    console.log('SQL executed successfully');
    return true;
  } catch (error) {
    fs.appendFileSync(logFile, `Error executing SQL: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`Error executing SQL: ${error.message}`);
    console.error(error.stack);
    return false;
  }
}

// Function to get total count with pagination
async function getTotalCount() {
  try {
    let totalCount = 0;
    let hasMore = true;
    let page = 0;
    const pageSize = 1000;

    while (hasMore) {
      const { count, error } = await supabase
        .from('reconcile_rpro_counts_to_veeqo')
        .select('*', { count: 'exact', head: true })
        .range(page * pageSize, (page + 1) * pageSize - 1);

      if (error) {
        fs.appendFileSync(logFile, `Error getting total count on page ${page}: ${error.message}\n`);
        console.error(`Error getting total count on page ${page}: ${error.message}`);
        return totalCount;
      }

      if (count === 0) {
        hasMore = false;
      } else {
        totalCount += count;
        page++;
        fs.appendFileSync(logFile, `Retrieved ${count} records on page ${page}, total so far: ${totalCount}\n`);
        console.log(`Retrieved ${count} records on page ${page}, total so far: ${totalCount}`);
      }
    }

    return totalCount;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in getTotalCount: ${error.message}\n`);
    console.error(`Error in getTotalCount: ${error.message}`);
    return 0;
  }
}

// Function to count records with a specific condition using pagination
async function getCountWithCondition(condition) {
  try {
    let totalCount = 0;
    let hasMore = true;
    let page = 0;
    const pageSize = 1000;

    while (hasMore) {
      let query = supabase
        .from('reconcile_rpro_counts_to_veeqo')
        .select('*', { count: 'exact', head: true })
        .range(page * pageSize, (page + 1) * pageSize - 1);

      // Apply the condition
      if (condition === 'discrepancy') {
        query = query.not('quantity_difference', 'eq', 0);
      } else if (condition === 'rpro_more') {
        query = query.gt('quantity_difference', 0);
      } else if (condition === 'veeqo_more') {
        query = query.lt('quantity_difference', 0);
      }

      const { count, error } = await query;

      if (error) {
        fs.appendFileSync(logFile, `Error getting ${condition} count on page ${page}: ${error.message}\n`);
        console.error(`Error getting ${condition} count on page ${page}: ${error.message}`);
        return totalCount;
      }

      if (count === 0) {
        hasMore = false;
      } else {
        totalCount += count;
        page++;
        fs.appendFileSync(logFile, `Retrieved ${count} ${condition} records on page ${page}, total so far: ${totalCount}\n`);
        console.log(`Retrieved ${count} ${condition} records on page ${page}, total so far: ${totalCount}`);
      }
    }

    return totalCount;
  } catch (error) {
    fs.appendFileSync(logFile, `Error in getCountWithCondition for ${condition}: ${error.message}\n`);
    console.error(`Error in getCountWithCondition for ${condition}: ${error.message}`);
    return 0;
  }
}

// Function to get reconciliation statistics
async function getReconcileStats() {
  try {
    fs.appendFileSync(logFile, `Getting reconciliation statistics...\n`);
    console.log('Getting reconciliation statistics...');

    // Get total count with pagination
    const totalCount = await getTotalCount();

    // Get count of discrepancies with pagination
    const discrepancyCount = await getCountWithCondition('discrepancy');

    // Get count of RPRO having more with pagination
    const rproMoreCount = await getCountWithCondition('rpro_more');

    // Get count of Veeqo having more with pagination
    const veeqoMoreCount = await getCountWithCondition('veeqo_more');

    // Log and display statistics
    const stats = {
      totalCount,
      discrepancyCount,
      rproMoreCount,
      veeqoMoreCount,
      matchingCount: totalCount - discrepancyCount
    };

    fs.appendFileSync(logFile, `Reconciliation statistics: ${JSON.stringify(stats, null, 2)}\n`);
    console.log('Reconciliation statistics:');
    console.log(`Total records: ${stats.totalCount}`);
    console.log(`Records with discrepancies: ${stats.discrepancyCount}`);
    console.log(`Records where RPRO has more: ${stats.rproMoreCount}`);
    console.log(`Records where Veeqo has more: ${stats.veeqoMoreCount}`);
    console.log(`Records with matching quantities: ${stats.matchingCount}`);

    return stats;
  } catch (error) {
    fs.appendFileSync(logFile, `Error getting statistics: ${error.message}\n`);
    console.error(`Error getting statistics: ${error.message}`);
  }
}

// Main function
async function main() {
  try {
    // Execute the SQL to refresh the reconciliation data
    const sqlExecuted = await executeSql(sqlContent);

    if (!sqlExecuted) {
      fs.appendFileSync(logFile, `Failed to refresh reconciliation data\n`);
      console.error('Failed to refresh reconciliation data');
      return;
    }

    // Get reconciliation statistics
    await getReconcileStats();

    fs.appendFileSync(logFile, `Reconciliation data refresh completed\n`);
    console.log('Reconciliation data refresh completed');
  } catch (error) {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`Unhandled error: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the main function
console.log('Starting reconciliation data refresh...');
main()
  .then(() => {
    fs.appendFileSync(logFile, `Process completed at ${new Date().toISOString()}\n`);
    console.log('Process completed.');
  })
  .catch(error => {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    console.error(`Unhandled error: ${error.message}`);
  });
