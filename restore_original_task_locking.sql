-- Restore the original lock_pending_tasks function (no future OSL task selection)
-- This reverts back to the simple working state

CREATE OR REPLACE FUNCTION lock_pending_tasks(
    worker_id TEXT,
    max_tasks INTEGER,
    task_time TIMESTAMP WITH TIME ZONE
)
RET<PERSON>NS SETOF t_task_queue AS $$
DECLARE
    lock_timeout INTERVAL := INTERVAL '5 minutes';
BEGIN
    RETURN QUERY
    WITH locked_tasks AS (
        UPDATE t_task_queue
        SET
            status = 'processing',
            locked_at = task_time,
            locked_by = worker_id
        WHERE id IN (
            SELECT id
            FROM t_task_queue
            WHERE
                status = 'pending'
                AND scheduled_at <= task_time
                AND (locked_at IS NULL OR locked_at < task_time - lock_timeout)
            ORDER BY scheduled_at ASC
            LIMIT max_tasks
            FOR UPDATE SKIP LOCKED
        )
        RETURNING *
    )
    SELECT * FROM locked_tasks;
END;
$$ LANGUAGE plpgsql;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Restored original simple lock_pending_tasks function.';
END $$;
