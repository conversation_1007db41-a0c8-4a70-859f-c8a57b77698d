# Supabase API Key Instructions

It looks like the Supabase API key in your `.env` file is not valid. Follow these steps to get the correct API key:

## Step 1: Log in to Supabase

1. Go to [https://app.supabase.com/](https://app.supabase.com/)
2. Log in with your credentials

## Step 2: Select Your Project

1. From the dashboard, select your project (the one with the URL `https://aepabhlwpjfjulrjeitn.supabase.co`)

## Step 3: Get the API Key

1. In the left sidebar, click on "Project Settings"
2. Click on "API" in the submenu
3. In the "Project API keys" section, you'll see two keys:
   - `anon` public key (for client-side code)
   - `service_role` secret key (for server-side code)
4. For our import script, we need the `service_role` key
5. Click the "Copy" button next to the `service_role` key

## Step 4: Update Your .env File

1. Open your `.env` file
2. Replace the current `SUPABASE_KEY` value with the new key you copied
3. Save the file

## Step 5: Create the Table in Supabase

Since we're having issues with the API, you can also create the table directly in the Supabase dashboard:

1. In the left sidebar of your Supabase project, click on "Table Editor"
2. Click "New Table"
3. Set the table name to `imported_table_rpro`
4. Add the following columns:

| Name | Type | Default Value | Primary Key | Is Nullable |
|------|------|--------------|------------|-------------|
| id | int8 | nextval('imported_table_rpro_id_seq'::regclass) | Yes | No |
| ivno | int4 | | No | Yes |
| ivalu | varchar | | No | Yes |
| ivvc | varchar | | No | Yes |
| ivdcs | varchar | | No | Yes |
| ivdesc2 | varchar | | No | Yes |
| ivdesc1 | varchar | | No | Yes |
| ivattr | varchar | | No | Yes |
| ivsize | varchar | | No | Yes |
| ivupc | varchar | | No | Yes |
| ivkityp | int4 | | No | Yes |
| ivdesc3 | varchar | | No | Yes |
| ivqohcm | numeric | | No | Yes |
| ivqsacm | numeric | | No | Yes |
| ivqsalaw | numeric | | No | Yes |
| ivqsatop | numeric | | No | Yes |
| ivdesc4 | varchar | | No | Yes |
| ivapd | numeric | | No | Yes |
| ivavgcd | numeric | | No | Yes |
| ivudnam | varchar | | No | Yes |
| ivaux3 | varchar | | No | Yes |
| ivaux4 | varchar | | No | Yes |
| ivqtylaw | numeric | | No | Yes |
| ivqtytop | numeric | | No | Yes |
| ivisid | varchar | | No | Yes |
| ivssid | varchar | | No | Yes |
| ivprcdzlis | numeric | | No | Yes |
| ivprcdz_dollar | numeric | | No | Yes |
| ivprcdzsal | numeric | | No | Yes |
| ivprcdzliv | numeric | | No | Yes |
| ivprcbtlis | numeric | | No | Yes |
| ivprcbt_dollar | numeric | | No | Yes |
| ivprcbtsal | numeric | | No | Yes |
| ivprcbtliv | numeric | | No | Yes |
| ivprcpplis | numeric | | No | Yes |
| ivprcpp_dollar | numeric | | No | Yes |
| ivprcppsal | numeric | | No | Yes |
| ivprcppliv | numeric | | No | Yes |
| ivprcmsrp | numeric | | No | Yes |
| ivprcmap | numeric | | No | Yes |
| ivprccase | numeric | | No | Yes |
| ivprcws_1 | numeric | | No | Yes |
| ivprcws_2 | numeric | | No | Yes |
| ivldr | date | | No | Yes |
| ivaux2 | varchar | | No | Yes |
| ivaux5 | varchar | | No | Yes |
| ivaux6 | varchar | | No | Yes |
| ivlstcd | numeric | | No | Yes |
| ivrpnlaw | numeric | | No | Yes |
| ivrpxlaw | numeric | | No | Yes |
| ivaux1 | varchar | | No | Yes |
| ivaux8 | varchar | | No | Yes |
| ivmsc4 | int4 | | No | Yes |
| ivdisdt | date | | No | Yes |
| ivmsc1 | int4 | | No | Yes |
| ivilmdt | date | | No | Yes |
| ivilmtm | varchar | | No | Yes |
| ivmsc3 | int4 | | No | Yes |
| ivuddt | date | | No | Yes |
| ivvnld | int4 | | No | Yes |
| ivvnlcd | int4 | | No | Yes |
| ivmsc2 | int4 | | No | Yes |
| imported_at | timestamptz | now() | No | No |
| import_batch_id | uuid | gen_random_uuid() | No | No |

5. Click "Save" to create the table

## Step 6: Try the Import Again

After updating your API key and creating the table, try running the import script again:

```
node importDbfToSupabase.js R:\Rpro\BRIDGE\invdb.dbf imported_table_rpro true
```

## Alternative: Use the SQL Editor

You can also create the table using the SQL Editor in Supabase:

1. In the left sidebar, click on "SQL Editor"
2. Create a new query
3. Paste the following SQL:

```sql
CREATE TABLE IF NOT EXISTS public.imported_table_rpro (
  id SERIAL PRIMARY KEY,
  ivno INTEGER,
  ivalu VARCHAR(255),
  ivvc VARCHAR(50),
  ivdcs VARCHAR(50),
  ivdesc2 VARCHAR(255),
  ivdesc1 VARCHAR(255),
  ivattr VARCHAR(255),
  ivsize VARCHAR(255),
  ivupc VARCHAR(50),
  ivkityp INTEGER,
  ivdesc3 VARCHAR(255),
  ivqohcm NUMERIC(10,2),
  ivqsacm NUMERIC(10,2),
  ivqsalaw NUMERIC(10,2),
  ivqsatop NUMERIC(10,2),
  ivdesc4 VARCHAR(255),
  ivapd NUMERIC(10,2),
  ivavgcd NUMERIC(10,3),
  ivudnam VARCHAR(255),
  ivaux3 VARCHAR(255),
  ivaux4 VARCHAR(255),
  ivqtylaw NUMERIC(10,2),
  ivqtytop NUMERIC(10,2),
  ivisid VARCHAR(50),
  ivssid VARCHAR(50),
  ivprcdzlis NUMERIC(10,2),
  ivprcdz_dollar NUMERIC(10,2),
  ivprcdzsal NUMERIC(10,2),
  ivprcdzliv NUMERIC(10,2),
  ivprcbtlis NUMERIC(10,2),
  ivprcbt_dollar NUMERIC(10,2),
  ivprcbtsal NUMERIC(10,2),
  ivprcbtliv NUMERIC(10,2),
  ivprcpplis NUMERIC(10,2),
  ivprcpp_dollar NUMERIC(10,2),
  ivprcppsal NUMERIC(10,2),
  ivprcppliv NUMERIC(10,2),
  ivprcmsrp NUMERIC(10,2),
  ivprcmap NUMERIC(10,2),
  ivprccase NUMERIC(10,2),
  ivprcws_1 NUMERIC(10,2),
  ivprcws_2 NUMERIC(10,2),
  ivldr DATE,
  ivaux2 VARCHAR(255),
  ivaux5 VARCHAR(50),
  ivaux6 VARCHAR(255),
  ivlstcd NUMERIC(10,2),
  ivrpnlaw NUMERIC(10,2),
  ivrpxlaw NUMERIC(10,2),
  ivaux1 VARCHAR(255),
  ivaux8 VARCHAR(255),
  ivmsc4 INTEGER,
  ivdisdt DATE,
  ivmsc1 INTEGER,
  ivilmdt DATE,
  ivilmtm VARCHAR(50),
  ivmsc3 INTEGER,
  ivuddt DATE,
  ivvnld INTEGER,
  ivvnlcd INTEGER,
  ivmsc2 INTEGER,
  imported_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  import_batch_id UUID NOT NULL DEFAULT gen_random_uuid()
);
```

4. Click "Run" to execute the SQL and create the table
