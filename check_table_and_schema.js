// check_table_and_schema.js - Check if t_images table exists and what schema we're in
import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in environment');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Checking if t_images table exists...');
    
    // Check if t_images table exists
    const tableCheckSql = `
      SELECT 
        schemaname,
        tablename,
        tableowner,
        hasindexes,
        hasrules,
        hastriggers
      FROM pg_tables 
      WHERE tablename = 't_images';
    `;
    
    const { data: tableData, error: tableError } = await supabase.rpc('exec_sql', { sql_query: tableCheckSql });
    
    if (tableError) {
      console.error('Error checking table:', tableError);
    } else {
      if (tableData && tableData.length > 0) {
        console.log('✅ t_images table exists:');
        tableData.forEach(table => {
          console.log(`   Schema: ${table.schemaname}`);
          console.log(`   Owner: ${table.tableowner}`);
          console.log(`   Has indexes: ${table.hasindexes}`);
          console.log(`   Has rules: ${table.hasrules}`);
          console.log(`   Has triggers: ${table.hastriggers}`);
        });
      } else {
        console.log('❌ t_images table not found');
      }
    }
    
    console.log('\nChecking current schema and search path...');
    
    const schemaCheckSql = `
      SELECT current_schema(), current_schemas(true) as search_path;
    `;
    
    const { data: schemaData, error: schemaError } = await supabase.rpc('exec_sql', { sql_query: schemaCheckSql });
    
    if (schemaError) {
      console.error('Error checking schema:', schemaError);
    } else {
      if (schemaData && schemaData.length > 0) {
        console.log('Current schema:', schemaData[0].current_schema);
        console.log('Search path:', schemaData[0].search_path);
      }
    }
    
    console.log('\nTesting basic table access...');
    
    // Test if we can access the table directly
    const { data: testData, error: testError } = await supabase
      .from('t_images')
      .select('id, table_name, record_id, image_verified')
      .limit(3);
    
    if (testError) {
      console.error('Error accessing t_images table:', testError);
    } else {
      console.log('✅ Can access t_images table directly');
      console.log(`Found ${testData.length} sample records`);
      if (testData.length > 0) {
        console.log('Sample data:', testData[0]);
      }
    }
    
    console.log('\nChecking for any functions with "variant" in the name...');
    
    const variantFunctionsCheckSql = `
      SELECT 
        proname as function_name,
        pg_get_function_result(oid) as return_type
      FROM pg_proc 
      WHERE proname ILIKE '%variant%'
      AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
      ORDER BY proname;
    `;
    
    const { data: variantFunctionsData, error: variantFunctionsError } = await supabase.rpc('exec_sql', { sql_query: variantFunctionsCheckSql });
    
    if (variantFunctionsError) {
      console.error('Error checking variant functions:', variantFunctionsError);
    } else {
      if (variantFunctionsData && variantFunctionsData.length > 0) {
        console.log(`Found ${variantFunctionsData.length} functions with "variant" in name:`);
        variantFunctionsData.forEach((func, index) => {
          console.log(`${index + 1}. ${func.function_name} -> ${func.return_type}`);
        });
      } else {
        console.log('No functions with "variant" in name found');
      }
    }

  } catch (e) {
    console.error('Fatal error:', e?.message || e);
    process.exit(1);
  }
}

main();
