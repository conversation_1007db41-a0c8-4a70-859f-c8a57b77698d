import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testEnhancedMpsExport() {
    try {
        console.log('🧪 Testing Enhanced MPS Export...\n');
        
        // 1. Get sample data including fundraiser and new release sections
        console.log('1. Getting sample data for enhanced MPS export...');
        
        const { data: sampleData, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('id, excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id, mold_name, plastic_name')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .in('excel_row_hint', [25, 28, 132, 135, 340, 341, 342]) // Include fundraiser, new release, and regular items
            .order('excel_row_hint');

        if (error) {
            console.error('❌ Error getting sample data:', error);
            return;
        }

        console.log(`✅ Found ${sampleData.length} sample records:`);
        sampleData.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.plastic_name} ${record.mold_name}`);
            console.log(`      ID: ${record.id}, MPS: ${record.calculated_mps_id || 'NO_MPS'}`);
        });

        // 2. Test regular export (current behavior)
        console.log('\n2. Testing regular export...');
        
        const regularData = sampleData.map(item => ({
            ...item,
            order: 5  // Test order quantity
        }));

        const timestamp = new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0];
        
        const regularResponse = await fetch('http://localhost:3001/api/discraft/export', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                includeHeader: false,
                filename: `test_regular_export_${timestamp}.xlsx`,
                orderData: regularData,
                enhancedMpsExport: false  // Regular export
            })
        });

        if (!regularResponse.ok) {
            throw new Error(`Regular export API returned ${regularResponse.status}: ${regularResponse.statusText}`);
        }

        const regularResult = await regularResponse.json();
        console.log('✅ Regular export completed!');
        console.log(`   📄 Filename: ${regularResult.filename}`);
        console.log(`   📊 Records: ${regularResult.totalRecords}`);

        // 3. Test enhanced MPS export (new behavior)
        console.log('\n3. Testing enhanced MPS export...');
        
        const enhancedResponse = await fetch('http://localhost:3001/api/discraft/export', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                includeHeader: false,
                filename: `test_enhanced_mps_export_${timestamp}.xlsx`,
                orderData: sampleData,  // Use original data with all fields
                enhancedMpsExport: true  // Enhanced MPS export
            })
        });

        if (!enhancedResponse.ok) {
            throw new Error(`Enhanced MPS export API returned ${enhancedResponse.status}: ${enhancedResponse.statusText}`);
        }

        const enhancedResult = await enhancedResponse.json();
        console.log('✅ Enhanced MPS export completed!');
        console.log(`   📄 Filename: ${enhancedResult.filename}`);
        console.log(`   📊 Records: ${enhancedResult.totalRecords}`);

        // 4. Summary
        console.log('\n🎯 Test Results Summary:');
        console.log('\n📄 Regular Export:');
        console.log(`   • File: ${regularResult.filePath}`);
        console.log(`   • Shows: Order quantities (5) in order cells`);
        console.log(`   • Column AC: Empty`);
        
        console.log('\n📄 Enhanced MPS Export:');
        console.log(`   • File: ${enhancedResult.filePath}`);
        console.log(`   • Shows: Order sheet line IDs in order cells`);
        console.log(`   • Column AC: MPS IDs or NO_MPS`);
        
        console.log('\n🔍 Check these specific cells in the enhanced export:');
        sampleData.forEach(record => {
            console.log(`   • Row ${record.excel_row_hint}, Col ${record.excel_column}: Should show ID ${record.id}`);
            console.log(`   • Row ${record.excel_row_hint}, Col AC: Should show MPS ${record.calculated_mps_id || 'NO_MPS'}`);
        });
        
        console.log('\n✅ Enhanced MPS export test completed successfully!');
        console.log('\n📋 Expected behavior:');
        console.log('   • Order cells contain order sheet line IDs (for traceability)');
        console.log('   • Column AC contains MPS IDs (for verification)');
        console.log('   • Fundraiser section: Row 25B and 28B have IDs, AC25 and AC28 have MPS');
        console.log('   • New Release section: Row 132B and 135B have IDs, AC132 and AC135 have MPS');
        
    } catch (error) {
        console.error('❌ Enhanced MPS export test failed:', error.message);
    }
}

testEnhancedMpsExport().catch(console.error);
