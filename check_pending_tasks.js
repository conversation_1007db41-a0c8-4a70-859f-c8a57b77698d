// check_pending_tasks.js
// Check if there are any pending tasks

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkPendingTasks() {
  try {
    console.log('Checking pending tasks...');
    
    const { data: tasks, error } = await supabase
      .from('t_task_queue')
      .select('id, task_type, status, scheduled_at, created_at, payload')
      .eq('status', 'pending')
      .order('scheduled_at')
      .limit(10);
    
    if (error) {
      console.error('Error fetching tasks:', error);
      return;
    }
    
    console.log(`Found ${tasks.length} pending tasks:`);
    
    tasks.forEach(task => {
      const payload = typeof task.payload === 'string' ? JSON.parse(task.payload) : task.payload;
      console.log(`Task ${task.id}: ${task.task_type} (OSL ${payload.id || 'N/A'}) - scheduled: ${task.scheduled_at}`);
    });
    
    // Check specifically for our test tasks
    const { data: testTasks } = await supabase
      .from('t_task_queue')
      .select('*')
      .in('id', [177954, 177955, 177956])
      .order('id');
    
    console.log('\nOur test tasks:');
    testTasks.forEach(task => {
      console.log(`Task ${task.id}: ${task.status} - scheduled: ${task.scheduled_at}`);
    });
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

checkPendingTasks();
