// enqueueMvpOslMapImportTask.js - helper to enqueue import_mvp_osl_map_and_status
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function enqueueMvpOslMapImportTask() {
  console.log('📋 Enqueueing MVP OSL Map/Status Task...');
  const taskData = {
    task_type: 'import_mvp_osl_map_and_status',
    payload: {},
    status: 'pending',
    scheduled_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    enqueued_by: 'manual_enqueue_script_mvp'
  };

  const { data, error } = await supabase
    .from('t_task_queue')
    .insert([taskData])
    .select()
    .single();

  if (error) throw error;
  console.log('✅ Enqueued:', data);
  return data;
}

enqueueMvpOslMapImportTask()
  .then(() => process.exit(0))
  .catch(err => { console.error(err); process.exit(1); });

