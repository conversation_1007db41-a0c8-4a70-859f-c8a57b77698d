// directImport.js - Direct script to import DBF to Supabase

import { createClient } from '@supabase/supabase-js';
import { DBFFile } from 'dbffile';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Create a log file
const logFile = 'direct_import.log';
fs.writeFileSync(logFile, `Starting direct import at ${new Date().toISOString()}\n`);

// Load environment variables
dotenv.config();
fs.appendFileSync(logFile, `Environment variables loaded\n`);

// Get parameters
const dbfFilePath = process.argv[2] || process.env.DBF_FILE_PATH || 'R:\\Rpro\\BRIDGE\\invdb.dbf';
const targetTable = process.argv[3] || process.env.TARGET_TABLE || 'imported_table_rpro';
const truncateBeforeImport = (process.argv[4] === 'true') || (process.env.TRUNCATE_BEFORE_IMPORT === 'true') || false;

fs.appendFileSync(logFile, `Parameters:\n`);
fs.appendFileSync(logFile, `- DBF file path: ${dbfFilePath}\n`);
fs.appendFileSync(logFile, `- Target table: ${targetTable}\n`);
fs.appendFileSync(logFile, `- Truncate before import: ${truncateBeforeImport}\n`);

// Check if file exists
if (!fs.existsSync(dbfFilePath)) {
  const errorMsg = `DBF file not found at path: ${dbfFilePath}`;
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

fs.appendFileSync(logFile, `DBF file exists\n`);

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  const errorMsg = 'Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file';
  fs.appendFileSync(logFile, `ERROR: ${errorMsg}\n`);
  console.error(errorMsg);
  process.exit(1);
}

fs.appendFileSync(logFile, `Supabase credentials found\n`);
fs.appendFileSync(logFile, `- URL: ${supabaseUrl}\n`);
fs.appendFileSync(logFile, `- Key: ${supabaseKey.substring(0, 10)}...\n`);

const supabase = createClient(supabaseUrl, supabaseKey);
fs.appendFileSync(logFile, `Supabase client created\n`);

// Main function
async function importDbf() {
  try {
    fs.appendFileSync(logFile, `Opening DBF file...\n`);
    const dbf = await DBFFile.open(dbfFilePath);
    fs.appendFileSync(logFile, `DBF file opened. Found ${dbf.recordCount} records.\n`);
    
    // Read a sample of records
    const sampleSize = Math.min(5, dbf.recordCount);
    const sampleRecords = await dbf.readRecords(sampleSize);
    
    fs.appendFileSync(logFile, `Read ${sampleRecords.length} sample records.\n`);
    fs.appendFileSync(logFile, `Sample record: ${JSON.stringify(sampleRecords[0])}\n`);
    
    // Check if table exists
    fs.appendFileSync(logFile, `Checking if table ${targetTable} exists...\n`);
    
    try {
      const { data, error } = await supabase
        .from(targetTable)
        .select('count(*)', { count: 'exact', head: true });
      
      if (error) {
        fs.appendFileSync(logFile, `Error checking table: ${error.message}\n`);
        throw new Error(`Error checking table: ${error.message}`);
      }
      
      fs.appendFileSync(logFile, `Table ${targetTable} exists.\n`);
      
      // Truncate table if requested
      if (truncateBeforeImport) {
        fs.appendFileSync(logFile, `Truncating table ${targetTable}...\n`);
        
        try {
          const { error: truncateError } = await supabase.rpc('truncate_table', { table_name: targetTable });
          
          if (truncateError) {
            fs.appendFileSync(logFile, `Error truncating table: ${truncateError.message}\n`);
            throw new Error(`Error truncating table: ${truncateError.message}`);
          }
          
          fs.appendFileSync(logFile, `Table ${targetTable} truncated successfully.\n`);
        } catch (truncateError) {
          fs.appendFileSync(logFile, `Error during truncate: ${truncateError.message}\n`);
          throw truncateError;
        }
      }
      
      // Read all records
      fs.appendFileSync(logFile, `Reading all records from DBF file...\n`);
      const records = await dbf.readRecords();
      fs.appendFileSync(logFile, `Read ${records.length} records from DBF file.\n`);
      
      // Process records
      fs.appendFileSync(logFile, `Processing records...\n`);
      const processedRecords = records.map(record => {
        const processedRecord = {};
        
        // Convert all field names to lowercase for Supabase compatibility
        for (const key in record) {
          const lowerKey = key.toLowerCase();
          
          // Convert Buffer objects to strings
          if (Buffer.isBuffer(record[key])) {
            processedRecord[lowerKey] = record[key].toString().trim();
          }
          // Convert Date objects to ISO strings
          else if (record[key] instanceof Date) {
            processedRecord[lowerKey] = record[key].toISOString();
          }
          // Handle null values
          else if (record[key] === null || record[key] === undefined) {
            processedRecord[lowerKey] = null;
          }
          // Handle numeric fields
          else if (typeof record[key] === 'string' && !isNaN(record[key]) && dbf.fields.find(f => f.name.toLowerCase() === lowerKey)?.type === 'N') {
            processedRecord[lowerKey] = Number(record[key]);
          }
          // Keep other values as is
          else {
            processedRecord[lowerKey] = record[key];
          }
        }
        
        // Add metadata fields
        processedRecord.imported_at = new Date().toISOString();
        
        return processedRecord;
      });
      
      fs.appendFileSync(logFile, `Processed ${processedRecords.length} records.\n`);
      
      // Insert records in batches
      const batchSize = 100; // Smaller batch size for testing
      let successCount = 0;
      
      fs.appendFileSync(logFile, `Inserting records in batches of ${batchSize}...\n`);
      
      for (let i = 0; i < processedRecords.length; i += batchSize) {
        const batch = processedRecords.slice(i, i + batchSize);
        fs.appendFileSync(logFile, `Inserting batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(processedRecords.length / batchSize)}...\n`);
        
        try {
          const { data: insertData, error: insertError } = await supabase
            .from(targetTable)
            .insert(batch);
          
          if (insertError) {
            fs.appendFileSync(logFile, `Error inserting batch: ${insertError.message}\n`);
            throw new Error(`Error inserting batch: ${insertError.message}`);
          }
          
          successCount += batch.length;
          fs.appendFileSync(logFile, `Batch inserted successfully. Total records inserted: ${successCount}\n`);
        } catch (insertError) {
          fs.appendFileSync(logFile, `Error during insert: ${insertError.message}\n`);
          throw insertError;
        }
      }
      
      fs.appendFileSync(logFile, `Import completed successfully. Imported ${successCount} records to ${targetTable}.\n`);
      console.log(`Import completed successfully. Imported ${successCount} records to ${targetTable}.`);
    } catch (tableError) {
      fs.appendFileSync(logFile, `Error with table operations: ${tableError.message}\n`);
      throw tableError;
    }
  } catch (error) {
    fs.appendFileSync(logFile, `Error importing DBF file: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`Error importing DBF file: ${error.message}`);
    console.error(error.stack);
  }
}

// Run the import
console.log(`Starting import of ${dbfFilePath} to ${targetTable}...`);
importDbf()
  .then(() => {
    fs.appendFileSync(logFile, `Import process completed at ${new Date().toISOString()}\n`);
    console.log('Import process completed.');
  })
  .catch(error => {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    console.error(`Unhandled error: ${error.message}`);
  });
