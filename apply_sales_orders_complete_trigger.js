// apply_sales_orders_complete_trigger.js - Apply sales orders Complete triggers
import 'dotenv/config';
import fs from 'fs';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in env');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

async function execSql(sql) {
  // Common RPC wrapper variants used in this repo
  let { error } = await supabase.rpc('exec_sql', { sql_query: sql });
  if (error) {
    ({ error } = await supabase.rpc('exec_sql', { sql_statement: sql }));
  }
  return { error };
}

async function main() {
  try {
    const sql = fs.readFileSync('create_sales_orders_complete_trigger.sql', 'utf8');
    console.log('Applying sales orders Complete triggers...');
    const { error } = await execSql(sql);
    if (error) {
      console.error('Failed to apply triggers:', error.message);
      process.exit(1);
    }
    console.log('Triggers applied successfully.');
  } catch (e) {
    console.error('Fatal:', e?.message || e);
    process.exit(1);
  }
}

main();

