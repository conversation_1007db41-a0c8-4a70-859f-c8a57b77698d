-- Create a stored procedure to truncate the imported_table_veeqo_sellables_export table
CREATE OR REPLACE FUNCTION truncate_imported_table_veeqo_sellables_export()
RETURNS void AS $$
BEGIN
  TRUNCATE TABLE imported_table_veeqo_sellables_export;
  
  -- Log the truncation
  INSERT INTO t_error_logs(error_message, created_at, context, created_by)
  VALUES (
    'Truncated imported_table_veeqo_sellables_export table via admin interface',
    NOW(),
    'truncate_imported_table_veeqo_sellables_export',
    'admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
