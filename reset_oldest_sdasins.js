import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Helper function to introduce delay
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

async function resetOldestSdasins() {
  try {
    // Get the 1200 oldest records by looked_for_matching_discs_at
    const { data: oldestRecords, error: selectError } = await supabase
      .from('t_sdasins')
      .select('id, looked_for_matching_discs_at')
      .not('looked_for_matching_discs_at', 'is', null)
      .order('looked_for_matching_discs_at', { ascending: true })
      .limit(1200);

    if (selectError) {
      throw selectError;
    }

    if (!oldestRecords || oldestRecords.length === 0) {
      console.log('No records found with non-null looked_for_matching_discs_at');
      return;
    }

    // Process records in smaller batches (3 at a time)
    const batchSize = 3;
    const batches = [];
    
    for (let i = 0; i < oldestRecords.length; i += batchSize) {
      batches.push(oldestRecords.slice(i, i + batchSize));
    }
    
    console.log(`Processing ${oldestRecords.length} records in ${batches.length} batches of ${batchSize}`);
    
    // Process each batch with a delay between batches
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const idsToReset = batch.map(record => record.id);
      
      console.log(`Batch ${i+1}/${batches.length}: Resetting looked_for_matching_discs_at for IDs: ${idsToReset.join(', ')}`);
      console.log(`Previous timestamps: ${batch.map(r => r.looked_for_matching_discs_at).join(', ')}`);
      
      // Update the records to set looked_for_matching_discs_at to null
      const { data: updateResult, error: updateError } = await supabase
        .from('t_sdasins')
        .update({ looked_for_matching_discs_at: null })
        .in('id', idsToReset);
      
      if (updateError) {
        console.error(`Error in batch ${i+1}:`, updateError.message);
        continue; // Continue with next batch even if this one fails
      }
      
      console.log(`Successfully reset ${idsToReset.length} records in batch ${i+1}`);
      
      // Add delay between batches to avoid overloading the database
      if (i < batches.length - 1) {
        console.log(`Waiting 2 seconds before next batch...`);
        await delay(2000); // 2 second delay
      }
    }
    
    console.log('All batches processed');
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Run the function
resetOldestSdasins();

