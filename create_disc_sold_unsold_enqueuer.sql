-- Function to enqueue a task for handling disc sold/unsold events
CREATE OR REPLACE FUNCTION fn_enqueue_disc_sold_or_unsold()
RETURNS TRIGGER AS $$
BEGIN
    -- Only enqueue a task if the sold_date has changed from null to not null or vice versa
    IF (OLD.sold_date IS NULL AND NEW.sold_date IS NOT NULL) OR 
       (OLD.sold_date IS NOT NULL AND NEW.sold_date IS NULL) THEN
        
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'disc_sold_or_unsold',
            jsonb_build_object(
                'id', NEW.id,
                'old_sold_date', OLD.sold_date,
                'new_sold_date', NEW.sold_date,
                'shopify_uploaded_at', NEW.shopify_uploaded_at
            ),
            'pending',
            NOW(),
            NOW()
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the new trigger
DROP TRIGGER IF EXISTS trg_enqueue_disc_sold_or_unsold ON t_discs;

CREATE TRIGGER trg_enqueue_disc_sold_or_unsold
AFTER UPDATE OF sold_date ON t_discs
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_disc_sold_or_unsold();

-- Drop the old triggers
DROP TRIGGER IF EXISTS trg_enqueue_d_sold_or_unsold_update_sdasin_inv ON t_discs;
DROP TRIGGER IF EXISTS trg_queue_disc_sold_or_unsold_veeqo_qty ON t_discs;
DROP TRIGGER IF EXISTS trig_manage_inv_sdasin_on_disc_sold_unsold ON t_discs;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Disc sold/unsold enqueuer function and trigger created. Old triggers dropped.';
END $$;
