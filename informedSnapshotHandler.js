/**
 * Informed Snapshot Handler
 * 
 * This module provides functions to create and manage Informed Repricer data snapshots
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = process.env.SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzc4NjU0MSwiZXhwIjoyMDQ5MzYyNTQxfQ.FQyPTgdBP83VhuykdlZkagHADc5nBmx7-yd0JYt5aP8';

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Create a manual Informed snapshot
 * @param {string} notes - Optional notes for the snapshot
 * @returns {Promise<Object>} - Result of the snapshot creation
 */
export async function createInformedSnapshot(notes = null) {
    try {
        console.log('[informedSnapshotHandler] Creating Informed snapshot...');
        
        // Call the database function to create the snapshot
        const { data, error } = await supabase.rpc('fn_create_informed_snapshot', {
            p_notes: notes || 'Manual snapshot created via admin interface'
        });
        
        if (error) {
            console.error('[informedSnapshotHandler] Error creating snapshot:', error);
            return {
                success: false,
                error: error.message
            };
        }
        
        if (!data || data.length === 0) {
            return {
                success: false,
                error: 'No data returned from snapshot function'
            };
        }
        
        const snapshot = data[0];
        console.log(`[informedSnapshotHandler] Created snapshot ID: ${snapshot.snapshot_id}`);
        
        return {
            success: true,
            snapshot: {
                id: snapshot.snapshot_id,
                snapshot_date: snapshot.snapshot_date,
                active_fbm_listing_count: snapshot.active_fbm_listing_count,
                active_fba_listing_count: snapshot.active_fba_listing_count,
                active_fbm_buybox_count_yes: snapshot.active_fbm_buybox_count_yes,
                active_fbm_buybox_count_no: snapshot.active_fbm_buybox_count_no,
                active_fba_buybox_count_yes: snapshot.active_fba_buybox_count_yes,
                active_fba_buybox_count_no: snapshot.active_fba_buybox_count_no,
                total_active_listings: snapshot.total_active_listings,
                fbm_buybox_rate: snapshot.fbm_buybox_rate,
                fba_buybox_rate: snapshot.fba_buybox_rate,
                overall_buybox_rate: snapshot.overall_buybox_rate
            }
        };
        
    } catch (error) {
        console.error('[informedSnapshotHandler] Exception creating snapshot:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Get recent Informed snapshots
 * @param {number} limit - Number of snapshots to retrieve (default: 10)
 * @returns {Promise<Object>} - Result with snapshots data
 */
export async function getRecentSnapshots(limit = 10) {
    try {
        console.log(`[informedSnapshotHandler] Getting ${limit} recent snapshots...`);
        
        const { data, error } = await supabase
            .from('rpt_informed')
            .select('*')
            .order('snapshot_date', { ascending: false })
            .limit(limit);
        
        if (error) {
            console.error('[informedSnapshotHandler] Error getting snapshots:', error);
            return {
                success: false,
                error: error.message
            };
        }
        
        console.log(`[informedSnapshotHandler] Retrieved ${data.length} snapshots`);
        
        return {
            success: true,
            snapshots: data
        };
        
    } catch (error) {
        console.error('[informedSnapshotHandler] Exception getting snapshots:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Get snapshot statistics and trends
 * @returns {Promise<Object>} - Result with statistics
 */
export async function getSnapshotStatistics() {
    try {
        console.log('[informedSnapshotHandler] Getting snapshot statistics...');
        
        // Get latest snapshot
        const { data: latest, error: latestError } = await supabase
            .from('rpt_informed')
            .select('*')
            .order('snapshot_date', { ascending: false })
            .limit(1);
        
        if (latestError) {
            throw latestError;
        }
        
        // Get count of total snapshots
        const { count, error: countError } = await supabase
            .from('rpt_informed')
            .select('*', { count: 'exact', head: true });
        
        if (countError) {
            throw countError;
        }
        
        // Get snapshots from last 7 days for trend analysis
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        
        const { data: recent, error: recentError } = await supabase
            .from('rpt_informed')
            .select('*')
            .gte('snapshot_date', sevenDaysAgo.toISOString())
            .order('snapshot_date', { ascending: false });
        
        if (recentError) {
            throw recentError;
        }
        
        return {
            success: true,
            statistics: {
                total_snapshots: count,
                latest_snapshot: latest[0] || null,
                recent_snapshots_7_days: recent.length,
                recent_snapshots: recent
            }
        };
        
    } catch (error) {
        console.error('[informedSnapshotHandler] Exception getting statistics:', error);
        return {
            success: false,
            error: error.message
        };
    }
}
