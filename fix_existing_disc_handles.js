import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import yargs from 'yargs';
import { hideBin } from 'yargs/helpers';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('dry-run', {
    describe: 'Show what would be fixed without making changes',
    type: 'boolean',
    default: true
  })
  .option('limit', {
    describe: 'Maximum number of records to process (0 = all records)',
    type: 'number',
    default: 0
  })
  .option('batch-size', {
    describe: 'Number of records to process in each batch',
    type: 'number',
    default: 1000
  })
  .option('fix', {
    describe: 'Actually fix the handles (turns off dry-run)',
    type: 'boolean',
    default: false
  })
  .option('start-id', {
    describe: 'Start processing from this disc ID',
    type: 'number',
    default: 0
  })
  .help()
  .alias('help', 'h')
  .argv;

const DRY_RUN = !argv.fix;
const LIMIT = argv.limit;
const BATCH_SIZE = argv['batch-size'];
const START_ID = argv['start-id'];

async function findProblematicHandles() {
  try {
    console.log('🔍 Searching for discs with problematic handles...\n');
    console.log(`📊 Configuration:`);
    console.log(`  - Mode: ${DRY_RUN ? 'DRY RUN (no changes made)' : 'FIXING HANDLES'}`);
    console.log(`  - Batch size: ${BATCH_SIZE} records`);
    console.log(`  - Limit: ${LIMIT === 0 ? 'All records' : LIMIT + ' records'}`);
    console.log(`  - Start ID: ${START_ID}`);
    console.log('');

    let totalProcessed = 0;
    let totalProblematic = 0;
    let allTasksToEnqueue = [];
    let currentId = START_ID;
    let hasMoreRecords = true;

    while (hasMoreRecords && (LIMIT === 0 || totalProcessed < LIMIT)) {
      const batchLimit = LIMIT === 0 ? BATCH_SIZE : Math.min(BATCH_SIZE, LIMIT - totalProcessed);

      console.log(`🔄 Processing batch starting from ID ${currentId} (limit: ${batchLimit})...`);

      // Find discs with handles containing problematic characters
      const { data: discs, error } = await supabase
        .from('t_discs')
        .select('id, g_handle, g_title')
        .or(`g_handle.like.%|%,g_handle.like.%.%,g_handle.like.%'%,g_handle.like.%/%,g_handle.like.%&%,g_handle.like.%(%,g_handle.like.%)%,g_handle.like.%"%,g_handle.like.%#%,g_handle.like.%$%,g_handle.like.%+%,g_handle.like.%=%,g_handle.like.%?%,g_handle.like.%!%,g_handle.like.%*%,g_handle.like.%[%,g_handle.like.%]%,g_handle.like.%{%,g_handle.like.%}%,g_handle.like.%<%,g_handle.like.%>%,g_handle.like.%:%,g_handle.like.%;%,g_handle.like.%,%,g_handle.like.%^%,g_handle.like.%~%,g_handle.like.%\`%`)
        .not('g_handle', 'is', null)
        .gte('id', currentId)
        .limit(batchLimit)
        .order('id', { ascending: true });

      if (error) {
        console.error('❌ Error fetching discs:', error.message);
        return;
      }

      if (!discs || discs.length === 0) {
        console.log(`✅ No more problematic handles found in this batch.`);
        hasMoreRecords = false;
        break;
      }

      console.log(`📋 Found ${discs.length} discs with problematic handles in this batch`);

      const batchTasksToEnqueue = [];

      discs.forEach((disc, index) => {
        const originalHandle = disc.g_handle;
        const sanitizedHandle = sanitizeShopifyHandle(originalHandle);
        const hasProblems = originalHandle !== sanitizedHandle;

        if (hasProblems) {
          totalProblematic++;

          if (DRY_RUN && totalProblematic <= 10) {
            // Only show first 10 examples in dry run to avoid spam
            console.log(`${totalProblematic}. Disc ID: ${disc.id}`);
            console.log(`   Title: ${disc.g_title || 'N/A'}`);
            console.log(`   Current handle: "${originalHandle}"`);
            console.log(`   Fixed handle:   "${sanitizedHandle}"`);
            console.log('');
          }

          batchTasksToEnqueue.push({
            task_type: 'generate_disc_title_pull_and_handle',
            payload: { id: disc.id },
            status: 'pending',
            scheduled_at: new Date().toISOString(),
            created_at: new Date().toISOString()
          });
        }
      });

      allTasksToEnqueue.push(...batchTasksToEnqueue);
      totalProcessed += discs.length;

      // Update currentId for next batch
      if (discs.length > 0) {
        currentId = discs[discs.length - 1].id + 1;
      }

      console.log(`✅ Batch complete. Found ${batchTasksToEnqueue.length} problematic handles in this batch.`);
      console.log(`📊 Progress: ${totalProcessed} processed, ${totalProblematic} problematic total\n`);

      // If we got fewer records than requested, we've reached the end
      if (discs.length < batchLimit) {
        hasMoreRecords = false;
      }
    }

    if (allTasksToEnqueue.length === 0) {
      console.log('✅ All handles are already properly sanitized!');
      return;
    }

    console.log(`📊 Final Summary:`);
    console.log(`  - Total discs processed: ${totalProcessed}`);
    console.log(`  - Discs needing fixes: ${allTasksToEnqueue.length}`);
    console.log(`  - Mode: ${DRY_RUN ? 'DRY RUN (no changes made)' : 'FIXING HANDLES'}`);

    if (DRY_RUN) {
      if (totalProblematic > 10) {
        console.log(`\n📝 Note: Only showed first 10 examples. Total problematic: ${totalProblematic}`);
      }
      console.log('\n💡 To actually fix these handles, run:');
      console.log('   node fix_existing_disc_handles.js --fix');
      console.log('\n💡 To process all records:');
      console.log('   node fix_existing_disc_handles.js --fix --limit 0');
      console.log('\n💡 To process in smaller batches:');
      console.log('   node fix_existing_disc_handles.js --fix --batch-size 500');
      console.log('\n⚠️  This will enqueue generate_disc_title_pull_and_handle tasks for each problematic disc.');
      console.log('\n🔄 IMPORTANT: Make sure to restart the task queue worker first to pick up the new sanitization code!');
    } else {
      console.log('\n🔧 Enqueueing tasks to fix handles...');

      // Enqueue tasks in batches
      const enqueueBatchSize = 50;
      let enqueueCount = 0;

      for (let i = 0; i < allTasksToEnqueue.length; i += enqueueBatchSize) {
        const batch = allTasksToEnqueue.slice(i, i + enqueueBatchSize);

        const { error: insertError } = await supabase
          .from('t_task_queue')
          .insert(batch);

        if (insertError) {
          console.error(`❌ Error enqueueing batch ${Math.floor(i / enqueueBatchSize) + 1}:`, insertError.message);
        } else {
          enqueueCount += batch.length;
          console.log(`✅ Enqueued batch ${Math.floor(i / enqueueBatchSize) + 1} (${batch.length} tasks) - Total: ${enqueueCount}/${allTasksToEnqueue.length}`);
        }
      }

      console.log(`\n✅ Successfully enqueued ${enqueueCount} tasks to fix disc handles.`);
      console.log('   The task queue worker will process these and update the handles.');
      console.log('\n🔄 IMPORTANT: Make sure the task queue worker has been restarted to use the new sanitization code!');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

function sanitizeShopifyHandle(handle) {
  if (!handle) return handle;
  
  let sanitized = handle.toLowerCase();
  
  // Replace spaces with dashes
  sanitized = sanitized.replace(/ /g, '-');
  
  // Replace periods with dashes (important for weights like "169.21g")
  sanitized = sanitized.replace(/\./g, '-');
  
  // Remove other special characters that aren't allowed in Shopify handles
  sanitized = sanitized.replace(/'/g, '');
  sanitized = sanitized.replace(/\//g, '');
  sanitized = sanitized.replace(/\|/g, ''); // Remove pipes
  sanitized = sanitized.replace(/&/g, '-');
  sanitized = sanitized.replace(/\(/g, '');
  sanitized = sanitized.replace(/\)/g, '');
  sanitized = sanitized.replace(/"/g, '');
  sanitized = sanitized.replace(/%/g, '');
  sanitized = sanitized.replace(/#/g, '');
  sanitized = sanitized.replace(/\$/g, '');
  sanitized = sanitized.replace(/\+/g, '');
  sanitized = sanitized.replace(/=/g, '');
  sanitized = sanitized.replace(/\?/g, '');
  sanitized = sanitized.replace(/!/g, '');
  sanitized = sanitized.replace(/\*/g, '');
  sanitized = sanitized.replace(/\[/g, '');
  sanitized = sanitized.replace(/\]/g, '');
  sanitized = sanitized.replace(/\{/g, '');
  sanitized = sanitized.replace(/\}/g, '');
  sanitized = sanitized.replace(/</g, '');
  sanitized = sanitized.replace(/>/g, '');
  sanitized = sanitized.replace(/:/g, '');
  sanitized = sanitized.replace(/;/g, '');
  sanitized = sanitized.replace(/,/g, '');
  sanitized = sanitized.replace(/\^/g, '');
  sanitized = sanitized.replace(/~/g, '');
  sanitized = sanitized.replace(/`/g, '');
  
  // Replace multiple dashes with a single dash
  sanitized = sanitized.replace(/-+/g, '-');
  
  // Remove leading/trailing dashes
  sanitized = sanitized.replace(/^-+|-+$/g, '');
  
  return sanitized;
}

findProblematicHandles();
