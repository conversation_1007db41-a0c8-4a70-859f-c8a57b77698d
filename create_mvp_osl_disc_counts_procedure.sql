-- Create stored procedure to calculate disc counts for MVP OSL map records
-- Counts discs for each OSL ID and updates the it_mvp_osl_map table

CREATE OR REPLACE FUNCTION calculate_mvp_osl_disc_counts(osl_ids INTEGER[])
RETURNS TABLE(
    osl_id INTEGER,
    in_stock SMALLINT,
    sold_last_90 SMALLINT,
    updated BOOLEAN
) AS $$
DECLARE
    current_osl_id INTEGER;
    in_stock_count SMALLINT;
    sold_last_90_count SMALLINT;
    lookback_days INTEGER;
BEGIN
    -- Read lookback days from config (default 60)
    SELECT COALESCE(NULLIF(value, '')::INTEGER, 60) INTO lookback_days
    FROM t_config WHERE key = 'mvp_disc_order_look_back_days';

    IF lookback_days IS NULL THEN
        lookback_days := 60;
    END IF;

    -- Iterate OSL IDs
    FOREACH current_osl_id IN ARRAY osl_ids LOOP
        -- In-stock count (unsold)
        SELECT COUNT(*)::SMALLINT INTO in_stock_count
        FROM t_discs
        WHERE vendor_osl_id = current_osl_id
          AND sold_date IS NULL;

        -- Sold during lookback (calendar-day based), excluding our 'Fixed' workflow channel
        SELECT COUNT(*)::SMALLINT INTO sold_last_90_count
        FROM t_discs
        WHERE vendor_osl_id = current_osl_id
          AND sold_date::date >= (CURRENT_DATE - lookback_days)
          AND sold_channel IS DISTINCT FROM 'Fixed';

        -- Update it_mvp_osl_map
        UPDATE it_mvp_osl_map
        SET in_stock = in_stock_count,
            sold_last_90 = sold_last_90_count,
            updated_at = NOW()
        WHERE id = current_osl_id;

        -- Return row
        osl_id := current_osl_id;
        in_stock := in_stock_count;
        sold_last_90 := sold_last_90_count;
        updated := TRUE;
        RETURN NEXT;
    END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;
