import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function debugMpsIds() {
    try {
        console.log('🔍 Debugging MPS IDs...\n');
        
        // Check what's in the calculated_mps_id field for records after line 332
        console.log('1. Checking calculated_mps_id field in it_discraft_order_sheet_lines...');
        const { data: discraftData, error: discraftError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id, mold_name, plastic_name')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .gt('excel_row_hint', 332)
            .limit(10);

        if (discraftError) {
            console.error('❌ Error querying Discraft data:', discraftError);
            return;
        }

        console.log(`✅ Found ${discraftData.length} records after line 332`);
        console.log('Sample records:');
        discraftData.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: calculated_mps_id=${record.calculated_mps_id}, mold=${record.mold_name}, plastic=${record.plastic_name}`);
        });

        // Check if there are any records WITH calculated_mps_id
        console.log('\n2. Checking how many records have calculated_mps_id...');
        const { data: withMpsData, error: withMpsError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('calculated_mps_id')
            .eq('is_orderable', true)
            .not('calculated_mps_id', 'is', null)
            .limit(5);

        if (withMpsError) {
            console.error('❌ Error querying records with MPS:', withMpsError);
            return;
        }

        console.log(`✅ Found ${withMpsData.length} records with calculated_mps_id`);
        if (withMpsData.length > 0) {
            console.log('Sample MPS IDs:');
            withMpsData.forEach((record, index) => {
                console.log(`   ${index + 1}. calculated_mps_id=${record.calculated_mps_id}`);
            });
        }

        // Check the view to see if it has MPS data
        console.log('\n3. Checking v_stats_by_osl_discraft view for MPS data...');
        const { data: viewData, error: viewError } = await supabase
            .from('v_stats_by_osl_discraft')
            .select('excel_mapping_key, excel_row_hint, calculated_mps_id, mold_name')
            .gt('excel_row_hint', 332)
            .limit(5);

        if (viewError) {
            console.error('❌ Error querying view data:', viewError);
            return;
        }

        console.log(`✅ Found ${viewData.length} view records after line 332`);
        if (viewData.length > 0) {
            console.log('Sample view records:');
            viewData.forEach((record, index) => {
                console.log(`   ${index + 1}. Row ${record.excel_row_hint}: calculated_mps_id=${record.calculated_mps_id}, mold=${record.mold_name}`);
            });
        }

        // Check if the issue is in our data mapping
        console.log('\n4. Checking our data mapping logic...');
        const testRecord = discraftData[0];
        if (testRecord) {
            console.log(`Test record: ${JSON.stringify(testRecord, null, 2)}`);
            console.log(`calculated_mps_id value: "${testRecord.calculated_mps_id}"`);
            console.log(`calculated_mps_id type: ${typeof testRecord.calculated_mps_id}`);
            console.log(`Is null? ${testRecord.calculated_mps_id === null}`);
            console.log(`Is undefined? ${testRecord.calculated_mps_id === undefined}`);
            
            // Test our mapping logic
            const mappedValue = testRecord.calculated_mps_id || 'NO_MPS';
            console.log(`Mapped value would be: "${mappedValue}"`);
        }

        console.log('\n🎉 Debug completed!');
        
    } catch (error) {
        console.error('❌ Debug failed:', error.message);
    }
}

debugMpsIds().catch(console.error);
