// rebuild_matches_sdasin_batches.js
// Rebuilds tjoin_discs_sdasins by iterating SDASINs in batches
// For each SDASIN, calls match_sdasin_to_all_discs(id), which deletes its joins and reinserts fresh matches

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';
import fs from 'fs/promises';
import pLimit from 'p-limit';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Tunables
const BATCH_SIZE = parseInt(process.env.SDASIN_REBUILD_BATCH_SIZE || '1000', 10);
const CONCURRENCY = parseInt(process.env.SDASIN_REBUILD_CONCURRENCY || '6', 10);
const BATCH_SLEEP_MS = parseInt(process.env.SDASIN_REBUILD_SLEEP_MS || '2000', 10);
const CHECKPOINT_FILE = 'rebuild_sdasin_checkpoint.json';

function sleep(ms) { return new Promise(r => setTimeout(r, ms)); }

async function readCheckpoint() {
  try {
    const txt = await fs.readFile(CHECKPOINT_FILE, 'utf8');
    return JSON.parse(txt);
  } catch {
    return { lastId: 0, processed: 0, startedAt: new Date().toISOString() };
  }
}

async function writeCheckpoint(cp) {
  await fs.writeFile(CHECKPOINT_FILE, JSON.stringify(cp, null, 2));
}

async function* sdasinIdIterator(startAfterId = 0) {
  let lastId = startAfterId;
  while (true) {
    // Pull next page of IDs ordered by id asc
    const { data, error } = await supabase
      .from('t_sdasins')
      .select('id')
      .gt('id', lastId)
      .order('id', { ascending: true })
      .limit(BATCH_SIZE);
    if (error) throw error;
    if (!data || data.length === 0) break;
    for (const row of data) {
      yield row.id;
      lastId = row.id;
    }
  }
}

async function processBatch(ids) {
  const limit = pLimit(CONCURRENCY);
  let ok = 0, fail = 0;
  await Promise.all(ids.map(id => limit(async () => {
    const { data, error } = await supabase.rpc('match_sdasin_to_all_discs', { sdasin_id_param: id });
    if (error) {
      fail++;
      console.error(`[SDASIN ${id}] ERROR: ${error.message}`);
    } else {
      ok++;
    }
  })));
  return { ok, fail };
}

async function main() {
  console.log('Starting SDASIN→discs rebuild...');
  console.log(`Batch size=${BATCH_SIZE}, concurrency=${CONCURRENCY}, sleep=${BATCH_SLEEP_MS}ms`);

  const cp = await readCheckpoint();
  let processed = cp.processed || 0;
  let lastId = cp.lastId || 0;
  const startedAt = cp.startedAt || new Date().toISOString();

  const startTime = Date.now();

  // Iterate in batches by pulling a page of IDs, then chunking within same page
  while (true) {
    const { data: page, error } = await supabase
      .from('t_sdasins')
      .select('id')
      .gt('id', lastId)
      .order('id', { ascending: true })
      .limit(BATCH_SIZE);
    if (error) {
      console.error('Page fetch error:', error.message);
      break;
    }
    if (!page || page.length === 0) {
      console.log('No more SDASINs. Rebuild complete.');
      break;
    }

    const ids = page.map(r => r.id);
    const batchStartId = ids[0];
    const batchEndId = ids[ids.length - 1];

    console.log(`Processing SDASIN ids [${batchStartId}..${batchEndId}] (${ids.length} records)...`);
    const { ok, fail } = await processBatch(ids);
    processed += ids.length;
    lastId = batchEndId;

    const elapsed = ((Date.now() - startTime) / 1000 / 60).toFixed(1);
    console.log(`Batch done: ok=${ok}, fail=${fail}. Total processed=${processed}. Elapsed ~${elapsed} min.`);

    await writeCheckpoint({ lastId, processed, startedAt, updatedAt: new Date().toISOString() });

    // Small pause between batches to avoid hammering the DB
    await sleep(BATCH_SLEEP_MS);
  }

  const totalElapsed = ((Date.now() - startTime) / 1000 / 60).toFixed(1);
  console.log(`All done. Total processed=${processed}. Total elapsed ~${totalElapsed} min.`);
}

main().catch(err => {
  console.error('Fatal error:', err);
  process.exit(1);
});

