-- Function to enqueue a task when t_plastics.ready changes from false to true
CREATE OR REPLACE FUNCTION fn_enqueue_publish_plastic_collection()
RETURNS TRIGGER AS $$
BEGIN
    -- Only enqueue a task if ready has changed from FALSE to TRUE
    IF (OLD.ready IS FALSE OR OLD.ready IS NULL) AND NEW.ready IS TRUE THEN
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'publish_plastic_collection',
            jsonb_build_object('id', NEW.id),
            'pending',
            NOW(),
            NOW()
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the old trigger if it exists
DROP TRIGGER IF EXISTS tr_enqueue_publish_plastic_collection ON t_plastics;

-- Create the new trigger
CREATE TRIGGER tr_enqueue_publish_plastic_collection
AFTER UPDATE OF ready
ON t_plastics
FOR EACH ROW
WHEN (
    (OLD.ready IS FALSE OR OLD.ready IS NULL) AND 
    NEW.ready IS TRUE
)
EXECUTE FUNCTION fn_enqueue_publish_plastic_collection();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger tr_enqueue_publish_plastic_collection has been created to enqueue a task.';
END $$;
