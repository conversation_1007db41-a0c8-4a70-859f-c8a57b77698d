-- Function to enqueue a task for calculating disc carrying cost
CREATE OR REPLACE FUNCTION fn_enqueue_set_disc_carry_cost()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert a task into the task queue
    INSERT INTO t_task_queue (
        task_type,
        payload,
        status,
        scheduled_at,
        created_at
    ) VALUES (
        'set_disc_carry_cost',
        jsonb_build_object('id', NEW.id),
        'pending',
        NOW(),
        NOW()
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the new INSERT trigger
DROP TRIGGER IF EXISTS tr_enqueue_set_carry_cost_insert ON t_discs;

CREATE TRIGGER tr_enqueue_set_carry_cost_insert
BEFORE INSERT ON t_discs
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_set_disc_carry_cost();

-- Create the new UPDATE trigger
DROP TRIGGER IF EXISTS tr_enqueue_set_carry_cost_update ON t_discs;

CREATE TRIGGER tr_enqueue_set_carry_cost_update
AFTER UPDATE OF mps_id, shipment_id ON t_discs
FOR EACH ROW
WHEN (OLD.mps_id IS DISTINCT FROM NEW.mps_id OR OLD.shipment_id IS DISTINCT FROM NEW.shipment_id)
EXECUTE FUNCTION fn_enqueue_set_disc_carry_cost();

-- Drop the old triggers
DROP TRIGGER IF EXISTS tr_set_carry_cost_insert ON t_discs;
DROP TRIGGER IF EXISTS tr_set_carry_cost_update ON t_discs;

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Carry cost enqueuer function and triggers created.';
END $$;
