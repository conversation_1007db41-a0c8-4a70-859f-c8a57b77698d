// verifyImagesAll.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';

// -----------------------------
// Supabase initialization
// -----------------------------
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY; // Must be your service role key
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Utility: chunk an array into smaller pieces.
 */
function chunkArray(arr, size) {
  const chunks = [];
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size));
  }
  return chunks;
}

/**
 * Fetch a configuration value from t_config by key.
 */
async function fetchConfigValue(key) {
  const { data, error } = await supabase
    .from('t_config')
    .select('value')
    .eq('key', key)
    .single();
  if (error || !data) {
    throw new Error(`[fetchConfigValue] Missing key=${key}: ${error?.message}`);
  }
  return data.value;
}

/**
 * Build the full image URL for non-t_discs records.
 * For non-t_discs, we assume the image file name is derived from record_id.
 */
async function buildImageUrl(record) {
  const baseUrl = await fetchConfigValue('public_image_server');
  let folderKey;
  switch (record.table_name) {
    case 't_molds':
      folderKey = 'folder_molds';
      break;
    case 't_players':
      folderKey = 'folder_players';
      break;
    case 't_brands':
      folderKey = 'folder_brands';
      break;
    case 't_mps':
      folderKey = 'folder_mps';
      break;
    default:
      throw new Error(`Unknown table_name: ${record.table_name}`);
  }
  const folderVal = await fetchConfigValue(folderKey);
  if (!record.record_id) {
    throw new Error(`Missing record_id for t_images.id=${record.id}, table=${record.table_name}`);
  }
  return `${baseUrl}/${folderVal}/${record.record_id}.jpg`;
}

/**
 * Mark a t_images row as verified.
 */
async function markVerified(id) {
  const now = new Date().toISOString();
  const { error } = await supabase
    .from('t_images')
    .update({
      image_verified: true,
      image_verified_at: now,
      image_verified_notes: 're-verified successfully',
    })
    .eq('id', id);
  if (error) {
    console.error(`[markVerified] Could not update t_images.id=${id}:`, error.message);
  } else {
    console.log(`[markVerified] t_images.id=${id} marked as verified.`);
  }
}

/**
 * Mark a t_images row as error.
 */
async function markAsError(id, notes) {
  const now = new Date().toISOString();
  const { error } = await supabase
    .from('t_images')
    .update({
      image_verified: false,
      image_verified_at: now,
      image_verified_notes: notes,
    })
    .eq('id', id);
  if (error) {
    console.error(`[markAsError] Could not update t_images.id=${id}:`, error.message);
  } else {
    console.log(`[markAsError] t_images.id=${id} marked with error: ${notes}`);
  }
}

// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
// STEP 1: Skip sold discs in bulk (for t_discs)
// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
async function skipSoldDiscs() {
  console.log('[skipSoldDiscs] Starting chunked fetch for sold discs...');

  const chunkSize = 1000;
  let offset = 0;
  let allDiscIds = [];

  while (true) {
    const { data, error } = await supabase
      .from('t_discs')
      .select('id, sold_date')
      .not('sold_date', 'is', null)
      .range(offset, offset + chunkSize - 1);
    if (error) {
      throw new Error(`[skipSoldDiscs] Error fetching chunk offset=${offset}: ${error.message}`);
    }
    if (!data || data.length === 0) break;
    allDiscIds.push(...data.map(d => d.id));
    if (data.length < chunkSize) break;
    offset += chunkSize;
  }
  console.log(`[skipSoldDiscs] Total sold discs found: ${allDiscIds.length}`);
  if (allDiscIds.length === 0) {
    console.log('[skipSoldDiscs] No sold discs found. Exiting step.');
    return;
  }
  const discIdChunks = chunkArray(allDiscIds, 500);
  let totalSkipped = 0;
  const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
  for (let c = 0; c < discIdChunks.length; c++) {
    const chunk = discIdChunks[c];
    console.log(`[skipSoldDiscs] Processing chunk ${c + 1}/${discIdChunks.length}, size=${chunk.length}`);
    const { data: soldImages, error: soldImagesError } = await supabase
      .from('t_images')
      .select('id')
      .eq('table_name', 't_discs')
      .in('record_id', chunk)
      .or(`image_verified_at.is.null,image_verified_at.lt.'${oneWeekAgo}'`);
    if (soldImagesError) {
      throw new Error(`[skipSoldDiscs] Error fetching t_images for chunk: ${soldImagesError.message}`);
    }
    if (soldImages && soldImages.length > 0) {
      const soldImageIds = soldImages.map(img => img.id);
      const now = new Date().toISOString();
      const { error: updateError } = await supabase
        .from('t_images')
        .update({
          image_verified: false,
          image_verified_at: now,
          image_verified_notes: 'skipped because disc sold',
        })
        .in('id', soldImageIds);
      if (updateError) {
        throw new Error(`[skipSoldDiscs] Error updating sold disc images: ${updateError.message}`);
      }
      totalSkipped += soldImageIds.length;
      console.log(`[skipSoldDiscs] Skipped ${soldImageIds.length} t_images rows in this chunk.`);
    }
  }
  console.log(`[skipSoldDiscs] Done skipping sold discs. Total t_images skipped: ${totalSkipped}`);
}

// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
// STEP 2: Skip discs with no image_file_name in bulk (for t_discs)
// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
async function skipNoImageDiscs() {
  console.log('[skipNoImageDiscs] Starting chunked fetch for discs missing image_file_name...');
  const chunkSize = 1000;
  let offset = 0;
  let allDiscIds = [];
  while (true) {
    const { data, error } = await supabase
      .from('t_discs')
      .select('id, sold_date, image_file_name')
      .is('sold_date', null)
      .is('image_file_name', null)
      .range(offset, offset + chunkSize - 1);
    if (error) {
      throw new Error(`[skipNoImageDiscs] Error fetching chunk offset=${offset}: ${error.message}`);
    }
    if (!data || data.length === 0) break;
    allDiscIds.push(...data.map(d => d.id));
    if (data.length < chunkSize) break;
    offset += chunkSize;
  }
  console.log(`[skipNoImageDiscs] Total no-image discs found: ${allDiscIds.length}`);
  if (allDiscIds.length === 0) {
    console.log('[skipNoImageDiscs] No discs without image_file_name found. Exiting step.');
    return;
  }
  const discIdChunks = chunkArray(allDiscIds, 500);
  let totalSkipped = 0;
  const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
  for (let c = 0; c < discIdChunks.length; c++) {
    const chunk = discIdChunks[c];
    console.log(`[skipNoImageDiscs] Processing chunk ${c + 1}/${discIdChunks.length}, size=${chunk.length}`);
    const { data: noImageRows, error: noImageRowsError } = await supabase
      .from('t_images')
      .select('id')
      .eq('table_name', 't_discs')
      .in('record_id', chunk)
      .or(`image_verified_at.is.null,image_verified_at.lt.'${oneWeekAgo}'`);
    if (noImageRowsError) {
      throw new Error(`[skipNoImageDiscs] Error fetching t_images chunk: ${noImageRowsError.message}`);
    }
    if (noImageRows && noImageRows.length > 0) {
      const tImageIds = noImageRows.map(img => img.id);
      const now = new Date().toISOString();
      const { error: updateError } = await supabase
        .from('t_images')
        .update({
          image_verified: false,
          image_verified_at: now,
          image_verified_notes: 'no image file name in t_discs yet',
        })
        .in('id', tImageIds);
      if (updateError) {
        throw new Error(`[skipNoImageDiscs] Error updating t_images: ${updateError.message}`);
      }
      totalSkipped += tImageIds.length;
      console.log(`[skipNoImageDiscs] Skipped ${tImageIds.length} t_images rows in this chunk.`);
    }
  }
  console.log(`[skipNoImageDiscs] Done skipping no-image discs. Total t_images skipped: ${totalSkipped}`);
}

// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
// STEP 3: Re-verify t_discs images (existing logic)
// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
async function reverifyFalseDiscs() {
  console.log('[reverifyFalseDiscs] Starting re-verification of t_discs images...');
  const chunkSize = 500;
  const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
  let offset = 0;
  let allTImages = [];
  while (true) {
    const { data, error } = await supabase
      .from('t_images')
      .select('*')
      .eq('table_name', 't_discs')
      .eq('image_verified', false)
      .or(`image_verified_at.is.null,image_verified_at.lt.'${oneWeekAgo}'`)
      .order('id', { ascending: true })
      .range(offset, offset + chunkSize - 1);
    if (error) {
      throw new Error(`[reverifyFalseDiscs] Error fetching t_images chunk offset=${offset}: ${error.message}`);
    }
    if (!data || data.length === 0) break;
    allTImages.push(...data);
    if (data.length < chunkSize) break;
    offset += chunkSize;
  }
  if (allTImages.length === 0) {
    console.log('[reverifyFalseDiscs] No t_discs records found to re-verify. Exiting step.');
    return;
  }
  console.log(`[reverifyFalseDiscs] Found ${allTImages.length} t_discs records to re-verify.`);
  const allChunks = chunkArray(allTImages, 500);
  let processed = 0;
  for (let i = 0; i < allChunks.length; i++) {
    const chunk = allChunks[i];
    console.log(`[reverifyFalseDiscs] Processing chunk ${i + 1}/${allChunks.length}, size=${chunk.length}`);
    const discIds = chunk.map(row => row.record_id);
    const { data: discs, error: discsError } = await supabase
      .from('t_discs')
      .select('id, image_file_name, sold_date')
      .in('id', discIds);
    if (discsError) {
      throw new Error(`[reverifyFalseDiscs] Error fetching t_discs: ${discsError.message}`);
    }
    const discMap = {};
    discs.forEach(d => { discMap[d.id] = d; });
    for (const tImg of chunk) {
      await headCheckDiscImage(tImg, discMap);
      processed++;
      await new Promise(resolve => setTimeout(resolve, 300));
    }
  }
  console.log(`[reverifyFalseDiscs] Done re-verifying t_discs. Processed ${processed} records total.`);
}

/**
 * HEAD-check a single disc image, building URL from disc.image_file_name.
 */
async function headCheckDiscImage(tImage, discMap) {
  const discRow = discMap[tImage.record_id];
  if (!discRow) {
    await markAsError(tImage.id, 'No matching disc record');
    return;
  }
  if (!discRow.image_file_name) {
    await markAsError(tImage.id, 'Missing disc image_file_name');
    return;
  }
  let url;
  try {
    const baseUrl = await fetchConfigValue('public_image_server');
    const folderVal = await fetchConfigValue('folder_discs');
    const sub = discRow.image_file_name.substring(0, 6);
    url = `${baseUrl}/${folderVal}/${sub}/${discRow.image_file_name}.jpg`;
  } catch (err) {
    console.error(`[headCheckDiscImage] URL build error (t_images.id=${tImage.id}):`, err.message);
    await markAsError(tImage.id, `URL build error: ${err.message}`);
    return;
  }
  console.log(`[headCheckDiscImage] HEAD => t_images.id=${tImage.id}, url=${url}`);
  let response;
  try {
    response = await fetch(url, { method: 'HEAD' });
  } catch (err) {
    console.error(`[headCheckDiscImage] HEAD request error (t_images.id=${tImage.id}):`, err.message);
    await markAsError(tImage.id, `HEAD request error: ${err.message}`);
    return;
  }
  if (!response.ok) {
    console.error(`[headCheckDiscImage] HEAD fail: status=${response.status} (t_images.id=${tImage.id})`);
    await markAsError(tImage.id, `HEAD fail: status=${response.status}`);
    return;
  }
  await markVerified(tImage.id);
}

// ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
// STEP 4: Re-verify non-t_discs images (NEW)
// Process records where either:
//   image_verified_at IS NULL
// OR image_verified = false AND image_verified_at < oneWeekAgo
// Exclude t_discs.
// We'll fetch two sets (A and B), merge them (deduplicating by id), and process them.
async function reverifyFalseNonDiscs() {
  console.log('[reverifyFalseNonDiscs] Starting re-verification of non-t_discs images...');
  const chunkSize = 500;
  const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
  
  // Set A: image_verified_at IS NULL
  let offset = 0;
  let setA = [];
  while (true) {
    const { data, error } = await supabase
      .from('t_images')
      .select('*')
      .not('table_name', 'eq', 't_discs')
      .is('image_verified_at', null)
      .order('id', { ascending: true })
      .range(offset, offset + chunkSize - 1);
    if (error) {
      console.error('[reverifyFalseNonDiscs] Error fetching set A chunk:', error.message);
      process.exit(1);
    }
    if (!data || data.length === 0) break;
    setA.push(...data);
    if (data.length < chunkSize) break;
    offset += chunkSize;
  }
  
  // Set B: image_verified = false AND image_verified_at < oneWeekAgo
  offset = 0;
  let setB = [];
  while (true) {
    const { data, error } = await supabase
      .from('t_images')
      .select('*')
      .not('table_name', 'eq', 't_discs')
      .eq('image_verified', false)
      .or(`image_verified_at.lt.'${oneWeekAgo}'`)
      .order('id', { ascending: true })
      .range(offset, offset + chunkSize - 1);
    if (error) {
      console.error('[reverifyFalseNonDiscs] Error fetching set B chunk:', error.message);
      process.exit(1);
    }
    if (!data || data.length === 0) break;
    setB.push(...data);
    if (data.length < chunkSize) break;
    offset += chunkSize;
  }
  
  // Merge Set A and Set B, deduplicating by id.
  const recordMap = {};
  for (const rec of [...setA, ...setB]) {
    recordMap[rec.id] = rec;
  }
  const allRecords = Object.values(recordMap);
  
  if (allRecords.length === 0) {
    console.log('[reverifyFalseNonDiscs] No non-t_discs records need re-verification.');
    return;
  }
  
  console.log(`[reverifyFalseNonDiscs] Found ${allRecords.length} non-t_discs records to re-verify.`);
  const recordChunks = chunkArray(allRecords, 100);
  let processed = 0;
  for (const chunk of recordChunks) {
    for (const record of chunk) {
      try {
        console.log(`[reverifyFalseNonDiscs] Processing t_images.id=${record.id}, table=${record.table_name}`);
        const url = await buildImageUrl(record);
        console.log(`[reverifyFalseNonDiscs] HEAD-check URL: ${url}`);
        let response;
        try {
          response = await fetch(url, { method: 'HEAD' });
        } catch (err) {
          console.error(`[reverifyFalseNonDiscs] HEAD request error for t_images.id=${record.id}:`, err.message);
          await markAsError(record.id, `HEAD request error: ${err.message}`);
          continue;
        }
        if (!response.ok) {
          console.error(`[reverifyFalseNonDiscs] HEAD fail for t_images.id=${record.id} with status=${response.status}`);
          await markAsError(record.id, `HEAD fail: status=${response.status}`);
          continue;
        }
        await markVerified(record.id);
      } catch (err) {
        console.error(`[reverifyFalseNonDiscs] Exception for t_images.id=${record.id}:`, err.message);
        await markAsError(record.id, `Exception: ${err.message}`);
      }
      processed++;
      await new Promise(resolve => setTimeout(resolve, 300));
    }
  }
  console.log(`[reverifyFalseNonDiscs] Re-verification complete. Processed ${processed} non-t_discs records.`);
}

// ------------------------------------
// MAIN: Run the 4 steps in sequence
// ------------------------------------
async function main() {
  try {
    console.log('[main] Step 1: Skip sold discs...');
    await skipSoldDiscs();

    console.log('[main] Step 2: Skip discs with no image_file_name...');
    await skipNoImageDiscs();

    console.log('[main] Step 3: Re-verify t_discs images...');
    await reverifyFalseDiscs();

    console.log('[main] Step 4: Re-verify non-t_discs images...');
    await reverifyFalseNonDiscs();

    console.log('[main] All steps complete!');
  } catch (err) {
    console.error('[main] Fatal error:', err.message);
    process.exit(1);
  }
}

main();
