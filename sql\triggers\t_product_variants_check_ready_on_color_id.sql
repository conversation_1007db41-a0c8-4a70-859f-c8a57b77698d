-- Trigger + function to enqueue check_if_product_variant_is_ready when readiness fields change
-- Scheduling: immediate (NOW())

-- Function
CREATE OR REPLACE FUNCTION public.enqueue_check_if_pv_ready_on_color_change()
R<PERSON><PERSON>NS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Enqueue when any readiness-related field actually changed
  IF
    (OLD.color_id IS DISTINCT FROM NEW.color_id) OR
    (OLD.price IS DISTINCT FROM NEW.price) OR
    (OLD.order_cost IS DISTINCT FROM NEW.order_cost) OR
    (OLD.notes IS DISTINCT FROM NEW.notes) OR
    (OLD.map_price IS DISTINCT FROM NEW.map_price) OR
    (OLD.msrp IS DISTINCT FROM NEW.msrp) OR
    (OLD.shopify_weight_lbs IS DISTINCT FROM NEW.shopify_weight_lbs) OR
    (OLD.release_date_and_time IS DISTINCT FROM NEW.release_date_and_time) OR
    (OLD.uploaded_to_shopify_at IS DISTINCT FROM NEW.uploaded_to_shopify_at) OR
    (OLD.notes_images IS DISTINCT FROM NEW.notes_images) OR
    (OLD.op1_name IS DISTINCT FROM NEW.op1_name) OR
    (OLD.op1_value IS DISTINCT FROM NEW.op1_value) OR
    (OLD.op2_name IS DISTINCT FROM NEW.op2_name) OR
    (OLD.op2_value IS DISTINCT FROM NEW.op2_value) OR
    (OLD.op3_name IS DISTINCT FROM NEW.op3_name) OR
    (OLD.op3_value IS DISTINCT FROM NEW.op3_value)
  THEN
    INSERT INTO public.t_task_queue (task_type, payload, scheduled_at, enqueued_by)
    VALUES (
      'check_if_product_variant_is_ready',
      jsonb_build_object('id', NEW.id),
      NOW(),
      'trigger:t_product_variants.readiness_fields'
    );
  END IF;
  RETURN NEW;
END;
$$;

-- Recreate trigger to watch all readiness fields
DROP TRIGGER IF EXISTS trg_t_product_variants_color_id_updated ON public.t_product_variants;

CREATE TRIGGER trg_t_product_variants_color_id_updated
AFTER UPDATE OF
  color_id,
  price,
  order_cost,
  notes,
  map_price,
  msrp,
  shopify_weight_lbs,
  release_date_and_time,
  uploaded_to_shopify_at,
  notes_images,
  op1_name,
  op1_value,
  op2_name,
  op2_value,
  op3_name,
  op3_value
ON public.t_product_variants
FOR EACH ROW
WHEN (
  (OLD.color_id IS DISTINCT FROM NEW.color_id) OR
  (OLD.price IS DISTINCT FROM NEW.price) OR
  (OLD.order_cost IS DISTINCT FROM NEW.order_cost) OR
  (OLD.notes IS DISTINCT FROM NEW.notes) OR
  (OLD.map_price IS DISTINCT FROM NEW.map_price) OR
  (OLD.msrp IS DISTINCT FROM NEW.msrp) OR
  (OLD.shopify_weight_lbs IS DISTINCT FROM NEW.shopify_weight_lbs) OR
  (OLD.release_date_and_time IS DISTINCT FROM NEW.release_date_and_time) OR
  (OLD.uploaded_to_shopify_at IS DISTINCT FROM NEW.uploaded_to_shopify_at) OR
  (OLD.notes_images IS DISTINCT FROM NEW.notes_images) OR
  (OLD.op1_name IS DISTINCT FROM NEW.op1_name) OR
  (OLD.op1_value IS DISTINCT FROM NEW.op1_value) OR
  (OLD.op2_name IS DISTINCT FROM NEW.op2_name) OR
  (OLD.op2_value IS DISTINCT FROM NEW.op2_value) OR
  (OLD.op3_name IS DISTINCT FROM NEW.op3_name) OR
  (OLD.op3_value IS DISTINCT FROM NEW.op3_value)
)
EXECUTE FUNCTION public.enqueue_check_if_pv_ready_on_color_change();
