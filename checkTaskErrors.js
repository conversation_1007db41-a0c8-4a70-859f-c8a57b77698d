import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkTaskDetails() {
  console.log('🔍 Checking task error details...');
  
  const { data, error } = await supabase
    .from('t_task_queue')
    .select('id, task_type, payload, status, result')
    .eq('task_type', 'update_veeqo_d_title')
    .eq('status', 'error')
    .limit(3);
  
  if (error) {
    console.error('❌ Error:', error);
    return;
  }
  
  data.forEach((task, index) => {
    console.log(`${index + 1}. Task ${task.id}: Disc ${task.payload.id}`);
    console.log(`   Error: ${task.result?.error || 'No error details'}`);
    console.log(`   Full result:`, JSON.stringify(task.result, null, 2));
    console.log('');
  });
}

checkTaskDetails();
