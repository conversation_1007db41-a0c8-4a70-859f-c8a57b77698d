/**
 * Process update_disc_variant_price_on_shopify task
 *
 * This task updates a specific disc's variant price on Shopify.
 * It uses the disc's SKU (format: 'D' + disc.id) to find the variant
 * and updates the price to the new retail price.
 */

import fetch from 'node-fetch';

// Shopify configuration
const shopifyEndpoint = 'https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/graphql.json';
const shopifyAccessToken = '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';
const variantsEndpoint = 'https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/variants/';

/**
 * Execute a Shopify GraphQL request
 * @param {string} query - The GraphQL query
 * @param {Object} variables - The query variables
 * @returns {Promise<Object>} - The response data
 */
async function shopifyGraphQLRequest(query, variables = {}) {
  const response = await fetch(shopifyEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken
    },
    body: JSON.stringify({
      query,
      variables
    })
  });

  if (!response.ok) {
    throw new Error(`Shopify GraphQL request failed: ${response.status} ${response.statusText}`);
  }

  const result = await response.json();
  
  if (result.errors) {
    throw new Error(`Shopify GraphQL errors: ${JSON.stringify(result.errors)}`);
  }

  return result.data;
}

/**
 * Find Shopify variant by SKU using GraphQL
 * @param {string} sku - The SKU to search for
 * @returns {Promise<Object|null>} - The variant data or null if not found
 */
async function findVariantBySku(sku) {
  try {
    console.log(`[processUpdateDiscVariantPriceOnShopifyTask] Searching for variant with SKU: ${sku}`);
    
    const query = `
      query getVariantBySku($query: String!) {
        productVariants(first: 1, query: $query) {
          edges {
            node {
              id
              sku
              price
              product {
                id
                title
              }
            }
          }
        }
      }
    `;
    
    const variables = {
      query: `sku:${sku}`
    };
    
    const data = await shopifyGraphQLRequest(query, variables);
    
    if (!data.productVariants.edges.length) {
      console.log(`[processUpdateDiscVariantPriceOnShopifyTask] No variant found with SKU: ${sku}`);
      return null;
    }
    
    const variant = data.productVariants.edges[0].node;
    
    // Convert GraphQL IDs to REST API IDs
    const variantId = variant.id.split('/').pop();
    const productId = variant.product.id.split('/').pop();
    
    const variantData = {
      variant_id: variantId,
      product_id: productId,
      current_price: variant.price,
      product_title: variant.product.title
    };
    
    console.log(`[processUpdateDiscVariantPriceOnShopifyTask] Found variant with ID: ${variantData.variant_id} for SKU: ${sku}, current price: ${variantData.current_price}`);
    return variantData;
  } catch (error) {
    console.error(`[processUpdateDiscVariantPriceOnShopifyTask] Failed to find variant by SKU: ${error.message}`);
    throw error;
  }
}

/**
 * Update variant price on Shopify using REST API
 * @param {string} variantId - The variant ID
 * @param {number} newPrice - The new price
 * @returns {Promise<Object>} - The updated variant data
 */
async function updateVariantPrice(variantId, newPrice) {
  try {
    console.log(`[processUpdateDiscVariantPriceOnShopifyTask] Updating variant ${variantId} with new price: ${newPrice}`);
    
    const updateEndpoint = `${variantsEndpoint}${variantId}.json`;
    
    const payload = {
      variant: {
        id: variantId,
        price: newPrice.toString()
      }
    };
    
    const response = await fetch(updateEndpoint, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      },
      body: JSON.stringify(payload)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Shopify REST API request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    const result = await response.json();
    console.log(`[processUpdateDiscVariantPriceOnShopifyTask] Successfully updated variant: ${JSON.stringify(result.variant)}`);
    return result.variant;
  } catch (error) {
    console.error(`[processUpdateDiscVariantPriceOnShopifyTask] Failed to update variant ${variantId}: ${error.message}`);
    throw error;
  }
}

/**
 * Process update disc variant price on Shopify task
 * @param {Object} task - The task object from the queue
 * @param {Object} context - Context object containing supabase client and helper functions
 */
export default async function processUpdateDiscVariantPriceOnShopifyTask(task, context = {}) {
  const { supabase, updateTaskStatus, logError } = context;

  if (!supabase) {
    console.error('[processUpdateDiscVariantPriceOnShopifyTask] Supabase client not provided in context');
    return;
  }
  const { payload } = task;
  const itemId = payload.id;
  const itemType = payload.item_type || 'disc'; // Default to 'disc' for backward compatibility
  const newRetailPrice = payload.new_retail_price;

  console.log(`[processUpdateDiscVariantPriceOnShopifyTask] Processing price update for ${itemType} id=${itemId}, new price=${newRetailPrice}`);

  try {
    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Validate that we have the required data
    if (!itemId) {
      throw new Error(`${itemType} ID is required in payload`);
    }

    if (!newRetailPrice) {
      throw new Error('New retail price is required in payload');
    }

    // Generate the SKU based on item type
    let itemSku;
    if (itemType === 'osl') {
      itemSku = `OS${itemId}`;
    } else {
      itemSku = `D${itemId}`;
    }
    console.log(`[processUpdateDiscVariantPriceOnShopifyTask] Generated SKU: ${itemSku} for ${itemType} id=${itemId}`);

    // Find the variant in Shopify
    const variantData = await findVariantBySku(itemSku);

    if (!variantData) {
      await updateTaskStatus(task.id, 'completed', {
        message: `No Shopify variant found for ${itemType} id=${itemId} with SKU=${itemSku}`,
        item_id: itemId,
        item_type: itemType,
        sku: itemSku,
        new_retail_price: newRetailPrice,
        status: 'variant_not_found'
      });
      return;
    }

    // Check if price actually needs updating
    const currentPrice = parseFloat(variantData.current_price);
    const targetPrice = parseFloat(newRetailPrice);
    
    if (Math.abs(currentPrice - targetPrice) < 0.01) {
      console.log(`[processUpdateDiscVariantPriceOnShopifyTask] Price already matches target price for ${itemType} id=${itemId}`);
      await updateTaskStatus(task.id, 'completed', {
        message: `Price already matches target price for ${itemType} id=${itemId}`,
        item_id: itemId,
        item_type: itemType,
        sku: itemSku,
        variant_id: variantData.variant_id,
        current_price: currentPrice,
        target_price: targetPrice,
        status: 'no_update_needed'
      });
      return;
    }

    // Update the variant price
    const updatedVariant = await updateVariantPrice(variantData.variant_id, newRetailPrice);
    
    // Mark task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully updated price for ${itemType} id=${itemId} from ${currentPrice} to ${newRetailPrice}`,
      item_id: itemId,
      item_type: itemType,
      sku: itemSku,
      variant_id: variantData.variant_id,
      product_title: variantData.product_title,
      old_price: currentPrice,
      new_price: parseFloat(updatedVariant.price),
      status: 'updated'
    });

    console.log(`[processUpdateDiscVariantPriceOnShopifyTask] Successfully completed price update for ${itemType} id=${itemId}`);

  } catch (error) {
    console.error(`[processUpdateDiscVariantPriceOnShopifyTask] Error updating price for ${itemType} id=${itemId}:`, error.message);

    await updateTaskStatus(task.id, 'failed', {
      error: error.message,
      item_id: itemId,
      item_type: itemType,
      sku: itemType === 'osl' ? `OS${itemId}` : `D${itemId}`,
      new_retail_price: newRetailPrice
    });
  }
}


