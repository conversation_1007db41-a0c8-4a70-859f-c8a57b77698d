-- Make fn_create_movements_for_completed_sale() support both UPDATE and INSERT
CREATE OR REPLACE FUNCTION public.fn_create_movements_for_completed_sale()
RETURNS trigger
LANGUAGE plpgsql
AS $function$
BEGIN
  -- Handle UPDATE: fire only on transition to Complete
  IF TG_OP = 'UPDATE' THEN
    IF NEW.status = 'Complete' AND OLD.status IS DISTINCT FROM NEW.status THEN
      INSERT INTO public.t_inventory_movements (
        product_variant_id,
        warehouse_id,
        movement_type,
        source_type,
        source_id,
        quantity,
        notes,
        created_at,
        created_by,
        movement_date,
        carrying_cost
      )
      SELECT
        l.product_variant_id,
        1,
        'sale',
        'sale',
        s.id,
        -l.quantity,
        s.notes,
        now(),
        s.created_by,
        s.order_date,
        COALESCE(pv.carrying_cost, pv.order_cost)
      FROM public.t_sales_order_lines l
      JOIN public.t_sales_orders s ON s.id = l.sales_order_id
      JOIN public.t_product_variants pv ON pv.id = l.product_variant_id
      WHERE s.id = NEW.id;
    END IF;

  -- Handle INSERT: fire when row is already Complete and not Migration (the trigger WHEN clause enforces not Migration)
  ELSIF TG_OP = 'INSERT' THEN
    IF NEW.status = 'Complete' THEN
      INSERT INTO public.t_inventory_movements (
        product_variant_id,
        warehouse_id,
        movement_type,
        source_type,
        source_id,
        quantity,
        notes,
        created_at,
        created_by,
        movement_date,
        carrying_cost
      )
      SELECT
        l.product_variant_id,
        1,
        'sale',
        'sale',
        s.id,
        -l.quantity,
        s.notes,
        now(),
        s.created_by,
        s.order_date,
        COALESCE(pv.carrying_cost, pv.order_cost)
      FROM public.t_sales_order_lines l
      JOIN public.t_sales_orders s ON s.id = l.sales_order_id
      JOIN public.t_product_variants pv ON pv.id = l.product_variant_id
      WHERE s.id = NEW.id;
    END IF;
  END IF;

  RETURN NEW;
END;
$function$;

