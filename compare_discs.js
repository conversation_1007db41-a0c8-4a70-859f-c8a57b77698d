import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM3ODY1NDEsImV4cCI6MjA0OTM2MjU0MX0.MtBXMZt7rCqXd6c9clhrNoVtfOxEilX2C8oboMceOGg';
const supabase = createClient(supabaseUrl, supabaseKey);

const compareDiscs = async () => {
  try {
    console.log('Comparing two discs and their matching with OSL 16890...');
    
    // Disc 1 (matched successfully)
    const disc1Id = 421485;
    const disc1MpsId = 6462;
    const disc1ColorId = 9;
    const disc1Weight = 162;
    
    // Disc 2 (not matching)
    const disc2Id = 421349;
    const disc2MpsId = 19105;
    const disc2ColorId = 6;
    const disc2Weight = 172.2;
    
    console.log('Disc 1 (matched successfully):');
    console.log(`ID: ${disc1Id}, MPS ID: ${disc1MpsId}, Color ID: ${disc1ColorId}, Weight: ${disc1Weight}`);
    
    console.log('\nDisc 2 (not matching):');
    console.log(`ID: ${disc2Id}, MPS ID: ${disc2MpsId}, Color ID: ${disc2ColorId}, Weight: ${disc2Weight}`);
    
    // Check OSL 16890
    console.log('\nChecking OSL 16890...');
    const { data: oslData, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('*')
      .eq('id', 16890)
      .maybeSingle();
      
    if (oslError) {
      console.error('Error fetching OSL 16890:', oslError);
      return;
    }
    
    if (!oslData) {
      console.log('OSL 16890 not found in the database');
      return;
    }
    
    console.log('OSL 16890 data:', oslData);
    
    // Manually check if Disc 1 should match
    console.log('\nChecking if Disc 1 should match with OSL 16890:');
    const disc1RoundedWeight = disc1Weight >= 172.5 ? Math.ceil(disc1Weight) : Math.floor(disc1Weight);
    
    const disc1MpsMatch = disc1MpsId === oslData.mps_id;
    const disc1WeightMatch = disc1RoundedWeight >= oslData.min_weight && disc1RoundedWeight <= oslData.max_weight;
    const disc1ColorMatch = oslData.color_id === 23 || disc1ColorId === oslData.color_id;
    
    console.log(`MPS Match: ${disc1MpsMatch} (${disc1MpsId} === ${oslData.mps_id})`);
    console.log(`Weight Match: ${disc1WeightMatch} (${disc1RoundedWeight} >= ${oslData.min_weight} && ${disc1RoundedWeight} <= ${oslData.max_weight})`);
    console.log(`Color Match: ${disc1ColorMatch} (${oslData.color_id} === 23 || ${disc1ColorId} === ${oslData.color_id})`);
    
    const disc1ShouldMatch = disc1MpsMatch && disc1WeightMatch && disc1ColorMatch;
    console.log(`Should Match: ${disc1ShouldMatch}`);
    
    // Manually check if Disc 2 should match
    console.log('\nChecking if Disc 2 should match with OSL 16890:');
    const disc2RoundedWeight = disc2Weight >= 172.5 ? Math.ceil(disc2Weight) : Math.floor(disc2Weight);
    
    const disc2MpsMatch = disc2MpsId === oslData.mps_id;
    const disc2WeightMatch = disc2RoundedWeight >= oslData.min_weight && disc2RoundedWeight <= oslData.max_weight;
    const disc2ColorMatch = oslData.color_id === 23 || disc2ColorId === oslData.color_id;
    
    console.log(`MPS Match: ${disc2MpsMatch} (${disc2MpsId} === ${oslData.mps_id})`);
    console.log(`Weight Match: ${disc2WeightMatch} (${disc2RoundedWeight} >= ${oslData.min_weight} && ${disc2RoundedWeight} <= ${oslData.max_weight})`);
    console.log(`Color Match: ${disc2ColorMatch} (${oslData.color_id} === 23 || ${disc2ColorId} === ${oslData.color_id})`);
    
    const disc2ShouldMatch = disc2MpsMatch && disc2WeightMatch && disc2ColorMatch;
    console.log(`Should Match: ${disc2ShouldMatch}`);
    
    // Call the find_matching_osl function for both discs
    console.log('\nCalling find_matching_osl function for Disc 1...');
    const { data: disc1Result, error: disc1Error } = await supabase.rpc(
      'find_matching_osl',
      {
        mps_id_param: disc1MpsId,
        color_id_param: disc1ColorId,
        weight_param: disc1Weight
      }
    );
    
    if (disc1Error) {
      console.error('Error calling find_matching_osl for Disc 1:', disc1Error);
    } else {
      console.log('Function result for Disc 1:', disc1Result);
    }
    
    console.log('\nCalling find_matching_osl function for Disc 2...');
    const { data: disc2Result, error: disc2Error } = await supabase.rpc(
      'find_matching_osl',
      {
        mps_id_param: disc2MpsId,
        color_id_param: disc2ColorId,
        weight_param: disc2Weight
      }
    );
    
    if (disc2Error) {
      console.error('Error calling find_matching_osl for Disc 2:', disc2Error);
    } else {
      console.log('Function result for Disc 2:', disc2Result);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
};

compareDiscs();
