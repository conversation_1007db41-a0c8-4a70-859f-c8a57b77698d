const fs = require('fs');
const txt = fs.readFileSync('admin.html','utf8');
const start = txt.indexOf('<script>');
const end = txt.lastIndexOf('</script>');
const js = txt.slice(start + '<script>'.length, end);
const lines = js.split(/\n/);
function parses(upto){
  try{
    const chunk = lines.slice(0,upto).join('\n');
    const wrapped = '(function(){\n' + chunk + '\n})();';
    new Function(wrapped);
    return true;
  }catch(e){
    return false;
  }
}
let lo=1, hi=lines.length, bad=hi;
while(lo<=hi){
  const mid = Math.floor((lo+hi)/2);
  if (parses(mid)) { lo = mid+1; }
  else { bad = mid; hi = mid-1; }
}
console.log('First failing line (JS):', bad);
const s = Math.max(1,bad-3), e = Math.min(lines.length, bad+3);
for(let i=s;i<=e;i++){
  console.log(String(i).padStart(6,' ')+': '+lines[i-1]);
}

