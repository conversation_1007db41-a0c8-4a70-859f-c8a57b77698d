import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function createFundraiserRecords() {
    try {
        console.log('🔧 Creating correct fundraiser records...\n');
        
        // Define the two fundraiser items based on your original data
        const fundraiserItems = [
            {
                raw_model: 'Fundraiser - <PERSON> Z Jawbreaker Thrasher (msrp $19.99, cost $10.00)',
                excel_row_hint: 25,
                mold_name: 'Thrasher',
                plastic_name: 'Elite Z Jawbreaker',
                stamp_name: 'Live Free - Be Happy - Funky Ben Askren Fundraiser'
            },
            {
                raw_model: 'Fundraiser - Ben Askren Big Z Buzzz  (msrp $19.99, cost $10.00)',
                excel_row_hint: 28,
                mold_name: '<PERSON><PERSON>',
                plastic_name: 'Big Z Collection',
                stamp_name: 'Live Free - Be Happy - Funky <PERSON> Fundraiser'
            }
        ];

        console.log(`Creating ${fundraiserItems.length} fundraiser records...\n`);

        for (const item of fundraiserItems) {
            console.log(`Creating record for: ${item.raw_model}`);
            console.log(`  Parsed: ${item.plastic_name} ${item.mold_name} (${item.stamp_name})`);

            // Create the correct record in column A
            const newRecord = {
                mold_name: item.mold_name,
                plastic_name: item.plastic_name,
                min_weight: 150, // Default weight range for fundraiser items
                max_weight: 180,
                color_name: 'Varies',
                stamp_name: item.stamp_name,
                vendor_product_code: `${item.plastic_name}_${item.mold_name}_150-180`.replace(/\s+/g, '_'),
                is_orderable: true,
                is_currently_available: true,
                cost_price: 10.00, // From the description
                excel_mapping_key: `SPECIAL|${item.raw_model}|Order Qty`,
                excel_row_hint: item.excel_row_hint,
                excel_column: 'A', // Correct column for fundraiser items
                raw_line_type: 'SPECIAL',
                raw_model: item.raw_model,
                raw_weight_range: 'Order Qty',
                vendor_description: '' // Empty like other records
            };

            const { error: insertError } = await supabase
                .from('it_discraft_order_sheet_lines')
                .insert(newRecord);

            if (insertError) {
                console.error(`❌ Error inserting record for ${item.raw_model}:`, insertError);
            } else {
                console.log(`✅ Created correct record for row ${item.excel_row_hint}`);
            }
        }

        // Verify the new records
        console.log('\n📋 Verifying new fundraiser records...');
        
        const { data: verifyData, error: verifyError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, mold_name, plastic_name, stamp_name, is_orderable, excel_mapping_key')
            .eq('excel_column', 'A')
            .ilike('raw_model', '%Fundraiser - Ben Askren%')
            .order('excel_row_hint');

        if (verifyError) {
            console.error('❌ Error verifying records:', verifyError);
        } else {
            console.log(`✅ Found ${verifyData.length} fundraiser records in column A:`);
            verifyData.forEach((record, index) => {
                console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.mold_name} | ${record.plastic_name}`);
                console.log(`      Stamp: ${record.stamp_name}`);
                console.log(`      Mapping: ${record.excel_mapping_key}`);
                console.log(`      Orderable: ${record.is_orderable}`);
                console.log('');
            });
        }

        // Test MPS matching
        console.log('🔍 Testing MPS matching for fundraiser records...');
        
        // Run MPS calculation for these new records
        const { data: mpsRecords, error: mpsError } = await supabase
            .from('t_mps')
            .select(`
                id,
                t_plastics!inner(plastic),
                t_molds!inner(mold),
                t_stamps!inner(stamp)
            `)
            .eq('t_plastics.brand_id', 6); // Discraft

        if (mpsError) {
            console.error('❌ Error fetching MPS records:', mpsError);
            return;
        }

        // Create lookup map
        const mpsMap = new Map();
        mpsRecords.forEach(mps => {
            const key = `${mps.t_plastics.plastic.trim()}|${mps.t_molds.mold.trim()}|${mps.t_stamps.stamp.trim()}`;
            mpsMap.set(key, mps.id);
        });

        // Check if fundraiser records can be matched
        for (const item of fundraiserItems) {
            const key = `${item.plastic_name.trim()}|${item.mold_name.trim()}|${item.stamp_name.trim()}`;
            const mpsId = mpsMap.get(key);
            
            console.log(`\nChecking: ${key}`);
            console.log(`MPS ID: ${mpsId || 'NOT FOUND'}`);
            
            if (mpsId) {
                // Update the record with MPS ID
                const { error: updateError } = await supabase
                    .from('it_discraft_order_sheet_lines')
                    .update({ calculated_mps_id: mpsId })
                    .eq('excel_column', 'A')
                    .eq('raw_model', item.raw_model);

                if (updateError) {
                    console.error(`❌ Error updating MPS ID:`, updateError);
                } else {
                    console.log(`✅ Updated with MPS ID: ${mpsId}`);
                }
            }
        }

        console.log('\n🎉 Fundraiser records creation completed!');
        console.log('\n📋 Summary:');
        console.log('   • Created 2 fundraiser records in column A');
        console.log('   • Proper mold/plastic/stamp parsing applied');
        console.log('   • MPS matching attempted');
        console.log('   • Records are now orderable in the correct column');
        
    } catch (error) {
        console.error('❌ Creation failed:', error.message);
    }
}

createFundraiserRecords().catch(console.error);
