# DBF to Supabase Daily Import

This package provides tools to automatically import a .DBF file into Supabase on a daily basis.

## Setup

1. Install dependencies:
   ```
   npm install
   ```

2. Create a `.env` file with your Supabase credentials and import configuration:
   ```
   # Supabase credentials
   SUPABASE_URL=https://your-project-id.supabase.co
   SUPABASE_KEY=your-supabase-service-role-key

   # DBF import configuration
   DBF_FILE_PATH=./data/daily_import.dbf
   TARGET_TABLE=imported_dbf_data
   TRUNCATE_BEFORE_IMPORT=true
   ```

3. Create the target table in Supabase if it doesn't exist yet. You can use the Supabase UI or run SQL commands.

4. Create the truncate function in Supabase:
   ```sql
   -- Run this in the Supabase SQL editor
   CREATE OR REPLACE FUNCTION public.truncate_table(table_name text)
   RETURNS void AS $$
   BEGIN
     EXECUTE 'TRUNCATE TABLE ' || quote_ident(table_name) || ' RESTART IDENTITY CASCADE';
   END;
   $$ LANGUAGE plpgsql SECURITY DEFINER;
   ```

5. Set up the daily scheduled task:
   ```
   node setupDailyImport.js
   ```
   Follow the instructions to set up either a Windows Task Scheduler task or a PM2 scheduled task.

## Usage

### Manual Import

To manually import a DBF file:

```
node manualImport.js --file=path/to/your/file.dbf --table=your_target_table --truncate=true
```

Or with shorter options:

```
node manualImport.js -f path/to/your/file.dbf -t your_target_table -c
```

### Scheduled Import

Once the scheduled task is set up, the import will run automatically at 6:15 AM every day (configurable in the setup script).

## Troubleshooting

### Check Logs

- For Windows Task Scheduler: Check the Windows Event Viewer for task execution logs
- For PM2: Run `pm2 logs dbf-import` to see the logs

### Common Issues

1. **File not found**: Make sure the DBF file path is correct and the file exists at the time the import runs
2. **Database connection issues**: Verify your Supabase credentials
3. **Permission issues**: Ensure the user running the task has access to the DBF file
4. **Table structure mismatch**: If the DBF file structure doesn't match the Supabase table, you may need to modify the import script

## Customization

You can customize the import process by modifying the `importDbfToSupabase.js` script. Common customizations include:

- Field mapping
- Data transformation
- Validation rules
- Error handling

## Creating the Target Table and Importing Data

### One-Step Setup

The easiest way to set up everything is to use the combined setup script:

```
node setupDbfImport.js --file=path/to/your/file.dbf
```

This script will:
1. Analyze your DBF file structure
2. Create a matching table in Supabase
3. Import the data into the table

Options:
- `--file` or `-f`: Path to the DBF file
- `--table` or `-t`: Target table name (optional, will be generated from file name if not provided)
- `--analyze-only` or `-a`: Only analyze the DBF structure without creating the table or importing data
- `--create-only` or `-c`: Only create the table without importing data
- `--import-only` or `-i`: Only import data without creating the table
- `--truncate`: Truncate the table before importing data (default: true)

### Step-by-Step Setup

If you prefer to do the process step by step:

1. First, create the SQL execution function in Supabase:
   ```sql
   -- Run this in the Supabase SQL editor
   CREATE OR REPLACE FUNCTION public.exec_sql(sql_query text)
   RETURNS void AS $$
   BEGIN
     EXECUTE sql_query;
   END;
   $$ LANGUAGE plpgsql SECURITY DEFINER;
   ```

2. Analyze your DBF file structure:
   ```
   node analyzeDbfStructure.js path/to/your/file.dbf
   ```
   This will generate a `create_table.sql` file with the appropriate table structure.

3. Create the table in Supabase:
   ```
   node createTableInSupabase.js path/to/your/file.dbf
   ```
   This will execute the SQL in Supabase to create the table.

4. Import the data:
   ```
   node manualImport.js --file=path/to/your/file.dbf --table=your_table_name
   ```

### Table Structure

The generated table will include:
- All fields from your DBF file with appropriate data types
- A primary key `id` column
- `imported_at` timestamp column to track when records were imported
- `import_batch_id` UUID column to group records from the same import batch
- Appropriate indexes for common query patterns
- Row-level security policies for authenticated users
