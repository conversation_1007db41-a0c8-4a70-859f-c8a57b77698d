// catchupCheckRproReadiness.js - Catch-up script to check all RPRO records for readiness
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

/**
 * Check a single RPRO record for readiness issues
 * @param {Object} record - RPRO record
 * @returns {Object} - Result with issues and todo message
 */
function checkRproReadiness(record) {
  const issues = [];

  // Issue 1: In stock items (ivqtylaw > 0) must have a bin section (ivaux3 not null)
  const quantity = parseFloat(record.ivqtylaw) || 0;
  const binSection = record.ivaux3;

  if (quantity > 0 && (!binSection || binSection.trim() === '')) {
    issues.push(`In stock item (qty: ${quantity}) missing bin section (ivaux3)`);
  }

  // Determine the todo message
  let todoMessage;
  if (issues.length === 0) {
    todoMessage = `No Issues Found on ${new Date().toISOString()}`;
  } else {
    todoMessage = issues.join('; ');
  }

  return {
    issues,
    todoMessage,
    ready: issues.length === 0
  };
}

/**
 * Process RPRO records in batches
 * @param {Array} records - Array of RPRO records
 * @returns {Object} - Processing results
 */
async function processBatch(records) {
  console.log(`\n📋 Processing batch of ${records.length} RPRO records...`);
  
  const results = {
    processed: 0,
    updated: 0,
    errors: 0,
    ready: 0,
    notReady: 0
  };

  for (const record of records) {
    try {
      const check = checkRproReadiness(record);
      
      // Update the record with the todo message
      const { error: updateError } = await supabase
        .from('imported_table_rpro')
        .update({ todo: check.todoMessage })
        .eq('id', record.id);

      if (updateError) {
        console.error(`❌ Error updating RPRO record ${record.id} (ivno: ${record.ivno}): ${updateError.message}`);
        results.errors++;
      } else {
        results.updated++;
        if (check.ready) {
          results.ready++;
        } else {
          results.notReady++;
          console.log(`⚠️  RPRO ${record.ivno}: ${check.todoMessage}`);
        }
      }
      
      results.processed++;
      
      // Progress indicator
      if (results.processed % 100 === 0) {
        console.log(`   Processed ${results.processed}/${records.length} records...`);
      }
      
    } catch (err) {
      console.error(`❌ Exception processing RPRO record ${record.id} (ivno: ${record.ivno}): ${err.message}`);
      results.errors++;
      results.processed++;
    }
  }

  return results;
}

/**
 * Main function to run the catch-up script
 */
async function main() {
  console.log('🚀 Starting RPRO Readiness Catch-up Script');
  console.log('==========================================');

  try {
    // First, ensure the todo column exists
    console.log('\n📋 Checking if todo column exists...');
    
    const { data: columns, error: columnError } = await supabase.rpc('exec_sql', {
      sql_query: `
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'imported_table_rpro' 
        AND column_name = 'todo'
      `
    });

    if (columnError) {
      console.error('❌ Error checking for todo column:', columnError.message);
      process.exit(1);
    }

    if (!columns || columns.length === 0) {
      console.log('⚠️  Todo column does not exist. Adding it now...');
      
      const { error: addColumnError } = await supabase.rpc('exec_sql', {
        sql_query: `
          ALTER TABLE public.imported_table_rpro 
          ADD COLUMN todo TEXT;
          
          COMMENT ON COLUMN public.imported_table_rpro.todo IS 'Readiness check results and todo items for RPRO records';
          
          CREATE INDEX IF NOT EXISTS idx_imported_table_rpro_todo 
          ON public.imported_table_rpro(todo);
        `
      });

      if (addColumnError) {
        console.error('❌ Error adding todo column:', addColumnError.message);
        process.exit(1);
      }
      
      console.log('✅ Todo column added successfully');
    } else {
      console.log('✅ Todo column already exists');
    }

    // Get total count of RPRO records
    console.log('\n📊 Getting total count of RPRO records...');
    const { count: totalCount, error: countError } = await supabase
      .from('imported_table_rpro')
      .select('id', { count: 'exact', head: true });

    if (countError) {
      console.error('❌ Error getting total count:', countError.message);
      process.exit(1);
    }

    console.log(`📋 Found ${totalCount} RPRO records to process`);

    if (totalCount === 0) {
      console.log('✅ No RPRO records found. Nothing to process.');
      return;
    }

    // Process records in batches
    const batchSize = 500;
    let offset = 0;
    let totalResults = {
      processed: 0,
      updated: 0,
      errors: 0,
      ready: 0,
      notReady: 0
    };

    while (offset < totalCount) {
      console.log(`\n📦 Fetching batch ${Math.floor(offset / batchSize) + 1} (records ${offset + 1}-${Math.min(offset + batchSize, totalCount)})...`);
      
      const { data: records, error: fetchError } = await supabase
        .from('imported_table_rpro')
        .select('id, ivno, ivqtylaw, ivaux3')
        .range(offset, offset + batchSize - 1)
        .order('id');

      if (fetchError) {
        console.error('❌ Error fetching records:', fetchError.message);
        break;
      }

      if (!records || records.length === 0) {
        console.log('✅ No more records to process');
        break;
      }

      const batchResults = await processBatch(records);
      
      // Accumulate results
      totalResults.processed += batchResults.processed;
      totalResults.updated += batchResults.updated;
      totalResults.errors += batchResults.errors;
      totalResults.ready += batchResults.ready;
      totalResults.notReady += batchResults.notReady;

      console.log(`✅ Batch completed: ${batchResults.updated} updated, ${batchResults.errors} errors`);

      offset += batchSize;
    }

    // Final summary
    console.log('\n🎉 RPRO Readiness Catch-up Script Completed');
    console.log('==========================================');
    console.log(`📊 Total Records Processed: ${totalResults.processed}`);
    console.log(`✅ Successfully Updated: ${totalResults.updated}`);
    console.log(`❌ Errors: ${totalResults.errors}`);
    console.log(`🟢 Ready Records: ${totalResults.ready}`);
    console.log(`🟡 Not Ready Records: ${totalResults.notReady}`);
    
    if (totalResults.errors > 0) {
      console.log(`\n⚠️  ${totalResults.errors} records had errors during processing`);
    }

  } catch (err) {
    console.error('❌ Fatal error in catch-up script:', err.message);
    console.error(err.stack);
    process.exit(1);
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(err => {
    console.error('❌ Unhandled error:', err.message);
    console.error(err.stack);
    process.exit(1);
  });
}
