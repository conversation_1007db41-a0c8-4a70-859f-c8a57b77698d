// catchupCheckRproReadiness.js - Catch-up script to check all RPRO records for readiness
import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

/**
 * Check a single RPRO record for readiness issues
 * @param {Object} record - RPRO record
 * @returns {Object} - Result with issues and todo message
 */
function checkRproReadiness(record) {
  const issues = [];
  const quantity = parseFloat(record.ivqtylaw) || 0;
  const isInStock = quantity > 0;

  // Issue 1: In stock items (ivqtylaw > 0) must have a bin section (ivaux3 not null)
  const binSection = record.ivaux3;
  if (isInStock && (!binSection || binSection.trim() === '')) {
    issues.push(`In stock item (qty: ${quantity}) missing bin section (ivaux3)`);
  }

  // Issue 2: Image checks
  const imageField = record.ivaux2;
  const expectedImage = '9 White Square Big';

  if (isInStock) {
    // In stock products should have ivaux2 = '9 White Square Big'
    if (imageField !== expectedImage) {
      issues.push('Ken - In stock item needs image.');
    }
  } else {
    // Out of stock products should also have image but different message
    if (imageField !== expectedImage) {
      issues.push('No image but out of stock.');
    }
  }

  // Issue 3: Pricing validation rules
  const listPrice = parseFloat(record.ivprcbtlis) || 0;
  const regularPrice = parseFloat(record.ivprcbt_dollar) || 0;
  const salePrice = parseFloat(record.ivprcbtsal) || 0;
  const livePrice = parseFloat(record.ivprcbtliv) || 0;
  const wholesale1Price = parseFloat(record.ivprcws_1) || 0;
  const wholesale2Price = parseFloat(record.ivprcws_2) || 0;
  const carryingCost = parseFloat(record.ivavgcd) || 0;
  const msrp = parseFloat(record.ivprcmsrp) || 0;
  const map = parseFloat(record.ivprcmap) || 0;

  const priceIssues = [];

  // If List Price is not 0, then it must be higher than or equal to the Regular Price
  if (listPrice !== 0 && listPrice < regularPrice) {
    priceIssues.push('List Price must be >= Regular Price');
  }

  // Regular Price must not be 0
  if (regularPrice === 0) {
    priceIssues.push('Regular Price cannot be 0');
  }

  // If Sale Price is not 0, then it must be less than Regular Price
  if (salePrice !== 0 && salePrice >= regularPrice) {
    priceIssues.push('Sale Price must be < Regular Price');
  }

  // Live Price can not be 0
  if (livePrice === 0) {
    priceIssues.push('Live Price cannot be 0');
  }

  // Live Price must be greater than Wholesale 1 Price
  if (livePrice <= wholesale1Price) {
    priceIssues.push('Live Price must be > Wholesale 1 Price');
  }

  // If Sale Price is 0, then Live Price must = Regular Price
  if (salePrice === 0 && livePrice !== regularPrice) {
    priceIssues.push('When Sale Price is 0, Live Price must = Regular Price');
  }

  // Wholesale 2 Price must be greater than Carrying Cost
  if (wholesale2Price <= carryingCost) {
    priceIssues.push('Wholesale 2 Price must be > Carrying Cost');
  }

  // Wholesale 2 Price must be .9* Wholesale 1 Price
  const expectedWholesale2 = wholesale1Price * 0.9;
  if (Math.abs(wholesale2Price - expectedWholesale2) > 0.01) { // Allow small floating point differences
    priceIssues.push('Wholesale 2 Price must be 90% of Wholesale 1 Price');
  }

  // If MSRP is not 0, then MAP must be <= to MSRP
  if (msrp !== 0 && map > msrp) {
    priceIssues.push('MAP must be <= MSRP');
  }

  // If MAP is not 0, then Live Price must not be < MAP
  if (map !== 0 && livePrice < map) {
    priceIssues.push('Live Price must not be < MAP');
  }

  // Add pricing issues to main issues list with appropriate prefix
  priceIssues.forEach(priceIssue => {
    if (isInStock) {
      issues.push(`Ken - ${priceIssue}`);
    } else {
      issues.push(priceIssue);
    }
  });

  // Determine the todo message
  let todoMessage;
  if (issues.length === 0) {
    todoMessage = `No Issues Found on ${new Date().toISOString()}`;
  } else {
    todoMessage = issues.join('; ');
  }

  return {
    issues,
    todoMessage,
    ready: issues.length === 0
  };
}

/**
 * Process RPRO records in batches
 * @param {Array} records - Array of RPRO records
 * @returns {Object} - Processing results
 */
async function processBatch(records) {
  console.log(`\n📋 Processing batch of ${records.length} RPRO records...`);
  
  const results = {
    processed: 0,
    updated: 0,
    errors: 0,
    ready: 0,
    notReady: 0
  };

  for (const record of records) {
    try {
      const check = checkRproReadiness(record);
      
      // Update the record with the todo message
      const { error: updateError } = await supabase
        .from('imported_table_rpro')
        .update({ todo: check.todoMessage })
        .eq('id', record.id);

      if (updateError) {
        console.error(`❌ Error updating RPRO record ${record.id} (ivno: ${record.ivno}): ${updateError.message}`);
        results.errors++;
      } else {
        results.updated++;
        if (check.ready) {
          results.ready++;
        } else {
          results.notReady++;
          console.log(`⚠️  RPRO ${record.ivno}: ${check.todoMessage}`);
        }
      }
      
      results.processed++;
      
      // Progress indicator
      if (results.processed % 100 === 0) {
        console.log(`   Processed ${results.processed}/${records.length} records...`);
      }
      
    } catch (err) {
      console.error(`❌ Exception processing RPRO record ${record.id} (ivno: ${record.ivno}): ${err.message}`);
      results.errors++;
      results.processed++;
    }
  }

  return results;
}

/**
 * Main function to run the catch-up script
 */
async function main() {
  console.log('🚀 Starting RPRO Readiness Catch-up Script');
  console.log('==========================================');

  try {
    // Check if we can access the table and todo field
    console.log('\n📋 Checking table access...');

    try {
      const { data: testData, error: testError } = await supabase
        .from('imported_table_rpro')
        .select('id, todo')
        .limit(1);

      if (testError) {
        console.error('❌ Error accessing table:', testError.message);
        process.exit(1);
      }

      console.log('✅ Table and todo column accessible');
    } catch (err) {
      console.error('❌ Exception accessing table:', err.message);
      process.exit(1);
    }

    // Get total count of RPRO records
    console.log('\n📊 Getting total count of RPRO records...');
    const { count: totalCount, error: countError } = await supabase
      .from('imported_table_rpro')
      .select('id', { count: 'exact', head: true });

    if (countError) {
      console.error('❌ Error getting total count:', countError.message);
      process.exit(1);
    }

    console.log(`📋 Found ${totalCount} RPRO records to process`);

    if (totalCount === 0) {
      console.log('✅ No RPRO records found. Nothing to process.');
      return;
    }

    // Process records in batches
    const batchSize = 500;
    let offset = 0;
    let totalResults = {
      processed: 0,
      updated: 0,
      errors: 0,
      ready: 0,
      notReady: 0
    };

    while (offset < totalCount) {
      console.log(`\n📦 Fetching batch ${Math.floor(offset / batchSize) + 1} (records ${offset + 1}-${Math.min(offset + batchSize, totalCount)})...`);
      
      const { data: records, error: fetchError } = await supabase
        .from('imported_table_rpro')
        .select(`
          id, ivno, ivqtylaw, ivaux3, ivaux2,
          ivprcbtlis, ivprcbt_dollar, ivprcbtsal, ivprcbtliv,
          ivprcws_1, ivprcws_2, ivavgcd, ivprcmsrp, ivprcmap
        `)
        .range(offset, offset + batchSize - 1)
        .order('id');

      if (fetchError) {
        console.error('❌ Error fetching records:', fetchError.message);
        break;
      }

      if (!records || records.length === 0) {
        console.log('✅ No more records to process');
        break;
      }

      const batchResults = await processBatch(records);
      
      // Accumulate results
      totalResults.processed += batchResults.processed;
      totalResults.updated += batchResults.updated;
      totalResults.errors += batchResults.errors;
      totalResults.ready += batchResults.ready;
      totalResults.notReady += batchResults.notReady;

      console.log(`✅ Batch completed: ${batchResults.updated} updated, ${batchResults.errors} errors`);

      offset += batchSize;
    }

    // Final summary
    console.log('\n🎉 RPRO Readiness Catch-up Script Completed');
    console.log('==========================================');
    console.log(`📊 Total Records Processed: ${totalResults.processed}`);
    console.log(`✅ Successfully Updated: ${totalResults.updated}`);
    console.log(`❌ Errors: ${totalResults.errors}`);
    console.log(`🟢 Ready Records: ${totalResults.ready}`);
    console.log(`🟡 Not Ready Records: ${totalResults.notReady}`);
    
    if (totalResults.errors > 0) {
      console.log(`\n⚠️  ${totalResults.errors} records had errors during processing`);
    }

  } catch (err) {
    console.error('❌ Fatal error in catch-up script:', err.message);
    console.error(err.stack);
    process.exit(1);
  }
}

// Run the script if called directly
console.log('Script starting...');
main().catch(err => {
  console.error('❌ Unhandled error:', err.message);
  console.error(err.stack);
  process.exit(1);
});
