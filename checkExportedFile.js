import ExcelJS from 'exceljs';
import path from 'path';

async function checkExportedFile() {
  try {
    console.log('🔍 Checking exported Excel file for row 198...\n');
    
    const filePath = path.join(process.cwd(), 'data', 'external data', 'test_row198_fix.xlsx');
    console.log(`📁 Reading file: ${filePath}`);
    
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(filePath);
    const worksheet = workbook.getWorksheet(1);
    
    console.log('📊 Checking row 198 for order quantities:\n');
    
    // Check specific cells in row 198
    const cellsToCheck = ['Q198', 'R198', 'P198', 'S198', 'N198', 'O198'];
    
    cellsToCheck.forEach(cellAddress => {
      const cell = worksheet.getCell(cellAddress);
      const value = cell.value;
      
      if (value && value !== 0) {
        console.log(`✅ Cell ${cellAddress}: ${value}`);
      } else {
        console.log(`⚪ Cell ${cellAddress}: ${value || 'empty'}`);
      }
    });
    
    console.log('\n🔍 Scanning entire row 198 for any non-zero values:');
    const row198 = worksheet.getRow(198);
    let foundValues = [];
    
    row198.eachCell((cell, colNumber) => {
      if (cell.value && cell.value !== 0 && typeof cell.value === 'number') {
        const columnLetter = String.fromCharCode(64 + colNumber); // Convert to letter
        foundValues.push(`${columnLetter}198: ${cell.value}`);
      }
    });
    
    if (foundValues.length > 0) {
      console.log('📍 Found order quantities in row 198:');
      foundValues.forEach(value => console.log(`   ${value}`));
    } else {
      console.log('❌ No order quantities found in row 198');
    }
    
    // Check total records exported
    console.log('\n📊 Scanning entire sheet for order quantities:');
    let totalOrderQuantities = 0;
    let cellsWithOrders = [];
    
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell, colNumber) => {
        if (cell.value && typeof cell.value === 'number' && cell.value > 0 && rowNumber > 20) { // Skip header rows
          const columnLetter = String.fromCharCode(64 + colNumber);
          totalOrderQuantities += cell.value;
          cellsWithOrders.push(`${columnLetter}${rowNumber}: ${cell.value}`);
        }
      });
    });
    
    console.log(`📦 Total order quantities found: ${totalOrderQuantities}`);
    console.log(`📍 Total cells with orders: ${cellsWithOrders.length}`);
    
    if (cellsWithOrders.length <= 20) {
      console.log('\n📋 All cells with order quantities:');
      cellsWithOrders.forEach(cell => console.log(`   ${cell}`));
    } else {
      console.log('\n📋 First 10 cells with order quantities:');
      cellsWithOrders.slice(0, 10).forEach(cell => console.log(`   ${cell}`));
      console.log(`   ... and ${cellsWithOrders.length - 10} more`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkExportedFile().catch(console.error);
