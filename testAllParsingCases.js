import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testAllParsingCases() {
  try {
    console.log('🧪 Testing all parsing cases to verify fixes...\n');
    
    // Test cases to verify
    const testCases = [
      {
        name: 'McBeth NEW - Hades (lines 269-279)',
        query: { raw_line_type: 'McB<PERSON>', raw_model: 'NEW - Hades' },
        expected: { plastic_name: 'ESP', mold_name: 'Hades', stamp_name: 'Dye Line Blank Top Bottom' }
      },
      {
        name: '<PERSON>c<PERSON><PERSON> Hard Luna (line 162)',
        query: { raw_line_type: 'McB<PERSON>', raw_model: 'Hard Luna' },
        expected: { plastic_name: 'Putter Line Hard', mold_name: 'Luna', stamp_name: 'PM Logo Stock Stamp' }
      },
      {
        name: '<PERSON><PERSON><PERSON><PERSON> Hard Kratos (line 163)',
        query: { raw_line_type: 'McBeth', raw_model: 'Hard Kratos' },
        expected: { plastic_name: 'Putter Line Hard', mold_name: 'Kratos', stamp_name: 'PM Logo Stock Stamp' }
      },
      {
        name: 'Pierce Hard Fierce (line 164)',
        query: { raw_line_type: 'Pierce', raw_model: 'Hard Fierce' },
        expected: { plastic_name: 'Putter Line Hard', mold_name: 'Fierce', stamp_name: 'PP Logo Stock Stamp' }
      },
      {
        name: 'Pierce Fierce (line 323)',
        query: { raw_line_type: 'Pierce', raw_model: 'Fierce' },
        expected: { plastic_name: 'Swirl', mold_name: 'Fierce', stamp_name: 'PP Logo Stock Stamp' }
      },
      {
        name: 'Titanium Zone GT (line 398)',
        query: { raw_line_type: 'Titanium', raw_model: 'Zone GT' },
        expected: { plastic_name: 'Ti Blend Titanium', mold_name: 'Zone GT', stamp_name: 'Stock' }
      },
      {
        name: 'McBeth Big Z Luna',
        query: { raw_line_type: 'McBeth', raw_model: 'Big Z Luna' },
        expected: { plastic_name: 'Big Z Collection', mold_name: 'Luna', stamp_name: 'Big Z Stock Stamp with Inside Rim Embossed PM Paul McBeth' }
      },
      {
        name: 'McBeth Big Z Hades',
        query: { raw_line_type: 'McBeth', raw_model: 'Big Z Hades' },
        expected: { plastic_name: 'Big Z Collection', mold_name: 'Hades', stamp_name: 'Big Z Stock Stamp with Inside Rim Embossed PM Paul McBeth' }
      },
      {
        name: 'Hard Challenger OS (line 369)',
        query: { raw_line_type: 'Hard', raw_model: 'Challenger OS' },
        expected: { plastic_name: 'Putter Line Hard', mold_name: 'Challenger OS', stamp_name: 'Stock' }
      },
      {
        name: 'Hard Challenger SS',
        query: { raw_line_type: 'Hard', raw_model: 'Challenger SS' },
        expected: { plastic_name: 'Putter Line Hard', mold_name: 'Challenger SS', stamp_name: 'Stock' }
      },
      {
        name: 'Soft Ringer GT (line 383)',
        query: { raw_line_type: 'Soft', raw_model: 'Ringer GT' },
        expected: { plastic_name: 'Putter Line Soft', mold_name: 'Ringer GT', stamp_name: 'Stock' }
      }
    ];
    
    for (const testCase of testCases) {
      console.log(`🔍 Testing ${testCase.name}:`);
      
      const { data: products, error } = await supabase
        .from('it_discraft_order_sheet_lines')
        .select('plastic_name, mold_name, stamp_name, raw_line_type, raw_model, vendor_description')
        .eq('raw_line_type', testCase.query.raw_line_type)
        .ilike('raw_model', `%${testCase.query.raw_model}%`)
        .limit(3);
      
      if (error) {
        console.error(`   ❌ Error: ${error.message}`);
        continue;
      }
      
      if (products.length === 0) {
        console.log(`   ⚪ No products found for "${testCase.query.raw_line_type}" | "${testCase.query.raw_model}"`);
        continue;
      }
      
      products.forEach((product, index) => {
        console.log(`   ${index + 1}. Raw: "${product.raw_line_type}" | "${product.raw_model}"`);
        console.log(`      Parsed: ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        console.log(`      Expected: ${testCase.expected.plastic_name} | ${testCase.expected.mold_name} | ${testCase.expected.stamp_name}`);
        
        const isCorrect = product.plastic_name === testCase.expected.plastic_name && 
                         product.mold_name === testCase.expected.mold_name && 
                         product.stamp_name === testCase.expected.stamp_name;
        
        console.log(`      ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
        
        if (product.vendor_description) {
          console.log(`      Vendor Description: "${product.vendor_description}"`);
        }
      });
      console.log('');
    }
    
    // Test SuperColor products
    console.log('🎨 Testing SuperColor products:');
    const { data: superColorProducts, error: scError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('plastic_name, mold_name, stamp_name, vendor_description')
      .ilike('raw_line_type', '%SuperColor%')
      .limit(5);
    
    if (scError) {
      console.error(`   ❌ Error: ${scError.message}`);
    } else if (superColorProducts.length > 0) {
      superColorProducts.forEach((product, index) => {
        console.log(`   ${index + 1}. ${product.plastic_name} | ${product.mold_name} | ${product.stamp_name}`);
        if (product.vendor_description) {
          console.log(`      Description: "${product.vendor_description}"`);
        }
      });
    } else {
      console.log('   ⚪ No SuperColor products found');
    }
    
    // Test weight range parsing (150 → 150-159)
    console.log('\n⚖️ Testing weight range parsing (150 → 150-159):');
    const { data: weight150Products, error: weightError } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('min_weight, max_weight, raw_weight_range')
      .eq('min_weight', 150)
      .eq('max_weight', 159)
      .limit(3);
    
    if (weightError) {
      console.error(`   ❌ Error: ${weightError.message}`);
    } else if (weight150Products.length > 0) {
      console.log(`   ✅ Found ${weight150Products.length} products with 150-159g weight range:`);
      weight150Products.forEach((product, index) => {
        console.log(`   ${index + 1}. Raw: "${product.raw_weight_range}" → Parsed: ${product.min_weight}-${product.max_weight}g`);
      });
    } else {
      console.log('   ⚪ No 150-159g weight range products found');
    }
    
    console.log('\n🎉 All parsing tests completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testAllParsingCases().catch(console.error);
