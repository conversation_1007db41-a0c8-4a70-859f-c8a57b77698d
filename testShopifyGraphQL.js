import fetch from 'node-fetch';
import dotenv from 'dotenv';
dotenv.config();

const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;

async function testGraphQLSearch() {
  try {
    // First, let's try to get any product variants to see the format
    const query = `
      query getAnyVariants {
        productVariants(first: 5) {
          edges {
            node {
              id
              sku
              product {
                id
                title
              }
            }
          }
        }
      }
    `;

    const response = await fetch(shopifyEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken,
      },
      body: JSON.stringify({ query }),
    });

    const result = await response.json();
    
    if (result.errors) {
      console.error('GraphQL errors:', result.errors);
      return;
    }
    
    console.log('Sample variants from Shopify:');
    result.data.productVariants.edges.forEach((edge, index) => {
      console.log(`${index + 1}. SKU: ${edge.node.sku}, Product: ${edge.node.product.title}`);
    });
    
    // Now try searching for our specific SKU
    console.log('\nSearching for D424221...');
    const searchQuery = `
      query getVariantBySku($query: String!) {
        productVariants(first: 1, query: $query) {
          edges {
            node {
              id
              sku
              product {
                id
                title
                tags
              }
            }
          }
        }
      }
    `;

    const searchResponse = await fetch(shopifyEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken,
      },
      body: JSON.stringify({ 
        query: searchQuery, 
        variables: { query: 'sku:D424221' }
      }),
    });

    const searchResult = await searchResponse.json();
    
    if (searchResult.errors) {
      console.error('Search GraphQL errors:', searchResult.errors);
      return;
    }
    
    console.log('Search result for D424221:', JSON.stringify(searchResult.data, null, 2));
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

testGraphQLSearch();
