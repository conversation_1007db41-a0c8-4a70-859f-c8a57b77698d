import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function comprehensiveFundraiserTest() {
    try {
        console.log('🔍 Comprehensive fundraiser test - checking everything...\n');
        
        // 1. Check database state
        console.log('1. Checking current database state...');
        
        // Check all records in fundraiser rows
        const { data: allFundraiserRows, error: allError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, mold_name, plastic_name, calculated_mps_id, is_orderable, excel_mapping_key')
            .gte('excel_row_hint', 22)
            .lte('excel_row_hint', 30)
            .order('excel_row_hint, excel_column');

        if (allError) {
            console.error('❌ Error querying all fundraiser rows:', allError);
            return;
        }

        console.log(`✅ Found ${allFundraiserRows.length} total records in rows 22-30:`);
        allFundraiserRows.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.mold_name} | ${record.plastic_name}`);
            console.log(`      Orderable: ${record.is_orderable}, MPS: ${record.calculated_mps_id || 'NO_MPS'}`);
        });

        // 2. Check what daily automation will export
        console.log('\n2. Checking what daily automation will export...');
        
        const { data: exportData, error: exportError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, calculated_mps_id, excel_mapping_key')
            .eq('is_orderable', true)
            .not('excel_mapping_key', 'is', null)
            .gte('excel_row_hint', 22)
            .lte('excel_row_hint', 30);

        if (exportError) {
            console.error('❌ Error querying export data:', exportError);
            return;
        }

        console.log(`✅ Daily automation will export ${exportData.length} records from fundraiser section:`);
        exportData.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: MPS ${record.calculated_mps_id || 'NO_MPS'}`);
        });

        // 3. Verify the structure matches expectations
        console.log('\n3. Verifying structure matches expectations...');
        
        const expectations = [
            { row: 22, shouldHaveRecords: false, description: 'Header row - should be empty' },
            { row: 24, shouldHaveRecords: false, description: 'Order qty header - should be empty' },
            { row: 25, shouldHaveRecords: true, expectedColumn: 'B', description: 'Thrasher product - should have record in column B' },
            { row: 27, shouldHaveRecords: false, description: 'Order qty header - should be empty' },
            { row: 28, shouldHaveRecords: true, expectedColumn: 'B', description: 'Buzzz product - should have record in column B' }
        ];

        let allCorrect = true;

        for (const expectation of expectations) {
            const rowRecords = allFundraiserRows.filter(r => r.excel_row_hint === expectation.row);
            
            console.log(`\n   Row ${expectation.row}: ${expectation.description}`);
            
            if (expectation.shouldHaveRecords) {
                if (rowRecords.length === 0) {
                    console.log(`   ❌ FAIL: Expected records but found none`);
                    allCorrect = false;
                } else {
                    const correctColumnRecord = rowRecords.find(r => r.excel_column === expectation.expectedColumn);
                    if (!correctColumnRecord) {
                        console.log(`   ❌ FAIL: Expected record in column ${expectation.expectedColumn} but found in: ${rowRecords.map(r => r.excel_column).join(', ')}`);
                        allCorrect = false;
                    } else {
                        console.log(`   ✅ PASS: Found record in column ${expectation.expectedColumn} (${correctColumnRecord.mold_name})`);
                    }
                }
            } else {
                if (rowRecords.length > 0) {
                    console.log(`   ❌ FAIL: Expected no records but found ${rowRecords.length}`);
                    allCorrect = false;
                } else {
                    console.log(`   ✅ PASS: No records found (correct)`);
                }
            }
        }

        // 4. Final summary
        console.log('\n4. Final summary...');
        
        if (allCorrect) {
            console.log('🎉 ALL TESTS PASSED! Fundraiser section is correctly configured.');
            console.log('\n📋 Your next automated order will show:');
            console.log('   • Row 22: EMPTY (header)');
            console.log('   • Row 24: EMPTY (header)');
            console.log('   • Row 25, Column B: 19704 or order quantity (Thrasher)');
            console.log('   • Row 27: EMPTY (header)');
            console.log('   • Row 28, Column B: NO_MPS or order quantity (Buzzz)');
            console.log('\n✅ The fundraiser section is now working correctly!');
        } else {
            console.log('❌ SOME TESTS FAILED! There are still issues with the fundraiser section.');
            console.log('   Check the failures above and fix them.');
        }
        
    } catch (error) {
        console.error('❌ Comprehensive test failed:', error.message);
    }
}

comprehensiveFundraiserTest().catch(console.error);
