-- Function to enqueue a task for plastic MSRP price changes
CREATE OR REPLACE FUNCTION fn_enqueue_plastic_msrp_price_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Only enqueue a task if val_msrp has changed
    IF OLD.val_msrp IS DISTINCT FROM NEW.val_msrp THEN
        
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'plastic_msrp_price_change',
            jsonb_build_object(
                'id', NEW.id,
                'old_msrp_price', OLD.val_msrp,
                'new_msrp_price', NEW.val_msrp,
                'plastic_name', NEW.plastic
            ),
            'pending',
            NOW(),
            NOW()
        );
        
        -- Log the change
        RAISE NOTICE 'Enqueued plastic_msrp_price_change task for plastic ID % (%) - MSRP changed from % to %', 
            NEW.id, NEW.plastic, OLD.val_msrp, NEW.val_msrp;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trg_enqueue_plastic_msrp_price_change ON t_plastics;

CREATE TRIGGER trg_enqueue_plastic_msrp_price_change
AFTER UPDATE OF val_msrp ON t_plastics
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_plastic_msrp_price_change();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger trg_enqueue_plastic_msrp_price_change has been created on t_plastics.val_msrp updates.';
END $$;
