# Inactive Records Reset Scripts Summary

## Overview

Two new scripts have been created to handle resetting inactive records for both FBM (Fulfilled by Merchant) and FBA (Fulfilled by Amazon) products that are discontinued and have zero inventory.

## Scripts Created

### 1. Reset FBM Inactive Records
- **File**: `resetFbmInactiveRecords.js`
- **View**: `v_sdasins_fbm_inv0_mps_inactive`
- **API Endpoint**: `/api/reset-fbm-inactive-records`
- **Button**: 🔄 Reset FBM Inactive Records (Warning style)

**Changes Applied:**
- `fbm_uploaded_at` = NULL
- `min_weight` = 1
- `max_weight` = 2

**Test Results:** ✅ Successfully processed 827 records

### 2. Reset FBA Inactive Records
- **File**: `resetFbaInactiveRecords.js`
- **View**: `v_sdasins_fba_inv0_mps_inactive`
- **API Endpoint**: `/api/reset-fba-inactive-records`
- **Button**: 🚫 Reset FBA Inactive Records (Danger style)

**Changes Applied:**
- `fba` = "N"
- `fbafnsku` = NULL
- `fba_uploaded_at` = NULL
- `min_weight` = 1
- `max_weight` = 2
- `notes` = Prepended with discontinuation message and timestamp

**Test Results:** ✅ Successfully processed 125 records

## Database Views Used

### FBM View Criteria
```sql
where
  s.fbm_uploaded_at is not null
  and inv.available_quantity = 0
  and mps.active = false
```

### FBA View Criteria
```sql
where
  s.fba_uploaded_at is not null
  and far.quantity_available = 0
  and inv.available_quantity = 0
  and mps.active = false
```

## Admin Interface Integration

Both scripts are integrated into the **Amazon FBA** tab in `admin.html`:

1. **FBM Reset Card**: Focuses on resetting FBM records for re-processing
2. **FBA Reset Card**: Focuses on removing discontinued products from Amazon

Each card includes:
- Detailed description of what the script does
- Color-coded information boxes explaining use cases and expected results
- Warning messages for important considerations
- Buttons with appropriate styling (warning for FBM, danger for FBA)
- Comprehensive result display with success/error handling

## Key Features

### Safety
- ✅ Safe to run multiple times
- ✅ Only processes records matching view criteria
- ✅ Comprehensive error handling
- ✅ Detailed logging and feedback

### Functionality
- ✅ Can be run standalone or via admin interface
- ✅ Individual record processing for partial failure handling
- ✅ Structured API responses
- ✅ Preserves existing notes (FBA script)

### Integration
- ✅ Consistent with existing admin patterns
- ✅ Proper error handling and user feedback
- ✅ RESTful API endpoints
- ✅ Comprehensive documentation

## Usage Patterns

### FBM Reset
**Purpose**: Reset records for products that can potentially be re-processed through FBM workflow when they become available again.

**When to Use**: When you want to clear FBM upload status for products that are temporarily out of stock or inactive.

### FBA Reset
**Purpose**: Permanently remove discontinued products from Amazon FBA and mark them as such.

**When to Use**: When products are truly discontinued and should no longer be sold on Amazon.

## File Structure

```
├── resetFbmInactiveRecords.js          # FBM reset script
├── resetFbaInactiveRecords.js          # FBA reset script
├── adminServer.js                      # Updated with both API endpoints
├── admin.html                          # Updated with both cards/buttons
├── RESET_FBM_INACTIVE_RECORDS_README.md
├── RESET_FBA_INACTIVE_RECORDS_README.md
└── INACTIVE_RECORDS_RESET_SUMMARY.md   # This file
```

## Environment Requirements

Both scripts require:
- `SUPABASE_URL` - Supabase project URL
- `SUPABASE_KEY` - Supabase service role key

## Testing Results

- **FBM Script**: Processed 827 records successfully
- **FBA Script**: Processed 125 records successfully
- **Subsequent runs**: Both scripts correctly show 0 records (confirming successful processing)
- **No syntax errors**: All files pass validation
- **Integration**: Admin interface properly integrated

## Next Steps

The scripts are ready for production use. Users can:

1. **Run scripts manually** via command line for testing or batch processing
2. **Use admin interface** for convenient web-based execution
3. **Monitor results** through detailed logging and structured responses
4. **Re-run safely** as needed since operations are idempotent

Both scripts follow the established patterns in the codebase and provide comprehensive functionality for managing inactive inventory records.
