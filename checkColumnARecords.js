import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkColumnARecords() {
    try {
        console.log('🔍 Checking column A records...\n');
        
        // Check ALL records in column A
        const { data: columnARecords, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('*')
            .eq('excel_column', 'A')
            .order('excel_row_hint');

        if (error) {
            console.error('❌ Error querying column A records:', error);
            return;
        }

        console.log(`✅ Found ${columnARecords.length} records in column A:`);
        
        if (columnARecords.length === 0) {
            console.log('   No records found in column A');
        } else {
            columnARecords.forEach((record, index) => {
                console.log(`\n--- Column A Record ${index + 1} ---`);
                console.log(`Row: ${record.excel_row_hint}, Column: ${record.excel_column}`);
                console.log(`Orderable: ${record.is_orderable}`);
                console.log(`Mold: "${record.mold_name}"`);
                console.log(`Plastic: "${record.plastic_name}"`);
                console.log(`Stamp: "${record.stamp_name}"`);
                console.log(`MPS ID: ${record.calculated_mps_id || 'NULL'}`);
                console.log(`Raw Line: "${record.raw_line_type}"`);
                console.log(`Raw Model: "${record.raw_model}"`);
                console.log(`Mapping Key: "${record.excel_mapping_key}"`);
            });
        }

        // Check for any fundraiser records anywhere
        console.log('\n🔍 Checking for ANY fundraiser records...');
        const { data: fundraiserRecords, error: fundraiserError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, mold_name, plastic_name, raw_model')
            .or('raw_model.ilike.%Fundraiser%,plastic_name.ilike.%Fundraiser%,mold_name.ilike.%Fundraiser%')
            .order('excel_row_hint, excel_column');

        if (fundraiserError) {
            console.error('❌ Error querying fundraiser records:', fundraiserError);
            return;
        }

        console.log(`✅ Found ${fundraiserRecords.length} fundraiser-related records:`);
        fundraiserRecords.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.mold_name} | ${record.plastic_name}`);
            if (record.raw_model) {
                console.log(`      Raw Model: ${record.raw_model}`);
            }
        });

        console.log('\n🎯 Status:');
        if (columnARecords.length === 0) {
            console.log('   ❌ No records in column A - fundraiser records were not created or were deleted');
        }
        
        const fundraiserInColumnA = columnARecords.filter(r => 
            r.raw_model && r.raw_model.includes('Fundraiser - Ben Askren')
        );
        
        if (fundraiserInColumnA.length === 0) {
            console.log('   ❌ No fundraiser records in column A - need to create them');
        } else {
            console.log(`   ✅ Found ${fundraiserInColumnA.length} fundraiser records in column A`);
        }
        
    } catch (error) {
        console.error('❌ Check failed:', error.message);
    }
}

checkColumnARecords().catch(console.error);
