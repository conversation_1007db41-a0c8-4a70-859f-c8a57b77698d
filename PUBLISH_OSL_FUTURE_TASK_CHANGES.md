# Publish Product OSL Future Task Enhancement

## Overview
Modified the `publish_product_osl` task processing in `taskQueueWorker.js` to handle tasks scheduled more than 30 minutes in the future by enqueueing `check_if_disc_is_ready` tasks for all related discs.

## Changes Made

### File: `taskQueueWorker.js`
**Function:** `processPublishProductOslTask(task)`
**Lines:** 4838-4949

### New Logic Flow

1. **Future Task Detection**: 
   - Check if `task.scheduled_at` is more than 30 minutes from current time
   - Compare: `scheduledAt > (now + 30 minutes)`

2. **Idempotent Secondary Action**:
   - Check if `task.result.discs_ready_checked` is already true
   - If yes, skip secondary action and reset task to pending status
   - If no, proceed with disc ready checks

3. **Disc Ready Check Enqueueing**:
   - Find all discs with `order_sheet_line_id = oslId`
   - Enqueue `check_if_disc_is_ready` task for each disc
   - Schedule these tasks for `now()` (immediate processing)
   - Set `enqueued_by` to `publish_product_osl_future_check`

4. **Task Status Management**:
   - Temporarily set task to 'processing' during secondary action
   - Reset task to 'pending' with original `scheduled_at` time
   - Add result data: `discs_ready_checked: true` for idempotency
   - Include summary: discs found, tasks enqueued, errors

5. **Normal Processing**:
   - If task is ≤30 minutes from now, process normally as before
   - No changes to existing publish logic

## Task Status Flow

### Future Tasks (>30 minutes):
1. **First Processing**: `pending` → `processing` → `disc_checks_completed`
2. **Subsequent Worker Runs**: `disc_checks_completed` tasks are ignored (no cycling)
3. **When Time Arrives**: `disc_checks_completed` → `processing` → normal OSL publish

### Ready Tasks (≤30 minutes):
- Process normally: `pending` → `processing` → `completed`

## Result Data Structure

When a future task is processed, the result includes:
```json
{
  "message": "Disc ready checks completed: X tasks enqueued, Y errors. Waiting for scheduled execution time.",
  "discs_ready_checked": true,
  "discs_found": 5,
  "tasks_enqueued": 5,
  "errors": 0,
  "original_scheduled_at": "2025-06-05T12:58:00.000Z"
}
```

## Benefits

1. **Proactive Disc Checking**: Discs are checked for readiness well before OSL publish time
2. **Idempotent**: Multiple worker runs won't duplicate disc ready checks
3. **Non-Disruptive**: Future OSL tasks remain scheduled for their original time
4. **Traceable**: Clear logging and result tracking for debugging
5. **Error Handling**: Graceful handling of disc lookup and task creation errors

## Testing

Use `test_future_osl_task.js` to verify the logic:
- Creates a test task scheduled 1 hour in future
- Validates the 30-minute threshold logic
- Cleans up test data

## Monitoring

Look for these log messages:
- `Task X is scheduled more than 30 minutes in the future`
- `Found Y discs related to OSL Z`
- `Enqueued X check_if_disc_is_ready tasks for future OSL publish`
- `Task X already has discs ready checked, skipping secondary action`
