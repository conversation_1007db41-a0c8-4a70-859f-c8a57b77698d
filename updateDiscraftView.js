import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Initialize Supabase client with service role key for admin operations
const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNDQ3Mjg3NCwiZXhwIjoyMDUwMDQ4ODc0fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateDiscraftView() {
    try {
        console.log('🔄 Updating v_stats_by_osl_discraft view...\n');
        
        // Read the SQL file
        const sqlContent = fs.readFileSync('create_discraft_view.sql', 'utf8');
        
        console.log('📄 SQL content loaded:');
        console.log('   - Setting order = 0 when is_currently_available = false');
        console.log('   - Preserving existing calculation for available products\n');
        
        // Execute the SQL
        console.log('🚀 Executing SQL...');
        const { data, error } = await supabase.rpc('exec_sql', { 
            sql_query: sqlContent 
        });
        
        if (error) {
            console.error('❌ Error updating view:', error);
            
            // Try alternative approach using direct SQL execution
            console.log('🔄 Trying alternative approach...');
            const { data: altData, error: altError } = await supabase
                .from('information_schema.tables')
                .select('*')
                .limit(1);
                
            if (altError) {
                console.error('❌ Database connection error:', altError);
                return;
            }
            
            console.log('✅ Database connection working, but view update failed');
            console.log('💡 You may need to run this SQL manually in the Supabase SQL editor:');
            console.log('\n' + sqlContent);
            
        } else {
            console.log('✅ View updated successfully!');
            
            // Test the view
            console.log('\n🧪 Testing updated view...');
            const { data: testData, error: testError } = await supabase
                .from('v_stats_by_osl_discraft')
                .select('mold_name, is_currently_available, order')
                .limit(10);
                
            if (testError) {
                console.error('❌ Error testing view:', testError);
            } else {
                console.log('✅ View test successful!');
                console.log('\nSample results:');
                testData.forEach(row => {
                    console.log(`   ${row.mold_name}: available=${row.is_currently_available}, order=${row.order}`);
                });
            }
        }
        
        console.log('\n📊 Expected behavior:');
        console.log('   - Products with is_currently_available = false → order = 0');
        console.log('   - Products with is_currently_available = true → order = max(sales - stock, 0)');
        console.log('   - This prevents ordering "out of stock" items from vendor');
        
    } catch (err) {
        console.error('❌ Script error:', err);
        console.log('\n💡 Manual SQL to run in Supabase:');
        console.log('\n' + fs.readFileSync('create_discraft_view.sql', 'utf8'));
    }
}

updateDiscraftView();
