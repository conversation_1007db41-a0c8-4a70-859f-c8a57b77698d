-- Function to enqueue a task for generating OSL fields
CREATE OR REPLACE FUNCTION fn_queue_generate_osl_fields()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert a task into the task queue
    INSERT INTO t_task_queue (
        task_type,
        payload,
        status,
        scheduled_at,
        created_at
    ) VALUES (
        'generate_osl_fields',
        jsonb_build_object('id', NEW.id),
        'pending',
        NOW(),
        NOW()
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS trg_queue_generate_osl_fields ON t_order_sheet_lines;

CREATE TRIGGER trg_queue_generate_osl_fields
AFTER INSERT OR UPDATE OF
    min_weight,
    max_weight,
    color_id,
    mps_id
ON t_order_sheet_lines
FOR EACH ROW
EXECUTE FUNCTION fn_queue_generate_osl_fields();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Trigger trg_queue_generate_osl_fields has been created.';
END $$;
