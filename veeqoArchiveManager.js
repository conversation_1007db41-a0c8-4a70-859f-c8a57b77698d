import fetch from 'node-fetch';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const veeqoApiKey = process.env.VEEQO_API_KEY;
const CHANNEL_ID = 244185; // Your DZDiscs channel ID

if (!veeqoApiKey) {
  console.error('Error: VEEQO_API_KEY must be set in .env file');
  process.exit(1);
}

// Function to make Veeqo API request
async function makeVeeqoRequest(endpoint, method = 'GET', data = null) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': veeqoApiKey
      }
    };
    
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      options.body = JSON.stringify(data);
    }
    
    const response = await fetch(endpoint, options);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API Error (${response.status}): ${errorText}`);
      return null;
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Request error: ${error.message}`);
    return null;
  }
}

// Function to get archived (pulled) products
async function getArchivedProducts(maxPages = 10) {
  console.log('🔍 Fetching archived (pulled) products...\n');
  
  const archivedProducts = [];
  let page = 1;
  
  while (page <= maxPages) {
    console.log(`📄 Fetching page ${page}...`);
    
    const products = await makeVeeqoRequest(`https://api.veeqo.com/products?page=${page}&page_size=100`);
    
    if (!products || !Array.isArray(products) || products.length === 0) {
      console.log(`No more products found on page ${page}`);
      break;
    }
    
    // Filter for products that are pulled from all channels
    const pulledProducts = products.filter(product => {
      if (!product.channel_products || product.channel_products.length === 0) {
        return false;
      }
      
      // Check if all channel products are pulled
      return product.channel_products.every(cp => cp.status === 'pulled');
    });
    
    archivedProducts.push(...pulledProducts);
    console.log(`Found ${pulledProducts.length} archived products on page ${page}`);
    
    // If we got less than 100 products, we're done
    if (products.length < 100) {
      break;
    }
    
    page++;
  }
  
  console.log(`\n📊 Total archived products found: ${archivedProducts.length}\n`);
  return archivedProducts;
}

// Function to display archived products
function displayArchivedProducts(products, limit = 20) {
  console.log('📋 ARCHIVED PRODUCTS:');
  console.log('='.repeat(80));
  
  products.slice(0, limit).forEach((product, index) => {
    console.log(`${index + 1}. ID: ${product.id}`);
    console.log(`   Title: ${product.title}`);
    console.log(`   Created: ${new Date(product.created_at).toLocaleDateString()}`);
    console.log(`   Channels: ${product.channel_products.map(cp => `${cp.channel?.short_name || 'Unknown'}(${cp.status})`).join(', ')}`);
    
    // Show stock levels if available
    if (product.sellables && product.sellables.length > 0) {
      const totalStock = product.sellables.reduce((sum, sellable) => {
        return sum + (sellable.available_stock_level_at_all_warehouses || 0);
      }, 0);
      console.log(`   Stock: ${totalStock} units`);
    }
    
    console.log('');
  });
  
  if (products.length > limit) {
    console.log(`... and ${products.length - limit} more archived products\n`);
  }
}

// Function to unarchive a product (attempt to push it back to channels)
async function unarchiveProduct(productId) {
  console.log(`🔄 Attempting to unarchive product ${productId}...`);
  
  // Get product details
  const product = await makeVeeqoRequest(`https://api.veeqo.com/products/${productId}`);
  
  if (!product) {
    console.error(`❌ Failed to get product ${productId}`);
    return false;
  }
  
  console.log(`📋 Product: "${product.title}"`);
  
  // Check if product has pulled channel products
  if (!product.channel_products || product.channel_products.length === 0) {
    console.log(`⚠️  Product has no channel products`);
    return false;
  }
  
  const pulledChannels = product.channel_products.filter(cp => cp.status === 'pulled');
  
  if (pulledChannels.length === 0) {
    console.log(`✅ Product is not archived (no pulled channels)`);
    return true;
  }
  
  console.log(`📤 Found ${pulledChannels.length} pulled channel(s)`);
  
  // Note: Veeqo's API for updating channel product status might require specific endpoints
  // This is a common pattern, but you may need to adjust based on Veeqo's actual API
  
  let success = true;
  
  for (const channelProduct of pulledChannels) {
    try {
      console.log(`🔄 Attempting to push product to channel ${channelProduct.channel?.short_name || 'Unknown'}...`);
      
      // This endpoint might need adjustment based on Veeqo's actual API
      const updateEndpoint = `https://api.veeqo.com/channel_products/${channelProduct.id}`;
      const updateData = {
        channel_product: {
          status: 'active' // or 'live' - check Veeqo API docs for correct status
        }
      };
      
      const result = await makeVeeqoRequest(updateEndpoint, 'PUT', updateData);
      
      if (result) {
        console.log(`✅ Successfully updated channel product ${channelProduct.id}`);
      } else {
        console.log(`❌ Failed to update channel product ${channelProduct.id}`);
        success = false;
      }
      
    } catch (error) {
      console.error(`❌ Error updating channel product: ${error.message}`);
      success = false;
    }
  }
  
  return success;
}

// Function to save archived products to JSON file
function saveArchivedProducts(products) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `veeqo_archived_products_${timestamp}.json`;
  
  const exportData = products.map(product => ({
    id: product.id,
    title: product.title,
    created_at: product.created_at,
    updated_at: product.updated_at,
    requires_review: product.requires_review,
    channel_products: product.channel_products.map(cp => ({
      id: cp.id,
      channel_name: cp.channel?.short_name || 'Unknown',
      status: cp.status,
      remote_title: cp.remote_title,
      remote_id: cp.remote_id
    })),
    sellables: product.sellables ? product.sellables.map(s => ({
      id: s.id,
      sku_code: s.sku_code,
      title: s.title,
      price: s.price,
      available_stock: s.available_stock_level_at_all_warehouses || 0
    })) : [],
    total_stock: product.sellables ? product.sellables.reduce((sum, s) => 
      sum + (s.available_stock_level_at_all_warehouses || 0), 0) : 0
  }));
  
  fs.writeFileSync(filename, JSON.stringify(exportData, null, 2));
  console.log(`💾 Saved ${products.length} archived products to: ${filename}`);
  
  return filename;
}

// Main function with menu
async function main() {
  console.log('🗂️  VEEQO ARCHIVE MANAGER');
  console.log('='.repeat(50));
  
  try {
    // Get archived products
    const archivedProducts = await getArchivedProducts(5); // Limit to 5 pages for now
    
    if (archivedProducts.length === 0) {
      console.log('✅ No archived products found!');
      return;
    }
    
    // Display summary
    displayArchivedProducts(archivedProducts, 10);
    
    // Save to file
    const filename = saveArchivedProducts(archivedProducts);
    
    console.log('\n🛠️  AVAILABLE ACTIONS:');
    console.log('1. To unarchive a specific product, use: unarchiveProduct(PRODUCT_ID)');
    console.log('2. Check the saved JSON file for detailed product information');
    console.log('3. Products with stock > 0 might be good candidates for unarchiving');
    
    // Show products with stock
    const productsWithStock = archivedProducts.filter(p => {
      if (!p.sellables) return false;
      const totalStock = p.sellables.reduce((sum, s) => 
        sum + (s.available_stock_level_at_all_warehouses || 0), 0);
      return totalStock > 0;
    });
    
    if (productsWithStock.length > 0) {
      console.log(`\n📦 ${productsWithStock.length} archived products have stock available:`);
      productsWithStock.slice(0, 5).forEach(product => {
        const totalStock = product.sellables.reduce((sum, s) => 
          sum + (s.available_stock_level_at_all_warehouses || 0), 0);
        console.log(`  - ID: ${product.id}, Stock: ${totalStock}, Title: "${product.title}"`);
      });
      
      if (productsWithStock.length > 5) {
        console.log(`  ... and ${productsWithStock.length - 5} more with stock`);
      }
    }
    
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
  }
}

// Export functions for use in other scripts
export { getArchivedProducts, unarchiveProduct, displayArchivedProducts, saveArchivedProducts };

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
