// watcher.js
import chokidar from 'chokidar';
import { exec } from 'child_process';

// Folder to watch (using forward slashes for ease)
const folderPath = 'N:/Images/DG MPS/Uploaded to AWS';

// Glob pattern: matches files whose name starts with digits and ends with .jpg (case-insensitive)
const globPattern = `${folderPath}/[0-9]*.jpg`;

console.log(`Watching for new image files in: ${folderPath}`);
console.log(`Using glob pattern: ${globPattern}`);

// Set up the watcher using polling with a 3-minute interval (180000ms)
const watcher = chokidar.watch(globPattern, {
  persistent: true,
  ignoreInitial: true,
  usePolling: true,
  interval: 180000,       // Poll every 3 minutes
  binaryInterval: 180000  // For binary files
});

// When a new file is added, process it.
watcher.on('add', (path) => {
  console.log(`New file detected: ${path}`);
  
  // Extract the filename from the full path
  const fileName = path.split(/[/\\]/).pop();
  
  // Use a regular expression to ensure it is an integer filename ending in .jpg
  const match = fileName.match(/^(\d+)\.jpg$/i);
  if (match) {
    const mpsId = match[1]; // The integer part from the filename
    console.log(`Detected file with mps id: ${mpsId}`);
    
    // Trigger the publishing process by executing your publishCollectionMPS.js script
    const command = `node publishCollectionMPS.js --id=${mpsId}`;
    console.log(`Executing command: ${command}`);
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error executing command: ${error.message}`);
        return;
      }
      if (stderr) {
        console.error(`Stderr: ${stderr}`);
      }
      console.log(`Stdout: ${stdout}`);
    });
  } else {
    console.warn(`File ${fileName} does not match the expected pattern (integer.jpg); ignoring.`);
  }
});

// Log additional events for debugging.
watcher.on('error', (error) => {
  console.error(`Watcher error: ${error}`);
});

watcher.on('ready', () => {
  console.log('Initial scan complete. Now watching for new files...');
});
