// imageUtils.js - Utility functions for handling image files and directories
import fs from 'fs/promises';
import path from 'path';
import { constants } from 'fs';
import { readFile } from 'fs/promises';
import mime from 'mime-types';
import { mkdir } from 'fs/promises';

// Supported image extensions
const SUPPORTED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff', '.tif'];

/**
 * Check if a file is an image based on its extension
 * @param {string} filePath - Path to the file
 * @returns {boolean} - True if the file is an image
 */
export function isImage(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return SUPPORTED_IMAGE_EXTENSIONS.includes(ext);
}

/**
 * Validate a path to prevent directory traversal attacks
 * @param {string} relativePath - Relative path to validate
 * @returns {boolean} - True if the path is valid
 */
export function validatePath(relativePath) {
  // Prevent directory traversal attacks
  const normalizedPath = path.normalize(relativePath);

  // Check if the path contains '..' or starts with '/'
  if (normalizedPath.includes('..') || normalizedPath.startsWith('/') || normalizedPath.startsWith('\\')) {
    return false;
  }

  return true;
}

/**
 * List the contents of a directory with pagination, sorting, and filtering
 * @param {string} dirPath - Full path to the directory
 * @param {string} relativePath - Relative path for constructing URLs
 * @param {Object} options - Options for listing
 * @param {number} options.page - Page number (1-based)
 * @param {number} options.pageSize - Number of items per page
 * @param {string} options.sort - Sort field (name, date, size)
 * @param {string} options.order - Sort order (asc, desc)
 * @param {boolean} options.latestOnly - Show only the latest files
 * @param {number} options.latestCount - Number of latest files to show
 * @returns {Promise<Object>} - Object containing directories and files with pagination info
 */
export async function listDirectory(dirPath, relativePath = '', options = {}) {
  try {
    // Set default options
    const {
      page = 1,
      pageSize = 50,
      sort = 'name',
      order = 'asc',
      latestOnly = false,
      latestCount = 20
    } = options;

    // Check if the directory exists and is accessible
    await fs.access(dirPath, constants.R_OK);

    // Get directory stats
    const stats = await fs.stat(dirPath);

    // If it's not a directory, throw an error
    if (!stats.isDirectory()) {
      throw new Error('Not a directory');
    }

    // Read the directory contents
    const entries = await fs.readdir(dirPath, { withFileTypes: true });

    // Process directories and files
    const directories = [];
    const filesWithStats = [];

    // First pass: collect all entries with their stats
    for (const entry of entries) {
      const entryPath = path.join(dirPath, entry.name);
      const entryRelativePath = path.join(relativePath, entry.name).replace(/\\/g, '/');

      if (entry.isDirectory()) {
        directories.push({
          name: entry.name,
          path: entryRelativePath,
          type: 'directory',
          url: `/api/list?path=${encodeURIComponent(entryRelativePath)}`
        });
      } else if (entry.isFile()) {
        try {
          const fileStats = await fs.stat(entryPath);
          const isImageFile = isImage(entry.name);

          filesWithStats.push({
            name: entry.name,
            path: entryRelativePath,
            type: 'file',
            isImage: isImageFile,
            url: isImageFile ? `/static/${encodeURIComponent(entryRelativePath)}` : null,
            metadataUrl: `/api/metadata?path=${encodeURIComponent(entryRelativePath)}`,
            stats: {
              size: fileStats.size,
              created: fileStats.birthtime,
              modified: fileStats.mtime
            }
          });
        } catch (err) {
          console.error(`Error getting stats for ${entryPath}: ${err.message}`);
        }
      }
    }

    // Sort directories by name (always)
    directories.sort((a, b) => a.name.localeCompare(b.name));

    // Apply sorting to files
    if (sort === 'date') {
      filesWithStats.sort((a, b) => {
        const dateA = new Date(a.stats.modified);
        const dateB = new Date(b.stats.modified);
        return order === 'asc' ? dateA - dateB : dateB - dateA;
      });
    } else if (sort === 'size') {
      filesWithStats.sort((a, b) => {
        return order === 'asc' ? a.stats.size - b.stats.size : b.stats.size - a.stats.size;
      });
    } else {
      // Default sort by name
      filesWithStats.sort((a, b) => {
        return order === 'asc' ? a.name.localeCompare(b.name) : b.name.localeCompare(a.name);
      });
    }

    // Filter for latest files if requested
    let files = filesWithStats;
    if (latestOnly) {
      // Sort by date modified (newest first) and take the top N
      files = [...filesWithStats].sort((a, b) => {
        const dateA = new Date(a.stats.modified);
        const dateB = new Date(b.stats.modified);
        return dateB - dateA;
      }).slice(0, latestCount);
    }

    // Calculate pagination
    const totalDirs = directories.length;
    const totalFiles = files.length;
    const totalItems = totalDirs + totalFiles;
    const totalPages = Math.ceil(totalItems / pageSize);
    const validPage = Math.max(1, Math.min(page, totalPages));

    // Apply pagination
    const startIndex = (validPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    // Directories always come first, then files
    let paginatedItems = [];

    if (startIndex < totalDirs) {
      // Include directories
      const dirsEndIndex = Math.min(endIndex, totalDirs);
      paginatedItems = directories.slice(startIndex, dirsEndIndex);

      // If we have room for files
      if (dirsEndIndex < endIndex) {
        // Add files, starting from index 0 up to remaining slots
        const filesCount = endIndex - dirsEndIndex;
        paginatedItems = [...paginatedItems, ...files.slice(0, filesCount)];
      }
    } else {
      // Only include files
      const fileStartIndex = startIndex - totalDirs;
      const fileEndIndex = Math.min(fileStartIndex + pageSize, totalFiles);
      paginatedItems = files.slice(fileStartIndex, fileEndIndex);
    }

    // Split back into directories and files for the response
    const paginatedDirs = paginatedItems.filter(item => item.type === 'directory');
    const paginatedFiles = paginatedItems.filter(item => item.type === 'file');

    // Remove stats from the response to keep it clean
    const cleanFiles = paginatedFiles.map(file => {
      const { stats, ...cleanFile } = file;
      return cleanFile;
    });

    return {
      directories: paginatedDirs,
      files: cleanFiles,
      pagination: {
        page: validPage,
        pageSize,
        totalItems,
        totalPages,
        totalDirectories: totalDirs,
        totalFiles: totalFiles,
        hasNextPage: validPage < totalPages,
        hasPrevPage: validPage > 1
      },
      sorting: {
        sort,
        order
      },
      filtering: {
        latestOnly,
        latestCount
      }
    };
  } catch (error) {
    if (error.code === 'ENOENT') {
      throw new Error(`Directory not found: ${dirPath}`);
    } else if (error.code === 'EACCES') {
      throw new Error(`Permission denied: ${dirPath}`);
    }
    throw error;
  }
}

/**
 * Get metadata for a file or directory
 * @param {string} itemPath - Full path to the file or directory
 * @returns {Promise<Object>} - Metadata object
 */
export async function getImageMetadata(itemPath) {
  try {
    // Check if the item exists and is accessible
    await fs.access(itemPath, constants.R_OK);

    // Get item stats
    const stats = await fs.stat(itemPath);

    // Base metadata
    const metadata = {
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      isDirectory: stats.isDirectory(),
      isFile: stats.isFile()
    };

    // If it's a file and an image, get additional metadata
    if (stats.isFile() && isImage(itemPath)) {
      try {
        // Get MIME type based on file extension
        const mimeType = mime.lookup(itemPath);
        if (mimeType) {
          metadata.mimeType = mimeType;
          metadata.extension = path.extname(itemPath).substring(1); // Remove the dot
        }
      } catch (error) {
        console.error(`Error getting file type: ${error.message}`);
      }
    }

    return metadata;
  } catch (error) {
    if (error.code === 'ENOENT') {
      throw new Error(`Item not found: ${itemPath}`);
    } else if (error.code === 'EACCES') {
      throw new Error(`Permission denied: ${itemPath}`);
    }
    throw error;
  }
}

/**
 * Create a new directory
 * @param {string} basePath - Base directory path
 * @param {string} relativePath - Relative path where to create the directory
 * @param {string} folderName - Name of the folder to create
 * @returns {Promise<Object>} - Result of the operation
 */
export async function createDirectory(basePath, relativePath, folderName) {
  try {
    // Validate folder name
    if (!folderName || folderName.trim() === '') {
      throw new Error('Folder name cannot be empty');
    }

    // Check for invalid characters in folder name
    const invalidChars = /[<>:"/\\|?*\x00-\x1F]/g;
    if (invalidChars.test(folderName)) {
      throw new Error('Folder name contains invalid characters');
    }

    // Construct the full path
    const fullPath = path.join(basePath, relativePath, folderName);

    // Check if the directory already exists
    try {
      await fs.access(fullPath, constants.F_OK);
      return {
        success: false,
        message: `Folder '${folderName}' already exists`,
        path: path.join(relativePath, folderName).replace(/\\/g, '/')
      };
    } catch (err) {
      // Directory doesn't exist, which is what we want
    }

    // Create the directory
    await mkdir(fullPath, { recursive: true });

    return {
      success: true,
      message: `Folder '${folderName}' created successfully`,
      path: path.join(relativePath, folderName).replace(/\\/g, '/')
    };
  } catch (error) {
    if (error.code === 'EACCES') {
      throw new Error(`Permission denied: Cannot create folder in ${relativePath}`);
    }
    throw error;
  }
}
