import { createClient } from '@supabase/supabase-js';
import XLSX from 'xlsx';

// Initialize Supabase client
const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0NzI4NzQsImV4cCI6MjA1MDA0ODg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
const supabase = createClient(supabaseUrl, supabaseKey);

// Test the fixed import logic on PP Z Lite section
async function testFixedImport() {
    try {
        console.log('🧪 Testing fixed import logic on PP Z Lite section...\n');

        const inputFile = 'data/external data/discraftstock.xlsx';
        const workbook = XLSX.readFile(inputFile);
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const range = XLSX.utils.decode_range(worksheet['!ref']);

        console.log(`📊 Excel file loaded: ${range.e.r + 1} rows`);

        // Import the dynamic header functions from fullDiscraftImport.js
        // (We'll copy them here for testing)

        // Function to parse weight range from header text
        function parseWeightHeader(headerText) {
            if (!headerText) return null;

            const text = headerText.toString().trim();

            // Handle single weight like "150" or "150g"
            const singleMatch = text.match(/^(\d+)g?$/);
            if (singleMatch) {
                const weight = parseInt(singleMatch[1]);
                return { min: weight, max: weight, name: `${weight}g` };
            }

            // Handle range like "141-150", "151-159", "160-166"
            const rangeMatch = text.match(/^(\d+)-(\d+)g?$/);
            if (rangeMatch) {
                const min = parseInt(rangeMatch[1]);
                const max = parseInt(rangeMatch[2]);
                return { min: min, max: max, name: `${min}-${max}g` };
            }

            // Handle "177+" format
            const plusMatch = text.match(/^(\d+)\+$/);
            if (plusMatch) {
                const min = parseInt(plusMatch[1]);
                return { min: min, max: 180, name: `${min}+g` }; // Assume max 180g
            }

            return null;
        }

        // Function to find weight headers in a row
        function findWeightHeaders(row, startCol = 11, endCol = 17) {
            const weightHeaders = {};

            for (let col = startCol; col <= endCol; col++) {
                const headerText = row[col];
                const weightInfo = parseWeightHeader(headerText);

                if (weightInfo) {
                    const columnLetter = String.fromCharCode(65 + col); // Convert to A, B, C, etc.
                    weightHeaders[col] = {
                        ...weightInfo,
                        letter: columnLetter,
                        columnIndex: col
                    };
                }
            }

            return weightHeaders;
        }

        // Test specifically on PP Z Lite section (around row 153)
        console.log('🔍 Testing PP Z Lite section (rows 153-159)...\n');

        let currentWeightHeaders = null;

        // Process rows 152-160 to test the header detection and parsing
        for (let row = 152; row <= 160; row++) {
            const rowData = {};

            // Read all columns for this row
            for (let col = 0; col <= range.e.c; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                const cell = worksheet[cellAddress];
                if (cell && cell.v !== null && cell.v !== undefined && cell.v !== '') {
                    rowData[`col_${col}`] = cell.v;
                }
            }

            const rawPlastic = rowData.col_1?.toString().trim() || '';
            const rawModel = rowData.col_4?.toString().trim() || '';

            console.log(`Row ${row + 1}: "${rawPlastic}" | "${rawModel}"`);

            // Check if this is a header row
            if (rawPlastic === 'Line' && rawModel === 'Model') {
                console.log(`  📋 Header row detected!`);

                // Convert rowData to array format
                const rowArray = [];
                for (let col = 0; col <= range.e.c; col++) {
                    rowArray[col] = rowData[`col_${col}`];
                }

                // Find weight headers
                const weightHeaders = findWeightHeaders(rowArray);

                if (Object.keys(weightHeaders).length > 0) {
                    currentWeightHeaders = weightHeaders;
                    console.log(`  ✅ Found ${Object.keys(weightHeaders).length} weight columns:`);
                    Object.entries(weightHeaders).forEach(([colIndex, weightInfo]) => {
                        console.log(`     Column ${weightInfo.letter} (${colIndex}): ${weightInfo.name} (${weightInfo.min}-${weightInfo.max}g)`);
                    });
                } else {
                    console.log(`  ❌ No weight headers found`);
                }
            }

            // Test data row parsing with current headers
            if (rawPlastic && rawModel && rawPlastic !== 'Line') {
                console.log(`  📦 Data row: ${rawPlastic} ${rawModel}`);

                if (currentWeightHeaders) {
                    console.log(`  🎯 Using dynamic weight headers:`);
                    Object.entries(currentWeightHeaders).forEach(([colIndex, weightInfo]) => {
                        const status = rowData[`col_${colIndex}`];
                        if (status !== undefined && status !== null && status !== '') {
                            console.log(`     ${weightInfo.name}: "${status}"`);
                        }
                    });
                } else {
                    console.log(`  ⚠️ No weight headers available, would use fallback`);
                }
            }

            console.log('');
        }

        console.log('✅ Test completed! The fixed logic should now correctly parse PP Z Lite weight ranges.');

    } catch (error) {
        console.error('❌ Test error:', error);
    }
}

testFixedImport();