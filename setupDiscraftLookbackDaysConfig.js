// setupDiscraftLookbackDaysConfig.js - Set up the configurable lookback days for Discraft disc ordering

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function setupConfig() {
    console.log('🔧 Setting up Discraft lookback days configuration...');
    console.log('=====================================================');

    try {
        // Check if the config already exists
        const { data: existingConfig, error: checkError } = await supabase
            .from('t_config')
            .select('key, value')
            .eq('key', 'discraft_disc_order_look_back_days')
            .single();

        if (checkError && checkError.code !== 'PGRST116') {
            throw new Error(`Error checking existing config: ${checkError.message}`);
        }

        if (existingConfig) {
            console.log(`✅ Config already exists: ${existingConfig.key} = ${existingConfig.value}`);
            console.log('No changes needed.');
        } else {
            // Insert the new config value
            console.log('📝 Creating new config entry...');
            
            const { error: insertError } = await supabase
                .from('t_config')
                .insert({
                    key: 'discraft_disc_order_look_back_days',
                    value: '60',
                    description: 'Number of days to look back when counting sold discs for Discraft order calculations'
                });

            if (insertError) {
                throw new Error(`Error inserting config: ${insertError.message}`);
            }

            console.log('✅ Successfully created config entry:');
            console.log('   Key: discraft_disc_order_look_back_days');
            console.log('   Value: 60');
            console.log('   Description: Number of days to look back when counting sold discs for Discraft order calculations');
        }

        // Test the configuration
        console.log('\n🧪 Testing configuration retrieval...');
        
        const { data: testConfig, error: testError } = await supabase
            .from('t_config')
            .select('value')
            .eq('key', 'discraft_disc_order_look_back_days')
            .single();

        if (testError) {
            throw new Error(`Error testing config retrieval: ${testError.message}`);
        }

        const lookbackDays = parseInt(testConfig.value) || 60;
        console.log(`✅ Configuration test successful: ${lookbackDays} days`);

        console.log('\n🎉 Setup completed successfully!');
        console.log('\n📋 Usage Instructions:');
        console.log('To change the lookback days, update the config value:');
        console.log('UPDATE t_config SET value = \'45\' WHERE key = \'discraft_disc_order_look_back_days\';');
        console.log('\nThe change will take effect immediately on the next Discraft import.');

    } catch (error) {
        console.error('❌ Setup failed:', error.message);
        process.exit(1);
    }
}

// Run the setup
setupConfig()
    .then(() => {
        console.log('\n✅ Setup script completed');
        process.exit(0);
    })
    .catch(error => {
        console.error('💥 Setup script failed:', error);
        process.exit(1);
    });
