param(
  [Parameter(Mandatory=$true)][string]$Path,
  [string[]]$Cells = @('M129','P129')
)
$ErrorActionPreference = 'Stop'
Add-Type -AssemblyName System.Data

if (-not (Test-Path -LiteralPath $Path)) {
  Write-Error "File not found: $Path"
}

# Use Microsoft.Jet.OLEDB.4.0 (requires 32-bit PowerShell)
$sheetName = $null
$cs = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=$Path;Extended Properties='Excel 8.0;HDR=No;IMEX=0';"
$conn = New-Object System.Data.OleDb.OleDbConnection($cs)
$conn.Open()
try {
  $schema = $conn.GetOleDbSchemaTable([System.Data.OleDb.OleDbSchemaGuid]::Tables, $null)
  if (-not $schema -or $schema.Rows.Count -eq 0) { throw 'No worksheets found via OLE DB.' }
  $row = $schema.Rows | Where-Object { $_.TABLE_TYPE -eq 'TABLE' -and $_.TABLE_NAME -like '*$' } | Select-Object -First 1
  if (-not $row) { throw 'No worksheet ending with $ found.' }
  $sheetName = [string]$row.TABLE_NAME
  $sheetName = $sheetName.Trim("'")
  if ($sheetName -notmatch '\$$') { $sheetName += '$' }

  $cmd = $conn.CreateCommand()
  foreach ($cell in $Cells) {
    $target = "$sheetName$cell:$cell"
    $cmd.CommandText = "UPDATE [$target] SET F1 = IIF(F1 IS NULL, 0, F1) + 10"
    [void]$cmd.ExecuteNonQuery()
  }
}
finally {
  $conn.Close()
}

Write-Output "Updated: $Path"

