import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkFundraiserParsing() {
    try {
        console.log('🔍 Checking fundraiser section parsing...\n');
        
        // Check what's in rows 22, 25, 28 (the fundraiser section)
        console.log('1. Checking rows 22-30 in the database...');
        const { data: fundraiserData, error } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, vendor_description, mold_name, plastic_name, stamp_name, is_orderable, excel_mapping_key')
            .gte('excel_row_hint', 22)
            .lte('excel_row_hint', 30)
            .order('excel_row_hint, excel_column');

        if (error) {
            console.error('❌ Error querying fundraiser data:', error);
            return;
        }

        console.log(`✅ Found ${fundraiserData.length} records in rows 22-30`);
        
        fundraiserData.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.vendor_description}`);
            console.log(`      Mold: ${record.mold_name}, Plastic: ${record.plastic_name}, Stamp: ${record.stamp_name}`);
            console.log(`      Orderable: ${record.is_orderable}, Mapping: ${record.excel_mapping_key}`);
            console.log('');
        });

        // Check specifically for "Fundraiser" or "Ben Askren" items
        console.log('2. Checking for fundraiser items specifically...');
        const { data: benAskrenData, error: benError } = await supabase
            .from('it_discraft_order_sheet_lines')
            .select('excel_row_hint, excel_column, vendor_description, mold_name, plastic_name, stamp_name, is_orderable')
            .ilike('vendor_description', '%Ben Askren%')
            .order('excel_row_hint, excel_column');

        if (benError) {
            console.error('❌ Error querying Ben Askren data:', benError);
            return;
        }

        console.log(`✅ Found ${benAskrenData.length} Ben Askren fundraiser records`);
        benAskrenData.forEach((record, index) => {
            console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: ${record.vendor_description}`);
            console.log(`      Parsed - Mold: ${record.mold_name}, Plastic: ${record.plastic_name}, Stamp: ${record.stamp_name}`);
            console.log(`      Orderable: ${record.is_orderable}`);
            console.log('');
        });

        // Check what should be the correct parsing
        console.log('3. Expected parsing for "Fundraiser - Ben Askren Z Jawbreaker Thrasher (msrp $19.99, cost $10.00)":');
        console.log('   Mold: Thrasher');
        console.log('   Plastic: Elite Z Jawbreaker');
        console.log('   Stamp: Live Free - Be Happy - Funky Ben Askren Fundraiser');
        console.log('   Should be orderable: true');
        console.log('   Should be in column A (not E)');

        console.log('\n🎉 Check completed!');
        
    } catch (error) {
        console.error('❌ Check failed:', error.message);
    }
}

checkFundraiserParsing().catch(console.error);
