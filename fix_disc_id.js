// Test disc ID extraction
const testDiscId = "B #229468 Westside Harp Origio Stock Solid White 174";

console.log('Testing disc ID extraction...');
console.log('Input:', testDiscId);

// Current (broken) logic
const oldLogic = testDiscId.replace(/^D/, '');
console.log('Old logic result:', oldLogic);

// New (correct) logic
const discIdMatch = testDiscId.match(/#(\d+)/);
if (discIdMatch) {
  const numericDiscId = discIdMatch[1];
  console.log('New logic result:', numericDiscId);
} else {
  console.log('No match found');
}

// Test with different formats
const testCases = [
  "B #229468 Westside Harp Origio Stock Solid White 174",
  "D123456",
  "B #123 Test",
  "Invalid format"
];

console.log('\nTesting various formats:');
testCases.forEach(test => {
  const match = test.match(/#(\d+)/);
  console.log(`"${test}" -> ${match ? match[1] : 'NO MATCH'}`);
});
