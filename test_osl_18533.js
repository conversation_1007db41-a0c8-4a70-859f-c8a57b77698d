// test_osl_18533.js
// Create a test task for OSL 18533 to test the improved error extraction

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function createTestTask() {
  try {
    console.log('Creating test task for OSL 18533...');
    
    const { data, error } = await supabase
      .from('t_task_queue')
      .insert({
        task_type: 'publish_product_osl',
        payload: { id: 18533 },
        status: 'pending',
        scheduled_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        enqueued_by: 'test_improved_error_extraction'
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error creating task:', error);
      return;
    }
    
    console.log('✅ Created test task:', data.id);
    console.log('This should now properly extract the MANUAL FIX REQUIRED error message');
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

createTestTask();
