import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Cache for lookup data
let brandsCache = {};
let allMoldsCache = [];
let allPlasticsCache = [];
let moldsCache = {};
let plasticsCache = {};
let stampsCache = [];

// Brand mappings for vendor names that don't match exactly
const brandMappings = {
    'Westside Discs': 'Westside',
    'Dynamic Discs': 'Dynamic Discs',
    'Latitude 64': 'Latitude 64',
    'Discmania': 'Discmania',
    'Kastaplast': 'Kastaplast'
};

// Plastic name mappings for vendor names that don't match database names
const plasticMappings = {
    'Gold': 'Gold Line',
    'Tournament': 'Tournament',
    'Lucid Moonshine': 'Lucid Moonshine Glow',
    'Classic Moonshine': 'Classic Soft Moonshine Glow',
    'Prime Moonshine': 'Prime Moonshine Glow'
    // Add more mappings as needed
};

async function loadLookupData() {
    console.log('Loading lookup data...');
    
    // Load all brands
    const { data: brands, error: brandsError } = await supabase
        .from('t_brands')
        .select('id, brand');
    
    if (brandsError) {
        throw new Error(`Error loading brands: ${brandsError.message}`);
    }
    
    // Create brand lookup
    brands.forEach(brand => {
        brandsCache[brand.brand] = brand.id;
    });
    
    // Load all molds with their brand_id
    const { data: molds, error: moldsError } = await supabase
        .from('t_molds')
        .select('mold, brand_id');
    
    if (moldsError) {
        throw new Error(`Error loading molds: ${moldsError.message}`);
    }
    
    // Store all molds and group by brand_id
    allMoldsCache = molds.map(m => ({ mold: m.mold, brand_id: m.brand_id }));
    molds.forEach(mold => {
        if (!moldsCache[mold.brand_id]) {
            moldsCache[mold.brand_id] = [];
        }
        moldsCache[mold.brand_id].push(mold.mold);
    });
    
    // Load all plastics with their brand_id
    const { data: plastics, error: plasticsError } = await supabase
        .from('t_plastics')
        .select('plastic, brand_id');
    
    if (plasticsError) {
        throw new Error(`Error loading plastics: ${plasticsError.message}`);
    }
    
    // Store all plastics and group by brand_id
    allPlasticsCache = plastics.map(p => ({ plastic: p.plastic, brand_id: p.brand_id }));
    plastics.forEach(plastic => {
        if (!plasticsCache[plastic.brand_id]) {
            plasticsCache[plastic.brand_id] = [];
        }
        plasticsCache[plastic.brand_id].push(plastic.plastic);
    });
    
    // Load all stamps (not brand-specific)
    const { data: stamps, error: stampsError } = await supabase
        .from('t_stamps')
        .select('stamp');
    
    if (stampsError) {
        throw new Error(`Error loading stamps: ${stampsError.message}`);
    }
    
    stampsCache = stamps.map(s => s.stamp);
    
    console.log(`Loaded ${brands.length} brands, ${molds.length} molds, ${plastics.length} plastics, ${stampsCache.length} stamps`);
}

function findPlasticWithMapping(plasticName, brandId) {
    // First try with mapping
    const mappedPlastic = plasticMappings[plasticName] || plasticName;
    
    // Check brand-specific plastics first
    const brandPlastics = plasticsCache[brandId] || [];
    
    // Try exact match with mapped name
    if (brandPlastics.includes(mappedPlastic)) {
        return mappedPlastic;
    }
    
    // Try exact match with original name
    if (brandPlastics.includes(plasticName)) {
        return plasticName;
    }
    
    // Try partial matches
    for (const plastic of brandPlastics) {
        if (plastic.includes(mappedPlastic) || mappedPlastic.includes(plastic)) {
            return plastic;
        }
    }
    
    return null;
}

function findMoldAcrossBrands(moldName) {
    // First try exact match
    for (const moldData of allMoldsCache) {
        if (moldData.mold === moldName) {
            return moldData.mold;
        }
    }
    
    // Then try partial match
    for (const moldData of allMoldsCache) {
        if (moldData.mold.includes(moldName) || moldName.includes(moldData.mold)) {
            return moldData.mold;
        }
    }
    
    return null;
}

function parseProductTitle(productTitle, brandId) {
    if (!productTitle || typeof productTitle !== 'string') {
        return { mold: null, plastic: null, stamp: null };
    }
    
    const title = productTitle.trim();
    
    // Skip non-disc products
    if (title.includes('Set') || title.includes('Mystery') || title.includes('Box') || 
        title.includes('DyeMax')) {
        return { mold: null, plastic: null, stamp: null };
    }
    
    let foundMold = null;
    let foundPlastic = null;
    let foundStamp = 'Stock';
    
    // Standard pattern: "Plastic Mold - Stamp" or "Plastic Mold"
    
    // Get brand plastics and sort by length (longest first)
    const brandPlastics = plasticsCache[brandId] || [];
    const sortedPlastics = [...brandPlastics].sort((a, b) => b.length - a.length);
    
    // Also try mapped plastic names
    const allPossiblePlastics = [...sortedPlastics];
    Object.keys(plasticMappings).forEach(vendorName => {
        if (!allPossiblePlastics.includes(vendorName)) {
            allPossiblePlastics.push(vendorName);
        }
    });
    allPossiblePlastics.sort((a, b) => b.length - a.length);
    
    // Find plastic at the beginning
    for (const plastic of allPossiblePlastics) {
        if (title.startsWith(plastic + ' ') || title === plastic) {
            // Use mapping to get the correct database plastic name
            foundPlastic = findPlasticWithMapping(plastic, brandId);
            break;
        }
    }
    
    if (foundPlastic) {
        // Find the original plastic name that matched (could be vendor name)
        let matchedPlasticName = foundPlastic;
        for (const plastic of allPossiblePlastics) {
            if (title.startsWith(plastic + ' ') || title === plastic) {
                matchedPlasticName = plastic;
                break;
            }
        }
        
        // Remove plastic from title to find mold and stamp
        let remainingTitle = title.substring(matchedPlasticName.length).trim();
        
        // Check if there's a dash indicating a stamp
        const dashIndex = remainingTitle.indexOf(' - ');
        let moldPart = remainingTitle;
        let stampPart = '';
        
        if (dashIndex !== -1) {
            moldPart = remainingTitle.substring(0, dashIndex).trim();
            stampPart = remainingTitle.substring(dashIndex + 3).trim();
        }
        
        // Find mold (try brand-specific first, then cross-brand)
        const brandMolds = moldsCache[brandId] || [];
        const sortedMolds = [...brandMolds].sort((a, b) => b.length - a.length);
        
        for (const mold of sortedMolds) {
            if (moldPart.startsWith(mold + ' ') || moldPart === mold) {
                foundMold = mold;
                break;
            }
        }
        
        // If not found in brand, try cross-brand
        if (!foundMold) {
            foundMold = findMoldAcrossBrands(moldPart);
        }
        
        // Set stamp
        if (stampPart) {
            foundStamp = stampPart;
        } else if (foundMold && moldPart.length > foundMold.length) {
            // Check if there's additional text after mold (without dash)
            const afterMold = moldPart.substring(foundMold.length).trim();
            if (afterMold) {
                foundStamp = afterMold;
            }
        }
    }
    
    return {
        mold: foundMold,
        plastic: foundPlastic,
        stamp: foundStamp
    };
}

function parseVariantTitle(variantTitle, brandId) {
    if (!variantTitle || typeof variantTitle !== 'string') {
        return { mold: null, plastic: null, stamp: null };
    }
    
    const title = variantTitle.trim();
    
    // Skip default titles
    if (title === 'Default Title' || title === 'Assorted') {
        return { mold: null, plastic: null, stamp: null };
    }
    
    // For DyeMax variants like "Fuzion Verdict (Midrange)"
    // Remove type information in parentheses
    const cleanTitle = title.replace(/\s*\([^)]*\)\s*$/, '').trim();
    
    // Use same logic as product title parsing
    return parseProductTitle(cleanTitle, brandId);
}

async function testParsingWithMappings() {
    console.log('Testing product parsing with plastic mappings...\n');
    
    try {
        await loadLookupData();
        
        // Test cases
        const testCases = [
            {
                title: 'BioFuzion Defender - Chris Clemons 2023',
                brand: 'Dynamic Discs',
                expected: { plastic: 'BioFuzion', mold: 'Defender', stamp: 'Chris Clemons 2023' }
            },
            {
                title: 'Lucid Moonshine Deputy - Timehop',
                brand: 'Dynamic Discs',
                expected: { plastic: 'Lucid Moonshine Glow', mold: 'Deputy', stamp: 'Timehop' }
            },
            {
                title: 'K3 Tuff',
                brand: 'Kastaplast',
                expected: { plastic: 'K3', mold: 'Tuff', stamp: 'Stock' }
            },
            {
                title: 'VIP Tide',
                brand: 'Westside',
                expected: { plastic: 'VIP', mold: 'Tide', stamp: 'Stock' }
            },
            {
                title: 'Classic Swirl Judge - Judgement Day',
                brand: 'Dynamic Discs',
                expected: { plastic: 'Classic Swirl', mold: 'Judge', stamp: 'Judgement Day' }
            }
        ];
        
        console.log('Test Results:');
        console.log('=============');
        
        for (const testCase of testCases) {
            // Apply brand mapping
            const mappedBrand = brandMappings[testCase.brand] || testCase.brand;
            const brandId = brandsCache[mappedBrand];
            
            if (!brandId) {
                console.log(`❌ Brand "${testCase.brand}" not found`);
                continue;
            }
            
            const result = parseProductTitle(testCase.title, brandId);
            
            console.log(`\nTitle: "${testCase.title}" [${testCase.brand}]`);
            console.log(`Expected: Plastic="${testCase.expected.plastic}", Mold="${testCase.expected.mold}", Stamp="${testCase.expected.stamp}"`);
            console.log(`Actual:   Plastic="${result.plastic || 'NOT FOUND'}", Mold="${result.mold || 'NOT FOUND'}", Stamp="${result.stamp || 'NOT FOUND'}"`);
            
            const plasticMatch = result.plastic === testCase.expected.plastic;
            const moldMatch = result.mold === testCase.expected.mold;
            const stampMatch = result.stamp === testCase.expected.stamp;
            
            if (plasticMatch && moldMatch && stampMatch) {
                console.log('✅ PASS');
            } else {
                console.log('❌ FAIL');
                if (!plasticMatch) console.log(`  - Plastic mismatch`);
                if (!moldMatch) console.log(`  - Mold mismatch`);
                if (!stampMatch) console.log(`  - Stamp mismatch`);
            }
        }
        
    } catch (error) {
        console.error('Test failed:', error);
    }
}

// Run the test
testParsingWithMappings();
