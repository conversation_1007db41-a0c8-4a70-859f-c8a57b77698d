import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function listMissingImages() {
  try {
    console.log('📋 Complete List of Missing Mold Images\n');
    
    // Get all unverified mold images
    const { data: unverifiedImages, error } = await supabase
      .from('t_images')
      .select('id, record_id')
      .eq('table_name', 't_molds')
      .eq('image_verified', false)
      .order('record_id');

    if (error) {
      console.error('❌ Error fetching unverified images:', error);
      return;
    }

    // Get mold details for each unverified image
    const moldIds = unverifiedImages.map(img => img.record_id);
    const { data: molds, error: moldsError } = await supabase
      .from('t_molds')
      .select(`
        id,
        mold,
        brand_id,
        t_brands(brand)
      `)
      .in('id', moldIds);
    
    if (moldsError) {
      console.error('❌ Error fetching molds:', moldsError);
      return;
    }

    if (!unverifiedImages || unverifiedImages.length === 0) {
      console.log('✅ No missing images found!');
      return;
    }

    console.log(`Found ${unverifiedImages.length} missing mold images:\n`);

    // Create a map of mold data for easy lookup
    const moldMap = {};
    for (const mold of molds) {
      moldMap[mold.id] = mold;
    }

    // Group by brand for easier organization
    const imagesByBrand = {};

    for (const image of unverifiedImages) {
      const mold = moldMap[image.record_id];
      if (!mold) continue;

      const brand = mold.t_brands?.brand || 'Unknown Brand';

      if (!imagesByBrand[brand]) {
        imagesByBrand[brand] = [];
      }

      imagesByBrand[brand].push({
        moldId: mold.id,
        moldName: mold.mold,
        imageId: image.id,
        url: `http://s3.amazonaws.com/paintball/shopify/molds/${mold.id}.jpg`
      });
    }
    
    // Print organized list
    for (const [brand, images] of Object.entries(imagesByBrand)) {
      console.log(`🏷️ ${brand} (${images.length} missing images):`);
      
      for (const image of images) {
        console.log(`   - Mold ${image.moldId}: ${image.moldName}`);
        console.log(`     Missing: ${image.url}`);
      }
      console.log('');
    }
    
    // Create a simple CSV-like list for easy copying
    console.log('\n📄 Simple list for uploading:');
    console.log('Mold ID | Mold Name | Brand | Required URL');
    console.log('--------|-----------|-------|-------------');
    
    for (const image of unverifiedImages) {
      const mold = moldMap[image.record_id];
      if (!mold) continue;

      const brand = mold.t_brands?.brand || 'Unknown';
      console.log(`${mold.id} | ${mold.mold} | ${brand} | http://s3.amazonaws.com/paintball/shopify/molds/${mold.id}.jpg`);
    }
    
    console.log(`\n📊 Summary: ${unverifiedImages.length} images need to be uploaded to S3`);
    console.log('💡 After uploading, mark images as verified and retry collection publishing');
    
  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

listMissingImages();
