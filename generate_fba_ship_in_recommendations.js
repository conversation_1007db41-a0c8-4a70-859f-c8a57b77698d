import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Calculate recommended ship-in quantity based on sales rank
 * @param {number} rank - Sales rank from Keepa data
 * @returns {number|null} - Recommended quantity or null if rank is too high
 */
function calculateRecommendedQuantity(rank) {
  if (!rank || rank > 2129) return null;
  
  if (rank < 300) return 5;
  if (rank < 600) return 4;
  if (rank < 1000) return 3;
  if (rank < 1500) return 2;
  if (rank < 2130) return 1;
  
  return null;
}

/**
 * Check if the fulfilled inventory report table exists and is accessible
 */
async function checkFulfilledInventoryReportTable() {
  console.log('Checking fulfilled inventory report table access...');

  try {
    // Test table access by getting count
    const { count, error } = await supabase
      .from('it_amaz_fulfilled_inventory_report')
      .select('*', { count: 'exact', head: true });

    if (error) {
      throw new Error(`Cannot access it_amaz_fulfilled_inventory_report table: ${error.message}`);
    }

    console.log(`Fulfilled inventory report table accessible with ${count || 0} existing records`);
    return true;
  } catch (err) {
    throw new Error(`Fulfilled inventory report table check failed: ${err.message}`);
  }
}

/**
 * Clear ALL existing FBA ship-in recommendations
 * This ensures fresh recommendations are calculated based on current Keepa data
 */
async function clearExistingRecommendations() {
  console.log('Clearing ALL existing FBA ship-in recommendations...');

  try {
    // Count all records that currently have recommendations
    const { count: existingRecsCount, error: countError } = await supabase
      .from('import_table_amaz_fba_inv_rpt')
      .select('*', { count: 'exact', head: true })
      .not('Recommended ship-in quantity', 'is', null);

    if (countError) {
      throw new Error(`Error counting existing recommendations: ${countError.message}`);
    }

    console.log(`Found ${existingRecsCount || 0} records with existing recommendations`);

    if (existingRecsCount > 0) {
      // Clear ALL recommendations
      const { count: clearedCount, error: clearError } = await supabase
        .from('import_table_amaz_fba_inv_rpt')
        .update({ 'Recommended ship-in quantity': null })
        .not('Recommended ship-in quantity', 'is', null);

      if (clearError) {
        throw new Error(`Error clearing recommendations: ${clearError.message}`);
      }

      console.log(`Cleared recommendations for ${clearedCount || 0} records`);
    } else {
      console.log('No existing recommendations found to clear');
    }

    return true;
  } catch (error) {
    console.error('Error clearing existing recommendations:', error.message);
    throw error;
  }
}

/**
 * Find t_sdasins records that need FBA ship-in recommendations
 */
async function findCandidateRecords() {
  console.log('Finding candidate t_sdasins records...');

  // Calculate date threshold for recent rank data (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  const dateThreshold = thirtyDaysAgo.toISOString().split('T')[0];

  console.log(`Using rank data from ${dateThreshold} onwards (last 30 days)`);
  console.log('Processing ALL FBA-enabled sdasins, then filtering by date in JavaScript...');

  // Process records in chunks to work around Supabase's 1000 record limit
  const chunkSize = 1000;
  const candidateRecords = [];
  let processedCount = 0;
  let offset = 0;
  let chunkNumber = 0;

  while (true) {
    chunkNumber++;
    console.log(`Processing chunk ${chunkNumber} (starting from record ${offset + 1})`);

    // Get chunk of ALL sdasin records, then filter by date in JavaScript
    const { data: sdasinRecords, error: sdasinError } = await supabase
      .from('t_sdasins')
      .select('id, fba_sku, fbafnsku, asin, so_rank_30day_avg, so_rank_30day_avg_date')
      .eq('fba', 'Y')
      .not('fbafnsku', 'is', null)
      .not('fba_sku', 'is', null)
      .not('so_rank_30day_avg', 'is', null)
      .order('id', { ascending: true })
      .range(offset, offset + chunkSize - 1);

    if (sdasinError) {
      throw new Error(`Error fetching t_sdasins chunk: ${sdasinError.message}`);
    }

    // If we got no records, we're done
    if (!sdasinRecords || sdasinRecords.length === 0) {
      break;
    }

    // Filter records by date in JavaScript (only process recent rank data)
    const recentRecords = sdasinRecords.filter(record =>
      record.so_rank_30day_avg_date && record.so_rank_30day_avg_date >= dateThreshold
    );

    console.log(`Chunk ${chunkNumber}: ${sdasinRecords.length} total records, ${recentRecords.length} with recent rank data`);

    // Process each recent record in the chunk
    for (const sdasin of recentRecords) {
      const recommendedQty = calculateRecommendedQuantity(sdasin.so_rank_30day_avg);

      // Skip if rank is too high
      if (recommendedQty === null) {
        continue;
      }

      // Check if record exists in fulfilled inventory report table
      const { data: existingRecords, error: checkError } = await supabase
        .from('it_amaz_fulfilled_inventory_report')
        .select('seller_sku, fulfillment_channel_sku, quantity_available')
        .or(`seller_sku.eq.${sdasin.fba_sku},fulfillment_channel_sku.eq.${sdasin.fbafnsku}`);

      if (checkError) {
        console.warn(`Error checking existing record for SKU ${sdasin.fba_sku}: ${checkError.message}`);
        continue;
      }

      const existingRecord = existingRecords && existingRecords.length > 0 ? existingRecords[0] : null;

      // Calculate intelligent recommendation based on target vs current inventory
      const currentAvailable = existingRecord.quantity_available || 0;
      const targetQuantity = recommendedQty; // This is our target based on sales rank
      const recommendationQuantity = Math.max(0, targetQuantity - currentAvailable);

      // Only create recommendation if we need to ship in more units
      if (recommendationQuantity > 0) {
        candidateRecords.push({
          sdasin_id: sdasin.id,
          sku: sdasin.fba_sku,
          fnsku: sdasin.fbafnsku,
          asin: sdasin.asin,
          sales_rank: sdasin.so_rank_30day_avg,
          target_quantity: targetQuantity,
          current_inventory: currentAvailable,
          recommended_quantity: recommendationQuantity,
          inventory_record: existingRecord
        });
      }
    }

    processedCount += sdasinRecords.length;

    // If we got fewer records than requested, we've reached the end
    if (sdasinRecords.length < chunkSize) {
      break;
    }

    // Move to next chunk
    offset += chunkSize;

    // Small delay between chunks to be gentle on the database
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log(`Processed ${processedCount} total records`);
  console.log(`Found ${candidateRecords.length} records that need FBA ship-in recommendations`);
  return candidateRecords;
}

/**
 * Create or update FBA inventory report records with recommendations
 */
async function createFbaRecommendations(candidateRecords) {
  console.log('Creating/updating FBA ship-in recommendations...');

  const currentTimestamp = new Date().toISOString();
  let createdCount = 0;
  let updatedCount = 0;
  let errorCount = 0;

  for (const record of candidateRecords) {
    try {
      // Check if record already exists in import_table_amaz_fba_inv_rpt
      const { data: existingFbaRecord, error: checkError } = await supabase
        .from('import_table_amaz_fba_inv_rpt')
        .select('fnsku, sku, available')
        .or(`sku.eq.${record.sku},fnsku.eq.${record.fnsku}`)
        .limit(1);

      if (checkError) {
        console.error(`Error checking existing FBA record for SKU ${record.sku}: ${checkError.message}`);
        errorCount++;
        continue;
      }

      const recordData = {
        sku: record.sku,
        fnsku: record.fnsku,
        asin: record.asin,
        available: record.current_inventory || 0, // Use current inventory from fulfilled inventory report
        'Recommended ship-in quantity': record.recommended_quantity,
        'snapshot-date': currentTimestamp.split('T')[0] // Convert to date format
      };

      if (existingFbaRecord && existingFbaRecord.length > 0) {
        // Update existing record using fnsku as primary key
        const { error } = await supabase
          .from('import_table_amaz_fba_inv_rpt')
          .update(recordData)
          .eq('fnsku', record.fnsku);

        if (error) {
          console.error(`Error updating record for SKU ${record.sku}: ${error.message}`);
          errorCount++;
        } else {
          updatedCount++;
          console.log(`Updated SKU ${record.sku}: rank ${record.sales_rank} → target ${record.target_quantity}, have ${record.current_inventory}, ship ${record.recommended_quantity}`);
        }
      } else {
        // Create new record
        const { error } = await supabase
          .from('import_table_amaz_fba_inv_rpt')
          .insert(recordData);

        if (error) {
          console.error(`Error creating record for SKU ${record.sku}: ${error.message}`);
          errorCount++;
        } else {
          createdCount++;
          console.log(`Created SKU ${record.sku}: rank ${record.sales_rank} → target ${record.target_quantity}, have ${record.current_inventory}, ship ${record.recommended_quantity}`);
        }
      }
    } catch (err) {
      console.error(`Exception processing SKU ${record.sku}: ${err.message}`);
      errorCount++;
    }
  }

  return { createdCount, updatedCount, errorCount };
}

/**
 * Main function to generate FBA ship-in recommendations
 */
async function main() {
  console.log('=== FBA Ship-In Quantity Recommendations Generator ===');
  console.log(`Started at: ${new Date().toISOString()}\n`);

  try {
    // Step 1: Check the fulfilled inventory report table access
    await checkFulfilledInventoryReportTable();

    // Step 2: Clear ALL existing recommendations (in import_table_amaz_fba_inv_rpt)
    await clearExistingRecommendations();

    // Step 3: Find candidate records
    const candidateRecords = await findCandidateRecords();

    if (candidateRecords.length === 0) {
      console.log('No records found that need FBA ship-in recommendations.');
      return;
    }

    // Step 4: Create/update recommendations
    const results = await createFbaRecommendations(candidateRecords);

    console.log('\n=== Summary ===');
    console.log(`Records processed: ${candidateRecords.length}`);
    console.log(`New records created: ${results.createdCount}`);
    console.log(`Existing records updated: ${results.updatedCount}`);
    console.log(`Errors encountered: ${results.errorCount}`);
    console.log(`Completed at: ${new Date().toISOString()}`);

  } catch (error) {
    console.error('Fatal error in main process:', error);
    process.exit(1);
  }
}

// Run the script
main();
