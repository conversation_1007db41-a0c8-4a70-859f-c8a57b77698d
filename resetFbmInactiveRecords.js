// Script to reset FBM inactive records
// Updates t_sdasins records based on v_sdasins_fbm_inv0_mps_inactive view
// Sets fbm_uploaded_at to null, min_weight to 1, max_weight to 2

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing required environment variables: SUPABASE_URL or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Reset FBM inactive records based on the view
 * @param {Object} supabaseClient - Optional Supabase client (for use from adminServer)
 * @returns {Object} Result object with success status and details
 */
export async function resetFbmInactiveRecords(supabaseClient = null) {
  const client = supabaseClient || supabase;
  
  try {
    console.log('🔄 Starting FBM inactive records reset...');
    
    // First, get the records from the view to see what we're working with
    console.log('📊 Querying v_sdasins_fbm_inv0_mps_inactive view...');
    
    const { data: viewRecords, error: viewError } = await client
      .from('v_sdasins_fbm_inv0_mps_inactive')
      .select('*');
    
    if (viewError) {
      console.error('❌ Error querying view:', viewError.message);
      return {
        success: false,
        error: `Failed to query view: ${viewError.message}`,
        recordsFound: 0,
        recordsUpdated: 0
      };
    }
    
    if (!viewRecords || viewRecords.length === 0) {
      console.log('ℹ️ No records found in v_sdasins_fbm_inv0_mps_inactive view');
      return {
        success: true,
        message: 'No records found to update',
        recordsFound: 0,
        recordsUpdated: 0
      };
    }
    
    console.log(`📋 Found ${viewRecords.length} records in view`);
    
    // Extract the sdasin IDs from the view records
    // The view uses 'sdasin_id' as the identifier field
    const sdasinIds = viewRecords.map(record => record.sdasin_id);

    if (!sdasinIds || sdasinIds.length === 0) {
      console.log('🔍 View record structure:', Object.keys(viewRecords[0]));
      return {
        success: false,
        error: 'No valid sdasin IDs found in view records',
        recordsFound: viewRecords.length,
        recordsUpdated: 0,
        viewStructure: Object.keys(viewRecords[0])
      };
    }
    
    console.log(`🎯 Updating ${sdasinIds.length} t_sdasins records...`);
    
    // Update the t_sdasins records
    const { data: updateResult, error: updateError } = await client
      .from('t_sdasins')
      .update({
        fbm_uploaded_at: null,
        min_weight: 1,
        max_weight: 2
      })
      .in('id', sdasinIds)
      .select('id');
    
    if (updateError) {
      console.error('❌ Error updating t_sdasins records:', updateError.message);
      return {
        success: false,
        error: `Failed to update records: ${updateError.message}`,
        recordsFound: viewRecords.length,
        recordsUpdated: 0
      };
    }
    
    const updatedCount = updateResult ? updateResult.length : 0;
    
    console.log(`✅ Successfully updated ${updatedCount} t_sdasins records`);
    console.log('📝 Changes made:');
    console.log('   - fbm_uploaded_at set to NULL');
    console.log('   - min_weight set to 1');
    console.log('   - max_weight set to 2');
    
    return {
      success: true,
      message: `Successfully updated ${updatedCount} t_sdasins records`,
      recordsFound: viewRecords.length,
      recordsUpdated: updatedCount,
      details: {
        changes: {
          fbm_uploaded_at: 'NULL',
          min_weight: 1,
          max_weight: 2
        }
      }
    };
    
  } catch (error) {
    console.error('❌ Exception in resetFbmInactiveRecords:', error);
    return {
      success: false,
      error: error.message,
      recordsFound: 0,
      recordsUpdated: 0
    };
  }
}

// If running directly (not imported)
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const isMainModule = process.argv[1] === __filename;

if (isMainModule) {
  console.log('🚀 Running FBM inactive records reset script...');

  resetFbmInactiveRecords()
    .then(result => {
      if (result.success) {
        console.log(`\n✅ Script completed successfully!`);
        console.log(`📊 Records found: ${result.recordsFound}`);
        console.log(`🔄 Records updated: ${result.recordsUpdated}`);
      } else {
        console.log(`\n❌ Script failed: ${result.error}`);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('\n💥 Script crashed:', error);
      process.exit(1);
    });
}

export default resetFbmInactiveRecords;
