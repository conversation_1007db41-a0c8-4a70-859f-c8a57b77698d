import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function applyNewParsingRules() {
  try {
    console.log('Applying new parsing rules to existing records...\n');

    // Handle new special cases
    await handleNewSpecialCases();

    // Remove filler text from existing records
    await removeFillerText();

    console.log('\nNew parsing rules applied successfully!');

  } catch (error) {
    console.error('Error:', error);
  }
}

async function handleNewSpecialCases() {
  console.log('Handling new special cases...');

  const newSpecialCases = [
    { contains: 'Starter Set', replacement: 'XXXX Accessory' },
    { contains: 'Disc Storage Rack', replacement: 'XXXX Accessory' },
    { contains: 'Golf Basket', replacement: 'XXXX Basket' }
  ];

  for (const specialCase of newSpecialCases) {
    // First get records that match and don't have raw_notes set
    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .like('notes', `%${specialCase.contains}%`)
      .is('raw_notes', null);

    if (fetchError) {
      console.error(`Error fetching records for ${specialCase.contains}:`, fetchError);
      continue;
    }

    // Update each record to preserve original notes in raw_notes
    for (const record of matchingRecords || []) {
      const { error } = await supabase
        .from('t_sdasins')
        .update({ 
          notes: specialCase.replacement,
          raw_notes: record.notes 
        })
        .eq('id', record.id);

      if (error) {
        console.error(`Error updating record ${record.id}:`, error);
      }
    }

    console.log(`Updated ${matchingRecords?.length || 0} records containing "${specialCase.contains}"`);
  }

  console.log('New special cases handled.\n');
}

async function removeFillerText() {
  console.log('Removing filler text from existing records...');

  // Filler phrases to remove (longest to shortest)
  const fillerPhrases = [
    'Supercolor Gallery Fire Mid-Range Golf Disc',
    'Long and Fast Disc Golf Driver',
    'Disc Golf Distance Driver',
    'Understable Disc Golf Driver',
    'Beginner Disc Golf Frisbee',
    'Disc Golf Fairway Driver',
    'Max Distance Driver',
    'Disc Golf Driver'
  ];

  let totalUpdated = 0;

  for (const phrase of fillerPhrases) {
    console.log(`Removing "${phrase}"...`);

    // Get records that contain this phrase
    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .like('notes', `%${phrase}%`)
      .not('notes', 'like', 'XXXX%'); // Skip already processed records

    if (fetchError) {
      console.error(`Error fetching records for "${phrase}":`, fetchError);
      continue;
    }

    let phraseUpdated = 0;

    for (const record of matchingRecords || []) {
      // Remove the phrase from notes
      const updatedNotes = record.notes.replace(new RegExp(escapeRegex(phrase), 'gi'), '').trim();
      
      // Clean up extra spaces and punctuation
      const cleanedNotes = updatedNotes
        .replace(/\s+/g, ' ')
        .replace(/^\s*[-|,]\s*/, '')
        .replace(/\s*[-|,]\s*$/, '')
        .trim();

      const updateData = {
        notes: cleanedNotes
      };

      // Store original notes in raw_notes if not already stored
      if (!record.raw_notes) {
        updateData.raw_notes = record.notes;
      }

      const { error } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', record.id);

      if (error) {
        console.error(`Error updating record ${record.id}:`, error);
      } else {
        phraseUpdated++;
        totalUpdated++;
      }
    }

    console.log(`  Removed from ${phraseUpdated} records`);
  }

  console.log(`\nTotal records updated with filler text removal: ${totalUpdated}`);
}

function escapeRegex(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Run the script
applyNewParsingRules();
