import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function applyAdditionalParsingRules() {
  try {
    console.log('Applying additional parsing rules...\n');

    // Handle new brand special cases first
    await handleNewBrandSpecialCases();

    // Remove additional filler text from existing records
    await removeAdditionalFillerText();

    console.log('\nAdditional parsing rules applied successfully!');

  } catch (error) {
    console.error('Error:', error);
  }
}

async function handleNewBrandSpecialCases() {
  console.log('Handling new brand special cases...');

  const newBrandCases = [
    { contains: 'Rogue Iron', replacement: 'XXXX Rogue Iron' },
    { contains: 'ITHWIU', replacement: 'XXXX ITHWIU' },
    { contains: 'Yikun', replacement: 'XXXX <PERSON>kun' }
  ];

  for (const brandCase of newBrandCases) {
    // Get records that contain this brand and don't start with XXXX
    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .like('notes', `%${brandCase.contains}%`)
      .not('notes', 'like', 'XXXX%');

    if (fetchError) {
      console.error(`Error fetching records for ${brandCase.contains}:`, fetchError);
      continue;
    }

    let brandUpdated = 0;

    for (const record of matchingRecords || []) {
      const updateData = {
        notes: brandCase.replacement
      };

      // Store original notes in raw_notes if not already stored
      if (!record.raw_notes) {
        updateData.raw_notes = record.notes;
      }

      const { error } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', record.id);

      if (error) {
        console.error(`Error updating record ${record.id}:`, error);
      } else {
        brandUpdated++;
        if (brandUpdated <= 5) { // Show first 5 updates
          console.log(`  Updated ID ${record.id}: ${brandCase.replacement}`);
        }
      }
    }

    console.log(`Updated ${brandUpdated} records containing "${brandCase.contains}"`);
  }

  console.log('New brand special cases handled.\n');
}

async function removeAdditionalFillerText() {
  console.log('Removing additional filler text from existing records...');

  // Additional filler phrases to remove (longest to shortest)
  const additionalFillerPhrases = [
    'Overstable Frisbee Golf Putt and Approach Disc',
    'Discs Mid-Range Disc Golf Disc',
    'Distance Driver Golf Disc',
    'Fairway Driver Disc Golf',
    'Driver Disc Golf Disc',
    'Rolling Mid-Range Disc',
    'Golf Disc Driver',
    'Disc Golf Midrange',
    'Disc Golf Putter',
    'Lightweight & Accurate',
    'Slightly Overstable',
    'Floats in Water'
  ];

  let totalUpdated = 0;

  for (const phrase of additionalFillerPhrases) {
    console.log(`Removing "${phrase}"...`);

    // Get records that contain this phrase
    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .like('notes', `%${phrase}%`)
      .not('notes', 'like', 'XXXX%'); // Skip already processed records

    if (fetchError) {
      console.error(`Error fetching records for "${phrase}":`, fetchError);
      continue;
    }

    let phraseUpdated = 0;

    for (const record of matchingRecords || []) {
      // Remove the phrase from notes
      const updatedNotes = record.notes.replace(new RegExp(escapeRegex(phrase), 'gi'), '').trim();
      
      // Clean up extra spaces and punctuation
      const cleanedNotes = updatedNotes
        .replace(/\s+/g, ' ')
        .replace(/^\s*[-|,]\s*/, '')
        .replace(/\s*[-|,]\s*$/, '')
        .trim();

      const updateData = {
        notes: cleanedNotes
      };

      // Store original notes in raw_notes if not already stored
      if (!record.raw_notes) {
        updateData.raw_notes = record.notes;
      }

      const { error } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', record.id);

      if (error) {
        console.error(`Error updating record ${record.id}:`, error);
      } else {
        phraseUpdated++;
        totalUpdated++;
        if (phraseUpdated <= 3) { // Show first 3 updates per phrase
          console.log(`  Updated ID ${record.id}: removed "${phrase}"`);
        }
      }
    }

    console.log(`  Removed from ${phraseUpdated} records`);
  }

  console.log(`\nTotal records updated with additional filler text removal: ${totalUpdated}`);
}

function escapeRegex(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Run the script
applyAdditionalParsingRules();
