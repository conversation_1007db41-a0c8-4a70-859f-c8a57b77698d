-- Create view to reconcile t_inv_sdasin quantities with Amazon Active Listings Report
-- This view compares local inventory quantities with Amazon listing quantities

CREATE OR REPLACE VIEW public.v_reconcile_inv_sdasin_to_amaz AS
WITH amazon_disc_skus AS (
  -- Get all Amazon listings that start with 'Disc_' but exclude FBA SKUs
  SELECT
    seller_sku,
    quantity as amazon_qty,
    asin1,
    item_name,
    fulfillment_channel,
    report_date,
    created_at as amazon_import_date
  FROM it_amaz_active_listings_report
  WHERE seller_sku LIKE 'Disc_%'
    AND seller_sku NOT LIKE '%_FBA'
    AND seller_sku NOT LIKE '%_fba'
),
local_inventory AS (
  -- Get local inventory data with SDASIN info, excluding FBA SKUs
  SELECT
    ts.id as sdasin_id,
    ts.fbm_sku,
    ts.asin,
    ts.parent_asin,
    ts.min_weight,
    ts.max_weight,
    inv.available_quantity as local_qty,
    inv.updated_at as local_updated_at
  FROM t_sdasins ts
  JOIN t_inv_sdasin inv ON ts.id = inv.id
  WHERE ts.fbm_sku IS NOT NULL
    AND ts.fbm_sku LIKE 'Disc_%'
    AND ts.fbm_sku NOT LIKE '%_FBA'
    AND ts.fbm_sku NOT LIKE '%_fba'
)
SELECT 
  -- Identification fields
  COALESCE(li.sdasin_id, -1) as sdasin_id,
  COALESCE(li.fbm_sku, amz.seller_sku) as sku,
  COALESCE(li.asin, amz.asin1) as asin,
  li.parent_asin,
  
  -- Quantity comparison
  COALESCE(li.local_qty, 0) as local_qty,
  COALESCE(amz.amazon_qty, 0) as amazon_qty,
  COALESCE(li.local_qty, 0) - COALESCE(amz.amazon_qty, 0) as quantity_difference,
  
  -- Status categorization
  CASE 
    WHEN li.sdasin_id IS NULL AND amz.seller_sku IS NOT NULL THEN 'Amazon Only'
    WHEN li.sdasin_id IS NOT NULL AND amz.seller_sku IS NULL THEN 'Local Only'
    WHEN COALESCE(li.local_qty, 0) = COALESCE(amz.amazon_qty, 0) THEN 'Match'
    WHEN COALESCE(li.local_qty, 0) > COALESCE(amz.amazon_qty, 0) THEN 'Local Higher'
    WHEN COALESCE(li.local_qty, 0) < COALESCE(amz.amazon_qty, 0) THEN 'Amazon Higher'
    ELSE 'Unknown'
  END as status,
  
  -- Additional Amazon info
  amz.item_name as amazon_item_name,
  amz.fulfillment_channel,
  amz.report_date as amazon_report_date,
  
  -- Additional local info
  li.min_weight,
  li.max_weight,
  li.local_updated_at,
  amz.amazon_import_date,
  
  -- Priority flag for attention
  CASE 
    WHEN li.sdasin_id IS NULL AND amz.seller_sku IS NOT NULL THEN 1  -- Amazon only (high priority)
    WHEN ABS(COALESCE(li.local_qty, 0) - COALESCE(amz.amazon_qty, 0)) > 0 THEN 2  -- Quantity mismatch (medium priority)
    ELSE 3  -- Match (low priority)
  END as priority

FROM local_inventory li
FULL OUTER JOIN amazon_disc_skus amz ON li.fbm_sku = amz.seller_sku

WHERE 
  -- Show records that need attention:
  -- 1. Amazon listings without local inventory (Amazon Only)
  (li.sdasin_id IS NULL AND amz.seller_sku IS NOT NULL)
  OR
  -- 2. Quantity mismatches between local and Amazon
  (li.sdasin_id IS NOT NULL AND amz.seller_sku IS NOT NULL 
   AND COALESCE(li.local_qty, 0) != COALESCE(amz.amazon_qty, 0))

ORDER BY 
  priority ASC,  -- Show high priority items first
  ABS(COALESCE(li.local_qty, 0) - COALESCE(amz.amazon_qty, 0)) DESC,  -- Then by largest quantity differences
  COALESCE(li.fbm_sku, amz.seller_sku);

-- Add comments for documentation
COMMENT ON VIEW public.v_reconcile_inv_sdasin_to_amaz IS 'Reconciliation view comparing local t_inv_sdasin quantities with Amazon Active Listings Report quantities for Disc_ SKUs (excluding FBA SKUs)';

-- Create indexes on the underlying tables if they don't exist (for better performance)
-- Note: These will only be created if the indexes don't already exist

-- Index on Amazon table for seller_sku lookups (excluding FBA)
CREATE INDEX IF NOT EXISTS idx_amaz_active_listings_seller_sku_disc_fbm
ON public.it_amaz_active_listings_report(seller_sku)
WHERE seller_sku LIKE 'Disc_%' AND seller_sku NOT LIKE '%_FBA' AND seller_sku NOT LIKE '%_fba';

-- Index on t_sdasins for fbm_sku lookups (excluding FBA)
CREATE INDEX IF NOT EXISTS idx_sdasins_fbm_sku_disc_fbm
ON public.t_sdasins(fbm_sku)
WHERE fbm_sku LIKE 'Disc_%' AND fbm_sku NOT LIKE '%_FBA' AND fbm_sku NOT LIKE '%_fba';

-- Print confirmation
DO $$
BEGIN
    RAISE NOTICE 'View v_reconcile_inv_sdasin_to_amaz created successfully';
    RAISE NOTICE 'This view shows:';
    RAISE NOTICE '1. Amazon Disc_ SKUs (FBM only) that are not in local inventory (Amazon Only)';
    RAISE NOTICE '2. SKUs where local and Amazon quantities do not match';
    RAISE NOTICE '3. FBA SKUs are excluded as they are managed by Amazon';
    RAISE NOTICE 'Results are ordered by priority and quantity difference';
END $$;
