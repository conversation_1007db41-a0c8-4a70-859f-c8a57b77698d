-- Updated functions to handle order_through_mps_id redirect logic for vendor OSL mapping

-- Function to find a matching order sheet line using manufacturer weight with redirect logic
CREATE OR REPLACE FUNCTION find_matching_osl_by_mfg_weight_with_redirect(
    mps_id_param INTEGER,
    color_id_param INTEGER,
    weight_mfg_param NUMERIC
)
RETURNS TABLE (
    osl_id INTEGER,
    redirect_info TEXT
) AS $$
DECLARE
    rounded_weight NUMERIC;
    initial_osl_id INTEGER;
    initial_mps_id INTEGER;
    redirect_mps_id INTEGER;
    final_osl_id INTEGER;
    redirect_text TEXT := '';
BEGIN
    -- Round the weight to the nearest integer using standard rounding rules
    rounded_weight := ROUND(weight_mfg_param);

    -- First, try to find a matching OSL with the original MPS
    SELECT id INTO initial_osl_id
    FROM t_order_sheet_lines
    WHERE mps_id = mps_id_param
      AND (color_id = color_id_param OR color_id = 23)
      AND rounded_weight >= min_weight
      AND rounded_weight <= max_weight
    LIMIT 1;

    -- If we found an OSL, check if its MPS has order_through_mps_id set
    IF initial_osl_id IS NOT NULL THEN
        -- Get the MPS details for the found OSL
        SELECT mps_id INTO initial_mps_id FROM t_order_sheet_lines WHERE id = initial_osl_id;

        -- Check if this MPS has order_through_mps_id set
        SELECT order_through_mps_id INTO redirect_mps_id
        FROM t_mps
        WHERE id = initial_mps_id AND order_through_mps_id IS NOT NULL;

        -- If redirect is needed, find OSL in the redirect MPS
        IF redirect_mps_id IS NOT NULL THEN
            redirect_text := 'Redirected from MPS ' || initial_mps_id || ' to MPS ' || redirect_mps_id;

            -- Find matching OSL in the redirect MPS
            SELECT id INTO final_osl_id
            FROM t_order_sheet_lines
            WHERE mps_id = redirect_mps_id
              AND (color_id = color_id_param OR color_id = 23)
              AND rounded_weight >= min_weight
              AND rounded_weight <= max_weight
            LIMIT 1;

            -- Return the redirect OSL if found
            IF final_osl_id IS NOT NULL THEN
                RETURN QUERY SELECT final_osl_id, redirect_text;
                RETURN;
            ELSE
                -- No matching OSL in redirect MPS
                redirect_text := redirect_text || ', but no matching OSL found in redirect MPS';
                RETURN QUERY SELECT NULL::INTEGER, redirect_text;
                RETURN;
            END IF;
        ELSE
            -- No redirect needed, return the original OSL
            RETURN QUERY SELECT initial_osl_id, 'No redirect needed'::TEXT;
            RETURN;
        END IF;
    ELSE
        -- No initial OSL found
        RETURN QUERY SELECT NULL::INTEGER, 'No matching OSL found for original MPS'::TEXT;
        RETURN;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to find a matching order sheet line using actual weight with redirect logic
CREATE OR REPLACE FUNCTION find_matching_osl_with_redirect(
    mps_id_param INTEGER,
    color_id_param INTEGER,
    weight_param NUMERIC
)
RETURNS TABLE (
    osl_id INTEGER,
    redirect_info TEXT
) AS $$
DECLARE
    rounded_weight NUMERIC;
    initial_osl_id INTEGER;
    initial_mps_id INTEGER;
    redirect_mps_id INTEGER;
    final_osl_id INTEGER;
    redirect_text TEXT := '';
    decimal_part NUMERIC;
BEGIN
    -- Custom rounding logic: X.5 and up rounds to X+1, X.4 and down rounds to X
    decimal_part := weight_param - FLOOR(weight_param);

    IF decimal_part >= 0.5 THEN
        rounded_weight := CEIL(weight_param);
    ELSE
        rounded_weight := FLOOR(weight_param);
    END IF;

    -- First, try to find a matching OSL with the original MPS
    SELECT id INTO initial_osl_id
    FROM t_order_sheet_lines
    WHERE mps_id = mps_id_param
      AND (color_id = color_id_param OR color_id = 23)
      AND rounded_weight >= min_weight
      AND rounded_weight <= max_weight
    LIMIT 1;

    -- If we found an OSL, check if its MPS has order_through_mps_id set
    IF initial_osl_id IS NOT NULL THEN
        -- Get the MPS details for the found OSL
        SELECT mps_id INTO initial_mps_id FROM t_order_sheet_lines WHERE id = initial_osl_id;

        -- Check if this MPS has order_through_mps_id set
        SELECT order_through_mps_id INTO redirect_mps_id
        FROM t_mps
        WHERE id = initial_mps_id AND order_through_mps_id IS NOT NULL;

        -- If redirect is needed, find OSL in the redirect MPS
        IF redirect_mps_id IS NOT NULL THEN
            redirect_text := 'Redirected from MPS ' || initial_mps_id || ' to MPS ' || redirect_mps_id;

            -- Find matching OSL in the redirect MPS
            SELECT id INTO final_osl_id
            FROM t_order_sheet_lines
            WHERE mps_id = redirect_mps_id
              AND (color_id = color_id_param OR color_id = 23)
              AND rounded_weight >= min_weight
              AND rounded_weight <= max_weight
            LIMIT 1;

            -- Return the redirect OSL if found
            IF final_osl_id IS NOT NULL THEN
                RETURN QUERY SELECT final_osl_id, redirect_text;
                RETURN;
            ELSE
                -- No matching OSL in redirect MPS
                redirect_text := redirect_text || ', but no matching OSL found in redirect MPS';
                RETURN QUERY SELECT NULL::INTEGER, redirect_text;
                RETURN;
            END IF;
        ELSE
            -- No redirect needed, return the original OSL
            RETURN QUERY SELECT initial_osl_id, 'No redirect needed'::TEXT;
            RETURN;
        END IF;
    ELSE
        -- No initial OSL found
        RETURN QUERY SELECT NULL::INTEGER, 'No matching OSL found for original MPS'::TEXT;
        RETURN;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Test the redirect functions with the example case
-- Disc 408210 should find OSL 11602 initially, then redirect to MPS 19687 and find OSL 18756

-- Example test queries:
-- SELECT * FROM find_matching_osl_by_mfg_weight_with_redirect(16912, 7, 174);
-- SELECT * FROM find_matching_osl_with_redirect(16912, 7, 174);

-- Function to find a matching order sheet line using actual weight with redirect logic
CREATE OR REPLACE FUNCTION find_matching_osl_with_redirect(
    mps_id_param INTEGER,
    color_id_param INTEGER,
    weight_param NUMERIC
)
RETURNS TABLE (
    osl_id INTEGER,
    redirect_info TEXT
) AS $$
DECLARE
    rounded_weight NUMERIC;
    initial_osl_id INTEGER;
    initial_mps_id INTEGER;
    redirect_mps_id INTEGER;
    final_osl_id INTEGER;
    redirect_text TEXT := '';
    decimal_part NUMERIC;
BEGIN
    -- Custom rounding logic: X.5 and up rounds to X+1, X.4 and down rounds to X
    decimal_part := weight_param - FLOOR(weight_param);
    
    IF decimal_part >= 0.5 THEN
        rounded_weight := CEIL(weight_param);
    ELSE
        rounded_weight := FLOOR(weight_param);
    END IF;
    
    -- First, try to find a matching OSL with the original MPS
    SELECT id INTO initial_osl_id
    FROM t_order_sheet_lines
    WHERE mps_id = mps_id_param
      AND (color_id = color_id_param OR color_id = 23)
      AND rounded_weight >= min_weight
      AND rounded_weight <= max_weight
    LIMIT 1;
    
    -- If we found an OSL, check if its MPS has order_through_mps_id set
    IF initial_osl_id IS NOT NULL THEN
        -- Get the MPS details for the found OSL
        SELECT mps_id INTO initial_mps_id FROM t_order_sheet_lines WHERE id = initial_osl_id;
        
        -- Check if this MPS has order_through_mps_id set
        SELECT order_through_mps_id INTO redirect_mps_id 
        FROM t_mps 
        WHERE id = initial_mps_id AND order_through_mps_id IS NOT NULL;
        
        -- If redirect is needed, find OSL in the redirect MPS
        IF redirect_mps_id IS NOT NULL THEN
            redirect_text := 'Redirected from MPS ' || initial_mps_id || ' to MPS ' || redirect_mps_id;
            
            -- Find matching OSL in the redirect MPS
            SELECT id INTO final_osl_id
            FROM t_order_sheet_lines
            WHERE mps_id = redirect_mps_id
              AND (color_id = color_id_param OR color_id = 23)
              AND rounded_weight >= min_weight
              AND rounded_weight <= max_weight
            LIMIT 1;
            
            -- Return the redirect OSL if found
            IF final_osl_id IS NOT NULL THEN
                RETURN QUERY SELECT final_osl_id, redirect_text;
                RETURN;
            ELSE
                -- No matching OSL in redirect MPS
                redirect_text := redirect_text || ', but no matching OSL found in redirect MPS';
                RETURN QUERY SELECT NULL::INTEGER, redirect_text;
                RETURN;
            END IF;
        ELSE
            -- No redirect needed, return the original OSL
            RETURN QUERY SELECT initial_osl_id, 'No redirect needed'::TEXT;
            RETURN;
        END IF;
    ELSE
        -- No initial OSL found
        RETURN QUERY SELECT NULL::INTEGER, 'No matching OSL found for original MPS'::TEXT;
        RETURN;
    END IF;
END;
$$ LANGUAGE plpgsql;
