import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client with your actual credentials
const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM3ODY1NDEsImV4cCI6MjA0OTM2MjU0MX0.MtBXMZt7rCqXd6c9clhrNoVtfOxEilX2C8oboMceOGg';
const supabase = createClient(supabaseUrl, supabaseKey);

const checkAndUpdateDiscOsl = async () => {
  try {
    // First, get the order sheet line details
    console.log('Fetching order sheet line 16890...');
    const { data: oslData, error: oslError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id, min_weight, max_weight, color_id')
      .eq('id', 16890)
      .maybeSingle();
      
    if (oslError) {
      console.error('Error fetching order sheet line:', oslError);
      return;
    }
    
    if (!oslData) {
      console.log('Order sheet line 16890 not found.');
      return;
    }
    
    console.log('Order sheet line found:', oslData);
    
    // Now get the disc details
    console.log('\nFetching disc 421349...');
    const { data: discData, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, color_id, sold_date, order_sheet_line_id')
      .eq('id', 421349)
      .maybeSingle();
      
    if (discError) {
      console.error('Error fetching disc:', discError);
      return;
    }
    
    if (!discData) {
      console.log('Disc 421349 not found.');
      return;
    }
    
    console.log('Disc found:', discData);
    
    // Check if they should match
    console.log('\nChecking if disc should match with order sheet line...');
    const mpsMatch = discData.mps_id === oslData.mps_id;
    const weightMatch = discData.weight >= oslData.min_weight && discData.weight <= oslData.max_weight;
    const colorMatch = oslData.color_id === 23 || discData.color_id === oslData.color_id;
    const notSold = discData.sold_date === null;
    
    console.log(`MPS Match: ${mpsMatch} (Disc MPS: ${discData.mps_id}, OSL MPS: ${oslData.mps_id})`);
    console.log(`Weight Match: ${weightMatch} (Disc Weight: ${discData.weight}, OSL Min: ${oslData.min_weight}, OSL Max: ${oslData.max_weight})`);
    console.log(`Color Match: ${colorMatch} (Disc Color: ${discData.color_id}, OSL Color: ${oslData.color_id})`);
    console.log(`Not Sold: ${notSold} (Sold Date: ${discData.sold_date})`);
    
    const shouldMatch = mpsMatch && weightMatch && colorMatch && notSold;
    console.log(`Should Match: ${shouldMatch}`);
    
    // If they should match, update the disc
    if (shouldMatch) {
      console.log('\nUpdating disc 421349 with order_sheet_line_id 16890...');
      const { data: updateData, error: updateError } = await supabase
        .from('t_discs')
        .update({ order_sheet_line_id: 16890 })
        .eq('id', 421349);
        
      if (updateError) {
        console.error('Error updating disc:', updateError);
        return;
      }
      
      console.log('Disc updated successfully!');
      
      // Verify the update
      const { data: verifyData, error: verifyError } = await supabase
        .from('t_discs')
        .select('id, order_sheet_line_id')
        .eq('id', 421349)
        .maybeSingle();
        
      if (verifyError) {
        console.error('Error verifying update:', verifyError);
        return;
      }
      
      console.log('Verification:', verifyData);
    } else {
      console.log('\nDisc should not match with this order sheet line. No update performed.');
    }
    
    // Check if there are any task queue entries for this disc
    console.log('\nChecking task queue for this disc...');
    const { data: taskData, error: taskError } = await supabase
      .from('t_task_queue')
      .select('id, task_type, payload, status, result, created_at')
      .eq('task_type', 'match_disc_to_osl')
      .contains('payload', { id: 421349 })
      .order('created_at', { ascending: false })
      .limit(5);
      
    if (taskError) {
      console.error('Error checking task queue:', taskError);
      return;
    }
    
    if (!taskData || taskData.length === 0) {
      console.log('No task queue entries found for this disc.');
    } else {
      console.log(`Found ${taskData.length} task queue entries:`);
      taskData.forEach(task => {
        console.log(`- Task ${task.id}: ${task.task_type}, Status: ${task.status}, Created: ${task.created_at}`);
        console.log(`  Payload: ${JSON.stringify(task.payload)}`);
        console.log(`  Result: ${JSON.stringify(task.result)}`);
      });
    }
    
    // Try to manually run the find_matching_osl function
    console.log('\nManually running find_matching_osl function...');
    try {
      const { data: fnData, error: fnError } = await supabase.rpc(
        'find_matching_osl',
        {
          mps_id_param: discData.mps_id,
          color_id_param: discData.color_id,
          weight_param: discData.weight
        }
      );
      
      if (fnError) {
        console.error('Error running find_matching_osl function:', fnError);
      } else {
        console.log('Function result:', fnData);
      }
    } catch (e) {
      console.log('Error calling function:', e.message);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
};

checkAndUpdateDiscOsl();
