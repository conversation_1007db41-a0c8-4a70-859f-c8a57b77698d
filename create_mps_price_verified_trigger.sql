-- Function to enqueue a task when cost_price_reviewed_at is updated
CREATE OR REPLACE FUNCTION fn_enqueue_mps_price_verified_task()
RETURNS TRIGGER AS $$
BEGIN
    -- Only enqueue a task if cost_price_reviewed_at has been updated
    IF OLD.cost_price_reviewed_at IS DISTINCT FROM NEW.cost_price_reviewed_at THEN
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'mps_price_verified_try_upload_osls',
            jsonb_build_object('id', NEW.id),
            'pending',
            NOW(),
            NOW()
        );
        
        -- Log the task creation
        INSERT INTO t_error_logs(error_message, created_at, context, created_by)
        VALUES (
            'Enqueued mps_price_verified_try_upload_osls task for MPS ID ' || NEW.id,
            NOW(),
            'fn_enqueue_mps_price_verified_task',
            'trigger'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS tr_enqueue_mps_price_verified_task ON t_mps;

CREATE TRIGGER tr_enqueue_mps_price_verified_task
AFTER UPDATE OF cost_price_reviewed_at ON t_mps
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_mps_price_verified_task();

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'MPS price verified task enqueuer function and trigger created.';
END $$;
