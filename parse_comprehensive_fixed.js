import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Cache for lookup data
let brandsCache = {};
let allMoldsCache = [];
let allPlasticsCache = [];
let moldsCache = {};
let plasticsCache = {};
let stampsCache = [];

// Brand mappings for vendor names that don't match exactly
const brandMappings = {
    'Westside Discs': 'Westside',
    'Dynamic Discs': 'Dynamic Discs',
    'Latitude 64': 'Latitude 64',
    'Discmania': 'Discmania',
    'Kastaplast': 'Kastaplast',
    'Active': 'Discmania Active'
};

// Plastic name mappings for vendor names that don't match database names
const plasticMappings = {
    'Gold': 'Gold Line',
    'Tournament': 'Tournament',
    'Lucid Moonshine': 'Lucid Moonshine Glow',
    'Classic Moonshine': 'Classic Soft Moonshine Glow',
    'Prime Moonshine': 'Prime Moonshine Glow',
    'BioGold': 'Gold Line Bio',
    'Active': 'Base Level',
    'Active Premium': 'Premium',
    'Active Premium Glow': 'Premium Glow',
    'Classic': 'Classic (Hard)',
    'Classic Burst': 'Classic (Hard) Burst',
    'Classic Blend': 'Classic Blend',
    'Classic Blend Burst': 'Classic Blend Burst',
    'Classic Soft': 'Classic Soft',
    'Classic Soft Burst': 'Classic Soft Burst'
};

async function loadLookupData() {
    console.log('Loading lookup data...');
    
    // Load all brands
    const { data: brands, error: brandsError } = await supabase
        .from('t_brands')
        .select('id, brand');
    
    if (brandsError) {
        throw new Error(`Error loading brands: ${brandsError.message}`);
    }
    
    // Create brand lookup
    brands.forEach(brand => {
        brandsCache[brand.brand] = brand.id;
    });
    
    // Load all molds with their brand_id
    const { data: molds, error: moldsError } = await supabase
        .from('t_molds')
        .select('mold, brand_id');
    
    if (moldsError) {
        throw new Error(`Error loading molds: ${moldsError.message}`);
    }
    
    // Store all molds and group by brand_id
    allMoldsCache = molds.map(m => ({ mold: m.mold, brand_id: m.brand_id }));
    molds.forEach(mold => {
        if (!moldsCache[mold.brand_id]) {
            moldsCache[mold.brand_id] = [];
        }
        moldsCache[mold.brand_id].push(mold.mold);
    });
    
    // Load all plastics with their brand_id
    const { data: plastics, error: plasticsError } = await supabase
        .from('t_plastics')
        .select('plastic, brand_id');
    
    if (plasticsError) {
        throw new Error(`Error loading plastics: ${plasticsError.message}`);
    }
    
    // Store all plastics and group by brand_id
    allPlasticsCache = plastics.map(p => ({ plastic: p.plastic, brand_id: p.brand_id }));
    plastics.forEach(plastic => {
        if (!plasticsCache[plastic.brand_id]) {
            plasticsCache[plastic.brand_id] = [];
        }
        plasticsCache[plastic.brand_id].push(plastic.plastic);
    });
    
    // Load all stamps (not brand-specific)
    const { data: stamps, error: stampsError } = await supabase
        .from('t_stamps')
        .select('stamp');
    
    if (stampsError) {
        throw new Error(`Error loading stamps: ${stampsError.message}`);
    }
    
    stampsCache = stamps.map(s => s.stamp);
    
    console.log(`Loaded ${brands.length} brands, ${molds.length} molds, ${plastics.length} plastics, ${stampsCache.length} stamps`);
}

function findPlasticWithMapping(plasticName, brandId) {
    // First try with mapping
    const mappedPlastic = plasticMappings[plasticName] || plasticName;
    
    // Check brand-specific plastics first
    const brandPlastics = plasticsCache[brandId] || [];
    
    // Try exact match with mapped name
    if (brandPlastics.includes(mappedPlastic)) {
        return mappedPlastic;
    }
    
    // Try exact match with original name
    if (brandPlastics.includes(plasticName)) {
        return plasticName;
    }
    
    // Try partial matches in brand
    for (const plastic of brandPlastics) {
        if (plastic.includes(mappedPlastic) || mappedPlastic.includes(plastic)) {
            return plastic;
        }
    }
    
    // If not found in brand, try across all brands
    for (const plasticData of allPlasticsCache) {
        if (plasticData.plastic === mappedPlastic) {
            return plasticData.plastic;
        }
    }
    
    for (const plasticData of allPlasticsCache) {
        if (plasticData.plastic === plasticName) {
            return plasticData.plastic;
        }
    }
    
    return null;
}

function findMoldAcrossBrands(moldName) {
    // Sort all molds by length (longest first) to prioritize exact matches
    const sortedMolds = [...allMoldsCache].sort((a, b) => b.mold.length - a.mold.length);
    
    // First try exact match (case-insensitive)
    for (const moldData of sortedMolds) {
        if (moldData.mold.toLowerCase() === moldName.toLowerCase()) {
            return moldData.mold;
        }
    }
    
    // Then try starts with match (case-insensitive)
    for (const moldData of sortedMolds) {
        if (moldName.toLowerCase().startsWith(moldData.mold.toLowerCase() + ' ') || 
            moldName.toLowerCase() === moldData.mold.toLowerCase()) {
            return moldData.mold;
        }
    }
    
    // Then try if mold appears at start (case-insensitive)
    for (const moldData of sortedMolds) {
        if (moldName.toLowerCase().startsWith(moldData.mold.toLowerCase())) {
            return moldData.mold;
        }
    }
    
    // Then try partial match
    for (const moldData of sortedMolds) {
        if (moldData.mold.toLowerCase().includes(moldName.toLowerCase()) || 
            moldName.toLowerCase().includes(moldData.mold.toLowerCase())) {
            return moldData.mold;
        }
    }
    
    // Finally try space-insensitive match (remove spaces and compare)
    const moldNameNoSpaces = moldName.replace(/\s+/g, '').toLowerCase();
    for (const moldData of sortedMolds) {
        const moldNoSpaces = moldData.mold.replace(/\s+/g, '').toLowerCase();
        if (moldNameNoSpaces === moldNoSpaces || 
            moldNameNoSpaces.startsWith(moldNoSpaces) ||
            moldNoSpaces.startsWith(moldNameNoSpaces)) {
            return moldData.mold;
        }
    }
    
    return null;
}

function parseProductTitle(productTitle, brandId) {
    if (!productTitle || typeof productTitle !== 'string') {
        return { mold: null, plastic: null, stamp: null };
    }
    
    const title = productTitle.trim();
    
    // Skip non-disc products - including anything with DyeMax
    if (title.includes('Set') || title.includes('Mystery') || title.includes('Box') || 
        title.includes('DyeMax') || title.toLowerCase().includes('dyemax')) {
        return { mold: null, plastic: null, stamp: null };
    }
    
    let foundMold = null;
    let foundPlastic = null;
    let foundStamp = 'Stock';
    
    // Get brand plastics and sort by length (longest first)
    const brandPlastics = plasticsCache[brandId] || [];
    const sortedPlastics = [...brandPlastics].sort((a, b) => b.length - a.length);
    
    // Also try mapped plastic names
    const allPossiblePlastics = [...sortedPlastics];
    Object.keys(plasticMappings).forEach(vendorName => {
        if (!allPossiblePlastics.includes(vendorName)) {
            allPossiblePlastics.push(vendorName);
        }
    });
    allPossiblePlastics.sort((a, b) => b.length - a.length);
    
    // Find plastic at the beginning
    for (const plastic of allPossiblePlastics) {
        if (title.startsWith(plastic + ' ') || title === plastic) {
            // Use mapping to get the correct database plastic name
            foundPlastic = findPlasticWithMapping(plastic, brandId);
            break;
        }
    }
    
    if (foundPlastic) {
        // Find the original plastic name that matched (could be vendor name)
        let matchedPlasticName = foundPlastic;
        for (const plastic of allPossiblePlastics) {
            if (title.startsWith(plastic + ' ') || title === plastic) {
                matchedPlasticName = plastic;
                break;
            }
        }
        
        // Remove plastic from title to find mold and stamp
        let remainingTitle = title.substring(matchedPlasticName.length).trim();
        
        // Check if there's a dash indicating a stamp
        const dashIndex = remainingTitle.indexOf(' - ');
        let moldPart = remainingTitle;
        let stampPart = '';
        
        if (dashIndex !== -1) {
            moldPart = remainingTitle.substring(0, dashIndex).trim();
            stampPart = remainingTitle.substring(dashIndex + 3).trim();
        }
        
        // Find mold (try brand-specific first, then cross-brand)
        const brandMolds = moldsCache[brandId] || [];
        const sortedMolds = [...brandMolds].sort((a, b) => b.length - a.length);
        
        // First try exact matches with longer molds first (case-insensitive)
        for (const mold of sortedMolds) {
            if (moldPart.toLowerCase() === mold.toLowerCase()) {
                foundMold = mold;
                break;
            }
        }
        
        // Then try starts with matches (case-insensitive)
        if (!foundMold) {
            for (const mold of sortedMolds) {
                if (moldPart.toLowerCase().startsWith(mold.toLowerCase() + ' ')) {
                    foundMold = mold;
                    break;
                }
            }
        }
        
        // Then try if mold appears at start (case-insensitive)
        if (!foundMold) {
            for (const mold of sortedMolds) {
                if (moldPart.toLowerCase().startsWith(mold.toLowerCase())) {
                    foundMold = mold;
                    break;
                }
            }
        }
        
        // If not found in brand, try cross-brand
        if (!foundMold) {
            foundMold = findMoldAcrossBrands(moldPart);
        }
        
        // Set stamp
        if (stampPart) {
            foundStamp = stampPart;
        } else if (foundMold && moldPart.length > foundMold.length) {
            // Check if there's additional text after mold (without dash)
            // Need to handle case where vendor uses spaces but database doesn't
            let afterMold = '';
            
            // Try to find where the mold ends in the original moldPart
            const moldLower = foundMold.toLowerCase();
            const moldPartLower = moldPart.toLowerCase();
            
            // Find the mold in moldPart (case-insensitive)
            let moldEndIndex = -1;
            if (moldPartLower.startsWith(moldLower)) {
                moldEndIndex = foundMold.length;
            } else {
                // Handle space differences like "Swan 1 Reborn" vs "Swan1 Reborn"
                const moldNoSpaces = moldLower.replace(/\s+/g, '');
                const moldPartNoSpaces = moldPartLower.replace(/\s+/g, '');
                if (moldPartNoSpaces.startsWith(moldNoSpaces)) {
                    // Find where the mold ends in the original string
                    let charCount = 0;
                    let moldCharIndex = 0;
                    for (let i = 0; i < moldPart.length && moldCharIndex < moldLower.length; i++) {
                        if (moldPart[i].toLowerCase() === moldLower[moldCharIndex]) {
                            moldCharIndex++;
                        } else if (moldPart[i] === ' ') {
                            // Skip spaces in moldPart that aren't in mold
                            continue;
                        }
                        charCount = i + 1;
                    }
                    moldEndIndex = charCount;
                }
            }
            
            if (moldEndIndex > 0 && moldEndIndex < moldPart.length) {
                afterMold = moldPart.substring(moldEndIndex).trim();
            }
            
            if (afterMold && afterMold !== '') {
                foundStamp = afterMold;
            }
        }
    }
    
    return {
        mold: foundMold,
        plastic: foundPlastic,
        stamp: foundStamp
    };
}

function parseVariantTitle(variantTitle, brandId) {
    if (!variantTitle || typeof variantTitle !== 'string') {
        return { mold: null, plastic: null, stamp: null };
    }
    
    const title = variantTitle.trim();
    
    // Skip default titles and DyeMax
    if (title === 'Default Title' || title === 'Assorted' || 
        title.includes('DyeMax') || title.toLowerCase().includes('dyemax')) {
        return { mold: null, plastic: null, stamp: null };
    }
    
    // For DyeMax variants like "Fuzion Verdict (Midrange)"
    // Remove type information in parentheses
    const cleanTitle = title.replace(/\s*\([^)]*\)\s*$/, '').trim();
    
    // Use same logic as product title parsing
    return parseProductTitle(cleanTitle, brandId);
}

async function testAllExamples() {
    console.log('Testing all parsing examples including new Classic examples...\n');
    
    try {
        await loadLookupData();
        
        // Test cases with expected results
        const testCases = [
            // New Classic examples
            {
                title: 'Classic Burst Deputy',
                brand: 'Dynamic Discs',
                expected: { plastic: 'Classic (Hard) Burst', mold: 'Deputy', stamp: 'Stock' }
            },
            {
                title: 'Classic Deputy',
                brand: 'Dynamic Discs',
                expected: { plastic: 'Classic (Hard)', mold: 'Deputy', stamp: 'Stock' }
            },
            {
                title: 'Classic Burst Deputy - #TeamDeputy',
                brand: 'Dynamic Discs',
                expected: { plastic: 'Classic (Hard) Burst', mold: 'Deputy', stamp: '#TeamDeputy' }
            },
            // Previous examples
            {
                title: 'Active Maestro',
                brand: 'Active',
                expected: { plastic: 'Base Level', mold: 'Maestro', stamp: 'Stock' }
            },
            {
                title: 'Active Premium Astronaut',
                brand: 'Active',
                expected: { plastic: 'Premium', mold: 'Astronaut', stamp: 'Stock' }
            },
            {
                title: 'Active Premium Glow Shogun',
                brand: 'Active',
                expected: { plastic: 'Premium Glow', mold: 'Shogun', stamp: 'Stock' }
            },
            {
                title: 'BioFuzion EMac Truth Halloween Triple Stamp',
                brand: 'Dynamic Discs',
                expected: { plastic: 'BioFuzion', mold: 'EMAC Truth', stamp: 'Halloween Triple Stamp' }
            },
            {
                title: 'BioGold Ballista Pro',
                brand: 'Dynamic Discs',
                expected: { plastic: 'Gold Line Bio', mold: 'Ballista Pro', stamp: 'Stock' }
            },
            {
                title: 'BT Medium Burst Swan 1 Reborn',
                brand: 'Westside',
                expected: { plastic: 'BT Medium Burst', mold: 'Swan1 Reborn', stamp: 'Stock' }
            },
            {
                title: 'BioFuzion Defender - Chris Clemons 2023',
                brand: 'Dynamic Discs',
                expected: { plastic: 'BioFuzion', mold: 'Defender', stamp: 'Chris Clemons 2023' }
            },
            {
                title: 'K3 Tuff',
                brand: 'Kastaplast',
                expected: { plastic: 'K3', mold: 'Tuff', stamp: 'Stock' }
            },
            {
                title: 'VIP Tide',
                brand: 'Westside',
                expected: { plastic: 'VIP', mold: 'Tide', stamp: 'Stock' }
            },
            // DyeMax test (should be skipped)
            {
                title: 'DyeMax Fuzion Verdict',
                brand: 'Dynamic Discs',
                expected: { plastic: null, mold: null, stamp: null }
            }
        ];
        
        console.log('Test Results:');
        console.log('=============');
        
        let passCount = 0;
        let failCount = 0;
        
        for (const testCase of testCases) {
            // Apply brand mapping
            const mappedBrand = brandMappings[testCase.brand] || testCase.brand;
            const brandId = brandsCache[mappedBrand];
            
            if (!brandId) {
                console.log(`❌ Brand "${testCase.brand}" not found`);
                failCount++;
                continue;
            }
            
            const result = parseProductTitle(testCase.title, brandId);
            
            console.log(`\nTitle: "${testCase.title}" [${testCase.brand}]`);
            console.log(`Expected: Plastic="${testCase.expected.plastic || 'null'}", Mold="${testCase.expected.mold || 'null'}", Stamp="${testCase.expected.stamp || 'null'}"`);
            console.log(`Actual:   Plastic="${result.plastic || 'null'}", Mold="${result.mold || 'null'}", Stamp="${result.stamp || 'null'}"`);
            
            const plasticMatch = result.plastic === testCase.expected.plastic;
            const moldMatch = result.mold === testCase.expected.mold;
            const stampMatch = result.stamp === testCase.expected.stamp;
            
            if (plasticMatch && moldMatch && stampMatch) {
                console.log('✅ PASS');
                passCount++;
            } else {
                console.log('❌ FAIL');
                if (!plasticMatch) console.log(`  - Plastic mismatch`);
                if (!moldMatch) console.log(`  - Mold mismatch`);
                if (!stampMatch) console.log(`  - Stamp mismatch`);
                failCount++;
            }
        }
        
        console.log(`\n=== SUMMARY ===`);
        console.log(`✅ Passed: ${passCount}`);
        console.log(`❌ Failed: ${failCount}`);
        console.log(`Total: ${passCount + failCount}`);
        
        if (failCount === 0) {
            console.log('\n🎉 All tests passed! Ready to run on full table.');
        } else {
            console.log('\n⚠️  Some tests failed. Please fix before running on full table.');
        }
        
    } catch (error) {
        console.error('Test failed:', error);
    }
}

// Run the test
testAllExamples();
