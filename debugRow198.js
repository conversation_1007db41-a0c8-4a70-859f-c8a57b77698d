import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function debugRow198() {
  try {
    console.log('🔍 Debugging Excel row 198 export issue...\n');
    
    // Get all records for excel_row_hint = 198
    const { data: row198Data, error } = await supabase
      .from('v_stats_by_osl_discraft')
      .select('excel_row_hint, excel_column, "order", mold_name, plastic_name, min_weight, max_weight, excel_mapping_key')
      .eq('excel_row_hint', 198)
      .gt('"order"', 0)
      .order('excel_column');
    
    if (error) {
      console.error('❌ Error:', error);
      return;
    }
    
    console.log(`📊 Found ${row198Data.length} records for excel_row_hint = 198 with order > 0:`);
    console.log('='.repeat(80));
    
    row198Data.forEach((record, i) => {
      console.log(`${i+1}. ${record.plastic_name} ${record.mold_name} ${record.min_weight}-${record.max_weight}g`);
      console.log(`   Excel Row: ${record.excel_row_hint}, Column: ${record.excel_column}`);
      console.log(`   Order Quantity: ${record.order}`);
      console.log(`   Mapping Key: ${record.excel_mapping_key}`);
      console.log('');
    });
    
    // Check if multiple records map to the same cell
    const cellMap = {};
    row198Data.forEach(record => {
      const cellAddress = `${record.excel_column}${record.excel_row_hint}`;
      if (!cellMap[cellAddress]) {
        cellMap[cellAddress] = [];
      }
      cellMap[cellAddress].push(record);
    });
    
    console.log('🎯 Cell Mapping Analysis:');
    console.log('='.repeat(50));
    
    Object.entries(cellMap).forEach(([cellAddress, records]) => {
      console.log(`📍 Cell ${cellAddress}:`);
      if (records.length > 1) {
        console.log(`   ⚠️  CONFLICT: ${records.length} records mapping to same cell!`);
        records.forEach((record, i) => {
          console.log(`     ${i+1}. ${record.min_weight}-${record.max_weight}g: Order ${record.order}`);
        });
        const totalOrder = records.reduce((sum, r) => sum + r.order, 0);
        console.log(`   💡 Should sum to: ${totalOrder} total order quantity`);
      } else {
        console.log(`   ✅ Single record: ${records[0].min_weight}-${records[0].max_weight}g, Order ${records[0].order}`);
      }
      console.log('');
    });
    
    // Show what the current export logic would do
    console.log('🔧 Current Export Logic Simulation:');
    console.log('='.repeat(50));
    
    const orderMapByRow = {};
    row198Data.forEach(row => {
      if (row.excel_row_hint && row.excel_column) {
        // This is the current logic - it overwrites!
        orderMapByRow[row.excel_row_hint] = {
          order: row.order,
          excel_column: row.excel_column,
          excel_mapping_key: row.excel_mapping_key,
          weight_range: `${row.min_weight}-${row.max_weight}g`
        };
      }
    });
    
    console.log('Current logic result (OVERWRITES):');
    Object.entries(orderMapByRow).forEach(([rowNum, data]) => {
      console.log(`   Row ${rowNum}, Column ${data.excel_column}: Order ${data.order} (${data.weight_range})`);
    });
    
    console.log('\n💡 SOLUTION NEEDED:');
    console.log('   - Multiple weight ranges need to export to different columns');
    console.log('   - Or sum the quantities if they map to the same cell');
    console.log('   - Current logic only keeps the LAST record processed');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

debugRow198().catch(console.error);
