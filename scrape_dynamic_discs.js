import puppeteer from 'puppeteer';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function scrapeDynamicDiscs() {
    console.log('Starting Dynamic Discs scraper...');

    const browser = await puppeteer.launch({
        headless: false, // Set to true for production
        defaultViewport: null,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
        console.log('Browser launched, creating new page...');
        const page = await browser.newPage();
        
        // Set user agent
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        console.log('Navigating to login page...');
        await page.goto('https://discgolfdistribution.com/account/login', { waitUntil: 'networkidle2' });
        
        // Wait for login form and fill credentials
        console.log('Waiting for login form...');
        await page.waitForSelector('input[name="customer[email]"]', { timeout: 30000 });
        console.log('Found email field, typing email...');
        await page.type('input[name="customer[email]"]', '<EMAIL>');
        console.log('Typing password...');
        await page.type('input[name="customer[password]"]', 'Sdisplatgun9!');

        // Submit login form
        console.log('Submitting login form...');
        await page.click('input[type="submit"], button[type="submit"]');
        console.log('Waiting for navigation after login...');
        await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 30000 });

        console.log('Logged in successfully');
        
        // Navigate to Dynamic Discs collection
        console.log('Navigating to Dynamic Discs collection...');
        await page.goto('https://discgolfdistribution.com/collections/dynamic-discs', { waitUntil: 'networkidle2' });
        
        let allProducts = [];
        let pageNum = 1;
        let hasMoreProducts = true;
        
        while (hasMoreProducts) {
            console.log(`Fetching page ${pageNum}...`);
            
            try {
                // Try the JSON endpoint approach first
                const jsonUrl = `https://discgolfdistribution.com/collections/dynamic-discs/products.json?page=${pageNum}&limit=250`;
                const response = await page.goto(jsonUrl, { waitUntil: 'networkidle2' });
                
                if (response.status() === 200) {
                    const content = await page.content();
                    const jsonMatch = content.match(/<pre[^>]*>(.*?)<\/pre>/s);
                    
                    if (jsonMatch) {
                        const jsonData = JSON.parse(jsonMatch[1]);
                        
                        if (jsonData.products && jsonData.products.length > 0) {
                            console.log(`Found ${jsonData.products.length} products on page ${pageNum}`);
                            allProducts.push(...jsonData.products);
                            pageNum++;
                        } else {
                            hasMoreProducts = false;
                            console.log('No more products found');
                        }
                    } else {
                        throw new Error('Could not parse JSON response');
                    }
                } else {
                    throw new Error(`HTTP ${response.status()}`);
                }
            } catch (error) {
                console.log(`JSON endpoint failed: ${error.message}`);
                console.log('Falling back to HTML scraping...');
                
                // Fallback to HTML scraping
                const collectionUrl = `https://discgolfdistribution.com/collections/dynamic-discs?page=${pageNum}`;
                await page.goto(collectionUrl, { waitUntil: 'networkidle2' });
                
                // Extract product information from HTML
                const pageProducts = await page.evaluate(() => {
                    const products = [];
                    const productElements = document.querySelectorAll('.product-item, .grid-product, [data-product-id]');
                    
                    productElements.forEach(element => {
                        try {
                            const title = element.querySelector('.product-title, .product-name, h3, h4')?.textContent?.trim();
                            const price = element.querySelector('.price, .product-price')?.textContent?.trim();
                            const link = element.querySelector('a')?.href;
                            const image = element.querySelector('img')?.src;
                            
                            if (title) {
                                products.push({
                                    title,
                                    price,
                                    link,
                                    image
                                });
                            }
                        } catch (e) {
                            console.log('Error parsing product element:', e);
                        }
                    });
                    
                    return products;
                });
                
                if (pageProducts.length > 0) {
                    console.log(`Found ${pageProducts.length} products on page ${pageNum} (HTML)`);
                    allProducts.push(...pageProducts);
                    pageNum++;
                } else {
                    hasMoreProducts = false;
                    console.log('No more products found');
                }
            }
            
            // Add delay to be respectful
            await page.waitForTimeout(1000);
        }
        
        console.log(`Total products found: ${allProducts.length}`);
        
        // Save to file
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `dynamic_discs_products_${timestamp}.json`;
        
        fs.writeFileSync(filename, JSON.stringify(allProducts, null, 2));
        console.log(`Products saved to ${filename}`);
        
        // Also save as CSV for easier viewing
        const csvFilename = `dynamic_discs_products_${timestamp}.csv`;
        const csvContent = convertToCSV(allProducts);
        fs.writeFileSync(csvFilename, csvContent);
        console.log(`Products also saved to ${csvFilename}`);
        
        return allProducts;
        
    } catch (error) {
        console.error('Error during scraping:', error);
        console.error('Error stack:', error.stack);

        // Take a screenshot for debugging
        try {
            const page = browser.pages()[0];
            if (page) {
                await page.screenshot({ path: 'error_screenshot.png', fullPage: true });
                console.log('Error screenshot saved as error_screenshot.png');
            }
        } catch (screenshotError) {
            console.error('Could not take screenshot:', screenshotError.message);
        }

        throw error;
    } finally {
        console.log('Closing browser...');
        await browser.close();
    }
}

function convertToCSV(products) {
    if (products.length === 0) return '';
    
    // Get all unique keys from all products
    const allKeys = new Set();
    products.forEach(product => {
        Object.keys(product).forEach(key => allKeys.add(key));
    });
    
    const headers = Array.from(allKeys);
    const csvRows = [headers.join(',')];
    
    products.forEach(product => {
        const row = headers.map(header => {
            const value = product[header] || '';
            // Escape commas and quotes in CSV
            return `"${String(value).replace(/"/g, '""')}"`;
        });
        csvRows.push(row.join(','));
    });
    
    return csvRows.join('\n');
}

// Run the scraper
if (import.meta.url === `file://${process.argv[1]}`) {
    scrapeDynamicDiscs()
        .then(products => {
            console.log('Scraping completed successfully');
            process.exit(0);
        })
        .catch(error => {
            console.error('Scraping failed:', error);
            process.exit(1);
        });
}

export { scrapeDynamicDiscs };
