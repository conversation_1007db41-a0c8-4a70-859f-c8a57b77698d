Prompt: Replicate the t_product_variants readiness + todo pattern for t_products

Context (source pattern on t_product_variants):
- Worker task type: check_if_product_variant_is_ready
- Behavior:
  - Evaluates readiness and writes messages into t_product_variants.todo
  - If uploaded_to_shopify_at is not null, prefix todo with: "Already published to Shopify, but …" and still run all checks
  - If all checks pass: set todo to "Ready to Publish"
- Payload: { "id": <variant_id> }
- Triggering:
  - AFTER UPDATE trigger on t_product_variants for readiness fields, enqueues task immediately (scheduled_at = NOW())
  - enqueued_by = 'trigger:t_product_variants.readiness_fields'
- Backfill:
  - One-off S<PERSON> enqueues the readiness task for all variants where uploaded_to_shopify_at is null, skipping any with pending/processing tasks
- Readiness checks covered on variants:
  - Verified image exists for the variant
  - If any option name (op1/op2/op3) equals "Color", then color_id must not be null
  - price >= order_cost * 1.10
  - order_cost not null and not 0
  - notes is null
  - price >= map_price
  - msrp, if not null, must be >= price
  - shopify_weight_lbs not null
  - release_date_and_time is null or in the past
  - notes_images is null

Requested replication for t_products:
- Create new worker task type: check_if_product_is_ready
  - Behavior:
    - Evaluate product-level readiness and write messages into t_products.todo
    - If uploaded_to_shopify_at is not null, prefix todo with: "Already published to Shopify, but …" and still run all checks
    - If all checks pass: set todo to "Ready to Publish"
  - Payload: { "id": <product_id> }
- Create trigger function + AFTER UPDATE trigger on t_products that enqueues this task immediately (scheduled_at = NOW())
  - enqueued_by = 'trigger:t_products.readiness_fields'
  - Watch only readiness-related product columns (list below)
  - Use WHEN (any listed column IS DISTINCT FROM OLD value) to avoid no-op enqueues
- Provide a one-off backfill SQL that enqueues this task for all products where uploaded_to_shopify_at is null, skipping existing pending/processing tasks for the same id

Product readiness checks to implement (adjust/confirm this list):
- Product must have at least one verified image (or product-level hero image verified) [specify actual product image schema]
- Title present and non-empty
- Handle present and sanitized for Shopify (no decimal points/pipe characters) [reuse existing sanitize rule]
- Vendor present and valid (vendor_id not null)
- product_type present (or acceptable default)
- Template_suffix correct per business rule (e.g., based on video or collection) [confirm rule]
- Description/body present above minimum length (if required)
- Tags not null (or acceptable default)
- MAP/MSRP rules at product level (if applicable) [confirm: some shops enforce at variant only]
- Release_date_and_time is null or in the past (if used at product level)
- Notes and notes_images are null (no blocking flags)
- Any collection or publication readiness requirements (e.g., related collections published)

Implementation notes:
- Messages: mirror the variant behavior (accumulate reasons; set "Ready to Publish" when none)
- If both product and its variants must be ready, include a note or optionally cascade checks (not required unless asked)
- Keep the worker idempotent; each run overwrites todo with the latest evaluation result
- Trigger should be scoped to the exact columns used in checks; list them explicitly (e.g., title, handle, vendor_id, product_type, template_suffix, description/body, tags, release_date_and_time, notes, notes_images, hero_image fields, etc.)
- Use immediate scheduling (NOW) for enqueue

Deliverables:
1) Worker handler: processCheckIfProductIsReadyTask(task)
   - Loads product by id
   - Runs the readiness checks above
   - Builds human-readable reasons; prefix when uploaded_to_shopify_at not null; sets todo accordingly
   - Updates t_products.todo and task status

2) Trigger SQL (t_products):
   - CREATE OR REPLACE FUNCTION public.enqueue_check_if_product_is_ready_on_update()
   - CREATE TRIGGER trg_t_products_readiness_updated AFTER UPDATE OF <fields> ON public.t_products FOR EACH ROW
   - WHEN clause: any listed field changed (IS DISTINCT FROM)
   - Insert into public.t_task_queue(task_type, payload, scheduled_at, enqueued_by) VALUES ('check_if_product_is_ready', jsonb_build_object('id', NEW.id), NOW(), 'trigger:t_products.readiness_fields')

3) Backfill SQL:
   - Enqueue 'check_if_product_is_ready' for all products where uploaded_to_shopify_at is null, skipping duplicate pending/processing

4) Optional: Admin action/button to enqueue checks for a specific product (and optionally for all variants of that product)

Please implement all of the above following the same code style and conventions as the existing t_product_variants readiness implementation.
