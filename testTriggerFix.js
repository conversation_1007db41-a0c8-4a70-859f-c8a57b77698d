// testTriggerFix.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

// Create a Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Testing the trigger fix...');
    
    // First, find a t_images record to update
    const { data: imageRecord, error: findError } = await supabase
      .from('t_images')
      .select('*')
      .eq('table_name', 't_discs')
      .limit(1)
      .maybeSingle();
      
    if (findError) {
      console.error(`Error finding t_images record: ${findError.message}`);
      return;
    }
    
    if (!imageRecord) {
      console.log('No t_images record found to test with');
      return;
    }
    
    console.log(`Found t_images record with id=${imageRecord.id}`);
    
    // Update the record
    const updateData = {
      image_verified: !imageRecord.image_verified, // Toggle the value
      image_verified_at: new Date().toISOString(),
      image_verified_notes: 'Test update after trigger fix',
      updated_by: 'testTriggerFix'
    };
    
    console.log(`Update data: ${JSON.stringify(updateData)}`);
    
    const { data: updateResult, error: updateError } = await supabase
      .from('t_images')
      .update(updateData)
      .eq('id', imageRecord.id)
      .select();
      
    if (updateError) {
      console.error(`Error updating record: ${updateError.message}`);
    } else {
      console.log(`Update successful: ${JSON.stringify(updateResult)}`);
    }
    
    // Check for new error logs
    console.log('\nChecking for new error logs...');
    
    const { data: errorLogs, error: logsError } = await supabase
      .from('t_error_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5);
      
    if (logsError) {
      console.error(`Error getting error logs: ${logsError.message}`);
      return;
    }
    
    console.log('Recent error logs:');
    errorLogs.forEach(log => {
      console.log(`\nID: ${log.id}`);
      console.log(`Error Message: ${log.error_message}`);
      console.log(`Context: ${log.context}`);
      console.log(`Created At: ${log.created_at}`);
    });
    
    // Check for new tasks
    console.log('\nChecking for new tasks...');
    
    const { data: tasks, error: tasksError } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('task_type', 'process_disc_queue')
      .order('created_at', { ascending: false })
      .limit(5);
      
    if (tasksError) {
      console.error(`Error getting tasks: ${tasksError.message}`);
      return;
    }
    
    if (!tasks || tasks.length === 0) {
      console.log('No process_disc_queue tasks found');
    } else {
      console.log('Recent process_disc_queue tasks:');
      tasks.forEach(task => {
        console.log(`\nID: ${task.id}`);
        console.log(`Payload: ${JSON.stringify(task.payload)}`);
        console.log(`Status: ${task.status}`);
        console.log(`Created At: ${task.created_at}`);
      });
    }
  } catch (err) {
    console.error(`Unexpected error: ${err.message}`);
    process.exit(1);
  }
}

main();
