// closeServer.js
import express from 'express';

const app = express();
const PORT = 3000;

// Start the server and save the server object.
const server = app.listen(PORT, () => {
  console.log(`Server started on port ${PORT}`);
});

// Later—say, after 5 seconds—close the server.
setTimeout(() => {
  server.close(() => {
    console.log('Server closed gracefully.');
    process.exit(0);
  });
}, 5000);
