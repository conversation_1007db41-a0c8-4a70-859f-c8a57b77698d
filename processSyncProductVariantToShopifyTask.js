/**
 * Process sync_product_variant_to_shopify task
 *
 * Coalesced worker to apply variant + product updates in one pass:
 * - Variant fields: price, compare_at_price (from msrp, null clears), barcode (from upc), grams (from shopify_weight_lbs), option1/2/3 values
 * - Product fields: option name changes (op1_name/op2_name/op3_name), tags for Color_Family_ and player_
 * - When option names change, also update our t_product_variants rows for ALL variants of that Shopify product
 */

import fetch from 'node-fetch';

// Shopify configuration (following existing pattern in processUpdateDiscVariantPriceOnShopifyTask.js)
const shopifyGraphqlEndpoint = 'https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/graphql.json';
const shopifyAccessToken = '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';
const variantsEndpoint = 'https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/variants/';
const productsEndpoint = 'https://dzdiscs-new-releases.myshopify.com/admin/api/2024-01/products/';

async function shopifyGraphQLRequest(query, variables = {}) {
  const response = await fetch(shopifyGraphqlEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken
    },
    body: JSON.stringify({ query, variables })
  });
  if (!response.ok) {
    const t = await response.text();
    throw new Error(`Shopify GraphQL ${response.status} ${response.statusText}: ${t}`);
  }
  const result = await response.json();
  if (result.errors) {
    throw new Error(`Shopify GraphQL errors: ${JSON.stringify(result.errors)}`);
  }
  return result.data;
}

async function findVariantBySku(sku) {
  const query = `
    query getVariantBySku($query: String!) {
      productVariants(first: 1, query: $query) {
        edges {
          node {
            id
            sku
            barcode
            product { id title }
          }
        }
      }
    }
  `;
  const data = await shopifyGraphQLRequest(query, { query: `sku:${sku}` });
  if (!data.productVariants?.edges?.length) return null;
  const node = data.productVariants.edges[0].node;
  return {
    variant_id: String(node.id).split('/').pop(),
    product_id: String(node.product.id).split('/').pop(),
    product_title: node.product.title
  };
}

async function getProduct(productId) {
  const resp = await fetch(`${productsEndpoint}${productId}.json`, {
    headers: { 'X-Shopify-Access-Token': shopifyAccessToken }
  });
  if (!resp.ok) {
    const t = await resp.text();
    throw new Error(`GET product ${productId} failed: ${resp.status} ${resp.statusText} - ${t}`);
  }
  const j = await resp.json();
  return j.product;
}

async function updateVariant(variantId, variantPayload) {
  const payload = { variant: { id: variantId, ...variantPayload } };
  const resp = await fetch(`${variantsEndpoint}${variantId}.json`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken
    },
    body: JSON.stringify(payload)
  });
  if (!resp.ok) {
    const t = await resp.text();
    throw new Error(`PUT variant ${variantId} failed: ${resp.status} ${resp.statusText} - ${t}`);
  }
  const j = await resp.json();
  return j.variant;
}

async function updateProduct(productId, productPayload) {
  const payload = { product: { id: productId, ...productPayload } };
  const resp = await fetch(`${productsEndpoint}${productId}.json`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken
    },
    body: JSON.stringify(payload)
  });
  if (!resp.ok) {
    const t = await resp.text();
    throw new Error(`PUT product ${productId} failed: ${resp.status} ${resp.statusText} - ${t}`);
  }
  const j = await resp.json();
  return j.product;
}

function lbsToGrams(lbs) {
  if (lbs === null || lbs === undefined) return undefined;
  const n = Number(lbs);
  if (Number.isNaN(n)) return undefined;
  return Math.round(n * 453.592);
}

function ensureArray(val) {
  if (!val) return [];
  if (Array.isArray(val)) return val;
  if (typeof val === 'string') return val.split(',').map(s => s.trim()).filter(Boolean);
  return [];
}

export default async function processSyncProductVariantToShopifyTask(task, { supabase, updateTaskStatus, logError } = {}) {
  try {
    await updateTaskStatus(task.id, 'processing');

    // Parse payload (object or JSON)
    const payload = (typeof task.payload === 'object' && task.payload !== null)
      ? task.payload
      : JSON.parse(task.payload || '{}');

    const variantIdLocal = payload.id;
    const changes = payload.changes || {};
    if (!variantIdLocal) throw new Error('payload.id (local t_product_variants.id) is required');

    // Fetch local variant row for uploaded flag and product grouping
    const { data: variantRow, error: vrErr } = await supabase
      .from('t_product_variants')
      .select('id, product_id, uploaded_to_shopify_at')
      .eq('id', variantIdLocal)
      .maybeSingle();
    if (vrErr) throw new Error(`DB error fetching t_product_variants ${variantIdLocal}: ${vrErr.message}`);

    // Check if this is only an option NAME change and variant not yet uploaded
    const optionNameChanges = {
      1: Object.prototype.hasOwnProperty.call(changes, 'op1_name') ? changes.op1_name : undefined,
      2: Object.prototype.hasOwnProperty.call(changes, 'op2_name') ? changes.op2_name : undefined,
      3: Object.prototype.hasOwnProperty.call(changes, 'op3_name') ? changes.op3_name : undefined
    };

    const hasAnyOptionNameChange = optionNameChanges[1] !== undefined || optionNameChanges[2] !== undefined || optionNameChanges[3] !== undefined;

    if (!variantRow?.uploaded_to_shopify_at) {
      // Not uploaded: only propagate option NAME changes locally (no Shopify calls)
      if (hasAnyOptionNameChange) {
        const updateCols = {};
        if (optionNameChanges[1] !== undefined) updateCols.op1_name = optionNameChanges[1];
        if (optionNameChanges[2] !== undefined) updateCols.op2_name = optionNameChanges[2];
        if (optionNameChanges[3] !== undefined) updateCols.op3_name = optionNameChanges[3];

        if (variantRow?.product_id) {
          const { error: updErr } = await supabase
            .from('t_product_variants')
            .update(updateCols)
            .eq('product_id', variantRow.product_id);
          if (updErr) throw new Error(`Supabase error updating option names by product_id: ${updErr.message}`);
        } else {
          const { error: updErr } = await supabase
            .from('t_product_variants')
            .update(updateCols)
            .eq('id', variantIdLocal);
          if (updErr) throw new Error(`Supabase error updating option names for variant ${variantIdLocal}: ${updErr.message}`);
        }

        await updateTaskStatus(task.id, 'completed', {
          message: 'Local option name propagation applied (variant not yet uploaded to Shopify)'
        });
        return;
      } else {
        await updateTaskStatus(task.id, 'completed', {
          status: 'skipped_not_uploaded',
          message: 'No option name changes; variant not uploaded to Shopify — skipping sync'
        });
        return;
      }
    }

    const sku = `DGACC${variantIdLocal}`;

    // 1) Resolve Shopify variant and product
    const variantInfo = await findVariantBySku(sku);
    if (!variantInfo) {
      await updateTaskStatus(task.id, 'completed', {
        status: 'variant_not_found',
        message: `No Shopify variant found for SKU ${sku}`
      });
      return;
    }

    const shopifyVariantId = variantInfo.variant_id;
    const shopifyProductId = variantInfo.product_id;

    // 2) Fetch product (for tags/options and peer variants)
    const product = await getProduct(shopifyProductId);

    // 3) Prepare updates
    const variantPatch = {};

    // price
    if (Object.prototype.hasOwnProperty.call(changes, 'price')) {
      if (changes.price !== null && changes.price !== undefined) variantPatch.price = String(changes.price);
    }
    // msrp -> compare_at_price (allow null)
    if (Object.prototype.hasOwnProperty.call(changes, 'msrp')) {
      variantPatch.compare_at_price = (changes.msrp === null || changes.msrp === undefined)
        ? null
        : String(changes.msrp);
    }
    // upc -> barcode
    if (Object.prototype.hasOwnProperty.call(changes, 'upc')) {
      // Empty string or null will clear barcode on Shopify
      variantPatch.barcode = (changes.upc === undefined) ? undefined : (changes.upc ?? null);
    }
    // shopify_weight_lbs -> grams
    if (Object.prototype.hasOwnProperty.call(changes, 'shopify_weight_lbs')) {
      const g = lbsToGrams(changes.shopify_weight_lbs);
      if (g !== undefined) variantPatch.grams = g;
    }
    // option values
    if (Object.prototype.hasOwnProperty.call(changes, 'op1_value')) variantPatch.option1 = changes.op1_value ?? null;
    if (Object.prototype.hasOwnProperty.call(changes, 'op2_value')) variantPatch.option2 = changes.op2_value ?? null;
    if (Object.prototype.hasOwnProperty.call(changes, 'op3_value')) variantPatch.option3 = changes.op3_value ?? null;

    // 4) Product-level: tags and option names
    let productNeedsUpdate = false;
    const productPatch = {};

    // Tags: handle color_id and player_id independently
    const tagsArr = ensureArray(product.tags);

    if (Object.prototype.hasOwnProperty.call(changes, 'color_id')) {
      // Remove all Color_Family_* tags
      const filtered = tagsArr.filter(t => !t.startsWith('Color_Family_'));
      // Lookup new color name
      if (changes.color_id) {
        const { data: colorRec, error: colorErr } = await supabase
          .from('t_colors')
          .select('color')
          .eq('id', changes.color_id)
          .maybeSingle();
        if (colorErr) throw new Error(`Supabase error fetching t_colors: ${colorErr.message}`);
        if (colorRec?.color) filtered.push(`Color_Family_${colorRec.color}`);
      }
      productPatch.tags = filtered.join(',');
      productNeedsUpdate = true;
    }

    if (Object.prototype.hasOwnProperty.call(changes, 'player_id')) {
      // Start from latest tags set (if we already modified above, use that; else from product.tags)
      const currentTags = productPatch.tags ? ensureArray(productPatch.tags) : tagsArr.slice();
      const filtered = currentTags.filter(t => !t.startsWith('player_'));
      if (changes.player_id) {
        const { data: playerRec, error: playerErr } = await supabase
          .from('t_players')
          .select('name')
          .eq('id', changes.player_id)
          .maybeSingle();
        if (playerErr) throw new Error(`Supabase error fetching t_players: ${playerErr.message}`);
        if (playerRec?.name) filtered.push(`player_${playerRec.name}`);
      }
      productPatch.tags = filtered.join(',');
      productNeedsUpdate = true;
    }

    // Option name changes (reuse previously computed optionNameChanges)

    if (optionNameChanges[1] !== undefined || optionNameChanges[2] !== undefined || optionNameChanges[3] !== undefined) {
      // Build options array preserving id and position; only change provided names
      const newOptions = (product.options || []).map((opt) => {
        const pos = opt.position; // 1..3
        const newName = optionNameChanges[pos];
        if (newName !== undefined && newName !== null) {
          return { id: opt.id, name: newName };
        }
        // Keep as-is (no need to send unchanged options)
        return { id: opt.id, name: opt.name };
      });
      productPatch.options = newOptions;
      productNeedsUpdate = true;
    }

    // 5) Apply updates
    const results = { variant: null, product: null };

    // Update product first if names/tags changed (option names may affect validation of variant option values)
    if (productNeedsUpdate) {
      results.product = await updateProduct(shopifyProductId, productPatch);

      // If option names changed, update local DB names across all variants of this local product
      if (optionNameChanges[1] !== undefined || optionNameChanges[2] !== undefined || optionNameChanges[3] !== undefined) {
        const updateCols = {};
        if (optionNameChanges[1] !== undefined) updateCols.op1_name = optionNameChanges[1];
        if (optionNameChanges[2] !== undefined) updateCols.op2_name = optionNameChanges[2];
        if (optionNameChanges[3] !== undefined) updateCols.op3_name = optionNameChanges[3];
        if (variantRow?.product_id && Object.keys(updateCols).length) {
          const { error: updErr } = await supabase
            .from('t_product_variants')
            .update(updateCols)
            .eq('product_id', variantRow.product_id);
          if (updErr) throw new Error(`Supabase error updating t_product_variants option names by product_id: ${updErr.message}`);
        }
      }
    }

    // Update variant if anything to send
    if (Object.keys(variantPatch).length > 0) {
      results.variant = await updateVariant(shopifyVariantId, variantPatch);
    }

    // After any Shopify update, stamp uploaded_to_shopify_at = now()
    if ((productNeedsUpdate && results.product) || Object.keys(variantPatch).length > 0) {
      const { error: stampErr } = await supabase
        .from('t_product_variants')
        .update({ uploaded_to_shopify_at: new Date().toISOString() })
        .eq('id', variantIdLocal);
      if (stampErr) {
        console.warn(`[processSyncProductVariantToShopifyTask] Failed to stamp uploaded_to_shopify_at for variant ${variantIdLocal}: ${stampErr.message}`);
      }
    }

    // 6) Complete
    await updateTaskStatus(task.id, 'completed', {
      message: 'sync_product_variant_to_shopify applied',
      sku,
      shopify_variant_id: shopifyVariantId,
      shopify_product_id: shopifyProductId,
      variant_patch: variantPatch,
      product_patch: productNeedsUpdate ? productPatch : null
    });
  } catch (err) {
    // Rate limit or transient error: reschedule once +5 min
    const msg = `[processSyncProductVariantToShopifyTask] ${err.message}`;
    console.error(msg);
    if (logError) await logError(msg, `task ${task.id}`);

    // Try to reschedule if 429 or 5xx hinted
    const isRetryable = /429|5\d\d|rate limit|timeout|ECONNRESET|ETIMEDOUT/i.test(err.message || '');
    if (isRetryable) {
      const next = new Date(Date.now() + 5 * 60 * 1000).toISOString();
      // Set back to pending and push scheduled_at forward
      await updateTaskStatus(task.id, 'pending');
      const { error: schedErr } = await (async () => {
        try {
          const { error } = await supabase
            .from('t_task_queue')
            .update({ scheduled_at: next })
            .eq('id', task.id);
          return { error };
        } catch (e) { return { error: { message: e.message } }; }
      })();
      if (schedErr) {
        await updateTaskStatus(task.id, 'error', { message: err.message });
      }
      return;
    }

    await updateTaskStatus(task.id, 'error', { message: err.message });
  }
}

