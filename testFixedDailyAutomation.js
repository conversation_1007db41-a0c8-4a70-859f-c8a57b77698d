import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testFixedDailyAutomation() {
  try {
    console.log('🧪 Testing FIXED daily automation logic...\n');
    
    // Get a small sample to test the fix
    console.log('1. Getting sample data...');
    const { data: sampleData, error } = await supabase
      .from('it_discraft_order_sheet_lines')
      .select('excel_mapping_key, excel_column, excel_row_hint, calculated_mps_id')
      .gte('excel_row_hint', 367)
      .lte('excel_row_hint', 370)
      .eq('is_orderable', true)
      .not('excel_mapping_key', 'is', null)
      .limit(10);

    if (error) {
      throw new Error(`Failed to query sample data: ${error.message}`);
    }

    console.log(`✅ Got ${sampleData.length} sample records`);

    // Simulate the FIXED daily automation mapping (with calculated_mps_id included)
    console.log('\n2. Testing FIXED mapping logic...');
    const orderableData = sampleData.map(item => {
      return {
        excel_mapping_key: item.excel_mapping_key,
        excel_column: item.excel_column,
        excel_row_hint: item.excel_row_hint,
        order: 0,  // Default order
        calculated_mps_id: item.calculated_mps_id,  // NOW INCLUDED!
        mold_name: 'Test',
        plastic_name: 'Test',
        is_currently_available: false
      };
    });

    console.log('Mapped data:');
    orderableData.forEach((record, index) => {
      console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: calculated_mps_id=${record.calculated_mps_id}`);
    });

    // Test MPS data creation (same as daily automation)
    console.log('\n3. Testing MPS data creation...');
    const mpsData = orderableData.map(item => ({
        ...item,
        order: item.calculated_mps_id || item.mps_id || 'NO_MPS'
    }));

    console.log('MPS data:');
    mpsData.forEach((record, index) => {
      console.log(`   ${index + 1}. Row ${record.excel_row_hint}, Col ${record.excel_column}: order=${record.order} (from calculated_mps_id=${record.calculated_mps_id})`);
    });

    // Test export
    console.log('\n4. Testing export with fixed data...');
    const timestamp = new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0];
    
    const response = await fetch('http://localhost:3001/api/discraft/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        includeHeader: false,
        filename: `test_fixed_mps_${timestamp}.xlsx`,
        orderData: mpsData
      })
    });

    if (!response.ok) {
      throw new Error(`Export API returned ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    console.log('✅ Export completed!');
    console.log(`📄 Filename: ${result.filename}`);
    console.log(`📊 Records processed: ${result.totalRecords}`);
    
    console.log('\n🎯 The fix should now show actual MPS IDs instead of NO_MPS!');
    console.log(`Check file: ${result.filePath}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testFixedDailyAutomation().catch(console.error);
