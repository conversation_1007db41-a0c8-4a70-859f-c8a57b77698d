CREATE OR REPLACE VIEW public.v_reconcile_osl_to_veeqo AS
SELECT
  v.product_id,
  v.qty_on_hand AS veeqo_qty,
  inv.available_quantity AS local_qty,
  v.qty_on_hand::integer - inv.available_quantity AS quantity_difference,
  v.sku_code,
  inv.veeqo_qty_updated_at,
  osl.id
FROM
  imported_table_veeqo_sellables_export v
  JOIN t_order_sheet_lines osl ON osl.veeqo_id = v.product_id
  JOIN t_inv_osl inv ON inv.id = osl.id
WHERE
  v.qty_on_hand ~ '^\d+$'::text
  AND v.qty_on_hand::integer <> inv.available_quantity;
