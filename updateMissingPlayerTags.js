import { createClient } from '@supabase/supabase-js';
import fetch from 'node-fetch';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Shopify API configuration
const shopifyEndpoint = process.env.SHOPIFY_ENDPOINT;
const shopifyAccessToken = process.env.SHOPIFY_ACCESS_TOKEN;
const productsEndpoint = shopifyEndpoint.replace("graphql.json", "products.json");

// Configuration
const DRY_RUN = process.argv.includes('--dry-run');
const BATCH_SIZE = parseInt(process.argv.find(arg => arg.startsWith('--batch-size='))?.split('=')[1]) || 20;
const RATE_LIMIT_DELAY = 600; // 600ms between API calls (under 2 calls/second)

console.log(`🚀 Starting updateMissingPlayerTags.js`);
console.log(`📊 Mode: ${DRY_RUN ? 'DRY RUN' : 'LIVE UPDATE'}`);
console.log(`📦 Batch size: ${BATCH_SIZE}`);
console.log(`⏱️  Rate limit delay: ${RATE_LIMIT_DELAY}ms`);

/**
 * Sleep function for rate limiting
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Execute Shopify GraphQL query
 */
async function executeShopifyGraphQL(query, variables = {}) {
  const response = await fetch(shopifyEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Shopify-Access-Token': shopifyAccessToken,
    },
    body: JSON.stringify({ query, variables }),
  });

  const result = await response.json();
  
  if (result.errors) {
    throw new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`);
  }
  
  return result.data;
}

/**
 * Find Shopify product by variant SKU
 */
async function findProductBySku(sku) {
  try {
    console.log(`🔍 Finding Shopify product for SKU: ${sku}`);
    
    const query = `
      query getVariantBySku($query: String!) {
        productVariants(first: 1, query: $query) {
          edges {
            node {
              id
              sku
              product {
                id
                title
                tags
              }
            }
          }
        }
      }
    `;

    const variables = {
      query: `sku:${sku}`
    };

    const result = await executeShopifyGraphQL(query, variables);

    if (!result.productVariants.edges.length) {
      console.log(`❌ No product found for SKU: ${sku}`);
      return null;
    }

    const variant = result.productVariants.edges[0].node;
    const productId = variant.product.id.split('/').pop(); // Extract numeric ID
    
    return {
      productId,
      variantId: variant.id,
      title: variant.product.title,
      currentTags: variant.product.tags
    };
  } catch (error) {
    console.error(`❌ Error finding product for SKU ${sku}:`, error.message);
    return null;
  }
}

/**
 * Update Shopify product tags
 */
async function updateProductTags(productId, newTags) {
  try {
    if (DRY_RUN) {
      console.log(`🧪 DRY RUN: Would update product ${productId} with tags: ${newTags.join(', ')}`);
      return { success: true, dryRun: true };
    }

    console.log(`🔄 Updating product ${productId} with new tags...`);
    
    const updateEndpoint = `${productsEndpoint.replace('.json', '')}/${productId}.json`;
    
    const payload = {
      product: {
        id: productId,
        tags: newTags.join(',')
      }
    };

    const response = await fetch(updateEndpoint, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-Shopify-Access-Token': shopifyAccessToken
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Shopify API error (${response.status}): ${errorText}`);
    }

    const result = await response.json();
    console.log(`✅ Successfully updated product ${productId}`);
    
    return { success: true, product: result.product };
  } catch (error) {
    console.error(`❌ Error updating product ${productId}:`, error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Get target discs that need player tag updates
 */
async function getTargetDiscs() {
  try {
    console.log(`📊 Querying target discs...`);
    
    const { data: discs, error } = await supabase
      .from('t_discs')
      .select(`
        id,
        mps_id,
        t_mps!inner (
          id,
          stamp_id,
          t_stamps!inner (
            id,
            stamp,
            player_id,
            t_players!inner (
              id,
              name
            )
          )
        )
      `)
      .is('sold_channel', null)
      .not('shopify_uploaded_at', 'is', null)
      .gt('id', 418423)
      .not('t_mps.t_stamps.player_id', 'is', null);

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    console.log(`📊 Found ${discs.length} discs that need player tag updates`);
    return discs;
  } catch (error) {
    console.error(`❌ Error querying target discs:`, error.message);
    throw error;
  }
}

/**
 * Process a single disc
 */
async function processDisc(disc) {
  const sku = `D${disc.id}`;
  const playerName = disc.t_mps.t_stamps.t_players.name;
  const expectedPlayerTag = `player_${playerName}`;
  
  console.log(`\n🎯 Processing disc ${disc.id} (SKU: ${sku})`);
  console.log(`👤 Expected player tag: ${expectedPlayerTag}`);

  // Find the product in Shopify
  const shopifyProduct = await findProductBySku(sku);
  await sleep(RATE_LIMIT_DELAY);

  if (!shopifyProduct) {
    return {
      discId: disc.id,
      sku,
      status: 'not_found',
      message: 'Product not found in Shopify'
    };
  }

  console.log(`📦 Found product: ${shopifyProduct.title}`);
  console.log(`🏷️  Current tags: ${shopifyProduct.currentTags.join(', ')}`);

  // Check if player tag already exists
  const hasPlayerTag = shopifyProduct.currentTags.some(tag => 
    tag.toLowerCase() === expectedPlayerTag.toLowerCase()
  );

  if (hasPlayerTag) {
    console.log(`✅ Player tag already exists, skipping`);
    return {
      discId: disc.id,
      sku,
      status: 'already_has_tag',
      message: 'Player tag already exists'
    };
  }

  // Add the player tag
  const newTags = [...shopifyProduct.currentTags, expectedPlayerTag];
  console.log(`➕ Adding player tag: ${expectedPlayerTag}`);

  const updateResult = await updateProductTags(shopifyProduct.productId, newTags);
  await sleep(RATE_LIMIT_DELAY);

  if (updateResult.success) {
    return {
      discId: disc.id,
      sku,
      status: updateResult.dryRun ? 'dry_run_success' : 'updated',
      message: `Successfully ${updateResult.dryRun ? 'would add' : 'added'} player tag: ${expectedPlayerTag}`,
      playerTag: expectedPlayerTag
    };
  } else {
    return {
      discId: disc.id,
      sku,
      status: 'error',
      message: `Failed to update: ${updateResult.error}`,
      error: updateResult.error
    };
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log(`\n🎬 Starting main execution...`);
    
    // Get target discs
    const targetDiscs = await getTargetDiscs();
    
    if (targetDiscs.length === 0) {
      console.log(`✅ No discs need player tag updates`);
      return;
    }

    // Process in batches
    const results = [];
    const totalBatches = Math.ceil(targetDiscs.length / BATCH_SIZE);
    
    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const startIndex = batchIndex * BATCH_SIZE;
      const endIndex = Math.min(startIndex + BATCH_SIZE, targetDiscs.length);
      const batch = targetDiscs.slice(startIndex, endIndex);
      
      console.log(`\n📦 Processing batch ${batchIndex + 1}/${totalBatches} (${batch.length} discs)`);
      
      for (const disc of batch) {
        const result = await processDisc(disc);
        results.push(result);
      }
      
      // Pause between batches
      if (batchIndex < totalBatches - 1) {
        console.log(`⏸️  Pausing between batches...`);
        await sleep(2000);
      }
    }

    // Summary
    console.log(`\n📊 EXECUTION SUMMARY`);
    console.log(`==================`);
    console.log(`Total discs processed: ${results.length}`);
    
    const statusCounts = results.reduce((acc, result) => {
      acc[result.status] = (acc[result.status] || 0) + 1;
      return acc;
    }, {});
    
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`${status}: ${count}`);
    });

    // Show errors if any
    const errors = results.filter(r => r.status === 'error');
    if (errors.length > 0) {
      console.log(`\n❌ ERRORS:`);
      errors.forEach(error => {
        console.log(`  Disc ${error.discId} (${error.sku}): ${error.message}`);
      });
    }

    // Show successful updates
    const updates = results.filter(r => r.status === 'updated' || r.status === 'dry_run_success');
    if (updates.length > 0) {
      console.log(`\n✅ ${DRY_RUN ? 'WOULD UPDATE' : 'UPDATED'}:`);
      updates.forEach(update => {
        console.log(`  Disc ${update.discId} (${update.sku}): ${update.playerTag}`);
      });
    }

    console.log(`\n🎉 Execution completed!`);
    
  } catch (error) {
    console.error(`💥 Fatal error:`, error.message);
    process.exit(1);
  }
}

// Run the script
main().catch(error => {
  console.error('💥 Unhandled error:', error);
  process.exit(1);
});
