-- Fix the failed task by updating its status back to 'pending'
-- This will allow the worker to pick it up again with the fixed code

UPDATE t_task_queue
SET status = 'pending',
    locked_at = NULL,
    locked_by = NULL,
    result = NULL,
    processed_at = NULL
WHERE task_type = 'reconcile_clear_count_from_shopify_for_sold_disc'
AND status = 'error'
AND result->>'error' LIKE '%JSON object requested, multiple (or no) rows returned%';

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Failed Shopify tasks have been reset to pending status.';
END $$;
