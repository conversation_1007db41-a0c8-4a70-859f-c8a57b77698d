// debug_task_processing.js
// Debug why the error extraction isn't working

import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function debugTaskProcessing() {
  try {
    console.log('Debugging task processing...');
    
    // Check recent tasks for OSL 18500
    const { data: tasks, error } = await supabase
      .from('t_task_queue')
      .select('*')
      .eq('task_type', 'publish_product_osl')
      .contains('payload', { id: 18500 })
      .order('created_at', { ascending: false })
      .limit(5);
    
    if (error) {
      console.error('Error fetching tasks:', error);
      return;
    }
    
    console.log(`Found ${tasks.length} recent tasks for OSL 18500:`);
    
    tasks.forEach(task => {
      console.log(`\n📋 Task ${task.id}:`);
      console.log('Status:', task.status);
      console.log('Created:', task.created_at);
      console.log('Processed:', task.processed_at);
      console.log('Enqueued By:', task.enqueued_by);
      
      if (task.result) {
        const result = typeof task.result === 'string' ? JSON.parse(task.result) : task.result;
        console.log('Error:', result.error);
        console.log('Exit Code:', result.exit_code);
        console.log('Stdout Length:', result.stdout_length);
        console.log('Stderr Length:', result.stderr_length);
      }
    });
    
    // Check if OSL 18500 is ready to be processed
    const { data: osl } = await supabase
      .from('t_order_sheet_lines')
      .select('ready_button, ready, shopify_uploaded_at')
      .eq('id', 18500)
      .single();
    
    console.log('\n📊 OSL 18500 Status:');
    console.log('Ready Button:', osl.ready_button);
    console.log('Ready:', osl.ready);
    console.log('Already Uploaded:', osl.shopify_uploaded_at ? 'Yes' : 'No');
    
    if (osl.shopify_uploaded_at) {
      console.log('\n⚠️  OSL is already uploaded! Tasks would complete early without error processing.');
    } else if (!osl.ready_button || !osl.ready) {
      console.log('\n⚠️  OSL is not ready! Tasks would be skipped.');
    } else {
      console.log('\n✅ OSL should trigger the manual fix error when processed.');
    }
    
  } catch (error) {
    console.error('Unexpected error:', error.message);
  }
}

debugTaskProcessing();
