import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkCounts() {
  console.log('Checking disc counts...');
  
  // Check total discs matching base criteria
  const { data: baseData, error: baseError } = await supabase
    .from('t_discs')
    .select('id', { count: 'exact' })
    .is('sold_channel', null)
    .not('shopify_uploaded_at', 'is', null)
    .gt('id', 418423);
    
  if (baseError) {
    console.error('Error with base query:', baseError);
    return;
  }
  
  console.log('Total discs matching base criteria (sold_channel IS NULL, shopify_uploaded_at IS NOT NULL, id > 418423):', baseData.length);
  
  // Now check how many have stamps with players
  const { data: withPlayers, error: playerError } = await supabase
    .from('t_discs')
    .select(`
      id,
      t_mps!inner (
        stamp_id,
        t_stamps!inner (
          player_id,
          t_players!inner (name)
        )
      )
    `)
    .is('sold_channel', null)
    .not('shopify_uploaded_at', 'is', null)
    .gt('id', 418423)
    .not('t_mps.t_stamps.player_id', 'is', null);
    
  if (playerError) {
    console.error('Error with player query:', playerError);
    return;
  }
  
  console.log('Discs with stamps that have players:', withPlayers.length);
  console.log('This should match the 637 from our script.');
}

checkCounts().catch(console.error);
