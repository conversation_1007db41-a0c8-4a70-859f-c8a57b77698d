// Function to process a disc_updated_reset task
async function processDiscUpdatedResetTask(task, { supabase, updateTaskStatus, logError }) {
  console.log(`[taskQueueWorker.js] Processing task ${task.id} of type ${task.task_type}`);

  try {
    // Parse the payload
    let payload;
    try {
      console.log(`[taskQueueWorker.js] Task ${task.id} payload type: ${typeof task.payload}`);
      console.log(`[taskQueueWorker.js] Task ${task.id} raw payload: ${JSON.stringify(task.payload)}`);

      // Handle object format directly
      if (typeof task.payload === 'object' && task.payload !== null) {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is an object, using directly`);
        payload = task.payload;
      }
      // Handle simple JSON string format
      else if (typeof task.payload === 'string') {
        console.log(`[taskQueueWorker.js] Task ${task.id} payload is a string, attempting to parse`);
        try {
          payload = JSON.parse(task.payload);
          console.log(`[taskQueueWorker.js] Task ${task.id} payload parsed successfully`);
        } catch (parseErr) {
          console.error(`[taskQueueWorker.js] Task ${task.id} failed to parse payload: ${parseErr.message}`);
          throw new Error(`Failed to parse payload: ${parseErr.message}. Only simple JSON format is supported.`);
        }
      }
      else {
        throw new Error(`Unsupported payload type: ${typeof task.payload}. Expected object or JSON string.`);
      }
    } catch (err) {
      const errMsg = `[taskQueueWorker.js] Error parsing payload for task ${task.id}: ${err.message}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to reset disc. Invalid payload format.",
        error: 'Invalid JSON payload'
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Parsed payload for task ${task.id}: ${JSON.stringify(payload)}`);

    // Check for required fields
    if (!payload.id) {
      const errMsg = `[taskQueueWorker.js] Missing id in payload for task ${task.id}`;
      console.error(errMsg);
      await logError(errMsg, `Processing task ${task.id}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to reset disc. Missing disc id in payload.",
        error: 'Missing id in payload'
      });
      return;
    }

    const discId = payload.id;
    console.log(`[taskQueueWorker.js] Resetting disc with id=${discId}`);

    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Update the disc record with the specified reset fields
    const updateData = {
      order_sheet_line_id: null,
      sdasin_searched_for_at: null,
      looked_for_matching_sdasin_at: null,
      tag_printed_at: null,
      todo: "Need to finish mps_id change over.",
      g_title: null,
      g_pull: null,
      g_handle: null
    };

    console.log(`[taskQueueWorker.js] Updating disc ${discId} with reset data: ${JSON.stringify(updateData)}`);

    const { error: updateError } = await supabase
      .from('t_discs')
      .update(updateData)
      .eq('id', discId);

    if (updateError) {
      const errMsg = `[taskQueueWorker.js] Error updating disc record: ${updateError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Updating disc record for id=${discId}`);
      await updateTaskStatus(task.id, 'error', {
        message: "Failed to reset disc. Database error when updating disc record.",
        error: updateError.message
      });
      return;
    }

    console.log(`[taskQueueWorker.js] Successfully reset disc ${discId}`);

    // Enqueue the next task in the sequence
    try {
      console.log(`[taskQueueWorker.js] Reset successful, enqueueing disc_updated_unsell task for disc ${discId}`);

      const { data: unsellTask, error: unsellError } = await supabase
        .from('t_task_queue')
        .insert({
          task_type: 'disc_updated_unsell',
          payload: {
            id: discId,
            original_sold_date: payload.sold_date  // Pass the original sold_date from the workflow start
          },
          status: 'pending',
          scheduled_at: new Date().toISOString(), // No delay
          created_at: new Date().toISOString(),
          enqueued_by: `disc_updated_reset_${discId}`
        })
        .select();

      if (unsellError) {
        console.error(`[taskQueueWorker.js] Error enqueueing disc_updated_unsell task: ${unsellError.message}`);
        await logError(`Error enqueueing disc_updated_unsell task: ${unsellError.message}`, `Enqueueing task for disc id=${discId}`);
      } else {
        console.log(`[taskQueueWorker.js] Successfully enqueued disc_updated_unsell task (ID: ${unsellTask[0].id}) for disc ${discId}`);
      }

      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully reset disc ${discId}. All specified fields have been cleared and todo updated.`,
        disc_id: discId,
        fields_reset: [
          'order_sheet_line_id',
          'sdasin_searched_for_at',
          'looked_for_matching_sdasin_at',
          'tag_printed_at',
          'g_title',
          'g_pull',
          'g_handle'
        ],
        todo_set: "Need to finish mps_id change over.",
        next_task_enqueued: unsellError ? false : true,
        next_task_error: unsellError?.message || null
      });
    } catch (err) {
      console.error(`[taskQueueWorker.js] Exception enqueueing disc_updated_unsell task: ${err.message}`);
      await logError(`Exception enqueueing disc_updated_unsell task: ${err.message}`, `Enqueueing task for disc id=${discId}`);

      await updateTaskStatus(task.id, 'completed', {
        message: `Successfully reset disc ${discId}. All specified fields have been cleared and todo updated. Failed to enqueue next task.`,
        disc_id: discId,
        fields_reset: [
          'order_sheet_line_id',
          'sdasin_searched_for_at',
          'looked_for_matching_sdasin_at',
          'tag_printed_at',
          'g_title',
          'g_pull',
          'g_handle'
        ],
        todo_set: "Need to finish mps_id change over.",
        next_task_enqueued: false,
        next_task_error: err.message
      });
    }

  } catch (err) {
    const errMsg = `[taskQueueWorker.js] Exception while processing task ${task.id}: ${err.message}`;
    console.error(errMsg);
    await logError(errMsg, `Processing task ${task.id}`);

    await updateTaskStatus(task.id, 'error', {
      message: "Failed to reset disc due to an unexpected error.",
      error: err.message
    });
  }
}

export default processDiscUpdatedResetTask;
