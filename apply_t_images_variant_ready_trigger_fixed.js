// apply_t_images_variant_ready_trigger_fixed.js - Apply t_images variant ready trigger to Supabase (fixed version)
import 'dotenv/config';
import fs from 'fs';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
if (!supabaseUrl || !supabaseKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_KEY in environment');
  process.exit(1);
}
const supabase = createClient(supabaseUrl, supabaseKey);

async function execSql(sql) {
  // Only try sql_query parameter since that's what works
  let res = await supabase.rpc('exec_sql', { sql_query: sql });
  return res;
}

async function main() {
  try {
    const triggerSql = fs.readFileSync('sql/triggers/t_images_enqueue_variant_ready_on_image_verified.sql', 'utf8');

    console.log('Applying t_images variant ready trigger as single statement...');
    let { data, error } = await execSql(triggerSql);
    if (error) {
      console.error('Error applying trigger SQL:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      process.exit(1);
    }
    console.log('Applied trigger successfully.');
    if (data) {
      console.log('Result:', data);
    }

    console.log('Done.');
  } catch (e) {
    console.error('Fatal error:', e?.message || e);
    process.exit(1);
  }
}

main();
