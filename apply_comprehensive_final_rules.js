import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function applyComprehensiveFinalRules() {
  try {
    console.log('Applying comprehensive final parsing rules...\n');

    // <PERSON><PERSON> missed multi-pack records
    await handleMissedMultiPacks();

    // Handle storage rack records
    await handleStorageRacks();

    // Handle DyeMax records
    await handleDyeMaxRecords();

    // Handle new brand exclusions
    await handleNewBrandExclusions();

    // Remove additional filler text
    await removeAdditionalFillerText();

    console.log('\nComprehensive final parsing rules applied successfully!');

  } catch (error) {
    console.error('Error:', error);
  }
}

async function handleMissedMultiPacks() {
  console.log('Handling missed multi-pack records...');

  // Specific records that should be marked as packs
  const missedPackIds = [69314, 66988, 66987, 64587, 39110, 39112, 48063, 39862, 66914, 60104];

  for (const id of missedPackIds) {
    const { data: record, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error(`Error fetching record ${id}:`, fetchError);
      continue;
    }

    const updateData = {
      notes: 'XXXX Pack'
    };

    if (!record.raw_notes) {
      updateData.raw_notes = record.notes;
    }

    const { error: updateError } = await supabase
      .from('t_sdasins')
      .update(updateData)
      .eq('id', id);

    if (updateError) {
      console.error(`Error updating record ${id}:`, updateError);
    } else {
      console.log(`  Updated ID ${id}: XXXX Pack`);
    }
  }

  // Also look for other pack patterns that might have been missed
  const additionalPackPatterns = [
    'Disc Golf Set',
    'Disc Set',
    'Battle Pack',
    'Beginner Set'
  ];

  for (const pattern of additionalPackPatterns) {
    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .like('notes', `%${pattern}%`)
      .not('notes', 'like', 'XXXX%');

    if (fetchError) {
      console.error(`Error fetching records for pattern "${pattern}":`, fetchError);
      continue;
    }

    for (const record of matchingRecords || []) {
      const updateData = {
        notes: 'XXXX Pack'
      };

      if (!record.raw_notes) {
        updateData.raw_notes = record.notes;
      }

      const { error: updateError } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', record.id);

      if (updateError) {
        console.error(`Error updating record ${record.id}:`, updateError);
      }
    }

    console.log(`  Updated ${matchingRecords?.length || 0} records with pattern "${pattern}"`);
  }

  console.log('Missed multi-pack records handled.\n');
}

async function handleStorageRacks() {
  console.log('Handling storage rack records...');

  const { data: storageRecords, error: fetchError } = await supabase
    .from('t_sdasins')
    .select('id, notes, raw_notes')
    .like('notes', '%Storage Rack%')
    .not('notes', 'like', 'XXXX%');

  if (fetchError) {
    console.error('Error fetching storage rack records:', fetchError);
    return;
  }

  for (const record of storageRecords || []) {
    const updateData = {
      notes: 'XXXX Accessory'
    };

    if (!record.raw_notes) {
      updateData.raw_notes = record.notes;
    }

    const { error: updateError } = await supabase
      .from('t_sdasins')
      .update(updateData)
      .eq('id', record.id);

    if (updateError) {
      console.error(`Error updating record ${record.id}:`, updateError);
    }
  }

  console.log(`Updated ${storageRecords?.length || 0} storage rack records\n`);
}

async function handleDyeMaxRecords() {
  console.log('Handling DyeMax records...');

  const { data: dyeMaxRecords, error: fetchError } = await supabase
    .from('t_sdasins')
    .select('id, notes, raw_notes')
    .like('notes', '%DyeMax%')
    .not('notes', 'like', 'XXXX%');

  if (fetchError) {
    console.error('Error fetching DyeMax records:', fetchError);
    return;
  }

  for (const record of dyeMaxRecords || []) {
    const updateData = {
      notes: 'XXXX DyeMax'
    };

    if (!record.raw_notes) {
      updateData.raw_notes = record.notes;
    }

    const { error: updateError } = await supabase
      .from('t_sdasins')
      .update(updateData)
      .eq('id', record.id);

    if (updateError) {
      console.error(`Error updating record ${record.id}:`, updateError);
    }
  }

  console.log(`Updated ${dyeMaxRecords?.length || 0} DyeMax records\n`);
}

async function handleNewBrandExclusions() {
  console.log('Handling new brand exclusions...');

  const newBrandExclusions = [
    { contains: 'Above Level', replacement: 'XXXX Above Level' },
    { contains: 'Bernoulli', replacement: 'XXXX Bernoulli' }
  ];

  for (const brandCase of newBrandExclusions) {
    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .like('notes', `%${brandCase.contains}%`)
      .not('notes', 'like', 'XXXX%');

    if (fetchError) {
      console.error(`Error fetching records for ${brandCase.contains}:`, fetchError);
      continue;
    }

    for (const record of matchingRecords || []) {
      const updateData = {
        notes: brandCase.replacement
      };

      if (!record.raw_notes) {
        updateData.raw_notes = record.notes;
      }

      const { error: updateError } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', record.id);

      if (updateError) {
        console.error(`Error updating record ${record.id}:`, updateError);
      }
    }

    console.log(`Updated ${matchingRecords?.length || 0} records containing "${brandCase.contains}"`);
  }

  console.log('New brand exclusions handled.\n');
}

async function removeAdditionalFillerText() {
  console.log('Removing additional filler text...');

  const additionalFillerPhrases = [
    'Putt & Approach Golf Disc',
    'Mid-Range Disc Golf Disc',
    'Lightweight w/Accurate Flight',
    'Fairway Driver Golf Disc',
    'Driver Golf Disc',
    'Approach Disc',
    'Grippy Line Plastic',
    'Frisbee',
    'Gram'
  ];

  let totalUpdated = 0;

  for (const phrase of additionalFillerPhrases) {
    console.log(`Removing "${phrase}"...`);

    const { data: matchingRecords, error: fetchError } = await supabase
      .from('t_sdasins')
      .select('id, notes, raw_notes')
      .like('notes', `%${phrase}%`)
      .not('notes', 'like', 'XXXX%');

    if (fetchError) {
      console.error(`Error fetching records for "${phrase}":`, fetchError);
      continue;
    }

    let phraseUpdated = 0;

    for (const record of matchingRecords || []) {
      const updatedNotes = record.notes.replace(new RegExp(escapeRegex(phrase), 'gi'), '').trim();
      
      const cleanedNotes = updatedNotes
        .replace(/\s+/g, ' ')
        .replace(/^\s*[-|,]\s*/, '')
        .replace(/\s*[-|,]\s*$/, '')
        .trim();

      const updateData = {
        notes: cleanedNotes
      };

      if (!record.raw_notes) {
        updateData.raw_notes = record.notes;
      }

      const { error: updateError } = await supabase
        .from('t_sdasins')
        .update(updateData)
        .eq('id', record.id);

      if (updateError) {
        console.error(`Error updating record ${record.id}:`, updateError);
      } else {
        phraseUpdated++;
        totalUpdated++;
      }
    }

    console.log(`  Removed from ${phraseUpdated} records`);
  }

  console.log(`\nTotal records updated with additional filler text removal: ${totalUpdated}`);
}

function escapeRegex(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Run the script
applyComprehensiveFinalRules();
