import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Cache for lookup data
let brandsCache = {};
let moldsCache = {};
let plasticsCache = {};
let stampsCache = [];

async function loadLookupData() {
    console.log('Loading lookup data...');
    
    // Load all brands
    const { data: brands, error: brandsError } = await supabase
        .from('t_brands')
        .select('id, brand');
    
    if (brandsError) {
        throw new Error(`Error loading brands: ${brandsError.message}`);
    }
    
    // Create brand lookup
    brands.forEach(brand => {
        brandsCache[brand.brand] = brand.id;
    });
    
    // Load all molds with their brand_id
    const { data: molds, error: moldsError } = await supabase
        .from('t_molds')
        .select('mold, brand_id');
    
    if (moldsError) {
        throw new Error(`Error loading molds: ${moldsError.message}`);
    }
    
    // Group molds by brand_id
    molds.forEach(mold => {
        if (!moldsCache[mold.brand_id]) {
            moldsCache[mold.brand_id] = [];
        }
        moldsCache[mold.brand_id].push(mold.mold);
    });
    
    // Load all plastics with their brand_id
    const { data: plastics, error: plasticsError } = await supabase
        .from('t_plastics')
        .select('plastic, brand_id');
    
    if (plasticsError) {
        throw new Error(`Error loading plastics: ${plasticsError.message}`);
    }
    
    // Group plastics by brand_id
    plastics.forEach(plastic => {
        if (!plasticsCache[plastic.brand_id]) {
            plasticsCache[plastic.brand_id] = [];
        }
        plasticsCache[plastic.brand_id].push(plastic.plastic);
    });
    
    // Load all stamps (not brand-specific)
    const { data: stamps, error: stampsError } = await supabase
        .from('t_stamps')
        .select('stamp');
    
    if (stampsError) {
        throw new Error(`Error loading stamps: ${stampsError.message}`);
    }
    
    stampsCache = stamps.map(s => s.stamp);
    
    console.log(`Loaded ${brands.length} brands, ${molds.length} molds, ${plastics.length} plastics, ${stampsCache.length} stamps`);
}

function parseVariantTitle(variantTitle, brandId) {
    if (!variantTitle || typeof variantTitle !== 'string') {
        return { mold: null, plastic: null, stamp: null };
    }
    
    const title = variantTitle.trim();
    
    // Skip default titles
    if (title === 'Default Title' || title === 'Assorted') {
        return { mold: null, plastic: null, stamp: null };
    }
    
    let foundMold = null;
    let foundPlastic = null;
    let foundStamp = 'Stock'; // Default stamp
    
    // Get molds and plastics for this brand
    const brandMolds = moldsCache[brandId] || [];
    const brandPlastics = plasticsCache[brandId] || [];
    
    // For DyeMax variants like "Fuzion Verdict (Midrange)"
    // Pattern: "Plastic Mold (Type)" or "Plastic Mold"
    
    // Remove type information in parentheses
    const cleanTitle = title.replace(/\s*\([^)]*\)\s*$/, '').trim();
    
    // Find mold - look for exact matches (prioritize longer matches)
    const sortedMolds = [...brandMolds].sort((a, b) => b.length - a.length);
    for (const mold of sortedMolds) {
        if (cleanTitle.includes(mold)) {
            foundMold = mold;
            break;
        }
    }
    
    // Find plastic - look for exact matches (prioritize longer matches)
    const sortedPlastics = [...brandPlastics].sort((a, b) => b.length - a.length);
    for (const plastic of sortedPlastics) {
        if (cleanTitle.includes(plastic)) {
            foundPlastic = plastic;
            break;
        }
    }
    
    // Extract stamp - everything after plastic and mold
    if (foundMold && foundPlastic) {
        // Remove plastic and mold from title to get stamp
        let remainingTitle = cleanTitle;
        remainingTitle = remainingTitle.replace(foundPlastic, '').trim();
        remainingTitle = remainingTitle.replace(foundMold, '').trim();
        
        // Clean up remaining text
        remainingTitle = remainingTitle.replace(/^[-\s]+|[-\s]+$/g, '').trim();
        
        if (remainingTitle && remainingTitle !== '') {
            foundStamp = remainingTitle;
        }
    }
    
    return {
        mold: foundMold,
        plastic: foundPlastic,
        stamp: foundStamp
    };
}

function parseProductTitle(productTitle, brandId) {
    if (!productTitle || typeof productTitle !== 'string') {
        return { mold: null, plastic: null, stamp: null };
    }

    const title = productTitle.trim();

    // Skip non-disc products
    if (title.includes('Set') || title.includes('Mystery') || title.includes('Box') ||
        title.includes('DyeMax')) {
        return { mold: null, plastic: null, stamp: null };
    }

    // Get molds and plastics for this brand
    const brandMolds = moldsCache[brandId] || [];
    const brandPlastics = plasticsCache[brandId] || [];

    let foundMold = null;
    let foundPlastic = null;
    let foundStamp = 'Stock';

    // Standard pattern: "Plastic Mold - Stamp" or "Plastic Mold"

    // Find plastic at the beginning
    const sortedPlastics = [...brandPlastics].sort((a, b) => b.length - a.length);
    for (const plastic of sortedPlastics) {
        if (title.startsWith(plastic + ' ') || title === plastic) {
            foundPlastic = plastic;
            break;
        }
    }

    if (foundPlastic) {
        // Remove plastic from title to find mold and stamp
        let remainingTitle = title.substring(foundPlastic.length).trim();

        // Check if there's a dash indicating a stamp
        const dashIndex = remainingTitle.indexOf(' - ');
        let moldPart = remainingTitle;
        let stampPart = '';

        if (dashIndex !== -1) {
            moldPart = remainingTitle.substring(0, dashIndex).trim();
            stampPart = remainingTitle.substring(dashIndex + 3).trim();
        }

        // Find mold in the mold part
        const sortedMolds = [...brandMolds].sort((a, b) => b.length - a.length);
        for (const mold of sortedMolds) {
            if (moldPart.startsWith(mold + ' ') || moldPart === mold) {
                foundMold = mold;
                break;
            }
        }

        // Set stamp
        if (stampPart) {
            foundStamp = stampPart;
        } else if (foundMold && moldPart.length > foundMold.length) {
            // Check if there's additional text after mold (without dash)
            const afterMold = moldPart.substring(foundMold.length).trim();
            if (afterMold) {
                foundStamp = afterMold;
            }
        }
    }

    return {
        mold: foundMold,
        plastic: foundPlastic,
        stamp: foundStamp
    };
}

async function updateAllParsedFields() {
    console.log('Starting product parsing for all vendors...');
    
    try {
        // Load lookup data
        await loadLookupData();
        
        // Get all disc records
        console.log('Fetching all disc records...');
        const { data: records, error: fetchError } = await supabase
            .from('it_dd_osl')
            .select('id, product_title, variant_title, product_vendor, product_product_type')
            .eq('product_product_type', 'Discs');
        
        if (fetchError) {
            console.error('Error fetching records:', fetchError);
            return;
        }
        
        console.log(`Found ${records.length} disc records`);
        
        // Parse titles and prepare updates
        const updates = [];
        const parseStats = {
            total: records.length,
            parsed_mold: 0,
            parsed_plastic: 0,
            parsed_stamp: 0,
            failed: 0,
            by_vendor: {},
            samples: []
        };
        
        for (const record of records) {
            const brandId = brandsCache[record.product_vendor];
            
            if (!brandId) {
                console.log(`Warning: Brand not found for vendor "${record.product_vendor}"`);
                continue;
            }
            
            // Try parsing variant title first (for DyeMax products)
            let parsed = parseVariantTitle(record.variant_title, brandId);
            
            // If that fails, try product title
            if (!parsed.mold && !parsed.plastic) {
                parsed = parseProductTitle(record.product_title, brandId);
            }
            
            const update = {
                id: record.id,
                parsed_mold: parsed.mold,
                parsed_plastic: parsed.plastic,
                parsed_stamp: parsed.stamp
            };
            
            updates.push(update);
            
            // Update statistics
            if (parsed.mold) parseStats.parsed_mold++;
            if (parsed.plastic) parseStats.parsed_plastic++;
            if (parsed.stamp) parseStats.parsed_stamp++;
            if (!parsed.mold && !parsed.plastic && !parsed.stamp) parseStats.failed++;
            
            // Track by vendor
            if (!parseStats.by_vendor[record.product_vendor]) {
                parseStats.by_vendor[record.product_vendor] = { total: 0, parsed: 0 };
            }
            parseStats.by_vendor[record.product_vendor].total++;
            if (parsed.mold || parsed.plastic) {
                parseStats.by_vendor[record.product_vendor].parsed++;
            }
            
            // Collect samples for review
            if (parseStats.samples.length < 15) {
                parseStats.samples.push({
                    vendor: record.product_vendor,
                    product_title: record.product_title,
                    variant_title: record.variant_title,
                    mold: parsed.mold,
                    plastic: parsed.plastic,
                    stamp: parsed.stamp
                });
            }
        }
        
        console.log(`\nParsing complete:`);
        console.log(`- Records with mold: ${parseStats.parsed_mold}`);
        console.log(`- Records with plastic: ${parseStats.parsed_plastic}`);
        console.log(`- Records with stamp: ${parseStats.parsed_stamp}`);
        console.log(`- Failed to parse: ${parseStats.failed}`);
        
        // Show vendor breakdown
        console.log('\nParsing success by vendor:');
        console.log('=====================================');
        Object.entries(parseStats.by_vendor).forEach(([vendor, stats]) => {
            const percentage = ((stats.parsed / stats.total) * 100).toFixed(1);
            console.log(`${vendor}: ${stats.parsed}/${stats.total} (${percentage}%)`);
        });
        
        // Show sample results
        console.log('\nSample parsing results:');
        console.log('=====================================');
        parseStats.samples.forEach((sample, index) => {
            console.log(`${index + 1}. [${sample.vendor}] "${sample.product_title}"`);
            console.log(`   Variant: "${sample.variant_title}"`);
            console.log(`   → Plastic: ${sample.plastic || 'NOT FOUND'}`);
            console.log(`   → Mold: ${sample.mold || 'NOT FOUND'}`);
            console.log(`   → Stamp: ${sample.stamp || 'NOT FOUND'}`);
            console.log('---');
        });
        
        if (updates.length === 0) {
            console.log('No updates to perform.');
            return;
        }
        
        // Update records in batches
        console.log(`\nUpdating ${updates.length} records...`);
        const batchSize = 100;
        let updatedCount = 0;
        
        for (let i = 0; i < updates.length; i += batchSize) {
            const batch = updates.slice(i, i + batchSize);
            
            for (const update of batch) {
                const { error: updateError } = await supabase
                    .from('it_dd_osl')
                    .update({
                        parsed_mold: update.parsed_mold,
                        parsed_plastic: update.parsed_plastic,
                        parsed_stamp: update.parsed_stamp
                    })
                    .eq('id', update.id);
                
                if (updateError) {
                    console.error(`Error updating record ${update.id}:`, updateError);
                } else {
                    updatedCount++;
                }
            }
            
            console.log(`Updated batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(updates.length/batchSize)} (${updatedCount}/${updates.length} records)`);
        }
        
        console.log(`\nUpdate complete! Updated ${updatedCount} records.`);
        
    } catch (error) {
        console.error('Script failed:', error);
    }
}

// Run the script
console.log('Multi-Vendor Product Parser\n');

updateAllParsedFields()
    .then(() => {
        console.log('\nProduct parsing completed successfully');
        process.exit(0);
    })
    .catch(error => {
        console.error('Product parsing failed:', error);
        process.exit(1);
    });

export { parseVariantTitle, parseProductTitle, updateAllParsedFields };
