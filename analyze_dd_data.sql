-- Analysis queries for Dynamic Discs Distribution data

-- 1. Basic counts and overview
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT product_id) as unique_products,
    COUNT(DISTINCT variant_id) as unique_variants,
    COUNT(DISTINCT product_vendor) as unique_vendors,
    COUNT(DISTINCT product_product_type) as unique_product_types
FROM test_dd_data;

-- 2. Vendor breakdown
SELECT 
    product_vendor,
    COUNT(*) as variant_count,
    COUNT(DISTINCT product_id) as product_count,
    COUNT(CASE WHEN variant_available = true THEN 1 END) as available_variants,
    ROUND(AVG(variant_price), 2) as avg_price
FROM test_dd_data 
WHERE product_vendor IS NOT NULL
GROUP BY product_vendor
ORDER BY variant_count DESC;

-- 3. Product type breakdown
SELECT 
    product_product_type,
    COUNT(*) as variant_count,
    COUNT(DISTINCT product_id) as product_count,
    COUNT(CASE WHEN variant_available = true THEN 1 END) as available_variants,
    ROUND(AVG(variant_price), 2) as avg_price
FROM test_dd_data 
WHERE product_product_type IS NOT NULL
GROUP BY product_product_type
ORDER BY variant_count DESC;

-- 4. Dynamic Discs products specifically
SELECT 
    product_title,
    variant_title,
    variant_sku,
    variant_price,
    variant_available,
    variant_option1 as color,
    variant_option2 as weight,
    variant_option3 as other_option
FROM test_dd_data 
WHERE product_vendor = 'Dynamic Discs'
ORDER BY product_title, variant_position;

-- 5. Price analysis
SELECT 
    product_vendor,
    MIN(variant_price) as min_price,
    MAX(variant_price) as max_price,
    ROUND(AVG(variant_price), 2) as avg_price,
    ROUND(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY variant_price), 2) as median_price
FROM test_dd_data 
WHERE variant_price IS NOT NULL
GROUP BY product_vendor
ORDER BY avg_price DESC;

-- 6. Availability analysis
SELECT 
    product_vendor,
    COUNT(*) as total_variants,
    COUNT(CASE WHEN variant_available = true THEN 1 END) as available_variants,
    ROUND(
        COUNT(CASE WHEN variant_available = true THEN 1 END) * 100.0 / COUNT(*), 
        1
    ) as availability_percentage
FROM test_dd_data 
WHERE product_vendor IS NOT NULL
GROUP BY product_vendor
ORDER BY availability_percentage DESC;

-- 7. Most common disc colors (option1)
SELECT 
    variant_option1 as color,
    COUNT(*) as count,
    COUNT(CASE WHEN variant_available = true THEN 1 END) as available_count
FROM test_dd_data 
WHERE variant_option1 IS NOT NULL 
    AND product_product_type = 'Discs'
GROUP BY variant_option1
ORDER BY count DESC
LIMIT 20;

-- 8. Most common disc weights (option2)
SELECT 
    variant_option2 as weight,
    COUNT(*) as count,
    COUNT(CASE WHEN variant_available = true THEN 1 END) as available_count
FROM test_dd_data 
WHERE variant_option2 IS NOT NULL 
    AND product_product_type = 'Discs'
GROUP BY variant_option2
ORDER BY count DESC
LIMIT 20;

-- 9. Source file breakdown
SELECT 
    source_file,
    COUNT(*) as record_count,
    COUNT(DISTINCT product_id) as unique_products,
    MIN(imported_at) as import_time
FROM test_dd_data
GROUP BY source_file
ORDER BY source_file;

-- 10. Products with multiple variants
SELECT 
    product_vendor,
    product_title,
    COUNT(*) as variant_count,
    STRING_AGG(DISTINCT variant_option1, ', ') as colors,
    STRING_AGG(DISTINCT variant_option2, ', ') as weights
FROM test_dd_data 
WHERE variant_id IS NOT NULL
GROUP BY product_vendor, product_title, product_id
HAVING COUNT(*) > 1
ORDER BY variant_count DESC
LIMIT 20;
