// verifyImage.js
import dotenv from 'dotenv';
dotenv.config();

import minimist from 'minimist';
import fetch from 'node-fetch';
import { createClient } from '@supabase/supabase-js';

// Parse command-line arguments (e.g., --id=68601)
// This id is the t_images record id
const args = minimist(process.argv.slice(2));
const id = args.id;
if (!id) {
  console.error('[verifyImage.js] Missing required --id= argument.');
  process.exit(1);
}

console.log(`[verifyImage.js] Received t_images id=${id}`);

// Create a Supabase client using your service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY; // Make sure this is your service role key!
console.log(`[verifyImage.js] Supabase URL: ${supabaseUrl}`);
console.log(`[verifyImage.js] Supabase Key length: ${supabaseKey ? supabaseKey.length : 'none'}`);

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to log errors to t_error_logs
async function logError(errorMessage, context) {
  try {
    const { error } = await supabase
      .from("t_error_logs")
      .insert({ error_message: errorMessage, context: context });
    if (error) {
      console.error(`[verifyImage.js] Failed to log error: ${error.message}`);
    }
  } catch (err) {
    console.error(`[verifyImage.js] Exception while logging error: ${err.message}`);
  }
}

async function main() {

  // 1. Retrieve the t_images record using its id
  console.log('[verifyImage.js] Waiting 5 seconds before querying t_images record...');
  await new Promise(resolve => setTimeout(resolve, 5000));
  console.log(`[verifyImage.js] Retrieving t_images record for id=${id}...`);
  const { data: imageRecord, error: fetchError } = await supabase
    .from('t_images')
    .select('*')
    .eq('id', id)
    .maybeSingle();
    
  if (fetchError) {
    const errMsg = `[verifyImage.js] Error retrieving t_images record: ${fetchError.message}`;
    console.error(errMsg);
    await logError(errMsg, `Retrieving t_images record for id=${id}`);
    process.exit(1);
  }
  if (!imageRecord) {
    const errMsg = `[verifyImage.js] No t_images record found for id=${id}`;
    console.error(errMsg);
    await logError(errMsg, `No t_images record for id=${id}`);
    process.exit(1);
  }
  console.log(`[verifyImage.js] Retrieved t_images record: ${JSON.stringify(imageRecord)}`);

  // 2. Retrieve configuration values from t_config:
  console.log(`[verifyImage.js] Retrieving configuration from t_config...`);
  const { data: configData, error: configError } = await supabase
    .from('t_config')
    .select('key, value')
    .in('key', ['public_image_server', 'folder_brands', 'folder_discs', 'folder_molds', 'folder_mps', 'folder_players', 'folder_product_variants']);
  if (configError) {
    const errMsg = `[verifyImage.js] Error retrieving t_config: ${configError.message}`;
    console.error(errMsg);
    await logError(errMsg, "Retrieving t_config values");
    process.exit(1);
  }
  
  const publicImageServer = configData.find(c => c.key === 'public_image_server')?.value;
  if (!publicImageServer) {
    const errMsg = '[verifyImage.js] public_image_server not found in t_config';
    console.error(errMsg);
    await logError(errMsg, "Missing public_image_server in t_config");
    process.exit(1);
  }
  console.log(`[verifyImage.js] public_image_server: ${publicImageServer}`);

  // 3. Determine the folder key based on imageRecord.table_name
  let folderKey;
  switch (imageRecord.table_name) {
    case 't_brands':
      folderKey = 'folder_brands';
      break;
    case 't_discs':
      folderKey = 'folder_discs';
      break;
    case 't_molds':
      folderKey = 'folder_molds';
      break;
    case 't_mps':
      folderKey = 'folder_mps';
      break;
    case 't_players':
      folderKey = 'folder_players';
      break;
    case 't_product_variants':
      folderKey = 'folder_product_variants';
      break;
    default:
      const errMsg = `[verifyImage.js] Unsupported table_name: ${imageRecord.table_name}`;
      console.error(errMsg);
      await logError(errMsg, "Determining folderKey from table_name");
      process.exit(1);
  }
  const folderValue = configData.find(c => c.key === folderKey)?.value;
  if (!folderValue) {
    const errMsg = `[verifyImage.js] ${folderKey} not found in t_config`;
    console.error(errMsg);
    await logError(errMsg, `Missing ${folderKey} in t_config`);
    process.exit(1);
  }
  console.log(`[verifyImage.js] Using folder for ${imageRecord.table_name} (${folderKey}): ${folderValue}`);

  // 4. Determine the file name:
  // For t_discs, retrieve the image_file_name using imageRecord.record_id.
  let fileName = imageRecord.record_id; // default fallback for non-discs records
  if (imageRecord.table_name === 't_discs') {
    const { data: discRecord, error: discError } = await supabase
      .from('t_discs')
      .select('image_file_name')
      .eq('id', imageRecord.record_id)
      .maybeSingle();
    if (discError) {
      const errMsg = `[verifyImage.js] Error retrieving t_discs record for id=${imageRecord.record_id}: ${discError.message}`;
      console.error(errMsg);
      await logError(errMsg, `Retrieving t_discs record for id=${imageRecord.record_id}`);
      process.exit(1);
    }
    if (discRecord && discRecord.image_file_name) {
      fileName = discRecord.image_file_name;
    } else {
      const errMsg = `[verifyImage.js] No image_file_name found for t_discs record id=${imageRecord.record_id}`;
      console.error(errMsg);
      await logError(errMsg, `Missing image_file_name for t_discs record id=${imageRecord.record_id}`);
      process.exit(1);
    }
  }

  // 5. Construct the image URL:
  // For t_discs, use a subfolder (first 6 characters of the fileName).
  let constructedImageUrl;
  if (imageRecord.table_name === 't_discs') {
    const subfolder = fileName.substring(0, 6);
    constructedImageUrl = `${publicImageServer}/${folderValue}/${subfolder}/${fileName}.jpg`;
  } else {
    constructedImageUrl = `${publicImageServer}/${folderValue}/${fileName}.jpg`;
  }
  console.log(`[verifyImage.js] Constructed image URL: ${constructedImageUrl}`);

  // 6. Perform a HEAD request to check image accessibility
  console.log(`[verifyImage.js] Performing HEAD request for URL: ${constructedImageUrl}`);
  const response = await fetch(constructedImageUrl, { method: 'HEAD' });
  console.log(`[verifyImage.js] HEAD request completed. Status: ${response.status}`);
  const isOk = response.ok;
  console.log(`[verifyImage.js] Image accessible: ${isOk}`);

  // 7. Build the update payload
  const updatePayload = {
    image_verified: isOk,
    image_verified_at: new Date().toISOString(),
    image_verified_notes: isOk
      ? 'Success! Image is accessible'
      : `Not Found! Image not accessible, status code: ${response.status} for URL: ${constructedImageUrl}`
  };
  console.log(`[verifyImage.js] Update payload for t_images record id=${id}: ${JSON.stringify(updatePayload)}`);

  // 8. Update the t_images row using its id
  const { error: updateError } = await supabase
    .from('t_images')
    .update(updatePayload)
    .eq('id', id);
  if (updateError) {
    const errMsg = `[verifyImage.js] Error updating t_images record for id=${id}: ${updateError.message}`;
    console.error(errMsg);
    await logError(errMsg, `Updating t_images record for id=${id}`);
  } else {
    console.log(`[verifyImage.js] Successfully updated t_images record for id=${id}`);
  }

  // 9. If this is a product variant and the image was not found, append a TODO note
  if (!isOk && imageRecord.table_name === 't_product_variants') {
    try {
      const { data: pvRecord, error: pvFetchError } = await supabase
        .from('t_product_variants')
        .select('todo')
        .eq('id', imageRecord.record_id)
        .maybeSingle();

      if (pvFetchError) {
        const errMsg = `[verifyImage.js] Error retrieving t_product_variants id=${imageRecord.record_id} to append TODO: ${pvFetchError.message}`;
        console.error(errMsg);
        await logError(errMsg, `Retrieving t_product_variants for id=${imageRecord.record_id}`);
      } else {
        const appendNote = `Image: checked ${new Date().toISOString()} and not found`;
        const newTodo = pvRecord && pvRecord.todo && pvRecord.todo.trim().length > 0
          ? `${pvRecord.todo}\n${appendNote}`
          : appendNote;

        const { error: pvUpdateError } = await supabase
          .from('t_product_variants')
          .update({ todo: newTodo })
          .eq('id', imageRecord.record_id);

        if (pvUpdateError) {
          const errMsg = `[verifyImage.js] Error updating t_product_variants.todo for id=${imageRecord.record_id}: ${pvUpdateError.message}`;
          console.error(errMsg);
          await logError(errMsg, `Updating t_product_variants.todo for id=${imageRecord.record_id}`);
        } else {
          console.log(`[verifyImage.js] Appended TODO note to t_product_variants id=${imageRecord.record_id}`);
        }
      }
    } catch (pvErr) {
      const errMsg = `[verifyImage.js] Exception updating t_product_variants TODO for id=${imageRecord.record_id}: ${pvErr.message}`;
      console.error(errMsg);
      await logError(errMsg, `Exception updating t_product_variants TODO for id=${imageRecord.record_id}`);
    }
  }
}

main().catch(async err => {
  console.error(`[verifyImage.js] Unexpected error: ${err.message}`);
  await logError(`[verifyImage.js] Unexpected error: ${err.message}`, "Main function");
  process.exit(1);
});
