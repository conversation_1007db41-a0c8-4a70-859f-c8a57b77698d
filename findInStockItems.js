// findInStockItems.js - Find RPRO records with quantity > 0 to test the readiness logic
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function findInStockItems() {
  try {
    console.log('🔍 Finding RPRO records with quantity > 0...');
    console.log('============================================');

    // Find records with quantity > 0
    const { data: inStockRecords, error: stockError } = await supabase
      .from('imported_table_rpro')
      .select('id, ivno, ivqtylaw, ivaux3, todo')
      .gt('ivqtylaw', 0)
      .limit(20);

    if (stockError) {
      console.error('❌ Error fetching in-stock records:', stockError.message);
      return;
    }

    console.log(`📊 Found ${inStockRecords.length} records with quantity > 0`);

    if (inStockRecords.length === 0) {
      console.log('ℹ️  No records found with quantity > 0');
      return;
    }

    console.log('\n📦 In-stock records:');
    console.log('====================');
    inStockRecords.forEach(record => {
      const hasIssue = record.ivqtylaw > 0 && (!record.ivaux3 || record.ivaux3.trim() === '');
      console.log(`  IVNO: ${record.ivno}`);
      console.log(`    Qty: ${record.ivqtylaw}`);
      console.log(`    Bin: ${record.ivaux3 || 'null'}`);
      console.log(`    Todo: ${record.todo || 'null'}`);
      console.log(`    Should have issue: ${hasIssue ? 'YES' : 'NO'}`);
      console.log('');
    });

    // Check if any have been processed yet
    const processedInStock = inStockRecords.filter(r => r.todo !== null);
    console.log(`📋 Processed in-stock records: ${processedInStock.length}`);

    if (processedInStock.length > 0) {
      console.log('\n✅ Processed in-stock records:');
      processedInStock.forEach(record => {
        console.log(`  IVNO: ${record.ivno}, Qty: ${record.ivqtylaw}, Bin: ${record.ivaux3 || 'null'}`);
        console.log(`    Todo: ${record.todo}`);
        console.log('');
      });
    }

  } catch (err) {
    console.error('❌ Exception:', err.message);
  }
}

findInStockItems();
