import fs from 'fs';
import path from 'path';

async function testCompleteFilenameUpdate() {
  try {
    console.log('🧪 Testing complete filename update with date and time...\n');
    
    // Test 1: Automation filename format
    console.log('1️⃣ Testing automation filename format:');
    const automationFilename = `discraft_order_${new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0]}.xlsx`;
    console.log(`   Generated: ${automationFilename}`);
    
    // Verify format
    const automationPattern = /^discraft_order_\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}\.xlsx$/;
    console.log(`   Format check: ${automationPattern.test(automationFilename) ? '✅ CORRECT' : '❌ INCORRECT'}`);
    
    // Test 2: Admin server filename format
    console.log('\n2️⃣ Testing admin server filename format:');
    const now = new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0];
    const adminFilename = `discraftstock_${now}.xlsx`;
    console.log(`   Generated: ${adminFilename}`);
    
    // Verify format
    const adminPattern = /^discraftstock_\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}\.xlsx$/;
    console.log(`   Format check: ${adminPattern.test(adminFilename) ? '✅ CORRECT' : '❌ INCORRECT'}`);
    
    // Test 3: API export test
    console.log('\n3️⃣ Testing API export with new filename:');
    
    const response = await fetch('http://localhost:3001/api/discraft/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        includeHeader: true,
        filename: `discraft_order_${new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0]}.xlsx`
      })
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log(`   API Response: ✅ SUCCESS`);
      console.log(`   Filename: ${result.filename}`);
      console.log(`   Records: ${result.totalRecords}`);
      
      // Check if file exists
      if (fs.existsSync(result.filePath)) {
        console.log(`   File exists: ✅ YES`);
        const stats = fs.statSync(result.filePath);
        console.log(`   File size: ${stats.size} bytes`);
        console.log(`   Created: ${stats.birthtime.toLocaleString()}`);
      } else {
        console.log(`   File exists: ❌ NO`);
      }
    } else {
      console.log(`   API Response: ❌ FAILED (${response.status})`);
    }
    
    // Test 4: Show comparison with old vs new format
    console.log('\n4️⃣ Format comparison:');
    const oldFormat = `discraft_order_${new Date().toISOString().split('T')[0]}.xlsx`;
    const newFormat = `discraft_order_${new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0]}.xlsx`;
    
    console.log(`   Old format: ${oldFormat}`);
    console.log(`   New format: ${newFormat}`);
    console.log(`   Difference: Added time (HH-MM-SS) to filename`);
    
    // Test 5: Show multiple timestamps to demonstrate uniqueness
    console.log('\n5️⃣ Demonstrating filename uniqueness:');
    for (let i = 0; i < 3; i++) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
      const uniqueFilename = `discraft_order_${new Date().toISOString().replace(/T/, '-').replace(/:/g, '-').split('.')[0]}.xlsx`;
      console.log(`   ${i + 1}. ${uniqueFilename}`);
    }
    
    console.log('\n✅ All filename format tests completed successfully!');
    console.log('\n📋 Summary of changes:');
    console.log('   • Automation files now include time: discraft_order_YYYY-MM-DD-HH-MM-SS.xlsx');
    console.log('   • Admin export files now include time: discraftstock_YYYY-MM-DD-HH-MM-SS.xlsx');
    console.log('   • Email attachments automatically use the new format');
    console.log('   • Files are now unique even when generated within the same day');
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testCompleteFilenameUpdate().catch(console.error);
