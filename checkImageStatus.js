// checkImageStatus.js
import dotenv from 'dotenv';
dotenv.config();

import { createClient } from '@supabase/supabase-js';
import minimist from 'minimist';

// Parse command-line arguments
const args = minimist(process.argv.slice(2));
const id = args.id;

// Create a Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: SUPABASE_URL and/or SUPABASE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function main() {
  try {
    console.log('Checking t_images records...');
    
    let query = supabase
      .from('t_images')
      .select('*');
      
    if (id) {
      console.log(`Checking specific t_images record with id=${id}`);
      query = query.eq('id', id);
    } else {
      console.log('Checking recent t_images records');
      query = query.order('id', { ascending: false }).limit(10);
    }
    
    const { data, error } = await query;
    
    if (error) {
      console.error(`Error fetching t_images records: ${error.message}`);
      return;
    }
    
    if (!data || data.length === 0) {
      console.log('No t_images records found');
      return;
    }
    
    console.log(`Found ${data.length} t_images records:`);
    
    data.forEach(record => {
      console.log(`\nRecord ID: ${record.id}`);
      console.log(`Table Name: ${record.table_name}`);
      console.log(`Record ID: ${record.record_id}`);
      console.log(`Image Verified: ${record.image_verified}`);
      console.log(`Image Verified At: ${record.image_verified_at}`);
      console.log(`Image Verified Notes: ${record.image_verified_notes}`);
      console.log(`Created By: ${record.created_by}`);
      console.log(`Created At: ${record.created_at}`);
      console.log(`Updated At: ${record.updated_at}`);
      console.log(`Updated By: ${record.updated_by}`);
    });
    
    // Also check the t_error_logs table for any recent errors
    console.log('\nChecking recent error logs...');
    
    const { data: errorLogs, error: errorLogsError } = await supabase
      .from('t_error_logs')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(5);
      
    if (errorLogsError) {
      console.error(`Error fetching error logs: ${errorLogsError.message}`);
      return;
    }
    
    if (!errorLogs || errorLogs.length === 0) {
      console.log('No recent error logs found');
      return;
    }
    
    console.log(`Found ${errorLogs.length} recent error logs:`);
    
    errorLogs.forEach(log => {
      console.log(`\nError ID: ${log.id}`);
      console.log(`Error Message: ${log.error_message}`);
      console.log(`Context: ${log.context}`);
      console.log(`Created At: ${log.created_at}`);
      console.log(`Created By: ${log.created_by}`);
    });
  } catch (err) {
    console.error(`Unexpected error: ${err.message}`);
    process.exit(1);
  }
}

main();
