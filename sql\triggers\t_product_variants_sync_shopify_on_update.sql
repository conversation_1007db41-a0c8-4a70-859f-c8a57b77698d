-- Consolidated trigger + enqueuer to sync many product variant fields to Shopify
-- Debounce/coalesce window: 1 minute (configurable in call)
-- Task type: sync_product_variant_to_shopify

-- Helper to upsert a single pending sync task per variant and merge changes
CREATE OR REPLACE FUNCTION public.enqueue_sync_pv_to_shopify(
  p_variant_id BIGINT,
  p_changes JSONB,
  p_debounce INTERVAL DEFAULT INTERVAL '1 minute',
  p_enqueued_by TEXT DEFAULT 'trigger:t_product_variants.sync'
) RETURNS VOID
LANGUAGE plpgsql
AS $$
DECLARE
  v_existing_task_id BIGINT;
BEGIN
  IF p_changes IS NULL OR p_changes = '{}'::jsonb THEN
    RETURN;
  END IF;

  -- Try to merge into an existing pending task for this variant
  UPDATE public.t_task_queue
  SET
    payload = jsonb_set(
      payload,
      '{changes}',
      COALESCE(payload->'changes', '{}'::jsonb) || p_changes,
      true
    ),
    scheduled_at = NOW() + p_debounce
  WHERE task_type = 'sync_product_variant_to_shopify'
    AND status = 'pending'
    AND (payload->>'id')::BIGINT = p_variant_id
  RETURNING id INTO v_existing_task_id;

  -- If no existing pending task, insert a new one
  IF v_existing_task_id IS NULL THEN
    INSERT INTO public.t_task_queue(
      task_type, payload, status, scheduled_at, created_at, enqueued_by
    ) VALUES (
      'sync_product_variant_to_shopify',
      jsonb_build_object('id', p_variant_id, 'changes', p_changes),
      'pending',
      NOW() + p_debounce,
      NOW(),
      p_enqueued_by
    );
  END IF;
END;
$$;

-- Trigger function: collect changed fields and enqueue a coalesced sync
CREATE OR REPLACE FUNCTION public.enqueue_sync_pv_to_shopify_on_update()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  changes JSONB := '{}'::jsonb;
  v_uploaded BOOLEAN := (NEW.uploaded_to_shopify_at IS NOT NULL);
BEGIN
  -- Build "changes" jsonb only for fields that changed

  -- Build "changes" jsonb only for fields that changed
  IF OLD.price IS DISTINCT FROM NEW.price THEN
    changes := changes || jsonb_build_object('price', NEW.price);
  END IF;

  -- MSRP: include key even when NULL (worker will clear compare_at_price on NULL)
  IF OLD.msrp IS DISTINCT FROM NEW.msrp THEN
    changes := changes || jsonb_build_object('msrp', to_jsonb(NEW.msrp));
  END IF;

  IF OLD."UPC" IS DISTINCT FROM NEW."UPC" THEN
    changes := changes || jsonb_build_object('upc', NEW."UPC");
  END IF;

  IF OLD.shopify_weight_lbs IS DISTINCT FROM NEW.shopify_weight_lbs THEN
    changes := changes || jsonb_build_object('shopify_weight_lbs', NEW.shopify_weight_lbs);
  END IF;

  -- Option names/values
  IF OLD.op1_name IS DISTINCT FROM NEW.op1_name THEN
    changes := changes || jsonb_build_object('op1_name', NEW.op1_name);
  END IF;
  IF OLD.op1_value IS DISTINCT FROM NEW.op1_value THEN
    changes := changes || jsonb_build_object('op1_value', NEW.op1_value);
  END IF;
  IF OLD.op2_name IS DISTINCT FROM NEW.op2_name THEN
    changes := changes || jsonb_build_object('op2_name', NEW.op2_name);
  END IF;
  IF OLD.op2_value IS DISTINCT FROM NEW.op2_value THEN
    changes := changes || jsonb_build_object('op2_value', NEW.op2_value);
  END IF;
  IF OLD.op3_name IS DISTINCT FROM NEW.op3_name THEN
    changes := changes || jsonb_build_object('op3_name', NEW.op3_name);
  END IF;
  IF OLD.op3_value IS DISTINCT FROM NEW.op3_value THEN
    changes := changes || jsonb_build_object('op3_value', NEW.op3_value);
  END IF;

  -- Color and player impact product tags
  IF OLD.color_id IS DISTINCT FROM NEW.color_id THEN
    changes := changes || jsonb_build_object('color_id', NEW.color_id);
  END IF;
  IF OLD.player_id IS DISTINCT FROM NEW.player_id THEN
    changes := changes || jsonb_build_object('player_id', NEW.player_id);
  END IF;

  -- If not uploaded, restrict to option NAME changes only
  IF NOT v_uploaded THEN
    changes := changes - 'price' - 'msrp' - 'upc' - 'shopify_weight_lbs' - 'op1_value' - 'op2_value' - 'op3_value' - 'color_id' - 'player_id';
  END IF;

  -- Enqueue only if something remains
  IF changes <> '{}'::jsonb THEN
    PERFORM public.enqueue_sync_pv_to_shopify(NEW.id, changes, INTERVAL '1 minute', 'trigger:t_product_variants.sync');
  END IF;

  RETURN NEW;
END;
$$;

-- Create trigger watching all relevant fields
DROP TRIGGER IF EXISTS trg_t_product_variants_sync_shopify_on_update ON public.t_product_variants;
CREATE TRIGGER trg_t_product_variants_sync_shopify_on_update
AFTER UPDATE OF
  price,
  msrp,
  "UPC",
  color_id,
  player_id,
  shopify_weight_lbs,
  op1_name, op1_value,
  op2_name, op2_value,
  op3_name, op3_value
ON public.t_product_variants
FOR EACH ROW
EXECUTE FUNCTION public.enqueue_sync_pv_to_shopify_on_update();

