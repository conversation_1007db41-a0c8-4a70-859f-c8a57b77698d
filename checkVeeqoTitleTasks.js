import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function checkTasks() {
  console.log('🔍 Checking for update_veeqo_d_title tasks...');
  
  const { data, error } = await supabase
    .from('t_task_queue')
    .select('id, task_type, payload, status, scheduled_at, created_at, enqueued_by')
    .eq('task_type', 'update_veeqo_d_title')
    .order('created_at', { ascending: false })
    .limit(10);
  
  if (error) {
    console.error('❌ Error:', error);
    return;
  }
  
  console.log(`✅ Found ${data.length} update_veeqo_d_title tasks`);
  
  data.forEach((task, index) => {
    console.log(`${index + 1}. Task ${task.id}: Disc ${task.payload.id} - "${task.payload.g_pull}" (${task.status})`);
    console.log(`   Scheduled: ${task.scheduled_at}`);
    console.log(`   Enqueued by: ${task.enqueued_by}`);
    console.log('');
  });
}

checkTasks();
