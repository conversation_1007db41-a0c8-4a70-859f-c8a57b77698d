require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function enqueueRemainingSoldDiscs() {
  try {
    console.log('Getting ALL remaining sold discs with null vendor_osl_id (excluding already enqueued ones)...');
    
    // First, check what's already been enqueued
    const { data: alreadyEnqueued, error: enqueuedError } = await supabase
      .from('t_task_queue')
      .select('payload')
      .eq('enqueued_by', 'sold_discs_vendor_osl_check');
    
    if (enqueuedError) {
      console.error('Error checking already enqueued tasks:', enqueuedError);
      return;
    }
    
    const enqueuedDiscIds = alreadyEnqueued.map(task => parseInt(task.payload.id));
    console.log(`Found ${enqueuedDiscIds.length} already enqueued sold disc tasks`);
    
    // Get ALL sold discs with null vendor_osl_id (no limit this time!)
    const { data: allSoldDiscs, error: fetchError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id, sold_date')
      .is('vendor_osl_id', null)
      .not('sold_date', 'is', null)  // Sold discs only
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null)
      .order('id');
    
    if (fetchError) {
      console.error('Error getting sold discs:', fetchError);
      return;
    }
    
    console.log(`Found ${allSoldDiscs.length} total sold discs with null vendor_osl_id`);
    
    // Filter out already enqueued discs
    const remainingSoldDiscs = allSoldDiscs.filter(disc => !enqueuedDiscIds.includes(disc.id));
    
    console.log(`Remaining to enqueue: ${remainingSoldDiscs.length} sold discs`);
    
    if (remainingSoldDiscs.length === 0) {
      console.log('✅ All sold discs with null vendor_osl_id have already been enqueued!');
      return;
    }
    
    console.log('\nFirst 10 remaining sold discs as sample:');
    remainingSoldDiscs.slice(0, 10).forEach((disc, index) => {
      console.log(`${index + 1}. Disc ${disc.id}: MPS ${disc.mps_id}, Weight ${disc.weight}g, Weight MFG ${disc.weight_mfg}g, Color ${disc.color_id}, Sold: ${disc.sold_date}`);
    });
    
    if (remainingSoldDiscs.length > 10) {
      console.log(`... and ${remainingSoldDiscs.length - 10} more remaining sold discs`);
    }
    
    // Enqueue match_disc_to_osl tasks for remaining sold discs
    console.log('\nEnqueueing match_disc_to_osl tasks for all remaining sold discs...');
    
    const tasksToEnqueue = remainingSoldDiscs.map(disc => ({
      task_type: 'match_disc_to_osl',
      payload: {
        id: disc.id,
        operation: 'UPDATE',
        old_data: disc
      },
      status: 'pending',
      scheduled_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      enqueued_by: 'sold_discs_vendor_osl_check_remaining'
    }));
    
    // Insert tasks in batches to avoid potential issues with large inserts
    const batchSize = 100;
    let totalEnqueued = 0;
    let allEnqueuedTasks = [];
    
    for (let i = 0; i < tasksToEnqueue.length; i += batchSize) {
      const batch = tasksToEnqueue.slice(i, i + batchSize);
      
      const { data: enqueuedBatch, error: enqueueError } = await supabase
        .from('t_task_queue')
        .insert(batch)
        .select('id, task_type, payload');
      
      if (enqueueError) {
        console.error(`Error enqueueing batch ${Math.floor(i / batchSize) + 1}:`, enqueueError);
        continue;
      }
      
      totalEnqueued += enqueuedBatch.length;
      allEnqueuedTasks = allEnqueuedTasks.concat(enqueuedBatch);
      
      console.log(`Batch ${Math.floor(i / batchSize) + 1}: Enqueued ${enqueuedBatch.length} tasks (Total: ${totalEnqueued}/${remainingSoldDiscs.length})`);
    }
    
    console.log(`\n✅ Successfully enqueued ${totalEnqueued} additional match_disc_to_osl tasks for remaining sold discs!`);
    
    console.log('\nFirst 5 new task details:');
    allEnqueuedTasks.slice(0, 5).forEach((task, index) => {
      const discId = task.payload.id;
      console.log(`${index + 1}. Task ${task.id}: match_disc_to_osl for disc ${discId}`);
    });
    
    if (allEnqueuedTasks.length > 5) {
      console.log(`... and ${allEnqueuedTasks.length - 5} more new tasks`);
    }
    
    // Show total summary
    const totalSoldTasksEnqueued = enqueuedDiscIds.length + totalEnqueued;
    console.log(`\n📊 TOTAL SOLD DISCS TASK SUMMARY:`);
    console.log(`Previously enqueued: ${enqueuedDiscIds.length} tasks`);
    console.log(`Just enqueued: ${totalEnqueued} tasks`);
    console.log(`Total sold disc tasks enqueued: ${totalSoldTasksEnqueued}`);
    console.log(`Total sold discs with null vendor_osl_id: ${allSoldDiscs.length}`);
    
    if (totalSoldTasksEnqueued >= allSoldDiscs.length) {
      console.log('✅ ALL sold discs with null vendor_osl_id have now been enqueued!');
    } else {
      console.log(`⚠️ Still missing: ${allSoldDiscs.length - totalSoldTasksEnqueued} sold discs`);
    }
    
    console.log('\n📋 To check status for ALL sold disc tasks, run:');
    console.log(`
SELECT 
  enqueued_by,
  COUNT(*) as total_tasks,
  status,
  COUNT(CASE WHEN result->>'vendor_osl_id' IS NOT NULL THEN 1 END) as found_vendor_mappings
FROM t_task_queue 
WHERE enqueued_by IN ('sold_discs_vendor_osl_check', 'sold_discs_vendor_osl_check_remaining')
GROUP BY enqueued_by, status
ORDER BY enqueued_by, status;
    `);
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

enqueueRemainingSoldDiscs();
