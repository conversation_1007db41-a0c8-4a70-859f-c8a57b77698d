// importScheduler.js - <PERSON>ript to schedule RPRO import without continuous restarts

import fs from 'fs';
import dotenv from 'dotenv';
import { exec } from 'child_process';

// Create a log file
const logFile = 'import_scheduler.log';
fs.writeFileSync(logFile, `Starting import scheduler at ${new Date().toISOString()}\n`);

// Load environment variables
dotenv.config();
fs.appendFileSync(logFile, `Environment variables loaded\n`);

// Get parameters
const dbfFilePath = process.env.DBF_FILE_PATH || 'R:\\Rpro\\BRIDGE\\invdb.dbf';
const targetTable = process.env.TARGET_TABLE || 'imported_table_rpro';
const truncateBeforeImport = process.env.TRUNCATE_BEFORE_IMPORT === 'true' || true;

fs.appendFileSync(logFile, `Parameters:\n`);
fs.appendFileSync(logFile, `- DBF file path: ${dbfFilePath}\n`);
fs.appendFileSync(logFile, `- Target table: ${targetTable}\n`);
fs.appendFileSync(logFile, `- Truncate before import: ${truncateBeforeImport}\n`);

// Function to check if the DBF file exists and is accessible
function checkDbfFile() {
  try {
    fs.appendFileSync(logFile, `Checking if DBF file exists: ${dbfFilePath}\n`);
    
    if (!fs.existsSync(dbfFilePath)) {
      fs.appendFileSync(logFile, `DBF file not found: ${dbfFilePath}\n`);
      console.error(`DBF file not found: ${dbfFilePath}`);
      return false;
    }
    
    // Check if the file is readable
    fs.accessSync(dbfFilePath, fs.constants.R_OK);
    
    fs.appendFileSync(logFile, `DBF file exists and is readable\n`);
    console.log(`DBF file exists and is readable`);
    return true;
  } catch (error) {
    fs.appendFileSync(logFile, `Error checking DBF file: ${error.message}\n`);
    console.error(`Error checking DBF file: ${error.message}`);
    return false;
  }
}

// Function to run the import
function runImport() {
  return new Promise((resolve, reject) => {
    fs.appendFileSync(logFile, `Running import...\n`);
    console.log('Running import...');
    
    // Run the import script as a separate process
    const command = `node batchImport.js "${dbfFilePath}" "${targetTable}" ${truncateBeforeImport}`;
    fs.appendFileSync(logFile, `Command: ${command}\n`);
    
    exec(command, (error, stdout, stderr) => {
      if (error) {
        fs.appendFileSync(logFile, `Error running import: ${error.message}\n`);
        fs.appendFileSync(logFile, `stderr: ${stderr}\n`);
        console.error(`Error running import: ${error.message}`);
        reject(error);
        return;
      }
      
      fs.appendFileSync(logFile, `Import completed successfully\n`);
      fs.appendFileSync(logFile, `stdout: ${stdout}\n`);
      console.log('Import completed successfully');
      resolve();
    });
  });
}

// Main function
async function main() {
  try {
    // Check if the DBF file exists and is accessible
    const dbfFileExists = checkDbfFile();
    
    if (dbfFileExists) {
      // Run the import
      await runImport();
    } else {
      fs.appendFileSync(logFile, `Import skipped due to missing or inaccessible DBF file\n`);
      console.log('Import skipped due to missing or inaccessible DBF file');
    }
    
    fs.appendFileSync(logFile, `Scheduler completed at ${new Date().toISOString()}\n`);
    console.log('Scheduler completed');
    
    // Exit the process
    process.exit(0);
  } catch (error) {
    fs.appendFileSync(logFile, `Unhandled error: ${error.message}\n`);
    fs.appendFileSync(logFile, `${error.stack}\n`);
    console.error(`Unhandled error: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the main function
console.log('Starting import scheduler...');
main();
