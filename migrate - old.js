/**
 * Migrate multiple CSV files into multiple tables with a single script,
 * chunking large upserts and returning minimal columns to avoid timeouts.
 */

import { createClient } from '@supabase/supabase-js';
import Papa from 'papaparse';
import fs from 'node:fs/promises'; // for reading local CSV files in Node
import path from 'node:path';

const supabaseUrl = 'https://aepabhlwpjfjulrjeitn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFlcGFiaGx3cGpmanVscmplaXRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczMzc4NjU0MSwiZXhwIjoyMDQ5MzYyNTQxfQ.FQyPTgdBP83VhuykdlZkagHADc5nBmx7-yd0JYt5aP8'; // 🔹 Replace with your actual key
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Break an array into smaller arrays ("chunks") of a given size.
 */
function chunkArray(array, chunkSize) {
  const chunks = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

/**
 * Upsert rows in batches to avoid timeouts. Returns total rows upserted.
 */
async function upsertInChunks(tableName, rows, onConflict, chunkSize = 5000) {
  const chunks = chunkArray(rows, chunkSize);
  let totalUpserted = 0;

  console.log(`\n➡ Starting chunked upsert for [${tableName}]:`);
  console.log(`   Total rows: ${rows.length}, chunk size: ${chunkSize}, total chunks: ${chunks.length}`);

  for (let i = 0; i < chunks.length; i++) {
    const chunk = chunks[i];
    console.log(`➡ Upserting chunk ${i + 1} of ${chunks.length} (rows: ${chunk.length})...`);

    const { data, error } = await supabase
      .from(tableName)
      .upsert(chunk, { onConflict })
      .select('id'); // Return minimal columns to reduce overhead

    if (error) {
      console.error(`❌ Error in chunk ${i + 1} for table [${tableName}]:`, error);
      throw error; // Stop script or handle it as needed
    } else {
      totalUpserted += data.length;
      console.log(`✅ Chunk ${i + 1} done. (Upserted rows this chunk: ${data.length}, total so far: ${totalUpserted})`);
    }
  }

  return totalUpserted;
}

/**
 * Configuration for your CSV-file → table migrations
 */
const migrations = [
  { csvFile: 'supabase05_t_players.csv', storagePath: 'supabase05_t_players.csv', tableName: 't_players', onConflict: 'id' },
  { csvFile: 'supabase06_t_molds.csv', storagePath: 'supabase06_t_molds.csv', tableName: 't_molds', onConflict: 'id' },
  { csvFile: 'supabase07_t_plastics.csv', storagePath: 'supabase07_t_plastics.csv', tableName: 't_plastics', onConflict: 'id' },
  { csvFile: 'supabase08_t_stamps.csv', storagePath: 'supabase08_t_stamps.csv', tableName: 't_stamps', onConflict: 'id' },
  { csvFile: 'supabase09_t_mps.csv', storagePath: 'supabase09_t_mps.csv', tableName: 't_mps', onConflict: 'id' },
  { csvFile: 'supabase10_t_sdasins.csv', storagePath: 'supabase10_t_sdasins.csv', tableName: 't_sdasins', onConflict: 'id' },
  { csvFile: 'supabase11_t_order_sheet_lines.csv', storagePath: 'supabase11_t_order_sheet_lines.csv', tableName: 't_order_sheet_lines', onConflict: 'id' },
  { csvFile: 'supabase12_t_invoices.csv', storagePath: 'supabase12_t_invoices.csv', tableName: 't_invoices', onConflict: 'id' },
  { csvFile: 'supabase13_t_shipments.csv', storagePath: 'supabase13_t_shipments.csv', tableName: 't_shipments', onConflict: 'id' },
  { csvFile: 'supabase14_t_discs.csv', storagePath: 'supabase14_t_discs.csv', tableName: 't_discs', onConflict: 'id' },
];

async function main() {
  try {
    console.log('🔧 Disabling triggers on t_sdasins and t_discs...');
    await supabase.rpc('disable_user_triggers');
    console.log('✅ Triggers disabled.');
    
    console.log('🚀 Starting migration of multiple CSV files...\n');

    for (const m of migrations) {
      const localCsvPath = path.join('data', m.csvFile);
      console.log(`📁 Processing [${m.csvFile}] => table [${m.tableName}]`);

      // Optional: Remove existing file in storage
      await supabase
        .storage
        .from('uploads')
        .remove([m.storagePath])
        .catch(() => { /* ignore error */ });

      // 1️⃣ Read the local CSV file
      const fileContents = await fs.readFile(localCsvPath, 'utf-8');

      // 2️⃣ Upload the CSV to Supabase Storage ('uploads' bucket)
      console.log(`⏫ Uploading [${m.csvFile}] to storage [${m.storagePath}]...`);
      const { error: uploadError } = await supabase
        .storage
        .from('uploads')
        .upload(m.storagePath, fileContents, {
          contentType: 'text/csv',
          upsert: true, // Overwrite if exists
        });

      if (uploadError) {
        console.error(`❌ Error uploading [${m.csvFile}] to storage`, uploadError);
        continue; // Skip to the next file
      }
      console.log(`✅ Uploaded [${m.csvFile}] successfully.`);

      // 3️⃣ Parse CSV data using PapaParse
      console.log(`📄 Parsing [${m.csvFile}] locally...`);
      const { data: parsedData, errors: parseErrors } = Papa.parse(fileContents, {
        header: true,
        skipEmptyLines: true,
        dynamicTyping: true,
        delimiter: ',',
      });

      if (parseErrors.length > 0) {
        console.warn(`⚠️ Warnings parsing [${m.csvFile}]:`, parseErrors.slice(0, 5));
      }
      console.log(`✅ Parsed ${parsedData.length} rows from [${m.csvFile}].`);

      // 4️⃣ Upsert in chunks
      console.log(`📥 Upserting data into [${m.tableName}] (onConflict: ${m.onConflict}) in 10k batches...`);

      try {
        const totalUpserted = await upsertInChunks(m.tableName, parsedData, m.onConflict, 5000);
        console.log(`✅ Successfully upserted [${m.csvFile}] into [${m.tableName}]. Total upserted: ${totalUpserted}`);
      } catch (upsertError) {
        console.error(`❌ Error upserting [${m.csvFile}] into [${m.tableName}]`, upsertError);
      }
    }

    console.log('\n🏁 All migrations complete!');
    // 🔧 Re-enable triggers on t_sdasins and t_discs after migration
    console.log('🔧 Re-enabling triggers on t_sdasins and t_discs...');
    await supabase.rpc('enable_user_triggers');
    console.log('✅ Triggers re-enabled.');
  } catch (err) {
    console.error('💥 Unexpected error in migration script:', err);
  }
}

main();
