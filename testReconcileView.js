// testReconcileView.js - Test and analyze the reconciliation view

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function analyzeReconcileView() {
  try {
    console.log('🔍 Analyzing v_reconcile_inv_sdasin_to_amaz view...\n');

    // Get total count
    const { count, error: countError } = await supabase
      .from('v_reconcile_inv_sdasin_to_amaz')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('Error getting count:', countError.message);
      return;
    }

    console.log(`📊 Total records needing attention: ${count}\n`);

    // Get breakdown by status
    const { data: statusData, error: statusError } = await supabase
      .from('v_reconcile_inv_sdasin_to_amaz')
      .select('status');

    if (statusError) {
      console.error('Error getting status breakdown:', statusError.message);
      return;
    }

    const statusCounts = statusData.reduce((acc, row) => {
      acc[row.status] = (acc[row.status] || 0) + 1;
      return acc;
    }, {});

    console.log('📈 Breakdown by status:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`  ${status}: ${count} records`);
    });

    console.log('\n');

    // Get some examples of each status
    for (const status of Object.keys(statusCounts)) {
      console.log(`🔍 Sample ${status} records:`);
      
      const { data: sampleData, error: sampleError } = await supabase
        .from('v_reconcile_inv_sdasin_to_amaz')
        .select('sku, local_qty, amazon_qty, quantity_difference, amazon_item_name')
        .eq('status', status)
        .limit(5);

      if (sampleError) {
        console.error(`Error getting ${status} samples:`, sampleError.message);
      } else if (sampleData && sampleData.length > 0) {
        console.table(sampleData);
      }
      console.log('');
    }

    // Get priority breakdown
    const { data: priorityData, error: priorityError } = await supabase
      .from('v_reconcile_inv_sdasin_to_amaz')
      .select('priority');

    if (priorityError) {
      console.error('Error getting priority breakdown:', priorityError.message);
    } else {
      const priorityCounts = priorityData.reduce((acc, row) => {
        const priorityLabel = row.priority === 1 ? 'High (Amazon Only)' : 
                             row.priority === 2 ? 'Medium (Quantity Mismatch)' : 
                             'Low (Match)';
        acc[priorityLabel] = (acc[priorityLabel] || 0) + 1;
        return acc;
      }, {});

      console.log('🚨 Priority breakdown:');
      Object.entries(priorityCounts).forEach(([priority, count]) => {
        console.log(`  ${priority}: ${count} records`);
      });
    }

    console.log('\n✅ Analysis complete!');
    console.log('\n💡 Next steps:');
    console.log('1. Review "Amazon Only" records - these are SKUs in Amazon but not in your local inventory');
    console.log('2. Check quantity mismatches to identify sync issues');
    console.log('3. Use this data to decide on inventory reconciliation actions');

  } catch (error) {
    console.error('Exception during analysis:', error.message);
  }
}

analyzeReconcileView().catch(console.error);
