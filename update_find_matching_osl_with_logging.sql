-- First drop the existing function
DROP FUNCTION IF EXISTS find_matching_osl(integer, integer, numeric);

-- Then create the new function with logging
CREATE OR REPLACE FUNCTION find_matching_osl(
    mps_id_param INTEGER,
    color_id_param INTEGER,
    weight_param NUMERIC
)
RETURNS TABLE (
    osl_id INTEGER,
    debug_info TEXT
) AS $$
DECLARE
    rounded_weight NUMERIC;
    debug_text TEXT;
BEGIN
    -- Ensure weight_param is treated as NUMERIC
    weight_param := weight_param::NUMERIC;
    
    -- Round the weight to the nearest integer using standard rounding rules
    -- (0.5 and up rounds up, below 0.5 rounds down)
    rounded_weight := ROUND(weight_param);
    
    -- Create debug info
    debug_text := 'Input weight: ' || weight_param || ', Rounded weight: ' || rounded_weight;
    
    RETURN QUERY
    SELECT 
        osl.id AS osl_id,
        debug_text || ', OSL min_weight: ' || osl.min_weight || ', OSL max_weight: ' || osl.max_weight AS debug_info
    FROM t_order_sheet_lines osl
    WHERE osl.mps_id = mps_id_param
      AND (osl.color_id = color_id_param OR osl.color_id = 23)
      AND rounded_weight >= osl.min_weight
      AND rounded_weight <= osl.max_weight
    LIMIT 1;
    
    -- If no rows returned, return a debug row
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT 
            NULL::INTEGER AS osl_id,
            debug_text || ', No matching OSL found for mps_id=' || mps_id_param || ', color_id=' || color_id_param AS debug_info;
    END IF;
END;
$$ LANGUAGE plpgsql;
