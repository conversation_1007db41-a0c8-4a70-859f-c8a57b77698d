require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function createRedirectFunctions() {
  try {
    console.log('Creating vendor OSL functions with redirect logic...');
    
    // Create the manufacturer weight function with redirect
    const mfgWeightFunction = `
CREATE OR REPLACE FUNCTION find_matching_osl_by_mfg_weight_with_redirect(
    mps_id_param INTEGER,
    color_id_param INTEGER,
    weight_mfg_param NUMERIC
)
RETURNS TABLE (
    osl_id INTEGER,
    redirect_info TEXT
) AS $$
DECLARE
    rounded_weight NUMERIC;
    initial_osl_id INTEGER;
    initial_mps_id INTEGER;
    redirect_mps_id INTEGER;
    final_osl_id INTEGER;
    redirect_text TEXT := '';
BEGIN
    -- Round the weight to the nearest integer using standard rounding rules
    rounded_weight := ROUND(weight_mfg_param);
    
    -- First, try to find a matching OSL with the original MPS
    SELECT id INTO initial_osl_id
    FROM t_order_sheet_lines
    WHERE mps_id = mps_id_param
      AND (color_id = color_id_param OR color_id = 23)
      AND rounded_weight >= min_weight
      AND rounded_weight <= max_weight
    LIMIT 1;
    
    -- If we found an OSL, check if its MPS has order_through_mps_id set
    IF initial_osl_id IS NOT NULL THEN
        -- Get the MPS details for the found OSL
        SELECT mps_id INTO initial_mps_id FROM t_order_sheet_lines WHERE id = initial_osl_id;
        
        -- Check if this MPS has order_through_mps_id set
        SELECT order_through_mps_id INTO redirect_mps_id 
        FROM t_mps 
        WHERE id = initial_mps_id AND order_through_mps_id IS NOT NULL;
        
        -- If redirect is needed, find OSL in the redirect MPS
        IF redirect_mps_id IS NOT NULL THEN
            redirect_text := 'Redirected from MPS ' || initial_mps_id || ' to MPS ' || redirect_mps_id;
            
            -- Find matching OSL in the redirect MPS
            SELECT id INTO final_osl_id
            FROM t_order_sheet_lines
            WHERE mps_id = redirect_mps_id
              AND (color_id = color_id_param OR color_id = 23)
              AND rounded_weight >= min_weight
              AND rounded_weight <= max_weight
            LIMIT 1;
            
            -- Return the redirect OSL if found
            IF final_osl_id IS NOT NULL THEN
                RETURN QUERY SELECT final_osl_id, redirect_text;
                RETURN;
            ELSE
                -- No matching OSL in redirect MPS
                redirect_text := redirect_text || ', but no matching OSL found in redirect MPS';
                RETURN QUERY SELECT NULL::INTEGER, redirect_text;
                RETURN;
            END IF;
        ELSE
            -- No redirect needed, return the original OSL
            RETURN QUERY SELECT initial_osl_id, 'No redirect needed'::TEXT;
            RETURN;
        END IF;
    ELSE
        -- No initial OSL found
        RETURN QUERY SELECT NULL::INTEGER, 'No matching OSL found for original MPS'::TEXT;
        RETURN;
    END IF;
END;
$$ LANGUAGE plpgsql;
    `;
    
    const { error: mfgError } = await supabase.rpc('exec_sql', { sql: mfgWeightFunction });
    
    if (mfgError) {
      console.error('Error creating manufacturer weight function:', mfgError);
      return;
    }
    
    console.log('✅ Created find_matching_osl_by_mfg_weight_with_redirect function');
    
    // Create the regular weight function with redirect
    const regularWeightFunction = `
CREATE OR REPLACE FUNCTION find_matching_osl_with_redirect(
    mps_id_param INTEGER,
    color_id_param INTEGER,
    weight_param NUMERIC
)
RETURNS TABLE (
    osl_id INTEGER,
    redirect_info TEXT
) AS $$
DECLARE
    rounded_weight NUMERIC;
    initial_osl_id INTEGER;
    initial_mps_id INTEGER;
    redirect_mps_id INTEGER;
    final_osl_id INTEGER;
    redirect_text TEXT := '';
    decimal_part NUMERIC;
BEGIN
    -- Custom rounding logic: X.5 and up rounds to X+1, X.4 and down rounds to X
    decimal_part := weight_param - FLOOR(weight_param);
    
    IF decimal_part >= 0.5 THEN
        rounded_weight := CEIL(weight_param);
    ELSE
        rounded_weight := FLOOR(weight_param);
    END IF;
    
    -- First, try to find a matching OSL with the original MPS
    SELECT id INTO initial_osl_id
    FROM t_order_sheet_lines
    WHERE mps_id = mps_id_param
      AND (color_id = color_id_param OR color_id = 23)
      AND rounded_weight >= min_weight
      AND rounded_weight <= max_weight
    LIMIT 1;
    
    -- If we found an OSL, check if its MPS has order_through_mps_id set
    IF initial_osl_id IS NOT NULL THEN
        -- Get the MPS details for the found OSL
        SELECT mps_id INTO initial_mps_id FROM t_order_sheet_lines WHERE id = initial_osl_id;
        
        -- Check if this MPS has order_through_mps_id set
        SELECT order_through_mps_id INTO redirect_mps_id 
        FROM t_mps 
        WHERE id = initial_mps_id AND order_through_mps_id IS NOT NULL;
        
        -- If redirect is needed, find OSL in the redirect MPS
        IF redirect_mps_id IS NOT NULL THEN
            redirect_text := 'Redirected from MPS ' || initial_mps_id || ' to MPS ' || redirect_mps_id;
            
            -- Find matching OSL in the redirect MPS
            SELECT id INTO final_osl_id
            FROM t_order_sheet_lines
            WHERE mps_id = redirect_mps_id
              AND (color_id = color_id_param OR color_id = 23)
              AND rounded_weight >= min_weight
              AND rounded_weight <= max_weight
            LIMIT 1;
            
            -- Return the redirect OSL if found
            IF final_osl_id IS NOT NULL THEN
                RETURN QUERY SELECT final_osl_id, redirect_text;
                RETURN;
            ELSE
                -- No matching OSL in redirect MPS
                redirect_text := redirect_text || ', but no matching OSL found in redirect MPS';
                RETURN QUERY SELECT NULL::INTEGER, redirect_text;
                RETURN;
            END IF;
        ELSE
            -- No redirect needed, return the original OSL
            RETURN QUERY SELECT initial_osl_id, 'No redirect needed'::TEXT;
            RETURN;
        END IF;
    ELSE
        -- No initial OSL found
        RETURN QUERY SELECT NULL::INTEGER, 'No matching OSL found for original MPS'::TEXT;
        RETURN;
    END IF;
END;
$$ LANGUAGE plpgsql;
    `;
    
    const { error: regularError } = await supabase.rpc('exec_sql', { sql: regularWeightFunction });
    
    if (regularError) {
      console.error('Error creating regular weight function:', regularError);
      return;
    }
    
    console.log('✅ Created find_matching_osl_with_redirect function');
    
    console.log('\n🎯 Both redirect functions created successfully!');
    console.log('These functions will:');
    console.log('1. Find initial OSL match');
    console.log('2. Check if the OSL\'s MPS has order_through_mps_id set');
    console.log('3. If yes, redirect to the replacement MPS and find appropriate OSL there');
    console.log('4. Return the final OSL ID and redirect information');
    
  } catch (err) {
    console.error('Fatal error:', err.message);
  }
}

createRedirectFunctions();
