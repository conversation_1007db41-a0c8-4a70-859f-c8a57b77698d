import XLSX from 'xlsx';

const originalFile = 'data/external data/discraftstock.xlsx';
const exportedFile = 'data/external data/discraftstock_2025-06-11.xlsx';

try {
  console.log('Checking formatting preservation...\n');
  
  // Read original file
  console.log('Reading original file...');
  const originalWorkbook = XLSX.readFile(originalFile);
  const originalSheet = originalWorkbook.Sheets[originalWorkbook.SheetNames[0]];
  
  // Read exported file
  console.log('Reading exported file...');
  const exportedWorkbook = XLSX.readFile(exportedFile);
  const exportedSheet = exportedWorkbook.Sheets[exportedWorkbook.SheetNames[0]];
  
  // Check a few specific cells that should have been updated
  const testCells = ['L28', 'Q132', 'P165', 'N172', 'P178'];
  
  console.log('Comparing cell properties...\n');
  
  testCells.forEach(cellAddress => {
    console.log(`--- Cell ${cellAddress} ---`);
    
    const originalCell = originalSheet[cellAddress];
    const exportedCell = exportedSheet[cellAddress];
    
    if (originalCell) {
      console.log('Original cell properties:', Object.keys(originalCell));
      console.log('Original cell:', originalCell);
    } else {
      console.log('Original cell: undefined');
    }
    
    if (exportedCell) {
      console.log('Exported cell properties:', Object.keys(exportedCell));
      console.log('Exported cell:', exportedCell);
    } else {
      console.log('Exported cell: undefined');
    }
    
    console.log('');
  });
  
  // Check if the workbook has any style information
  console.log('--- Workbook Properties ---');
  console.log('Original workbook props:', Object.keys(originalWorkbook));
  console.log('Exported workbook props:', Object.keys(exportedWorkbook));
  
  if (originalWorkbook.Styles) {
    console.log('Original has Styles');
  }
  if (exportedWorkbook.Styles) {
    console.log('Exported has Styles');
  }
  
} catch (err) {
  console.error('Error:', err.message);
}
