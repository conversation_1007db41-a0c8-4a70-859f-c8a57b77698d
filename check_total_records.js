import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkTotalRecords() {
    console.log('Checking total record counts...\n');
    
    try {
        // Get total count of all records
        const { count: totalCount, error: totalError } = await supabase
            .from('it_dd_osl')
            .select('*', { count: 'exact', head: true });
        
        if (totalError) {
            console.error('Error getting total count:', totalError);
            return;
        }
        
        console.log(`Total records in it_dd_osl: ${totalCount}`);
        
        // Get count of disc records
        const { count: discCount, error: discError } = await supabase
            .from('it_dd_osl')
            .select('*', { count: 'exact', head: true })
            .eq('product_product_type', 'Discs');
        
        if (discError) {
            console.error('Error getting disc count:', discError);
            return;
        }
        
        console.log(`Disc records in it_dd_osl: ${discCount}`);
        
        // Get count by vendor
        const { data: vendors, error: vendorsError } = await supabase
            .from('it_dd_osl')
            .select('product_vendor')
            .eq('product_product_type', 'Discs');
        
        if (vendorsError) {
            console.error('Error getting vendor data:', vendorsError);
            return;
        }
        
        const vendorCounts = {};
        vendors.forEach(row => {
            vendorCounts[row.product_vendor] = (vendorCounts[row.product_vendor] || 0) + 1;
        });
        
        console.log('\nDisc records by vendor:');
        console.log('=======================');
        Object.entries(vendorCounts)
            .sort((a, b) => b[1] - a[1])
            .forEach(([vendor, count]) => {
                console.log(`${vendor}: ${count} records`);
            });
        
        // Check how many have been parsed
        const { count: parsedCount, error: parsedError } = await supabase
            .from('it_dd_osl')
            .select('*', { count: 'exact', head: true })
            .eq('product_product_type', 'Discs')
            .not('parsed_mold', 'is', null);
        
        if (parsedError) {
            console.error('Error getting parsed count:', parsedError);
            return;
        }
        
        console.log(`\nRecords with parsed_mold: ${parsedCount}/${discCount} (${((parsedCount/discCount)*100).toFixed(1)}%)`);
        
    } catch (error) {
        console.error('Query failed:', error);
    }
}

// Run the check
checkTotalRecords();
