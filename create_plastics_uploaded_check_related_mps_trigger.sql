-- Function to enqueue a task when t_plastics.shopify_collection_uploaded_at changes from null to not null
CREATE OR REPLACE FUNCTION fn_enqueue_plastics_uploaded_check_related_mps_task()
RETURNS TRIGGER AS $$
BEGIN
    -- Only enqueue a task if shopify_collection_uploaded_at has changed from null to not null
    IF OLD.shopify_collection_uploaded_at IS NULL AND NEW.shopify_collection_uploaded_at IS NOT NULL THEN
        -- Insert a task into the task queue
        INSERT INTO t_task_queue (
            task_type,
            payload,
            status,
            scheduled_at,
            created_at
        ) VALUES (
            'plastics_uploaded_check_related_mps_for_ready',
            jsonb_build_object('id', NEW.id),
            'pending',
            NOW(),
            NOW()
        );
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE OR REPLACE TRIGGER trg_plastics_uploaded_check_related_mps
AFTER UPDATE OF shopify_collection_uploaded_at ON t_plastics
FOR EACH ROW
EXECUTE FUNCTION fn_enqueue_plastics_uploaded_check_related_mps_task();
