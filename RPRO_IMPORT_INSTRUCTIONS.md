# RPRO DBF Import Instructions

This guide explains how to import your RPRO DBF file (invdb.dbf) into Supabase with the table name `imported_table_rpro`.

## Quick Start

1. **Create the Table**: Run `node createRproTable.js` to create the table in Supabase.
2. **Import the Data**: Run `node batchImport.js` to import the data in batches of 1000 records.
3. **Set Up Daily Import**: Run `node setupDailyRproImport.js` to set up a daily scheduled task.

## Detailed Instructions

### Step 1: Create the Table

```
node createRproTable.js
```

This script will:
- Check if the `imported_table_rpro` table exists in Supabase
- Create the table if it doesn't exist
- Create the necessary functions for truncating the table

### Step 2: Import the Data

```
node batchImport.js
```

This script will:
- Open the DBF file at `R:\Rpro\BRIDGE\invdb.dbf`
- Truncate the `imported_table_rpro` table (if specified)
- Read all records from the DBF file
- Process the records in batches of 1000
- Insert each batch into the Supabase table
- Log the progress to `batch_import.log`

You can also specify parameters:

```
node batchImport.js <dbf_file_path> <target_table> <truncate> <batch_size> <start_batch> <end_batch>
```

For example:
```
node batchImport.js R:\Rpro\BRIDGE\invdb.dbf imported_table_rpro true 500 0 5
```

This will import the first 5 batches of 500 records each.

### Step 3: Set Up Daily Import

#### Option 1: Windows Task Scheduler

```
node setupWindowsTask.js
```

This script will:
- Create a batch file for running the import
- Set up a Windows scheduled task to run at 6:15 AM every day
- Provide instructions for manual setup if needed

#### Option 2: PM2 Process Manager

```
node setupPm2Direct.js
```

This script will:
- Set up a PM2 scheduled task to run at 6:15 AM every day
- Save the PM2 configuration
- Display the PM2 process list

**Note**: You need to have PM2 installed globally. If not, install it with:
```
npm install -g pm2
```

## Configuration

The scripts use the following environment variables from the `.env` file:

- `SUPABASE_URL`: The URL of your Supabase project
- `SUPABASE_KEY`: The service role API key for your Supabase project
- `DBF_FILE_PATH`: The path to the DBF file (default: `R:\Rpro\BRIDGE\invdb.dbf`)
- `TARGET_TABLE`: The name of the target table in Supabase (default: `imported_table_rpro`)
- `TRUNCATE_BEFORE_IMPORT`: Whether to truncate the table before importing (default: `true`)
- `BATCH_SIZE`: The number of records to import in each batch (default: `1000`)

## Troubleshooting

If you encounter any issues:

1. Check the log files:
   - `create_rpro_table.log`: Log for table creation
   - `batch_import.log`: Log for data import
   - `direct_import.log`: Log for direct import

2. Common issues:
   - **Invalid API Key**: Make sure your Supabase API key is correct in the `.env` file
   - **Table Not Found**: Run `node createRproTable.js` to create the table
   - **File Not Found**: Make sure the DBF file exists at the specified path
   - **Import Errors**: Try reducing the batch size or importing specific batches

## Manual Import

If you need to manually import the data:

1. Create the table:
   ```
   node createRproTable.js
   ```

2. Import the data:
   ```
   node batchImport.js
   ```

3. Verify the import:
   ```
   node checkImportedData.js
   ```
