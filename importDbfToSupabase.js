// importDbfToSupabase.js - Script to import DBF files into Supabase

import { createClient } from '@supabase/supabase-js';
import { DBFFile } from 'dbffile';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import crypto from 'crypto';

// Load environment variables
dotenv.config();

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Main function to import DBF file to Supabase
 * @param {string} filePath - Path to the DBF file
 * @param {string} tableName - Target table name
 * @param {boolean} truncate - Whether to truncate the table before import
 */
async function importDbfToSupabase(filePath = './data/daily_import.dbf', tableName = 'imported_dbf_data', truncate = false) {
  // Store the parameters
  const dbfFilePath = filePath;
  const targetTable = tableName;
  const truncateBeforeImport = truncate;
  try {
    console.log(`Starting import of ${dbfFilePath} to ${targetTable}...`);

    // Check if file exists
    if (!fs.existsSync(dbfFilePath)) {
      throw new Error(`DBF file not found at path: ${dbfFilePath}`);
    }

    // Read the DBF file
    console.log('Reading DBF file...');
    const dbf = await DBFFile.open(dbfFilePath);
    console.log(`DBF file opened. Found ${dbf.recordCount} records.`);

    // Read all records
    const records = await dbf.readRecords();
    console.log(`Read ${records.length} records from DBF file.`);

        // Generate a batch ID for this import
    const importBatchId = crypto.randomUUID();
    console.log(`Generated import batch ID: ${importBatchId}`);

    // Process records to ensure they're compatible with Supabase
    const processedRecords = records.map(record => {
      const processedRecord = {};

      // Convert all field names to lowercase for Supabase compatibility
      for (const key in record) {
        const lowerKey = key.toLowerCase();

        // Convert Buffer objects to strings
        if (Buffer.isBuffer(record[key])) {
          processedRecord[lowerKey] = record[key].toString().trim();
        }
        // Convert Date objects to ISO strings
        else if (record[key] instanceof Date) {
          processedRecord[lowerKey] = record[key].toISOString();
        }
        // Handle null values
        else if (record[key] === null || record[key] === undefined) {
          processedRecord[lowerKey] = null;
        }
        // Handle numeric fields that should be numbers
        else if (typeof record[key] === 'string' && !isNaN(record[key]) && dbf.fields.find(f => f.name.toLowerCase() === lowerKey)?.type === 'N') {
          processedRecord[lowerKey] = Number(record[key]);
        }
        // Keep other values as is
        else {
          processedRecord[lowerKey] = record[key];
        }
      }

      // Add metadata fields
      processedRecord.imported_at = new Date().toISOString();
      processedRecord.import_batch_id = importBatchId;

      return processedRecord;
    });

    // If truncateBeforeImport is true, truncate the table first
    if (truncateBeforeImport) {
      console.log(`Truncating table ${targetTable} before import...`);
      const { error: truncateError } = await supabase.rpc('truncate_table', { table_name: targetTable });

      if (truncateError) {
        throw new Error(`Error truncating table: ${truncateError.message}`);
      }
      console.log(`Table ${targetTable} truncated successfully.`);
    }

    // Insert records in batches to avoid request size limitations
    const batchSize = 1000;
    let successCount = 0;

    for (let i = 0; i < processedRecords.length; i += batchSize) {
      const batch = processedRecords.slice(i, i + batchSize);
      console.log(`Inserting batch ${i / batchSize + 1} of ${Math.ceil(processedRecords.length / batchSize)}...`);

      const { data, error } = await supabase
        .from(targetTable)
        .insert(batch);

      if (error) {
        throw new Error(`Error inserting batch: ${error.message}`);
      }

      successCount += batch.length;
      console.log(`Batch inserted successfully.`);
    }

    console.log(`Import completed successfully. Imported ${successCount} records to ${targetTable}.`);

    // Return success information
    return {
      success: true,
      recordsImported: successCount,
      sourceFile: dbfFilePath,
      targetTable: targetTable,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error(`Error importing DBF file: ${error.message}`);
    console.error(error.stack);

    // Return error information
    return {
      success: false,
      error: error.message,
      sourceFile: dbfFilePath,
      targetTable: targetTable,
      timestamp: new Date().toISOString()
    };
  }
}

// Run the import function if this script is executed directly
// For ES modules, we check if the import.meta.url is the same as the process.argv[1]
if (import.meta.url === `file://${process.argv[1]}`) {
  // Get command line arguments
  const filePath = process.argv[2] || './data/daily_import.dbf';
  const tableName = process.argv[3] || 'imported_dbf_data';
  const truncate = process.argv[4] === 'true' || false;

  console.log(`Running import with parameters:`);
  console.log(`- File path: ${filePath}`);
  console.log(`- Table name: ${tableName}`);
  console.log(`- Truncate: ${truncate}`);

  importDbfToSupabase(filePath, tableName, truncate)
    .then(result => {
      if (result.success) {
        console.log('Import completed successfully.');
        process.exit(0);
      } else {
        console.error('Import failed.');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Unhandled error during import:', error);
      process.exit(1);
    });
}

// Export the function for use in other scripts
export default importDbfToSupabase;
