import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

dotenv.config();

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function main() {
  const id = parseInt(process.argv[2], 10);
  const { data, error } = await supabase
    .from('t_task_queue')
    .select('*')
    .eq('id', id)
    .single();
  if (error) { console.error(error); process.exit(1); }
  console.log(data);
}

main();

