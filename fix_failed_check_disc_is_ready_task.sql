-- Fix the failed task by updating its status back to 'pending'
-- This will allow the worker to pick it up again with the fixed code

UPDATE t_task_queue
SET status = 'pending',
    locked_at = NULL,
    locked_by = NULL,
    result = NULL,
    processed_at = NULL
WHERE task_type = 'check_if_disc_is_ready'
AND status = 'error'
AND result->>'error' = 'Disc record not found';

-- Confirmation message
DO $$
BEGIN
    RAISE NOTICE 'Failed check_if_disc_is_ready tasks have been reset to pending status.';
END $$;
