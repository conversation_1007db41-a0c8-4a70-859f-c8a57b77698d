require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_KEY);

async function testVendorOslMapping() {
  try {
    console.log('Testing vendor OSL mapping functionality...');
    
    // First, let's find a disc with weight_mfg data
    const { data: testDisc, error: discError } = await supabase
      .from('t_discs')
      .select('id, mps_id, weight, weight_mfg, color_id, order_sheet_line_id, vendor_osl_id')
      .not('weight_mfg', 'is', null)
      .not('mps_id', 'is', null)
      .not('color_id', 'is', null)
      .limit(1)
      .single();
    
    if (discError) {
      console.error('Error finding test disc:', discError);
      return;
    }
    
    console.log('Test disc found:', testDisc);
    
    // Test the new vendor OSL function
    console.log('\nTesting find_matching_osl_by_mfg_weight function...');
    const { data: vendorOslData, error: vendorOslError } = await supabase.rpc(
      'find_matching_osl_by_mfg_weight',
      {
        mps_id_param: testDisc.mps_id,
        color_id_param: testDisc.color_id,
        weight_mfg_param: testDisc.weight_mfg
      }
    );
    
    if (vendorOslError) {
      console.error('Error calling vendor OSL function:', vendorOslError);
      return;
    }
    
    console.log('Vendor OSL function result:', vendorOslData);
    
    // Also test the regular OSL function for comparison
    console.log('\nTesting regular find_matching_osl function for comparison...');
    const { data: regularOslData, error: regularOslError } = await supabase.rpc(
      'find_matching_osl',
      {
        mps_id_param: testDisc.mps_id,
        color_id_param: testDisc.color_id,
        weight_param: testDisc.weight
      }
    );
    
    if (regularOslError) {
      console.error('Error calling regular OSL function:', regularOslError);
      return;
    }
    
    console.log('Regular OSL function result:', regularOslData);
    
    // Compare results
    const vendorOslId = vendorOslData && vendorOslData.length > 0 ? vendorOslData[0].osl_id : null;
    const regularOslId = regularOslData && regularOslData.length > 0 ? regularOslData[0].osl_id : null;
    
    console.log('\n=== COMPARISON ===');
    console.log(`Disc weight: ${testDisc.weight}`);
    console.log(`Disc weight_mfg: ${testDisc.weight_mfg}`);
    console.log(`Regular OSL (using weight): ${regularOslId}`);
    console.log(`Vendor OSL (using weight_mfg): ${vendorOslId}`);
    console.log(`Current order_sheet_line_id: ${testDisc.order_sheet_line_id}`);
    console.log(`Current vendor_osl_id: ${testDisc.vendor_osl_id}`);
    
    if (regularOslId !== vendorOslId) {
      console.log('✅ Different mappings found - this demonstrates the dual mapping functionality!');
    } else {
      console.log('ℹ️ Same OSL found for both weight and weight_mfg');
    }
    
    // Now test creating a match_disc_to_osl task
    console.log('\n=== TESTING TASK CREATION ===');
    const { data: taskData, error: taskError } = await supabase
      .from('t_task_queue')
      .insert([
        {
          task_type: 'match_disc_to_osl',
          payload: { 
            id: testDisc.id, 
            operation: 'UPDATE',
            old_data: testDisc
          },
          status: 'pending',
          scheduled_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          enqueued_by: 'test_vendor_osl_mapping'
        }
      ])
      .select();
      
    if (taskError) {
      console.error('Error creating test task:', taskError);
      return;
    }
    
    console.log('Test task created:', taskData[0]);
    console.log('\n✅ Test completed successfully!');
    console.log('The task queue worker should now process this task and update both order_sheet_line_id and vendor_osl_id fields.');
    
  } catch (err) {
    console.error('Error:', err.message);
  }
}

testVendorOslMapping();
