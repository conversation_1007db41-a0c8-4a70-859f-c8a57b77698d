/**
 * Process mps_override_prices_changed_so_update_shopify task
 * 
 * This task is triggered when an MPS override price changes (retail or MSRP).
 * It finds all related discs and OSLs that use this MPS and are uploaded to Shopify,
 * and enqueues individual price update tasks for each item.
 * 
 * Since these items use MPS override prices, they should be updated regardless
 * of the plastic's base prices.
 */

/**
 * Process MPS override prices changed task
 * @param {Object} task - The task object from the queue
 * @param {Object} context - Context object containing supabase client and helper functions
 */
export default async function processMpsOverridePricesChangedTask(task, context = {}) {
  const { supabase, updateTaskStatus, logError } = context;
  
  if (!supabase) {
    console.error('[processMpsOverridePricesChangedTask] Supabase client not provided in context');
    return;
  }

  const { payload } = task;
  const mpsId = payload.id;
  const retailPriceChanged = payload.retail_price_changed || false;
  const msrpPriceChanged = payload.msrp_price_changed || false;

  console.log(`[processMpsOverridePricesChangedTask] Processing MPS override price changes for MPS id=${mpsId}, retail=${retailPriceChanged}, msrp=${msrpPriceChanged}`);

  try {
    // Update task to 'processing' status
    await updateTaskStatus(task.id, 'processing');

    // Get the MPS data with current override prices
    const { data: mpsData, error: mpsError } = await supabase
      .from('t_mps')
      .select(`
        id,
        val_override_retail_price,
        val_override_msrp,
        t_molds!inner (mold),
        t_plastics!inner (plastic),
        t_stamps!inner (stamp)
      `)
      .eq('id', mpsId)
      .single();

    if (mpsError) {
      throw new Error(`Failed to fetch MPS data: ${mpsError.message}`);
    }

    if (!mpsData) {
      throw new Error(`MPS with id=${mpsId} not found`);
    }

    const mpsDescription = `${mpsData.t_molds.mold} ${mpsData.t_plastics.plastic} ${mpsData.t_stamps.stamp}`;
    console.log(`[processMpsOverridePricesChangedTask] MPS: ${mpsDescription}`);
    console.log(`[processMpsOverridePricesChangedTask] Override retail price: ${mpsData.val_override_retail_price}`);
    console.log(`[processMpsOverridePricesChangedTask] Override MSRP: ${mpsData.val_override_msrp}`);

    // Find all discs that use this MPS and are uploaded to Shopify
    // Criteria:
    // 1. Not sold (sold_date IS NULL)
    // 2. Already uploaded to Shopify (shopify_uploaded_at IS NOT NULL)
    // 3. Uses the specified mps_id
    const { data: discsToUpdate, error: discsError } = await supabase
      .from('t_discs')
      .select('id, mps_id')
      .is('sold_date', null)
      .not('shopify_uploaded_at', 'is', null)
      .eq('mps_id', mpsId);

    if (discsError) {
      throw new Error(`Failed to fetch discs to update: ${discsError.message}`);
    }

    console.log(`[processMpsOverridePricesChangedTask] Found ${discsToUpdate.length} discs that need updates`);

    // Find all OSLs that use this MPS and are uploaded to Shopify
    // Criteria:
    // 1. Already uploaded to Shopify (shopify_uploaded_at IS NOT NULL)
    // 2. Uses the specified mps_id
    const { data: oslsToUpdate, error: oslsError } = await supabase
      .from('t_order_sheet_lines')
      .select('id, mps_id')
      .not('shopify_uploaded_at', 'is', null)
      .eq('mps_id', mpsId);

    if (oslsError) {
      throw new Error(`Failed to fetch OSLs to update: ${oslsError.message}`);
    }

    console.log(`[processMpsOverridePricesChangedTask] Found ${oslsToUpdate.length} OSLs that need updates`);

    const totalItemsToUpdate = discsToUpdate.length + oslsToUpdate.length;

    if (totalItemsToUpdate === 0) {
      await updateTaskStatus(task.id, 'completed', {
        message: `No discs or OSLs found that need updates for MPS id=${mpsId}`,
        mps_id: mpsId,
        mps_description: mpsDescription,
        retail_price_changed: retailPriceChanged,
        msrp_price_changed: msrpPriceChanged,
        discs_found: discsToUpdate.length,
        osls_found: oslsToUpdate.length,
        total_items_processed: 0
      });
      return;
    }

    // Enqueue individual price update tasks for each disc and OSL
    let enqueuedCount = 0;
    let errorCount = 0;

    // Process discs - enqueue both retail and MSRP tasks if needed
    for (const disc of discsToUpdate) {
      try {
        // Enqueue retail price update if retail price changed
        if (retailPriceChanged) {
          console.log(`[processMpsOverridePricesChangedTask] Enqueueing retail price update task for disc id=${disc.id}`);

          const { error: retailEnqueueError } = await supabase
            .from('t_task_queue')
            .insert({
              task_type: 'update_disc_variant_price_on_shopify',
              payload: {
                id: disc.id,
                item_type: 'disc',
                new_retail_price: mpsData.val_override_retail_price
              },
              status: 'pending',
              scheduled_at: new Date().toISOString(),
              created_at: new Date().toISOString(),
              enqueued_by: `mps_override_prices_changed_${mpsId}`
            });

          if (retailEnqueueError) {
            console.error(`[processMpsOverridePricesChangedTask] Error enqueueing retail task for disc id=${disc.id}:`, retailEnqueueError);
            errorCount++;
          } else {
            enqueuedCount++;
          }
        }

        // Enqueue MSRP update if MSRP changed
        if (msrpPriceChanged) {
          console.log(`[processMpsOverridePricesChangedTask] Enqueueing MSRP update task for disc id=${disc.id}`);

          const { error: msrpEnqueueError } = await supabase
            .from('t_task_queue')
            .insert({
              task_type: 'update_disc_variant_msrp_on_shopify',
              payload: {
                id: disc.id,
                item_type: 'disc',
                new_msrp_price: mpsData.val_override_msrp
              },
              status: 'pending',
              scheduled_at: new Date().toISOString(),
              created_at: new Date().toISOString(),
              enqueued_by: `mps_override_prices_changed_${mpsId}`
            });

          if (msrpEnqueueError) {
            console.error(`[processMpsOverridePricesChangedTask] Error enqueueing MSRP task for disc id=${disc.id}:`, msrpEnqueueError);
            errorCount++;
          } else {
            enqueuedCount++;
          }
        }
      } catch (error) {
        console.error(`[processMpsOverridePricesChangedTask] Error processing disc id=${disc.id}:`, error.message);
        errorCount++;
      }
    }

    // Process OSLs - enqueue both retail and MSRP tasks if needed
    for (const osl of oslsToUpdate) {
      try {
        // Enqueue retail price update if retail price changed
        if (retailPriceChanged) {
          console.log(`[processMpsOverridePricesChangedTask] Enqueueing retail price update task for OSL id=${osl.id}`);

          const { error: retailEnqueueError } = await supabase
            .from('t_task_queue')
            .insert({
              task_type: 'update_disc_variant_price_on_shopify',
              payload: {
                id: osl.id,
                item_type: 'osl',
                new_retail_price: mpsData.val_override_retail_price
              },
              status: 'pending',
              scheduled_at: new Date().toISOString(),
              created_at: new Date().toISOString(),
              enqueued_by: `mps_override_prices_changed_${mpsId}`
            });

          if (retailEnqueueError) {
            console.error(`[processMpsOverridePricesChangedTask] Error enqueueing retail task for OSL id=${osl.id}:`, retailEnqueueError);
            errorCount++;
          } else {
            enqueuedCount++;
          }
        }

        // Enqueue MSRP update if MSRP changed
        if (msrpPriceChanged) {
          console.log(`[processMpsOverridePricesChangedTask] Enqueueing MSRP update task for OSL id=${osl.id}`);

          const { error: msrpEnqueueError } = await supabase
            .from('t_task_queue')
            .insert({
              task_type: 'update_disc_variant_msrp_on_shopify',
              payload: {
                id: osl.id,
                item_type: 'osl',
                new_msrp_price: mpsData.val_override_msrp
              },
              status: 'pending',
              scheduled_at: new Date().toISOString(),
              created_at: new Date().toISOString(),
              enqueued_by: `mps_override_prices_changed_${mpsId}`
            });

          if (msrpEnqueueError) {
            console.error(`[processMpsOverridePricesChangedTask] Error enqueueing MSRP task for OSL id=${osl.id}:`, msrpEnqueueError);
            errorCount++;
          } else {
            enqueuedCount++;
          }
        }
      } catch (error) {
        console.error(`[processMpsOverridePricesChangedTask] Error processing OSL id=${osl.id}:`, error.message);
        errorCount++;
      }
    }

    // Mark task as completed
    await updateTaskStatus(task.id, 'completed', {
      message: `Successfully enqueued ${enqueuedCount} price update tasks (${discsToUpdate.length} discs + ${oslsToUpdate.length} OSLs), ${errorCount} errors`,
      mps_id: mpsId,
      mps_description: mpsDescription,
      retail_price_changed: retailPriceChanged,
      msrp_price_changed: msrpPriceChanged,
      override_retail_price: mpsData.val_override_retail_price,
      override_msrp: mpsData.val_override_msrp,
      discs_found: discsToUpdate.length,
      osls_found: oslsToUpdate.length,
      total_items_found: totalItemsToUpdate,
      tasks_enqueued: enqueuedCount,
      errors: errorCount
    });

    console.log(`[processMpsOverridePricesChangedTask] Completed processing MPS id=${mpsId}. Found ${discsToUpdate.length} discs + ${oslsToUpdate.length} OSLs. Enqueued ${enqueuedCount} tasks, ${errorCount} errors`);

  } catch (error) {
    console.error(`[processMpsOverridePricesChangedTask] Error processing MPS override price changes for MPS id=${mpsId}:`, error.message);
    
    await updateTaskStatus(task.id, 'failed', {
      error: error.message,
      mps_id: mpsId
    });
  }
}
